{"family": "iac-PRODFLOW-task-def-live", "executionRoleArn": "arn:aws:iam::156515529675:role/iac-task-exec-role-live-3d7abfd", "taskRoleArn": "arn:aws:iam::156515529675:role/iac-taskRoleProd-live-c6b7250", "networkMode": "awsvpc", "containerDefinitions": [{"name": "airflow-webserver", "image": "IMAGENAME_AIRFLOW", "essential": true, "portMappings": [{"hostPort": 8080, "protocol": "tcp", "containerPort": 8080}], "healthCheck": {"retries": 3, "command": ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"], "timeout": 5, "interval": 30, "startPeriod": 30}, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "iac-PRODFLOW-log-group-live-8041aa1", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "webserver"}}, "mountPoints": [{"containerPath": "/opt/airflow", "sourceVolume": "efs-airflow-home"}], "secrets": [{"valueFrom": "arn:aws:secretsmanager:us-east-2:156515529675:secret:prod/airflow/database-9gov1j:AIRFLOW__DATABASE__SQL_ALCHEMY_CONN::", "name": "AIRFLOW__DATABASE__SQL_ALCHEMY_CONN"}, {"valueFrom": "arn:aws:secretsmanager:us-east-2:156515529675:secret:prod/airflow/fernet_key-GUvQdI:AIRFLOW__CORE__FERNET_KEY::", "name": "AIRFLOW__CORE__FERNET_KEY"}, {"valueFrom": "arn:aws:secretsmanager:us-east-2:156515529675:secret:dev/airflow/google_auth_config-rIjk1K:client_id::", "name": "AIRFLOW__GOOGLE__CLIENT_ID"}, {"valueFrom": "arn:aws:secretsmanager:us-east-2:156515529675:secret:dev/airflow/google_auth_config-rIjk1K:client_secret::", "name": "AIRFLOW__GOOGLE__CLIENT_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:us-east-2:156515529675:secret:dev/airflow/google_auth_config-rIjk1K:ADMIN_EMAILS::", "name": "AIRFLOW__GOOGLE__ADMIN_EMAILS"}], "environment": [{"name": "AIRFLOW_COMPONENT", "value": "webserver"}, {"name": "AIRFLOW__LOG_RETENTION_DAYS", "value": "2"}, {"name": "AIRFLOW__CORE__ENVIRONMENT", "value": "prod"}, {"name": "AIRFLOW_PLUGIN_DAG_CONFIG_REGION", "value": "prod"}]}, {"name": "airflow-scheduler", "image": "IMAGENAME_AIRFLOW", "essential": true, "secrets": [{"valueFrom": "arn:aws:secretsmanager:us-east-2:156515529675:secret:prod/airflow/database-9gov1j:AIRFLOW__CELERY__RESULT_BACKEND::", "name": "AIRFLOW__CELERY__RESULT_BACKEND"}, {"valueFrom": "arn:aws:secretsmanager:us-east-2:156515529675:secret:prod/airflow/database-9gov1j:AIRFLOW__DATABASE__SQL_ALCHEMY_CONN::", "name": "AIRFLOW__DATABASE__SQL_ALCHEMY_CONN"}, {"valueFrom": "arn:aws:secretsmanager:us-east-2:156515529675:secret:prod/airflow/fernet_key-GUvQdI:AIRFLOW__CORE__FERNET_KEY::", "name": "AIRFLOW__CORE__FERNET_KEY"}, {"valueFrom": "arn:aws:secretsmanager:us-east-2:156515529675:secret:dev/airflow/google_auth_config-rIjk1K:client_id::", "name": "AIRFLOW__GOOGLE__CLIENT_ID"}, {"valueFrom": "arn:aws:secretsmanager:us-east-2:156515529675:secret:dev/airflow/google_auth_config-rIjk1K:client_secret::", "name": "AIRFLOW__GOOGLE__CLIENT_SECRET"}, {"valueFrom": "arn:aws:secretsmanager:us-east-2:156515529675:secret:dev/airflow/google_auth_config-rIjk1K:ADMIN_EMAILS::", "name": "AIRFLOW__GOOGLE__ADMIN_EMAILS"}], "mountPoints": [{"containerPath": "/opt/airflow", "sourceVolume": "efs-airflow-home"}], "healthCheck": {"retries": 2, "command": ["CMD-SHELL", "curl -f http://localhost:8974/health || exit 1"], "timeout": 2, "interval": 30, "startPeriod": 30}, "environment": [{"name": "AIRFLOW_COMPONENT", "value": "scheduler"}, {"name": "AIRFLOW__LOG_RETENTION_DAYS", "value": "2"}, {"name": "AIRFLOW__CELERY__BROKER_URL", "value": "redis://prodflow-celery-redis.z0q9fi.ng.0001.use2.cache.amazonaws.com:6379/0"}, {"name": "AIRFLOW__CORE__ENVIRONMENT", "value": "prod"}, {"name": "AIRFLOW_PLUGIN_DAG_CONFIG_REGION", "value": "prod"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "iac-PRODFLOW-log-group-live-8041aa1", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "scheduler"}}}], "requiresCompatibilities": ["FARGATE"], "volumes": [{"name": "efs-airflow-home", "efsVolumeConfiguration": {"fileSystemId": "fs-0c20700466b35ed4d", "transitEncryption": "ENABLED", "rootDirectory": "/airflow_prod"}}], "cpu": "2048", "memory": "6 GB"}
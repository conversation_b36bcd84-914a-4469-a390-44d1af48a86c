version: 0.0
Resources:
  - TargetService:
      Type: AWS::ECS::Service
      Properties:
        TaskDefinition: <TASK_DEFINITION>
        LoadBalancerInfo:
          ContainerName: "airflow-worker"
          ContainerPort: 9200
        CapacityProviderStrategy:
          - Base: 0
            CapacityProvider: "FARGATE_SPOT"
            Weight: 1
          - Base: 1
            CapacityProvider: "FARGATE"
            Weight: 0
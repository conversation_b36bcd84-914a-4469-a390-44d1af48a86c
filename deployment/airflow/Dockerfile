# Use the official Apache Airflow image as the base
# this is cloned to our ecr to avoid throttling.
# Image details: https://hub.docker.com/layers/apache/airflow/slim-2.9.2-python3.12/images/sha256-c1474a7e0c9049e3575fcad4a971095d6c3cde4617dc3f9c420254bcb518533f?context=explore
# airflow: 2.9.2, python: 3.12
FROM 156515529675.dkr.ecr.us-east-2.amazonaws.com/iac-devflow-ecr-live-7e39387:airflow-2.9.2-py3.12-base

USER root
# Install dependencies
RUN apt-get update && apt-get -y install jq netcat-traditional

# Add airflow to sudoers list
RUN  echo "airflow ALL=(ALL) NOPASSWD: /usr/bin/mkdir,/usr/bin/cp" >> /etc/sudoers.d/airflow_entry

# make way for airlfow pids
RUN mkdir -p /opt/airflow_pids
RUN chown -R airflow:root /opt/airflow_pids
RUN chmod -R g=rwx /opt/airflow_pids

# Copy the entrypoint script
COPY deployment/airflow/entrypoint.sh /entrypoint.sh
COPY deployment/airflow/log_cleanup.sh /log_cleanup.sh
RUN chmod +x /entrypoint.sh
RUN chmod +x /log_cleanup.sh

USER airflow

# Install PyYAML (if needed)
RUN python3 -m pip install pyyaml

# Add fastapi deps for healthcheck server for workers
RUN python3 -m pip install fastapi[standard] dbt-core dbt-snowflake
# copy airflow deps
COPY deployment/airflow/requirements.txt /
RUN python3 -m pip install -r /requirements.txt
RUN python3 -m pip install --upgrade protobuf

# Set the entrypoint
ENTRYPOINT ["/entrypoint.sh"]
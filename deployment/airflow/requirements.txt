--constraint "https://raw.githubusercontent.com/apache/airflow/constraints-2.9.2/constraints-3.12.txt"
apache-airflow-providers-celery
apache-airflow-providers-amazon
apache-airflow-providers-postgres
apache-airflow-providers-snowflake
apache-airflow-providers-common-sql
apache-airflow-providers-papermill
apache-airflow-providers-slack
apache-airflow-providers-ssh
apache-airflow-providers-fab
apache-airflow-providers-common-compat
snowflake-sqlalchemy
snowflake-connector-python
annotated-types
anyio
black
boto3
botocore
certifi
charset-normalizer
click
docker
gitdb
gitpython
h11
httpcore
httpx
idna
jmespath
markdown-it-py
mdurl
mypy
mypy-extensions
packaging
pathspec
platformdirs
pydantic
pydantic-core
pydantic-settings
pygments
python-dateutil
python-dotenv
pyyaml
requests
rich
ruff
s3transfer
six
smmap
sniffio
tomli
typer
typing-extensions
urllib3
psycopg2-binary
awswrangler
pandas
Authlib
gevent
psycogreen
nest_asyncio
dask
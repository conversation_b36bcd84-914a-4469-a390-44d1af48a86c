from fastapi import FastAPI

app = FastAPI()


@app.get("/")
async def root():
    return {"message": "Healthy"}
@app.get("/ping")
async def root():
    return {"message": "Healthy"}
@app.get("/health")
async def root():
    return {"message": "Healthy"}

@app.get("/api/v1/random_path")
@app.get("/api/v1/random_path/")
async def root():
    return {"message": "Healthy"}

@app.get("/api/v1/random_path/health/")
@app.get("/api/v1/random_path/health")
async def root():
    return {"message": "Healthy"}
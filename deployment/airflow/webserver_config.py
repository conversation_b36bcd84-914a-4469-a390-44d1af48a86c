from airflow.providers.fab.auth_manager.security_manager.override import FabAirflowSecurityManagerOverride
from flask_appbuilder.security.manager import AUTH_OAUTH
from typing import List, Any, Union, Dict
import logging
import os

log = logging.getLogger(__name__)

AUTH_TYPE = AUTH_OAUTH
AUTH_ROLES_SYNC_AT_LOGIN = True  # Checks roles on every login
AUTH_USER_REGISTRATION = True  # allow users who are not already in the FAB DB to register
AUTH_USER_REGISTRATION_ROLE = "Viewer"
WTF_CSRF_ENABLED = True

AUTH_ROLES_MAPPING = {
    "Viewer": ["Viewer"],
    "Admin": ["Admin"],
    "User": ["User"],
}


def get_admin_emails_list() -> List[str]:
    email_list_str = os.getenv("AIRFLOW__GOOGLE__ADMIN_EMAILS", None)
    if email_list_str is None:
        return []
    emails_list = email_list_str.split(",")
    emails_list = [ele.strip() for ele in emails_list if len(ele.strip()) > 0]
    return emails_list


def get_roles_for_email(email: str) -> List[str]:
    """Returns a list of roles for an email.
    When a user logs in using Google, the email is used to determine their role.
    This is useful for granting admin access to specific users.
    All other users will be granted the "User" role.
    """
    log.info(f"Determinig Role for User: {email}")
    stripped_email = email.strip()
    if not stripped_email.endswith("goessor.com"):
        log.info(f"Email: {email} does not belong to our org hence granting Incorrect Role")
        return ["RandomRole"]

    ADMIN_EMAILS: List[str] = get_admin_emails_list()
    log.info(f"Admin emails={ADMIN_EMAILS}")
    if stripped_email in ADMIN_EMAILS:
        return ["Admin"]
    else:
        return ["User"]


# If you wish, you can add multiple OAuth providers.
OAUTH_PROVIDERS = [
    {
        "name": "google",
        "icon": "fa-google",
        "token_key": "access_token",
        "remote_app": {
            "client_id": os.getenv("AIRFLOW__GOOGLE__CLIENT_ID"),
            "client_secret": os.getenv("AIRFLOW__GOOGLE__CLIENT_SECRET"),
            "api_base_url": "https://www.googleapis.com/oauth2/v2/",
            "client_kwargs": {"scope": "email profile"},
            "request_token_url": None,
            "access_token_url": "https://accounts.google.com/o/oauth2/token",
            "authorize_url": "https://accounts.google.com/o/oauth2/auth",
            "jwks_uri": "https://www.googleapis.com/oauth2/v3/certs",
        },
    },
]


class OauthAuthorizer(FabAirflowSecurityManagerOverride):
    # For other providers:
    # https://github.com/dpgaspar/Flask-AppBuilder/blob/master/flask_appbuilder/security/manager.py#L550
    def get_oauth_user_info(self, provider: str, resp: Any) -> Dict[str, Union[str, List[str]]]:
        log.info(f"Getting user info from {provider}")

        if provider == "google":
            userinfo = self.appbuilder.sm.oauth_remotes[provider].get("userinfo")

            user_data = userinfo.json()
            email = user_data.get("email", "")
            log.info(f"Login initiated for User: {email}")
            roles = get_roles_for_email(email)
            log.info(f"User {email} has roles: {roles}")
            return {
                "username": "google_" + user_data.get("id", ""),
                "first_name": user_data.get("given_name", ""),
                "last_name": user_data.get("family_name", ""),
                "email": email,
                "role_keys": roles,
            }

        # elif provider == "github":
        #     remote_app = self.appbuilder.sm.oauth_remotes[provider]

        #     user_data = remote_app.get("user").json()
        #     team_data = remote_app.get("user/teams").json()
        #     login = user_data.get("login")
        #     roles = get_roles_for_gh_team(team_data)
        #     log.info(f"User {login} has roles: {roles}")
        #     return {"username": "gh_" + login, "role_keys": roles}

        else:
            return {}


SECURITY_MANAGER_CLASS = OauthAuthorizer

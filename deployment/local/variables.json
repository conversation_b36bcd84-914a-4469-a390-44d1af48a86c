{"DISABLED_ALERTS": "none", "adtech_suggested_bid_logic": "case\r\n            when delta_tacos < -2 then -10\r\n            when delta_tacos > 2 then 10\r\n            when delta_acos > 20 then 20\r\n            when delta_acos > 10 then 10\r\n            when delta_acos < -60 then -40\r\n            when delta_acos < -30 then -20\r\n            when delta_acos < -10 then -10\r\n        else 0 end", "custom_dag_schedule_1": "0 5,7,11,13,14,15,16,17,18,19,20,23 * * *", "dag_schedules": {"adtech_ads_metrics_all": "10,27 * * * *", "amazon_listings": "0 0,1,7,8,11,13,14,15,16,17,18,22,23 * * *", "amazon_promotions_planning": "0 11,13 * * *", "dsie_asin_insights": "20 0-3,9,14-23 * * *", "dsie_pricing_insights": "50 */3 * * *", "dtc_ads_campaign_report": "50 * * * *", "dynamic_pricing": "35 * * * *", "dynamic_promotions": "15 * * * *", "fact_amazon_reviews": "30 6 * * *", "fact_amazon_settlements": "30 * * * *", "hannibal_data_replication": "14 * * * *", "monitor_hourly": "30 14,17,19,21,3 * * *", "performance_planning": "15 3,5,9,11,14,17,19,23 * * *", "promotion_auto_updater": "05 0,6,12,18 * * *", "retail_unified_model": "45 */2 * * *", "s3_datalake_event_logging": "30 * * * *", "s3_monitoring": "45 6,18 * * *"}, "dags_to_skip_auto_snooze": ["klaviyo_v1"], "data_delay_dag_schedule": "30 12,15,17,19 * * *", "data_delay_lag_in_days": {"amazon_fee_preview": 1, "fact_all_item_sales_traffic_report": 2, "fact_all_orders": 0, "fact_all_pricing": 1, "fact_all_shipments": 1, "fact_amazon_ad_campaigns": 1, "fact_amazon_ad_keywords": 1, "fact_amazon_bsr": 1, "fact_amazon_fba_storage_fees": 39, "fact_amazon_inventory": 1, "fact_amazon_listings": 0, "fact_amazon_subledger": 1, "settlement": 15}, "disable_auto_snooze": 0, "disable_snooze": 0, "environment": "dev", "monitor_s3_dashboard": "http://superset.hydy.co/superset/dashboard/31/?native_filters_key=8WCYbKJVOfVxpn1Yp6LuJac7q2KVxI0LEDkekvXfsZ8nKt4aepf_vT4pITF8xEGi\t", "num_hours_to_auto_snooze": 24, "num_snooze_alert_limit": 3, "rum_num_days_to_process": 730, "stg_ad_campaign_metrics_today_all__budget_flag_logic": "case\r\nwhen upper(campaign_calculated_status_name_today) = 'CAMPAIGN_OUT_OF_BUDGET' then 'OUT OF BUDGET'\r\nwhen div0(spend_today*100.0, daily_budget) > 70 then 'SPEND>70'\r\nend ", "stg_ad_campaign_metrics_today_all__suggested_budget_pct_change_logic": "case \r\nwhen -delta_tacos_today <=0 and div0(spend_today*100.0, daily_budget) >= 70 then 50\r\nwhen -delta_tacos_today > 0 and -delta_tacos_today < 2 and div0(spend_today*100.0, daily_budget) >= 70 then 25\r\nelse 0 end", "stg_ad_group_metrics_today_all__suggested_bid_logic": "case\r\nwhen PARENT_ASIN_OPS_7D >= 1000 and PARENT_ASIN_TACOS_7D > 1.25 * finance_target_tacos then -15\r\nwhen PARENT_ASIN_OPS_7D < 1000 and PARENT_ASIN_TACOS_7D > 1.5 * finance_target_tacos then -15\r\nwhen PARENT_ASIN_TACOS_7D < 0.5 * finance_target_tacos then 15\r\nelse 0 end", "stg_ad_group_metrics_today_all__tacos_flag_logic": "case\r\nwhen delta_tacos_today > 2 then 'UNDER'\r\nwhen delta_tacos_today < -2 then 'OVER'\r\nelse 'NORMAL'\r\nend"}
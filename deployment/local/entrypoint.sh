#!/bin/bash

# redirect stderr to stdout as well
exec 2>&1

# for gevent
export _AIRFLOW_PATCH_GEVENT=1

## Check the AIRFLOW__CORE__ENVIRONMENT environment variable
if [ -z "${AIRFLOW__CORE__ENVIRONMENT}" ]; then
 echo "Environment variable AIRFLOW__CORE__ENVIRONMENT is not set"
 exit 1
fi

if [ -z "${AIRFLOW_PLUGIN_DAG_CONFIG_REGION}" ]; then
 echo "Environment variable AIRFLOW_PLUGIN_DAG_CONFIG_REGION is not set"
 exit 1
fi


# Perform database upgrade if necessary
if [ "$1" == "db" ] && [ "$2" == "upgrade" ]; then
    echo "Running airflow db upgrade..."
    airflow db upgrade
    exit 0
fi

# Parse and get a new hostname for celery workers on the same host. This would be the DockerId of the container
# CONTAINER_ID=$(curl -s "$ECS_CONTAINER_METADATA_URI_V4" | jq -r ".DockerId")
CONTAINER_ID=$HOSTNAME
echo "Container ID=$CONTAINER_ID"

echo "Checking mounted $AIRFLOW_HOME/logs perms inside dir"
if [ ! -d "$AIRFLOW_HOME/logs" ]; then
  echo "Logs dir not found on $AIRFLOW_HOME"
  mkdir -p "$AIRFLOW_HOME/logs"
  retVal=$?
  if [ $retVal -ne 0 ]; then
    echo "Error creating logs dir on mounted EFS"
    exit $retVal
  fi
fi

# Check the AIRFLOW_COMPONENT environment variable
case "$AIRFLOW_COMPONENT" in
  "scheduler")
    echo "Initalizing variables"
    airflow variables import "$AIRFLOW_HOME/variables.json"
    echo "Initalizing Connections"
    airflow connections import "$AIRFLOW_HOME/connections.json"

    echo "Starting Airflow Scheduler..."
    exec airflow scheduler --pid /opt/airflow_pids/scheduler.pid
    ;;
  "webserver")
    echo "Initializing airflow DB"
    airflow db migrate || true
    if [ -z "${_AIRFLOW_WWW_USER_PASSWORD}" ]; then
      echo "Airflow default User pass not set on webserver"
      exit 1
    fi
    airflow users create \
       --username "${_AIRFLOW_WWW_USER_USERNAME="admin"}" \
       --firstname "${_AIRFLOW_WWW_USER_FIRSTNAME="Airflow"}" \
       --lastname "${_AIRFLOW_WWW_USER_LASTNAME="Admin"}" \
       --email "${_AIRFLOW_WWW_USER_EMAIL="<EMAIL>"}" \
       --role "${_AIRFLOW_WWW_USER_ROLE="Admin"}" \
       --password "${_AIRFLOW_WWW_USER_PASSWORD}" || true
    echo "Starting Airflow Webserver..."
    exec airflow webserver --pid /opt/airflow_pids/webserver.pid
    ;;
  "worker")
    echo "Starting Airflow Worker..."
    if [ -z "${CELERY_WORKER_CUSTOM_HOSTNAME}" ]; then
      echo "started a fastapi session to satisfy the ALB health checks for deployment"
      uvicorn health_server:app --host 0.0.0.0 --port 9200 &
      echo "Environment variable CELERY_WORKER_CUSTOM_HOSTNAME is not set using: $CONTAINER_ID"
      exec airflow celery worker --celery-hostname "$CONTAINER_ID" --pid /opt/airflow_pids/worker.pid
    else
      # start log cleanup process
      echo "Starting Included Log cleaner"
      /clean-logs &
      exec airflow celery worker --celery-hostname "$CELERY_WORKER_CUSTOM_HOSTNAME" --pid /opt/airflow_pids/worker.pid
    fi
    ;;
  *)
    echo "Invalid AIRFLOW_COMPONENT specified: $AIRFLOW_COMPONENT"
    echo "Please set the AIRFLOW_COMPONENT environment variable to 'scheduler', 'webserver', or 'worker'"
    exit 1
    ;;
esac

# Default command
exec "$@"

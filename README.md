# dataplatform-core

### Development Environment setup



Clone repository to local machine using

`<NAME_EMAIL>:hydy-co/dataplatform-core-modules.git`

Once repository is cloned, following pre-requisites need to be performed

#### Pre-requisites setup
Following are the steps that are needed to be performed 

**on mac**

~~~
open .bash_profile and add the following 

export PYTHONPATH="<local_repo_path>/dataplatform-core-modules:${PYTHONPATH}"

By default new macs have shell as zsh. Update to use bash by opening 
Terminal > Preferences >  shells open with, choose command add bin/bash

exit terminal, re-open terminal and type source ~/.bash_profile
~~~


**on windows**
Add/ update PYTHONPATH system variable. Add the local repo location



local_repo_path is the location where the repository is on the local machine. Adding the repo to PYTHONPATH is essential
because of the dependency of different modules and package in the repo.

Also, since the aws services that we will be zipping two packages `commonlibs and sparklibs`. It is advised to create shell
scripts that will zip the two packages

**zip packages**

There are three packages that will be zipped
1. commonlibs
2. sparklibs
3. plugins

create a directory from the home folder

~~~
cd \
mkdir platform_zip_modules
cd platform_zip_modules
~~~

create a shell script that will resemble as following
~~~
#!/bin/bash

cd <local_repository_path>/dataplatform-core-modules
rm <homedir>/platform_zip_modules/commonlibs.zip
echo "Zipping commonlibs..."
zip -r <homedir>/platform_zip_modules/commonlibs.zip commonlibs -x "commonlibs/my_env/*" "*/.DS_Store" "*/__pycache__/*" "*/.vscode/*" "*/.idea/*"
echo "Zipping of commonlibs complete"

echo $PWD
rm <homedir>/platform_zip_modules/sparklibs.zip
zip -r <homedir>/platform_zip_modules/sparklibs.zip sparklibs -x "sparklibs/my_venv/*" "*/.DS_Store" "*/__pycache__/*" "*/.vscode/*" "*/.idea/*" "*/data/*"

echo $PWD

cd airflow/plugins
rm <homedir>/platform_zip_modules/plugins.zip
zip -r <homedir>/platform_zip_modules/plugins.zip * -x "*/__pycache__/*" "__pycache__/*" "*/.DS_Store" "*/.vscode/*" "*/.idea/*" "*/data/*"

~~~

The shell script above will do the following things
1. remove commonlibs.zip, create a zip file by zipping commonlibs package
2. remove sparklibs.zip, create a zip file by zipping sparklibs package
3. remove plugins.zip, create a zip file by zipping plugins package

The glue jobs depend on commonlibs.zip and sparklibs.zip files

We also have plugins.zip that airflow depends on and plugins have db connectors and helper functions that airflow dags depend on.

### Low Touch Ingestion Framework (RAPTOR)

The DAG generation is driven by a YAML configuration file that needs to be setup by the user. A sample configuration file is included in the git repo (*tools/raptor/etc/sample.yaml*).

Configuration parameters:
| Parameter  | Description | Optional |
| :------------- | :------------- | :------------- |
| project_id | Name of the project. Also used as DAG id| |
| author | Author creating the DAG. Use your slack username here. [Link](https://heydaycorp.slack.com/account/settings#username) to get slack username| |
| dag_start_date | Start date for DAG in YYYY-MM-DD format | |
| release_def | Can be set some integer value if user wants to track releases | Yes |
| build_num | Can be set some integer value if user wants to track builds | Yes |
| tables | list of dictionaries, where each element will define one table end-to-end (raw, dedupe, merge) | |

Each table element has the following parameters:
| Parameter  | Description | Optional |
| :------------- | :------------- | :------------- |
| table_name | Name of Table. All tables will use this suffix to create the intermediate tables (e.g. raw_{{table_name}}) |
| s3_sample_files | List of s3 files. For tables with evolving schema, pick one file each for the different schemas. All tables will be created with the merged schema from all listed files. |
| enabled | Set to False if you want to skip this table generation. |
| s3_locations | List of s3 locations to read all data from | 
| dedupe_config | Specify dedupe_config if you want to add a "dedupe" step | Yes |
| merge_config | Specify merge_config if you want to add a "merge" step | Yes |
| primary_key_fields | List of fields to create the primary key. A column called "pk" will be added to the output | Yes |

The dedupe step removes duplicates in data by using ROW_NUMBER or RANK window function based on partition_fields & order_fields.
The dedupe_config has the following parameters:
| Parameter  | Description |
| :------------- | :------------- |
| func | Function to apply for deduping. Should be either RANK or ROW_NUMBER |
| partition_fields | List of fields to partition the data by |
| order_fields | List of fields to order the data by |

The "merge" step uses upsert to update existing rows & insert new rows, based on join_fields (& additional match_fields). 
The merge_config has the following parameters:
| Parameter  | Description |
| :------------- | :------------- |
| join_fields | List of fields to join on in the merge step |
| match_fields | List of fields to additional match on join, If the field is not specified, defaults to is _daton_batch_runtime; If you do not need a match field, specify as empty (match_fields: []) |

**If the column read needs to be case-sensitive, use '"$COLNAME"' to force case.**

#### How to run

Create a python virtual environment, activate it and install the requirements:
- python -m venv raptor_env
- source raptor_env/bin/activate
- pip install -r tools/raptor/requirements.txt

**Also needs Java Runtime Environment ([Download](https://www.java.com/en/download/manual.jsp))**

Generate DAG:
- python tools/raptor/raptor.py --env dev --cfg_file tools/raptor/etc/sample.yaml --project_dir $HOME_DIR/raptor_output

Deploy DAG (DEV):
- python tools/raptor/deploy_dag.py --env dev --cfg_file tools/raptor/etc/sample.yaml --project_dir $HOME_DIR/raptor_output

Deploy DAG (PROD):
- python tools/raptor/deploy_dag.py --env dev --cfg_file tools/raptor/etc/sample.yaml --project_dir $HOME_DIR/raptor_output --path_to_github_dir $PATH_TO_DATAPLATFORM-CORE-MODULES

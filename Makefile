CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain hydy --domain-owner 156515529675 --query authorizationToken --output text`
POETRY_HTTP_BASIC_ARTIFACT_USERNAME=aws
POETRY_HTTP_BASIC_ARTIFACT_PASSWORD=${CODEARTIFACT_AUTH_TOKEN}
AIRFLOW_HOME_LOCAL=airflow_home_local
AIRFLOW_HOME_LOCAL_OPS=airflow_home_local/dags/ops_dags
AWS_HOME_LOCAL=aws_home_local
POSTGRES_DB_HOME=postgres_db
AIRFLOW_ENV=local
OPS_REPO_LOCAL_PATH=./
ENV_FILE=deployment/${AIRFLOW_ENV}/.env.local

code-artifact-auth:
	aws codeartifact get-repository-endpoint --domain hydy --domain-owner 156515529675 --repository python-common --format pypi --output text

clean:  ## Remove all build, test, coverage and Python artifacts
	rm -rf tests/__pycache__
	rm -rf pydantic/__pycache__
	rm -rf dist
	rm -rf htmlcov
	rm -rf coverage.xml
	rm -rf .coverage
	rm -rf .pytest_cache/
	find . | grep -E "(__pycache__)" | xargs rm -rf
	rm -rf ./"${AIRFLOW_HOME_LOCAL}"
	rm -rf ./"${AWS_HOME_LOCAL}"
	rm -rf ./"${POSTGRES_DB_HOME}"
	docker image prune -f
	docker compose down
	docker rmi airflow-local-base || true
	#docker rmi airflow-local-ops-base || true

requirements:
	aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 156515529675.dkr.ecr.us-east-2.amazonaws.com

build:
	docker build -t airflow-local-base:latest -f deployment/${AIRFLOW_ENV}/Dockerfile .

docker-run:
	docker run -ti --rm --entrypoint bash --env-file ./"${ENV_FILE}" -p 8081:8080 --network dataplatform-core-modules_back-tier -v ./"${AIRFLOW_HOME_LOCAL}":/opt/airflow -v ~/${AWS_HOME_LOCAL}:/home/<USER>/.aws:ro airflow-local-base:latest

create-volume:
	echo "Creating ${AIRFLOW_HOME_LOCAL} and copying files"
	mkdir -p "${AIRFLOW_HOME_LOCAL}"
	mkdir -p "${AWS_HOME_LOCAL}"
	mkdir -p "${POSTGRES_DB_HOME}"
	rsync -rltD --progress airflow/plugins "${AIRFLOW_HOME_LOCAL}"
	rsync -rltD --progress airflow/dags "${AIRFLOW_HOME_LOCAL}"
	rsync -rltD --progress deployment/airflow/health_server.py "${AIRFLOW_HOME_LOCAL}"
	rsync -rltD --progress deployment/${AIRFLOW_ENV}/airflow.cfg "${AIRFLOW_HOME_LOCAL}"
	rsync -rltD --progress deployment/${AIRFLOW_ENV}/*.json "${AIRFLOW_HOME_LOCAL}"
	rsync -rltD --progress "${HOME}/.aws/" "${AWS_HOME_LOCAL}/"
#	mkdir -p "${AIRFLOW_HOME_LOCAL_OPS}/dags"
#	mkdir -p "${AIRFLOW_HOME_LOCAL_OPS}/notebooks"
#	rsync -rltD --progress "${OPS_REPO_LOCAL_PATH}/dags/po_need_by_date" "${AIRFLOW_HOME_LOCAL_OPS}/dags"
#	rsync -rltD --progress "${OPS_REPO_LOCAL_PATH}/notebooks/po_need_by_date" "${AIRFLOW_HOME_LOCAL_OPS}/notebooks"
	# chown -R 50000 "${AIRFLOW_HOME_LOCAL}"

run: build create-volume docker-run
local: clean create-volume
	docker compose up

down:
	docker compose down

up:
	docker compose up

ops_sync:
	echo "Syncing Ops DAG to ${AIRFLOW_HOME_LOCAL}"
	rsync -rltD --progress "${OPS_REPO_LOCAL_PATH}/dags/po_need_by_date" "${AIRFLOW_HOME_LOCAL_OPS}/dags"
	rsync -rltD --progress "${OPS_REPO_LOCAL_PATH}/notebooks/po_need_by_date" "${AIRFLOW_HOME_LOCAL_OPS}/notebooks"

sync:
	echo "Syncing ${AIRFLOW_HOME_LOCAL} and local files"
	rsync -rltD --progress airflow/plugins "${AIRFLOW_HOME_LOCAL}"
	rsync -rltD --progress airflow/dags "${AIRFLOW_HOME_LOCAL}"
	rsync -rltD --progress "${HOME}/.aws/" "${AWS_HOME_LOCAL}/"
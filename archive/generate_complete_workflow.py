from airflow.generate_dag_from_template import generate_transformation_dag_from_template, generate_ingestion_dag_from_template
from aurora_db.workflow_configurations_load import set_transform_configurations, set_report_ingestion_configurations
import argparse


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='This script is used to generate dag code based on the templates that'
                                                 'are build by the Data Engineering team')
    parser.add_argument('-t', '--templateType', required=True,
                        help="What dag needs to be created based on the template. Currently supported templates"
                             "are INGESTION and TRANSFORMATION. The inputs are case sensitive")
    parser.add_argument('-r', '--reportName', required=False,
                        help="Name of the report if the templateType is INGESTION")
    parser.add_argument('-a', '--reportApi', required=False,
                        help="Name of the api if the templateType is INGESTION")
    parser.add_argument('-c', '--channel', required=False,
                        help="report channel if the templateType is INGESTION. Examples are SP, SHOPIFY, AMAZON_ADS")
    parser.add_argument('-p', '--transformName', required=False,
                        help="Name of the transformation for which dag needs to be generated. This is the name from"
                             "workflow_configurations.transform_job_info")
    parser.add_argument('-e', '--runEnvironment', required=False,
                        help="For which environment the template need to be generated. Dev and other environments will have"
                             "schema difference in database. This parameter will append appropriate environment variable")
    parser.add_argument('-d', '--dagFileName', required=False,
                        help="Name of dag file. Format is {chanel}_{report_name}_[Ingestion if its ingestion].py")
    parser.add_argument('-s', 'regionName', required=True,
                        help="aws region name where the secrets need to be pulled. It is us-east-1 for dev and us-east-2"
                             "for prod")
    parser.add_argument('-f', '--filesList', nargs='+')

    
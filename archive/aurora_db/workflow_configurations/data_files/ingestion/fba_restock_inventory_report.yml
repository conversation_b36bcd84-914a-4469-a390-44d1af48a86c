report_name: FBARestockInventoryReport
report_api: SP
channel: Amazon
stage_table_schema: dwh${env}.raw
stage_table_name: fba_restock_inventory_stg
ingestion_glue_job: sp_report_ingestion
report_refresh_type: INCREMENTAL
compressed_dataset_s3_location: s3://heyday-dataplatform-datalake/raw_compressed/daton/sp/FBARestockInventoryReport/
compressed_data_glue_db: None
compressed_data_glue_crawler: None
dag_name: fba_restock_inventory_ingestion
dag_schedule_cron: 0 */2 * * *
dag_description: Ingestion DAG for list financial events
dag_tags: Ingestion,Amazon,ListFinancialEvents,SP
is_active: Y
created_by: harsha
updated_by: harsha
seller_report_configurations:
  - BLC_US:
      seller_id: A2OJTSYWS5H2NP
      seller_code: BLC
      country_code: US
      s3_bucket: heyday-dataplatform-datalake
      source_data_location: raw/daton/BLC/BLC_AMAZON_SP_US/FBARestockInventoryReport/
      object_suffixes: .csv,.parquet
      is_active: Y
      created_by: harsha
      updated_by: harsha
  - SMT_US:
      seller_id: A1V0Z78OL1M5CE
      seller_code: SMT
      country_code: US
      s3_bucket: heyday-dataplatform-datalake
      source_data_location: raw/daton/SMT/SMT_AMAZON_SP_US/FBARestockInventoryReport/
      object_suffixes: .csv,.parquet
      is_active: Y
      created_by: harsha
      updated_by: harsha
  - BSY_US:
      seller_id: A33GEHI6Y6M3GW
      seller_code: BSY
      country_code: US
      s3_bucket: heyday-dataplatform-datalake
      source_data_location: raw/daton/BSY/BSY_AMAZON_SP_US/FBARestockInventoryReport/
      object_suffixes: .csv,.parquet
      is_active: Y
      created_by: harsha
      updated_by: harsha

report_name: orders
report_api: SHOPIFY
channel: Shopify
stage_table_schema: dwh${env}.raw
stage_table_name: shopify_orders_stg
ingestion_glue_job: shopify_report_ingestion
report_refresh_type: INCREMENTAL
compressed_dataset_s3_location: s3://heyday-dataplatform-datalake/raw_compressed/daton/shopify/orders/
compressed_data_glue_db: None
compressed_data_glue_crawler: None
dag_name: shopify_orders_ingestion
dag_schedule_cron: 0 */2 * * *
dag_description: Ingestion DAG for shopify orders
dag_tags: Ingestion,Shopify,Orders
is_active: Y
created_by: harsha
updated_by: harsha
seller_report_configurations:
  - BOK_US:
      seller_id: N/A
      seller_code: BOK
      country_code: US
      s3_bucket: orion-daton
      source_data_location: BRAND_BOK/BOK_SHOPIFY_MAIN_US/orders/
      object_suffixes: .csv,.parquet
      is_active: Y
      created_by: harsha
      updated_by: harsha
  - CAV_US:
      seller_id: N/A
      seller_code: CAV
      country_code: US
      s3_bucket: orion-daton
      source_data_location: BRAND_CAV/CAV_SHOPIFY_MAIN_US/orders/
      object_suffixes: .csv,.parquet
      is_active: Y
      created_by: harsha
      updated_by: harsha
  - IRO_US:
      seller_id: N/A
      seller_code: IRO
      country_code: US
      s3_bucket: orion-daton
      source_data_location: BRAND_IRO/IRO_SHOPIFY_MAIN_US/orders/
      object_suffixes: .csv,.parquet
      is_active: Y
      created_by: harsha
      updated_by: harsha
  - KAC_US:
      seller_id: N/A
      seller_code: KAC
      country_code: US
      s3_bucket: orion-daton
      source_data_location: BRAND_KAC/KAC_SHOPIFY_MAIN_US/orders/
      object_suffixes: .csv,.parquet
      is_active: Y
      created_by: harsha
      updated_by: harsha
  - KWX_US:
      seller_id: N/A
      seller_code: KWX
      country_code: US
      s3_bucket: orion-daton
      source_data_location: BRAND_KWX/KWX_SHOPIFY_MAIN_US/orders/
      object_suffixes: .csv,.parquet
      is_active: Y
      created_by: harsha
      updated_by: harsha
  - MRD_US:
      seller_id: N/A
      seller_code: MRD
      country_code: US
      s3_bucket: orion-daton
      source_data_location: BRAND_MRD/MRD_SHOPIFY_MAIN_US/orders/
      object_suffixes: .csv,.parquet
      is_active: Y
      created_by: harsha
      updated_by: harsha
  - NUM_US:
      seller_id: N/A
      seller_code: NUM
      country_code: US
      s3_bucket: orion-daton
      source_data_location: BRAND_NUM/NUM_SHOPIFY_MAIN_US/orders/
      object_suffixes: .csv,.parquet
      is_active: Y
      created_by: harsha
      updated_by: harsha
  - SUR_US:
      seller_id: N/A
      seller_code: SUR
      country_code: US
      s3_bucket: orion-daton
      source_data_location: BRAND_SUR/SUR_SHOPIFY_MAIN_US/orders/
      object_suffixes: .csv,.parquet
      is_active: Y
      created_by: harsha
      updated_by: harsha
  - TOL_US:
      seller_id: N/A
      seller_code: TOL
      country_code: US
      s3_bucket: orion-daton
      source_data_location: BRAND_TOL/TOL_SHOPIFY_MAIN_US/orders/
      object_suffixes: .csv,.parquet
      is_active: Y
      created_by: harsha
      updated_by: harsha

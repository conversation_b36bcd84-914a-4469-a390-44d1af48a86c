transform_job_name: fba_restock_inventory_transform
table_schema: DWH${env}.STAGING
table_name: FBA_RESTOCK_INVENTORY
dag_name: fba_restock_inventory_transform
dag_description: Transformation to upsert data into fba_restock_inventory_ingestion table
dag_tags: Transformation,fba,inventory
dag_dependencies: fba_restock_inventory_ingestion
dag_schedule_cron: 0 */2 * * *
transform_sql_file_name: merge_fba_restock_inventory.sql
is_active: Y
created_by: harsha
updated_by: harsha
data_quality_checks:
  - row_count:
      query_to_execute: >
        select case when "row_cnt" > 100 THEN 0 ELSE 1 END as "result"
        from(select count(1) as "row_cnt"
            from "DWH${env}"."STAGING"."FBA_RESTOCK_INVENTORY")
      is_active: N
      created_by: harsha
      updated_by: harsha
  - row_count_is_not_0:
      query_to_execute: >
        select case when "row_cnt" <> 0 THEN 0 ELSE 2 END as "result"
        from(select count(1) as "row_cnt"
        from "DWH${env}"."STAGING"."FBA_RESTOCK_INVENTORY")
      is_active: N
      created_by: harsha
      updated_by: harsha

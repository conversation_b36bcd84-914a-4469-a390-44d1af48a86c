import os
import argparse
import yaml
import hashlib
from commonlibs.db_connectors.postgres_pg8k import Postgres
from commonlibs.custom_logging.logger import get_logger

log = get_logger(__name__)

"""
Use this script to insert metdata into workflow_configurations table either for ingestion or transformation
Inorder to add configurations for ingestion for specific file(s)

python3 workflow_configurations_load.py -e 'dev' -f file1.yml -c 'Ingestion' -r 'us-east-1'

if more than file need to be processed
python3 workflow_configurations_load.py -e 'dev' -f file1.yml file2.yml -c 'Ingestion' -r 'us-east-1'

if all the files in ingestion need to be processed
python3 workflow_configurations_load.py -e 'dev' -p 'Y' -c 'Ingestion' -r 'us-east-1'

To run transformations
python3 workflow_configurations_load.py -e 'dev' -p 'Y' -c 'Transformation' -r 'us-east-1'
python3 workflow_configurations_load.py -e 'dev' -f file1.yml file2.yml -c 'Transformation' -r 'us-east-1'

"""


def set_report_ingestion_configurations(env, region_name, files_to_process=None, process_all=False):
    """
    This function will read list of file(s) that are passed as input, loop through each yml file, checks whether
    configurations are already present. If configurations are already present, process will upsert data if anything changed.
    If the configurations are not present, then configurations are inserted. These are specific to ingestion and the
    following are the tables where the data is inserted
    1. workflow_configurations.api_report_info
    2. workflow_configurations.seller_report_metadata_info
    Either files_to_process or process_all should be passed
    :param env: run environment. By default its set to prod. variable that gets impacted is stage_table_schema
    :param region_name: region where the secrets are stored
    :param files_to_process: list of ingestion files to process. These files are present at
    <local dev>/aurora_db/workflow_configurations/data_files/ingestion/ *.yml.
    :param process_all: boolean either True/ False
    :return:
    """
    env = f'_{env}' if env=='dev' else ''
    if not files_to_process and not process_all:
        log.info("""Atleast one of the parameters should be passed
        1. files_to_process (list of files)
        2. process_all (set it to true)
        If both parameters evaluate to False, process will not continue
        """)
        exit()
    dir_ = '/Users/<USER>/workspace/dataplatform-core-modules/aurora_db/workflow_configurations/data_files/ingestion'
    # if process_all is set to true, get all the yml files in the ingestion folder to process
    if process_all:
        files = [file_name for file_name in os.listdir(dir_) if file_name.endswith('yml')]
    else:
        files = files_to_process
    # When running in prod, update aws region to us-east-2. This can be done by running
    # aws configure and set the region to us-east-2. Or open .aws/configure and change region to us-east-2
    # this will ensure, correct region's secret manager secrets are retrieved
    pgc = Postgres(secret_name='dataplatform/aurorapostgres/etl_user', region_name=region_name)

    # traverse through each file
    for file_ in files:
        log.info(f'Processing {file_}')
        with open(f'{dir_}/{file_}', 'r') as inp:
            configs = yaml.safe_load(inp)

        # get all the variables
        report_name = configs.get('report_name')
        channel = configs.get('channel')
        report_api = configs.get('report_api')
        stage_table_schema = configs.get('stage_table_schema').replace('${env}',env)
        stage_table_name = configs.get('stage_table_name')
        ingestion_glue_job = configs.get('ingestion_glue_job')
        report_refresh_type = configs.get('report_refresh_type')
        compressed_dataset_s3_location = configs.get('compressed_dataset_s3_location')
        compressed_data_glue_db = configs.get('compressed_data_glue_db')
        compressed_data_glue_crawler = configs.get('compressed_data_glue_crawler')
        dag_name = configs.get('dag_name')
        dag_schedule_cron = configs.get('dag_schedule_cron')
        dag_description = configs.get('dag_description')
        dag_tags = configs.get('dag_tags')
        is_active = configs.get('is_active')
        created_by = configs.get('created_by')
        updated_by = configs.get('updated_by')

        # check if the report already exists
        query = f"""SELECT 1 AS exists
                    FROM workflow_configurations.api_report_info
                    WHERE report_name = '{report_name}'
                    AND channel='{channel}'
                    AND report_api = '{report_api}'
                """
        resp, _ = pgc.get_data(query, as_dict=True)
        if resp:
            exists = resp[0].get('exists')
        else:
            exists = False

        # If the data exists, try an update
        # If the data doesn't exist insert a new row
        if exists:
            query = f"""
                    UPDATE workflow_configurations.api_report_info
                        SET stage_table_schema = '{stage_table_schema}'
                            , stage_table_name = '{stage_table_name}'
                            , report_refresh_type = '{report_refresh_type}'
                            , ingestion_glue_job = '{ingestion_glue_job}'
                            , compressed_dataset_s3_location = '{compressed_dataset_s3_location}'
                            , compressed_data_glue_db = '{compressed_data_glue_db}'
                            , compressed_data_glue_crawler = '{compressed_data_glue_crawler}'
                            , dag_name = '{dag_name}'
                            , dag_schedule_cron = '{dag_schedule_cron}'
                            , dag_description = '{dag_description}'
                            , dag_tags = '{dag_tags}'
                            , is_active = '{is_active}'
                            , updated_timestamp_utc = CURRENT_TIMESTAMP
                            ,updated_by = '{updated_by}'
                        WHERE report_name = '{report_name}'
                        AND channel = '{channel}'
                        AND report_api = '{report_api}'
                        AND (stage_table_schema <> '{stage_table_schema}'
                            OR stage_table_name <> '{stage_table_name}'
                            OR report_refresh_type <> '{report_refresh_type}'
                            OR ingestion_glue_job <> '{ingestion_glue_job}'
                            OR compressed_dataset_s3_location <> '{compressed_dataset_s3_location}'
                            OR compressed_data_glue_db <> '{compressed_data_glue_db}'
                            OR compressed_data_glue_crawler <> '{compressed_data_glue_crawler}'
                            OR dag_name <> '{dag_name}'
                            OR dag_schedule_cron <> '{dag_schedule_cron}'
                            OR dag_description <> '{dag_description}'
                            OR dag_tags <> '{dag_tags}'
                            OR is_active = '{is_active}')
                    """
            pgc.execute_statement(query)
        else:
            query = f"""
                    INSERT INTO workflow_configurations.api_report_info
                    (
                        report_id
                        ,channel
                        ,report_api
                        ,report_name
                        ,stage_table_schema
                        ,stage_table_name
                        ,report_refresh_type
                        ,ingestion_glue_job
                        ,compressed_dataset_s3_location
                        ,compressed_data_glue_db
                        ,compressed_data_glue_crawler
                        ,dag_name
                        ,dag_schedule_cron
                        ,dag_description
                        ,dag_tags
                        ,is_active
                        ,created_by
                        ,updated_by
                    )
                    SELECT md5('{channel}' || '{report_api}' || '{report_name}')
                            ,'{channel}'
                            ,'{report_api}'
                            ,'{report_name}'
                            ,'{stage_table_schema}'
                            ,'{stage_table_name}'
                            ,'{report_refresh_type}'
                            ,'{ingestion_glue_job}'
                            ,'{compressed_dataset_s3_location}'
                            ,'{compressed_data_glue_db}'
                            ,'{compressed_data_glue_crawler}'
                            ,'{dag_name}'
                            ,'{dag_schedule_cron}'
                            ,'{dag_description}'
                            ,'{dag_tags}'
                            ,'{is_active}'
                            ,'{created_by}'
                            ,'{updated_by}'
                    """

            pgc.execute_statement(query)

        # get the report_id
        query = f"""
                SELECT report_id
                FROM workflow_configurations.api_report_info
                WHERE report_name = '{report_name}'
                AND channel='{channel}'
                AND report_api = '{report_api}'
                """
        resp, _ = pgc.get_data(query, as_dict=True)
        report_id = resp[0].get('report_id')

        # iterate through all the seller_report_configurations
        for seller_metadata in configs.get('seller_report_configurations'):
            for seller_configs in seller_metadata.values():
                seller_id = seller_configs.get('seller_id')
                country_code = seller_configs.get('country_code')
                seller_code = seller_configs.get('seller_code')
                s3_bucket = seller_configs.get('s3_bucket')
                source_data_location = seller_configs.get('source_data_location')
                object_suffixes = seller_configs.get('object_suffixes')
                is_active = seller_configs.get('is_active')

                # check if metadata already exists in seller_report_metadata_info
                query = f"""
                            SELECT 1 AS exists
                            FROM workflow_configurations.seller_report_metadata_info
                            WHERE seller_id = '{seller_id}'
                            AND seller_code = '{seller_code}'
                            AND country_code = '{country_code}'
                            AND report_id = '{report_id}'
                        """
                resp, _ = pgc.get_data(query, as_dict=True)
                if resp:
                    exists = resp[0].get('exists')
                else:
                    exists = False
                # If data exists already, do an update
                # If data doesn't exist, do an insert
                if exists:
                    query = f"""
                            UPDATE workflow_configurations.seller_report_metadata_info
                                SET s3_bucket = '{s3_bucket}'
                                    ,source_data_location = '{source_data_location}'
                                    ,object_suffixes = '{object_suffixes}'
                                    ,is_active = '{is_active}'
                                    ,updated_by = '{updated_by}'
                                    ,updated_timestamp_utc = CURRENT_TIMESTAMP
                            WHERE seller_id = '{seller_id}'
                            AND seller_code = '{seller_code}'
                            AND country_code = '{country_code}'
                            AND report_id = '{report_id}'
                            AND (s3_bucket <> '{s3_bucket}'
                            OR source_data_location <> '{source_data_location}'
                            OR object_suffixes <> '{object_suffixes}'
                            OR is_active <> '{is_active}')
                            """
                    pgc.execute_statement(query)
                else:
                    query = f"""
                            INSERT INTO workflow_configurations.seller_report_metadata_info
                            (
                                report_metadata_id
                                ,report_id
                                ,seller_code
                                ,seller_id
                                ,country_code
                                ,s3_bucket
                                ,source_data_location
                                ,object_suffixes
                                ,is_active
                                ,created_by
                                ,updated_by
                            )
                            SELECT md5('{seller_code}' || '{country_code}' || '{seller_id}' || '{report_id}')
                                    ,'{report_id}'
                                    ,'{seller_code}'
                                    ,'{seller_id}'
                                    ,'{country_code}'
                                    ,'{s3_bucket}'
                                    ,'{source_data_location}'
                                    ,'{object_suffixes}'
                                    ,'{is_active}'
                                    ,'{created_by}'
                                    ,'{updated_by}'
                              """
                    pgc.execute_statement(query)


def set_transform_configurations(env, region_name, files_to_process=None, process_all=False):
    """
    This function will read configurations from YML file and adds/ updates relevant metadata to
    transform_job_info and transform_job_audit_info tables
    :param env: either dev or prod
    :param region_name: aws region where secrets are stored
    :param files_to_process: list of files to process
    :param process_all: if its true
    :return:
    """
    env = f'_{env}' if env == 'DEV' else ''
    if not files_to_process and not process_all:
        log.info("""Atleast one of the parameters should be passed
        1. files_to_process (list of files)
        2. process_all (set it to true)
        If both parameters evaluate to False, process will not continue
        """)
        exit()
    dir_ = '/Users/<USER>/workspace/dataplatform-core-modules/aurora_db/workflow_configurations/data_files/transformation'
    # if process_all is set to true, get all the yml files in the ingestion folder to process
    if process_all:
        files = [file_name for file_name in os.listdir(dir_) if file_name.endswith('yml')]
    else:
        files = files_to_process
    # When running in prod, update aws region to us-east-2. This can be done by running
    # aws configure and set the region to us-east-2. Or open .aws/configure and change region to us-east-2
    # this will ensure, correct region's secret manager secrets are retrieved
    pgc = Postgres(secret_name='dataplatform/aurorapostgres/etl_user', region_name=region_name)

    # traverse through each file
    for file_ in files:
        log.info(f'Processing {file_}')
        with open(f'{dir_}/{file_}', 'r') as inp:
            configs = yaml.safe_load(inp)

        # get transformation properties
        transform_job_name = configs.get('transform_job_name')
        table_schema = configs.get('table_schema').replace('${env}',env)
        table_name = configs.get('table_name')
        dag_name = configs.get('dag_name')
        dag_description = configs.get('dag_description')
        dag_tags = configs.get('dag_tags')
        dag_schedule_cron = configs.get('dag_schedule_cron')
        dag_dependencies = configs.get('dag_dependencies')
        transform_sql_file_name = configs.get('transform_sql_file_name')
        is_active = configs.get('is_active')
        created_by = configs.get('created_by')
        updated_by = configs.get('updated_by')

        query = f"""
                SELECT 1 AS exists
                FROM workflow_configurations.transform_job_info
                WHERE transform_job_name = '{transform_job_name}'
                """
        resp, _ = pgc.get_data(query, as_dict=True)
        if resp:
            exists = resp[0].get('exists')
        else:
            exists = False

        if exists:
            query = f"""
                    UPDATE workflow_configurations.transform_job_info
                        SET table_schema = '{table_schema}'
                            ,table_name = '{table_name}'
                            ,dependency_dags = '{dag_dependencies}'
                            ,transform_sql_file_name = '{transform_sql_file_name}'
                            ,dag_name = '{dag_name}'
                            ,dag_schedule_cron = '{dag_schedule_cron}'
                            ,dag_description = '{dag_description}'
                            ,dag_tags = '{dag_tags}'
                            ,is_active = '{is_active}'
                            ,updated_by = '{updated_by}'
                            ,updated_timestamp_utc = CURRENT_TIMESTAMP
                    WHERE transform_job_name = '{transform_job_name}'
                    AND (table_schema <> '{table_schema}'
                            OR table_name <> '{table_name}'
                            OR dependency_dags <> '{dag_dependencies}'
                            OR transform_sql_file_name <> '{transform_sql_file_name}'
                            OR dag_name <> '{dag_name}'
                            OR dag_schedule_cron <> '{dag_schedule_cron}'
                            OR dag_description <> '{dag_description}'
                            OR dag_tags <> '{dag_tags}'
                            OR is_active <> '{is_active}')
                    """
            pgc.execute_statement(query)
        else:
            query = f"""
                    INSERT INTO workflow_configurations.transform_job_info
                    (
                        transform_job_id
                        ,transform_job_name
                        ,table_schema
                        ,table_name
                        ,dependency_dags
                        ,transform_sql_file_name
                        ,dag_name
                        ,dag_schedule_cron
                        ,dag_description
                        ,dag_tags
                        ,is_active
                        ,created_by
                        ,updated_by
                    )
                    SELECT md5('{transform_job_name}')
                            ,'{transform_job_name}'
                            ,'{table_schema}'
                            ,'{table_name}'
                            ,'{dag_dependencies}'
                            ,'{transform_sql_file_name}'
                            ,'{dag_name}'
                            ,'{dag_schedule_cron}'
                            ,'{dag_description}'
                            ,'{dag_tags}'
                            ,'{is_active}'
                            ,'{created_by}'
                            ,'{updated_by}'
                    """
            pgc.execute_statement(query)

        query = f"""
                SELECT transform_job_id 
                FROM workflow_configurations.transform_job_info
                WHERE transform_job_name = '{transform_job_name}'
                """
        resp, _ = pgc.get_data(query, as_dict=True)
        transform_job_id = resp[0].get('transform_job_id')

        # Loop through audit tests
        for audit_test in configs.get('data_quality_checks'):
            for audit_test_name, configurations in audit_test.items():
                query_to_execute = configurations.get('query_to_execute').replace('${env}', env)
                is_active = configurations.get('is_active')
                created_by = configurations.get('created_by')
                updated_by = configurations.get('updated_by')

                query = f"""
                        SELECT 1 AS exists
                        FROM workflow_configurations.transform_job_audit_info
                        WHERE transform_job_id = '{transform_job_id}'
                        AND audit_test_name = '{audit_test_name}'
                        """
                resp, _ = pgc.get_data(query, as_dict=True)
                if resp:
                    exists = resp[0].get('exists')
                else:
                    exists = False
                if exists:
                    query = f"""
                            UPDATE workflow_configurations.transform_job_audit_info
                                SET query_to_execute = '{query_to_execute}'
                                    ,is_active = '{is_active}'
                                    ,updated_by = '{updated_by}'
                                    ,updated_timestamp_utc = CURRENT_TIMESTAMP
                            WHERE transform_job_id = '{transform_job_id}'
                            AND audit_test_name = '{audit_test_name}'
                            AND ( query_to_execute <> '{query_to_execute}'
                                    OR is_active <> '{is_active}'
                                )
                            """
                    pgc.execute_statement(query)
                else:
                    query = f"""
                            INSERT INTO workflow_configurations.transform_job_audit_info
                            (
                                transform_job_audit_id
                                ,transform_job_id
                                ,audit_test_name
                                ,query_to_execute
                                ,is_active
                                ,created_by
                                ,updated_by
                            )
                            SELECT uuid_in(md5(random()::text || clock_timestamp()::text)::cstring)
                                    ,'{transform_job_id}'
                                    ,'{audit_test_name}'
                                    ,'{query_to_execute}'
                                    ,'{is_active}'
                                    ,'{created_by}'
                                    ,'{updated_by}'
                            """
                    pgc.execute_statement(query)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='This script is used to insert/ update configurations')

    parser.add_argument('-e', '--environment', required=True,
                        help='Where the configurations should run prod/ dev')
    parser.add_argument('-f', '--fileList', required=False, nargs='+',
                        help='List of files to process')
    parser.add_argument('-p', '--processAll', required=False,
                        help='Pass yes and all the files will be processed')
    parser.add_argument('-c', '--configType', required=True,
                        help='Whether ingestion or transformation')
    parser.add_argument('-r', '--regionName', required=True,
                        help="aws region name. For dev its us-east-1 and for prod its us-east-2")
    job_args = parser.parse_args()

    env = job_args.environment
    files_to_process = job_args.fileList if job_args.fileList else None
    process_all = True if job_args.processAll == 'Y' else False
    config_type = job_args.configType
    region_name = job_args.regionName

    log.info(f"""Passed Arguments
                1. Environment: {env}
                2. Files to Process: {files_to_process}
                3. Process All: {process_all}
                4. Region Name: {region_name}""")
    if config_type == 'Ingestion':
        set_report_ingestion_configurations (env, region_name=region_name, files_to_process=files_to_process, process_all=process_all)
    else:
        set_transform_configurations(env.upper(), region_name=region_name, files_to_process=files_to_process,process_all=process_all)



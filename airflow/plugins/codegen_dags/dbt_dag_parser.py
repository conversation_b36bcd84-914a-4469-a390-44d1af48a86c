"""
This file defines the DAG parser module that reads the DBT metadata and convert into the Airflow dags.

The output DAG contains several components:

- `Sensor` taskgroup: Contains sensors to wait external DAGs or Task. If no sensor, it will be converted to the task that is the root of graph.
- `DbtRun` taskgroup: Dbt models will be displayed and executed in this taskgroup. The Dbt tasks follow the same dependencies in the Dbt models.
- `DbtTest`: The `dbt test` command will be executed after the Dbt run models finished. Each dbt project will store the result into separated folder.
- `DbtTrigger` taskgroup: Contains trigger operators to trigger another tasks/DAGs.


Notes:
    If there are any `critical-test` tag in the model after running the `dbt run` model, the `dbt test [model_name]` must be success after going to the next steps.
"""

import json
import logging
import os
import sys

from dbt_operators.dbt import DbtRunOperator, DbtTestOperator
from tasklib import alerts

from airflow.configuration import AIRFLOW_HOME
from airflow.hooks.base import BaseHook
from airflow.operators.bash import BashOperator
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.utils.task_group import TaskGroup

DBT_DAG_METADATA = f"{AIRFLOW_HOME}/dags/dbt_metadata/files"
NO_TEST_TAG = "no-test"
CRITICAL_TEST = "critical-test"


class DbtDagParser:
    """
    A utility class that parses out a dbt project and creates the respective task groups

    :param dag: The Airflow DAG
    :param dbt_global_cli_flags: Any global flags for the dbt CLI
    :param dbt_project_dir: The directory containing the dbt_project.yml
    :param dbt_profiles_dir: The directory containing the profiles.yml
    :param dbt_target: The dbt target profile (e.g. dev, prod)
    :param dbt_tag: Limit dbt models to this tag if specified.
    """

    verb_operator_mapping = {
        "test": DbtTestOperator,
        "run": DbtRunOperator,
    }

    def __init__(
        self,
        dag=None,
        dbt_global_cli_flags=None,
        dbt_project_dir=None,
        dbt_profiles_dir=None,
        dbt_target=None,
        dbt_tag=None,
        included_test=None,
        sensors=None,
        triggers=None,
        external_metadata_filename=None,
        dbt_model_suffix=None,
        push_metadata=None,
        is_incremental=False,
    ):
        if sensors is None:
            sensors = {}
        self.dag = dag
        self.dbt_global_cli_flags = dbt_global_cli_flags
        self.dbt_project_dir = dbt_project_dir
        self.dbt_project_name = dbt_project_dir.split("/")[-1]
        self.dbt_profiles_dir = dbt_profiles_dir
        self.dbt_target = dbt_target
        self.dbt_tag = dbt_tag
        self.external_metadata_filename = external_metadata_filename
        self.dbt_model_suffix = dbt_model_suffix
        self.included_test = included_test
        self.sensors = sensors
        self.triggers = triggers
        self.push_metadata = push_metadata
        self.is_incremental = is_incremental

        self.dbt_run_group = TaskGroup("dbt_run", dag=self.dag)
        if self.included_test:
            # self.dbt_test_group = DbtTestOperator(
            #     dag=self.dag,
            #     task_id="dbt_test",
            #     selector_flag="--selector",
            #     append_env=True,
            #     dbt_global_cli_flags=self.dbt_global_cli_flags,
            #     dbt_target=self.dbt_target,
            #     dbt_profiles_dir=self.dbt_profiles_dir,
            #     dbt_project_dir=self.dbt_project_dir,
            #     dbt_model_suffix=self.dbt_model_suffix,
            # )
            self.dbt_test_group = EmptyOperator(dag=dag, task_id="dbt_test")
        else:
            self.dbt_test_group = EmptyOperator(dag=dag, task_id="dbt_end")
        if self.sensors:
            self.dbt_sensor_group = TaskGroup("dbt_sensor", dag=self.dag)
        else:
            self.dbt_sensor_group = EmptyOperator(dag=dag, task_id="start")
        if self.is_incremental:
            # import tasklib.workflow as tlw
            #
            # self.incremental_task = PythonOperator(
            #     task_id="get_workflow_params",
            #     python_callable=tlw.get_workflow_params,
            #     provide_context=True,
            #     on_failure_callback=alerts.send_failure_alert,
            #     op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
            # )
            # WF_PARAMS_EXPR = (
            #     "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"
            # )
            # self.update_wf_task = PythonOperator(
            #     task_id="task_update_workflow_parameters",
            #     python_callable=tlw.update_workflow_params,
            #     op_kwargs={"wf_params": WF_PARAMS_EXPR},
            #     provide_context=True,
            #     on_failure_callback=alerts.send_failure_alert,
            # )
            self.incremental_task = EmptyOperator(dag=dag, task_id="get_workflow_params")
            self.update_wf_task = EmptyOperator(dag=dag, task_id="task_update_workflow_parameters")
        if self.triggers:
            self.dbt_trigger_group = TaskGroup("dbt_trigger", dag=self.dag)

    def load_dbt_manifest(self):
        """
        Helper function to load the dbt manifest file.

        Returns: A JSON object containing the dbt manifest content.

        """
        manifest_path = os.path.join(
            "",
            f"{DBT_DAG_METADATA}/{self.external_metadata_filename or self.dag.dag_id}",
        )
        with open(manifest_path) as f:
            file_content = json.load(f)
        return file_content

    def make_dbt_task(self, node_name, dbt_verb, overwrite_task_group=None, **kwargs):
        """
        Takes the manifest JSON content and returns a BashOperator task
        to run a dbt command.

        Args:
            node_name: The name of the node
            dbt_verb: 'run' or 'test'
            overwrite_task_group: TaskGroup

        Returns: A BashOperator task that runs the respective dbt command

        """
        if dbt_verb == "test":
            node_name = node_name.replace(
                "model", "test"
            )  # Just a cosmetic renaming of the task
            task_group = self.dbt_test_group
        else:
            task_group = self.dbt_run_group
        if overwrite_task_group:
            task_group = overwrite_task_group

        dbt_task = self.verb_operator_mapping[dbt_verb](
            task_id=node_name,
            task_group=task_group,
            dag=self.dag,
            append_env=True,
            dbt_global_cli_flags=self.dbt_global_cli_flags,
            dbt_target=self.dbt_target,
            dbt_profiles_dir=self.dbt_profiles_dir,
            dbt_project_dir=self.dbt_project_dir,
            dbt_model_suffix=self.dbt_model_suffix,
            project=kwargs.get("project"),
            is_incremental=self.is_incremental,
            schema=kwargs.get("schema"),
            cwd="{{ dag_run.dag.folder }}",
        )
        logging.info("Created task: %s", node_name)
        return dbt_task

    def make_dbt_task_groups(self):
        """
        Parse out a JSON file and populates the task groups with dbt tasks

        Returns: None

        """
        if self.sensors:
            self.make_sensor_task_groups()
        if self.triggers:
            self.make_trigger_task_groups()
        manifest_json = self.load_dbt_manifest()
        dbt_tasks = self.create_task(manifest_json)
        if self.push_metadata:
            push_run_results = EmptyOperator(task_id="push_run_results")
        else:
            push_run_results = EmptyOperator(task_id="end")

        self.create_dependencies(manifest_json, dbt_tasks)
        self.dbt_run_group >> self.dbt_test_group
        if self.is_incremental:
            self.dbt_sensor_group >> self.incremental_task
            self.incremental_task >> self.dbt_run_group
            self.dbt_test_group >> self.update_wf_task
        else:
            self.dbt_sensor_group >> self.dbt_run_group
        if self.push_metadata:
            self.dbt_test_group >> push_run_results

    def create_task(self, manifest_json):
        dbt_tasks = {}
        for node_name in manifest_json["nodes"].keys():
            if node_name.split(".")[0] != "model":
                continue
            node = manifest_json["nodes"][node_name]
            tags = node["tags"]
            node_metadata = {
                "schema": node["schema"],
                "project": node["package_name"],
            }
            if not ((self.dbt_tag and self.dbt_tag in tags) or not self.dbt_tag):
                continue

            dbt_tasks[node_name] = self.make_dbt_task(node_name, "run", **node_metadata)
            if not (self.included_test or CRITICAL_TEST in tags):
                continue
            if NO_TEST_TAG in tags:
                continue
            node_test = node_name.replace("model", "test")
            if CRITICAL_TEST in tags:
                test_task = self.make_dbt_task(node_name, "test", self.dbt_run_group)
                dbt_tasks[node_name] >> test_task
                dbt_tasks[node_test] = test_task
        return dbt_tasks

    def create_dependencies(self, manifest_json, dbt_tasks):
        for node_name in manifest_json["nodes"].keys():
            if node_name.split(".")[0] != "model":
                continue
            tags = manifest_json["nodes"][node_name]["tags"]
            if not ((self.dbt_tag and self.dbt_tag in tags) or not self.dbt_tag):
                continue
            for upstream_node in manifest_json["nodes"][node_name]["depends_on"][
                "nodes"
            ]:
                upstream_node_type = upstream_node.split(".")[0]
                if upstream_node_type != "model":
                    continue
                upstream_tags = manifest_json["nodes"][upstream_node]["tags"]
                if CRITICAL_TEST not in upstream_tags:
                    dbt_tasks[upstream_node] >> dbt_tasks[node_name]
                else:
                    critical_test_node = upstream_node.replace("model", "test")
                    (
                        dbt_tasks[upstream_node]
                        >> dbt_tasks[critical_test_node]
                        >> dbt_tasks[node_name]
                    )

    def get_dbt_run_group(self):
        """
        Getter method to retrieve the previously constructed dbt tasks.

        Returns: An Airflow task group with dbt run nodes.

        """
        return self.dbt_run_group

    def get_dbt_test_group(self):
        """
        Getter method to retrieve the previously constructed dbt tasks.

        Returns: An Airflow task group with dbt test nodes.

        """
        return self.dbt_test_group

    def make_sensor_task_groups(self):
        for sensor in self.sensors:
            sensor_type = sensor.pop("sensor_class")
            getattr(sys.modules[__name__], sensor_type)(
                dag=self.dag, task_group=self.dbt_sensor_group, **sensor
            )

    def make_trigger_task_groups(self):
        for trigger in self.triggers:
            trigger_type = trigger.pop("trigger_class")
            getattr(sys.modules[__name__], trigger_type)(
                dag=self.dag, task_group=self.dbt_trigger_group, **trigger
            )
        self.dbt_test_group >> self.dbt_trigger_group

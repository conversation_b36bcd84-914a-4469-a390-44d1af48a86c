import pendulum
import yaml
from codegen_dags.dbt_dag_parser import DbtDagParser

from airflow import D<PERSON>


def create_dag(dag_config_file_path):
    project_dir = "/".join(dag_config_file_path.split("/")[:-1])
    with open(dag_config_file_path, "r") as f:
        dag_configs = yaml.safe_load(f)
    dags = []
    for config in dag_configs["selectors"]:
        dag = _create_dag(config)
        make_task_group(config, project_dir, dag)
        dags.append((config["name"], dag))
    return dags


def _create_dag(config):
    dag_config = config["dag"]
    addition_dag_configs = dag_config.get("addition_args") or {}
    addition_dag_configs["concurrency"] = addition_dag_configs.get("concurrency") or 5
    dag = DAG(
        config["name"],
        start_date=pendulum.parse(dag_config["start_date"]),
        description=dag_config["description"],
        schedule=dag_config["schedule"],
        catchup=dag_config["catchup"],
        tags=dag_config.get("tags"),
        doc_md=__doc__,
        params={"workflow_name": config["name"], "author": "thinh.nguyen"},
        **addition_dag_configs,
    )
    return dag


def make_task_group(config, project_dir, dag):
    dbt_config = config["dbt_config"] or {}
    addition_dbt_configs = dbt_config.get("addition_args") or {}
    parser = DbtDagParser(
        dbt_global_cli_flags=dbt_config.get("flags") or "",
        dbt_project_dir=f"{project_dir}{dbt_config.get('dbt_project_dir') or ''}",
        dbt_profiles_dir=f"{project_dir}{dbt_config.get('profiles_dir') or ''}",
        dbt_target=f"{dbt_config.get('dbt_target') or 'dev'}",
        dag=dag,
        sensors=config["dag"].get("sensors"),
        triggers=config["dag"].get("triggers"),
        included_test=config["dag"].get("included_test"),
        dbt_model_suffix=config["dag"].get("dbt_model_suffix"),
        push_metadata=config["dag"].get("push_metadata"),
        is_incremental=dbt_config.get("is_incremental"),
        **addition_dbt_configs,
    )
    parser.make_dbt_task_groups()

import logging
import yaml
from airflow.models import Variable
from collections import defaultdict
from dataclasses import dataclass
from typing import List
from yaml.loader import SafeLoader

from tasklib.alerts_logging import log_record
from tasklib.alerts_snooze import check_if_alert_snoozed, read_snooze_alert_data, read_snooze_log_data
from helpers.helper import resolve_db_variables, set_query_tag
from tasklib import alerts
from tasklib import config as tk_cfg
from tasklib import constants

log = logging.getLogger(__name__)


# ---- [ Data Quality Checks V1 ] ----
#   -- DQ Error Codes --
DQ_CODE_SOFT_ERROR = 1
DQ_CODE_HARD_ERROR = 2


@dataclass
class DqRecord:
    test_id: str
    query: str
    enabled: bool = True


# -- DQ checks code generation --------------------------------------------------------------------
def gen_check_if_nulls(tb_name: str, field_list: list, hard_alert: bool = False) -> str:
    """
    Generates SQL code to verify that there are no Null in the fields
    :param tb_name: The table to check
    :param field_list: The list of fields to test
    :param hard_alert: If true alarms with 2 if fails otherwise alarms with 1
    :return: Error level code (1=Soft, 2=Hard)
    """

    if not field_list:
        return """SELECT 0 as "result" """
    alert_code = DQ_CODE_HARD_ERROR if hard_alert else DQ_CODE_SOFT_ERROR
    code_pad = 17
    cmp_code = []
    for idx, fld in enumerate(field_list):
        if idx == 0:
            cmp_code.append(f'WHEN count(*) = count({fld})')
        else:
            cmp_code.append(' ' * code_pad + f' AND count(*) = count({fld})')
    cmp_code_str = '\n'.join(cmp_code)
    sql_tpl = f"""
        SELECT 
            CASE
                {cmp_code_str}
                THEN 0 ELSE {alert_code}
            END AS "result"
        FROM {tb_name}     
    """
    return sql_tpl


def gen_check_if_row_count_greater_than_x(tb_name: str, x: int, hard_alert: bool = False) -> str:
    """
    Generates SQL code to verify that the row count of a table is greater than X
    :param tb_name: The table to check
    :param x: The value to compare
    :param hard_alert: If true alarms with 2 if fails otherwise alarms with 1
    :return: Error level code (1=Soft, 2=Hard)
    """
    alert_code = DQ_CODE_HARD_ERROR if hard_alert else DQ_CODE_SOFT_ERROR
    sql_tpl = f"""
        SELECT  
           CASE WHEN "row_cnt" > {x} THEN 0 ELSE {alert_code} END as "result" 
        FROM ( 
          SELECT count(1) as "row_cnt" FROM {tb_name}
        )
    """
    return sql_tpl


def gen_check_unique_key(tb_name: str, field_list: list, hard_alert: bool = False) -> str:
    """
    Generates SQL code to verify that there are no duplicates for the field list combination
    :param tb_name: The table to check
    :param field_list: The list of fields to test
    :param hard_alert: If true alarms with 2 if fails otherwise alarms with 1
    :return: Error level code (1=Soft, 2=Hard)
    """
    alert_code = DQ_CODE_HARD_ERROR if hard_alert else DQ_CODE_SOFT_ERROR
    field_lst_str = ','.join(field_list)
    sql_tpl = f"""
        SELECT 
            CASE WHEN COUNT(1) > 0 THEN {alert_code} ELSE 0 END as "result"
        FROM (
            SELECT 1
            FROM {tb_name}
            GROUP BY {field_lst_str}
            HAVING COUNT(1) > 1
            LIMIT 1
        ) T
    """
    return sql_tpl


# -- Helper functions for executing DQ checks -----------------------------------------------------
def variable_value_present(airflow_variable: str, value: str) -> bool:
    """
    Checks if airflow variable contain a value
    :param airflow_variable: The airflow variable
    :param value: The value to compare to
    :return: True if value present False otherwise

    """
    value_list = Variable.get(airflow_variable)
    log.info(f'Variable {airflow_variable} values: {value_list}')
    if value in value_list:
        log.info(f'Value "{value}" found.')
        return True
    log.info(f'Value "{value}" not found.')
    return False


def _load_sql_file(file_name) -> str:
    dags_folder = tk_cfg.get_dags_folder()
    sql_file_path = f'{dags_folder}/data_transformations/{file_name}'
    log.info(f'loading file {sql_file_path}...')
    with open(sql_file_path, 'r') as in_file:
        sql_query = in_file.read()
        sql_query = sql_query.strip()
    return resolve_db_variables(sql_query)


def _load_yaml_commands(yaml_file: str, use_default_path: bool = True) -> List[DqRecord]:
    dags_folder = tk_cfg.get_dags_folder()
    if use_default_path:
        yaml_file_path = f'{dags_folder}/data_transformations/{yaml_file}'
    else:
        yaml_file_path = f'{dags_folder}/{yaml_file}'
    with open(yaml_file_path, 'r') as file:
        data = list(yaml.load_all(file, Loader=SafeLoader))
        result = []
        for record in data:
            resolved_query = resolve_db_variables(record['query'])
            result.append(
                DqRecord(
                    test_id=record['test_id'],
                    query=resolved_query,
                    enabled=record['enabled'])
            )
        return result


# -- Run DQ tests ---------------------------------------------------------------------------------
def run_dq_tests(dq_id, **context):
    """
    This function will execute audit tests for the current transformation and alerts via slack if there
    are any audit test failures
    :param dq_id
    :param context:
    :return:
    """
    from db_connectors.pg_connector import Postgres
    from db_connectors.sf_connector import Snowflake
    from helpers.helper import traceback_fmt
    import json

    pgc = Postgres()
    sfc = Snowflake()
    ti = context['ti']

    dag_run_id = context['dag'].dag_id + '_' + context['run_id']
    log.info('DAG run id : %s', dag_run_id)

    results = dict()
    failed_tests = []

    # Get all the audit test to run for this transformation
    query = f"""SELECT * FROM workflow_configurations.udf_get_audit_tests_for_transform_job('{dq_id}')"""
    data, _ = pgc.get_data(query, as_dict=True)

    # If there are audit tests, execute each audit test and store the results in results dictionary
    # The format will be {'audit_test_name': (0,1,2)}
    if data:
        for audit_test in data:
            audit_test_name = audit_test.get('audit_test_name')
            query_to_execute = audit_test.get('query_to_execute')
            audit_result, _ = sfc.get_data(query_to_execute, as_dict=True)
            results[audit_test_name] = audit_result[0].get('result')
            # if the output of query is > 0 then add it to failed_tests list
            if audit_result[0].get('result') > 0:
                failed_tests.append(audit_test_name)
        # convert the results dict to string to store it as json object in the backend
        res_str = json.dumps(results)
        # get what the max value for the audit test results is.
        max_res = max(results.values())
        pass_results = False
        if max_res == 0:
            pass_results = True

        query = f"""call workflow_configurations.sp_set_audit_test_results_for_transformation('{dag_run_id}',
                                                                                                '{dq_id}',
                                                                                                '{res_str}',
                                                                                       {pass_results})"""
        pgc.execute_statement(query)

        # Check if either audit fail alert or dag failure should happen
        # if max_res is 1 then is a soft failure
        # if its 2 then is a hard failure
        try:
            if failed_tests and max_res == 1:
                alerts.send_audit_test_failure_alert(
                    dq_id=dq_id, failed_tests=failed_tests,
                    severity=constants.DQ_FAILURE_SEVERITY_SOFT, **context)
            if failed_tests and max_res == 2:
                alerts.send_audit_test_failure_alert(
                    dq_id=dq_id, failed_tests=failed_tests,
                    severity=constants.DQ_FAILURE_SEVERITY_HARD, **context)
                raise Exception('Audit tests failed with hard failure alert. Failing the task')
        except Exception as err:
            msg = traceback_fmt(err)
            log.error(msg)
            raise


def run_dq_string(**context):
    """
    Runs query in the string and captures the result
    :param context: dictionary with values
    :return: None, side effect of sending Slack messages

    The result should be one of the following values:
    0 = Success
    1 = Soft Failure
    2 = Hard Failure
    """
    sql_query = resolve_db_variables(context['sql_query'])
    run_dq(context, dq_query=sql_query)


def run_dq_sql_file(**context):
    """
    Executes queries stored in a file and captures the result value
    TODO: To be deprecated and replaced with yaml call
    :param context: dictionary with values
    :return: None, side effect of sending Slack messages

    The result should be one of the following values
    0 = Success
    1 = Soft Failure
    2 = Hard Failure
    """
    sql_query = _load_sql_file(context['sql_file'])
    run_dq(context, dq_query=sql_query)


def run_dq_file(**context):
    """
    Executes queries stored in a yaml file and captures the result value
    :param context: dictionary with values
    :return: None, side effect of sending Slack messages

    The result should be one of the following values
    0 = Success
    1 = Soft Failure
    2 = Hard Failure

    The yaml file should be formatted as below:
        ---
        test_id: " order count number one"
        enabled: true
        query: |
         /*
         SELECT 1
         FROM DWH_DEV.STAGING.STG_AMAZON_FBA_STORAGE_FEES_REPORT
         LIMIT 1
         */

         SELECT 2
         FROM $db_stage.STG_AMAZON_FBA_STORAGE_FEES_REPORT
         LIMIT 1
        ---
        test_id: "order amount number 5"
        enabled: true
        query: |
          SELECT 2
          FROM $db_stage.STG_AMAZON_FBA_STORAGE_FEES_REPORT
          LIMIT 1
    """
    use_default_path = context.get('use_default_path', True)
    dq_commands = _load_yaml_commands(context['query_file'], use_default_path)
    run_dq(context, dq_commands=dq_commands)


def run_dq(context, dq_query=None, dq_commands=None):
    '''
    Common function to run the DQ check
    For every failed alert, log to the failed alert log table.
    Also check if a particular alert needs to be snoozed.
        If True, dont send a failure alert.
        For every alert that snoozed, save a record in the snoozed alert log table.
    :param context: airflow context
    :param dq_query: str, DQ query to run
    :param dq_commands: DQ object, read from YAML with multiple DQ checks to run
        Either query or dq_commands will be specified.
    '''
    from db_connectors.sf_connector import Snowflake
    from helpers.helper import traceback_fmt

    sfc = Snowflake()

    dag_id = context['dag'].dag_id
    task_id = context.get('task_instance').task_id
    run_id = context['run_id']
    workflow_name = context['wk_name']
    dq_id = resolve_db_variables(f'Ref:{workflow_name}')

    # Disable all snoozing
    disable_snooze = Variable.get("disable_snooze", 0)
    disable_snooze = int(disable_snooze)

    dag_run_id =  f'{dag_id}_{run_id}'
    log.info(f'DAG run id : {dag_run_id}')

    # List of failed tests & severity codes (), snoozed & non snoozed
    failed_snoozed_tests = defaultdict(list)
    failed_non_snoozed_tests = defaultdict(list)

    # Read the data for snoozed alerts
    data = read_snooze_alert_data(sfc, context)

    # Check if alert is snoozed at task or dag level
    is_alert_snoozed_for_task, is_alert_auto_snoozed_for_task, snooze_alert_id_for_task = check_if_alert_snoozed(data, task_id)
    print(f'TASK: {task_id}, SNOOZE INFO: {is_alert_snoozed_for_task}, {is_alert_auto_snoozed_for_task}, {snooze_alert_id_for_task}')

    # Task with single DQ
    if dq_query:
        test_name = context['test_name']
        query_tag = set_query_tag(dq_test=test_name, **context)

        # Run the DQ query
        result, _ = sfc.get_data(dq_query, as_dict=True, query_tag=query_tag)

        # If the DQ check failed, and alert is not snoozed, send a notification
        severity = result[0].get('result')
        if severity > 0:
            # For a task with single DQ, log the snoozed alert
            if not disable_snooze and is_alert_snoozed_for_task:
                failed_snoozed_tests[severity].append((test_name, snooze_alert_id_for_task, is_alert_auto_snoozed_for_task))
            else:
                failed_non_snoozed_tests[severity].append(test_name)

    # Task with multiple DQs (e.g. YAMLs)
    elif dq_commands:
        for dq_test in dq_commands:
            test_id = dq_test.test_id
            query_tag = set_query_tag(dq_test=test_id, **context)

            # Run the DQ query
            result, _ = sfc.get_data(dq_test.query, as_dict=True, query_tag=query_tag)

            # Add to failed tests
            severity = result[0].get('result') if 'result' in result[0] else result[0].get('RESULT')
            if severity > 0:

                # First check if the test has been snoozed, & log the record if the test is snoozed
                is_alert_snoozed_for_test, is_alert_auto_snoozed_for_test, snooze_alert_id_for_test = \
                    check_if_alert_snoozed(data, task_id=task_id, test_id=test_id)

                print(f'TASK: {task_id}, TEST: {test_id}, SNOOZE INFO: {is_alert_snoozed_for_test}, {is_alert_auto_snoozed_for_test}, {snooze_alert_id_for_test}')

                if not disable_snooze and is_alert_snoozed_for_test:
                    failed_snoozed_tests[severity].append((test_id, snooze_alert_id_for_test, is_alert_auto_snoozed_for_test))

                # If the test has not been snoozed, but the task has a snooze, log the record
                elif not disable_snooze and is_alert_snoozed_for_task:
                    failed_snoozed_tests[severity].append((task_id, snooze_alert_id_for_task, is_alert_auto_snoozed_for_task))

                else:
                    failed_non_snoozed_tests[severity].append(test_id)

    # No failed tests, can be either: a) snoozed failed alerts or b) non-snoozed failed alerts
    if not (failed_snoozed_tests or failed_non_snoozed_tests):
        return

    try:
        # Run through each of the error codes and send (or snooze) the alert failure notification
        for code in [DQ_CODE_SOFT_ERROR, DQ_CODE_HARD_ERROR]:
            severity = constants.DQ_FAILURE_SEVERITY_SOFT if code == DQ_CODE_SOFT_ERROR \
                        else constants.DQ_FAILURE_SEVERITY_HARD

            # Failed tests & snoozed failed tests
            cur_failed_non_snoozed_tests = failed_non_snoozed_tests[code]
            cur_failed_snoozed_tests = failed_snoozed_tests[code]

            # Failed tests that were not set to snooze
            # Log the message to the logs table & sends an alert
            if cur_failed_non_snoozed_tests:
                for failed_test in cur_failed_non_snoozed_tests:
                    log_record(context, dq_id=dq_id, dq_severity=severity, dq_failed_test=failed_test)
                alerts.send_audit_test_failure_alert(
                    dq_id=dq_id, failed_tests=cur_failed_non_snoozed_tests, severity=severity, **context)

            # Failed tests that were set to snooze
            # Log the message to the logs table & skip sending the alert
            if cur_failed_snoozed_tests:

                # Logging records & send snooze notification for first snooze
                for failed_test, snooze_alert_id, is_alert_auto_snoozed in cur_failed_snoozed_tests:
                    log_record(context, dq_id=dq_id, dq_severity=severity, dq_failed_test=failed_test,
                                is_alert_snoozed=True, is_alert_auto_snoozed=is_alert_auto_snoozed,
                                snooze_alert_id=snooze_alert_id)

                    # Send a notification if its the first snoozed alert
                    snooze_log_data = read_snooze_log_data(sfc, snooze_alert_id)
                    if len(snooze_log_data) == 1:
                        alerts.send_snooze_failure_alert(
                            dq_id=dq_id, failed_test_name=failed_test, severity=severity, 
                            is_alert_auto_snoozed=is_alert_auto_snoozed, **context)

            # Hard failures will always fail the DAG, even if task was snoozed
            if (cur_failed_non_snoozed_tests or cur_failed_snoozed_tests) and code == DQ_CODE_HARD_ERROR:
                raise Exception('DQ tests failed with hard failure alert. Failing the task')

    except Exception as err:
        msg = traceback_fmt(err)
        log.error(msg)
        raise Exception

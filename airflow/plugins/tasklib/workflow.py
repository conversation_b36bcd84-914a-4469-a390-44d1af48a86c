import logging
import pytz
import ast
from datetime import datetime, timedelta
log = logging.getLogger(__name__)

# get workflow parameters formatted with utc datetime
def _get_workflow_params_formatted(workflow_params:dict)->dict:
    name=workflow_params['wf_name']
    start_ts=pytz.utc.localize(datetime.fromisoformat(workflow_params['wf_start_ts_iso_utc']))
    end_ts=pytz.utc.localize(datetime.fromisoformat(workflow_params['wf_end_ts_iso_utc']))
    interval=workflow_params['wf_interval']
    wf_params_new={"name":name, "start_ts":start_ts, "end_ts":end_ts, "interval":interval}
    return wf_params_new

# reads workflow date start, date end timestamp and frequency from the application metastore
def get_workflow_params(workflow_name:str, forward_end_ts:bool=True)->dict:
    from db_connectors.pg_connector import Postgres
    postgres = Postgres()
    result=None
    if not workflow_name:
        raise Exception("workflow name not specified.")
    query=f"select name as wf_name, TO_CHAR(watermark_start_timestamp_utc,'YYYY-MM-DD HH24:MI:SS') as wf_start_ts_iso_utc, TO_CHAR(watermark_end_timestamp_utc,'YYYY-MM-DD HH24:MI:SS') as wf_end_ts_iso_utc, frequency as wf_interval from workflow_configurations.workflow where name='{workflow_name}'"
    workflow_params={}
    try:
        result=postgres.get_data(sql_statement=query, as_dict=True)
    except Exception as err:
        message="Error occured while fetching workflow attributes"
        log.error(message)
        log.error(err)
        raise
    else:
        logging.info(result)
        if result or result[0]:
            workflow_params=result[0][0]
            interval=workflow_params['wf_interval']

            #read worflow start and end timestamps
            curr_ts=datetime.now(pytz.utc)
            start_ts_str=workflow_params['wf_start_ts_iso_utc']
            end_ts_str=workflow_params['wf_end_ts_iso_utc']
            start_ts=pytz.utc.localize(datetime.fromisoformat(start_ts_str))
            end_ts=pytz.utc.localize(datetime.fromisoformat(end_ts_str))

            #check start and end timestamps are valid
            if start_ts >= end_ts:
                raise Exception(f"Workflow start timestamp {start_ts_str} must be less than end timestamp {end_ts_str}.")

            if interval == 'HOURLY' and forward_end_ts:
                #truncate timestamps to date and hour
                start_ts=start_ts.replace(minute=0, second=0, microsecond=0)
                end_ts=curr_ts.replace(minute=0, second=0, microsecond=0)
                curr_ts=curr_ts.replace(minute=0, second=0, microsecond=0)
                curr_ts_str=curr_ts.strftime('%Y-%m-%d %H:%M:%S')

                #check start and end timestamps are valid for the load hour
                if start_ts >= curr_ts:
                    raise Exception(f"Workflow start timestamp:{start_ts_str} and end timestamp:{end_ts_str} is ahead of the expected logical start timestamp and end timestamps range for the load hour:{curr_ts_str}. Workflow timestamps have been forwarded. Possibly workflow was run many times within an hour.")
                workflow_params['wf_end_ts_iso_utc']=curr_ts_str

            elif interval == 'DAILY' and forward_end_ts:
                #truncate timestamps to date
                start_ts=start_ts.replace(hour=0, minute=0, second=0, microsecond=0)
                end_ts=curr_ts.replace(hour=0, minute=0, second=0, microsecond=0)
                curr_ts=curr_ts.replace(hour=0, minute=0, second=0, microsecond=0)
                curr_ts_str=curr_ts.strftime('%Y-%m-%d %H:%M:%S')

                #check start and end timestamps are valid for the load hour
                if start_ts >= curr_ts:
                    raise Exception(f"Workflow start timestamp:{start_ts_str} and end timestamp:{end_ts_str} is ahead of the expected logical start timestamp and end timestamps range for the load day:{curr_ts_str}. Workflow timestamps have been forwarded. Possibly workflow was run many times within a day.")
                
                workflow_params['wf_end_ts_iso_utc']=curr_ts_str

    return workflow_params

# update data start timestamp and date end timestamps parameters for the next run of the DAG
def update_workflow_params(wf_params:str)->None:
    from db_connectors.pg_connector import Postgres

    wf_params_dict=ast.literal_eval(wf_params)
    workflow_name=wf_params_dict['wf_name']
    start_ts=wf_params_dict['wf_start_ts_iso_utc']
    end_ts=wf_params_dict['wf_end_ts_iso_utc']
    interval=wf_params_dict['wf_interval']

    upd_start_ts=end_ts
    end_tsz=pytz.utc.localize(datetime.fromisoformat(end_ts))
    if interval == 'HOURLY':
        end_tsz=end_tsz + timedelta(hours=1)     
    elif interval == 'DAILY':
        end_tsz=end_tsz + timedelta(hours=24)     
    upd_end_ts=end_tsz.strftime('%Y-%m-%d %H:%M:%S') 

    query=f"UPDATE workflow_configurations.workflow SET watermark_start_timestamp_utc='{upd_start_ts}', watermark_end_timestamp_utc='{upd_end_ts}', updated_timestamp_utc=current_timestamp, updated_by='amazon_fba_orders_etl' where name='{workflow_name}'"
    log.info(query)
    try:
        postgres = Postgres()
        postgres.execute_statement(query)
    except Exception as err:
        message="Error occured while updating workflow attributes"
        log.info(message)
        log.error(err)
        raise

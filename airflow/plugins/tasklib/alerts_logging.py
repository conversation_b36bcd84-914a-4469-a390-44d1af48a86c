from datetime import datetime, timezone

from db_connectors.sf_connector import <PERSON><PERSON><PERSON>
from helpers.helper import resolve_db_variables


def log_record(context, **kwargs):
    '''
    Log the failures to the logs table in Snowflake
    '''

    sf_obj = Snowflake()

    author = context.get('params',{}).get('author','')
    task_id = context.get('task_instance').task_id
    dag_id = context.get('task_instance').dag_id
    execution_date = context.get('logical_date')
    start_date = context.get('dag_run').start_date
    log_url = context.get('task_instance').log_url

    # Following values only for DQ failures, nulls for other failures
    dq_id = kwargs.get('dq_id')
    dq_failed_test = kwargs.get('dq_failed_test')
    dq_severity = kwargs.get('dq_severity')
    is_alert_snoozed = kwargs.get('is_alert_snoozed', False)
    is_alert_auto_snoozed = kwargs.get('is_alert_auto_snoozed', False)
    snooze_alert_id = kwargs.get('snooze_alert_id', -1)

    utc_ts = datetime.strftime(datetime.now(timezone.utc), '%Y-%m-%d %H:%M:%S')
    if dq_id:
        query = f"""
        INSERT INTO $infra_db.alert_failure_log (
            dag_id
          , task_id
          , execution_date
          , start_date
          , log_url  
          , author
          , dq_id
          , dq_failed_test
          , dq_severity
          , is_alert_snoozed
          , is_alert_auto_snoozed
          , snooze_alert_id
          , record_created_timestamp_utc
        )
        VALUES(
            '{dag_id}'
          , '{task_id}'
          , '{execution_date}'
          , '{start_date}'
          , '{log_url}'
          , '{author}'
          , '{dq_id}'
          , '{dq_failed_test}'
          , '{dq_severity}'
          , {is_alert_snoozed}
          , {is_alert_auto_snoozed}
          , {snooze_alert_id}
          , '{utc_ts}'
        )"""
    else:
        query = f"""
        INSERT INTO $infra_db.alert_failure_log (
            dag_id
          , task_id
          , execution_date
          , start_date
          , log_url  
          , author
          , record_created_timestamp_utc
        )
        VALUES(
            '{dag_id}'
          , '{task_id}'
          , '{execution_date}'
          , '{start_date}'
          , '{log_url}'
          , '{author}'
          , '{utc_ts}'
        )"""
    query = resolve_db_variables(query)
    sf_obj.execute_statement(query)

import os
import logging
from typing import Tuple, Any

import yaml

#for accessing airflow configuration variables - core, ...
from airflow import configuration

#import application configuration
from configs import dev_config, prd_config

log = logging.getLogger(__name__)

DAGS_FOLDER="/opt/airflow/dags"
CONFIG_YAML=DAGS_FOLDER + "/configurations"
CONFIG_YML_NAME="app_config.yaml"
AUDIT_SQL_FILE="audit/audit.sql"

def get_env()->str:
    #fetch environment from airflow configuration
    airflow_conf=configuration.conf
    env=airflow_conf.get('core', 'environment')
    log.info(f'Environment from configuration: {env}')

    #check environment variable is initialized
    if not env:
        log.error('Environment variable core.environment is not initialized.')
        raise

    #check environment variable value is valid
    if env not in ['dev', 'prod']:
        log.error('Environment variable must be dev or prod.')
        raise

    return env

def get_config_dict()->dict:
    #fetch environment string
    env=get_env()

    #set the application configuration for the environment
    app_config=prd_config if env=='prod' else dev_config
    return app_config.dict() 

#this function will be deprecated and all references of it must be updated
def get_config()->dict:
    #read schema, database names from application configuration for variables used in the sql
    config_dict={}
    env=get_env()
    config_file_path = os.path.join(CONFIG_YAML, env,CONFIG_YML_NAME)
    log.info('configurations file: %s', config_file_path)
    with open(config_file_path, 'r') as f:
        config_dict=yaml.safe_load(f)
    log.info('configurations: %s', str(config_dict))
    return config_dict

def get_db_config()->dict:
    config_dict=get_config()
    db_vars_dict=config_dict.get('database_variables',{}).get('snowflake',{})
    log.info('database variables configuration: %s', str(db_vars_dict))
    return db_vars_dict

def get_dags_folder()->str:
    return DAGS_FOLDER

def get_audit_sql_file()->str:
    return AUDIT_SQL_FILE

def get_glue_config_s3_uri()->str:
    config_dict=get_config()
    glue_config_dict=config_dict.get('glue_config', {})
    s3_bucket=glue_config_dict.get('s3_bucket')
    config_path=glue_config_dict.get('config_path')
    if s3_bucket is None or config_path is None:
        raise Exception("glue config s3 bucket not specified in app configuration.")
    return "s3://" + s3_bucket + "/" + config_path 

def get_slack_conn_id(msg_type)->str:
    config_dict = get_config()
    conn_id = config_dict.get('slack_conn_ids', {}).get(msg_type, "")
    return conn_id

def get_s3_staging_bucket()->str:
    config_dict = get_config()
    s3_staging_bucket = config_dict.get('s3_staging_bucket')
    log.info(f'S3 staging bucket: {s3_staging_bucket}')
    return s3_staging_bucket

def get_adtech_endpoint()->str:
    config_dict = get_config()
    adtech_endpoint = config_dict.get('connections', {}).get('adtech_default', {}).get('url', "")
    log.info(f'AdTech endpoint: {adtech_endpoint}')
    return adtech_endpoint

import logging
from airflow.providers.slack.operators.slack_webhook import SlackWebhookOperator

import tasklib.config as tlcfg
from tasklib import constants
from tasklib.alerts_logging import log_record

log = logging.getLogger(__name__)

SLACK_CONN_ID='slack'
def send_failure_alert(context):
    """
    This is a call back function that will be called when a dag fails. The first step is setting up the job run status
    in workflow_configurations.etl_job_runs table and then alerting the DE team of a failure via slack
    :param context:
    :return:
    """
    log.info('Sending alert')
    author=context.get('params',{}).get('author','')
    formatted_author = ','.join([f'@{a}' for a in author.replace(' ', '').split(',')])

    slack_msg = """
            :red_circle: Task Failed while executing dag *{dag}*.
            *Task*: {task}
            *Dag*: {dag}
            *Execution Time*: {exec_date}
            *Log Url*: <{log_url}|Click Here>
            *Author*: {formatted_author}
            """.format(
            task=context.get('task_instance').task_id,
            dag=context.get('task_instance').dag_id,
            ti=context.get('task_instance'),
            exec_date=context.get('logical_date'),
            log_url=context.get('task_instance').log_url,
            formatted_author=formatted_author
        )

    log_record(context)

    try:
        failed_alert = SlackWebhookOperator(
            slack_webhook_conn_id='slack',
            task_id='dag_failure_notification',
            message=slack_msg,
            username='airflow',
        )
        failed_alert.execute(context=context)
    except Exception as e:
        log.error(f"Failed to send alert to slack: {e}")


def send_failure_alert_ops(context):
    """
    This is a call back function that will be called when a dag fails. The first step is setting up the job run status
    in workflow_configurations.etl_job_runs table and then alerting the Ops team of a failure via slack

    Sends a msg to slack channel #ops_planning_alerts
    :param context:
    :return:
    """
    log.info('Sending alert')
    author=context.get('params',{}).get('author','')
    formatted_author = ','.join([f'@{a}' for a in author.replace(' ', '').split(',')])

    slack_msg = """
            :red_circle: Task Failed while executing dag *{dag}*.
            *Task*: {task}
            *Dag*: {dag}
            *Execution Time*: {exec_date}
            *Log Url*: <{log_url}|Click Here>
            *Author*: {formatted_author}
            """.format(
            task=context.get('task_instance').task_id,
            dag=context.get('task_instance').dag_id,
            ti=context.get('task_instance'),
            exec_date=context.get('logical_date'),
            log_url=context.get('task_instance').log_url,
            formatted_author=formatted_author
        )

    log_record(context)

    failed_alert = SlackWebhookOperator(
        slack_webhook_conn_id='slack_ops',
        task_id='dag_failure_notification',
        message=slack_msg,
        username='airflow',
    )
    failed_alert.execute(context=context)


def send_audit_test_failure_alert(
        dq_id=None, failed_tests=None, severity=None, slack_msg=None,
        msg_type=constants.SLACK_MSG_TYPE_DQ, **context):
    """
    This function will be called if there are audit test failures. Slack alert will be sent
    """

    author = context.get('params',{}).get('author','')
    formatted_author = ','.join([f'@{a}' for a in author.replace(' ', '').split(',')])

    slack_conn_id = tlcfg.get_slack_conn_id(msg_type)
    if not slack_conn_id:
        log.error(f"Unable to fetch data quality alert slack connection id from configuration: {msg_type}")
        raise

    if not slack_msg:
        icon = ""
        if severity == constants.DQ_FAILURE_SEVERITY_SOFT:
            icon = ":flashlight:" 
        elif severity == constants.DQ_FAILURE_SEVERITY_HARD:
            icon = ":alert:"

        slack_msg = """
        {icon} Audit tests failed for the *{dag}* with execution date *{exec_date}*.
        *Task*: {task}
        *Failed audit tests*: {failed_tests_str}
        *Severity*: {severity} 
        *Data_Quality_Id*: {dq_id}
        *Author*: {formatted_author}
        """.format(dag=context.get('task_instance').dag_id,
                task=context.get('task_instance').task_id,
                failed_tests_str=failed_tests,
                dq_id=dq_id,
                severity=severity,
                exec_date=context.get('logical_date'),
                formatted_author=formatted_author,
                icon=icon
                )

    try:
        failed_audit_alert = SlackWebhookOperator(
            task_id='failed_audit_notification',
            slack_webhook_conn_id=slack_conn_id,
            message=slack_msg,
            username='airflow',
        )
        failed_audit_alert.execute(context=context)
    except Exception as e:
        log.error(f"Failed to send alert to slack: {e}")


def send_snooze_failure_alert(dq_id, failed_test_name, severity, is_alert_auto_snoozed,
        msg_type=constants.SLACK_MSG_TYPE_DQ, **context):
    """
    This function will be called if there are audit test failures. Slack alert will be sent
    """

    author = context.get('params',{}).get('author','')
    formatted_author = ','.join([f'@{a}' for a in author.replace(' ', '').split(',')])

    slack_conn_id = tlcfg.get_slack_conn_id(msg_type)
    if not slack_conn_id:
        log.error(f"Unable to fetch data quality alert slack connection id from configuration: {msg_type}")
        raise

    snooze_prefix = 'Auto-snoozed' if is_alert_auto_snoozed == True else 'Snoozed'
    slack_msg = """
    {icon} *{snooze_prefix}* the failed test notification for DAG: *{dag}* with execution date *{exec_date}*.
    *Task/Test Name*: {failed_test_name}
    *Severity*: {severity}
    *Author*: {formatted_author}
    """.format(
            icon=":zzz:",
            snooze_prefix=snooze_prefix,
            dag=context.get('task_instance').dag_id,
            task=context.get('task_instance').task_id,
            failed_test_name=failed_test_name,
            dq_id=dq_id,
            severity=severity,
            exec_date=context.get('logical_date'),
            formatted_author=formatted_author,
            )

    snoozed_audit_alert = SlackWebhookOperator(
        task_id='snoozed_alert_notification',
        slack_webhook_conn_id=slack_conn_id,
        message=slack_msg,
        username='airflow',
    )
    snoozed_audit_alert.execute(context=context)

import logging
import sys
import warnings
import yaml
from boto3 import client
from datetime import timedelta
from dateutil import parser

from db_connectors.pg_connector import Postgres
from db_connectors.sf_connector import Snowflake
import tasklib.config as tlcfg

warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)

_AIRFLOW_CONN_ID = 'airflow_conn_id'
_DB_TYPE = 'db_type'
_FULL_TABLE_NAME = 'full_table_name'
_INCREMENTAL_COLUMN = 'incremental_column'
_LOAD_DATA_OPTIONS = 'load_data_from_s3_copy_options'
_POSTGRES = 'postgres'
_SNOWFLAKE = 'snowflake'
_SOURCE = 'source'
_TARGET = 'target'
_UNLOAD_DATA_OPTIONS = 'unload_data_into_s3_copy_options'
_UNIQUE_KEY_COLUMNS = 'unique_key_columns'
_FUNC_MAPPING = {
    _SNOWFLAKE: Snowflake,
    _POSTGRES: Postgres,
}
_MAX_BATCH_LOAD_SIZE = 'batch_size'
_IS_FULL_LOAD = 'fullload'


class DataReplicator():
    def __init__(self, args_file):
        self.s3_client = client('s3')
        env_configs = tlcfg.get_config()
        self.source_run_env = env_configs.get('environment')
        self.aws_region = env_configs.get('region')

        logger.info(f"run_env: {self.source_run_env}, aws_region: {self.aws_region}")

        self.snowflake_stage = env_configs.get('replication_config').get('snowflake_stage')
        self.s3_staging_bucket = env_configs.get('s3_staging_bucket')
        self.config_file_bucket = env_configs.get('glue_config').get('s3_bucket')
        self.config_file_key = env_configs.get('glue_config').get('config_path')
        self.config_file_path = f"{self.config_file_key}/{args_file}"

        self.config_res = self.s3_client.get_object(Bucket=self.config_file_bucket, Key=self.config_file_path)
        self.replication_configs = yaml.safe_load(self.config_res["Body"])

        # Source parameters
        self.src_airflow_conn_id = self.replication_configs[_SOURCE][_AIRFLOW_CONN_ID]
        self.src_full_table_name = self.replication_configs[_SOURCE][_FULL_TABLE_NAME]
        self.src_unload_copy_options = self.replication_configs[_SOURCE][_UNLOAD_DATA_OPTIONS]
        self.src_client_type = self.replication_configs[_SOURCE][_DB_TYPE].lower()

        # Target parameters
        self.tgt_airflow_conn_id = self.replication_configs[_TARGET][_AIRFLOW_CONN_ID]
        self.tgt_full_table_name = self.replication_configs[_TARGET][_FULL_TABLE_NAME]
        self.tgt_load_copy_options = self.replication_configs[_TARGET][_LOAD_DATA_OPTIONS]
        self.tgt_client_type = self.replication_configs[_TARGET][_DB_TYPE].lower()

        # Incremental & unique key columns (Default: Null)
        self.incremental_column = self.replication_configs.get(_INCREMENTAL_COLUMN)
        self.key_columns = self.replication_configs.get(_UNIQUE_KEY_COLUMNS)

        if (not self.incremental_column) ^ (not self.key_columns):
            raise Exception(f'unique_key_columns & incremental_column should either both be nulls or have values specified')

        if self.src_client_type == self.tgt_client_type:
            raise Exception('Source & Target db_type should not be the same')

        if len(set([self.src_client_type, self.tgt_client_type]) ^ set([_SNOWFLAKE, _POSTGRES])):
            raise Exception(f'Source & Target db_type should be either {_SNOWFLAKE} or {_POSTGRES}')

        if bool(self.key_columns) ^ bool(self.incremental_column):
            raise Exception('unique_key_columns & incremental_column should either both be null or have specified values')

        # The snowflake s3 stage is setup with the raptor prefix e.g. s3://staging-data-layer/raptor
        # For postgres, we need to add the raptor prefix to the path
        s3_prefix = self.src_full_table_name.replace('.', '_').lower()
        self.s3_rel_prefix = f"data_replication/{self.src_client_type}_landing/{s3_prefix}"
        self.s3_prefix = f"raptor/{self.s3_rel_prefix}"

        # Make sure no trailing slash
        self.s3_prefix = self.s3_prefix.strip('/')
        self.s3_rel_prefix = self.s3_rel_prefix.strip('/')

        if self.src_client_type == _SNOWFLAKE:
            self.src_s3_prefix = self.s3_rel_prefix
            self.tgt_s3_prefix = self.s3_prefix
        elif self.src_client_type == _POSTGRES:
            self.src_s3_prefix = self.s3_prefix
            self.tgt_s3_prefix = self.s3_rel_prefix

        self.src_client = _FUNC_MAPPING[self.src_client_type](self.src_airflow_conn_id)
        self.tgt_client = _FUNC_MAPPING[self.tgt_client_type](self.tgt_airflow_conn_id)
        self.src_database_name, _, _ = self.src_full_table_name.split('.')
        self.tgt_database_name, _, _ = self.tgt_full_table_name.split('.')

    def get_batch_size(self) -> int:
        if _MAX_BATCH_LOAD_SIZE in self.replication_configs:
            return self.replication_configs[_MAX_BATCH_LOAD_SIZE]
        return -1

    def is_full_load(self) -> int:
        if _IS_FULL_LOAD in self.replication_configs:
            return self.replication_configs[_IS_FULL_LOAD]
        return False


def replicate_data(args_file) -> None:
    """
    Sync data from Source to Target using S3 as middle layer.
    """

    rep_obj = DataReplicator(args_file)

    # Snowflake needs the stage name, Postgres needs the s3 bucket name
    if rep_obj.src_client_type == _SNOWFLAKE:
        unload_param = rep_obj.snowflake_stage
        load_param = rep_obj.s3_staging_bucket
    elif rep_obj.src_client_type == _POSTGRES:
        unload_param = rep_obj.s3_staging_bucket
        load_param = rep_obj.snowflake_stage

    try:
        run = True
        condition = ""
        if rep_obj.incremental_column:
            query = f"""SELECT COALESCE(MAX({rep_obj.incremental_column}), '1990-01-01 00:00:00.000000') FROM {rep_obj.src_full_table_name}"""
            src_max_datetime, _ = rep_obj.src_client.get_data(query)
            src_max_datetime = parser.parse(str(src_max_datetime[0][0]))

            query = f"""SELECT COALESCE(MAX({rep_obj.incremental_column}), '1990-01-01 00:00:00.000000') FROM {rep_obj.tgt_full_table_name}"""
            tgt_max_datetime, _ = rep_obj.tgt_client.get_data(query)
            tgt_max_datetime = parser.parse(str(tgt_max_datetime[0][0]))

            if src_max_datetime > tgt_max_datetime:
                condition = f"WHERE {rep_obj.incremental_column} >= '{(tgt_max_datetime + timedelta(seconds=1)).replace(microsecond=0)}'"
            else:
                logger.info(f"Skipping incremental replication for table {rep_obj.src_full_table_name}; data up-to-date")
                run = False

        if run:
            # Block Summary: Unload the data from source db to s3
            data_query = f"""SELECT * FROM {rep_obj.src_full_table_name} {condition}"""
            rep_obj.src_client.unload_data_into_s3(unload_param, rep_obj.src_s3_prefix, data_query,
                                                   rep_obj.src_unload_copy_options)

            # Get the source column names
            src_column_names = rep_obj.src_client.get_table_schema(rep_obj.src_full_table_name)

            # Block Summary: Load the data from s3 into target db
            if rep_obj.is_full_load():
                rep_obj.tgt_client.full_load_with_batches(load_param, rep_obj.tgt_s3_prefix,
                                                          rep_obj.tgt_full_table_name,
                                                          rep_obj.key_columns, rep_obj.tgt_load_copy_options,
                                                          rep_obj.get_batch_size())

            if rep_obj.get_batch_size() > 0:
                rep_obj.tgt_client.load_data_from_s3_in_batches(load_param, rep_obj.tgt_s3_prefix,
                                                                rep_obj.tgt_full_table_name,
                                                                rep_obj.key_columns, rep_obj.tgt_load_copy_options,
                                                                rep_obj.get_batch_size())

            if rep_obj.get_batch_size() < 0:
                rep_obj.tgt_client.load_data_from_s3(
                    load_param,
                    s3_prefix=rep_obj.tgt_s3_prefix,
                    qualified_table_name=rep_obj.tgt_full_table_name,
                    unique_keys_ls=rep_obj.key_columns,
                    copy_options=rep_obj.tgt_load_copy_options,
                    column_names=src_column_names)

        return run

    except Exception as err:
        exc_type, exception_value, _ = sys.exc_info()
        logger.error(
            str(exception_value),
            extra={
                "failure_details": {
                    "failure_code": f"{exc_type.__name__}",
                    "failure_reason": str(exception_value),
                }
            },
            exc_info=err,
        )
        raise err

import os
import logging
import papermill as pm
from typing import Dict, Any, Optional

log = logging.getLogger(__name__)


def local_papermill_runner(input_notebook_path: str, output_notebook_path: Optional[str] = None, papermil_parms: Dict[str, Any] = dict()):
    if not os.path.exists(input_notebook_path):
        log.error(f"Unable to find input notebook at path: {input_notebook_path}")
        raise FileNotFoundError(f"Unable to find input notebook at path: {input_notebook_path}")

    pm.execute_notebook(input_notebook_path, output_notebook_path, parameters=papermil_parms, nested_asyncio=True)

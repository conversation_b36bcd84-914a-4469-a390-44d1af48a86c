import logging
import yaml
from aws.s3 import S3
from tasklib import config
from helpers import helper

log = logging.getLogger(__name__)

class S3ToSnowflakeConfigParser():
   def __init__(self, args_file):
      self.args_file=args_file
      self.args_dict={}

   def set_args_file_dict(self):
      #fetch application configuration dictionary
      config_dict=config.get_config_dict()

      s3=S3()
      #read the contents of the job configuration
      config_str_raw=s3.read_text_file(file_name=self.args_file)

      #resolve job configuration variables
      config_str=helper.render_jinja_template(config_str_raw, config_dict)

      #get dictionary version of the configuration
      try:
         self.args_dict=yaml.safe_load(config_str)
      except Exception as e:
         msg=f"Error when converting configuration file to dictionary. Check whether {self.args_file} exists and is valid."
         log.error(msg)
         log.error(e)
         raise
      else:
         #check configuration dictionary is valid
         if not self.args_dict: 
            raise Exception("Configuration file {self.args_file} is empty or configuration file does not exist or file is not valid.")
         
   def get_s3_uri_list_file(self):
      #initialize arguments dictionary if not valid
      if not self.args_dict:
          self.set_args_file_dict()

      # read parameter from the dictionary
      s3_uri_list_file=""
      try: 
          s3_uri_list_file=self.args_dict['job_parameters']['s3_uri_list_file']
      except Exception as e:
          msg="Error when reading config parameter 's3_uri_list_file' from the configuration file."
          log.error(msg)
          log.error(e)
          raise

      return s3_uri_list_file

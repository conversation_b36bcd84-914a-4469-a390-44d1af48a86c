import ast
import logging
from airflow.operators.python import get_current_context
from datetime import datetime
from typing import List, Dict

import tasklib.config as tlcfg
import tasklib.sql as tlsql
from db_connectors.sf_connector import Snowflake
from helpers.helper import get_absolute_sql_file_path, read_and_parse_sql_file, resolve_db_variables
from tasklib.alerts import send_audit_test_failure_alert
from tasklib.constants import SLACK_MSG_TYPE_DATA_DELAY

log = logging.getLogger(__name__)

def _generate_sql_audit(ts_created_field: str = None, ts_updated_field: str = None) -> str:
    if not ts_created_field:
        ts_created_field = '"record_created_timestamp_utc"'
    if not ts_updated_field:
        ts_updated_field = '"record_updated_timestamp_utc"'
    query_str = f"""
        INSERT INTO $curated_db.AUDIT_LOG
        (
          table_name,
          log_timestamp_utc,
          rows_created,
          rows_updated
        )
        SELECT 
          '$table_name' as table_name,
          SYSDATE() as log_timestamp_utc,
          COUNT(CASE WHEN {ts_created_field} >= DATEADD(DAY, -2, SYSDATE()) THEN 1 END) as rows_created,
          COUNT(CASE WHEN {ts_updated_field} >= DATEADD(DAY, -2, SYSDATE()) 
                          AND {ts_updated_field} <> {ts_created_field} THEN 1 END
          ) as rows_updated
        FROM 
          $table_name
        WHERE 
          {ts_created_field} BETWEEN DATEADD(DAY, -2, SYSDATE()) AND SYSDATE() OR 
          {ts_updated_field} BETWEEN DATEADD(DAY, -2, SYSDATE()) AND SYSDATE(); 
    """
    return query_str


def run_audit(table_name: str, wf_params: str, **context):
    # Fetch database variables from configuration file
    table_name_resolved = resolve_db_variables(table_name)

    # Convert workflow parameter dictionary string to dictionary
    log.info('Wf params:' + str(wf_params))
    wf_params_dict = {}
    if wf_params is not None:
        wf_params_dict = ast.literal_eval(wf_params)

    log.info('Wf after:' + str(wf_params_dict))

    # Add table name to the dictionary
    wf_params_dict['table_name'] = table_name_resolved

    ts_created_field = context.get('ts_created_field', None)
    ts_updated_field = context.get('ts_updated_field', None)
    # Get audit sql
    audit_sql_text = _generate_sql_audit(
        ts_created_field=ts_created_field, ts_updated_field=ts_updated_field)
    # Run query to capture audit metrics
    tlsql.run_query_text(audit_sql_text, str(wf_params_dict), **context)


def generate_alert_message(data:List[Dict]) -> str:
    """
    Generates the message sent as part of the data delay part.
    The input is Snowflake results dataset.
    """

    import pandas as pd

    data_df = pd.DataFrame(data)

    snapshot_date = data_df['SNAPSHOT_DATE'].iloc[0].strftime("%b %d %Y %H %p")
    msg = f'`DATA DELAY ALERT:` Data unavailable for the following as of  *{snapshot_date} * PST : \n'

    grouped_df = data_df.groupby(['DATABASE_NAME', 'SCHEMA_NAME'])
    for (db_name, sch_name), grp in grouped_df:
        for table_status in ['delayed', 'partially_delayed']:
            filtered_grp = grp[grp['TABLE_STATUS']==table_status]
            if not len(filtered_grp):
                continue
            msg += f'> {db_name}.{sch_name} : {table_status.upper()} TABLES\n'
            grouped_by_table = filtered_grp.groupby(['TABLE_NAME', 'COUNTRY', 'ROOT_TABLE_NAME'])
            for nm, group_by_table in grouped_by_table:
                table_name, country, sub_table_name = nm
                brand_codes = ','.join(sorted(group_by_table['BRAND'].unique()))
                if table_name.lower() == 'fact_amazon_ad_campaigns':
                    msg += f'- Table: `{table_name}` Campaign Type: `{sub_table_name}` '
                else:
                    msg += f'- Table: `{table_name}` '
                if table_status == 'delayed':
                    msg += f' Brands: `All brands` \n'
                else:
                    msg += f' Brands: `{brand_codes}` \n'
    return msg


def run_data_delay_brand_audit(
        sql_file: str,
        wf_params:str) -> None:
    """
    Runs the data delay alert for brands. This will send out an alert to the
    slack channel for every brand that is delayed based on specified number of 
    days (lag_in_days).
    """

    sql_file_abs_path = get_absolute_sql_file_path(sql_file)
    sql_text = read_and_parse_sql_file(sql_file_abs_path, wf_params)
    log.info(f'Query to execute: {sql_text}')

    # invoke library call to execute the queries
    sf_obj = Snowflake()
    results, num_results = sf_obj.get_data(sql_text, as_dict=True)
    log.info(f'Number of records in alert: {num_results}')

    if num_results:
        alert_msg = generate_alert_message(results)
    else:
        utc_date = datetime.utcnow().strftime("%Y-%m-%d")
        alert_msg = f'`DATA DELAY ALERT:` No delays, data caught up as of *{utc_date}*'
    
    log.info(f'\nAlert Message:{alert_msg}\n')

    context = get_current_context()
    send_audit_test_failure_alert(
        **context,
        slack_msg=alert_msg,
        msg_type=SLACK_MSG_TYPE_DATA_DELAY
    )


def update_signal_table(
    table_name: str, singal_table_name: str, current_run: str, next_run: str, audit_table: str
) -> None:
    """
    Insert data to signal audit table

    Insert data to signal audit table if next airflow_run_date is next day to current_run_date

    Parameters:
    table_name (str) : table to update in signal table
    signal_table_name (str) : name of signal audit table
    current_run (str) : current airflow runtime
    next_run (str) : next airflow run time

    Return:
    Bool
    """
    import pendulum
    next_run_date = pendulum.parse(next_run).date()
    current_run_date = pendulum.parse(current_run).date()
    if next_run_date > current_run_date:
        historical_days = 30

        sf = Snowflake()
        my_query = f"""
        insert into {singal_table_name}(TABLE_NAME, LOG_TIMESTAMP_UTC, STATUS)
        with data_completeness_confidence as(
                select dt, confidence , case when confidence >0 then 'true' else 'false' end as status
                    from(
                            select date(LOG_TIMESTAMP_UTC) as dt, 
                            SUM(ROWS_CREATED+ROWS_UPDATED) as cnt, 
                            PERCENT_RANK() over(order by cnt) as confidence
                            from {audit_table}
                            where date(LOG_TIMESTAMP_UTC) between  current_date()- {historical_days} AND current_date() -1
                            and TABLE_NAME = '{table_name}'
                            GROUP BY date(LOG_TIMESTAMP_UTC)
                            qualify PERCENT_RANK() over(order by cnt) > 0.05
                        )
                    where dt = current_date() -1
                )
        select '{table_name}', sysdate(), status
        from data_completeness_confidence;"""

        log.info("query to execute:" + str(my_query))
        sf.execute_multi_statements([my_query])
        return True
    else:
        log.info("table update condition not satisfied")
        return False

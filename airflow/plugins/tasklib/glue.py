import boto3
from aws.s3 import S3
from helpers.helper import traceback_fmt
import yaml
import time
import logging
import tasklib.config as tlcfg
from tasklib.parser import S3ToSnowflakeConfigParser

log = logging.getLogger(__name__)

def run_glue_job(glue_job_name:str, glue_job_args:dict={}, **context):

    log.info('Glue job to run: %s',glue_job_name)
    log.info('Glue job arguments: %s',str(glue_job_args))

    if not glue_job_name:
        raise Exception("Glue jobs are not specified.")

    ti=context['ti']
    glue_con = boto3.client('glue')

    try:
        # Submit new glue job run to process data
        if glue_job_args:
             response = glue_con.start_job_run(JobName=glue_job_name, Arguments=glue_job_args)
        else:
             response = glue_con.start_job_run(JobName=glue_job_name)
        log.info(f"Job response is:\n{response}")
        job_run_id = response.get('JobRunId')
        ti.xcom_push(key='glue_job_run_id', value=job_run_id)
        submission_status = response.get('ResponseMetadata').get('HTTPStatusCode')

        if submission_status != 200:
            raise Exception('Glue job submission returned non 200 status')
    except Exception as err:
        msg = traceback_fmt(err)
        log.error(msg)
        raise

    # Poke glue job run
    while True:
        response = glue_con.get_job_run(JobName=glue_job_name, RunId=job_run_id)
        job_run_status = response.get('JobRun').get('JobRunState')

        try:
            # if the job run failed or stopped, raise an exception and fail the task
            if job_run_status in ['FAILED', 'STOPPED']:
                raise Exception(f"Glue run did not succeed. Status is:\n{job_run_status}")
        except Exception as err:
            msg = traceback_fmt(err)
            log.error(msg)
            raise
        # if the job run succeeded, break the loop and exit the task
        if job_run_status == 'SUCCEEDED':
            break
        time.sleep(15)

def transfer_s3_to_snowflake(args_file,**context):
    from tasklib.parser import S3ToSnowflakeConfigParser as parser
   
    #check args file is valid 
    if not args_file:
        raise Exception("s3 to snowflake configuration file not specified.")

    ti=context['ti']

    #fetch glue s3 configuration home 
    args_s3_uri=tlcfg.get_glue_config_s3_uri()
    s3_args_file_uri=args_s3_uri + "/" + args_file
    log.info('s3 arguments file uri: %s', s3_args_file_uri)

    #get s3 list file
    parser=S3ToSnowflakeConfigParser(s3_args_file_uri)
    s3_uri_list_file=parser.get_s3_uri_list_file()
    s3_client=S3()
    file_size=s3_client.get_s3_file_size(s3_uri_list_file)
   
    if file_size >= 0: 
        log.info(f"Size of {s3_uri_list_file} is {file_size} bytes.")

    if file_size == 0:
        log.info("No new files to process.")
        log.info("Skipping execution of s3 to snowflake glue job.")
        return

    glue_job_name = "s3_to_snowflake"

    job_args={}
    job_args['--s3_uri_config_file']=s3_args_file_uri

    run_glue_job(glue_job_name, job_args, **context)

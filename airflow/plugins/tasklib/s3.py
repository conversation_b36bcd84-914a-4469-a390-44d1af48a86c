import ast
import boto3
import logging
import os
import pandas as pd
import pytz
import yaml
from datetime import datetime
from aws.s3 import S3

import tasklib.config as tlcfg
from db_connectors.sf_connector import Snowf<PERSON>
from helpers.helper import get_absolute_sql_file_path, get_s3_parts, \
    read_and_parse_sql_file, render_jinja_template

log = logging.getLogger(__name__)


def list_s3_modified_files(args_file: str, wf_params: str) -> None:
    s3 = S3()
    log.info('arguments file: %s', args_file)
    log.info('workflow parameters: %s', wf_params)

    # convert dictionary string to dictionary
    wf_params_dict = ast.literal_eval(wf_params)

    # fetch arguments configuration path
    args_s3_uri = tlcfg.get_glue_config_s3_uri()
    s3_args_file_uri = args_s3_uri + "/" + args_file
    log.info('s3 arguments file uri: %s', s3_args_file_uri)

    # fetch application configuration dictionary
    config_dict = tlcfg.get_config_dict()

    # parse the arguments from the file
    args_str_raw = s3.read_text_file(s3_args_file_uri)
    log.info(f'Job arguments before variable resolution: \n{args_str_raw}')
    args_str = render_jinja_template(args_str_raw, config_dict)
    log.info(f'Job arguments after variable resolution: \n{args_str}')

    args = yaml.safe_load(args_str)
    s3_args = args.get('list_folder', {})
    folders = s3_args.get('s3_folder_uris')
    suffix = s3_args.get('s3_file_suffix')
    list_file = s3_args.get('s3_uri_list_file')
    exclude_files = s3_args.get('s3_exclude_file_uris', [])

    # make timestamps utc aware
    start_ts = pytz.utc.localize(datetime.fromisoformat(wf_params_dict['wf_start_ts_iso_utc']))
    end_ts = pytz.utc.localize(datetime.fromisoformat(wf_params_dict['wf_end_ts_iso_utc']))

    # make library call to fetch created/modified files for the input
    files = s3.list_s3_modified_files(folders=folders, start_ts=start_ts, end_ts=end_ts, suffix=suffix)

    # remove files in the exclusion list
    new_files = files
    if exclude_files:
        new_files = list(set(files) - set(exclude_files))

    # check if there are created/modified files for the input
    if new_files:
        file_contents = "\n".join(new_files)
        log.info('files to be loaded:')
        log.info(file_contents)
    else:
        log.info('no files found for the input.')
        file_contents = ""

    # save changed file names to a list file
    s3.write_text_file(content=file_contents, file_name=list_file)


def copy_s3_file(src_file, dest_file):
    s3 = boto3.resource('s3')
    src_bucket, src_key = get_s3_parts(src_file)
    dest_bucket, dest_key = get_s3_parts(dest_file)
    copy_source = {
        'Bucket': src_bucket,
        'Key': src_key
    }
    s3.meta.client.copy(copy_source, dest_bucket, dest_key)


def move_s3_file(src_file, dest_file):
    copy_s3_file(src_file, dest_file)

    s3 = boto3.resource('s3')
    src_bucket, src_key = get_s3_parts(src_file)
    obj = s3.Object(src_bucket, src_key)
    obj.delete()


def write_snowflake_data_to_s3(sql_file: str, wf_params: str, s3_bucket: str, s3_key: str, delim: str = '\t') -> None:
    # Generate the absolute path for the sql file
    sql_file_absolute_path = get_absolute_sql_file_path(sql_file)

    # Read the query from the sql file
    query = read_and_parse_sql_file(sql_file_absolute_path, wf_params)
    log.info(f'query: {query}')

    # Run the query
    sf_obj = Snowflake()
    results, num_results = sf_obj.get_data(query, as_dict=True)
    results_df = pd.DataFrame(results)
    log.info(f'{num_results} records read')

    file_name = os.path.basename(s3_key)
    out_file = f'/opt/airflow/{file_name}'
    log.info(f'Writing data to local file: {out_file}')
    results_df.to_csv(out_file, header=True, sep=delim, index=False)

    log.info(f'Writing to S3: Bucket:{s3_bucket}, Key: {s3_key}')
    s3 = boto3.resource('s3')
    s3.Bucket(s3_bucket).upload_file(out_file, s3_key)
    log.info(f'Copied local file to s3')

    try:
        os.remove(out_file)
        log.info(f'Deleted local file {out_file}')
    except:
        log.info(f'Could not delete local file {out_file}')


def get_all_s3_files(bucket: str, prefix: str) -> list:
    _CON_KEY = 'NextContinuationToken'
    _IS_TRUNCATED_KEY = 'IsTruncated'
    files = list()
    s3_client = boto3.client('s3')
    s3_response = s3_client.list_objects_v2(Bucket=bucket, Prefix=prefix)
    extract_file_names(s3_response, files)
    while s3_response[_IS_TRUNCATED_KEY]:
        print(s3_response.keys())
        next_token = s3_response[_CON_KEY]
        s3_response = s3_client.list_objects_v2(Bucket=bucket, Prefix=prefix, ContinuationToken=next_token)
        extract_file_names(s3_response, files)

    return files


def extract_file_names(s3_response, file_list: list):
    _CONTENTS_KEY = 'Contents'
    _FILE_KEY = 'Key'
    if _CONTENTS_KEY not in s3_response:
        return
    for key in s3_response[_CONTENTS_KEY]:
        file_list.append(key[_FILE_KEY])
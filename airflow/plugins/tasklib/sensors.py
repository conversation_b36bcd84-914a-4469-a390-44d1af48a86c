import logging
from airflow.sensors.base_sensor_operator import BaseSensorOperator

log = logging.getLogger(__name__)

## TODO: <PERSON><PERSON><PERSON> needs to update the sensor to default to the correct audit table
# class SnowflakeTableSensor(BaseSensorOperator):
#     """
#     sensor to check availabilty of input query condition to be True from snowflake db
#     Template: 'table_name' -> query to run against table
#                 'date_delta' -> completeness check for (eg. 'T-1' / 'T-2')
#     Return: Success/ TimeOut
#     """

#     def __init__(self, table_name:str, date_delta:str, audit_table:str,
#                  poke_interval:int=180, timeout:int=1800, mode:str='reschedule', **kwargs):
#         self.table_name = table_name
#         self.delta = date_delta.split('-')[1]
#         self.audit_table = audit_table
#         self.poke_interval = poke_interval
#         self.timeout = timeout
#         self.mode = mode
#         # below query update is subjective to audit table schema and query pattern
#         self.sql = f"""           
#                     select 
#                         refresh_date
#                     from 
#                         {self.audit_table}
#                     WHERE 1=1
#                         AND TABLE_NAME = UPPER('{self.table_name}')
#                         AND REFRESH_DATE = CURRENT_DATE()-{self.delta}; 
#                     """
#         super().__init__(poke_interval=self.poke_interval,timeout=self.timeout,mode=self.mode,**kwargs)

#     def poke(self, context):
#         from db_connectors.sf_connector import Snowflake
#         self.snowflake_conn = Snowflake()
#         self.log.info("connection with snowflake completed")
#         rows, _ = self.snowflake_conn.get_data(self.sql, as_dict=True)
#         if not rows:
#             self.log.info("query executed with results output.")
#             return False
#         else:
#             self.log.info("query executed with no results output.")
#             return True


class SQLWorkflowSensor(BaseSensorOperator):
    '''
    Reads the start timestamp from the workflow Postgres table for the specified parent workflows.
    If a child workflow name is specified, reads the start timestamp from Postgres,
      else the function will read the data interval start timestamp from the airflow context.
    Returns False if any of the parent timestamps are earlier than the child workflow timestamp
    Returns True if all of the parent timestamps are later than the child workflow timestamp
    '''
    def __init__(self, parent_workflow_names:list, child_workflow_name:str = None,
                 poke_interval:int = 180, timeout:int = 1800, mode:str = 'reschedule', **kwargs):
        self.parent_workflow_names = list(map(str.upper, parent_workflow_names))
        self.child_workflow_name = child_workflow_name.upper() if child_workflow_name else None
        super().__init__(poke_interval=poke_interval, timeout=timeout, mode=mode, **kwargs)

    def poke(self, context):
        import pytz
        from dateutil import parser
        from db_connectors.pg_connector import Postgres

        utc = pytz.UTC

        try:
            postgres_conn = Postgres()
            self.log.info("Connected to Postgres")
        except:
            raise Exception('Failed to connect to Postgres')
        
        table_names = self.parent_workflow_names.copy()
        if self.child_workflow_name:
            table_names.append(self.child_workflow_name)
        table_names_str = ','.join([f"'{t}'" for t in table_names])

        sql = f"""           
            SELECT
                UPPER(name) AS table_name
              , TO_CHAR(watermark_start_timestamp_utc, 'YYYY-MM-DD HH24:MI:SS') AS workflow_start_utc
              , TO_CHAR(watermark_end_timestamp_utc, 'YYYY-MM-DD HH24:MI:SS') AS workflow_end_utc
            FROM workflow_configurations.workflow
            WHERE UPPER(name) IN ({table_names_str})
        """
        results, _ = postgres_conn.get_data(sql, as_dict=True)
        if len(results) != len(table_names):
            raise Exception(f'Failed to find matching records:\n{sql}')

        # Get the start timestamp for the child workflow
        if self.child_workflow_name:
            child_workflow_utc = [record['workflow_end_utc'] for record in results if record['table_name'] == self.child_workflow_name][0]
            child_workflow_utc = utc.localize(parser.parse(child_workflow_utc))
            child_workflow_name = self.child_workflow_name
        # Alternatively, get the start timestamp from the DAG run
        else:
            child_workflow_utc = context['data_interval_start']
            child_workflow_name = ''

        log.info(f'''Child workflow {child_workflow_name} timestamp: {child_workflow_utc}''')

        # Check if the parent tables have been refreshed; if any of the parent tables are not refreshed, return False
        parent_workflows_completed = True
        for record in results:
            table_name, workflow_start_utc = record['table_name'], utc.localize(parser.parse(record['workflow_start_utc']))
            if table_name in self.parent_workflow_names:
                if workflow_start_utc < child_workflow_utc:
                    parent_workflows_completed = False
                    log.info(f'Parent workflow {table_name} timestamp: {workflow_start_utc} - Yet to finish')
                else:
                    log.info(f'Parent workflow {table_name} timestamp: {workflow_start_utc} - Finished')
                    
        return parent_workflows_completed

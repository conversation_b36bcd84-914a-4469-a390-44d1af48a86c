import logging
import os
from aws.s3 import S3
import tasklib.config as tlc
import tasklib.parser as tlp

log = logging.getLogger(__name__)

def check_all_files(args_files, skip_task_id, next_task_id):
    """Function to check if any new file found in any of the files"""
    s3_client = S3()
    file_size = 0
    for args_file in args_files:
        s3_uri_list_file = get_s3_uri_list_file(args_file)
        file_size += s3_client.get_s3_file_size(s3_uri_list_file)

    if file_size:
        return next_task_id
    return skip_task_id


def check_for_new_files(args_file, skip_task_id, next_task_id, args_file_second=None):
    """
    Function called in Airflow branch operator to pick the task to
    run after checking if s3 list file is empty.
    :param prefix_list_file: str, relative path to list file
    :param skip_task_id: str, ID of task to run if file is empty.
        should mostly be a dummy task
    :param next_task_id: str, ID of task to run if file is not empty.
        should mostly be the s3_to_snowflake task
    """
    file_size_second=None
    s3_uri_list_file = get_s3_uri_list_file(args_file)
    s3_client = S3()
    file_size = s3_client.get_s3_file_size(s3_uri_list_file)
    if args_file_second:
        s3_uri_list_file_second = get_s3_uri_list_file(args_file_second)
        file_size_second = s3_client.get_s3_file_size(s3_uri_list_file_second)

    if file_size or file_size_second:
        return next_task_id
    return skip_task_id


def get_s3_uri_list_file(args_file:str)->bool:
    """
    Gets the s3 list (.lst) file
    :param args_file: dict, has the s3 ur list file as one of the keys
    """
    args_s3_uri = tlc.get_glue_config_s3_uri()
    s3_args_file_uri = os.path.join(args_s3_uri, args_file)
    log.info(f's3 arguments file uri: {s3_args_file_uri}')

    #get s3 list file
    parser = tlp.S3ToSnowflakeConfigParser(s3_args_file_uri)
    s3_uri_list_file = parser.get_s3_uri_list_file()

    return s3_uri_list_file

class S3ToSnowflake(object):
    def __init__(self):
        self.batch_size = 1000

    def get_s3_uris(self, s3_uri_list_file: str):
        import sys
        """
        get sub-lists(list of list) of s3 files without s3 bucket name prefix
        """
        s3_obj = S3()
        s3_uri_list = s3_obj.read_text_file(s3_uri_list_file)
        if s3_uri_list:
            # remove bucket name from str
            bucket_name = s3_uri_list.split("\n")[0].split('/', 3)[2]
            REPLACE_STR = f"s3://{bucket_name}/"
            s3_uris = s3_uri_list.replace(REPLACE_STR, '')
            uris = s3_uris.split("\n")
            # split files in a batch of
            if sys.getsizeof(uris[0:self.batch_size]) < 15728640:  # 15MB
                batch_size = self.batch_size
            else:
                batch_size = self.batch_size / 2
            return [uris[x:x + batch_size] for x in range(0, len(uris), batch_size)], bucket_name

    def get_absolute_file_path(self,args_file:str) -> str:
        """
        Takes the relative path to the args file and creates the absolute path
        """
        import tasklib.config as tlcfg
        dags_folder = tlcfg.get_dags_folder()

        file_abs_path = dags_folder + "/data_transfers/" + args_file
        log.info(f'filepath (absolute) is {file_abs_path}')
        return file_abs_path

    def get_copy_template_uri(self) -> str:
        """
        get uri for sf copy template file
        """
        import tasklib.config as tlcfg
        config_dict = tlcfg.get_config_dict()
        return self.get_absolute_file_path(args_file=config_dict.get('aws').get('s3').get('objects').get('sf_transfer_template'))

    def s3_to_snowflake_load(self, args_file, **context):
        """
        create copy into snowflake command and execute it for batches of files
        """
        from helpers.helper import render_jinja_template
        import yaml
        import tasklib.config as tlcfg
        from db_connectors.sf_connector import Snowflake

        # read base copy command template file
        sf_copy_jnj_abs_path = self.get_copy_template_uri()
        log.info(f"jinja file for sf copy template is {sf_copy_jnj_abs_path}")

        if not args_file:
            raise Exception("s3 to snowflake configuration file not specified.")

        # fetch table specific s3 configuration
        s3_job_file_uri = self.get_absolute_file_path(args_file=args_file)
        log.info('s3 arguments file uri: %s', s3_job_file_uri)

        # fetch env spec dictionary
        config_dict = tlcfg.get_config_dict()
        # parse the arguments for s3_to_sf job specific config file
        f = open(s3_job_file_uri, 'r')
        args_str_raw = f.read()
        # args_str_raw = s3_client.read_text_file(s3_job_file_uri)
        args_str = render_jinja_template(args_str_raw, config_dict)
        _conf = yaml.safe_load(args_str)
        s3_uri_list_file = _conf.get('s3_uri_list_file')
        s3_client = S3()
        file_size = s3_client.get_s3_file_size(s3_uri_list_file)

        if file_size >= 0:
            log.info(f"Size of {s3_uri_list_file} is {file_size} bytes.")

        if file_size == 0:
            log.info("No new files to process.")
            log.info("Skipping execution.")
            return

        list_files, source_bucket_name = self.get_s3_uris(s3_uri_list_file=s3_uri_list_file)

        config_dict["source_bucket_name"] = f's3://{source_bucket_name}/'
        log.info(f"config dict is {config_dict}")
        job_conf_str = render_jinja_template(args_str_raw, config_dict)
        jinja_conf = yaml.safe_load(job_conf_str)
        log.info(f"data in job specs is \n {jinja_conf}")

        obj = Snowflake()

        if jinja_conf.get('snow_savemode').lower() == 'override':
            log.info(f"run truncate command in sf")
            trunc_query = f""" TRUNCATE TABLE {jinja_conf.get('snow_database')}.{jinja_conf.get('snow_schema')}.{jinja_conf.get('snow_table_name')};
                    """
            log.info(f"sf command to execute- {trunc_query}")
            obj.execute_statement(trunc_query)

        g = open(sf_copy_jnj_abs_path, 'r')
        copy_str_raw = g.read()

        # execute copy command in batches
        for files in list_files:
            log.info(f"files list is - {files}")
            jinja_conf["file_list"] = files
            copy_statement = render_jinja_template(copy_str_raw, jinja_conf)
            # execute query
            log.info("Executing copy query in sf")
            log.info(f"sf command to execute- {copy_statement}")
            obj.execute_statement(copy_statement)
        
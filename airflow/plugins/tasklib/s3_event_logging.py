import ast
import boto3
import os
import logging

import tasklib.config as tlcfg
import tasklib.s3 as tls3
from db_connectors.sf_connector import Snowflake
from helpers.helper import get_s3_parts, render_jinja_template

config = tlcfg.get_config()
_AWS_ACCOUNT_ID = ************
_REGION_NAME = config['s3_event_logging']['region_name']
_SQS_QUEUE_NAME = config['s3_event_logging']['sqs_queue_name']
_S3_PREFIX_STAGING = 's3_create_events'   
_S3_BACKUP_FILE = 's3://{{aws.s3.buckets.s3_staging_bucket}}/{s3_prefix}/s3_events_{sqs_queue_name}_{epoch_time}.csv'
_S3_FILE = 's3://{{aws.s3.buckets.s3_staging_bucket}}/raptor/{s3_prefix}/s3_events_{sqs_queue_name}.csv'
_SQS_URL = "https://sqs.{region_name}.amazonaws.com/{aws_account_id}/{sqs_queue_name}"

log = logging.getLogger(__name__)


def backfill_events_to_snowflake():
    '''
    One time backfill of data not pulled in by sqs
    '''
    from db_connectors.sf_connector import Snowflake
    obj = Snowflake()
    obj.load_data_from_s3(
        stage_name='dwh.raptor.s3_stage',
        s3_prefix='s3_create_events_backfill/',
        qualified_table_name='dwh.staging.staging_monitor_s3_datalake_event_logs_backfill',
        copy_options="FILE_FORMAT = (TYPE=CSV SKIP_HEADER=1 FIELD_DELIMITER='\t')")
    

def data_cleanup(**context):
    '''
    Delete messages that have been loaded to Snowflake from the SQS queue
    Remove the s3 file created in the current run
    '''
    from helpers.helper import resolve_db_variables

    sqs_queue_name = context.get('sqs_queue_name', _SQS_QUEUE_NAME)
    aws_account_id = context.get('aws_account_id', _AWS_ACCOUNT_ID)
    region_name = context.get('region_name', _REGION_NAME)

    sf_client = Snowflake()
    ts = context.get('ts')
    query = f'''
    SELECT DISTINCT message_id, receipt_handle
    FROM $infra_db.monitor_s3_datalake_event_logs
    WHERE sqs_queue_name = '{sqs_queue_name}'
      AND record_updated_timestamp_utc = '{ts}';'''
    query = resolve_db_variables(query)
    result, num_rows = sf_client.get_data(query, as_dict=True)
    log.info(f'{num_rows} messages to delete')

    sqs_client = boto3.client("sqs", region_name=region_name)
    queue_url = _SQS_URL.format(
        region_name=region_name,
        aws_account_id=aws_account_id,
        sqs_queue_name=sqs_queue_name)
    
    # can delete only 10 messages at a time
    for idx in range(0, len(result), 10):
        entries = [{'Id': msg['MESSAGE_ID'], 'ReceiptHandle': msg['RECEIPT_HANDLE']} for msg in result[idx:idx+10]]
        try:
            response = sqs_client.delete_message_batch(QueueUrl=queue_url, Entries=entries)
            if len(response['Successful']) != len(entries):
                raise RuntimeError(
                    f"Failed to delete all messages: entries={entries!r} resp={response!r}"
                )
        except:
            raise Exception('Error deleting messages')
    s3 = boto3.resource('s3')
    s3_file = context.get('s3_file')
    if not s3_file:
        s3_file = resolve_variables(_S3_FILE, s3_prefix=_S3_PREFIX_STAGING, sqs_queue_name=sqs_queue_name)
    s3_bucket, s3_key = get_s3_parts(s3_file)
    s3.Object(s3_bucket, s3_key).delete()


def load_messages_to_snowflake():
    config = tlcfg.get_config_dict()
    stage_name = config['snowflake']['stage']['raptor']

    table_name_raw = '{{snowflake.db.dwh}}.{{snowflake.schema_.staging}}.staging_monitor_s3_datalake_event_logs'
    table_name = render_jinja_template(table_name_raw, config)
    log.info(f'Loading to table: {table_name}')

    obj = Snowflake()
    obj.load_data_from_s3(
        stage_name=stage_name,
        s3_prefix=_S3_PREFIX_STAGING,
        qualified_table_name=table_name,
        copy_options="FILE_FORMAT = (TYPE=CSV SKIP_HEADER=1 FIELD_DELIMITER='\t')")


def read_messages_from_queue(sqs_queue_name=_SQS_QUEUE_NAME, aws_account_id=_AWS_ACCOUNT_ID,
                             region_name=_REGION_NAME):

    sqs_client = boto3.client("sqs", region_name=region_name)
    queue_url = _SQS_URL.format(
        region_name=region_name,
        aws_account_id=aws_account_id,
        sqs_queue_name=sqs_queue_name)

    messages = []
    while True:
        response = sqs_client.receive_message(
            QueueUrl=queue_url,
            AttributeNames=['All'],
            MaxNumberOfMessages=10,
        )
        try:
            messages.extend(response['Messages'])
        except KeyError:
            break

    log.info(f'{len(messages)} messages read from the {queue_url} queue')
    return messages


def resolve_variables(in_str, **kwargs):
    config_dict = tlcfg.get_config_dict()
    out_str = render_jinja_template(in_str, config_dict).format(**kwargs)
    return out_str


def save_sqs_messages(**context):
    '''
    Read messages from the SQS queue and write the data to s3
    '''

    sqs_queue_name = context.get('sqs_queue_name', _SQS_QUEUE_NAME)
    aws_account_id = context.get('aws_account_id', _AWS_ACCOUNT_ID)
    region_name = context.get('region_name', _REGION_NAME)
    
    s3_file = context.get('s3_file')
    if not s3_file:
        s3_file = resolve_variables(_S3_FILE, s3_prefix=_S3_PREFIX_STAGING, sqs_queue_name=sqs_queue_name)

    # Read messages from SQS and write to s3
    messages = read_messages_from_queue(
        sqs_queue_name=sqs_queue_name, aws_account_id=aws_account_id, region_name=region_name)
    
    log.info(f'Writing s3 file: {s3_file}')
    write_formatted_data_to_s3(messages, sqs_queue_name, s3_file)

    # Backup data
    execution_date = context['execution_date'].strftime('%s')
    s3_backup_file = context.get('s3_backup_file')
    if not s3_backup_file:
        s3_backup_file = resolve_variables(_S3_BACKUP_FILE, 
            s3_prefix=_S3_PREFIX_STAGING, epoch_time=execution_date, sqs_queue_name=sqs_queue_name)
    log.info(f'Writing backup s3 file: {s3_backup_file}')
    tls3.copy_s3_file(s3_file, s3_backup_file)


def write_formatted_data_to_s3(messages, sqs_queue_name, s3_file):
    import time
    from collections import OrderedDict

    epoch_time = int(time.time())
    out_file = f'/opt/airflow/formatted_s3_events_{sqs_queue_name}_{epoch_time}.csv'
    log.info(f'Writing data to local file: {out_file}')
    header = None
    with open(out_file, 'w') as f:
        for msg in messages:
            body = ast.literal_eval(msg['Body'])
            if 'Records' in body:
                s3_record = body['Records'][0]['s3']
                # This matches the staging_monitor_s3_datalake_event_logs table
                data = OrderedDict({
                    's3_bucket': s3_record['bucket']['name'],
                    's3_key': s3_record['object']['key'],            
                    'event_time_utc': body['Records'][0]['eventTime'],
                    'size': s3_record['object']['size'],            
                    'message_id': msg['MessageId'],
                    'receipt_handle': msg['ReceiptHandle'],
                    'configuration_id': s3_record['configurationId'],
                    'arn': s3_record['bucket']['arn'],
                    'principal_id': s3_record['bucket']['ownerIdentity']['principalId'],
                    'sqs_queue_name': sqs_queue_name,
                })
                if header is None:
                    header = data.keys()
                    f.write('\t'.join(header) + '\n')
                line = '\t'.join(map(str, [data[h] for h in header]))
                f.write(line + '\n')

    s3_bucket, s3_key = get_s3_parts(s3_file)
    s3 = boto3.resource('s3')
    s3.Bucket(s3_bucket).upload_file(out_file, s3_key)

    try:
        os.remove(out_file)
        log.info(f'Deleted local file {out_file}')
    except:
        log.info(f'Could not delete local file {out_file}')

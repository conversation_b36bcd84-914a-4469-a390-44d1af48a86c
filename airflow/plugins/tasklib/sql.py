import logging
from typing import <PERSON>ple
from snowflake.connector.pandas_tools import write_pandas

log = logging.getLogger(__name__)


def resolve_query(sql_file:str = None, sql_text:str = None, wf_params:str = None, add_params_dict:dict = {}, **context) -> str:
    '''
    Read the query file or query text and resolve the db, workflow and any custom variables.
    '''
    import ast
    from helpers.helper import get_absolute_sql_file_path, load_db_variables, multi_replace_string

    if sql_file:
        sql_file_abs_path = get_absolute_sql_file_path(sql_file)
        log.info(f"sql file is {sql_file_abs_path}")

        #read query file contents
        sql_text = ""
        with open(sql_file_abs_path, 'r') as f:
            sql_text = f.read()

    if sql_text:
        sql_text = sql_text.strip()

    # Loads configuration variables for dbs.
    sql_vars_dict = load_db_variables()

    # convert params dictionary string to dictionary
    wf_params_dict = {}
    if wf_params is not None:
        wf_params_dict = ast.literal_eval(wf_params)

        if 'wf_start_ts_iso_utc' in wf_params_dict:
            sql_vars_dict['start_ts'] = wf_params_dict.get('wf_start_ts_iso_utc')
        if 'wf_end_ts_iso_utc' in wf_params_dict:
            sql_vars_dict['end_ts'] = wf_params_dict.get('wf_end_ts_iso_utc')

    # replace variables names with the appropriate values
    sql_text = multi_replace_string(sql_text, sql_vars_dict)
    sql_text = multi_replace_string(sql_text, wf_params_dict)
    sql_text = multi_replace_string(sql_text, add_params_dict)
    log.info("queries to be executed:")
    log.info(sql_text)

    return sql_text


def get_data_frame_from_query(sql_text: str = None, wf_params: str = None, as_dict: bool = False, **context):
    from db_connectors.sf_connector import Snowflake
    from helpers.helper import set_query_tag

    query = resolve_query(sql_text=sql_text, wf_params=wf_params, **context)

    query_tag = None

    if context:
        query_tag = set_query_tag(**context)

    sf = Snowflake()
    data, num_rows = sf.get_data_frame(sql_statement=query, query_tag=query_tag, as_dict=as_dict)
    return data, num_rows

def get_data_from_query(sql_file: str = None, sql_text: str = None, wf_params: str = None, **context) -> Tuple[dict, int]:
    '''
    Read the query file or query text, resolve the db, workflow and any custom variables, and return
    the data
    '''
    from db_connectors.sf_connector import Snowflake
    from helpers.helper import set_query_tag

    query = resolve_query(sql_file=sql_file, sql_text=sql_text, wf_params=wf_params, **context)

    query_tag = None

    if context:
        query_tag = set_query_tag(sql_file=sql_file, **context)

    sf = Snowflake()
    data, num_rows = sf.get_data(sql_statement=query, query_tag=query_tag, as_dict=True)
    return data, num_rows


def run_query_file(sql_file:str=None, wf_params:str=None, connection:str='Snowflake', **context):
    import ast
    from db_connectors.sf_connector import Snowflake
    from helpers.helper import get_absolute_sql_file_path, load_db_variables, multi_replace_string, set_query_tag

    #read schema, database names from application configuration for variables used in the sql
    sql_vars_dict = load_db_variables()

    sql_file_abs_path = get_absolute_sql_file_path(sql_file)
    log.info(f"sql file is {sql_file_abs_path}")

    #read query file contents
    sql_text=""
    with open(sql_file_abs_path, 'r') as f:
        sql_text=f.read()

    #remove leading and trailing spaces, tabs
    if sql_text:
        sql_text=sql_text.strip()

    #convert params dictionary string to dictionary
    wf_params_dict=ast.literal_eval(wf_params)

    sql_vars_dict['start_ts']=wf_params_dict.get('wf_start_ts_iso_utc')
    sql_vars_dict['end_ts']=wf_params_dict.get('wf_end_ts_iso_utc')

    #replace variables names with the appropriate values
    sql_text2=multi_replace_string(sql_text, sql_vars_dict)
    sql_text2=multi_replace_string(sql_text2, wf_params_dict)
    
    log.info("queries to be executed:")
    log.info(sql_text2)

    query_tag = None
    if context:
        query_tag = set_query_tag(sql_file=sql_file, **context)

    #make a list of queries using ; as query delimiter
    query_list=sql_text2.split(';')

    # invoke library call to execute the queries
    sf=Snowflake()
    sf.execute_multi_statements(query_list, query_tag=query_tag)


def run_query_text(sql_text: str = None, wf_params: str = None, connection: str = 'Snowflake', **context):
    import ast
    from db_connectors.sf_connector import Snowflake
    from helpers.helper import load_db_variables, multi_replace_string, set_query_tag

    # Loads configuration variables for dbs.
    sql_vars_dict = load_db_variables()

    if sql_text:
        sql_text = sql_text.strip()

    # convert params dictionary string to dictionary
    wf_params_dict = ast.literal_eval(wf_params)

    sql_vars_dict['start_ts'] = wf_params_dict.get('wf_start_ts_iso_utc')
    sql_vars_dict['end_ts'] = wf_params_dict.get('wf_end_ts_iso_utc')

    # replace variables names with the appropriate values
    sql_text2 = multi_replace_string(sql_text, sql_vars_dict)
    sql_text2 = multi_replace_string(sql_text2, wf_params_dict)
    log.info("queries to be executed:")
    log.info(sql_text2)

    query_tag = None
    if context:
        query_tag = set_query_tag(**context)

    # make a list of queries using ; as query delimiter
    query_list = sql_text2.split(';')

    # invoke library call to execute the queries
    sf = Snowflake()
    sf.execute_multi_statements(query_list, query_tag=query_tag)

def run_pandas_data(dataframe, table_name: str, auto_create_table: bool = False, overwrite: bool = False,  **context):
    from db_connectors.sf_connector import Snowflake
    from helpers.helper import load_db_variables, multi_replace_string, set_query_tag

    sql_vars_dict = load_db_variables()

    if table_name:
        table_name = table_name.strip()

    table_name = multi_replace_string(table_name, sql_vars_dict)

    table_name_split = table_name.split(".")
    database_name = None
    schema_name = None
    if len(table_name_split) == 3:
        database_name = table_name_split[0]
        schema_name = table_name_split[1]
        table_name = table_name_split[2]

    log.info(f"dataframe to be executed on table: {table_name}")

    # query_tag = set_query_tag(**context)
    query_tag = None

    sf = Snowflake()
    sf.write_data_frame(dataframe, database_name, schema_name, table_name, query_tag, auto_create_table, overwrite)
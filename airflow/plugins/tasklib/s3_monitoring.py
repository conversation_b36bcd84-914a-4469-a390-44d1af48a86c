import logging
import pandas as pd
import time
from airflow.models import Variable
from datetime import datetime, timedelta

from helpers.helper import get_snapshot_date
from tasklib.alerts import send_audit_test_failure_alert
from tasklib.config import get_config_dict
from tasklib.sql import get_data_from_query

log = logging.getLogger(__name__)

_ALERT_HEADER_TEMPLATE = '`{msg_prefix} - OMNICHANNEL BRANDS: TIME {pst_time_now} PST{add_str}`\n'
_DAG_HYPERLINK = '<https://12d5ebd7-0523-41b0-a483-c8658e474d3b.c1.us-east-2.airflow.amazonaws.com/tree?dag_id={nm}|{nm}>'
_DEPENDANT_DAG = 'DEPENDANT_DAG'
_ENTITY_NAME = 'ENTITY_NAME'
_INTEGRATION_NAME = 'INTEGRATION_NAME'
_INTEGRATION_NAME_HYPERLINK = 'INTEGRATION_NAME_HYPERLINK'
_IS_DELAYED_NO_FILE = 'IS_DELAYED_NO_FILE'
_IS_PREVIOUSLY_ALERTED = 'IS_PREVIOUSLY_ALERTED'
_MSG_PREFIXES = {
    _IS_DELAYED_NO_FILE: 'MISSING S3 FILES',
}
_OMNICHANNEL_BRANDS = ['BOK', 'BSY', 'DCZ', 'IRO', 'KWX', 'NUM', 'TOL', 'ZSK', 'DBLY', 'FCTY', 'SSCB']
_RECORD_CREATED_TIMESTAMP = 'RECORD_CREATED_TIMESTAMP_UTC'
_REPORT_NAME_NORMALIZED = 'REPORT_NAME_NORMALIZED'


def create_additional_alert_message(df, delay_col, msg_prefix):
    '''
    Create additional messaging for number of alerts in last 24 hours and last 7 days
    '''

    previously_alerted_df = df[(df[_IS_PREVIOUSLY_ALERTED] == 0) & (df[delay_col] == 1)]

    # -- Omnichannel alerts ----------

    filtered_df = previously_alerted_df[(previously_alerted_df[_ENTITY_NAME].isin(_OMNICHANNEL_BRANDS))]
    log.info(f'Number of records for omnichannel brands: {len(filtered_df)}')

    # Check new omnichannel alerts in the last 24 hours
    min_ts = datetime.now() - timedelta(hours=24)
    cur_filtered_df = filtered_df[filtered_df[_RECORD_CREATED_TIMESTAMP] >= min_ts]
    alert_msg_1 = f'''{len(cur_filtered_df)} new alerts in the last 24 hours'''
    log.info(alert_msg_1)
   
    # Check new omnichannel alerts in the last 7 days
    min_ts = datetime.now() - timedelta(days=7)
    cur_filtered_df = filtered_df[filtered_df[_RECORD_CREATED_TIMESTAMP] >= min_ts]
    alert_msg_2 = f'''{len(cur_filtered_df)} new alerts in the last 7 days'''
    log.info(alert_msg_2)

    # -- Non-Omnichannel alerts ----------

    filtered_df = previously_alerted_df[~(previously_alerted_df[_ENTITY_NAME].isin(_OMNICHANNEL_BRANDS))]
    log.info(f'Number of records for non-omnichannel brands: {len(filtered_df)}')

    # Check new non-omnichannel alerts in the last 24 hours
    min_ts = datetime.now() - timedelta(hours=24)
    cur_filtered_df = filtered_df[filtered_df[_RECORD_CREATED_TIMESTAMP] >= min_ts]
    alert_msg_3 = f'''{len(cur_filtered_df)} new alerts in the last 24 hours'''
    log.info(alert_msg_3)

    # Check new omnichannel alerts in the last 7 days
    min_ts = datetime.now() - timedelta(days=7)
    cur_filtered_df = filtered_df[filtered_df[_RECORD_CREATED_TIMESTAMP] >= min_ts]
    alert_msg_4 = f'''{len(cur_filtered_df)} new alerts in the last 7 days'''
    log.info(alert_msg_4)
    
    alert_msg = f'OMNICHANNEL BRANDS:\n{alert_msg_1}\n{alert_msg_2}\n\n'
    alert_msg += f'NON-OMNICHANNEL BRANDS:\n{alert_msg_3}\n{alert_msg_4}\n\n'

    monitor_s3_dashboard = Variable.get("monitor_s3_dashboard")
    alert_msg += f'DASHBOARD: <{monitor_s3_dashboard}|link>'

    alert_msg = f'`{msg_prefix} - STATS`\n```{alert_msg}```'

    return alert_msg


def create_alert_message(df, pst_time_now, delay_col, msg_prefix):
    '''
    Create the alert message using the results dataframe
    :param df: pandas dataframe with alert results
    :pst_time_now: str, pst time to add to messaging
    :delay_col: str, column for delay (e.g. IS_DELAYED_NO_FILE)
    :msg_prefix: str, prefix to add at start of message block
    '''

    # Stores all the alert messages
    alert_msgs = []

    # Get the latest records not previously alerted on
    filtered_df = df[(df[_RECORD_CREATED_TIMESTAMP] == df[_RECORD_CREATED_TIMESTAMP].max())
                   & (df[_IS_PREVIOUSLY_ALERTED] == 0) 
                   & (df[delay_col] == 1)
                   & (df[_ENTITY_NAME].isin(_OMNICHANNEL_BRANDS))]
    log.info(f'Number of records not previously alerted for {delay_col}: {len(filtered_df)}')
    
    alert_header = _ALERT_HEADER_TEMPLATE.format(msg_prefix=msg_prefix, pst_time_now=pst_time_now, add_str='')
    alert_msg = ''

    # Split alerts into chunks to avoid message truncation in slack
    if not filtered_df.empty:
        # Create a column with hyperlink formatted for slack
        filtered_df[_INTEGRATION_NAME_HYPERLINK] = filtered_df.apply(lambda x: f"<{x['S3_URL']}|{x[_INTEGRATION_NAME]}>", axis=1)

        # Get all the integrations for a given report and dag
        grouped_df = filtered_df \
                        .groupby([_REPORT_NAME_NORMALIZED, _DEPENDANT_DAG])[_INTEGRATION_NAME_HYPERLINK] \
                        .agg(lambda x: ', '.join(x)) \
                        .reset_index()
        # When we send out alerts, group all reports for a dag in one message block
        grouped_dag_df = grouped_df.groupby([_DEPENDANT_DAG])

        # Header for each message block
        for idx, (nm, grp) in enumerate(grouped_dag_df):
            data = grp.to_dict('records')
            data_str = '\n'.join([f'REPORT NAME: {r[_REPORT_NAME_NORMALIZED]}, INTEGRATION NAMES: {r[_INTEGRATION_NAME_HYPERLINK]}' for r in data])
            dag_str = _DAG_HYPERLINK.format(nm=nm)
            cur_msg = f'\nDAG: {dag_str}\n{data_str}'
            # Limiting to fewer characters to avoid message truncation
            if len(alert_msg) + len(cur_msg) <= 5000:
                alert_msg += cur_msg
            # Send out new message when character limit is met or its the last message block
            elif len(alert_msg) + len(cur_msg) > 5000:
                alert_msg = f'{alert_header}\n```{alert_msg}```'
                alert_msgs.append(alert_msg)
                alert_header = _ALERT_HEADER_TEMPLATE.format(msg_prefix=msg_prefix, pst_time_now=pst_time_now, add_str=' (contd) ')
                alert_msg = cur_msg
            # If this is the last message block, add it to the alerts
            if len(grouped_dag_df) == idx + 1:
                alert_msg = f'{alert_header}\n```{alert_msg}```'
                alert_msgs.append(alert_msg)
    else:
        alert_msg = f'{alert_header}\n```No new alerts```'
        alert_msgs.append(alert_msg)

    return alert_msgs


def send_alert(sql_file: str, wf_params: str, delay_col: str, **context):
    '''
    Send the alerts to the specified slack channel
    :param sql_file: The query to generate the results for alerting
    :param wf_params: parameters passed on from airflow dag
    :param delay_col: Delay column name (e.g. IS_DELAYED_NO_FILE)
    '''

    # Get the PST Time now
    pst_time_now = get_snapshot_date(delta_hours=0, date_format='%Y-%m-%d %H:%M')

    # Read the slack channel to send notifications 
    slack_connection_name = context.get('slack_connection_name')
    if not slack_connection_name:
        config = get_config_dict()
        slack_connection_name = config['airflow']['connections']['slack']['source_monitor_alerts']

    # Get the alerts that were not previously sent
    results, num_results = get_data_from_query(sql_file=sql_file, wf_params=wf_params, add_params_dict={}, **context)
    results_df = pd.DataFrame(results)
    log.info(f'Number of records in current alert: {num_results}')

    if not results_df.empty:
        results_df[_RECORD_CREATED_TIMESTAMP] = pd.to_datetime(results_df[_RECORD_CREATED_TIMESTAMP])

    try:
        msg_prefix = _MSG_PREFIXES[delay_col]
    except:
        raise Exception('{delay_col} not supported yet')

    if delay_col == _IS_DELAYED_NO_FILE:
        alert_msgs = create_alert_message(results_df, pst_time_now, delay_col, msg_prefix)
    else:
        raise Exception('{delay_col} not implemented yet')

    add_alert_msg = create_additional_alert_message(results_df, delay_col, msg_prefix)
    alert_msgs.append(add_alert_msg)

    # Send out messages separately
    for idx, alert_msg in enumerate(alert_msgs):
        log.info(f'alert_msg: {alert_msg}')
        send_audit_test_failure_alert(
            slack_msg=alert_msg,
            msg_type=slack_connection_name,
            **context,
        )
        if idx < len(alert_msg) - 1:
            time.sleep(5)

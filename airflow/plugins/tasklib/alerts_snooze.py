from airflow.models import Variable
from datetime import datetime
from helpers.helper import resolve_db_variables


def auto_snooze_alert(sf_obj, dag_id):
    '''
    Auto snooze a soft alert when it fails for a specified number of times in a day
    This works only for hourly alerts, as it looks at a specified number of failed
    alerts in the last N (default = 12) hours.
    :param sf_obj: Snowflake object
    :param dag_id: str, DAG id to setup auto snooze for
    '''

    # Auto snooze on 4 or more failures in the last 24 hours
    num_snooze_alert_limit = Variable.get("num_snooze_alert_limit", 4)
    num_snooze_alert_limit = int(num_snooze_alert_limit)
    print(f'num_snooze_alert_limit: {num_snooze_alert_limit}')

    # Auto snooze for 12 hours by default
    num_hours_to_auto_snooze = Variable.get('num_hours_to_auto_snooze', 12)
    num_hours_to_auto_snooze = int(num_hours_to_auto_snooze)
    print(f'num_hours_to_auto_snooze: {num_hours_to_auto_snooze}')

    query = f'''
        INSERT INTO $infra_db.alert_snooze (
            dag_id
          , task_id
          , test_id
          , start_date_utc
          , end_date_utc
          , is_alert_auto_snoozed
          , is_active
        )
            WITH failed_test_counts AS (
                SELECT
                    dag_id
                  , task_id
                  , dq_failed_test AS test_id
                  , COUNT(1) AS num_failed_tests
                FROM $infra_db.alert_failure_log
                WHERE UPPER(dq_severity) = 'SOFT'
                  AND LOWER(dag_id) = '{dag_id.lower()}'
                  AND start_date >= TIMEADD(hour, -{num_hours_to_auto_snooze}, SYSDATE())
                  AND task_id IS NOT NULL
                  AND test_id IS NOT NULL
                  AND NOT is_alert_snoozed
                  AND NOT is_alert_auto_snoozed
                GROUP BY dag_id, task_id, test_id
                HAVING num_failed_tests >= {num_snooze_alert_limit}
            )
            , active_records AS (
                SELECT DISTINCT
                    dag_id
                  , task_id
                  , test_id
                  , start_date_utc
                  , end_date_utc
                FROM $infra_db.alert_snooze
                WHERE is_alert_auto_snoozed = True
                   OR is_active = True
                QUALIFY ROW_NUMBER() OVER (
                    PARTITION BY dag_id, task_id, test_id 
                    ORDER BY is_alert_auto_snoozed DESC
                           , is_active DESC
                           , start_date_utc DESC NULLS LAST
                           , end_date_utc DESC NULLS LAST) = 1
            )
            SELECT 
                f.dag_id
              , f.task_id
              , f.test_id
              , SYSDATE() AS start_date_utc_new
              , TIMEADD(hour, {num_hours_to_auto_snooze}, SYSDATE()) AS end_date_utc_new
              , True AS is_alert_auto_snoozed
              , False AS is_active
            FROM failed_test_counts AS f
            LEFT JOIN active_records AS a
                ON f.dag_id = a.dag_id
               AND f.task_id = a.task_id
               AND f.test_id = a.test_id
              -- No matching record found
            WHERE COALESCE(a.dag_id, a.task_id, a.test_id) IS NULL
              -- OR the previous record is not in an overlapping timeframe
              OR NOT(a.start_date_utc BETWEEN start_date_utc_new AND end_date_utc_new
                  OR a.end_date_utc BETWEEN start_date_utc_new AND end_date_utc_new
                  OR start_date_utc_new BETWEEN a.start_date_utc AND a.end_date_utc);'''
    query = resolve_db_variables(query)
    sf_obj.execute_statement(query)


def check_if_alert_snoozed(data, task_id=None, test_id=None):
    '''
    Check if an alert needs to be snoozed
    :param data: dictionary, includes dag_id, task_id for snoozed alerts
    :param task_id: str, task_id to check if it needs to be snoozed
    :param test_id: str, test_id to check if it needs to be snoozed
    :return (is_alert_snoozed, is_alert_auto_snoozed, snooze_alert_id): 
        is_alert_snoozed is True if the alert is marked to be snoozed manually
        is_alert_auto_snoozed is True if the alert marked to be snoozed automatically
        snooze_alert_id is the value from the snooze table (primary key) to refer to
    '''

    def _check_alert(filtered_data):
        '''Returns True & alert id if a particular alert was marked to be snoozed'''
        snooze_alert_id = -1
        is_alert_snoozed = False
        is_alert_auto_snoozed = False
        if filtered_data:
            utc_time = datetime.utcnow()
            filtered_data = filtered_data[0]
            snooze_alert_id = filtered_data['SNOOZE_ALERT_ID']
            # Get the start & end timestamp for matched record
            start_date_utc, end_date_utc = filtered_data['START_DATE_UTC'], filtered_data['END_DATE_UTC']
            # Check if its an auto snoozed alert
            is_alert_auto_snoozed = filtered_data['IS_ALERT_AUTO_SNOOZED']
            # Snooze the alert if a) the time now is between start & end time where start and end time is not null
            # OR b) start and end time is null
            if (start_date_utc and end_date_utc and (start_date_utc <= utc_time <= end_date_utc)) or \
                    not (start_date_utc and end_date_utc):
                is_alert_snoozed = True
        return is_alert_snoozed, is_alert_auto_snoozed, snooze_alert_id

    # Check if the test is set to snooze
    if test_id:
        filtered_data = [record for record in data if record['TASK_ID']==task_id and record['TEST_ID']==test_id]
        if filtered_data:
            return _check_alert(filtered_data)

    # Check if the task is set to snooze
    if task_id:
        filtered_data = [record for record in data if record['TASK_ID']==task_id and not record['TEST_ID']]
        if filtered_data:
            return _check_alert(filtered_data)

    # Check if the DAG is set to snooze
    filtered_data = [record for record in data if not record['TASK_ID'] and not record['TEST_ID']]
    return _check_alert(filtered_data)


def read_snooze_alert_data(sf_obj, context):
    '''
    Read active records from snooze alert data
    :param sf_obj: Snowflake object
    :param context: dict, Airflow parameters
    :return data: dict, Data with task_id, test_id etc if alert was set to snooze
    '''

    dag_id = context['dag'].dag_id

    dags_to_skip_auto_snooze = Variable.get('dags_to_skip_auto_snooze', deserialize_json=True)
    dags_to_skip_auto_snooze = [d.lower() for d in dags_to_skip_auto_snooze]
    disable_auto_snooze = Variable.get("disable_auto_snooze", 0)

    if int(disable_auto_snooze) == 1 or dag_id.lower() in dags_to_skip_auto_snooze:
        print('Skipping auto snooze')
    else:
        auto_snooze_alert(sf_obj, dag_id)

    # Skip read auto snooze data when dag is set to skip auto snooze
    where_predicate_str = 'WHERE (is_active = True OR is_alert_auto_snoozed = True)'
    if dags_to_skip_auto_snooze:
        dags_to_skip_auto_snooze_str = ','.join([f"'{tbl.upper()}'" for tbl in dags_to_skip_auto_snooze])
        where_predicate_str = f'''
        WHERE (is_active = True
           OR (is_alert_auto_snoozed = True AND UPPER(dag_id) NOT IN ({dags_to_skip_auto_snooze_str})))'''

    # Read the latest  matching record
    query = f"""
        SELECT
            *
          , (CASE WHEN is_alert_auto_snoozed = True THEN 0 ELSE 1 END) AS is_alert_auto_snoozed_rank
        FROM $infra_db.alert_snooze
        {where_predicate_str}
          AND UPPER(dag_id) = '{dag_id.upper()}'
        QUALIFY ROW_NUMBER() OVER (
            PARTITION BY dag_id, task_id, test_id
            ORDER BY snooze_alert_id DESC) = 1;
        """
    query = resolve_db_variables(query)
    data, _ = sf_obj.get_data(query, as_dict=True)
    return data


def read_snooze_log_data(sf_obj, alert_id):
    '''Read the previously logged data for specifed alert_id'''

    query = f"""
        SELECT 
            *
        FROM $infra_db.alert_failure_log
        WHERE snooze_alert_id = {alert_id}
        ORDER BY record_created_timestamp_utc DESC
        """
    query = resolve_db_variables(query)
    data, _ = sf_obj.get_data(query, as_dict=True)
    return data

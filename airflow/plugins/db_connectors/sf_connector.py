import logging
from airflow.providers.snowflake.hooks.snowflake import SnowflakeHook
from snowflake.connector import Dict<PERSON>ursor
from snowflake.connector.pandas_tools import write_pandas

from helpers.helper import *

log = logging.getLogger(__name__)


class Snowflake(object):
    def __init__(self, conn_id=None):
        self.conn_id = conn_id if conn_id else 'snowflake_connection'

    def _get_connection(self):
        """
        This method will get the snowflake connection by reading the connection properties that are set in airflow
        :return: snowflake connection
        """
        try:
            return SnowflakeHook(snowflake_conn_id=self.conn_id).get_conn()
        except Exception as err:
            msg = traceback_fmt(err)
            log.error(f"Error occured while trying to connect to database:\n{msg}")
            raise

    def get_data(self, sql_statement: str, as_dict: bool = False, query_tag: str = None):
        """
        Method will execute query and returns result. An optional parameter as_dict can be passed to get the resultset
        as list of dictionaries instead of list of tuples for further processing or referring columns. Method returns
        resultset and record count.
        Usage: res, _=get_data('select * from table')
                res, _=get_data('select * from table', as_dict=True)
          to discard row_count just use _ or some garbage variable that can be discarded. If row_count is required for
          further processing, assign it to a named variable

        :param sql_statement: valid SQL statement that can be executed by most of the db engines
        :param as_dict:
        :return: resultset and row count (resultset can be either a list of tuples or list of dictionaries)
        """
        try:
            conn = self._get_connection()
            cur = conn.cursor(DictCursor) if as_dict else conn.cursor()

            if query_tag:
                sql_query_tag = generate_query_tag_sql(query_tag)
                cur.execute(sql_query_tag)

            log.info(f"Executing sql statement:\n{sql_statement}")
            res = cur.execute(sql_statement).fetchall()
            row_count = cur.rowcount
            cur.close()
            conn.close()
            return res, row_count
        except Exception as err:
            if not cur.is_closed():
                cur.close()
            if not conn.is_closed():
                conn.close()
            msg = traceback_fmt(err)
            log.error(f"Error occurred while trying to connect to database:\n{msg}")
            raise

    def _execute(self, query_to_execute: str = None, statements_list: list = None, query_tag: str = None):
        """
        This is a private method that will be called by execute_statement and execute_multi_statement methods
        depending on whether a single statement is executed or list of statements are executed, this method will loop
        through all the different statements and executes them.

        :param: query_to_execute: valid sql query to execute
        :param: statements_list: list of valid sql statements to execute

        #TO-DO: Implementation of multistatement execution in a transaction
        """
        try:
            conn = self._get_connection()
            with conn.cursor() as cur:

                if query_tag:
                    sql_query_tag = generate_query_tag_sql(query_tag)
                    cur.execute(sql_query_tag)

                if query_to_execute:
                    log.info(f"Executing sql statement:\n{query_to_execute}")
                    cur.execute(query_to_execute)

                if statements_list:
                    for query_to_execute in statements_list:
                        log.info(f"Executing sql statement:\n{query_to_execute}")
                        cur.execute(query_to_execute)

            conn.close()
        except Exception as err:
            if not conn.is_closed():
                conn.close()
            msg = traceback_fmt(err)
            log.error(f"Error occured while trying to connect to database:\n {msg}")
            raise

    def execute_statement(self, query_to_execute: str, query_tag: str = None):
        """
        This method will call _execute private method and passes sql query to execute
        """
        self._execute(query_to_execute=query_to_execute, query_tag=query_tag)

    def execute_multi_statements(self, statements_list: list, query_tag: str = None):
        """
        This method will call _execute private method and passses list of sql statements to execute
        """
        self._execute(statements_list=statements_list, query_tag=query_tag)

    def unload_data_into_s3(
            self, stage_name: str, s3_prefix: str, data_query: str, copy_options: str = None
    ) -> None:
        """Unloads the data from Snowflake table to AWS S3 files after deleting any old files
            that are present on the same location

        Args:
            stage_name: str => name of the external stage created for AWS S3,
            s3_prefix: str => path inside the AWS S3 bucket,
            data_query: str => database select query to target table data which is to be unloaded,
            copy_options: str => copy options for dumping file in specific format

        Returns:
            None
        """
        query_list = []

        query = f"""rm @{stage_name}/{s3_prefix}/"""
        query_list.append(query)

        query = f"""copy into @{stage_name}/{s3_prefix}/
                    from ({data_query})
                    {copy_options}"""
        query_list.append(query)

        self.execute_multi_statements(query_list)

    def load_data_from_s3(self, stage_name: str, s3_prefix: str, qualified_table_name: str,
                          unique_keys_ls: list = None, copy_options: str = None, column_names: list = []) -> None:
        """
        Loads the data from AWS S3 files into Snowflake table using either Merge or Insert
        command and then removes the AWS S3 files.
        If argument "unique_keys_ls" is None then insert data into table
        If argument "unique_keys_ls" is not None then merge data into table

        :param stage_name: name of the external stage created for AWS S3,
        :param s3_prefix: path inside the AWS S3 bucket where data files resides,
        :param qualified_table_name: full qualified name of the target snowflake table (db.schema.table) to write data to
        :param unique_keys_ls: list of the unique column names which defines the primary key for target table
        :param copy_options: copy options for loading file from specific format
        :param column_names: Source table column names
        :returns: None
        """
        query = f"LIST @{stage_name}/{s3_prefix}/"
        _, row_count = self.get_data(query)

        if not row_count:
            log.info("No Data Available to Load into Snowflake")
        else:
            query_list = []

            database_name, schema_name, table_name = qualified_table_name.lower().split('.')
            temp_table_name = f"{database_name}_{schema_name}_{table_name}_replication_temp"
            qualified_temp_table_name = f'{database_name}.{schema_name}.{temp_table_name}'

            if not column_names:
                query = f"""
                    SELECT column_name
                    FROM {database_name}.information_schema.columns
                    WHERE UPPER(table_schema) = UPPER('{schema_name}')
                    AND UPPER(table_name) = UPPER('{table_name}')
                    ORDER BY ordinal_position"""
                column_names, _ = self.get_data(query)
                column_names = [col[0] for col in column_names]
            column_names_str = ','.join(column_names)

            query = f"USE DATABASE {database_name}"
            query_list.append(query)

            # In case the unload table has a schema with shifted columns
            # we can create the temporary table to match that schema and load the files correctly
            query = f"""
                CREATE TEMPORARY TABLE {qualified_temp_table_name} AS 
                    SELECT {column_names_str} 
                    FROM {qualified_table_name}
                    WHERE 1 = 0"""
            query_list.append(query)

            query = f"""
                COPY INTO {qualified_temp_table_name}
                FROM @{stage_name}/{s3_prefix}/
                {copy_options}"""
            query_list.append(query)

            if unique_keys_ls is not None:
                query = f"""
                    MERGE INTO {qualified_table_name} AS tgt
                    USING {qualified_temp_table_name} AS src ON (
                        {' AND '.join([f'tgt.{col} = src.{col}' for col in unique_keys_ls])}
                    )
                    WHEN MATCHED THEN UPDATE
                        SET {', '.join([f'tgt.{col} = src.{col}' for col in column_names])}
                    WHEN NOT MATCHED THEN 
                        INSERT ({column_names_str})
                        VALUES (
                            {', '.join([f'src.{col}' for col in column_names])}
                        )"""
                query_list.append(query)

            else:
                query_list.append('BEGIN TRANSACTION;')

                query = f"""DELETE FROM {qualified_table_name}"""
                query_list.append(query)

                query = f"""
                    INSERT INTO {qualified_table_name} ({column_names_str}) 
                        SELECT {column_names_str}
                        FROM {qualified_temp_table_name}"""
                query_list.append(query)

                query_list.append('COMMIT;')

            query = f"""DROP TABLE IF EXISTS {qualified_temp_table_name}"""
            query_list.append(query)

            query = f"""RM @{stage_name}/{s3_prefix}/"""
            query_list.append(query)

            self.execute_multi_statements(query_list)
            log.info("Processed files are deleted")

    def get_table_schema(self, qualified_table_name: str) -> list:
        """
        Get the schema, data type etc for the specified table
        :param qualified_table_name: Fully qualified table name (db.schema.table)
        :return column_names: list of column names
        """
        database_name, schema_name, table_name = qualified_table_name.upper().split('.')
        query = f"""
            SELECT column_name
            FROM {database_name}.information_schema.columns
            WHERE UPPER(table_schema) = UPPER('{schema_name}')
              AND UPPER(table_name) = UPPER('{table_name}')
            ORDER BY ordinal_position"""
        data, _ = self.get_data(query, as_dict=True)
        column_names = [record['COLUMN_NAME'] for record in data]
        return column_names

    def write_data_frame(self, dataframe, database_name: str, schema_name: str, table_name: str, query_tag: str, auto_create_table: bool = False,
                         overwrite: bool = False):
        """
        Write a pandas dataframe to Snowflake table
        :param dataframe: pandas dataframe
        :param table_name: snowflake table name
        :return: None
        """
        try:
            conn = self._get_connection()
            with conn.cursor() as cur:

                if query_tag:
                    sql_query_tag = generate_query_tag_sql(query_tag)
                    cur.execute(sql_query_tag)

                if dataframe is not None and not dataframe.empty:
                    log.info(f"Writing data to table {table_name}")
                    success, nchunks, nrows, _ = write_pandas(conn=conn, df=dataframe, database=database_name, schema=schema_name, table_name=table_name,
                                                              auto_create_table=auto_create_table, overwrite=overwrite)
                    log.info(f"Dataframe write operation successful: {success}")

            conn.close()
        except Exception as err:
            if not conn.is_closed():
                conn.close()
            msg = traceback_fmt(err)
            log.error(f"Error occured while trying to connect to database:\n {msg}")
            raise

    def get_data_frame(self, sql_statement: str, query_tag: str = None, as_dict: bool = False):
        try:
            conn = self._get_connection()
            cur = conn.cursor(DictCursor) if as_dict else conn.cursor()

            if query_tag:
                sql_query_tag = generate_query_tag_sql(query_tag)
                cur.execute(sql_query_tag)

            log.info(f"Executing sql statement:\n{sql_statement}")
            cur.execute(sql_statement)
            res = cur.fetch_pandas_all()
            row_count = cur.rowcount
            cur.close()
            conn.close()
            return res, row_count
        except Exception as err:
            if not cur.is_closed():
                cur.close()
            if not conn.is_closed():
                conn.close()
            msg = traceback_fmt(err)
            log.error(f"Error occurred while trying to connect to database:\n{msg}")
            raise

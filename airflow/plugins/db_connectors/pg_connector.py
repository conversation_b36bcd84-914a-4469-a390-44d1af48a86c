import logging
from airflow.providers.postgres.hooks.postgres import PostgresHook

from helpers.helper import *
from boto3 import client
import tasklib.s3 as tls3

log = logging.getLogger(__name__)

"""
This module is specific to airflow and uses PostgresHook to get the connection details and corresponding values rather
than using .connect method of the API. Standard methods are implemented that will get the results/ set the data
"""


class Postgres(object):
    def __init__(self, conn_id=None):

        self.conn_id = conn_id if conn_id else 'postgres_default'

    def _get_connection(self):
        """
        private method that will return the connection object based on the connection parameters passed as input. For
        airflow, connection properties are set in the Connections and have an id associated. For postgres its named as
        postgres_conn_id
        :return:
        """
        return PostgresHook(postgres_conn_id=self.conn_id).get_conn()

    def get_data(self, sql_statement: str, as_dict: bool = False):
        """
        Method will execute query and returns result. An optional parameter as_dict can be passed to get the resultset
        as list of dictionaries instead of list of tuples for further processing or referring columns. Method returns
        resultset and record count.
        Usage: res, _=get_data('select * from table')
                res, _=get_data('select * from table', as_dict=True)
          to discard row_count just use _ or some garbage variable that can be discarded. If row_count is required for
          further processing, assign it to a named variable

        :param sql_statement: valid SQL statement that can be executed by most of the db engines
        :param as_dict:
        :return: resultset and row count (resultset can be either a list of tuples or list of dictionaries)
        """
        try:
            conn = self._get_connection()
            cur = conn.cursor()
            log.info(f"Executing sql statement:\n{sql_statement}")
            cur.execute(sql_statement)
            row_count = cur.rowcount
            res = cur.fetchall()
            if as_dict:
                column_names = [i[0] for i in cur.description]
                cur.close()
                conn.close()
                return [dict(zip(column_names, row)) for row in res], row_count
            cur.close()
            conn.close()
            return res, row_count
        except Exception as err:
            if not cur.closed:
                cur.close()
            if not conn.closed:
                conn.close()
            msg = traceback_fmt(err)
            log.error(f"Error occured while trying to connect to database:\n{msg}")
            raise

    def _execute(self, query_to_execute: str = None, statements_list: list = None):
        """
        This is a private method that will be called by execute_statement and execute_multi_statement methods
        depending on whether a single statement is executed or list of statements are executed, this method will loop
        through all the different statements and executes them.

        :param: query_to_execute: valid sql query to execute
        :param: statements_list: list of valid sql statements to execute

        #TO-DO: Implementation of multistatement execution in a transaction
        """
        try:
            conn = self._get_connection()
            with conn.cursor() as cur:
                if query_to_execute:
                    log.info(f"Executing sql statement:\n{query_to_execute}")
                    cur.execute(query_to_execute)

                if statements_list:
                    for query_to_execute in statements_list:
                        log.info(f"Executing sql statement:\n{query_to_execute}")
                        cur.execute(query_to_execute)

                conn.commit()
                cur.close()
            conn.close()

        except Exception as err:
            if not conn.closed:
                conn.close()
            msg = traceback_fmt(err)
            log.error(f"Error occured while trying to connect to database:\n {msg}")
            raise


    def _execute_ignore_errors(self, query_to_execute: str = None, statements_list: list = None):
        """
        This is a private method that will be called by execute_statement and execute_multi_statement methods
        depending on whether a single statement is executed or list of statements are executed, this method will loop
        through all the different statements and executes them.
        :param: query_to_execute: valid sql query to execute
        :param: statements_list: list of valid sql statements to execute
        #TO-DO: Implementation of multistatement execution in a transaction
        """
        try:
            conn = self._get_connection()
            with conn.cursor() as cur:
                if query_to_execute:
                    log.info(f"Executing sql statement:\n{query_to_execute}")
                    cur.execute(query_to_execute)
                    conn.commit()
                    cur.close()

                if statements_list:
                    # Starting transaction
                    # cur.execute('begin;')
                    log.info(f"Total statements to execute: {len(statements_list)}")
                    for query in statements_list:
                        try:
                            cur.execute(query)
                            conn.commit()
                        except Exception as e:
                            log.error(e)
                            log.error(f"Error running the query: \n {query}")
                    cur.close()
            conn.close()
        except Exception as err:
            if not conn.closed:
                conn.close()
            msg = traceback_fmt(err)
            log.error(f"Error occured while trying to connect to database:\n {msg}")
            raise


    def execute_statement(self, query_to_execute: str):
        """
        This method will call _execute private method and passes sql query to execute
        """
        self._execute(query_to_execute=query_to_execute)

    def execute_multi_statements(self, statements_list: list):
        """
        This method will call _execute private method and passses list of sql statements to execute
        """
        self._execute(statements_list=statements_list)

    def unload_data_into_s3(
            self, s3_bucket: str, s3_prefix: str, data_query: str, copy_options: str = None
    ) -> None:
        """This function will only work with active aws_s3 extension with below grants:
            CREATE EXTENSION IF NOT EXISTS aws_s3 CASCADE;
            GRANT USAGE ON SCHEMA aws_s3 TO <role>;
            GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA aws_s3 TO <role>;

        It unloads the data from Postgre table to AWS S3 files after deleting any old files
            that are present on the same location

        Args:
            s3_bucket: str => name of the AWS S3 bucket,
            s3_prefix: str => path inside the AWS S3 bucket,
            data_query: str => database select query to target table data which is to be unloaded
            copy_options: str => copy options for dumping file in specific format

        Returns:
            None
        """
        s3_client = client("s3")
        s3_prefix = s3_prefix.strip('/')

        """Block Summary: Removing any old pending files from S3"""
        obj_dict = s3_client.list_objects(Bucket=s3_bucket, Prefix=s3_prefix)
        if 'Contents' in obj_dict:
            for key in obj_dict['Contents']:
                file_prefix = key['Key']
                s3_client.delete_object(Bucket=s3_bucket, Key=file_prefix)

        start_index = copy_options.upper().index('FORMAT')
        end_index = copy_options.index(',', start_index)
        file_format = copy_options[start_index:end_index].split(' ')[1]
        s3_prefix = s3_prefix + f"/datafile.{file_format}"

        data_query = data_query.replace("'", "''")
        query = f"""SELECT *
                FROM aws_s3.query_export_to_s3
                ('{data_query}',
                aws_commons.create_s3_uri('{s3_bucket}','{s3_prefix}','us-east-2'),
                {copy_options}"""
        self.execute_statement(query)

    def load_data_from_s3(self, s3_bucket: str, s3_prefix: str, qualified_table_name: str,
        unique_keys_ls: list = None, copy_options: str = None, column_names: list = []) -> None:
        """This function will only work with active aws_s3 extension with below grants:
            CREATE EXTENSION IF NOT EXISTS aws_s3 CASCADE;
            GRANT USAGE ON SCHEMA aws_s3 TO <role>;
            GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA aws_s3 TO <role>;
            GRANT TEMPORARY ON DATABASE <database> TO <role>

        It loads the data from AWS S3 files into postgre table using either Merge or Insert
        command and then removes the AWS S3 files.
            If argument "unique_keys_ls" is None then insert data into table
            If argument "unique_keys_ls" is not None then merge data into table

        :param s3_bucket: name of the AWS S3 bucket
        :param s3_prefix: path inside the AWS S3 bucket where data files resides,
        :param qualified_table_name: full qualified name of the target postgres table (db.schema.table) to write data to
        :param unique_keys_ls: list of the unique column names which defines the primary key for target table
        :param copy_options: copy options for loading file from specific format
        :param column_names: Source table column names
        :returns: None
        """
        s3_client = client("s3")

        obj_dict = s3_client.list_objects(Bucket=s3_bucket, Prefix=s3_prefix)
        if 'Contents' not in obj_dict:
            log.info("No Data Available to Load into Postgre")
        else:
            query_list = []

            file_names = [record['Key'] for record in obj_dict['Contents']]
            database_name, schema_name, table_name = qualified_table_name.split('.')
            temp_table_name = f"{database_name}_{schema_name}_{table_name}_replication_temp"

            if not column_names:
                query = f"""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE UPPER(table_catalog) = UPPER('{database_name}')
                    AND UPPER(table_schema) = UPPER('{schema_name}')
                    AND UPPER(table_name) = UPPER('{table_name}')
                    ORDER BY ordinal_position"""
                column_names, _ = self.get_data(query)
                column_names = [col[0] for col in column_names]
            column_names_str = ','.join(column_names)

            # In case the unload table has a schema with shifted columns
            # we can create the temporary table to match that schema and load the files correctly
            query = f"""
                CREATE TEMPORARY TABLE {temp_table_name} AS
                    SELECT {column_names_str}
                    FROM {qualified_table_name}
                    WHERE 1 = 0"""
            query_list.append(query)

            for file_prefix in file_names:
                query = f"""
                    SELECT aws_s3.table_import_from_s3(
                        '{temp_table_name}'
                      , ''
                      , '{copy_options}'
                      , aws_commons.create_s3_uri('{s3_bucket}','{file_prefix}','us-east-2'))"""
                query_list.append(query)

            if unique_keys_ls is not None:
                query = f"""
                    WITH UPSERT AS (
                        UPDATE {qualified_table_name} AS tgt
                        SET {', '.join([f'{col} = src.{col}' for col in column_names])}
                        FROM {temp_table_name} AS src
                        WHERE {' AND '.join([f'tgt.{col} = src.{col}' for col in unique_keys_ls])}
                        RETURNING tgt.*
                    )
                    INSERT INTO {qualified_table_name}
                        SELECT {','.join([f't.{col}' for col in column_names])}
                        FROM {temp_table_name} AS t
                        WHERE ({','.join([f't.{col}' for col in unique_keys_ls])}) NOT IN
                            (SELECT {','.join([f's.{col}' for col in unique_keys_ls])} FROM UPSERT AS s)"""
                query_list.append(query)

            else:
                query_list.append('BEGIN TRANSACTION;')

                query = f"""DELETE FROM {qualified_table_name}"""
                query_list.append(query)

                query = f"""
                    INSERT INTO {qualified_table_name} ({column_names_str})
                        SELECT {column_names_str}
                        FROM {temp_table_name}"""
                query_list.append(query)

                query_list.append('COMMIT;')

            query = f"""DROP TABLE IF EXISTS {temp_table_name}"""
            query_list.append(query)

            self.execute_multi_statements(query_list)

            for file_prefix in file_names:
                s3_client.delete_object(Bucket=s3_bucket, Key=file_prefix)
            log.info("Processed files are deleted")

    def get_table_schema(self, qualified_table_name: str) -> list:
        """
        Get the schema, data type etc for the specified table
        :param qualified_table_name: Fully qualified table name (db.schema.table)
        :return column_names: list of column names
        """
        database_name, schema_name, table_name = qualified_table_name.upper().split('.')
        query = f"""
            SELECT column_name
            FROM information_schema.columns
            WHERE UPPER(table_catalog) = UPPER('{database_name}')
              AND UPPER(table_schema) = UPPER('{schema_name}')
              AND UPPER(table_name) = UPPER('{table_name}')
            ORDER BY ordinal_position"""
        data, _ = self.get_data(query, as_dict=True)
        column_names = [record['column_name'] for record in data]
        return column_names


    def load_data_from_s3_in_batches(
            self, s3_bucket: str, s3_prefix: str,
            table_name: str, unique_keys_ls: list = None,
            copy_options: str = None, batch_size=160
    ) -> None:
        s3_client = client("s3")
        query_list = []
        s3_prefix = s3_prefix.strip('/')

        temp_table_name = f"{table_name.replace('.', '_')}_replication_temp"
        database_name, schema_name, table_name = table_name.split('.')

        file_names = tls3.get_all_s3_files(s3_bucket, s3_prefix)

        if len(file_names) == 0:
            log.info("No Data Available to Load into Postgres")
            return

        query = f"""SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
                                                WHERE UPPER(TABLE_CATALOG) = UPPER('{database_name}')
                                                AND UPPER(TABLE_SCHEMA) = UPPER('{schema_name}')
                                                AND UPPER(TABLE_NAME) = UPPER('{table_name}')
                                                ORDER BY ORDINAL_POSITION"""
        column_names_ls, _ = self.get_data(query)
        column_names_ls = [col[0] for col in column_names_ls]

        batch = max(int(batch_size / 16), 1)
        total_file_size = len(file_names)
        log.info(f"loading in the batches of {batch}")
        log.info(f"total files to load are {total_file_size}")

        loopsize = int(total_file_size / batch) + min(1, total_file_size % batch)

        for i in range(loopsize):
            log.info(f"Running the batch : {i}")
            start = i * batch
            end = min((i + 1) * batch, total_file_size)
            query_list.clear()
            query = f"""CREATE TEMPORARY TABLE {temp_table_name}
                                                    AS
                                                    TABLE {database_name}.{schema_name}.{table_name} WITH NO DATA"""
            query_list.append(query)
            for x in range(start, end):
                query = f"""SELECT aws_s3.table_import_from_s3('{temp_table_name}', '', '{copy_options}',
                                                        aws_commons.create_s3_uri('{s3_bucket}',
                                                        '{file_names[x]}','us-east-2'))"""
                query_list.append(query)

            if unique_keys_ls is not None:
                query = f"""WITH UPSERT AS
                        (
                          UPDATE {database_name}.{schema_name}.{table_name} AS TARGET
                             SET {', '.join([a + ' = Source.' + a for a in column_names_ls])}
                          FROM {temp_table_name} AS SOURCE
                          WHERE {' AND '.join(['Target.' + a + ' = Source.' + a for a in unique_keys_ls])}
                          RETURNING TARGET.*
                        )
                        INSERT INTO {database_name}.{schema_name}.{table_name}
                          SELECT {','.join(['T.' + a for a in column_names_ls])}
                          FROM {temp_table_name} AS T
                          WHERE ({','.join(['T.' + a for a in unique_keys_ls])}) NOT IN
                            (SELECT {','.join(['S.' + a for a in unique_keys_ls])} FROM UPSERT AS S)
                        """
            else:
                query = f"""INSERT INTO {database_name}.{schema_name}.{table_name}
                            (
                              {','.join(column_names_ls)}
                            )
                            SELECT {','.join(column_names_ls)}
                            FROM {temp_table_name}"""
            query_list.append(query)
            query = f"""DROP TABLE {temp_table_name}"""
            query_list.append(query)
            self.execute_multi_statements(query_list)

        for file_prefix in file_names:
            s3_client.delete_object(Bucket=s3_bucket, Key=file_prefix)
        log.info("Processed files are deleted")


    def full_load_with_batches(
            self, s3_bucket: str, s3_prefix: str,
            table_name: str, unique_keys_ls: list = None,
            copy_options: str = None, batch_size=160
    ) -> None:
        s3_client = client("s3")
        query_list = []
        s3_prefix = s3_prefix.strip('/')
        database_name, schema_name, tbl_name = table_name.split('.')
        file_names = tls3.get_all_s3_files(s3_bucket, s3_prefix)

        if len(file_names) == 0:
            log.info("No Data Available to Load into Postgres")
            return

        batch = max(int(batch_size / 16), 1)
        total_file_size = len(file_names)
        log.info(f"loading in the batches of {batch}")
        log.info(f"total files to load are {total_file_size}")

        loopsize = int(total_file_size / batch) + min(1, total_file_size % batch)
        log.info(f"Loading data into {database_name}.{schema_name}.{tbl_name}")
        for i in range(loopsize):
            log.info(f"Running the batch : {i}")
            start = i * batch
            end = min((i + 1) * batch, total_file_size)
            query_list.clear()
            for x in range(start, end):
                query = f"""SELECT aws_s3.table_import_from_s3('{database_name}.{schema_name}.{tbl_name}',
                 '', '{copy_options}',aws_commons.create_s3_uri('{s3_bucket}',
                 '{file_names[x]}','us-east-2'))"""
                query_list.append(query)
            self._execute_ignore_errors(query_list)
        for file_prefix in file_names:
            s3_client.delete_object(Bucket=s3_bucket, Key=file_prefix)
        log.info("Processed files are deleted")

import ast
import json
import logging
import pkgutil
import pytz
import traceback
import uuid
from collections import OrderedDict
from datetime import datetime, timedelta
from urllib.parse import urlparse

#import Jinja2 for template rendering
from jinja2 import Template

from tasklib.config import get_dags_folder, get_db_config, get_env


log = logging.getLogger(__name__)

def render_jinja_template(str_: str, dictionary_map: dict) -> str:
    """
    :param str_: Pass a Jinja template
    :param dictionary_map: dictionary that will have placeholders and the values to replaces
    :return: string with replaced placeholders
    """
    #log.info('Applying parameters to placeholders for the given string: \n{}'.format(str_))
    return Template(str_).render(**dictionary_map)

def multi_replace_string(str_: str, dictionary_map: dict) -> str:
    """
    :param str_: Pass a string with place holders
    :param dictionary_map: dictionary that will have placeholders and the values to replaces
    :return: string with replaced placeholders
    """
    from string import Template
    #log.info('Applying parameters to placeholders for the given string: \n{}'.format(str_))
    return Template(str_).safe_substitute(**dictionary_map)


def read_contents_from_file(package_path: str, file_name: str):
    """
    This method will take package_path and file_name and returns content of file as string
    :param package_path: absolute package path where the file is
    :param file_name: name of the file
    :return: string
    """
    return pkgutil.get_data(package_path, file_name).decode('utf-8')


def multi_replace(package_path: str, file_name: str, dictionary_map: dict) -> str:
    """
    This method will take package_path and file_name and mapping for placeholders and returns replaced string
    :param package_path: absolute package path where the file is
    :param file_name: name of the file
    :param dictionary_map: placeholders that need to be replaced
    :return: formatted string with replaced placeholders with actual values
    """
    from string import Template
    return Template(pkgutil.get_data(package_path, file_name).decode('utf-8')).safe_substitute(**dictionary_map)


def extract_from_configuration_file(package_path: str, file_name: str, key: str=None) -> dict:
    """
    Given a package path and file name and key to extract, this function will parse the file load contents as json and
    returns the value associated with the key
    :param package_path:
    :param file_name:
    :param key:
    :return:
    """
    configurations = eval(pkgutil.get_data(package_path, file_name).decode('utf-8'))
    if key:
        return configurations[key]
    else:
        return configurations


def generate_etl_job_run_id() -> str:
    """
    This function generates and returns new uuid. It does not take any input parameters and uses uuid module
    to generate and return string version of uuid4
    :return:stringified value for uuid4()
    """
    logging.info("Generating new etl_job_run_id")
    return str(uuid.uuid4())


def generate_etl_batch_run_time() -> str:
    """
    Function generates and returns string version of utc date time with hour value only
    :return:
    """
    logging.info("Generating new etl_batch_run_time")
    # Get current utc time
    utc_datetime = datetime.utcnow()
    # Format datetime object to get only the hour value and discard minute, second and microsecond precision
    utc_datetime_hour = utc_datetime.replace(minute=0, second=0, microsecond=0)
    return utc_datetime_hour.strftime('%Y-%m-%d %H:%M:%S')


def traceback_fmt(err):
    """
    takes in the stack trace error and formats the exception that will look like regular python traceback
    :param err: stack trace
    :return: formatted string version of stack trace
    """
    return "".join(traceback.format_exception(type(err), err, err.__traceback__))


def get_absolute_sql_file_path(sql_file: str) -> str:
    """
    Takes the relative path to the sql file and creates the absolute path
    """
    dags_folder = get_dags_folder()

    sql_file_abs_path = dags_folder + "/data_transformations/" + sql_file
    log.info(f'sql filepath (absolute) is {sql_file_abs_path}')
    return sql_file_abs_path


def read_and_parse_sql_file(sql_file:str, params:str) -> str:
    """
    Reads the queries from specified sql file (absolute path); replaces the parameters in the 
    queries based on the specified params, and returns the query string.
    """

    #read schema, database names from application configuration for variables used in the sql
    sql_vars_dict = load_db_variables()

    #read query file contents
    sql_text = ''
    with open(sql_file, 'r') as f:
        sql_text = f.read()

    #remove leading and trailing spaces, tabs
    if sql_text:
        sql_text = sql_text.strip()

    #convert params dictionary string to dictionary
    params_dict = ast.literal_eval(params)

    sql_vars_dict['start_ts'] = params_dict.get('wf_start_ts_iso_utc')
    sql_vars_dict['end_ts'] = params_dict.get('wf_end_ts_iso_utc')

    #replace variables names with the appropriate values
    sql_text = multi_replace_string(sql_text, sql_vars_dict)
    sql_text = multi_replace_string(sql_text, params_dict)

    return sql_text


def load_db_variables() -> dict:
    db_config = get_db_config()
    log.info('Database variables found: %s', str(db_config))
    ctx_sql_vars = {}
    for key, rec in db_config.items():
        ctx_sql_vars[key] = '{0}.{1}'.format(rec['database'], rec['schema'])
    return ctx_sql_vars

def resolve_db_variables(text_str: str) -> str:
    """
    Resolve db references like $db_stage, $db_raw etc.
    returns: Clean SQL ready to execute.
    """
    db_vars = load_db_variables()
    return multi_replace_string(text_str, db_vars)

def set_query_tag(sql_file=None, dq_test=None, **context):
    dag_id = context['dag'].dag_id
    task_id = context.get('task_instance').task_id
    query_tag = OrderedDict([('DAG_ID', dag_id), ('TASK_ID', task_id), ('TEAM_NAME', 'ENGINEERING'), ('ENV', get_env())])
    if sql_file:
        query_tag['SQL_FILE'] = sql_file
    if dq_test:
        query_tag['DQ_TEST'] = dq_test
    return json.dumps(dict(query_tag))

def generate_query_tag_sql(query_tag):
    query_tag = query_tag.replace("'", "\'")
    return f"ALTER SESSION SET QUERY_TAG='{query_tag}'"

def get_s3_parts(s3_file):
    """
    Function to split a s3 path string into the bucket and prefix
    e.g. s3_file = 's3://bucket-name/path/to/file.txt'
        returns bucket='bucket-name', prefix='path/to/file.txt'
    :param s3_file: str, s3 file path
    :return bucket: str, s3 bucket name
    :return prefix: str, s3 prefix
    """
    parts = urlparse(s3_file)
    bucket = parts.netloc.replace('s3://', '')
    prefix = parts.path.lstrip("/")
    return bucket, prefix

def get_snapshot_date(delta_hours=18, date_format='%Y-%m-%d'):
    '''
    Gets the snapshot date to run. Uptil specified delta every day, return the previous date;
    After the specified delta, return the same date
    e.g. if delta is 18, will use previous date until 6pm PST, and then switches to current date
    '''
    utc_time = datetime.utcnow()
    local_time_adjusted = pytz.utc.localize(utc_time).astimezone(pytz.timezone('US/Pacific')) - timedelta(hours=delta_hours)
    return datetime.strftime(local_time_adjusted, date_format)

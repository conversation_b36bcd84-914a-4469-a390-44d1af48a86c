import json
import logging
import os
import subprocess
import time

import redis

from airflow.configuration import AIR<PERSON>OW_HOME
from airflow.hooks.base import <PERSON>Hook
from airflow.operators.bash import BashOperator

WAIT_TABLE_UNLOCK_TIMEOUT = 30 * 60  # 30m


def get_postgres_cli():
    from db_connectors.pg_connector import Postgres

    postgres = Postgres()
    return postgres


def lock_model(context, wait_time=WAIT_TABLE_UNLOCK_TIMEOUT):
    task_id = context["ti"].task_id
    postgres = get_postgres_cli()
    start = time.time()
    while start > (time.time() - wait_time):
        query = f"""
        SELECT
            *
        FROM
            workflow_configurations.dbt_model_runtime_flag
        WHERE
                model_name = '{task_id}'
                and is_locked != 0
        """
        data, row_count = postgres.get_data(query, as_dict=True)
        logging.info(data)
        logging.info(row_count)
        if row_count:
            time.sleep(10)
        else:
            query = f"""
            INSERT INTO workflow_configurations.dbt_model_runtime_flag (model_name, is_locked, updated_at)
            VALUES ('{task_id}', 1, current_timestamp)
            ON CONFLICT (model_name) DO UPDATE
            SET is_locked = 1, updated_at=current_timestamp;
            """
            postgres.execute_statement(query)
            return
    raise RuntimeError("Wait too long!")


def release_model(context):
    task_id = context["ti"].task_id
    postgres = get_postgres_cli()
    query = f"""
            INSERT INTO workflow_configurations.dbt_model_runtime_flag (model_name, is_locked, updated_at)
            VALUES ('{task_id}', 0, current_timestamp)
            ON CONFLICT (model_name) DO UPDATE
            SET is_locked = 0, updated_at=current_timestamp;
            """
    postgres.execute_statement(query)


class DbtOperator(BashOperator):
    verb = None

    def __init__(
        self,
        dbt_global_cli_flags: dict = None,
        dbt_target: dict = None,
        dbt_profiles_dir: dict = None,
        dbt_project_dir: dict = None,
        dbt_model_suffix=None,
        project=None,
        schema=None,
        infer_model=True,
        selector_flag="--models",
        additional_options=None,
        is_incremental=False,
        model_lock_wait_time=WAIT_TABLE_UNLOCK_TIMEOUT,
        *args,
        **kwargs,
    ):
        kwargs["bash_command"] = kwargs.get("bash_command") or ""
        super().__init__(*args, **kwargs)
        self.dbt_global_cli_flags = dbt_global_cli_flags
        self.dbt_target = dbt_target
        self.dbt_profiles_dir = dbt_profiles_dir
        self.dbt_project_dir = dbt_project_dir
        self.dbt_model_suffix = dbt_model_suffix
        self.selector_flag = selector_flag
        self.project = project
        self.schema = schema
        self.is_incremental = is_incremental
        if infer_model:
            self.model_name = self.task_id.split(".")[-1]
        else:
            self.model_name = " "
        self.bash_command = self.build_command(additional_options)
        self.model_lock_wait_time = model_lock_wait_time
        self.on_failure_callback = release_model
        self.on_success_callback = release_model
        self.on_retry_callback = release_model
        self.on_skipped_callback = release_model

    def build_command(self, additional_options=None):
        model_name = self.task_id.split(".")[-1]
        copy_project = f"""set -e; rm -rf /tmp/{model_name}; mkdir /tmp/{model_name}; cp -R /opt/airflow/dags/dbt/finance /tmp/{model_name};cd /tmp/{model_name}/finance"""
        command = [
            f"{copy_project};dbt --version;/home/<USER>/.local/bin/dbt deps;/home/<USER>/.local/bin/dbt --debug ",
            f"{self.dbt_global_cli_flags}",
            " ",
            f"{self.verb}",
            " ",
            "--models",
            " ",
            model_name,
            " ",
            # f"--target-path",
            # " ",
            # f"/tmp/{self.dag_id}",
            # " ",
            f"--profiles-dir",
            " ",
            ".",
            " ",
            "--project-dir",
            " ",
            ".",
            f";rm -rf /tmp/{model_name}",
        ]
        if additional_options:
            for k, v in additional_options.items():
                command.append(k)
                command.append(" ")
                command.append(v)
        return command

    def execute(self, context):
        from db_connectors.sf_connector import Snowflake

        snf = Snowflake(conn_id="snowflake_connection_dbt")
        conn = BaseHook.get_connection(snf.conn_id)
        extra = json.loads(conn.extra)
        profiles_vars = {
            "SNOWFLAKE_USER": conn.login,
            "SNOWFLAKE_PASSWORD": conn.password,
            "SNOWFLAKE_ACCOUNT": extra["extra__snowflake__account"],
            "SNOWFLAKE_ROLE": extra["extra__snowflake__role"],
            "SNOWFLAKE_DATABASE": extra["extra__snowflake__database"],
            "SNOWFLAKE_WAREHOUSE": extra["extra__snowflake__warehouse"],
        }
        if self.is_incremental:
            env_vars = (
                context["ti"].xcom_pull(
                    task_ids="get_workflow_params", key="return_value"
                )
                or {}
            )
            profiles_vars.update(env_vars)
        self.env = profiles_vars
        self.bash_command = "".join(self.bash_command)
        lock_model(context, self.model_lock_wait_time)
        super().execute(context)
        release_model(context)


class DbtRunOperator(DbtOperator):
    verb = "run"


class DbtTestOperator(DbtOperator):
    verb = "test"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.model_name = self.dag_id
        self.bash_command = self.build_command()


class DbtCleanOperator(DbtOperator):
    verb = "clean"

    def __init__(self, *args, **kwargs):
        kwargs["selector_flag"] = ""
        kwargs["model_name"] = ""
        super().__init__(*args, **kwargs)
        self.bash_command = self.build_command()


class DbtDocOperator(DbtOperator):
    verb = "docs generate"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.bash_command = self.build_command()

import boto3
import botocore.exceptions
import logging
from datetime import datetime
import pytz

log = logging.getLogger(__name__)

class S3(object):
    def __init__(self):
        self.client = boto3.client('s3')
        self.paginator = None 

    def list_objects(self, bucket: str = None, key: str = None, prefix: str = None, start_ts: datetime = None, 
                     end_ts: datetime = None, suffix: str = None, start_after=None, pagination_config:dict={}) -> list:
        is_truncated = True
        StartingToken = None
        files = []
        if self.paginator is None: 
            self.paginator=client.get_paginator('list_objects_v2')
        new_pagination_config={}
        if pagination_config:
           new_pagination_config=pagination_config
        new_pagination_config['StartingToken'] = StartingToken 
        
        while is_truncated:
            try:
                page_iterator = self.paginator.paginate(
                                         Bucket=bucket, 
                                         Prefix=prefix,
                                         PaginationConfig=new_pagination_config)
                for page in page_iterator:
                    new_pagination_config['StartingToken'] = page.get('NextContinuationToken')
                    is_truncated = page.get('IsTruncated')
                    LastModified = None
                    for content in page.get('Contents', []):
                        LastModified = content['LastModified']
                        if start_ts <= LastModified <= end_ts:
                            if suffix is None or content['Key'].endswith(suffix):
                                files.append('s3://' + bucket + '/' + content['Key'])
            except Exception as e:
                message="Error encountered"
                print(e)
                raise
        return files

    def write_text_file(self, file_name:str=None, content:str=None, encoding:str='utf-8'):
        name_parts=S3.parse_obj_name(file_name)
        bucket=name_parts[0]
        key=name_parts[1]
        try:
            self.client.put_object(Body=content.encode('utf-8'), Bucket=bucket, Key=key)
        except botocore.exceptions.ClientError as error:
            raise error
            return 1
        return 0

    def read_text_file(self, file_name:str=None, encoding:str='utf-8') -> str:
        name_parts=S3.parse_obj_name(file_name)
        bucket=name_parts[0]
        key=name_parts[1]
        content=""
        try:
            obj=self.client.get_object(Bucket=bucket, Key=key)
            content=obj['Body'].read().decode(encoding)
        except botocore.exceptions.ClientError as error:
            raise error

        return content
 

    @staticmethod
    def parse_obj_name(obj_name: str) -> list:
        result = []
        uri_id = obj_name[:5]
        if uri_id == 's3://' or uri_id == 'S3://':
            name_parts = obj_name[5:].split('/', 1)
            result = [name_parts[0], name_parts[1]]
        return result

    """
    list files under s3 folder
    filter by file last modified time and file name end with suffix
    """
    def list_s3_modified_files(self, folders:list=[], prefix:str=None, start_ts:datetime=None, end_ts:datetime=None, 
                               suffix:str=None, start_after:str=None, pagination_config:dict={})->list:
        files=[]
        self.paginator=self.client.get_paginator('list_objects_v2')
        for folder in folders:
            name_parts=S3.parse_obj_name(folder)
            bucket=name_parts[0]
            prefix=name_parts[1]
            result=self.list_objects(bucket = bucket, 
                                prefix = prefix,
                                start_ts = start_ts,
                                end_ts = end_ts, 
                                suffix = suffix)
            if result:
                files=files + result
        return files 

    """
    get size of s3 file
    """
    def get_s3_file_size(self, s3_uri:str)->int:
        name_parts=S3.parse_obj_name(s3_uri)
        bucket=name_parts[0]
        prefix=name_parts[1]
        file_size=-1
        obj={}
        try:
            obj=self.client.get_object(Bucket=bucket, Key=prefix)
        except Exception as e:
            log.error(f"Error occured when calling boto s3 get_object to fetch size of file {s3_uri}")
            raise
        else:
            if obj:
                log.info("s3 get object response: %s",str(obj))
                try:
                    file_size=obj['ContentLength']
                except Exception as e:
                    log.error("Error occured when attempting to read file size from s3 response.") 
                    raise 
            else:
                log.error("s3 response is not valid")
                raise
             
        return file_size

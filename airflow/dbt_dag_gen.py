"""
This file will do the following:
- Read the `manifest.json` file and only keep the fields which were defined by `-s` option in the command.
- Select the models filtered by the `tags`
- Generate the dag metadata file, only contain the filtered models
- Generate `.py` file to generate the DAGs.
"""
import getopt
import json
import logging
import os
import sys

import yaml


class ModelSelector:
    nodes = {}

    def __init__(self, dbt_target, select_fields=None):
        if not select_fields:
            select_fields = ["depends_on", "tags", "schema", "name", "package_name"]
        self.manifest_path = f"{dbt_target}/manifest.json"
        self.select_fields = select_fields

        self.load_nodes()

    def load_nodes(self):
        with open(self.manifest_path, "r") as f:
            content = f.read()
        content = json.loads(content)
        for model_name, node in content["nodes"].items():
            light_weight = {k: node[k] for k in self.select_fields}
            self.nodes[model_name] = light_weight

    def select_by_tags(self, tags: list, exclude=None):
        nodes = {}
        if not exclude:
            exclude = []
        for model_name, node in self.nodes.items():
            if set(tags).issubset(node["tags"]) and not len(
                set(exclude).intersection(set(node["tags"]))
            ):
                nodes[model_name] = node
        nodes = self.get_all_depend_nodes(self.nodes, nodes)
        return nodes

    def get_all_depend_nodes(self, all_nodes, select_nodes):
        status = True
        for node in list(select_nodes.values()):
            for model in node["depends_on"].get("nodes") or []:
                if model not in select_nodes and not model.startswith("source"):
                    select_nodes[model] = all_nodes[model]
                    status = False
        if status:
            return select_nodes
        else:
            return self.get_all_depend_nodes(all_nodes, select_nodes)

    def create_dag_metadata(
        self, tags: list, path: str, dag_id: str, exclude_tags=None
    ):
        if not os.path.exists(path):
            os.makedirs(path)

        nodes = self.select_by_tags(tags, exclude_tags)
        with open(f"{path}/{dag_id}", "w") as f:
            json.dump({"nodes": nodes}, f)


def get_arg():
    select_fields = []
    dbt_project_path = "dags/dbt/finance"
    dag_config_path = f"{dbt_project_path}/dag_config.yml"
    dag_metadata_path = f"dags/dbt_metadata"

    arguments, _ = getopt.getopt(sys.argv[1:], "m:r:s:D:C")
    logging.info(f"List arguments: {arguments}")

    for opt, v in arguments:
        if opt == "-s":
            select_fields = v.split(",")
        elif opt == "-d":
            dbt_project_path = v
        elif opt == "-C":
            dag_config_path = v
        else:
            raise ValueError(f"Not support: {opt}")

    selector_path = f"{dbt_project_path}"

    return (
        dbt_project_path,
        select_fields,
        selector_path,
        dag_config_path,
        dag_metadata_path,
    )


def get_dag_config(dag_config_path):
    with open(f"{dag_config_path}", "r") as f:
        config = yaml.safe_load(f)
    return config


def get_tag_method(items):
    tags = [item["tag"] for item in items if "tag" in item]
    exclude = []
    for item in items:
        if "exclude" not in item:
            continue
        exclude_tags = [item["tag"] for item in item["exclude"] if "tag" in item]
        exclude += exclude_tags
    return tags, exclude


def get_selectors(root_project_path):
    with open(f"{root_project_path}/selectors.yml", "r") as f:
        content = yaml.safe_load(f)
    selectors = {}
    for item in content["selectors"]:
        tags, exclude = get_tag_method(item["definition"]["union"])
        selectors[item["name"]] = {"tags": tags, "exclude": exclude}
    return selectors


def gen_py_file(dag_id):
    s = f"""from airflow.configuration import AIRFLOW_HOME
from codegen_dags.dbt_advanced_utility import create_dag


dags = create_dag(AIRFLOW_HOME + "/dags/dbt/finance/dag_config.yml")
for dag_id, dag in dags:
    globals()[dag_id] = dag
    """
    with open(f"dags/{dag_id}.py", "w") as f:
        f.write(s)


if __name__ == "__main__":
    dbt_path, fields, selectors_path, config_path, dag_metadata_path = get_arg()
    print(f"select_fields: {dbt_path}")
    print(f"select_fields: {fields}")
    print(f"dag_config_path: {config_path}")

    dag_config = get_dag_config(config_path)
    dag_selectors = get_selectors(selectors_path)
    selector = ModelSelector(f"{dbt_path}/target", fields)
    for dag in dag_config["selectors"]:
        dag_id = dag["name"]
        selector.create_dag_metadata(
            tags=dag_selectors[dag_id]["tags"],
            path=f"./dags/dbt_metadata/files",
            dag_id=dag_id,
            exclude_tags=dag_selectors[dag_id].get("exclude"),
        )
        logging.info("Gen py file...")
        gen_py_file(dag_id.replace("-", "_"))

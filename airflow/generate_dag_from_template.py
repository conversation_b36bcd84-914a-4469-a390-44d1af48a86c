import argparse
import fileinput
from commonlibs.db_connectors.postgres_pg8k import Postgres
from commonlibs.helpers.helpers import *

TEMPLATES = 'airflow.dag_templates'

"""
This file will be used to generate dag code from template. 

Change the following to your environment specifics

file_path variable: '/Users/<USER>/workspace to your current working directory.

DO NOT COMMIT THIS FILE. ONCE DAGS ARE CREATED REVERT THE FILE BACK TO ITS ORIGINAL STATE

Usage Example:
python3 generate_dag_from_template.py  -t 'INGESTION' -r 'FlatFileOrdersByLastUpdateReport' -a 'SP' -c 'Amazon' -s 'us-east-1'
python3 generate_dag_from_template.py -t 'TRANSFORMATION' -p 'fba_restock_inventory_transform' -s 'us-east-1'

This will generate an ingestion dag named amazon_orders_ingestion.
"""


def generate_ingestion_dag_from_template(report_name, channel, report_api, region_name):
    pgc = Postgres(secret_name='dataplatform/aurorapostgres/etl_user', region_name=region_name)

    data, _ = pgc.get_data(f"""SELECT * FROM workflow_configurations.udf_get_report_configurations('{report_name}', '{channel}','{report_api}')""", as_dict=True)

    params = data[0]
    dag_name = params.get('dag_name')
    params['dag_tags'] = params.get('dag_tags').split(',')
    params['region_name'] = region_name
    template_file = multi_replace(TEMPLATES, 'ingestion_template.template', params)

    file_path = f'/Users/<USER>/workspace/dataplatform-core-modules/airflow/dags/{dag_name}.py'

    with open(file_path, 'w') as f_:
        f_.write(template_file)


def generate_transformation_dag_from_template(transform_job_name, region_name):
    pgc = Postgres(secret_name='dataplatform/aurorapostgres/etl_user',region_name=region_name)
    data, _ = pgc.get_data(f"""SELECT * FROM workflow_configurations.udf_get_transform_configurations('{transform_job_name}')""", as_dict=True)

    params = data[0]
    params['dag_tags'] = params.get('dag_tags').split(',')
    params['dependency_dags'] = params.get('dependency_dags').split(',')
    params['region_name'] = region_name
    dag_name = params.get('dag_name')
    template_file = multi_replace(TEMPLATES, 'transformation_template.template', params)

    file_path = f'/Users/<USER>/workspace/dataplatform-core-modules/airflow/dags/{dag_name}.py'
    with open(file_path, 'w') as f_:
        f_.write(template_file)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='This script is used to generate dag code based on the templates that'
                                                 'are build by the Data Engineering team')
    parser.add_argument('-t', '--templateType', required=True,
                        help="What dag needs to be created based on the template. Currently supported templates"
                             "are INGESTION and TRANSFORMATION. The inputs are case sensitive")
    parser.add_argument('-r', '--reportName', required=False,
                        help="Name of the report if the templateType is INGESTION")
    parser.add_argument('-a', '--reportApi', required=False,
                        help="Name of the api if the templateType is INGESTION")
    parser.add_argument('-c', '--channel', required=False,
                        help="report channel if the templateType is INGESTION. Examples are SP, SHOPIFY, AMAZON_ADS")
    parser.add_argument('-p', '--transformName', required=False,
                        help="Name of the transformation for which dag needs to be generated. This is the name from"
                             "workflow_configurations.transform_job_info")
    parser.add_argument('-e', '--runEnvironment', required=False,
                        help="For which environment the template need to be generated. Dev and other environments will have"
                             "schema difference in database. This parameter will append appropriate environment variable")
    parser.add_argument('-d', '--dagFileName', required=False,
                        help="Name of dag file. Format is {chanel}_{report_name}_[Ingestion if its ingestion].py")
    parser.add_argument('-s', '--regionName', required=True,
                        help="aws region name where the secrets need to be pulled. It is us-east-1 for dev and us-east-2"
                             "for prod")

    job_args = parser.parse_args()
    dag_template = job_args.templateType
    report_name = job_args.reportName
    report_api = job_args.reportApi
    channel = job_args.channel
    dag_name = job_args.dagFileName
    transform_job_name = job_args.transformName
    region_name = job_args.regionName

    if dag_template == 'INGESTION':
        if not report_name:
            log.info('INGESTION template needs report_name')
            exit()
        elif not report_api:
            log.info('INGESTION template requires report_api')
            exit()
        elif not channel:
            log.info('INGESTION template requires channel')
            exit()
        generate_ingestion_dag_from_template(report_name, channel, report_api, region_name)

    if dag_template == 'TRANSFORMATION':
        generate_transformation_dag_from_template(transform_job_name, region_name)



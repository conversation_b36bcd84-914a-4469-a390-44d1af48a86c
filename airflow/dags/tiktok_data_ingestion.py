"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'tiktok_data_ingestion'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2024, 1, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'TIKTOK_DATA_INGESTION',
            'author': 'vikas'},
    tags=['vikas', 'Raptor','Goessor']
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
        
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load tiktok_campagin_report_daily ---
    
    with TaskGroup(group_id='load_tiktok_campagin_report_daily') as tg_tiktok_campagin_report_daily:
        list_s3_files_tiktok_campagin_report_daily_task = PythonOperator(
            task_id="list_s3_files_tiktok_campagin_report_daily",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_data_ingestion/list_s3_tiktok_campagin_report_daily.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            
        )

        list_s3_files_tiktok_campagin_report_daily_task_goessor = PythonOperator(
            task_id="list_s3_files_tiktok_campagin_report_daily_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_data_ingestion/list_s3_tiktok_campagin_report_daily_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_tiktok_campagin_report_daily_task = DummyOperator(task_id="begin_insert_tiktok_campagin_report_daily")
        skip_insert_tiktok_campagin_report_daily_task = DummyOperator(task_id="skip_insert_tiktok_campagin_report_daily")
        end_insert_tiktok_campagin_report_daily_task = DummyOperator(task_id="end_insert_tiktok_campagin_report_daily")
        end_tiktok_campagin_report_daily_task = DummyOperator(
            task_id="end_tiktok_campagin_report_daily",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_tiktok_campagin_report_daily_task = BranchPythonOperator(
            task_id='check_new_files_found_tiktok_campagin_report_daily',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "tiktok_data_ingestion/s3_to_snowflake_tiktok_campagin_report_daily.yaml",
                       "skip_task_id": "load_tiktok_campagin_report_daily.skip_insert_tiktok_campagin_report_daily",
                       "next_task_id": "load_tiktok_campagin_report_daily.begin_insert_tiktok_campagin_report_daily",
                       "args_file_second": "tiktok_data_ingestion/s3_to_snowflake_tiktok_campagin_report_daily_goessor.yaml"
            },
        )

        s3_to_snowflake_tiktok_campagin_report_daily_task = PythonOperator(
            task_id="s3_to_snowflake_tiktok_campagin_report_daily",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_data_ingestion/s3_to_sf_raw_tiktok_campagin_report_daily.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_tiktok_campagin_report_daily_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_tiktok_campagin_report_daily_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_data_ingestion/s3_to_sf_raw_tiktok_campagin_report_daily_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_tiktok_campagin_report_daily',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_tiktok_campagin_report_daily', 
                    field_list=['campaign_id'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        insert_log_tiktok_campagin_report_daily_task = PythonOperator(
            task_id="insert_log_tiktok_campagin_report_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_data_ingestion/insert_log_tiktok_campagin_report_daily.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_tiktok_campagin_report_daily_task = PythonOperator(
            task_id="dedupe_tiktok_campagin_report_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_data_ingestion/dedupe_tiktok_campagin_report_daily.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_campagin_report_daily',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_tiktok_campagin_report_daily', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_campagin_report_daily',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_tiktok_campagin_report_daily', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        merge_stage_tiktok_campagin_report_daily_task = PythonOperator(
            task_id="merge_stage_tiktok_campagin_report_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_data_ingestion/merge_stage_tiktok_campagin_report_daily.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_campagin_report_daily',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_tiktok_campagin_report_daily', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_campagin_report_daily',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_tiktok_campagin_report_daily', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        run_audit_tiktok_campagin_report_daily_task = PythonOperator(
            task_id="run_audit_tiktok_campagin_report_daily",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_tiktok_campagin_report_daily", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        (
            [list_s3_files_tiktok_campagin_report_daily_task,list_s3_files_tiktok_campagin_report_daily_task_goessor ] >>
            check_new_files_found_tiktok_campagin_report_daily_task >>
            [begin_insert_tiktok_campagin_report_daily_task, skip_insert_tiktok_campagin_report_daily_task]
        )

        (
            begin_insert_tiktok_campagin_report_daily_task >> 
            s3_to_snowflake_tiktok_campagin_report_daily_task >>
            s3_to_snowflake_tiktok_campagin_report_daily_task_goessor >>
            
         ( run_dq_is_null_raw_task) >>
    
            insert_log_tiktok_campagin_report_daily_task >>
            dedupe_tiktok_campagin_report_daily_task >>
            
         ( run_dq_is_unique_dedupe_pk_hard_task, 
    
         run_dq_is_null_dedupe_pk_hard_task) >>
    
            merge_stage_tiktok_campagin_report_daily_task >>
            
         ( run_dq_is_null_merge_pk_hard_task, 
    
         run_dq_is_unique_merge_pk_hard_task) >>
    
            run_audit_tiktok_campagin_report_daily_task >>
            end_insert_tiktok_campagin_report_daily_task >>
            end_tiktok_campagin_report_daily_task
        )

        skip_insert_tiktok_campagin_report_daily_task >> end_tiktok_campagin_report_daily_task

    #  --- Load tiktok_ads_report_daily ---
    
    with TaskGroup(group_id='load_tiktok_ads_report_daily') as tg_tiktok_ads_report_daily:
        list_s3_files_tiktok_ads_report_daily_task = PythonOperator(
            task_id="list_s3_files_tiktok_ads_report_daily",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_data_ingestion/list_s3_tiktok_ads_report_daily.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_tiktok_ads_report_daily_task = DummyOperator(task_id="begin_insert_tiktok_ads_report_daily")
        skip_insert_tiktok_ads_report_daily_task = DummyOperator(task_id="skip_insert_tiktok_ads_report_daily")
        end_insert_tiktok_ads_report_daily_task = DummyOperator(task_id="end_insert_tiktok_ads_report_daily")
        end_tiktok_ads_report_daily_task = DummyOperator(
            task_id="end_tiktok_ads_report_daily",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        list_s3_files_tiktok_ads_report_daily_task_goessor = PythonOperator(
            task_id="list_s3_files_tiktok_ads_report_daily_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_data_ingestion/list_s3_tiktok_ads_report_daily_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_tiktok_ads_report_daily_task = BranchPythonOperator(
            task_id='check_new_files_found_tiktok_ads_report_daily',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "tiktok_data_ingestion/s3_to_snowflake_tiktok_ads_report_daily.yaml",
                       "skip_task_id": "load_tiktok_ads_report_daily.skip_insert_tiktok_ads_report_daily",
                       "next_task_id": "load_tiktok_ads_report_daily.begin_insert_tiktok_ads_report_daily",
                       "args_file_second": "tiktok_data_ingestion/s3_to_snowflake_tiktok_ads_report_daily_goessor.yaml"
            },
        )

        s3_to_snowflake_tiktok_ads_report_daily_task = PythonOperator(
            task_id="s3_to_snowflake_tiktok_ads_report_daily",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_data_ingestion/s3_to_sf_raw_tiktok_ads_report_daily.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_tiktok_ads_report_daily_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_tiktok_ads_report_daily_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_data_ingestion/s3_to_sf_raw_tiktok_ads_report_daily_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_tiktok_ads_report_daily',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_tiktok_ads_report_daily', 
                    field_list=['ad_id'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        insert_log_tiktok_ads_report_daily_task = PythonOperator(
            task_id="insert_log_tiktok_ads_report_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_data_ingestion/insert_log_tiktok_ads_report_daily.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_tiktok_ads_report_daily_task = PythonOperator(
            task_id="dedupe_tiktok_ads_report_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_data_ingestion/dedupe_tiktok_ads_report_daily.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_ads_report_daily',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_tiktok_ads_report_daily', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_ads_report_daily',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_tiktok_ads_report_daily', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        merge_stage_tiktok_ads_report_daily_task = PythonOperator(
            task_id="merge_stage_tiktok_ads_report_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_data_ingestion/merge_stage_tiktok_ads_report_daily.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_ads_report_daily',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_tiktok_ads_report_daily', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_ads_report_daily',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_tiktok_ads_report_daily', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_fact_tiktok_ads_report_daily_task = PythonOperator(
            task_id="merge_fact_tiktok_ads_report_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "tiktok_data_ingestion/merge_fact_tiktok_ads_report_daily.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_tiktok_ads_report_daily_task = PythonOperator(
            task_id="run_audit_tiktok_ads_report_daily",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_tiktok_ads_report_daily", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        (
            [list_s3_files_tiktok_ads_report_daily_task,list_s3_files_tiktok_ads_report_daily_task_goessor ] >>
            check_new_files_found_tiktok_ads_report_daily_task >>
            [begin_insert_tiktok_ads_report_daily_task, skip_insert_tiktok_ads_report_daily_task]
        )

        (
            begin_insert_tiktok_ads_report_daily_task >> 
            s3_to_snowflake_tiktok_ads_report_daily_task >>
            s3_to_snowflake_tiktok_ads_report_daily_task_goessor >>
            
         ( run_dq_is_null_raw_task) >>
    
            insert_log_tiktok_ads_report_daily_task >>
            dedupe_tiktok_ads_report_daily_task >>
            
         ( run_dq_is_unique_dedupe_pk_hard_task, 
    
         run_dq_is_null_dedupe_pk_hard_task) >>
    
            merge_stage_tiktok_ads_report_daily_task >>
            
         ( run_dq_is_null_merge_pk_hard_task, 
    
         run_dq_is_unique_merge_pk_hard_task) >>
            merge_fact_tiktok_ads_report_daily_task >>
    
            run_audit_tiktok_ads_report_daily_task >>
            end_insert_tiktok_ads_report_daily_task >>
            end_tiktok_ads_report_daily_task
        )

        skip_insert_tiktok_ads_report_daily_task >> end_tiktok_ads_report_daily_task

    #  --- Load tiktok_adgroup_report_daily ---
    
    with TaskGroup(group_id='load_tiktok_adgroup_report_daily') as tg_tiktok_adgroup_report_daily:
        list_s3_files_tiktok_adgroup_report_daily_task = PythonOperator(
            task_id="list_s3_files_tiktok_adgroup_report_daily",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_data_ingestion/list_s3_tiktok_adgroup_report_daily.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_tiktok_adgroup_report_daily_task = DummyOperator(task_id="begin_insert_tiktok_adgroup_report_daily")
        skip_insert_tiktok_adgroup_report_daily_task = DummyOperator(task_id="skip_insert_tiktok_adgroup_report_daily")
        end_insert_tiktok_adgroup_report_daily_task = DummyOperator(task_id="end_insert_tiktok_adgroup_report_daily")
        end_tiktok_adgroup_report_daily_task = DummyOperator(
            task_id="end_tiktok_adgroup_report_daily",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_tiktok_adgroup_report_daily_task = BranchPythonOperator(
            task_id='check_new_files_found_tiktok_adgroup_report_daily',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "tiktok_data_ingestion/s3_to_snowflake_tiktok_adgroup_report_daily.yaml",
                       "skip_task_id": "load_tiktok_adgroup_report_daily.skip_insert_tiktok_adgroup_report_daily",
                       "next_task_id": "load_tiktok_adgroup_report_daily.begin_insert_tiktok_adgroup_report_daily",
                       "args_file_second": "tiktok_data_ingestion/s3_to_snowflake_tiktok_adgroup_report_daily_goessor.yaml",
            },
        )

        list_s3_files_tiktok_adgroup_report_daily_task_goessor = PythonOperator(
            task_id="list_s3_files_tiktok_adgroup_report_daily_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_data_ingestion/list_s3_tiktok_adgroup_report_daily_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        s3_to_snowflake_tiktok_adgroup_report_daily_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_tiktok_adgroup_report_daily_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_data_ingestion/s3_to_sf_raw_tiktok_adgroup_report_daily_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_tiktok_adgroup_report_daily_task = PythonOperator(
            task_id="s3_to_snowflake_tiktok_adgroup_report_daily",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_data_ingestion/s3_to_sf_raw_tiktok_adgroup_report_daily.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_tiktok_adgroup_report_daily',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_tiktok_adgroup_report_daily', 
                    field_list=['adgroup_id'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        insert_log_tiktok_adgroup_report_daily_task = PythonOperator(
            task_id="insert_log_tiktok_adgroup_report_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_data_ingestion/insert_log_tiktok_adgroup_report_daily.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_tiktok_adgroup_report_daily_task = PythonOperator(
            task_id="dedupe_tiktok_adgroup_report_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_data_ingestion/dedupe_tiktok_adgroup_report_daily.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_adgroup_report_daily',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_tiktok_adgroup_report_daily', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_adgroup_report_daily',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_tiktok_adgroup_report_daily', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        merge_stage_tiktok_adgroup_report_daily_task = PythonOperator(
            task_id="merge_stage_tiktok_adgroup_report_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_data_ingestion/merge_stage_tiktok_adgroup_report_daily.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_adgroup_report_daily',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_tiktok_adgroup_report_daily', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_adgroup_report_daily',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_tiktok_adgroup_report_daily', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        run_audit_tiktok_adgroup_report_daily_task = PythonOperator(
            task_id="run_audit_tiktok_adgroup_report_daily",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_tiktok_adgroup_report_daily", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        (
            [list_s3_files_tiktok_adgroup_report_daily_task, list_s3_files_tiktok_adgroup_report_daily_task_goessor] >>
            check_new_files_found_tiktok_adgroup_report_daily_task >>
            [begin_insert_tiktok_adgroup_report_daily_task, skip_insert_tiktok_adgroup_report_daily_task]
        )

        (
            begin_insert_tiktok_adgroup_report_daily_task >> 
            s3_to_snowflake_tiktok_adgroup_report_daily_task >>
            s3_to_snowflake_tiktok_adgroup_report_daily_task_goessor >>
            
         ( run_dq_is_null_raw_task) >>
    
            insert_log_tiktok_adgroup_report_daily_task >>
            dedupe_tiktok_adgroup_report_daily_task >>
            
         ( run_dq_is_unique_dedupe_pk_hard_task, 
    
         run_dq_is_null_dedupe_pk_hard_task) >>
    
            merge_stage_tiktok_adgroup_report_daily_task >>
            
         ( run_dq_is_null_merge_pk_hard_task, 
    
         run_dq_is_unique_merge_pk_hard_task) >>
    
            run_audit_tiktok_adgroup_report_daily_task >>
            end_insert_tiktok_adgroup_report_daily_task >>
            end_tiktok_adgroup_report_daily_task
        )

        skip_insert_tiktok_adgroup_report_daily_task >> end_tiktok_adgroup_report_daily_task

    #  --- Load tiktok_ad_report_daily_age_gender ---
    
    with TaskGroup(group_id='load_tiktok_ad_report_daily_age_gender') as tg_tiktok_ad_report_daily_age_gender:
        list_s3_files_tiktok_ad_report_daily_age_gender_task = PythonOperator(
            task_id="list_s3_files_tiktok_ad_report_daily_age_gender",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_data_ingestion/list_s3_tiktok_ad_report_daily_age_gender.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_tiktok_ad_report_daily_age_gender_task = DummyOperator(task_id="begin_insert_tiktok_ad_report_daily_age_gender")
        skip_insert_tiktok_ad_report_daily_age_gender_task = DummyOperator(task_id="skip_insert_tiktok_ad_report_daily_age_gender")
        end_insert_tiktok_ad_report_daily_age_gender_task = DummyOperator(task_id="end_insert_tiktok_ad_report_daily_age_gender")
        end_tiktok_ad_report_daily_age_gender_task = DummyOperator(
            task_id="end_tiktok_ad_report_daily_age_gender",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_tiktok_ad_report_daily_age_gender_task = BranchPythonOperator(
            task_id='check_new_files_found_tiktok_ad_report_daily_age_gender',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "tiktok_data_ingestion/s3_to_snowflake_tiktok_ad_report_daily_age_gender.yaml",
                       "skip_task_id": "load_tiktok_ad_report_daily_age_gender.skip_insert_tiktok_ad_report_daily_age_gender",
                       "next_task_id": "load_tiktok_ad_report_daily_age_gender.begin_insert_tiktok_ad_report_daily_age_gender",
                       "args_file_second": "tiktok_data_ingestion/s3_to_snowflake_tiktok_ad_report_daily_age_gender_goessor.yaml",
            },
        )

        list_s3_files_tiktok_ad_report_daily_age_gender_task_goessor = PythonOperator(
            task_id="list_s3_files_tiktok_ad_report_daily_age_gender_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_data_ingestion/list_s3_tiktok_ad_report_daily_age_gender_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        s3_to_snowflake_tiktok_ad_report_daily_age_gender_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_tiktok_ad_report_daily_age_gender_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_data_ingestion/s3_to_sf_raw_tiktok_ad_report_daily_age_gender_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_tiktok_ad_report_daily_age_gender_task = PythonOperator(
            task_id="s3_to_snowflake_tiktok_ad_report_daily_age_gender",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_data_ingestion/s3_to_sf_raw_tiktok_ad_report_daily_age_gender.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_tiktok_ad_report_daily_age_gender',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_tiktok_ad_report_daily_age_gender', 
                    field_list=['ad_id'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        insert_log_tiktok_ad_report_daily_age_gender_task = PythonOperator(
            task_id="insert_log_tiktok_ad_report_daily_age_gender",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_data_ingestion/insert_log_tiktok_ad_report_daily_age_gender.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_tiktok_ad_report_daily_age_gender_task = PythonOperator(
            task_id="dedupe_tiktok_ad_report_daily_age_gender",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_data_ingestion/dedupe_tiktok_ad_report_daily_age_gender.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_ad_report_daily_age_gender',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_tiktok_ad_report_daily_age_gender', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_ad_report_daily_age_gender',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_tiktok_ad_report_daily_age_gender', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        merge_stage_tiktok_ad_report_daily_age_gender_task = PythonOperator(
            task_id="merge_stage_tiktok_ad_report_daily_age_gender",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_data_ingestion/merge_stage_tiktok_ad_report_daily_age_gender.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_ad_report_daily_age_gender',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_tiktok_ad_report_daily_age_gender', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_ad_report_daily_age_gender',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_tiktok_ad_report_daily_age_gender', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        run_audit_tiktok_ad_report_daily_age_gender_task = PythonOperator(
            task_id="run_audit_tiktok_ad_report_daily_age_gender",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_tiktok_ad_report_daily_age_gender", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        (
            [list_s3_files_tiktok_ad_report_daily_age_gender_task, list_s3_files_tiktok_ad_report_daily_age_gender_task_goessor ] >>
            check_new_files_found_tiktok_ad_report_daily_age_gender_task >>
            [begin_insert_tiktok_ad_report_daily_age_gender_task, skip_insert_tiktok_ad_report_daily_age_gender_task]
        )

        (
            begin_insert_tiktok_ad_report_daily_age_gender_task >> 
            s3_to_snowflake_tiktok_ad_report_daily_age_gender_task >>
            s3_to_snowflake_tiktok_ad_report_daily_age_gender_task_goessor >>
            
         ( run_dq_is_null_raw_task) >>
    
            insert_log_tiktok_ad_report_daily_age_gender_task >>
            dedupe_tiktok_ad_report_daily_age_gender_task >>
            
         ( run_dq_is_unique_dedupe_pk_hard_task, 
    
         run_dq_is_null_dedupe_pk_hard_task) >>
    
            merge_stage_tiktok_ad_report_daily_age_gender_task >>
            
         ( run_dq_is_null_merge_pk_hard_task, 
    
         run_dq_is_unique_merge_pk_hard_task) >>
    
            run_audit_tiktok_ad_report_daily_age_gender_task >>
            end_insert_tiktok_ad_report_daily_age_gender_task >>
            end_tiktok_ad_report_daily_age_gender_task
        )

        skip_insert_tiktok_ad_report_daily_age_gender_task >> end_tiktok_ad_report_daily_age_gender_task

    # ---- Main branch ----
    chain(
       begin,
       get_workflow_parameters,
       [tg_tiktok_campagin_report_daily,
       tg_tiktok_ads_report_daily,
       tg_tiktok_adgroup_report_daily,
       tg_tiktok_ad_report_daily_age_gender],
       update_workflow_parameters,
       end
    )
    
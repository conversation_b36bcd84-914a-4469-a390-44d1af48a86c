from airflow import DAG
from datetime import datetime
from airflow.operators.python import Python<PERSON><PERSON>ator
from airflow.operators.dummy_operator import Dummy<PERSON>perator
from airflow.models import Variable
from airflow.models.baseoperator import chain
from airflow.providers.http.operators.http import SimpleHttpOperator
from airflow.providers.http.sensors.http import HttpSensor
from datetime import datetime
import logging
import uuid
import json

log = logging.getLogger(__name__)

API_ENDPOINT_CONN_ID = 'insights_engine_api_endpoint'
WF_PARAMS_EXPR = "{}"
DAG_ID ="amazon_promotions_planning"
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 */2 * * 2')


def get_snapshot_date():
    import pytz
    utc_time = datetime.utcnow()
    local_time_adjusted = pytz.utc.localize(utc_time).astimezone(pytz.timezone('US/Pacific'))
    return datetime.strftime(local_time_adjusted , '%Y-%m-%d')

def generate_payload():
    '''
    Generate the payload for the Insights API call. The snapshot_date is chosen to be one day prior, 
    based on UTC time.
    '''
    request_id = str(uuid.uuid4())
    snapshot_date = get_snapshot_date()
    payload = {
        "request_id": request_id,
        "snapshot_date": snapshot_date,
        "entity_type": "SCHEDULE_PROMOTION",
        "user_id": "hydy"
        }
    return json.dumps(payload)

def check_response(response):
    '''
    Monitor the Insights API job status.
    The function returns True if status="completed" else it returns False.
    Any error in reading status does not cause the job to fail, as we might
    see intermittent errors in the API call
    '''
    try:
        json_response = response.json()
        print(f'RESPONSE: {json_response}')
        status = json_response.get('status', '').lower()
        if status == 'completed':
            return True
    except Exception as e:
        print(f'API ERROR - {e}')
    return False

with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022, 12, 10),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={
        "workflow_name": "AMAZON_PROMOTIONS_PLANNING",
        "author": "ruchira"
    },
) as dag:
    import tasklib.workflow as tlw
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    import tasklib.replication as tlr
    from tasklib import alerts

    create_stg_amazon_asin_portfolio = PythonOperator(
        task_id="create_stg_amazon_asin_portfolio",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "promotions/amazon/planning/create_stg_amazon_asin_portfolio.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_amazon_promotion_seasonality = PythonOperator(
        task_id="create_stg_amazon_promotion_seasonality",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "promotions/amazon/planning/create_stg_amazon_promotion_seasonality.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_amazon_promotion_plan_metadata = PythonOperator(
        task_id="create_stg_amazon_promotion_plan_metadata",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "promotions/amazon/planning/create_stg_amazon_promotion_plan_metadata.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_uniqueness_check_stg_amazon_promotion_plan_metadata=PythonOperator(
        task_id="task_run_uniqueness_check_stg_amazon_promotion_plan_metadata",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.stg_amazon_promotion_plan_metadata",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_amazon_promotion_plan_metadata", 
                field_list=['WEEK_START_DATE','WEEK_NUMBER','COUNTRY_CODE','BRAND_CODE','ITEM_ID'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )
    
    #replication
    replicate_promotion_planning_sf_to_pg_task = PythonOperator(
        task_id="replicate_promotion_planning_sf_to_pg",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/amazon_promotion_plan_metadata.yaml"}
    )
    
    
    insights_api_call_task =  SimpleHttpOperator(
        task_id="insights_api_call",
        http_conn_id=API_ENDPOINT_CONN_ID,
        endpoint='insights/items/async',
        method='PUT',
        data=generate_payload(),
        headers={"Content-Type": "application/json", "Authorization": "Bearer dummy_jwt"},
        response_filter=lambda response: response.json(),
        log_response=True,
    )
    
    insights_api_status_monitor_task = HttpSensor(
        task_id='insights_api_status_monitor',
        http_conn_id=API_ENDPOINT_CONN_ID, 
        method="GET",
        headers={"Authorization": "Bearer dummy_jwt"},
        endpoint="requests/{{ti.xcom_pull(task_ids='insights_api_call')}}",
        response_check=lambda response: check_response(response), 
        poke_interval=300, #Check the job status every 5 minutes
        mode='reschedule', #Free up the worker slot for other jobs
        timeout=10800,     #Job timeout after 3 hours
        extra_options={'check_response': False},
    )

    replicate_promotion_planning_insights_pg_to_sf_task = PythonOperator(
        task_id="replicate_promotion_planning_insights_pg_to_sf",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/pg_to_sf/amazon_promotion_planning_insights.yaml"}
    )

    create_stg_fact_amazon_promotion_plan = PythonOperator(
        task_id="create_stg_amazon_promotion_plan",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "promotions/amazon/planning/create_stg_fact_amazon_promotion_plan.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_uniqueness_check_stg_fact_amazon_promotion_plan=PythonOperator(
        task_id="task_run_uniqueness_check_stg_fact_amazon_promotion_plan",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.stg_fact_amazon_promotion_plan",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_amazon_promotion_plan", 
                field_list=['SNAPSHOT_DATE','SELLER_ID','COUNTRY_CODE','BRAND_CODE','ASIN','SKU','START_DATE','END_DATE'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    merge_fact_amazon_promotion_plan = PythonOperator(
        task_id="merge_fact_amazon_promotion_plan",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "promotions/amazon/planning/merge_fact_amazon_promotion_plan.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_audit_promotion_plan = PythonOperator(
            task_id="run_audit_promotion_plan",
            python_callable=tla.run_audit,
            op_kwargs={"table_name": "$curated_db.fact_amazon_promotion_plan",
                       "wf_params": WF_PARAMS_EXPR,
                        "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                        "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC' 
                      },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin")

    end = DummyOperator(task_id="end")

    chain(
        begin,
        create_stg_amazon_asin_portfolio,
        create_stg_amazon_promotion_seasonality,
        create_stg_amazon_promotion_plan_metadata,
        run_uniqueness_check_stg_amazon_promotion_plan_metadata,
        replicate_promotion_planning_sf_to_pg_task,
        insights_api_call_task,
        insights_api_status_monitor_task,
        replicate_promotion_planning_insights_pg_to_sf_task,
        create_stg_fact_amazon_promotion_plan,
        run_uniqueness_check_stg_fact_amazon_promotion_plan,
        merge_fact_amazon_promotion_plan,
        run_audit_promotion_plan,
        end,
    )

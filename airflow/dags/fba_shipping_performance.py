"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'fba_shipping_performance'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2024, 1, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'FBA_SHIPPING_PERFORMANCE',
            'author': 'vikas'},
    tags=['vikas', 'Raptor']
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
        
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load fba_shipping_performance ---
    
    with TaskGroup(group_id='load_fba_shipping_performance') as tg_fba_shipping_performance:
        list_s3_files_fba_shipping_performance_task = PythonOperator(
            task_id="list_s3_files_fba_shipping_performance",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fba_shipping_performance/list_s3_fba_shipping_performance.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            
        )

        # Need a begin dummy operator for branching
        begin_insert_fba_shipping_performance_task = DummyOperator(task_id="begin_insert_fba_shipping_performance")
        skip_insert_fba_shipping_performance_task = DummyOperator(task_id="skip_insert_fba_shipping_performance")
        end_insert_fba_shipping_performance_task = DummyOperator(task_id="end_insert_fba_shipping_performance")
        end_fba_shipping_performance_task = DummyOperator(
            task_id="end_fba_shipping_performance",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_fba_shipping_performance_task = BranchPythonOperator(
            task_id='check_new_files_found_fba_shipping_performance',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "fba_shipping_performance/s3_to_snowflake_fba_shipping_performance.yaml",
                       "skip_task_id": "load_fba_shipping_performance.skip_insert_fba_shipping_performance",
                       "next_task_id": "load_fba_shipping_performance.begin_insert_fba_shipping_performance"
            },
        )

        s3_to_snowflake_fba_shipping_performance_task = PythonOperator(
            task_id="s3_to_snowflake_fba_shipping_performance",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fba_shipping_performance/s3_to_sf_raw_fba_shipping_performance.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_fba_shipping_performance',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_fba_shipping_performance', 
                    field_list=['defect_id', 'fba_shipment_id'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        insert_log_fba_shipping_performance_task = PythonOperator(
            task_id="insert_log_fba_shipping_performance",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "fba_shipping_performance/insert_log_fba_shipping_performance.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_fba_shipping_performance_task = PythonOperator(
            task_id="dedupe_fba_shipping_performance",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "fba_shipping_performance/dedupe_fba_shipping_performance.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_fba_shipping_performance',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_fba_shipping_performance', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_fba_shipping_performance',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_fba_shipping_performance', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        merge_stage_fba_shipping_performance_task = PythonOperator(
            task_id="merge_stage_fba_shipping_performance",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "fba_shipping_performance/merge_stage_fba_shipping_performance.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.fact_fba_shipping_performance',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$curated_db.fact_fba_shipping_performance',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.fact_fba_shipping_performance',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$curated_db.fact_fba_shipping_performance',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        run_audit_fba_shipping_performance_task = PythonOperator(
            task_id="run_audit_fba_shipping_performance",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_fba_shipping_performance", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        (
            list_s3_files_fba_shipping_performance_task >>
            check_new_files_found_fba_shipping_performance_task >>
            [begin_insert_fba_shipping_performance_task, skip_insert_fba_shipping_performance_task]
        )

        (
            begin_insert_fba_shipping_performance_task >> 
            s3_to_snowflake_fba_shipping_performance_task >>
            
         ( run_dq_is_null_raw_task) >>
    
            insert_log_fba_shipping_performance_task >>
            dedupe_fba_shipping_performance_task >>
            
         ( run_dq_is_unique_dedupe_pk_hard_task, 
    
         run_dq_is_null_dedupe_pk_hard_task) >>
    
            merge_stage_fba_shipping_performance_task >>
            
         ( run_dq_is_null_merge_pk_hard_task, 
    
         run_dq_is_unique_merge_pk_hard_task) >>
    
            run_audit_fba_shipping_performance_task >>
            end_insert_fba_shipping_performance_task >>
            end_fba_shipping_performance_task
        )

        skip_insert_fba_shipping_performance_task >> end_fba_shipping_performance_task

    # ---- Main branch ----
    chain(
       begin,
       get_workflow_parameters,
       tg_fba_shipping_performance,
       update_workflow_parameters,
       end
    )
    
from airflow import DAG
from datetime import datetime
from airflow.utils.task_group import TaskGroup
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain

import logging

log = logging.getLogger(__name__)

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id="shopify_orders_hourly",
    start_date=datetime(2022, 5, 8),
    schedule_interval="@hourly",
    catchup=False,
    max_active_runs=1,
    params={
        "workflow_name": "SHOPIFY_ORDERS_HOURLY",
        "author": "ruchira"
    },
    tags=['RETIRED']
) as dag:
    import tasklib.workflow as tlw
    import tasklib.s3 as tls3
    import tasklib.glue as tlg
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts
    from tasklib.loader import S3ToSnowflake
    load_obj = S3ToSnowflake()

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    update_brand_timezone_dim = PythonOperator(
        task_id="update_brand_timezone_dim",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "orders/shopify/update_brand_timezones_dim.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    with TaskGroup(
        group_id="ingest_parquet_data"
    ) as ingest_parquet_data:
        begin_ingestion = DummyOperator(task_id="begin_ingestion")
        end_ingestion = DummyOperator(task_id="end_ingestion")

        list_s3_modified_files_parquet = PythonOperator(
            task_id="task_list_s3_modified_files_parquet",
            python_callable=tls3.list_s3_modified_files,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "args_file": "shopify_orders_hourly/s3_list_folders_parquet.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        transfer_s3_to_snowflake_parquet = PythonOperator(
            task_id="transfer_s3_to_snowflake_parquet",
            python_callable=load_obj.s3_to_snowflake_load,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "args_file": "shopify_orders_hourly/s3_to_sf_raw_shopify_orders_parquet.yaml"
            },
        )

        #shop data
        shopify_shop_list_s3_modified_files_parquet = PythonOperator(
            task_id="task_shopify_shop_list_s3_modified_files_parquet",
            python_callable=tls3.list_s3_modified_files,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "args_file": "shopify_orders_hourly/shopify_shop_s3_list_folders_parquet.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        shopify_shop_transfer_s3_to_snowflake_parquet = PythonOperator(
            task_id="transfer_shopify_shop_s3_to_snowflake_parquet",
            python_callable=load_obj.s3_to_snowflake_load,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "args_file": "shopify_orders_hourly/s3_to_sf_raw_shopify_shop_parquet.yaml"
            },
        )

        stg_shopify_shop = PythonOperator(
            task_id="stg_shopify_shop",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/stg_shopify_shop.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        end_raw_load_shopify_orders = DummyOperator(task_id="end_raw_load_shopify_orders")
    
        chain(
            begin_ingestion,
            [
                list_s3_modified_files_parquet,
                shopify_shop_list_s3_modified_files_parquet
            ],
            [
                transfer_s3_to_snowflake_parquet,
                shopify_shop_transfer_s3_to_snowflake_parquet
            ],
            [
                end_raw_load_shopify_orders,
                stg_shopify_shop
            ],
            end_ingestion,
        )
        
    with TaskGroup(
        group_id="raw_shopify_orders"
    ) as raw_shopify_orders:

        create_stg_raw_shopify_orders = PythonOperator(
            task_id="create_stg_raw_shopify_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_raw_shopify_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        merge_raw_shopify_orders = PythonOperator(
            task_id="merge_raw_shopify_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/merge_raw_shopify_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_raw_order_pk_is_unique = PythonOperator(
            task_id="run_dq_raw_order_pk_is_unique",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': 'shopify_orders_hourly',
                'test_name': 'staging_raw_shopify_orders_pk_is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.raw_shopify_orders",
                    field_list=['"id"','"brand_name"','"shopify_store"'],
                    hard_alert=True
                )
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_raw_shopify_orders = PythonOperator(
            task_id="run_audit_raw_shopify_orders",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.raw_shopify_orders",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        create_stg_raw_shopify_orders  >> merge_raw_shopify_orders >> run_dq_raw_order_pk_is_unique >> run_audit_raw_shopify_orders

    with TaskGroup(group_id="line_item_updates") as line_item_updates:

        merge_shopify_variant_sku_mapping = PythonOperator(
            task_id="merge_shopify_variant_sku_mapping",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/merge_shopify_variant_sku_mapping.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_one_row_per_variant_sku = PythonOperator(
            task_id="task_run_dq_one_row_per_variant_sku",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': 'shopify_orders_hourly',
                'test_name': 'one_row_per_variant_sku',
                'sql_query': """
                    SELECT CASE WHEN COUNT(1) > 0 THEN 2 ELSE 0 END AS "result" 
                    FROM (
                        SELECT 1  FROM $common_dwh_db.shopify_variant_sku_mapping 
                        QUALIFY ROW_NUMBER() OVER (PARTITION BY "brand_name", "variant_id", "sku" order by "first_encountered_at") > 1 LIMIT 1
                    );           
                """
            },
            provide_context=True,
            on_failure_callback = alerts.send_failure_alert
        )

        merge_shopify_order_line_item_updates = PythonOperator(
            task_id="merge_shopify_order_line_item_updates",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/merge_shopify_order_line_item_updates.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_one_row_per_line_item = PythonOperator(
            task_id="task_run_dq_one_row_per_line_item",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': 'shopify_orders_hourly',
                'test_name': 'one_row_per_line_item',
                'sql_query': """
                    SELECT CASE WHEN COUNT(1) > 0 THEN 2 ELSE 0 END AS "result" 
                    FROM (
                        SELECT 1  FROM $common_dwh_db.shopify_order_line_item_updates 
                        QUALIFY ROW_NUMBER() OVER (PARTITION BY "line_item_id" order by "record_updated_timestamp_utc") > 1 LIMIT 1
                    );            
                """
            },
            provide_context=True,
            on_failure_callback = alerts.send_failure_alert
        )

        create_stg_raw_shopify_orders_to_process = PythonOperator(
            task_id="create_stg_raw_shopify_orders_to_process",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_raw_shopify_orders_to_process.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        (
            merge_shopify_variant_sku_mapping
            >> run_dq_one_row_per_variant_sku
            >> merge_shopify_order_line_item_updates
            >> run_dq_one_row_per_line_item
            >> create_stg_raw_shopify_orders_to_process
        )

    with TaskGroup(group_id="stg_shopify_orders_grp") as stg_shopify_orders_grp:

        create_stg_shopify_order_line_items = PythonOperator(
            task_id="create_stg_shopify_order_line_items",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_shopify_order_line_items.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_stg_shopify_order_fulfillments = PythonOperator(
            task_id="create_stg_shopify_order_fulfillments",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_shopify_order_fulfillments.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_stg_shopify_order_shipping_lines_agg = PythonOperator(
            task_id="create_stg_shopify_order_shipping_lines_agg",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_shopify_order_shipping_lines_agg.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_stg_shopify_order_discount_codes = PythonOperator(
            task_id="create_stg_shopify_order_discount_codes",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_shopify_order_discount_codes.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_stg_shopify_orders = PythonOperator(
            task_id="create_stg_shopify_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_shopify_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        chain(
            [
                create_stg_shopify_order_line_items,
                create_stg_shopify_order_fulfillments,
                create_stg_shopify_order_shipping_lines_agg,
                create_stg_shopify_order_discount_codes,
            ],
            create_stg_shopify_orders,
        )

    with TaskGroup(group_id="curated_shopify_orders_grp") as curated_shopify_orders_grp:
        merge_curated_fact_shopify_orders = PythonOperator(
            task_id="merge_curated_fact_shopify_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/merge_curated_fact_shopify_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_uniqueness_check_fact=PythonOperator(
        task_id="task_run_uniqueness_check_fact",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'shopify_orders_hourly',
            'tb_name': "$curated_db.fact_shopify_orders",
            'test_name': 'fact dup check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$curated_db.fact_shopify_orders", 
                field_list=['"order_number"','"line_item_id"'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )
        run_audit_fact_shopify_orders = PythonOperator(
            task_id="run_audit_fact_shopify_orders",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_shopify_orders",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_curated_fact_shopify_orders >> run_uniqueness_check_fact >> run_audit_fact_shopify_orders

    with TaskGroup(
        group_id="curated_fact_all_orders_grp"
    ) as curated_fact_all_orders_grp:
        create_stg_shopify_orders_agg = PythonOperator(
            task_id="create_stg_shopify_orders_agg",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_shopify_orders_agg.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_stg_shopify_fact_all_orders = PythonOperator(
            task_id="create_stg_shopify_fact_all_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_shopify_fact_all_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_order_pk_is_unique = PythonOperator(
            task_id="task_run_dq_order_pk_is_unique",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': 'shopify_orders_hourly',
                'test_name': 'order_pk_is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.stg_shopify_fact_all_orders",
                    field_list=['"order_pk"'],
                    hard_alert=True
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        merge_fact_all_orders = PythonOperator(
            task_id="merge_fact_all_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/merge_fact_all_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        update_fact_all_orders = PythonOperator(
            task_id="update_fact_all_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/update_fact_all_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_tests_curated=PythonOperator(
            task_id="task_run_dq_tests_curated",
            python_callable=tldq.run_dq_file,
            op_kwargs={
            'wk_name': 'shopify_orders_hourly',
            'run_id': 'task_run_dq_tests_curated',
            'query_file': "orders/shopify/dq_fact_all_orders.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_audit_fact_all_orders = PythonOperator(
            task_id="run_audit_fact_all_orders",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_all_orders",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        chain(
            create_stg_shopify_orders_agg,
            create_stg_shopify_fact_all_orders,
            run_dq_order_pk_is_unique,
            merge_fact_all_orders,
            update_fact_all_orders,
            run_dq_tests_curated,
            run_audit_fact_all_orders,
        )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
    )

    begin = DummyOperator(
        task_id="begin", depends_on_past=True, wait_for_downstream=True
    )
    end = DummyOperator(task_id="end")

    chain(
        begin,
        get_workflow_params,
        ingest_parquet_data,
        [
            raw_shopify_orders,
            update_brand_timezone_dim
        ],
        line_item_updates,
        stg_shopify_orders_grp,
        [
            curated_shopify_orders_grp,
            curated_fact_all_orders_grp
        ],
        update_workflow_params,
        end,
    )

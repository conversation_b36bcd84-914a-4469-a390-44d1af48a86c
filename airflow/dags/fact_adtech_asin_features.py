
import logging
from datetime import datetime
import json

from airflow import DAG
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
import tasklib.sql as tlsql
from tasklib import alerts
from airflow.models.baseoperator import chain
import tasklib.workflow as tlw
import tasklib.dq as tldq
import tasklib.dq as tldq
from airflow.utils.task_group import TaskGroup

"""
This pipeline unifies Amazon Ads data with Finance and Retail data. This dataset is being used by the
ad bidding ML algorithm to create model which will be used to predict future bid values.
To backfill data use run dag with conf option and set the 'backfill_date' parameter to the desired date.
e.g. set the data to "backfill_date":"2021-01-01" to backfill from begining of 2021
Below is the exact config you can use:
{
    "workflow_name": "FACT_ADTECH_ASIN_FEATURES",
    "author": "Sauvik",
    "backfill_date":"2021-01-01"
}
"""

DAG_ID = 'fact_adtech_asin_features'
TRANSFORM_BASE = 'ads/adtech/' + DAG_ID
RELEASE_DEF = '1'
BUILD_NUM = '01'

WF_PARAMS_EXPR = json.dumps({'start_date': "{{ execution_date if dag_run.conf.get('backfill_date') is none else dag_run.conf.get('backfill_date') }}"})

log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2022, 5, 8),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'sauvik'
            },
    tags=['RETIRED' ,'Adtech']
) as dag:
    
    with TaskGroup(group_id='tg_load_stg_adtech_asin_item_map') as tg_load_stg_adtech_asin_item_map:
    
        load_stg_adtech_asin_item_map = PythonOperator(
                task_id="load_stg_adtech_asin_item_map",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection": "Snowflake",
                    "sql_file": TRANSFORM_BASE + "/stg_adtech_asin_item_map.sql",
                    "wf_params": WF_PARAMS_EXPR},
                on_failure_callback=alerts.send_failure_alert,
                provide_context=True
        )

        run_dq_stg_adtech_asin_item_map = PythonOperator(
            task_id="run_dq_stg_adtech_asin_item_map",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$stage_db.stg_adtech_asin_item_map",
                'test_name': 'pk_not_duplicate',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.stg_adtech_asin_item_map ", 
                    field_list=['item_number'],
                    hard_alert=True
                )
                },
            on_failure_callback=alerts.send_failure_alert
        )

    load_stg_adtech_asin_item_map >> run_dq_stg_adtech_asin_item_map

    with TaskGroup(group_id='tg_load_stg_adtech_asin_cpaa') as tg_load_stg_adtech_asin_cpaa:
        load_stg_adtech_asin_cpaa = PythonOperator(
                task_id="load_stg_adtech_asin_cpaa",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection": "Snowflake",
                    "sql_file": TRANSFORM_BASE + "/stg_adtech_asin_cpaa.sql",
                    "wf_params": WF_PARAMS_EXPR},
                on_failure_callback=alerts.send_failure_alert,
                provide_context=True
        )

        run_dq_stg_adtech_asin_cpaa = PythonOperator(
            task_id="run_dq_stg_adtech_asin_cpaa",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$stage_db.stg_adtech_asin_cpaa",
                'test_name': 'pk_not_duplicate',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.stg_adtech_asin_cpaa", 
                    field_list=['date_pst','asin'],
                    hard_alert=True
                )
                },
            on_failure_callback=alerts.send_failure_alert
        )

    load_stg_adtech_asin_cpaa >> run_dq_stg_adtech_asin_cpaa

    load_stg_fact_adtech_asin_features = PythonOperator(
        task_id="load_stg_fact_adtech_asin_features",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/stg_fact_adtech_asin_features.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_stg_fact_adtech_asin_features = PythonOperator(
        task_id="run_dq_stg_fact_adtech_asin_features",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.stg_fact_adtech_asin_features",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_adtech_asin_features", 
                field_list=['advertisement_pk'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_cpaa_cpea_stg_fact_adtech_asin_features = PythonOperator(
        task_id="run_dq_cpaa_cpea_stg_fact_adtech_asin_features",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.stg_fact_adtech_asin_features",
            'test_name': 'cpaa_gt_than_cpea',
            'sql_query': """
                SELECT 
                    CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END as "result"
                FROM (
                    SELECT 1
                    FROM $stage_db.stg_fact_adtech_asin_features
                    WHERE cpaa > cpea
                    LIMIT 1
                ) T
            """
            },
        on_failure_callback=alerts.send_failure_alert
    )

    run_merge = PythonOperator(
        task_id="run_merge",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/merge.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    # --- Audit the data checks ---

    import tasklib.audit as tla

    run_audit = PythonOperator(
        task_id="run_audit",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.FACT_ADTECH_ASIN_FEATURES",
                   "wf_params": "{}",
                   "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                   "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                   },
        on_failure_callback=alerts.send_failure_alert
    )


begin = DummyOperator(task_id="begin")
end = DummyOperator(task_id="end")

chain(
    begin,
    tg_load_stg_adtech_asin_item_map,
    tg_load_stg_adtech_asin_cpaa,
    load_stg_fact_adtech_asin_features,
    [run_dq_stg_fact_adtech_asin_features, run_dq_cpaa_cpea_stg_fact_adtech_asin_features],
    run_merge,
    run_audit,
    end,
)

"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'amazon_supply_chain_scraper_data'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2025, 1, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'AMAZON_SUPPLY_CHAIN_SCRAPER_DATA',
            'author': 'arnab'},
    tags=['arnab', 'Raptor']
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}

    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load historical_reservation_fees ---

    with TaskGroup(group_id='load_historical_reservation_fees') as tg_historical_reservation_fees:
        list_s3_files_historical_reservation_fees_task = PythonOperator(
            task_id="list_s3_files_historical_reservation_fees",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_supply_chain_scraper_data/list_s3_historical_reservation_fees.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_historical_reservation_fees_task = DummyOperator(task_id="begin_insert_historical_reservation_fees")
        skip_insert_historical_reservation_fees_task = DummyOperator(task_id="skip_insert_historical_reservation_fees")
        end_insert_historical_reservation_fees_task = DummyOperator(task_id="end_insert_historical_reservation_fees")
        end_historical_reservation_fees_task = DummyOperator(
            task_id="end_historical_reservation_fees",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_historical_reservation_fees_task = BranchPythonOperator(
            task_id='check_new_files_found_historical_reservation_fees',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_supply_chain_scraper_data/s3_to_snowflake_historical_reservation_fees.yaml",
                       "skip_task_id": "load_historical_reservation_fees.skip_insert_historical_reservation_fees",
                       "next_task_id": "load_historical_reservation_fees.begin_insert_historical_reservation_fees"
            },
        )

        s3_to_snowflake_historical_reservation_fees_task = PythonOperator(
            task_id="s3_to_snowflake_historical_reservation_fees",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_supply_chain_scraper_data/s3_to_sf_raw_historical_reservation_fees.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )


        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_historical_reservation_fees',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_historical_reservation_fees',
                    field_list=['reservation_date'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )


        insert_log_historical_reservation_fees_task = PythonOperator(
            task_id="insert_log_historical_reservation_fees",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_supply_chain_scraper_data/insert_log_historical_reservation_fees.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_historical_reservation_fees_task = PythonOperator(
            task_id="dedupe_historical_reservation_fees",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_supply_chain_scraper_data/dedupe_historical_reservation_fees.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_historical_reservation_fees',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_historical_reservation_fees',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_historical_reservation_fees',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_historical_reservation_fees',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )


        merge_stage_historical_reservation_fees_task = PythonOperator(
            task_id="merge_stage_historical_reservation_fees",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_supply_chain_scraper_data/merge_stage_historical_reservation_fees.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )



        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.historical_reservation_fees',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$curated_db.historical_reservation_fees',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.historical_reservation_fees',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$curated_db.historical_reservation_fees',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )


        run_audit_historical_reservation_fees_task = PythonOperator(
            task_id="run_audit_historical_reservation_fees",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.historical_reservation_fees", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )


        (
            list_s3_files_historical_reservation_fees_task >>
            check_new_files_found_historical_reservation_fees_task >>
            [begin_insert_historical_reservation_fees_task, skip_insert_historical_reservation_fees_task]
        )

        (
            begin_insert_historical_reservation_fees_task >>
            s3_to_snowflake_historical_reservation_fees_task >>

         ( run_dq_is_null_raw_task) >>

            insert_log_historical_reservation_fees_task >>
            dedupe_historical_reservation_fees_task >>

         ( run_dq_is_unique_dedupe_pk_hard_task,

         run_dq_is_null_dedupe_pk_hard_task) >>

            merge_stage_historical_reservation_fees_task >>

         ( run_dq_is_unique_merge_pk_hard_task,

         run_dq_is_null_merge_pk_hard_task) >>

            run_audit_historical_reservation_fees_task >>
            end_insert_historical_reservation_fees_task >>
            end_historical_reservation_fees_task
        )

        skip_insert_historical_reservation_fees_task >> end_historical_reservation_fees_task

    #  --- Load inventory_capacity ---

    with TaskGroup(group_id='load_inventory_capacity') as tg_inventory_capacity:
        list_s3_files_inventory_capacity_task = PythonOperator(
            task_id="list_s3_files_inventory_capacity",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_supply_chain_scraper_data/list_s3_inventory_capacity.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_inventory_capacity_task = DummyOperator(task_id="begin_insert_inventory_capacity")
        skip_insert_inventory_capacity_task = DummyOperator(task_id="skip_insert_inventory_capacity")
        end_insert_inventory_capacity_task = DummyOperator(task_id="end_insert_inventory_capacity")
        end_inventory_capacity_task = DummyOperator(
            task_id="end_inventory_capacity",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_inventory_capacity_task = BranchPythonOperator(
            task_id='check_new_files_found_inventory_capacity',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_supply_chain_scraper_data/s3_to_snowflake_inventory_capacity.yaml",
                       "skip_task_id": "load_inventory_capacity.skip_insert_inventory_capacity",
                       "next_task_id": "load_inventory_capacity.begin_insert_inventory_capacity"
            },
        )

        s3_to_snowflake_inventory_capacity_task = PythonOperator(
            task_id="s3_to_snowflake_inventory_capacity",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_supply_chain_scraper_data/s3_to_sf_raw_inventory_capacity.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )


        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_inventory_capacity',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_inventory_capacity',
                    field_list=['storage_type'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )


        insert_log_inventory_capacity_task = PythonOperator(
            task_id="insert_log_inventory_capacity",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_supply_chain_scraper_data/insert_log_inventory_capacity.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_inventory_capacity_task = PythonOperator(
            task_id="dedupe_inventory_capacity",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_supply_chain_scraper_data/dedupe_inventory_capacity.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_inventory_capacity',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_inventory_capacity',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_inventory_capacity',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_inventory_capacity',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )


        merge_stage_inventory_capacity_task = PythonOperator(
            task_id="merge_stage_inventory_capacity",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_supply_chain_scraper_data/merge_stage_inventory_capacity.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )



        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.inventory_capacity',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$curated_db.inventory_capacity',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.inventory_capacity',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$curated_db.inventory_capacity',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )


        run_audit_inventory_capacity_task = PythonOperator(
            task_id="run_audit_inventory_capacity",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.inventory_capacity", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )


        (
            list_s3_files_inventory_capacity_task >>
            check_new_files_found_inventory_capacity_task >>
            [begin_insert_inventory_capacity_task, skip_insert_inventory_capacity_task]
        )

        (
            begin_insert_inventory_capacity_task >>
            s3_to_snowflake_inventory_capacity_task >>

         ( run_dq_is_null_raw_task) >>

            insert_log_inventory_capacity_task >>
            dedupe_inventory_capacity_task >>

         ( run_dq_is_unique_dedupe_pk_hard_task,

         run_dq_is_null_dedupe_pk_hard_task) >>

            merge_stage_inventory_capacity_task >>

         ( run_dq_is_unique_merge_pk_hard_task,

         run_dq_is_null_merge_pk_hard_task) >>

            run_audit_inventory_capacity_task >>
            end_insert_inventory_capacity_task >>
            end_inventory_capacity_task
        )

        skip_insert_inventory_capacity_task >> end_inventory_capacity_task

    #  --- Load sourcing_cost ---

    with TaskGroup(group_id='load_sourcing_cost') as tg_sourcing_cost:
        list_s3_files_sourcing_cost_task = PythonOperator(
            task_id="list_s3_files_sourcing_cost",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_supply_chain_scraper_data/list_s3_sourcing_cost.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # Need a begin dummy operator for branching
        begin_insert_sourcing_cost_task = DummyOperator(task_id="begin_insert_sourcing_cost")
        skip_insert_sourcing_cost_task = DummyOperator(task_id="skip_insert_sourcing_cost")
        end_insert_sourcing_cost_task = DummyOperator(task_id="end_insert_sourcing_cost")
        end_sourcing_cost_task = DummyOperator(
            task_id="end_sourcing_cost",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_sourcing_cost_task = BranchPythonOperator(
            task_id='check_new_files_found_sourcing_cost',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_supply_chain_scraper_data/s3_to_snowflake_sourcing_cost.yaml",
                       "skip_task_id": "load_sourcing_cost.skip_insert_sourcing_cost",
                       "next_task_id": "load_sourcing_cost.begin_insert_sourcing_cost"
            },
        )

        s3_to_snowflake_sourcing_cost_task = PythonOperator(
            task_id="s3_to_snowflake_sourcing_cost",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_supply_chain_scraper_data/s3_to_sf_raw_sourcing_cost.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_sourcing_cost',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_sourcing_cost',
                    field_list=['asin', 'fnsku', 'msku'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_sourcing_cost_task = PythonOperator(
            task_id="insert_log_sourcing_cost",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_supply_chain_scraper_data/insert_log_sourcing_cost.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_sourcing_cost_task = PythonOperator(
            task_id="dedupe_sourcing_cost",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_supply_chain_scraper_data/dedupe_sourcing_cost.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sourcing_cost',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_sourcing_cost',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sourcing_cost',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_sourcing_cost',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_sourcing_cost_task = PythonOperator(
            task_id="merge_stage_sourcing_cost",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_supply_chain_scraper_data/merge_stage_sourcing_cost.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.sourcing_cost',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$curated_db.sourcing_cost',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.sourcing_cost',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$curated_db.sourcing_cost',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_sourcing_cost_task = PythonOperator(
            task_id="run_audit_sourcing_cost",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.sourcing_cost",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_sourcing_cost_task >>
            check_new_files_found_sourcing_cost_task >>
            [begin_insert_sourcing_cost_task, skip_insert_sourcing_cost_task]
        )

        (
            begin_insert_sourcing_cost_task >>
            s3_to_snowflake_sourcing_cost_task >>

            (run_dq_is_null_raw_task) >>

            insert_log_sourcing_cost_task >>
            dedupe_sourcing_cost_task >>

            (run_dq_is_unique_dedupe_pk_hard_task,
            run_dq_is_null_dedupe_pk_hard_task) >>

            merge_stage_sourcing_cost_task >>

            (run_dq_is_unique_merge_pk_hard_task,
            run_dq_is_null_merge_pk_hard_task) >>

            run_audit_sourcing_cost_task >>
            end_insert_sourcing_cost_task >>
            end_sourcing_cost_task
        )

        skip_insert_sourcing_cost_task >> end_sourcing_cost_task

    # ---- Main branch ----
    chain(
        begin,
        get_workflow_parameters,
        [tg_historical_reservation_fees, tg_inventory_capacity, tg_sourcing_cost],
        update_workflow_parameters,
        end
    )

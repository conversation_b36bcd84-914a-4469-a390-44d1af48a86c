"""
dag to update DWH.shopify_transaction_record snowflake table
"""
from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
import logging

log = logging.getLogger(__name__)

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"


with DAG(
    dag_id="SHOPIFY_TRANSACTIONAL_RECORDS",
    start_date=datetime(2022, 11, 8),
    schedule_interval="@daily",
    catchup=False,
    max_active_runs=1,
    tags=["Shopify","Vikas"],
    params={"workflow_name": "SHOPIFY_TRANSACTIONAL_RECORDS", "author": "vikas"},
) as dag:
    import tasklib.workflow as tlw
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts
    import tasklib.dq as tldq

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    create_shopify_product_records_records = PythonOperator(
        task_id="create_shopify_product_records_records",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "transactional_records/shopify/create_stg_shopify_dwh_product_records.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_shopify_orders_dwh = PythonOperator(
        task_id="create_stg_shopify_orders_dwh",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "transactional_records/shopify/create_stg_orders_shopify_sku.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    curated_table_update = PythonOperator(
        task_id="curated_table_update",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "transactional_records/shopify/publish_final_curated.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
    )

    dq_curated_dq_null_check = PythonOperator(
        task_id="dq_curated_dq_null_check",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            "wk_name": "SHOPIFY_TRANSACTIONAL_RECORDS",
            "tb_name": "$curated_db.shopify_transactional_records_v1",
            "test_name": "is_null",
            "sql_query": tldq.gen_check_if_nulls(
                tb_name="$curated_db.shopify_transactional_records_v1",
                field_list=[
                    "connector_region","source_type",
                    "variant_id",
                    "product_id","product_name"
                ],
                hard_alert=False,
            ),
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    dq_stg_is_unique_pk = PythonOperator(
        task_id="dq_stg_is_unique_pk",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            "wk_name": "SHOPIFY_TRANSACTIONAL_RECORDS",
            "tb_name": "$stage_db.stg_orders_shopify_sku",
            "test_name": "is_unique",
            "sql_query": tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_orders_shopify_sku",
                field_list=["ITEM_PK"],
                hard_alert=False,
            ),
        },
        on_failure_callback=alerts.send_failure_alert,
    )


    begin = DummyOperator(task_id="begin")

    end = DummyOperator(task_id="end")

    (
        begin
        >> get_workflow_params
        >> [
            create_shopify_product_records_records,
            create_stg_shopify_orders_dwh,
        ]
        >> dq_stg_is_unique_pk
        >> curated_table_update
        >> dq_curated_dq_null_check
        >> update_workflow_params
        >> end
    )

""" Data transaformation code generator for Brand EBIDTDA framework """

import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

BUILD_NUM = '1'
DAG_ID = 'brand_ebitda_Incre_wip_ops_item_level_hourly_WIP_incre'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2024, 1, 1),
    schedule_interval='30 * * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'BRAND_EBITDA_SKU', 'author': 'akshay'},
    tags=['akshay', 'Raptor']
) as dag:

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    # --- Hourly processing ---
    with TaskGroup(group_id='brand_ebitda_hourly_ops_item_level_all_markets') as tg_brand_ebitda_item_ops_hourly:
        target_table_creation = PythonOperator(
            task_id="target_table_creation",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "brand_ebitda_framework/wip_incre_brand_ebitda_hourly_ops_item_target_table_creation.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        staging_incremental = PythonOperator(
            task_id="staging_incremental",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "brand_ebitda_framework/wip_incre_stg_brand_ebitda_hourly_ops_item_level_all_markets_stg_incremental.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_incremental = PythonOperator(
            task_id="merge_incremental",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "brand_ebitda_framework/wip_incre_merge_brand_ebitda_hourly_ops_item_level_all_markets_merge_incremental.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        target_table_creation >> staging_incremental >> merge_incremental

    # --- Daily item-level processing ---
    with TaskGroup(group_id='brand_ebitda_daily_ops_item_level_all_channel_markets') as tg_brand_ebitda_item_ops_daily:
        target_table_creation_daily = PythonOperator(
            task_id="target_table_creation_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "brand_ebitda_framework/wip_incre_brand_ebitda_daily_ops_item_level_target_table_creation.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        staging_incremental_daily = PythonOperator(
            task_id="staging_incremental_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "brand_ebitda_framework/wip_incre_stg_brand_ebitda_daily_ops_item_level_stg.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_incremental_daily = PythonOperator(
            task_id="merge_incremental_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "brand_ebitda_framework/wip_incre_merge_brand_ebitda_daily_ops_item_level_all_channel_markets.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        target_table_creation_daily >> staging_incremental_daily >> merge_incremental_daily

    # --- Daily brand-level processing ---
    with TaskGroup(group_id='brand_ebitda_daily_ops_brand_level_all_channel_markets') as tg_brand_ebitda_brand_daily:
        target_table_creation_brand_daily = PythonOperator(
            task_id="target_table_creation_brand_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "brand_ebitda_framework/wip_incre_brand_ebitda_daily_ops_brand_level_target_table_creation.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        staging_incremental_brand_daily = PythonOperator(
            task_id="staging_incremental_brand_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "brand_ebitda_framework/wip_incre_stg_brand_ebitda_daily_ops_brand_level_stg.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_incremental_brand_daily = PythonOperator(
            task_id="merge_incremental_brand_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "brand_ebitda_framework/wip_incre_merge_brand_ebitda_daily_ops_brand_level_all_channel_markets.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        target_table_creation_brand_daily >> staging_incremental_brand_daily >> merge_incremental_brand_daily

    # --- Main DAG chain ---
    chain(
        begin,
        tg_brand_ebitda_item_ops_hourly,  # Hourly TaskGroup
        tg_brand_ebitda_item_ops_daily,   # Daily item-level TaskGroup
        tg_brand_ebitda_brand_daily,      # Daily brand-level TaskGroup (corrected variable)
        end
    )

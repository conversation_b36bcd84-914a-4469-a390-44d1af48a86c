""" Data transaformation code generator for Brand EBIDTDA framework """

import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake


BUILD_NUM = '1'
DAG_ID = 'brand_ebitda_settlements'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2024, 1, 1),
    schedule_interval='20 9 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'BRAND_EBITDA',
            'author': 'akshay'},
    tags=['akshay', 'Raptor']
) as dag:


    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    #  --- process brand edbidta metrics for _report ---
    
    with TaskGroup(group_id='process_brand_ebitda_settlement_metrics') as tg_brand_ebitda_framework:
        process_brand_ebitda_metrics_task = PythonOperator(
            task_id="process_brand_ebitda_settlements",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/brand_ebitda_settlements_bi_metrics_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

    # --- Data Quality Check for NULL values in critical fields ---
        # run_dq_is_null_metrics_task = PythonOperator(
        #     task_id="run_dq_is_null_metrics",
        #     python_callable=tldq.run_dq_string,
        #     op_kwargs={
        #         'wk_name': DAG_ID,
        #         'tb_name': '$stage_db.brand_ebitda_sku_settlements_metrics',
        #         'test_name': 'is_null',
        #         'sql_query': tldq.gen_check_if_nulls(
        #             tb_name='$stage_db.brand_ebitda_sku_settlements_metrics',
        #             field_list=['shipping_date', 'country_code', 'sku'], 
        #             hard_alert=True,
        #         )
        #     },
        #     on_failure_callback=alerts.send_failure_alert,
        # )


    # --- Chain the tasks inside the Task Group ---
    process_brand_ebitda_metrics_task 

    # --- Main DAG chain ---
    chain(
        begin,
        tg_brand_ebitda_framework,  # This TaskGroup includes the processing and DQ tasks
        end
    )

import json
import logging
from datetime import datetime
from airflow import DAG
from airflow.models import Variable
from airflow.models.baseoperator import chain
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator

import tasklib.dq as tldq
import tasklib.sql as tlsql
import tasklib.s3_event_logging as tlsel
from tasklib import alerts

log = logging.getLogger(__name__)

DAG_ID = "s3_datalake_event_logging"
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@hourly') # Default to hourly runs
WF_PARAMS_EXPR = json.dumps({'execution_timestamp': '{{ ts }}'})


with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2023, 4, 11),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'author': 'harshad'},
    tags=["harshad"],
) as dag:
    
    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    task_save_sqs_messages = PythonOperator(
        task_id="save_sqs_messages",
        python_callable=tlsel.save_sqs_messages,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    task_load_messages_to_snowflake = PythonOperator(
        task_id="load_messages_to_snowflake",
        python_callable=tlsel.load_messages_to_snowflake,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    task_update_monitor_s3_datalake_event_logs = PythonOperator(
        task_id="update_monitor_s3_datalake_event_logs",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file": "s3_datalake_event_logging/update_monitor_s3_datalake_event_logs.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    task_data_cleanup = PythonOperator(
        task_id="data_cleanup",
        python_callable=tlsel.data_cleanup,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    task_update_monitor_s3_daily_ingestions = PythonOperator(
        task_id="update_monitor_s3_daily_ingestions",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file": "s3_datalake_event_logging/update_monitor_s3_daily_ingestions.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_dq_monitor_s3_daily_ingestions = PythonOperator(
        task_id="run_dq_monitor_s3_daily_ingestions",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'run_dq_monitor_s3_daily_ingestions',
            'tb_name': "$infra_db.monitor_s3_daily_ingestions",
            'query_file': "s3_datalake_event_logging/dq_monitor_s3_daily_ingestions.yaml"
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    task_insert_monitor_s3_latest_snapshot = PythonOperator(
        task_id="insert_monitor_s3_latest_snapshot",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file": "s3_datalake_event_logging/insert_monitor_s3_latest_snapshot.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    chain(
        begin,
        task_save_sqs_messages,
        task_load_messages_to_snowflake,
        task_update_monitor_s3_datalake_event_logs,
        task_data_cleanup,
        task_update_monitor_s3_daily_ingestions,
        run_dq_monitor_s3_daily_ingestions,
        task_insert_monitor_s3_latest_snapshot,
        end
    )

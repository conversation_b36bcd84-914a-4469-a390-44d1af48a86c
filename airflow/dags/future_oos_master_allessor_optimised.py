import logging
from airflow.models import DAG
from airflow.providers.snowflake.operators.snowflake import SnowflakeOperator
from airflow.utils.dates import days_ago
from tasklib.config import get_env
from tasklib.alerts import send_failure_alert_ops
from datetime import timedelta
from airflow.operators.python import PythonOperator
import tasklib.sql as tlsql
from airflow.operators.dummy_operator import DummyOperator


DAG_ID = "future_oos_master_stage_one_allessor_prod"
WF_PARAMS_EXPR = "{}"

logger = logging.getLogger(__name__)


# This is my parent DAG
with DAG(
    dag_id=DAG_ID,
    description="Main source of channel future OOS data All Essor",
    start_date = days_ago(1),
    tags=["ods"],
    catchup=False,
    # Schedule interval hourly.
    schedule_interval="0 14 * * *",
    params={'author': 'chirag'}
) as dag:  
        

        gsr_stage_1_fbt_items = PythonOperator(
            task_id="gsr_stage_1_fbt_items",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "future_oos/optimized_code/GSR_1_FBT_ITEMS.sql",
                "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=send_failure_alert_ops)
       

        gsr_stage_2_channel_forecast = PythonOperator(
            task_id="gsr_stage_2_channel_forecast",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "future_oos/optimized_code/GSR_2_CHANNEL_FORECAST.sql",
                "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=send_failure_alert_ops)


        gsr_stage_3_channel_mape_bias = PythonOperator(
        task_id="gsr_stage_3_channel_mape_bias",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "future_oos/optimized_code/GSR_3_CHANNEL_MAPE_BIAS.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=send_failure_alert_ops)


        gsr_stage_4_channel_pos = PythonOperator(
        task_id="gsr_stage_4_channel_pos",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "future_oos/optimized_code/GSR_4_CHANNEL_POS.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=send_failure_alert_ops)

        
        gsr_stage_5_channel_fcast_inv_pos = PythonOperator(
        task_id="gsr_stage_5_channel_fcast_inv_pos",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "future_oos/optimized_code/GSR_5_CHANNEL_FCAST_POS_INV.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=send_failure_alert_ops)
        

        gsr_stage_6_cp = PythonOperator(
        task_id="gsr_stage_6_cp",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "future_oos/optimized_code/GSR_6_CP.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=send_failure_alert_ops)


        gsr_stage_7_channel_final_op = PythonOperator(
        task_id="gsr_stage_7_channel_final_op",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "future_oos/optimized_code/GSR_7_CHANNEL_FINAL_OP.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=send_failure_alert_ops)

       
        gsr_stage_8_all_forecast = PythonOperator(
        task_id="gsr_stage_8_all_forecast",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "future_oos/optimized_code/GSR_8_ALL_FORECAST.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=send_failure_alert_ops)


        gsr_stage_9_all_mape_bias = PythonOperator(
        task_id="gsr_stage_9_all_mape_bias",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "future_oos/optimized_code/GSR_9_ALL_MAPE_BIAS.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=send_failure_alert_ops)


        gsr_stage_10_all_pos = PythonOperator(
        task_id="gsr_stage_10_all_pos",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "future_oos/optimized_code/GSR_10_ALL_POS.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=send_failure_alert_ops) 


        gsr_stage_11_all_fcast_pos_inv = PythonOperator(
        task_id="gsr_stage_11_all_fcast_pos_inv",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "future_oos/optimized_code/GSR_11_ALL_FCAST_POS_INV.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=send_failure_alert_ops) 
       

        gsr_stage_12_all_final_op = PythonOperator(
        task_id="gsr_stage_12_all_final_op",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "future_oos/optimized_code/GSR_12_ALL_FINAL_OP.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=send_failure_alert_ops) 


        gsr_stage_13_complete_final_op = PythonOperator(
        task_id="gsr_stage_13_complete_final_op",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "future_oos/optimized_code/GSR_13_COMPLETE_FINAL_OP.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=send_failure_alert_ops) 

        begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
        end = DummyOperator(task_id="end")
        
        
        begin >> gsr_stage_1_fbt_items >> gsr_stage_2_channel_forecast >> gsr_stage_3_channel_mape_bias >> gsr_stage_4_channel_pos >> gsr_stage_5_channel_fcast_inv_pos >> gsr_stage_6_cp >> gsr_stage_7_channel_final_op >> gsr_stage_8_all_forecast >> gsr_stage_9_all_mape_bias  >> gsr_stage_10_all_pos >> gsr_stage_11_all_fcast_pos_inv >> gsr_stage_12_all_final_op >> gsr_stage_13_complete_final_op >> end
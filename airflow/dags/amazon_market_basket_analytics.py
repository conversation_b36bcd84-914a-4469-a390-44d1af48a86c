from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.models import Variable
import logging
from tasklib import alerts
import tasklib.workflow as tlw
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.glue as tlg
import tasklib.dq as tldq
import tasklib.audit as tla
from tasklib.loader import S3ToSnowflake

load_obj = S3ToSnowflake()

log = logging.getLogger(__name__)

WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

DAG_ID = 'amazon_market_basket_analytics'

with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022,5,8),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'ruchira'
           }
) as dag:

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

    list_s3_modified_files = PythonOperator(
        task_id="task_list_s3_modified_files",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "amazon_market_basket_analytics/s3_list_folders.yaml", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )
    
    s3_to_snowflake=PythonOperator(
        task_id="task_s3_to_snowflake",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file":"amazon_market_basket_analytics/s3_to_sf_raw_amazon_market_basket_analytics.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    insert_raw_amazon_market_basket_analytics_log = PythonOperator(
        task_id="task_insert_raw_amazon_market_basket_analytics_log",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
                "connection": "Snowflake",
                "sql_file": "market_basket_analytics/amazon/insert_raw_amazon_market_basket_analytics_log.sql",
                "wf_params": WF_PARAMS_EXPR,
        },
    )

    merge_stg_amazon_market_basket_analytics = PythonOperator(
        task_id="task_merge_stg_amazon_market_basket_analytics",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
                "connection": "Snowflake",
                "sql_file": "market_basket_analytics/amazon/merge_stg_amazon_market_basket_analytics.sql",
                "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_fact_amazon_market_basket_analytics = PythonOperator(
        task_id="task_create_stg_fact_amazon_market_basket_analytics",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
                "connection": "Snowflake",
                "sql_file": "market_basket_analytics/amazon/create_stg_fact_amazon_market_basket_analytics.sql",
                "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_dq_tests_stg_fact_amazon_market_basket_analytics = PythonOperator(
        task_id="task_run_dq_tests_stg_fact_amazon_market_basket_analytics",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'stg_fact_amazon_market_basket_analytics_is_unique',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_amazon_market_basket_analytics",
                field_list=['asin','purchased_with_asin','country_code','start_date','end_date'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    merge_fact_amazon_market_basket_analytics = PythonOperator(
        task_id="task_merge_fact_amazon_market_basket_analytics",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
                "connection": "Snowflake",
                "sql_file": "market_basket_analytics/amazon/merge_fact_amazon_market_basket_analytics.sql",
                "wf_params": WF_PARAMS_EXPR,
        },
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    chain(
        begin,
        get_workflow_params,
        list_s3_modified_files,
        s3_to_snowflake,
        insert_raw_amazon_market_basket_analytics_log,
        merge_stg_amazon_market_basket_analytics,
        create_stg_fact_amazon_market_basket_analytics,
        run_dq_tests_stg_fact_amazon_market_basket_analytics,
        merge_fact_amazon_market_basket_analytics,
        update_workflow_params,
        end,
    )

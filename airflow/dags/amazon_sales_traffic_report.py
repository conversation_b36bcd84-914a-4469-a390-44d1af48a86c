from airflow import D<PERSON>
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging
from tasklib import alerts
import tasklib.workflow as tlw
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.dq as tldq
import tasklib.audit as tla
from tasklib.loader import S3ToSnowflake

load_obj = S3ToSnowflake()
log = logging.getLogger(__name__)

WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

DAG_ID = 'amazon_sales_traffic_report'

with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022,5,8),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'ruchira'
           }
) as dag:

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

    list_s3_modified_files = PythonOperator(
        task_id="task_list_s3_modified_files",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "amazon_sales_traffic_report/child_asin_s3_list_folders.yaml", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_goessor = PythonOperator(
        task_id="task_list_s3_modified_files_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "amazon_sales_traffic_report/child_asin_s3_list_folders_goessor.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake = PythonOperator(
        task_id="task_transfer_s3_to_snowflake",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_sales_traffic_report/s3_to_sf_raw_amazon_sales_traffic_report_child_asin.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake_goessor = PythonOperator(
        task_id="task_transfer_s3_to_snowflake_goessor",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_sales_traffic_report/s3_to_sf_raw_amazon_sales_traffic_report_child_asin_goessor.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    insert_raw_child_asin_log = PythonOperator(
        task_id="task_insert_raw_child_asin_log",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection": "Snowflake", "sql_file": "sales_traffic_report/amazon/insert_raw_sales_traffic_report_child_asin_log.sql",
                   "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    merge_stg_child_asin = PythonOperator(
        task_id="task_merge_stg_child_asin",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection": "Snowflake", "sql_file": "sales_traffic_report/amazon/merge_stg_amazon_sales_traffic_report_child_asin.sql",
                   "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_uniqueness_check_staging=PythonOperator(
        task_id="task_run_uniqueness_check_staging",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.STG_AMAZON_SALES_TRAFFIC_REPORT_CHILD_ASIN",
            'test_name': 'stage dup check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.STG_AMAZON_SALES_TRAFFIC_REPORT_CHILD_ASIN", 
                field_list=['child_asin','parent_asin','date','seller_id','report_start_date','report_end_date','marketplace_id'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_fact_amazon_asin_sales_traffic_report = PythonOperator(
        task_id="task_create_stg_fact_amazon_asin_sales_traffic_report",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection": "Snowflake", "sql_file": "sales_traffic_report/amazon/create_stg_fact_amazon_asin_sales_traffic_report.sql",
                   "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    merge_fact_all_item_sales_traffic_report = PythonOperator(
        task_id="task_merge_fact_all_item_sales_traffic_report",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection": "Snowflake", "sql_file": "sales_traffic_report/amazon/merge_fact_all_item_sales_traffic_report.sql",
                   "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_fact_amazon_sales_traffic_report = PythonOperator(
        task_id="task_update_fact_amazon_sales_traffic_report",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection": "Snowflake", "sql_file": "sales_traffic_report/amazon/update_fact_amazon_sales_traffic_report.sql",
                   "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_uniqueness_check_fact=PythonOperator(
        task_id="task_run_uniqueness_check_fact",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.stg_fact_amazon_asin_sales_traffic_report",
            'test_name': 'fact dup check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_amazon_asin_sales_traffic_report", 
                field_list=['item_id','parent_item_id','snapshot_date','seller_id','country_code'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )
    
    run_record_processed_check = PythonOperator(
        task_id="task_run_record_processed_check",
        python_callable=tldq.run_dq_sql_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.stg_fact_amazon_asin_sales_traffic_report",
            'test_name': 'Not all asins processed in fact table',
            'sql_file': "sales_traffic_report/amazon/run_record_processed_check.sql"
            },
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_sales_vs_order_check = PythonOperator(
        task_id="run_dq_sales_vs_order_check",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'traffic_vs_orders_checks',
            'tb_name': "$curated_db.fact_all_item_sales_traffic_report",
            'query_file': "sales_traffic_report/amazon/amazon_sales_traffic_dq.yaml"
        },
        on_failure_callback=alerts.send_failure_alert
    )

    run_audit=PythonOperator(
        task_id="task_run_audit",
        python_callable=tla.run_audit,
        op_kwargs={
                    "table_name": "$curated_db.fact_all_item_sales_traffic_report", 
                    "wf_params": WF_PARAMS_EXPR,
                    "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                    "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC' 
                  },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    chain(
        begin,
        get_workflow_params,
        [list_s3_modified_files, list_s3_modified_files_goessor],
        transfer_s3_to_snowflake,
        transfer_s3_to_snowflake_goessor,
        insert_raw_child_asin_log,
        merge_stg_child_asin,
        run_uniqueness_check_staging,
        create_stg_fact_amazon_asin_sales_traffic_report,
        run_uniqueness_check_fact,
        merge_fact_all_item_sales_traffic_report,
        update_fact_amazon_sales_traffic_report,
        run_record_processed_check,
        run_dq_sales_vs_order_check,
        run_audit,
        update_workflow_params,
        end,
    )

from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging
from tasklib import alerts
import tasklib.sql as tlsql
import tasklib.dq as tldq

log = logging.getLogger(__name__)

BUILD_NUM = '1'
RELEASE_DEF = '1'
DAG_ID = 'sqp_refined_competitor_list'
WF_PARAMS_EXPR = "{'wf_name': '" + DAG_ID.upper() + "'}"

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2023, 5, 1),
    schedule_interval='@daily',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'ayush'},
    tags=['Ayush']
) as dag:

    create_stg_sqp_refined_competitor_list = PythonOperator(
        task_id="create_stg_sqp_refined_competitor_list",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "sqp_refined_competitor_list/create_stg_sqp_refined_competitor_list.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_stg_sqp_refined_competitor_list = PythonOperator(
        task_id="run_dq_stg_sqp_refined_competitor_list",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'check_duplicates_stg_sqp_refined_competitor_list',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_sqp_refined_competitor_list",
                field_list=[
                    'heyday_asin', 'seller_id', 'marketplace_id', 'country_code', 'snapshot_week', 'competitor_asin',
                    'asin_query', 'query_rank'
                ],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    delete_insert_sqp_refined_competitor_list = PythonOperator(
        task_id="delete_insert_sqp_refined_competitor_list",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "sqp_refined_competitor_list/delete_insert_sqp_refined_competitor_list.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_sqp_refined_competitor_list = PythonOperator(
        task_id="run_dq_sqp_refined_competitor_list",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'check_duplicates_sqp_refined_competitor_list',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$curated_db.sqp_refined_competitor_list",
                field_list=['heyday_asin', 'country_code', 'snapshot_week', 'competitor_asin'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    chain(
        begin,
        create_stg_sqp_refined_competitor_list,
        run_dq_stg_sqp_refined_competitor_list,
        delete_insert_sqp_refined_competitor_list,
        run_dq_sqp_refined_competitor_list,
        end
    )

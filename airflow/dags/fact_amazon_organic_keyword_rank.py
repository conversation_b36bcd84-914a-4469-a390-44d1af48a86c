from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.utils.task_group import TaskGroup
from airflow.models import Variable
import logging
from tasklib import alerts
import tasklib.sql as tlsql
import tasklib.dq as tldq

log = logging.getLogger(__name__)

BUILD_NUM = '1'
RELEASE_DEF = '1'
DAG_ID = 'fact_amazon_organic_keyword_rank'
WF_PARAMS_EXPR = "{'wf_name': '" + DAG_ID.upper() + "'}"

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2023, 5, 1),
    schedule_interval=Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily'),
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'nagesh'},
    tags=['Nagesh']
) as dag:
    with TaskGroup(group_id='create_stg_amazon_okr') as create_stg_amazon_okr:
        create_stg_amazon_organic_keyword_rank = PythonOperator(
            task_id="create_stg_amazon_organic_keyword_rank",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "fact_amazon_organic_keyword_rank/create_stg_amazon_organic_keyword_rank.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_unique_stg_amazon_organic_keyword_rank = PythonOperator(
            task_id="run_dq_is_unique_stg_amazon_organic_keyword_rank",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'check_duplicates_stg_amazon_organic_keyword_rank',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.stg_amazon_organic_keyword_rank",
                    field_list=['keyword', 'asin', 'report_fetched_and_loaded_at_pst', 'is_sponsored', 'country_code'],
                    hard_alert=True
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_null_stg_amazon_organic_keyword_rank = PythonOperator(
            task_id="run_dq_is_null_stg_amazon_organic_keyword_rank",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.stg_amazon_organic_keyword_rank',
                    field_list=['keyword', 'asin', 'report_fetched_and_loaded_at_pst'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        create_stg_amazon_organic_keyword_rank_extended = PythonOperator(
            task_id="create_stg_amazon_organic_keyword_rank_extended",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "fact_amazon_organic_keyword_rank/create_stg_amazon_organic_keyword_rank_extended.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        chain(
            create_stg_amazon_organic_keyword_rank,
            [run_dq_is_unique_stg_amazon_organic_keyword_rank, run_dq_is_null_stg_amazon_organic_keyword_rank],
            create_stg_amazon_organic_keyword_rank_extended
        )


    with TaskGroup(group_id='merge_fact_amazon_okr') as merge_fact_amazon_okr:
        delete_insert_fact_amazon_organic_keyword_rank = PythonOperator(
            task_id="delete_insert_fact_amazon_organic_keyword_rank",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "fact_amazon_organic_keyword_rank/delete_insert_fact_amazon_organic_keyword_rank.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_fact_amazon_organic_keyword_rank = PythonOperator(
            task_id="run_dq_fact_amazon_organic_keyword_rank",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'check_duplicates_fact_amazon_organic_keyword_rank',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$curated_db.fact_amazon_organic_keyword_rank",
                    field_list=['keyword', 'asin', 'page_scrape_time_pst', 'country_code'],
                    hard_alert=True
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_null_fact_amazon_organic_keyword_rank = PythonOperator(
            task_id="run_dq_is_null_fact_amazon_organic_keyword_rank",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$curated_db.fact_amazon_organic_keyword_rank',
                    field_list=['keyword', 'asin', 'page_scrape_time_pst'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        chain(
            delete_insert_fact_amazon_organic_keyword_rank,
            [run_dq_fact_amazon_organic_keyword_rank, run_dq_is_null_fact_amazon_organic_keyword_rank],
        )


    with TaskGroup(group_id='create_fact_amazon_weighted_okr') as create_fact_amazon_weighted_okr:
        create_stg_amazon_weighted_okr = PythonOperator(
            task_id="create_stg_amazon_weighted_okr",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "fact_amazon_organic_keyword_rank/create_stg_amazon_weighted_okr.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        delete_insert_fact_amazon_weighted_okr = PythonOperator(
            task_id="delete_insert_fact_amazon_weighted_okr",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "fact_amazon_organic_keyword_rank/delete_insert_fact_amazon_weighted_okr.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        chain (
            create_stg_amazon_weighted_okr,
            delete_insert_fact_amazon_weighted_okr
        )


    with TaskGroup(group_id='create_fact_amazon_weighted_okr_parent_asin') as create_fact_amazon_weighted_okr_parent_asin:
        create_stg_amazon_weighted_okr_parent_asin = PythonOperator(
            task_id="create_stg_amazon_weighted_okr_parent_asin",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "fact_amazon_organic_keyword_rank/create_stg_amazon_weighted_okr_parent_asin.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        delete_insert_fact_amazon_weighted_okr_parent_asin = PythonOperator(
            task_id="delete_insert_fact_amazon_weighted_okr_parent_asin",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "fact_amazon_organic_keyword_rank/delete_insert_fact_amazon_weighted_okr_parent_asin.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        chain(
            create_stg_amazon_weighted_okr_parent_asin,
            delete_insert_fact_amazon_weighted_okr_parent_asin
        )


    with TaskGroup(group_id='merge_fact_amazon_okr_sponsored') as merge_fact_amazon_okr_sponsored:
        delete_insert_fact_amazon_sponsored_keyword_rank = PythonOperator(
            task_id="delete_insert_fact_amazon_sponsored_keyword_rank",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "fact_amazon_organic_keyword_rank/delete_insert_fact_amazon_sponsored_keyword_rank.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_fact_amazon_sponsored_keyword_rank = PythonOperator(
            task_id="run_dq_fact_amazon_organic_keyword_rank",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'check_duplicates_fact_amazon_sponsored_keyword_rank',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$curated_db.fact_amazon_sponsored_keyword_rank",
                    field_list=['keyword', 'asin', 'page_scrape_time_pst', 'country_code'],
                    hard_alert=True
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_null_fact_amazon_sponsored_keyword_rank = PythonOperator(
            task_id="run_dq_is_null_fact_amazon_sponsored_keyword_rank",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$curated_db.fact_amazon_sponsored_keyword_rank',
                    field_list=['keyword', 'asin', 'page_scrape_time_pst'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        chain(
            delete_insert_fact_amazon_sponsored_keyword_rank,
            [run_dq_fact_amazon_sponsored_keyword_rank, run_dq_is_null_fact_amazon_sponsored_keyword_rank]
        )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    chain(
        begin,
        create_stg_amazon_okr,
        [merge_fact_amazon_okr, merge_fact_amazon_okr_sponsored],
    )

    chain(
        merge_fact_amazon_okr,
        [create_fact_amazon_weighted_okr, create_fact_amazon_weighted_okr_parent_asin],
        end
    )

    chain(
        merge_fact_amazon_okr_sponsored,
        end
    )

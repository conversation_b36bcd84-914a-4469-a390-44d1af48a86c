from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.models import Variable
import logging
from tasklib import alerts
import tasklib.sql as tlsql
import tasklib.dq as tldq
from datetime import datetime

log = logging.getLogger(__name__)

DAG_ID = "item_mapping_essor"
WF_PARAMS_EXPR="{'wf_name': '" + DAG_ID.upper() + "'}"
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 */4 * * *')


with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2025, 1, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name':DAG_ID.upper()
            ,'author':'<PERSON>yush <PERSON>an'},
    tags=['Ayush']
) as dag:

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    create_stg_item_mapping_history = PythonOperator(
        task_id="task_create_stg_item_mapping_history",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/create_stg_item_mapping_history.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    merge_item_mapping_history = PythonOperator(
        task_id="task_merge_item_mapping_history",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/merge_item_mapping_history.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    delete_insert_item_mapping = PythonOperator(
        task_id="task_delete_insert_item_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/delete_insert_item_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_sku_item_mapping = PythonOperator(
        task_id="task_create_stg_sku_item_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/create_stg_sku_item_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    # dq checks 
    run_unique_check_sku_item_mapping = PythonOperator(
        task_id="task_run_run_unique_check_sku_item_mapping",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'sku_item_mapping duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_sku_item_mapping",
                field_list=['brand_code', 'sku', 'country_code'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    delete_insert_sku_item_mapping = PythonOperator(
        task_id="task_delete_insert_sku_item_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/delete_insert_sku_item_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_seller_sku_item_mapping = PythonOperator(
        task_id="task_create_stg_seller_sku_item_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/create_stg_seller_sku_item_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    # dq checks 
    run_unique_check_seller_sku_item_mapping = PythonOperator(
        task_id="task_run_run_unique_check_seller_sku_item_mapping",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'seller_sku_item_mapping duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_seller_sku_item_mapping",
                field_list=['seller_id', 'sku', 'country_code'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    merge_seller_sku_item_mapping = PythonOperator(
        task_id="task_merge_seller_sku_item_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/merge_seller_sku_item_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_asin_item_mapping = PythonOperator(
        task_id="task_create_stg_asin_item_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/create_stg_asin_item_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    # dq checks 
    run_unique_check_asin_item_mapping = PythonOperator(
        task_id="task_run_run_unique_check_asin_item_mapping",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'asin_item_mapping duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_asin_item_mapping",
                field_list=['brand_code', 'asin', 'country_code'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    delete_insert_asin_item_mapping = PythonOperator(
        task_id="task_delete_insert_asin_item_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/delete_insert_asin_item_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_seller_asin_item_mapping = PythonOperator(
        task_id="task_create_stg_seller_asin_item_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/create_stg_seller_asin_item_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    # dq checks 
    run_unique_check_seller_asin_item_mapping = PythonOperator(
        task_id="task_run_run_unique_check_seller_asin_item_mapping",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'seller_asin_item_mapping duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_seller_asin_item_mapping",
                field_list=['seller_id', 'asin', 'country_code'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    merge_seller_asin_item_mapping = PythonOperator(
        task_id="task_merge_seller_asin_item_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/merge_seller_asin_item_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_sku_asin_item_mapping = PythonOperator(
        task_id="task_create_stg_sku_asin_item_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/create_stg_sku_asin_item_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    # dq checks 
    run_unique_check_sku_asin_item_mapping = PythonOperator(
        task_id="task_run_run_unique_check_sku_asin_item_mapping",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'sku_asin_item_mapping duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_sku_asin_item_mapping",
                field_list=['brand_code', 'sku', 'asin', 'country_code'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    delete_insert_sku_asin_item_mapping = PythonOperator(
        task_id="task_delete_insert_sku_asin_item_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/delete_insert_sku_asin_item_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_seller_netsuite_item_mapping = PythonOperator(
        task_id="task_create_stg_seller_netsuite_item_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/create_stg_seller_netsuite_item_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    # dq checks 
    run_unique_check_seller_netsuite_item_mapping = PythonOperator(
        task_id="task_run_run_unique_check_seller_netsuite_item_mapping",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'seller_netsuite_item_mapping duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_seller_netsuite_item_mapping",
                field_list=['seller_id', 'netsuite_item_number', 'country_code'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    delete_insert_seller_netsuite_item_mapping = PythonOperator(
        task_id="task_delete_insert_seller_netsuite_item_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/delete_insert_seller_netsuite_item_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_marketplace_asin_brand_mapping = PythonOperator(
        task_id="task_create_stg_marketplace_asin_brand_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/create_stg_marketplace_asin_brand_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    # dq checks 
    run_unique_check_marketplace_asin_brand_mapping = PythonOperator(
        task_id="task_run_run_unique_check_marketplace_asin_brand_mapping",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'marketplace_asin_brand_mapping duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_marketplace_asin_brand_mapping",
                field_list=['country_code', 'asin'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    delete_insert_marketplace_asin_brand_mapping = PythonOperator(
        task_id="task_delete_insert_marketplace_asin_brand_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/delete_insert_marketplace_asin_brand_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_item_sku_asin_mapping = PythonOperator(
        task_id="task_create_stg_item_sku_asin_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/create_stg_item_sku_asin_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    # dq checks 
    run_unique_check_item_sku_asin_mapping = PythonOperator(
        task_id="task_run_run_unique_check_item_sku_asin_mapping",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'item_sku_asin_mapping duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_item_sku_asin_mapping",
                field_list=['netsuite_item_number', 'country_code'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    delete_insert_item_sku_asin_mapping = PythonOperator(
        task_id="task_delete_insert_item_sku_asin_mapping",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "item_mapping_essor/delete_insert_item_sku_asin_mapping.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    # # ops specific tables
    # create_stg_ops_sku_item_mapping = PythonOperator(
    #     task_id="task_create_stg_ops_sku_item_mapping",
    #     python_callable=tlsql.run_query_file,
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    #     op_kwargs={
    #         "connection": "Snowflake",
    #         "sql_file": "item_mapping_essor/create_stg_ops_sku_item_mapping.sql",
    #         "wf_params": WF_PARAMS_EXPR,
    #     },
    # )
    #
    # # dq checks
    # run_unique_check_ops_sku_item_mapping = PythonOperator(
    #     task_id="task_run_run_unique_check_ops_sku_item_mapping",
    #     python_callable=tldq.run_dq_string,
    #     op_kwargs={
    #         'wk_name': DAG_ID,
    #         'test_name': 'ops_sku_item_mapping duplicate check',
    #         'sql_query': tldq.gen_check_unique_key(
    #             tb_name="$stage_db.stg_ops_sku_item_mapping",
    #             field_list=['brand_code', 'sku', 'country_code'],
    #             hard_alert=True
    #         )
    #     },
    #     on_failure_callback=alerts.send_failure_alert
    # )
    #
    # delete_insert_ops_sku_item_mapping = PythonOperator(
    #     task_id="task_delete_insert_ops_sku_item_mapping",
    #     python_callable=tlsql.run_query_file,
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    #     op_kwargs={
    #         "connection": "Snowflake",
    #         "sql_file": "item_mapping_essor/delete_insert_ops_sku_item_mapping.sql",
    #         "wf_params": WF_PARAMS_EXPR,
    #     },
    # )
    #
    # create_stg_ops_asin_item_mapping = PythonOperator(
    #     task_id="task_create_stg_ops_asin_item_mapping",
    #     python_callable=tlsql.run_query_file,
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    #     op_kwargs={
    #         "connection": "Snowflake",
    #         "sql_file": "item_mapping_essor/create_stg_ops_asin_item_mapping.sql",
    #         "wf_params": WF_PARAMS_EXPR,
    #     },
    # )
    #
    # # dq checks
    # run_unique_check_ops_asin_item_mapping = PythonOperator(
    #     task_id="task_run_run_unique_check_ops_asin_item_mapping",
    #     python_callable=tldq.run_dq_string,
    #     op_kwargs={
    #         'wk_name': DAG_ID,
    #         'test_name': 'ops_asin_item_mapping duplicate check',
    #         'sql_query': tldq.gen_check_unique_key(
    #             tb_name="$stage_db.stg_ops_asin_item_mapping",
    #             field_list=['brand_code', 'asin', 'country_code'],
    #             hard_alert=True
    #         )
    #     },
    #     on_failure_callback=alerts.send_failure_alert
    # )
    #
    # delete_insert_ops_asin_item_mapping = PythonOperator(
    #     task_id="task_delete_insert_ops_asin_item_mapping",
    #     python_callable=tlsql.run_query_file,
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    #     op_kwargs={
    #         "connection": "Snowflake",
    #         "sql_file": "item_mapping_essor/delete_insert_ops_asin_item_mapping.sql",
    #         "wf_params": WF_PARAMS_EXPR,
    #     },
    # )
    #
    # create_stg_ops_item_sku_asin_mapping = PythonOperator(
    #     task_id="task_create_stg_ops_item_sku_asin_mapping",
    #     python_callable=tlsql.run_query_file,
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    #     op_kwargs={
    #         "connection": "Snowflake",
    #         "sql_file": "item_mapping_essor/create_stg_ops_item_sku_asin_mapping.sql",
    #         "wf_params": WF_PARAMS_EXPR,
    #     },
    # )
    #
    # # dq checks
    # run_unique_check_ops_item_sku_asin_mapping = PythonOperator(
    #     task_id="task_run_run_unique_check_ops_item_sku_asin_mapping",
    #     python_callable=tldq.run_dq_string,
    #     op_kwargs={
    #         'wk_name': DAG_ID,
    #         'test_name': 'ops_item_sku_asin_mapping duplicate check',
    #         'sql_query': tldq.gen_check_unique_key(
    #             tb_name="$stage_db.stg_ops_item_sku_asin_mapping",
    #             field_list=['netsuite_item_number', 'country_code'],
    #             hard_alert=True
    #         )
    #     },
    #     on_failure_callback=alerts.send_failure_alert
    # )
    #
    # delete_insert_ops_item_sku_asin_mapping = PythonOperator(
    #     task_id="task_delete_insert_ops_item_sku_asin_mapping",
    #     python_callable=tlsql.run_query_file,
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    #     op_kwargs={
    #         "connection": "Snowflake",
    #         "sql_file": "item_mapping_essor/delete_insert_ops_item_sku_asin_mapping.sql",
    #         "wf_params": WF_PARAMS_EXPR,
    #     },
    # )
    
    stg_derived_tables = [
        create_stg_sku_item_mapping,
        create_stg_seller_sku_item_mapping,
        create_stg_asin_item_mapping,
        create_stg_seller_asin_item_mapping,
        create_stg_sku_asin_item_mapping,
        create_stg_seller_netsuite_item_mapping,
        create_stg_marketplace_asin_brand_mapping,
        create_stg_item_sku_asin_mapping
        # create_stg_ops_sku_item_mapping,
        # create_stg_ops_asin_item_mapping,
        # create_stg_ops_item_sku_asin_mapping
    ]
    
    dq_derived_tables = [
        run_unique_check_sku_item_mapping,
        run_unique_check_seller_sku_item_mapping,
        run_unique_check_asin_item_mapping,
        run_unique_check_seller_asin_item_mapping,
        run_unique_check_sku_asin_item_mapping,
        run_unique_check_seller_netsuite_item_mapping,
        run_unique_check_marketplace_asin_brand_mapping,
        run_unique_check_item_sku_asin_mapping
        # run_unique_check_ops_sku_item_mapping,
        # run_unique_check_ops_asin_item_mapping,
        # run_unique_check_ops_item_sku_asin_mapping
    ]
    
    merge_derived_tables = [
        delete_insert_sku_item_mapping,
        merge_seller_sku_item_mapping,
        delete_insert_asin_item_mapping,
        merge_seller_asin_item_mapping,
        delete_insert_sku_asin_item_mapping,
        delete_insert_seller_netsuite_item_mapping,
        delete_insert_marketplace_asin_brand_mapping,
        delete_insert_item_sku_asin_mapping
        # delete_insert_ops_sku_item_mapping,
        # delete_insert_ops_asin_item_mapping,
        # delete_insert_ops_item_sku_asin_mapping
    ]

    chain (
        begin,
        create_stg_item_mapping_history,
        merge_item_mapping_history,
        delete_insert_item_mapping,
        stg_derived_tables,
        dq_derived_tables,
        merge_derived_tables,
        end
    )

"""
Code generator for airflow ingestion dags.
"""

import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain

# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = "1"
DAG_ID = "fact_sale_tax_reports"
DAG_SCHEDULE = Variable.get("dag_schedules", deserialize_json=True).get(
    DAG_ID, "@daily"
)
RELEASE_DEF = "1"
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f"Release:{RELEASE_DEF}-Build:{BUILD_NUM}",
    start_date=datetime(2025, 1, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={"workflow_name": "SCDR_SALE_TAX_REPORTS", "author": "huynh"},
    tags=["huynh", "Raptor"],
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    begin = DummyOperator(
        task_id="begin", depends_on_past=True, wait_for_downstream=True
    )
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load scdr_sale_tax_reports ---

    with TaskGroup(
        group_id="load_scdr_sale_tax_reports"
    ) as tg_scdr_sale_tax_reports:
        list_s3_files_scdr_sale_tax_reports_task = PythonOperator(
            task_id="list_s3_files_scdr_sale_tax_reports",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "seller_central_scraper/sale_tax_reports/list_s3_scdr_sale_tax_reports.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # Need a begin dummy operator for branching
        begin_insert_scdr_sale_tax_reports_task = DummyOperator(
            task_id="begin_insert_scdr_sale_tax_reports"
        )
        skip_insert_scdr_sale_tax_reports_task = DummyOperator(
            task_id="skip_insert_scdr_sale_tax_reports"
        )
        end_insert_scdr_sale_tax_reports_task = DummyOperator(
            task_id="end_insert_scdr_sale_tax_reports"
        )
        end_scdr_sale_tax_reports_task = DummyOperator(
            task_id="end_scdr_sale_tax_reports",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_scdr_sale_tax_reports_task = BranchPythonOperator(
            task_id="check_new_files_found_scdr_sale_tax_reports",
            python_callable=check_for_new_files,
            op_kwargs={
                "args_file": "seller_central_scraper/sale_tax_reports/s3_to_snowflake_scdr_sale_tax_reports.yaml",
                "skip_task_id": "load_scdr_sale_tax_reports.skip_insert_scdr_sale_tax_reports",
                "next_task_id": "load_scdr_sale_tax_reports.begin_insert_scdr_sale_tax_reports",
            },
        )

        s3_to_snowflake_scdr_sale_tax_reports_task = PythonOperator(
            task_id="s3_to_snowflake_scdr_sale_tax_reports",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "seller_central_scraper/sale_tax_reports/s3_to_sf_raw_scdr_sale_tax_reports.yaml"
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_scdr_sale_tax_reports_task = PythonOperator(
            task_id="insert_log_scdr_sale_tax_reports",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "seller_central_scraper/sale_tax_reports/insert_log_scdr_sale_tax_reports.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_scdr_sale_tax_reports_task = PythonOperator(
            task_id="dedupe_scdr_sale_tax_reports",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "seller_central_scraper/sale_tax_reports/dedupe_scdr_sale_tax_reports.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # Update DQ checks based on the new PK
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.dedupe_scdr_sale_tax_reports",
                "test_name": "is_null",
                "sql_query": tldq.gen_check_if_nulls(
                    tb_name="$stage_db.dedupe_scdr_sale_tax_reports",
                    field_list=["pk"],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.dedupe_scdr_sale_tax_reports",
                "test_name": "is_unique",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.dedupe_scdr_sale_tax_reports",
                    field_list=["pk"],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_scdr_sale_tax_reports_task = PythonOperator(
            task_id="merge_stage_scdr_sale_tax_reports",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "seller_central_scraper/sale_tax_reports/merge_stage_scdr_sale_tax_reports.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # Update DQ checks based on the new PK
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.merge_scdr_sale_tax_reports",
                "test_name": "is_null",
                "sql_query": tldq.gen_check_if_nulls(
                    tb_name="$stage_db.merge_scdr_sale_tax_reports",
                    field_list=["pk"],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.merge_scdr_sale_tax_reports",
                "test_name": "is_unique",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.merge_scdr_sale_tax_reports",
                    field_list=["pk"],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_fact_scdr_sale_tax_reports_task = PythonOperator(
            task_id="merge_fact_scdr_sale_tax_reports",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "seller_central_scraper/sale_tax_reports/merge_fact_scdr_sale_tax_reports.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_scdr_sale_tax_reports_task = PythonOperator(
            task_id="run_audit_scdr_sale_tax_reports",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_scdr_sale_tax_reports",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": "record_created_timestamp_utc",
                "ts_updated_field": "record_updated_timestamp_utc",
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_scdr_sale_tax_reports_task
            >> check_new_files_found_scdr_sale_tax_reports_task
            >> [
                begin_insert_scdr_sale_tax_reports_task,
                skip_insert_scdr_sale_tax_reports_task,
            ]
        )

        (
            begin_insert_scdr_sale_tax_reports_task
            >> s3_to_snowflake_scdr_sale_tax_reports_task
            >> insert_log_scdr_sale_tax_reports_task
            >> dedupe_scdr_sale_tax_reports_task
            >> (
                run_dq_is_null_dedupe_pk_hard_task,
                run_dq_is_unique_dedupe_pk_hard_task,
            )
            >> merge_stage_scdr_sale_tax_reports_task
            >> (run_dq_is_null_merge_pk_hard_task, run_dq_is_unique_merge_pk_hard_task)
            >> merge_fact_scdr_sale_tax_reports_task
            >> run_audit_scdr_sale_tax_reports_task
            >> end_insert_scdr_sale_tax_reports_task
            >> end_scdr_sale_tax_reports_task
        )

        skip_insert_scdr_sale_tax_reports_task >> end_scdr_sale_tax_reports_task

    # ---- Main branch ----
    chain(
        begin,
        get_workflow_parameters,
        [tg_scdr_sale_tax_reports],
        update_workflow_parameters,
        end,
    )
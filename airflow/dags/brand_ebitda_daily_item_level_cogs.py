from airflow import DAG
from datetime import datetime, timedelta
from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator
from airflow.models import Variable
from airflow.models.baseoperator import chain
from airflow.utils.task_group import TaskGroup

import logging
from tasklib import alerts
import tasklib.workflow as tlw
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.dq as tldq
from tasklib.loader import S3ToSnowflake

load_obj = S3ToSnowflake()
log = logging.getLogger(__name__)

DAG_ID = 'brand_ebitda_daily_item_level_cogs'

default_args = {
    'depends_on_past': False,
    'start_date': datetime(2025, 6, 1),
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

with DAG(
    dag_id=DAG_ID,
    default_args=default_args,
    schedule_interval='0 5 * * 1,2,6',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'leav'},
    tags=['leav']
) as dag:

    begin = EmptyOperator(task_id="begin")
    end = EmptyOperator(task_id="end")

    # Define the task group for creating the staging table
    with TaskGroup(group_id='create_stg_brand_ebitda_daily_item_level_cogs') as create_stg_brand_ebitda_daily_item_level_cogs_group:
        create_stg_brand_ebitda_daily_item_level_cogs_task = PythonOperator(
            task_id="task_create_stg_brand_ebitda_daily_item_level_cogs",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "brand_ebitda_framework/cogs/create_stg_brand_ebitda_daily_item_level_cogs.sql",
                       "wf_params": "{}"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Run data quality tests on the staging table to make sure that there are no dupes
        run_dq_is_unique_create_stg_brand_ebitda_daily_item_level_cogs_task = PythonOperator(
            task_id="task_run_dq_is_unique_create_stg_brand_ebitda_daily_item_level_cogs",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'stg brand ebitda daily item level cogs duplicate check',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.create_stg_brand_ebitda_daily_item_level_cogs",
                    field_list=['pk'],
                    hard_alert=True
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        # Create an incremental model for staging table
        merge_stg_brand_ebitda_daily_item_level_cogs_task = PythonOperator(
            task_id="merge_stg_brand_ebitda_daily_item_level_cogs_task",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "brand_ebitda_framework/cogs/merge_stg_brand_ebitda_daily_item_level_cogs.sql",
                       "wf_params": "{}"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_unique_merge_stg_brand_ebitda_daily_item_level_cogs_task = PythonOperator(
            task_id="task_run_dq_unique_test_merge_stg_brand_ebitda_daily_item_level_cogs",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'duplicate check',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.merge_stg_brand_ebitda_daily_item_level_cogs",
                    field_list=['pk'],
                    hard_alert=True
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        # Define the dependency between the tasks within the group
        create_stg_brand_ebitda_daily_item_level_cogs_task >> run_dq_is_unique_create_stg_brand_ebitda_daily_item_level_cogs_task
        run_dq_is_unique_create_stg_brand_ebitda_daily_item_level_cogs_task >> merge_stg_brand_ebitda_daily_item_level_cogs_task
        merge_stg_brand_ebitda_daily_item_level_cogs_task >> run_dq_is_unique_merge_stg_brand_ebitda_daily_item_level_cogs_task

    # Define the merge task for the final table
    merge_brand_ebitda_daily_item_level_cogs_task = PythonOperator(
        task_id="task_merge_brand_ebitda_daily_item_level_cogs",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection": "Snowflake",
                   "sql_file": "brand_ebitda_framework/cogs/merge_brand_ebitda_daily_item_level_cogs.sql",
                   "wf_params": "{}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    # Run data quality tests on the merging table to make sure that there are no dupes
    run_dq_tests_brand_ebitda_daily_item_level_cogs_task = PythonOperator(
        task_id="task_run_dq_tests_brand_ebitda_daily_item_level_cogs",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'brand ebitda daily item level cogs duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$curated_db.brand_ebitda_daily_item_level_cogs",
                field_list=['pk'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    # Define dependencies
    create_stg_brand_ebitda_daily_item_level_cogs_group >> merge_brand_ebitda_daily_item_level_cogs_task
    merge_brand_ebitda_daily_item_level_cogs_task >> run_dq_tests_brand_ebitda_daily_item_level_cogs_task

    # Assemble the DAG
    chain(
        begin,
        create_stg_brand_ebitda_daily_item_level_cogs_group,
        merge_brand_ebitda_daily_item_level_cogs_task,
        run_dq_tests_brand_ebitda_daily_item_level_cogs_task,
        end
    )

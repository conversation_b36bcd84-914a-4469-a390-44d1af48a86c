import logging
from airflow import DAG
from datetime import datetime
from airflow.operators.python import Python<PERSON>perator
from airflow.operators.dummy_operator import DummyOperator

from tasklib.sql import run_query_file

log = logging.getLogger(__name__)

_WF_PARAMS_EXPR = '{}'

with DAG(
    dag_id='fountain9_mapping_table',
    start_date=datetime(2022, 9, 1),
    schedule_interval='@daily',
    catchup=False,
    max_active_runs=1,
    params={
        'workflow_name': 'FOUNTAIN9_MAPPING_TABLE',
        'author': 'harshad'
    },
    tags=['RETIRED','Harshad'],
) as dag:

    begin = DummyOperator(
        task_id='begin',
    )

    end = DummyOperator(
        task_id='end',
    )

    create_mapping_table_task = PythonOperator(
        task_id='create_mapping_table',
        python_callable=run_query_file,
        op_kwargs={
            'connection': 'Snowflake',
            'sql_file': 'fountain9/create_mapping_table.sql',
            'wf_params': _WF_PARAMS_EXPR,
        },
        provide_context=True,
    )

    update_mapping_table_task = PythonOperator(
        task_id='update_mapping_table',
        python_callable=run_query_file,
        op_kwargs={
            'connection': 'Snowflake',
            'sql_file': 'fountain9/update_mapping_table.sql',
            'wf_params': _WF_PARAMS_EXPR,
        },
        provide_context=True,
    )

    begin >> create_mapping_table_task >> update_mapping_table_task >> end

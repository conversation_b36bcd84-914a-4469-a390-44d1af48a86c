from airflow import D<PERSON>
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models import Variable
from airflow.utils.task_group import TaskGroup

import logging
from tasklib import alerts
import tasklib.workflow as tlw
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.dq as tldq
from tasklib.loader import S3ToSnowflake

load_obj = S3ToSnowflake()
log = logging.getLogger(__name__)

DAG_ID = 'amazon_deferred_transactions'
WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2025,1,1),
    schedule_interval=Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 */2 * * *'),
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': "<PERSON>yu<PERSON>"},
    tags=['Ayush']
) as dag:

    get_workflow_params=PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )

    list_s3_modified_files_settlement_transactions=PythonOperator(
        task_id="task_list_s3_modified_files_deferred_transactions",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/deferred_transactions_s3_list_folders.yaml",
                   "wf_params": WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake_settlement_transactions=PythonOperator(
        task_id="task_transfer_s3_to_snowflake_settlement_transactions",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": DAG_ID + "/s3_to_sf_amazon_deferred_transactions.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_log_raw_amazon_deferred_transactions = PythonOperator(
        task_id="task_create_log_raw_amazon_deferred_transactions",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection": "Snowflake",
                   "sql_file": DAG_ID + "/log_raw_amazon_deferred_transactions.sql",
                   "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    with TaskGroup(group_id='create_stg_deferred_transactions') as create_stg_deferred_transactions:
        create_stg_deferred_transactions_dedup=PythonOperator(
            task_id="task_create_stg_deferred_transactions_dedup",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": DAG_ID + "/create_stg_deferred_transactions_dedup.sql",
                       "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_tests_stg_deferred_transactions = PythonOperator(
            task_id="task_run_dq_tests_stg_deferred_transactions",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'amazon deferred transactions duplicate check',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.amazon_deferred_transactions",
                    field_list=['deferred_transaction_key', 'report_requested_date'],
                    hard_alert=True
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        create_stg_deferred_transactions_dedup >> run_dq_tests_stg_deferred_transactions


    merge_fact_amazon_deferred_transactions = PythonOperator(
        task_id="task_merge_fact_amazon_deferred_transactions",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection": "Snowflake",
                   "sql_file": DAG_ID + "/merge_fact_amazon_deferred_transactions.sql",
                   "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_fact_amazon_deferred_transactions = PythonOperator(
        task_id="task_run_dq_tests_fact_amazon_deferred_transactions",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'fact amazon deferred transactions duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$curated_db.fact_amazon_deferred_transactions",
                field_list=['deferred_transaction_key', 'report_requested_date'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    #workflow update
    update_workflow_params=PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )


    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    #Assemble the dag
    begin >> get_workflow_params >> list_s3_modified_files_settlement_transactions \
    >> transfer_s3_to_snowflake_settlement_transactions >> create_log_raw_amazon_deferred_transactions \
    >> create_stg_deferred_transactions \
    >> merge_fact_amazon_deferred_transactions \
    >> run_dq_tests_fact_amazon_deferred_transactions >> update_workflow_params >> end

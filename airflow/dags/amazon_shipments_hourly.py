from airflow import DAG
from datetime import datetime
from airflow.utils.task_group import TaskGroup
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.sensors.external_task import ExternalTaskSensor
from airflow.models.baseoperator import chain
import logging

log = logging.getLogger(__name__)

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id="amazon_shipments_hourly",
    start_date=datetime(2022, 5, 8),
    schedule_interval="0 */4 * * *",
    catchup=False,
    max_active_runs=1,
    params={
        "workflow_name": "AMAZON_SHIPMENTS_HOURLY",
        "author": "ruchira"
        },
    tags=['Goessor']
) as dag:
    import tasklib.workflow as tlw
    import tasklib.s3 as tls3
    import tasklib.glue as tlg
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts
    from tasklib.loader import S3ToSnowflake
    load_obj = S3ToSnowflake()

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    with TaskGroup(
        group_id="fba_amazon_fulfilled_shipments"
    ) as fba_amazon_fulfilled_shipments:

        begin_ingestion_fba_amazon_fulfilled_shipments = DummyOperator(
            task_id="begin_ingestion_fba_amazon_fulfilled_shipments"
        )
        end_ingestion_fba_amazon_fulfilled_shipments = DummyOperator(
            task_id="end_ingestion_fba_amazon_fulfilled_shipments"
        )

        list_s3_modified_files = PythonOperator(
            task_id="task_list_s3_modified_files",
            python_callable=tls3.list_s3_modified_files,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "args_file": "fba_amazon_fulfilled_shipments_hourly/s3_list_folders.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        transfer_s3_to_snowflake = PythonOperator(
            task_id="task_transfer_s3_to_snowflake",
            python_callable=load_obj.s3_to_snowflake_load,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "args_file": "fba_amazon_fulfilled_shipments_hourly/s3_to_sf_raw_amazon_fba_amazon_fulfilled_shipments.yaml"},
        )

        list_s3_modified_files_goessor = PythonOperator(
            task_id="task_list_s3_modified_files_goessor",
            python_callable=tls3.list_s3_modified_files,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "args_file": "fba_amazon_fulfilled_shipments_hourly/s3_list_folders_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        transfer_s3_to_snowflake_goessor = PythonOperator(
            task_id="task_transfer_s3_to_snowflake_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "args_file": "fba_amazon_fulfilled_shipments_hourly/s3_to_sf_raw_amazon_fba_amazon_fulfilled_shipments_goessor.yaml"},
        )

        transform_raw_fba_amazon_fulfilled_shipments = PythonOperator(
            task_id="transform_raw_fba_amazon_fulfilled_shipments",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "shipments/amazon/transform_raw_fba_amazon_fulfilled_shipments.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        dedup_fba_amazon_fulfilled_shipments = PythonOperator(
            task_id="dedup_fba_amazon_fulfilled_shipments",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "shipments/amazon/dedup_fba_amazon_fulfilled_shipments.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        stage_fba_amazon_fulfilled_shipments = PythonOperator(
            task_id="stage_fba_amazon_fulfilled_shipments",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "shipments/amazon/stage_fba_amazon_fulfilled_shipments.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        merge_fba_amazon_fulfilled_shipments = PythonOperator(
            task_id="merge_fba_amazon_fulfilled_shipments",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "shipments/amazon/merge_fba_amazon_fulfilled_shipments.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_tests_fba_amazon_fulfilled_shipments = PythonOperator(
            task_id="run_dq_tests_fba_amazon_fulfilled_shipments",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "b15434ce-6d00-4a84-b20c-5f3cb78cfafb"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            begin_ingestion_fba_amazon_fulfilled_shipments
            >> [list_s3_modified_files, list_s3_modified_files_goessor]
            >> transfer_s3_to_snowflake
            >> transfer_s3_to_snowflake_goessor
            >> transform_raw_fba_amazon_fulfilled_shipments
            >> dedup_fba_amazon_fulfilled_shipments
            >> stage_fba_amazon_fulfilled_shipments
            >> merge_fba_amazon_fulfilled_shipments
            >> run_dq_tests_fba_amazon_fulfilled_shipments
            >> end_ingestion_fba_amazon_fulfilled_shipments
        )

    with TaskGroup(group_id="amazon_fact_all_shipments") as amazon_fact_all_shipments:
        begin_amazon_fact_all_shipments = DummyOperator(
            task_id="begin_amazon_fact_all_shipments"
        )
        end_amazon_fact_all_shipments = DummyOperator(
            task_id="end_amazon_fact_all_shipments"
        )

        create_join_shipments_orders = PythonOperator(
            task_id="create_join_shipments_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "shipments/amazon/create_join_shipments_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_park_amazon_shipments_with_no_orders = PythonOperator(
            task_id="create_park_amazon_shipments_with_no_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "shipments/amazon/create_park_amazon_shipments_with_no_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_tests_amazon_shipments_with_no_orders = PythonOperator(
            task_id="run_dq_tests_amazon_shipments_with_no_orders",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "f124b1c3-f373-4f9b-a2fa-8041c023776d"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        stage_amazon_fact_all_shipments = PythonOperator(
            task_id="stage_amazon_fact_all_shipments",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "shipments/amazon/stage_amazon_fact_all_shipments.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        merge_fact_all_shipments = PythonOperator(
            task_id="merge_fact_all_shipments",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "shipments/amazon/merge_fact_all_shipments.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_tests_fact_all_shipments = PythonOperator(
            task_id="run_dq_tests_fact_all_shipments",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "808f1faa-6b4e-4f52-a247-4cc856b0553c"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        update_updated_orders = PythonOperator(
            task_id="update_updated_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "shipments/amazon/update_updated_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_audit_fact_all_shipments = PythonOperator(
            task_id="run_audit_fact_all_shipments",
            python_callable=tla.run_audit,
            op_kwargs={"table_name": "$curated_db.fact_all_shipments",
                       "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            begin_amazon_fact_all_shipments
            >> create_join_shipments_orders
            >> create_park_amazon_shipments_with_no_orders
            >> run_dq_tests_amazon_shipments_with_no_orders
            >> stage_amazon_fact_all_shipments
            >> merge_fact_all_shipments
            >> update_updated_orders
            >> run_dq_tests_fact_all_shipments
            >> run_audit_fact_all_shipments
            >> end_amazon_fact_all_shipments
        )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
    )

    wait_on_amazon_orders = ExternalTaskSensor(task_id="wait_on_amazon_orders",
                               external_dag_id="amazon_orders",
                               external_task_id="end",
                               allowed_states=["success"],
                               poke_interval=60 * 3,
                               mode="reschedule",
                               timeout=60 * 60 * 4,
                               on_failure_callback=alerts.send_failure_alert
                               )
    end = DummyOperator(task_id="end")

    chain(
        get_workflow_params,
        wait_on_amazon_orders,
        fba_amazon_fulfilled_shipments,
        amazon_fact_all_shipments,
        update_workflow_params,
        end,
    )

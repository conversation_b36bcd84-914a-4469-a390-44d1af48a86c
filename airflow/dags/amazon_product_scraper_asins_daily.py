import json
import logging
from airflow import DAG
from airflow.operators.dummy_operator import Dummy<PERSON>perator
from airflow.operators.python import PythonOperator
from tasklib.config import get_env
from airflow.providers.http.operators.http import SimpleHttpOperator
from datetime import datetime

from tasklib.alerts import send_failure_alert
from tasklib.s3 import write_snowflake_data_to_s3

log = logging.getLogger(__name__)

_DELIM = ","
_S3_BUCKET = "hydy-amazon-scraper"
_S3_KEY = get_env() + "/input_data/asins_{{ ds }}.csv"
API_ENDPOINT_CONN_ID = "amazon_product_scraper_api_endpoint"

with DAG(
    dag_id="amazon_product_scraper_asins_daily",
    start_date=datetime(2022, 1, 1),
    schedule_interval="0 7 * * *",
    catchup=False,
    max_active_runs=1,
    params={"workflow_name": "AMAZON_PRODUCT_SCRAPER_ASINS_DAILY", "author": "duy"},
    tags=["Duy"],
) as dag:

    begin = DummyOperator(task_id="begin")

    write_snowflake_data_to_s3_task = PythonOperator(
        task_id="write_data_to_s3",
        python_callable=write_snowflake_data_to_s3,
        provide_context=True,
        op_kwargs={
            "sql_file": "amazon_product_scraper/unload_asins_data.sql",
            "wf_params": json.dumps({}),
            "s3_bucket": _S3_BUCKET,
            "s3_key": _S3_KEY,
            'delim': _DELIM,
        },
        on_failure_callback=send_failure_alert,
    )

    send_s3_info_to_aps_task = SimpleHttpOperator(
        task_id="amazon_product_scraper_api_call",
        http_conn_id=API_ENDPOINT_CONN_ID,
        endpoint="/fetch-data",
        method="POST",
        data=json.dumps(
            {
                "fetch_data_type": "PRODUCT",
                "s3_bucket_name": _S3_BUCKET,
                "s3_file_path": _S3_KEY,
            }
        ),
        headers={
            "Content-Type": "application/json",
            "Authorization": "Bearer dummy_jwt",
        },
        response_filter=lambda response: response.json(),
        log_response=True,
    )

    end = DummyOperator(task_id="end")

    (begin >> write_snowflake_data_to_s3_task >> send_s3_info_to_aps_task >> end)

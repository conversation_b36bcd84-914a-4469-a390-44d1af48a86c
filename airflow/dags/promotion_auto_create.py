import warnings
import uuid
import json
import logging
from datetime import datetime
from airflow import DAG
from airflow.models import Variable
from airflow.providers.http.operators.http import SimpleHttpOperator
from airflow.operators.python import PythonOperator, BranchPythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule
from tasklib import alerts
from tasklib.loader import S3ToSnowflake

load_obj = S3ToSnowflake()

log = logging.getLogger(__name__)

warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)

DAG_ID = 'promotion_auto_create'
API_ENDPOINT_CONN_ID = 'promotions_api_endpoint'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 17 * * *') # Default to hourly runs
REQUEST_ID = str(uuid.uuid4())
WF_PARAMS_EXPR_MOD = json.dumps({"request_id": f'\'{REQUEST_ID}\''})
WF_PARAMS_EXPR = "{'wf_name': '" + DAG_ID.upper() + "'}"


def check_if_data_ready(**kwargs):
    '''
    Check if the data replication (snowflake to postgres) added any new rows.
    If yes, run the next steps including insights api call
    If no, skip the api call
    '''
    from db_connectors.sf_connector import Snowflake
    ti = kwargs['ti']
    sf_client = Snowflake()
    query = f"""SELECT * FROM dwh.sandbox.promotion_create_automation LIMIT 1"""
    _, num_rows = sf_client.get_data(query)
    if num_rows > 0:
        return 'create_promotions.replicate_promotion_creation_sf_to_pg_task'
    return 'skip_create_promotions'


def check_response(response):
    try:
        if response.status_code == 204:
            return True
        else:
            return False
    except Exception as e:
        log.error(f'API ERROR - {e}')
    return False


def generate_payload():
    '''
    Generate the payload for the Insights API call. The snapshot_date is chosen to be one day prior,
    based on UTC time.
    '''
    payload = {
        "batch_id": REQUEST_ID
    }
    return json.dumps(payload)


with DAG(
        dag_id=DAG_ID,
        start_date=datetime(2024, 7, 2),
        schedule_interval=DAG_SCHEDULE,
        catchup=False,
        max_active_runs=1,
        params={"author": "nagesh", 'workflow_name': DAG_ID.upper()},
        tags=['Nagesh'],
) as dag:
    import tasklib.sql as tlsql
    import tasklib.replication as tlr

    create_stg_promotion_task = PythonOperator(
        task_id="create_stg_promotion_task",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "promotions/automation/auto_create_promo.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    check_if_data_ready_task = BranchPythonOperator(
        task_id='check_if_data_ready',
        python_callable=check_if_data_ready,
        provide_context=True,
    )

    skip_create_promotions = DummyOperator(task_id="skip_create_promotions")

    with TaskGroup(group_id="create_promotions") as create_promotion_task:
        replicate_promotion_creation_sf_to_pg_task = PythonOperator(
            task_id="replicate_promotion_creation_sf_to_pg_task",
            python_callable=tlr.replicate_data,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={"args_file": "data_replication/sf_to_pg/promotion_auto_create.yaml"}
        )

        promotion_api_call_task = SimpleHttpOperator(
            task_id="promotion_api_call",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/auto-create',
            method='PUT',
            data=generate_payload(),
            headers={"Content-Type": "application/json", "Authorization": "Basic dGVzdDp0ZXN0"},
            response_check=lambda response: check_response(response),
            log_response=True,
        )

        replicate_promotion_creation_sf_to_pg_task >> promotion_api_call_task

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    (
            begin >> create_stg_promotion_task >>
            check_if_data_ready_task >>
            [create_promotion_task, skip_create_promotions]
    )

    (
            create_promotion_task >> end
    )

    (
            skip_create_promotions >> end
    )

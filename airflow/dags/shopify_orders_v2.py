from airflow import DAG
from datetime import datetime
from airflow.utils.task_group import TaskGroup
from airflow.operators.python import PythonOperator, BranchPythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.utils.trigger_rule import TriggerRule
from tasklib import alerts
from tasklib.loader import check_for_new_files

import logging

log = logging.getLogger(__name__)

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
        dag_id="shopify_v2_orders",
        start_date=datetime(2022, 5, 8),
        schedule_interval="@hourly",
        catchup=False,
        max_active_runs=1,
        params={
                "workflow_name": "SHOPIFY_ORDERS_V2",
            "author": "vikas"
        },
) as dag:
    import tasklib.workflow as tlw
    import tasklib.s3 as tls3
    import tasklib.glue as tlg
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts
    from tasklib.loader import S3ToSnowflake

    load_obj = S3ToSnowflake()

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    update_brand_timezone_dim = PythonOperator(
        task_id="update_brand_timezone_dim",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "orders/shopify/update_brand_timezones_dim.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load fulfillment_events_shopify ---

    with TaskGroup(group_id='load_fulfillment_events_shopify') as tg_fulfillment_events_shopify:
        list_s3_files_fulfillment_events_shopify_task = PythonOperator(
            task_id="list_s3_files_fulfillment_events_shopify",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "shopify_orders_v2/list_s3_fulfillment_events_shopify.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_fulfillment_events_shopify_task = DummyOperator(
            task_id="begin_insert_fulfillment_events_shopify")
        skip_insert_fulfillment_events_shopify_task = DummyOperator(
            task_id="skip_insert_fulfillment_events_shopify")
        end_insert_fulfillment_events_shopify_task = DummyOperator(
            task_id="end_insert_fulfillment_events_shopify")
        end_fulfillment_events_shopify_task = DummyOperator(
            task_id="end_fulfillment_events_shopify",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_fulfillment_events_shopify_task = BranchPythonOperator(
            task_id='check_new_files_found_fulfillment_events_shopify',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "shopify_orders_v2/s3_to_snowflake_fulfillment_events_shopify.yaml",
                       "skip_task_id": "load_fulfillment_events_shopify.skip_insert_fulfillment_events_shopify",
                       "next_task_id": "load_fulfillment_events_shopify.begin_insert_fulfillment_events_shopify",
                       "args_file_second": "shopify_orders_v2/s3_to_snowflake_fulfillment_events_shopify_goessor.yaml",
                       },
        )

        s3_to_snowflake_fulfillment_events_shopify_task = PythonOperator(
            task_id="s3_to_snowflake_fulfillment_events_shopify",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fulfillment_events/shopify/s3_to_sf_raw_fulfillment_events_shopify.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        list_s3_files_fulfillment_events_shopify_task_goessor = PythonOperator(
            task_id="list_s3_files_fulfillment_events_shopify_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "shopify_orders_v2/list_s3_fulfillment_events_shopify_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        s3_to_snowflake_fulfillment_events_shopify_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_fulfillment_events_shopify_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fulfillment_events/shopify/s3_to_sf_raw_fulfillment_events_shopify_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_fulfillment_events_shopify_task = PythonOperator(
            task_id="insert_log_fulfillment_events_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "fulfillment_events/shopify/insert_log_fulfillment_events_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_fulfillment_events_shopify_task = PythonOperator(
            task_id="dedupe_fulfillment_events_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "fulfillment_events/shopify/dedupe_fulfillment_events_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_fulfillment_events_shopify_task = PythonOperator(
            task_id="merge_stage_fulfillment_events_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "fulfillment_events/shopify/merge_stage_fulfillment_events_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_fulfillment_events_shopify_task = PythonOperator(
            task_id="run_audit_fulfillment_events_shopify",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_fulfillment_events_shopify",
                # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                [list_s3_files_fulfillment_events_shopify_task, list_s3_files_fulfillment_events_shopify_task_goessor]>>
                check_new_files_found_fulfillment_events_shopify_task >>
                [begin_insert_fulfillment_events_shopify_task, skip_insert_fulfillment_events_shopify_task]
        )

        (
                begin_insert_fulfillment_events_shopify_task >>
                s3_to_snowflake_fulfillment_events_shopify_task >>
                s3_to_snowflake_fulfillment_events_shopify_task_goessor >>

                insert_log_fulfillment_events_shopify_task >>
                dedupe_fulfillment_events_shopify_task >>

                merge_stage_fulfillment_events_shopify_task >>

                run_audit_fulfillment_events_shopify_task >>
                end_insert_fulfillment_events_shopify_task >>
                end_fulfillment_events_shopify_task
        )

        skip_insert_fulfillment_events_shopify_task >> end_fulfillment_events_shopify_task

    #  --- Load fulfillment_orders_shopify ---

    with TaskGroup(group_id='load_fulfillment_orders_shopify') as tg_fulfillment_orders_shopify:
        list_s3_files_fulfillment_orders_shopify_task = PythonOperator(
            task_id="list_s3_files_fulfillment_orders_shopify",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "shopify_orders_v2/list_s3_fulfillment_orders_shopify.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_fulfillment_orders_shopify_task = DummyOperator(
            task_id="begin_insert_fulfillment_orders_shopify")
        skip_insert_fulfillment_orders_shopify_task = DummyOperator(
            task_id="skip_insert_fulfillment_orders_shopify")
        end_insert_fulfillment_orders_shopify_task = DummyOperator(
            task_id="end_insert_fulfillment_orders_shopify")
        end_fulfillment_orders_shopify_task = DummyOperator(
            task_id="end_fulfillment_orders_shopify",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_fulfillment_orders_shopify_task = BranchPythonOperator(
            task_id='check_new_files_found_fulfillment_orders_shopify',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "shopify_orders_v2/s3_to_snowflake_fulfillment_orders_shopify.yaml",
                       "skip_task_id": "load_fulfillment_orders_shopify.skip_insert_fulfillment_orders_shopify",
                       "next_task_id": "load_fulfillment_orders_shopify.begin_insert_fulfillment_orders_shopify",
                       "args_file_second": "shopify_orders_v2/s3_to_snowflake_fulfillment_orders_shopify_goessor.yaml",
                       },
        )

        s3_to_snowflake_fulfillment_orders_shopify_task = PythonOperator(
            task_id="s3_to_snowflake_fulfillment_orders_shopify",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fulfillment_orders/shopify/s3_to_sf_raw_fulfillment_orders_shopify.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        list_s3_files_fulfillment_orders_shopify_task_goessor = PythonOperator(
            task_id="list_s3_files_fulfillment_orders_shopify_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "shopify_orders_v2/list_s3_fulfillment_orders_shopify_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        s3_to_snowflake_fulfillment_orders_shopify_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_fulfillment_orders_shopify_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fulfillment_orders/shopify/s3_to_sf_raw_fulfillment_orders_shopify_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_fulfillment_orders_shopify_task = PythonOperator(
            task_id="insert_log_fulfillment_orders_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "fulfillment_orders/shopify/insert_log_fulfillment_orders_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_fulfillment_orders_shopify_task = PythonOperator(
            task_id="dedupe_fulfillment_orders_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "fulfillment_orders/shopify/dedupe_fulfillment_orders_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_fulfillment_orders_shopify_task = PythonOperator(
            task_id="merge_stage_fulfillment_orders_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "fulfillment_orders/shopify/merge_stage_fulfillment_orders_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_fulfillment_orders_shopify_task = PythonOperator(
            task_id="run_audit_fulfillment_orders_shopify",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_fulfillment_orders_shopify",
                # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                [list_s3_files_fulfillment_orders_shopify_task, list_s3_files_fulfillment_orders_shopify_task_goessor] >>
                check_new_files_found_fulfillment_orders_shopify_task >>
                [begin_insert_fulfillment_orders_shopify_task, skip_insert_fulfillment_orders_shopify_task]
        )

        (
                begin_insert_fulfillment_orders_shopify_task >>
                s3_to_snowflake_fulfillment_orders_shopify_task >>
                s3_to_snowflake_fulfillment_orders_shopify_task_goessor >>
                insert_log_fulfillment_orders_shopify_task >>
                dedupe_fulfillment_orders_shopify_task >>

                merge_stage_fulfillment_orders_shopify_task >>

                run_audit_fulfillment_orders_shopify_task >>
                end_insert_fulfillment_orders_shopify_task >>
                end_fulfillment_orders_shopify_task
        )

        skip_insert_fulfillment_orders_shopify_task >> end_fulfillment_orders_shopify_task

    #  --- Load transactions_shopify ---

    with TaskGroup(group_id='load_transactions_shopify') as tg_transactions_shopify:
        list_s3_files_transactions_shopify_task = PythonOperator(
            task_id="list_s3_files_transactions_shopify",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "shopify_orders_v2/list_s3_transactions_shopify.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_transactions_shopify_task = DummyOperator(task_id="begin_insert_transactions_shopify")
        skip_insert_transactions_shopify_task = DummyOperator(task_id="skip_insert_transactions_shopify")
        end_insert_transactions_shopify_task = DummyOperator(task_id="end_insert_transactions_shopify")
        end_transactions_shopify_task = DummyOperator(
            task_id="end_transactions_shopify",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_transactions_shopify_task = BranchPythonOperator(
            task_id='check_new_files_found_transactions_shopify',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "shopify_orders_v2/s3_to_snowflake_transactions_shopify.yaml",
                       "skip_task_id": "load_transactions_shopify.skip_insert_transactions_shopify",
                       "next_task_id": "load_transactions_shopify.begin_insert_transactions_shopify",
                       "args_file_second": "shopify_orders_v2/s3_to_snowflake_transactions_shopify_goessor.yaml",
                       },
        )

        s3_to_snowflake_transactions_shopify_task = PythonOperator(
            task_id="s3_to_snowflake_transactions_shopify",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "transactions/shopify/s3_to_sf_raw_transactions_shopify.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        list_s3_files_transactions_shopify_task_goessor = PythonOperator(
            task_id="list_s3_files_transactions_shopify_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "shopify_orders_v2/list_s3_transactions_shopify_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        s3_to_snowflake_transactions_shopify_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_transactions_shopify_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "transactions/shopify/s3_to_sf_raw_transactions_shopify_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_transactions_shopify_task = PythonOperator(
            task_id="insert_log_transactions_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "transactions/shopify/insert_log_transactions_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_transactions_shopify_task = PythonOperator(
            task_id="dedupe_transactions_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "transactions/shopify/dedupe_transactions_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_transactions_shopify_task = PythonOperator(
            task_id="merge_stage_transactions_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "transactions/shopify/merge_stage_transactions_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_transactions_shopify_task = PythonOperator(
            task_id="run_audit_transactions_shopify",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_transactions_shopify",
                # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                [list_s3_files_transactions_shopify_task,list_s3_files_transactions_shopify_task_goessor ]>>
                check_new_files_found_transactions_shopify_task >>
                [begin_insert_transactions_shopify_task, skip_insert_transactions_shopify_task]
        )

        (
                begin_insert_transactions_shopify_task >>
                s3_to_snowflake_transactions_shopify_task >>
                s3_to_snowflake_transactions_shopify_task_goessor >>
                insert_log_transactions_shopify_task >>
                dedupe_transactions_shopify_task >>

                merge_stage_transactions_shopify_task >>

                run_audit_transactions_shopify_task >>
                end_insert_transactions_shopify_task >>
                end_transactions_shopify_task
        )

        skip_insert_transactions_shopify_task >> end_transactions_shopify_task

    #  --- Load orders_shopify ---

    with TaskGroup(group_id='load_orders_shopify') as tg_orders_shopify:
        list_s3_files_orders_shopify_task = PythonOperator(
            task_id="list_s3_files_orders_shopify",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "shopify_orders_v2/list_s3_orders_shopify.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_orders_shopify_task = DummyOperator(task_id="begin_insert_orders_shopify")
        skip_insert_orders_shopify_task = DummyOperator(task_id="skip_insert_orders_shopify")
        end_insert_orders_shopify_task = DummyOperator(task_id="end_insert_orders_shopify")
        end_orders_shopify_task = DummyOperator(
            task_id="end_orders_shopify",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_orders_shopify_task = BranchPythonOperator(
            task_id='check_new_files_found_orders_shopify',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "shopify_orders_v2/s3_to_snowflake_orders_shopify.yaml",
                       "skip_task_id": "load_orders_shopify.skip_insert_orders_shopify",
                       "next_task_id": "load_orders_shopify.begin_insert_orders_shopify",
                       "args_file_second": "shopify_orders_v2/s3_to_snowflake_orders_shopify_goessor.yaml",
                       },
        )

        s3_to_snowflake_orders_shopify_task = PythonOperator(
            task_id="s3_to_snowflake_orders_shopify",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "orders/shopify/s3_to_sf_raw_orders_shopify.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        list_s3_files_orders_shopify_task_goessor = PythonOperator(
            task_id="list_s3_files_orders_shopify_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "shopify_orders_v2/list_s3_orders_shopify_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        s3_to_snowflake_orders_shopify_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_orders_shopify_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "orders/shopify/s3_to_sf_raw_orders_shopify_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_orders_shopify_task = PythonOperator(
            task_id="insert_log_orders_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "orders/shopify/insert_log_orders_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_orders_shopify_task = PythonOperator(
            task_id="dedupe_orders_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "orders/shopify/dedupe_orders_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_orders_shopify_task = PythonOperator(
            task_id="merge_stage_orders_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "orders/shopify/merge_stage_orders_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_orders_shopify_task = PythonOperator(
            task_id="run_audit_orders_shopify",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_orders_shopify",  # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                [list_s3_files_orders_shopify_task, list_s3_files_orders_shopify_task_goessor] >>
                check_new_files_found_orders_shopify_task >>
                [begin_insert_orders_shopify_task, skip_insert_orders_shopify_task]
        )

        (
                begin_insert_orders_shopify_task >>
                s3_to_snowflake_orders_shopify_task >>
                s3_to_snowflake_orders_shopify_task_goessor >>
                insert_log_orders_shopify_task >>
                dedupe_orders_shopify_task >>

                merge_stage_orders_shopify_task >>

                run_audit_orders_shopify_task >>
                end_insert_orders_shopify_task >>
                end_orders_shopify_task
        )

        skip_insert_orders_shopify_task >> end_orders_shopify_task

    #  --- Load refunds_shopify ---

    with TaskGroup(group_id='load_refunds_shopify') as tg_refunds_shopify:
        list_s3_files_refunds_shopify_task = PythonOperator(
            task_id="list_s3_files_refunds_shopify",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "shopify_orders_v2/list_s3_refunds_shopify.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_refunds_shopify_task = DummyOperator(task_id="begin_insert_refunds_shopify")
        skip_insert_refunds_shopify_task = DummyOperator(task_id="skip_insert_refunds_shopify")
        end_insert_refunds_shopify_task = DummyOperator(task_id="end_insert_refunds_shopify")
        end_refunds_shopify_task = DummyOperator(
            task_id="end_refunds_shopify",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_refunds_shopify_task = BranchPythonOperator(
            task_id='check_new_files_found_refunds_shopify',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "shopify_orders_v2/s3_to_snowflake_refunds_shopify.yaml",
                       "skip_task_id": "load_refunds_shopify.skip_insert_refunds_shopify",
                       "next_task_id": "load_refunds_shopify.begin_insert_refunds_shopify",
                       "args_file_second": "shopify_orders_v2/s3_to_snowflake_refunds_shopify_goessor.yaml",
                       },
        )

        s3_to_snowflake_refunds_shopify_task = PythonOperator(
            task_id="s3_to_snowflake_refunds_shopify",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "refunds/shopify/s3_to_sf_raw_refunds_shopify.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        list_s3_files_refunds_shopify_task_goessor = PythonOperator(
            task_id="list_s3_files_refunds_shopify_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "shopify_orders_v2/list_s3_refunds_shopify_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        s3_to_snowflake_refunds_shopify_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_refunds_shopify_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "refunds/shopify/s3_to_sf_raw_refunds_shopify_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_refunds_shopify_task = PythonOperator(
            task_id="insert_log_refunds_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "refunds/shopify/insert_log_refunds_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_refunds_shopify_task = PythonOperator(
            task_id="dedupe_refunds_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "refunds/shopify/dedupe_refunds_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_refunds_shopify_task = PythonOperator(
            task_id="merge_stage_refunds_shopify",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "refunds/shopify/merge_stage_refunds_shopify.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_refunds_shopify_task = PythonOperator(
            task_id="run_audit_refunds_shopify",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_refunds_shopify",  # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                [list_s3_files_refunds_shopify_task,list_s3_files_refunds_shopify_task_goessor ] >>
                check_new_files_found_refunds_shopify_task >>
                [begin_insert_refunds_shopify_task, skip_insert_refunds_shopify_task]
        )

        (
                begin_insert_refunds_shopify_task >>
                s3_to_snowflake_refunds_shopify_task >>
                s3_to_snowflake_refunds_shopify_task_goessor >>
                insert_log_refunds_shopify_task >>
                dedupe_refunds_shopify_task >>

                merge_stage_refunds_shopify_task >>

                run_audit_refunds_shopify_task >>
                end_insert_refunds_shopify_task >>
                end_refunds_shopify_task
        )

        skip_insert_refunds_shopify_task >> end_refunds_shopify_task

    with TaskGroup(
            group_id="ingest_parquet_data"
    ) as ingest_parquet_data:
        begin_ingestion = DummyOperator(task_id="begin_ingestion")
        end_ingestion = DummyOperator(task_id="end_ingestion")


        # shop data
        shopify_shop_list_s3_modified_files_parquet = PythonOperator(
            task_id="task_shopify_shop_list_s3_modified_files_parquet",
            python_callable=tls3.list_s3_modified_files,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "args_file": "shopify_orders_v2/shopify_shop_s3_list_folders_parquet.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        shopify_shop_transfer_s3_to_snowflake_parquet = PythonOperator(
            task_id="transfer_shopify_shop_s3_to_snowflake_parquet",
            python_callable=load_obj.s3_to_snowflake_load,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "args_file": "orders/shopify/s3_to_sf_raw_shopify_shop_parquet.yaml"
            },
        )

        shopify_shop_list_s3_modified_files_parquet_goessor = PythonOperator(
            task_id="task_shopify_shop_list_s3_modified_files_parquet_goessor",
            python_callable=tls3.list_s3_modified_files,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "args_file": "shopify_orders_v2/shopify_shop_s3_list_folders_parquet_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        shopify_shop_transfer_s3_to_snowflake_parquet_goessor = PythonOperator(
            task_id="transfer_shopify_shop_s3_to_snowflake_parquet_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "args_file": "orders/shopify/s3_to_sf_raw_shopify_shop_parquet_goessor.yaml"
            },
        )

        stg_shopify_shop = PythonOperator(
            task_id="stg_shopify_shop",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/stg_shopify_shop.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        list_s3_modified_files_parquet = PythonOperator(
            task_id="task_list_s3_modified_files_parquet",
            python_callable=tls3.list_s3_modified_files,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "args_file": "shopify_orders_hourly/s3_list_folders_parquet.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        transfer_s3_to_snowflake_parquet = PythonOperator(
            task_id="transfer_s3_to_snowflake_parquet",
            python_callable=load_obj.s3_to_snowflake_load,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "args_file": "shopify_orders_hourly/s3_to_sf_raw_shopify_orders_parquet.yaml"
            },
        )

        # list_s3_modified_files_parquet_goessor = PythonOperator(
        #     task_id="task_list_s3_modified_files_parquet_goessor",
        #     python_callable=tls3.list_s3_modified_files,
        #     provide_context=True,
        #     on_failure_callback=alerts.send_failure_alert,
        #     op_kwargs={
        #         "args_file": "shopify_orders_hourly/s3_list_folders_parquet_goessor.yaml",
        #         "wf_params": WF_PARAMS_EXPR,
        #     },
        # )

        # transfer_s3_to_snowflake_parquet_goessor = PythonOperator(
        #     task_id="transfer_s3_to_snowflake_parquet_goessor",
        #     python_callable=load_obj.s3_to_snowflake_load,
        #     provide_context=True,
        #     on_failure_callback=alerts.send_failure_alert,
        #     op_kwargs={
        #         "args_file": "shopify_orders_hourly/s3_to_sf_raw_shopify_orders_parquet_goessor.yaml"
        #     },
        # )

        #end_raw_load_shopify_orders = DummyOperator(task_id="end_raw_load_shopify_orders")

        chain(
            begin_ingestion,
            [
                list_s3_modified_files_parquet,
                shopify_shop_list_s3_modified_files_parquet,
               # list_s3_modified_files_parquet_goessor,
                shopify_shop_list_s3_modified_files_parquet_goessor
            ],
            [
                transfer_s3_to_snowflake_parquet,
                shopify_shop_transfer_s3_to_snowflake_parquet,
                shopify_shop_transfer_s3_to_snowflake_parquet_goessor
            ],

            stg_shopify_shop,

            end_ingestion,
        )

    with TaskGroup(
            group_id="raw_shopify_orders"
    ) as raw_shopify_orders:
        create_stg_raw_shopify_orders = PythonOperator(
            task_id="create_stg_raw_shopify_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_raw_shopify_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        merge_raw_shopify_orders = PythonOperator(
            task_id="merge_raw_shopify_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/merge_raw_shopify_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_raw_order_pk_is_unique = PythonOperator(
            task_id="run_dq_raw_order_pk_is_unique",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': 'shopify_orders_hourly',
                'test_name': 'staging_raw_shopify_orders_pk_is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.raw_shopify_orders",
                    field_list=['"id"', '"brand_name"', '"shopify_store"'],
                    hard_alert=True
                )
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_raw_shopify_orders = PythonOperator(
            task_id="run_audit_raw_shopify_orders",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.raw_shopify_orders",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        create_stg_raw_shopify_orders >> merge_raw_shopify_orders >> run_dq_raw_order_pk_is_unique >> run_audit_raw_shopify_orders

    with TaskGroup(
            group_id="raw_shopify_orders_legacy"
    ) as raw_shopify_orders_legacy:
        create_stg_raw_shopify_orders_legacy = PythonOperator(
            task_id="create_stg_raw_shopify_orders_legacy",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/legacy/create_stg_raw_shopify_orders_legacy.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        merge_raw_shopify_orders_legacy = PythonOperator(
            task_id="merge_raw_shopify_orders_legacy",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/legacy/merge_raw_shopify_orders_legacy.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_raw_order_legacy_pk_is_unique = PythonOperator(
            task_id="run_dq_raw_order_legacy_pk_is_unique",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': 'shopify_orders_hourly',
                'test_name': 'staging_raw_shopify_orders_legacy_pk_is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.raw_shopify_orders_v1",
                    field_list=['"id"', '"brand_name"', '"shopify_store"'],
                    hard_alert=True
                )
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )


        create_stg_raw_shopify_orders_legacy >> merge_raw_shopify_orders_legacy >> run_dq_raw_order_legacy_pk_is_unique

    with TaskGroup(group_id="line_item_updates") as line_item_updates:
        merge_shopify_variant_sku_mapping = PythonOperator(
            task_id="merge_shopify_variant_sku_mapping",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/merge_shopify_variant_sku_mapping.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_one_row_per_variant_sku = PythonOperator(
            task_id="task_run_dq_one_row_per_variant_sku",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': 'shopify_orders_hourly',
                'test_name': 'one_row_per_variant_sku',
                'sql_query': """
                    SELECT CASE WHEN COUNT(1) > 0 THEN 2 ELSE 0 END AS "result" 
                    FROM (
                        SELECT 1  FROM $common_dwh_db.shopify_variant_sku_mapping 
                        QUALIFY ROW_NUMBER() OVER (PARTITION BY "brand_name", "variant_id", "sku" order by "first_encountered_at") > 1 LIMIT 1
                    );           
                """
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        merge_shopify_order_line_item_updates = PythonOperator(
            task_id="merge_shopify_order_line_item_updates",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/merge_shopify_order_line_item_updates.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_one_row_per_line_item = PythonOperator(
            task_id="task_run_dq_one_row_per_line_item",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': 'shopify_orders_hourly',
                'test_name': 'one_row_per_line_item',
                'sql_query': """
                    SELECT CASE WHEN COUNT(1) > 0 THEN 2 ELSE 0 END AS "result" 
                    FROM (
                        SELECT 1  FROM $common_dwh_db.shopify_order_line_item_updates 
                        QUALIFY ROW_NUMBER() OVER (PARTITION BY "line_item_id" order by "record_updated_timestamp_utc") > 1 LIMIT 1
                    );            
                """
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        create_stg_raw_shopify_orders_to_process = PythonOperator(
            task_id="create_stg_raw_shopify_orders_to_process",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_raw_shopify_orders_to_process.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        (
                merge_shopify_variant_sku_mapping
                >> run_dq_one_row_per_variant_sku
                >> merge_shopify_order_line_item_updates
                >> run_dq_one_row_per_line_item
                >> create_stg_raw_shopify_orders_to_process
        )

    with TaskGroup(group_id="stg_shopify_orders_grp") as stg_shopify_orders_grp:
        create_stg_shopify_order_line_items = PythonOperator(
            task_id="create_stg_shopify_order_line_items",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_shopify_order_line_items.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_stg_shopify_order_fulfillments = PythonOperator(
            task_id="create_stg_shopify_order_fulfillments",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_shopify_order_fulfillments.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_stg_shopify_order_shipping_lines_agg = PythonOperator(
            task_id="create_stg_shopify_order_shipping_lines_agg",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_shopify_order_shipping_lines_agg.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_stg_shopify_order_discount_codes = PythonOperator(
            task_id="create_stg_shopify_order_discount_codes",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_shopify_order_discount_codes.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_stg_shopify_orders = PythonOperator(
            task_id="create_stg_shopify_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_shopify_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        chain(
            [
                create_stg_shopify_order_line_items,
                create_stg_shopify_order_fulfillments,
                create_stg_shopify_order_shipping_lines_agg,
                create_stg_shopify_order_discount_codes,
            ],
            create_stg_shopify_orders,
        )

    with TaskGroup(group_id="curated_shopify_orders_grp") as curated_shopify_orders_grp:
        merge_curated_fact_shopify_orders = PythonOperator(
            task_id="merge_curated_fact_shopify_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/merge_curated_fact_shopify_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_uniqueness_check_fact = PythonOperator(
            task_id="task_run_uniqueness_check_fact",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': 'shopify_orders_hourly',
                'tb_name': "$curated_db.fact_shopify_orders",
                'test_name': 'fact dup check',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$curated_db.fact_shopify_orders",
                    field_list=['"order_number"', '"line_item_id"'],
                    hard_alert=True
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_fact_shopify_orders = PythonOperator(
            task_id="run_audit_fact_shopify_orders",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_shopify_orders",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_curated_fact_shopify_orders >> run_uniqueness_check_fact >> run_audit_fact_shopify_orders

    with TaskGroup(
            group_id="curated_fact_all_orders_grp"
    ) as curated_fact_all_orders_grp:
        create_stg_shopify_orders_agg = PythonOperator(
            task_id="create_stg_shopify_orders_agg",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_shopify_orders_agg.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_stg_shopify_fact_all_orders = PythonOperator(
            task_id="create_stg_shopify_fact_all_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/create_stg_shopify_fact_all_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_order_pk_is_unique = PythonOperator(
            task_id="task_run_dq_order_pk_is_unique",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': 'shopify_orders_hourly',
                'test_name': 'order_pk_is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.stg_shopify_fact_all_orders",
                    field_list=['"order_pk"'],
                    hard_alert=True
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        merge_fact_all_orders = PythonOperator(
            task_id="merge_fact_all_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/merge_fact_all_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        update_fact_all_orders = PythonOperator(
            task_id="update_fact_all_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/shopify/update_fact_all_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_tests_curated = PythonOperator(
            task_id="task_run_dq_tests_curated",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': 'shopify_orders_hourly',
                'run_id': 'task_run_dq_tests_curated',
                'query_file': "orders/shopify/dq_fact_all_orders.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_audit_fact_all_orders = PythonOperator(
            task_id="run_audit_fact_all_orders",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_all_orders",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        chain(
            create_stg_shopify_orders_agg,
            create_stg_shopify_fact_all_orders,
            run_dq_order_pk_is_unique,
            merge_fact_all_orders,
            update_fact_all_orders,
            run_dq_tests_curated,
            run_audit_fact_all_orders,
        )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
    )

    begin = DummyOperator(
        task_id="begin", depends_on_past=True, wait_for_downstream=True
    )
    end = DummyOperator(task_id="end")

    chain(
        begin,
        get_workflow_params,
        [tg_fulfillment_events_shopify,
         tg_fulfillment_orders_shopify,
         tg_transactions_shopify,
         tg_orders_shopify,
         tg_refunds_shopify],
        ingest_parquet_data,
        [
            raw_shopify_orders,
            raw_shopify_orders_legacy,
            update_brand_timezone_dim
        ],
        line_item_updates,
        stg_shopify_orders_grp,
        [
            curated_shopify_orders_grp,
            curated_fact_all_orders_grp
        ],
        update_workflow_params,
        end,
    )

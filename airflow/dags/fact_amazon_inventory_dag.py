from airflow import DAG
from datetime import datetime
from airflow.utils.task_group import TaskGroup
from airflow.operators.python import Python<PERSON>perator, BranchPythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.utils.trigger_rule import TriggerRule
from airflow.models.baseoperator import chain
import logging
from tasklib import alerts
from tasklib.loader import check_for_new_files

import tasklib.s3 as tls3
import tasklib.dq as tldq
import tasklib.sql as tlsql
from tasklib.loader import S3ToSnowflake
load_obj = S3ToSnowflake()

log = logging.getLogger(__name__)

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

DAG_ID = "fact_amazon_inventory"

with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022, 5, 8),
    schedule_interval="@hourly",
    catchup=False,
    max_active_runs=1,
    tags=["Inventory-v2", "Vikas"],
    params={"workflow_name": "FACT_AMAZON_INVENTORY_HOURLY", "author": "vikas"},
) as dag:

    import tasklib.workflow as tlw

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )

    create_stg_fact_amazon_inventory_driver = PythonOperator(
        task_id="create_stg_fact_amazon_inventory_driver",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "inventory/amazon/fba_inventory/create_stg_fact_amazon_inventory_driver.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    join_stage_tables = PythonOperator(
        task_id="task_join_stage_tables",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "inventory/amazon/fba_inventory/create_stg_fact_amazon_inventory.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    join_all_reports_stage_tables = PythonOperator(
        task_id="join_all_reports_stage_tables",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "inventory/amazon/fba_inventory/fact_inventory_all_report/stg_amazon_inventory_all_report.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )


    run_dq_tests_pre_curated = PythonOperator(
        task_id="run_dq_tests_pre_curated",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            "wk_name": DAG_ID,
            "run_id": "task_run_dq_tests_fact_inventory_stage",
            "tb_name": "$stage_db.STG_FACT_AMAZON_INVENTORY",
            "query_file": "inventory/amazon/fba_inventory/dq_stg_fact_amazon_inventory.yaml",
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    merge_fact_all_report = PythonOperator(
        task_id="merge_fact_all_report",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "inventory/amazon/fba_inventory/fact_inventory_all_report/merge_fact_amazon_inventory_all_report.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    merge = PythonOperator(
        task_id="task_merge",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "inventory/amazon/fba_inventory/merge_fact_amazon_inventory.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    update_fact_amazon_inventory = PythonOperator(
        task_id="update_fact_amazon_inventory",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "inventory/amazon/fba_inventory/update_fact_amazon_inventory.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    update_narf_status = PythonOperator(
        task_id="update_narf_status",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "inventory/amazon/fba_inventory/update_narf_flag.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_dq_tests_curated = PythonOperator(
        task_id="run_dq_tests_curated",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            "wk_name": DAG_ID,
            "run_id": "task_run_dq_tests_fact_amazon_inventory",
            "tb_name": "$curated_db.FACT_AMAZON_INVENTORY",
            "query_file": "inventory/amazon/fba_inventory/dq_fact_amazon_inventory.yaml",
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    import tasklib.audit as tla

    run_audit=PythonOperator(
        task_id="task_run_audit",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.FACT_AMAZON_INVENTORY", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
    end = DummyOperator(task_id="end")
    all_staging_loads_completed = DummyOperator(
        task_id="all_staging_loads_completed",
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    with TaskGroup(group_id='load_reserved_inventory') as load_reserved_inventory:
        list_s3_files_fba_reserved_inventory_report_task = PythonOperator(
            task_id="list_s3_files_fba_reserved_inventory_report",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_reserved_inv_list_s3.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_fba_reserved_inventory_report_task = DummyOperator(task_id="begin_insert_fba_reserved_inventory_report")
        skip_insert_fba_reserved_inventory_report_task = DummyOperator(task_id="skip_insert_fba_reserved_inventory_report")
        end_insert_fba_reserved_inventory_report_task = DummyOperator(task_id="end_insert_fba_reserved_inventory_report")

        s3_to_snowflake_fba_reserved_inventory_report_task = PythonOperator(
            task_id="s3_to_snowflake_fba_reserved_inventory_report",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fact_amazon_inventory/s3_to_sf_raw_fba_reserved_inventory_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_fba_reserved_inventory_report_task_goessor = PythonOperator(
            task_id="list_s3_files_fba_reserved_inventory_report_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_reserved_inv_list_s3_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        s3_to_snowflake_fba_reserved_inventory_report_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_fba_reserved_inventory_report_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fact_amazon_inventory/s3_to_sf_raw_fba_reserved_inventory_report_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_fba_reserved_inventory_report_task = BranchPythonOperator(
            task_id='check_new_files_found_fba_reserved_inventory_report',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "fba_reserved_inventory_report/s3_to_snowflake_fba_reserved_inventory_report.yaml",
                       "skip_task_id": "load_reserved_inventory.skip_insert_fba_reserved_inventory_report",
                       "next_task_id": "load_reserved_inventory.begin_insert_fba_reserved_inventory_report",
                       "args_file_second": "fba_reserved_inventory_report/s3_to_snowflake_fba_reserved_inventory_report_goessor.yaml"
            },
        )

        insert_raw_fba_reserved_inventory_report_log_task = PythonOperator(
            task_id="insert_raw_fba_reserved_inventory_report_log",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "inventory/amazon/fba_inventory/fba_reserved_inv/insert_fba_reserved_inventory_report_log.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_fba_reserved_inventory_report_task = PythonOperator(
            task_id="dedupe_fba_reserved_inventory_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "inventory/amazon/fba_inventory/fba_reserved_inv/dedupe_fba_reserved_inventory_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        merge_stage_fba_reserved_inventory_report_task = PythonOperator(
            task_id="merge_stage_fba_reserved_inventory_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "inventory/amazon/fba_inventory/fba_reserved_inv/merge_stage_fba_reserved_inventory_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_fba_reserved_inventory_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_fba_reserved_inventory_report',
                    field_list=['asin', 'fnsku', 'sku', 'marketplaceid', 'sellingpartnerid', 'file_name'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )


        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_fba_reserved_inventory_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_fba_reserved_inventory_report',
                    field_list=['country', 'connector_region', 'snapshot_date', 'sku', 'asin', 'fnsku', 'seller_id', 'marketplace_id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )


        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.fba_reserved_inventory_report_p',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.fba_reserved_inventory_report_p',
                    field_list=['snapshot_date', 'sku', 'asin', 'fnsku', 'seller_id', 'marketplace_id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_audit_fba_reserved_inventory_report_task = PythonOperator(
            task_id="run_audit_fba_reserved_inventory_report",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.fba_reserved_inventory_report_p", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_fba_reserved_inventory_report_task,list_s3_files_fba_reserved_inventory_report_task_goessor] >>
            check_new_files_found_fba_reserved_inventory_report_task >>
            [begin_insert_fba_reserved_inventory_report_task, skip_insert_fba_reserved_inventory_report_task]
        )

        (
            begin_insert_fba_reserved_inventory_report_task >>
            s3_to_snowflake_fba_reserved_inventory_report_task >>
            s3_to_snowflake_fba_reserved_inventory_report_task_goessor >>
            run_dq_is_null_raw_task >>
            insert_raw_fba_reserved_inventory_report_log_task >>
            dedupe_fba_reserved_inventory_report_task >>
            run_dq_is_unique_dedupe_pk_task >> 
            merge_stage_fba_reserved_inventory_report_task >>
            run_dq_is_unique_merge_pk_task >>
            run_audit_fba_reserved_inventory_report_task >>
            end_insert_fba_reserved_inventory_report_task
        )

    with TaskGroup(group_id="load_manage_archived_inventory") as load_manage_archived_inventory:
        # Need a begin dummy operator for branching
        begin_insert_manage_archived_inventory_task = DummyOperator(
            task_id="begin_insert_manage_archived_inventory_task"
        )
        skip_insert_manage_archived_inventory_task = DummyOperator(
            task_id="skip_insert_manage_archived_inventory_task"
        )
        end_insert_manage_archived_inventory_task = DummyOperator(
            task_id="end_insert_manage_archived_inventory_task"
        )

        manage_archived_list_s3_modified_files = PythonOperator(
            task_id="manage_archived_list_s3_modified_files",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_manage_inv_archived_list_s3.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        check_new_files_found_manage_archived_inventory_task = BranchPythonOperator(
            task_id="check_new_files_found_manage_archived_inventory_task",
            python_callable=check_for_new_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_manage_inv_archived_s3_to_snowflake.yaml",
                "skip_task_id": "load_manage_archived_inventory.skip_insert_manage_archived_inventory_task",
                "next_task_id": "load_manage_archived_inventory.begin_insert_manage_archived_inventory_task",
                "args_file_second": "fact_amazon_inventory/fba_manage_inv_archived_s3_to_snowflake_goessor.yaml"
            },
        )

        manage_archived_transfer_s3_to_snowflake = PythonOperator(
            task_id="manage_archived_transfer_s3_to_snowflake",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "fact_amazon_inventory/s3_to_sf_raw_amazon_fba_manage_archived_inventory_report.yaml"
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_archived_list_s3_modified_files_goessor = PythonOperator(
            task_id="manage_archived_list_s3_modified_files_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_manage_inv_archived_list_s3_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_archived_transfer_s3_to_snowflake_goessor = PythonOperator(
            task_id="manage_archived_transfer_s3_to_snowflake_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "fact_amazon_inventory/s3_to_sf_raw_amazon_fba_manage_archived_inventory_report_goessor.yaml"
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_archived_dedup = PythonOperator(
            task_id="manage_archived_dedup",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "inventory/amazon/fba_inventory/fba_manage_inv_archived/dedup_raw.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_archived_run_dq_is_null_dedupe_task = PythonOperator(
            task_id="run_dq_is_null_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MANAGE_ARCHIVED_REPORT_DEDUP",
                "test_name": "is_null",
                "sql_query": tldq.gen_check_if_nulls(
                    tb_name="$stage_db.STG_AMAZON_FBA_MANAGE_ARCHIVED_REPORT_DEDUP",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "connector_region",
                        "snapshot_date",
                        "condition",
                        "country",
                    ],
                    hard_alert=False,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_archived_stage = PythonOperator(
            task_id="manage_archived_stage",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "inventory/amazon/fba_inventory/fba_manage_inv_archived/stage.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_archived_stage_run_dq_is_null = PythonOperator(
            task_id="run_dq_is_null_archive_stage",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MANAGE_ARCHIVED_INVENTORY_REPORT",
                "test_name": "is_null",
                "sql_query": tldq.gen_check_if_nulls(
                    tb_name="$stage_db.STG_AMAZON_FBA_MANAGE_ARCHIVED_INVENTORY_REPORT",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "connector_region",
                        "snapshot_date",
                        "condition",
                        "country",
                    ],
                    hard_alert=False,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_archived_stage_run_dq_pk_not_dup = PythonOperator(
            task_id="run_dq_pk_not_dup_manage_archive_stage",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MANAGE_ARCHIVED_INVENTORY_REPORT",
                "test_name": "pk_not_duplicate",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.STG_AMAZON_FBA_MANAGE_ARCHIVED_INVENTORY_REPORT",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "connector_region",
                        "snapshot_date",
                        "condition",
                        "country",
                        "seller_id"
                    ],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_archived_stage_perm = PythonOperator(
            task_id="manage_archived_stage_perm",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "inventory/amazon/fba_inventory/fba_manage_inv_archived/stage_perm.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_archived_stage_perm_run_dq_pk_not_dup = PythonOperator(
            task_id="run_dq_pk_not_dup_manage_archive_stage_perm",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MANAGE_ARCHIVED_INVENTORY_REPORT_P",
                "test_name": "pk_not_duplicate",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.STG_AMAZON_FBA_MANAGE_ARCHIVED_INVENTORY_REPORT_P",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "connector_region",
                        "snapshot_date",
                        "condition",
                        "country",
                        "seller_id"
                    ],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            [manage_archived_list_s3_modified_files,manage_archived_list_s3_modified_files_goessor]
            >> check_new_files_found_manage_archived_inventory_task
            >> [
                begin_insert_manage_archived_inventory_task,
                skip_insert_manage_archived_inventory_task,
            ]
        )

        (
            begin_insert_manage_archived_inventory_task
            >> manage_archived_transfer_s3_to_snowflake
            >> manage_archived_transfer_s3_to_snowflake_goessor
            >> manage_archived_dedup
            >> manage_archived_run_dq_is_null_dedupe_task
            >> manage_archived_stage
            >> manage_archived_stage_run_dq_is_null
            >> manage_archived_stage_run_dq_pk_not_dup
            >> manage_archived_stage_perm
            >> manage_archived_stage_perm_run_dq_pk_not_dup
            >> end_insert_manage_archived_inventory_task
        )

    with TaskGroup(group_id="load_manage_inventory") as load_manage_inventory:
        # Need a begin dummy operator for branching
        begin_insert_manage_inventory_task = DummyOperator(
            task_id="begin_insert_manage_inventory_task"
        )
        skip_insert_manage_inventory_task = DummyOperator(
            task_id="skip_insert_manage_inventory_task"
        )
        end_insert_manage_inventory_task = DummyOperator(
            task_id="end_insert_manage_inventory_task"
        )

        manage_list_s3_modified_files = PythonOperator(
            task_id="manage_list_s3_modified_files",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_manage_inv_list_s3.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        check_new_files_found_manage_inventory_task = BranchPythonOperator(
            task_id="check_new_files_found_manage_inventory_task",
            python_callable=check_for_new_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_manage_inv_s3_to_snowflake.yaml",
                "skip_task_id": "load_manage_inventory.skip_insert_manage_inventory_task",
                "next_task_id": "load_manage_inventory.begin_insert_manage_inventory_task",
                "args_file_second": "fact_amazon_inventory/fba_manage_inv_s3_to_snowflake_goessor.yaml"
            },
        )

        manage_transfer_s3_to_snowflake = PythonOperator(
            task_id="manage_transfer_s3_to_snowflake",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fact_amazon_inventory/s3_to_sf_raw_amazon_fba_manage_inventory_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_list_s3_modified_files_goessor = PythonOperator(
            task_id="manage_list_s3_modified_files_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_manage_inv_list_s3_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_transfer_s3_to_snowflake_goessor = PythonOperator(
            task_id="manage_transfer_s3_to_snowflake_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fact_amazon_inventory/s3_to_sf_raw_amazon_fba_manage_inventory_report_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_dedup = PythonOperator(
            task_id="manage_dedup",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "inventory/amazon/fba_inventory/fba_managed_inv/dedup_raw.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_run_dq_is_null_dedupe_task = PythonOperator(
            task_id="run_dq_is_null_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MANAGE_REPORT_DEDUP",
                "test_name": "is_null",
                "sql_query": tldq.gen_check_if_nulls(
                    tb_name="$stage_db.STG_AMAZON_FBA_MANAGE_REPORT_DEDUP",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "connector_region",
                        "snapshot_date",
                        "condition",
                        "country",
                    ],
                    hard_alert=False,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_stage = PythonOperator(
            task_id="manage_stage",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "inventory/amazon/fba_inventory/fba_managed_inv/stage.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_run_dq_is_null_stage_task = PythonOperator(
            task_id="run_dq_is_null_stage",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MANAGE_INVENTORY_REPORT",
                "test_name": "is_null",
                "sql_query": tldq.gen_check_if_nulls(
                    tb_name="$stage_db.STG_AMAZON_FBA_MANAGE_INVENTORY_REPORT",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "connector_region",
                        "snapshot_date",
                        "condition",
                        "country",
                    ],
                    hard_alert=False,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_stage_run_dq_pk_not_dup = PythonOperator(
            task_id="run_dq_pk_not_dup_manage_stage",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MANAGE_INVENTORY_REPORT",
                "test_name": "pk_not_duplicate",
                "sql_query": tldq.gen_check_if_nulls(
                    tb_name="$stage_db.STG_AMAZON_FBA_MANAGE_INVENTORY_REPORT",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "connector_region",
                        "snapshot_date",
                        "condition",
                        "country",
                    ],
                    hard_alert=False,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_stage_perm = PythonOperator(
            task_id="manage_stage_perm",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "inventory/amazon/fba_inventory/fba_managed_inv/stage_perm.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            [manage_list_s3_modified_files, manage_list_s3_modified_files_goessor]
            >> check_new_files_found_manage_inventory_task
            >> [
                begin_insert_manage_inventory_task,
                skip_insert_manage_inventory_task,
            ]
        )
        (
            begin_insert_manage_inventory_task
            >> manage_transfer_s3_to_snowflake
            >> manage_transfer_s3_to_snowflake_goessor
            >> manage_dedup
            >> manage_run_dq_is_null_dedupe_task
            >> manage_stage
            >> manage_run_dq_is_null_stage_task
            >> manage_stage_run_dq_pk_not_dup
            >> manage_stage_perm
            >> end_insert_manage_inventory_task
        )

    with TaskGroup(group_id="load_manage_health_inventory") as load_manage_health_inventory:
        # Need a begin dummy operator for branching
        begin_insert_manage_inventory_health_task = DummyOperator(
            task_id="begin_insert_manage_inventory_health_task"
        )
        skip_insert_manage_inventory_health_task = DummyOperator(
            task_id="skip_insert_manage_inventory_health_task"
        )
        end_insert_manage_inventory_health_task = DummyOperator(
            task_id="end_insert_manage_inventory_health_task"
        )

        manage_health_list_s3_modified_files = PythonOperator(
            task_id="manage_health_list_s3_modified_files",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_manage_inv_health_list_s3.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        check_new_files_found_manage_inventory_health_task = BranchPythonOperator(
            task_id="check_new_files_found_manage_inventory_health_task",
            python_callable=check_for_new_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_manage_inv_health_s3_to_snowflake.yaml",
                "skip_task_id": "load_manage_health_inventory.skip_insert_manage_inventory_health_task",
                "next_task_id": "load_manage_health_inventory.begin_insert_manage_inventory_health_task",
                "args_file_second": "fact_amazon_inventory/fba_manage_inv_health_s3_to_snowflake_goessor.yaml"
            },
        )

        manage_health_transfer_s3_to_snowflake = PythonOperator(
            task_id="manage_health_transfer_s3_to_snowflake",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fact_amazon_inventory/s3_to_sf_raw_amazon_fba_manage_inventory_health_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_health_list_s3_modified_files_goessor = PythonOperator(
            task_id="manage_health_list_s3_modified_files_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_manage_inv_health_list_s3_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_health_transfer_s3_to_snowflake_goessor = PythonOperator(
            task_id="manage_health_transfer_s3_to_snowflake_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "fact_amazon_inventory/s3_to_sf_raw_amazon_fba_manage_inventory_health_report_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_health_raw_run_dq_is_null = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$raw_db.RAW_AMAZON_FBA_MANAGE_INVENTORY_HEALTH_REPORT",
                "test_name": "is_null",
                "sql_query": tldq.gen_check_if_nulls(
                    tb_name="$raw_db.RAW_AMAZON_FBA_MANAGE_INVENTORY_HEALTH_REPORT",
                    field_list=[
                         "sku", 
                         "fnsku", 
                         "asin", 
                         "marketplaceId", 
                         "condition", 
                         "snapshot_date", 
                         "sellingPartnerId", 
                         "ReportstartDate"
                    ],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_health_stage_dedup = PythonOperator(
            task_id="manage_health_stage_dedup",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "inventory/amazon/fba_inventory/fba_manage_inv_health/dedup_raw.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_health_stage_dedup_run_dq_is_null = PythonOperator(
            task_id="run_dq_is_null_manage_health_stage_dedup",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MANAGE_INVENTORY_HEALTH_REPORT_DEDUP",
                "test_name": "is_null",
                "sql_query": tldq.gen_check_if_nulls(
                    tb_name="$stage_db.STG_AMAZON_FBA_MANAGE_INVENTORY_HEALTH_REPORT_DEDUP",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "snapshot_date",
                        "condition",
                        "country",
                        "seller_id",
                        "connector_region"
                    ],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_health_dedup_actual_date = PythonOperator(
            task_id="manage_health_dedup_actual_date",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "inventory/amazon/fba_inventory/fba_manage_inv_health/dedup_raw_actual_date.sql",
                "wf_params": WF_PARAMS_EXPR,
            },  
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_health_stage_dedup_actual_date_run_dq_is_null = PythonOperator(
            task_id="run_dq_is_null_health_stage_dedup_actual_date",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MANAGE_INVENTORY_HEALTH_REPORT_DEDUP_ACTUAL_DATE",
                "test_name": "is_null",
                "sql_query": tldq.gen_check_if_nulls(
                    tb_name="$stage_db.STG_AMAZON_FBA_MANAGE_INVENTORY_HEALTH_REPORT_DEDUP_ACTUAL_DATE",
                    field_list=["sku", "condition", "country", "connector_region"],
                    hard_alert=True,
                ),   
            },   
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_health_stage_dedup_run_dq_pk_not_dup = PythonOperator(
            task_id="run_dq_pk_not_dup_health_stage_dedup",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MANAGE_INVENTORY_HEALTH_REPORT_DEDUP",
                "test_name": "pk_not_duplicate",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.STG_AMAZON_FBA_MANAGE_INVENTORY_HEALTH_REPORT_DEDUP",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "snapshot_date",
                        "condition",
                        "country",
                        "seller_id"
                    ],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_health_stage_perm = PythonOperator(
            task_id="manage_health_stage_perm",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "inventory/amazon/fba_inventory/fba_manage_inv_health/stage_perm.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_health_stage_perm_run_dq_pk_not_dup = PythonOperator(
            task_id="run_dq_pk_no_dup_health_stage_perm",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MANAGE_INVENTORY_HEALTH_REPORT_P",
                "test_name": "pk_not_duplicate",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.STG_AMAZON_FBA_MANAGE_INVENTORY_HEALTH_REPORT_P",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "snapshot_date",
                        "condition",
                        "country",
                        "seller_id"
                    ],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_health_stage_perm_actual_date = PythonOperator(
            task_id="manage_health_stage_perm_actual_date",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "inventory/amazon/fba_inventory/fba_manage_inv_health/stage_perm_actual_date.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        manage_health_stage_actual_date_perm_run_dq_pk_not_dup = PythonOperator(
            task_id="run_dq_pk_no_dup_health_stage_actual_date_perm",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MANAGE_INVENTORY_HEALTH_REPORT_ACTUAL_DATE_P",
                "test_name": "pk_not_duplicate",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.STG_AMAZON_FBA_MANAGE_INVENTORY_HEALTH_REPORT_ACTUAL_DATE_P",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "snapshot_date",
                        "condition",
                        "country",
                        "seller_id"
                    ],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            [manage_health_list_s3_modified_files, manage_health_list_s3_modified_files_goessor]
            >> check_new_files_found_manage_inventory_health_task
            >> [begin_insert_manage_inventory_health_task, skip_insert_manage_inventory_health_task]
        )

        (
            begin_insert_manage_inventory_health_task
            >> manage_health_transfer_s3_to_snowflake
            >> manage_health_transfer_s3_to_snowflake_goessor
            >> manage_health_raw_run_dq_is_null
            >> manage_health_stage_dedup
            >> manage_health_stage_dedup_run_dq_is_null
            >> manage_health_stage_dedup_run_dq_pk_not_dup 
            >> manage_health_stage_perm
            >> manage_health_stage_perm_run_dq_pk_not_dup
            >> manage_health_dedup_actual_date
            >> manage_health_stage_dedup_actual_date_run_dq_is_null
            >> manage_health_stage_perm_actual_date
            >> manage_health_stage_actual_date_perm_run_dq_pk_not_dup 
            >> end_insert_manage_inventory_health_task
        )

    with TaskGroup(group_id="load_restock_inventory") as load_restock_inventory:

        # Need a begin dummy operator for branching
        begin_insert_restock_inventory_task = DummyOperator(
            task_id="begin_insert_restock_inventory_task"
        )
        skip_insert_restock_inventory_task = DummyOperator(
            task_id="skip_insert_restock_inventory_task"
        )
        end_insert_restock_inventory_task = DummyOperator(
            task_id="end_insert_restock_inventory_task"
        )

        check_new_files_found_restock_inventory_task = BranchPythonOperator(
            task_id="check_new_files_found_restock_inventory_task",
            python_callable=check_for_new_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_restock_inv_s3_to_snowflake.yaml",
                "skip_task_id": "load_restock_inventory.skip_insert_restock_inventory_task",
                "next_task_id": "load_restock_inventory.begin_insert_restock_inventory_task",
                "args_file_second": "fact_amazon_inventory/fba_restock_inv_s3_to_snowflake_goessor.yaml",
            },
        )

        restock_list_s3_modified_files = PythonOperator(
            task_id="restock_list_s3_modified_files",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_restock_inv_list_s3.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        restock_transfer_s3_to_snowflake = PythonOperator(
            task_id="restock_transfer_s3_to_snowflake",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fact_amazon_inventory/s3_to_sf_raw_amazon_fba_restock_inventory_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        restock_list_s3_modified_files_goessor = PythonOperator(
            task_id="restock_list_s3_modified_files_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_restock_inv_list_s3_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        restock_transfer_s3_to_snowflake_goessor = PythonOperator(
            task_id="restock_transfer_s3_to_snowflake_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fact_amazon_inventory/s3_to_sf_raw_amazon_fba_restock_inventory_report_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        restock_dedup = PythonOperator(
            task_id="restock_dedup",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "inventory/amazon/fba_inventory/fba_restock_inv/dedup_raw.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        restock_dedup_run_dq_is_null = PythonOperator(
            task_id="run_dq_is_null_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_RESTOCK_INVENTORY_REPORT_DEDUP",
                "test_name": "is_null",
                "sql_query": tldq.gen_check_if_nulls(
                    tb_name="$stage_db.STG_AMAZON_FBA_RESTOCK_INVENTORY_REPORT_DEDUP",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "connector_region",
                        "snapshot_date",
                        "condition",
                        "country",
                    ],
                    hard_alert=False,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        restock_dedup_run_dq_pk_not_dup = PythonOperator(
            task_id="run_dq_pk_no_dup_restock_dedup",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_RESTOCK_INVENTORY_REPORT_DEDUP",
                "test_name": "pk_not_duplicate",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.STG_AMAZON_FBA_RESTOCK_INVENTORY_REPORT_DEDUP",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "snapshot_date",
                        "condition",
                        "country",
                        "seller_id"
                    ],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        restock_stage_perm = PythonOperator(
            task_id="restock_stage_perm",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "inventory/amazon/fba_inventory/fba_restock_inv/stage_perm.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        restock_stage_perm_run_dq_pk_not_dup = PythonOperator(
            task_id="run_dq_pk_no_dup_restock_stage_perm",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_RESTOCK_INVENTORY_REPORT_P",
                "test_name": "pk_not_duplicate",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.STG_AMAZON_FBA_RESTOCK_INVENTORY_REPORT_P",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "snapshot_date",
                        "condition",
                        "country",
                        "seller_id"
                    ],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            [restock_list_s3_modified_files, restock_list_s3_modified_files_goessor]
            >> check_new_files_found_restock_inventory_task
            >> [begin_insert_restock_inventory_task, skip_insert_restock_inventory_task]
        )

        (
            begin_insert_restock_inventory_task
            >> restock_transfer_s3_to_snowflake
            >> restock_transfer_s3_to_snowflake_goessor
            >> restock_dedup
            >> restock_dedup_run_dq_is_null
            >> restock_dedup_run_dq_pk_not_dup
            >> restock_stage_perm
            >> restock_stage_perm_run_dq_pk_not_dup
            >> end_insert_restock_inventory_task
        )

    with TaskGroup(group_id="load_multi_inventory") as load_multi_inventory:

        # Need a begin dummy operator for branching
        begin_insert_multi_inventory_task = DummyOperator(
            task_id="begin_insert_multi_inventory_task"
        )
        skip_insert_multi_inventory_task = DummyOperator(
            task_id="skip_insert_multi_inventory_task"
        )
        end_insert_multi_inventory_task = DummyOperator(
            task_id="end_insert_multi_inventory_task"
        )

        check_new_files_found_multi_inventory_task = BranchPythonOperator(
            task_id="check_new_files_found_multi_inventory_task",
            python_callable=check_for_new_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_multi_country_inv_s3_to_snowflake.yaml",
                "skip_task_id": "load_multi_inventory.skip_insert_multi_inventory_task",
                "next_task_id": "load_multi_inventory.begin_insert_multi_inventory_task",
                "args_file_second": "fact_amazon_inventory/fba_multi_country_inv_s3_to_snowflake_goessor.yaml",
            },
        )

        multi_list_s3_modified_files = PythonOperator(
            task_id="multi_list_s3_modified_files",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_multi_country_inv_list_s3.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        multi_transfer_s3_to_snowflake = PythonOperator(
            task_id="multi_transfer_s3_to_snowflake",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fact_amazon_inventory/s3_to_sf_raw_amazon_fba_multi_country_inventory_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        multi_list_s3_modified_files_goessor = PythonOperator(
            task_id="multi_list_s3_modified_files_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inventory/fba_multi_country_inv_list_s3_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        multi_transfer_s3_to_snowflake_goessor = PythonOperator(
            task_id="multi_transfer_s3_to_snowflake_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "fact_amazon_inventory/s3_to_sf_raw_amazon_fba_multi_country_inventory_report_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        multi_dedup = PythonOperator(
            task_id="multi_dedup",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "inventory/amazon/fba_inventory/fba_multi_country_inv/dedup_raw.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        multi_dedupe_run_dq_is_null = PythonOperator(
            task_id="run_dq_is_null_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MULTI_COUNTRY_INVENTORY_REPORT_DEDUP",
                "test_name": "is_null",
                "sql_query": tldq.gen_check_if_nulls(
                    tb_name="$stage_db.STG_AMAZON_FBA_MULTI_COUNTRY_INVENTORY_REPORT_DEDUP",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "connector_region",
                        "snapshot_date",
                        "condition",
                        "country",
                    ],
                    hard_alert=False,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        multi_run_dq_pk_not_dup_dedupe_task = PythonOperator(
            task_id="run_dq_pk_not_dup_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MULTI_COUNTRY_INVENTORY_REPORT_DEDUP",
                "test_name": "pk_not_duplicate",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.STG_AMAZON_FBA_MULTI_COUNTRY_INVENTORY_REPORT_DEDUP",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "connector_region",
                        "snapshot_date",
                        "condition",
                        "country",
                        "seller_id"
                    ],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        multi_stage_perm = PythonOperator(
            task_id="multi_stage_perm",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "inventory/amazon/fba_inventory/fba_multi_country_inv/stage_perm.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        multi_stage_perm_run_dq_pk_not_dup = PythonOperator(
            task_id="run_dq_pk_no_dup_multi_stage_p",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.STG_AMAZON_FBA_MULTI_COUNTRY_INVENTORY_REPORT_P",
                "test_name": "pk_not_duplicate",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.STG_AMAZON_FBA_MULTI_COUNTRY_INVENTORY_REPORT_P",
                    field_list=[
                        "sku",
                        "fnsku",
                        "asin",
                        "snapshot_date",
                        "condition",
                        "country",
                        "seller_id"
                    ],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            [multi_list_s3_modified_files, multi_list_s3_modified_files_goessor]
            >> check_new_files_found_multi_inventory_task
            >> [begin_insert_multi_inventory_task, skip_insert_multi_inventory_task]
        )
        (
            begin_insert_multi_inventory_task
            >> multi_transfer_s3_to_snowflake
            >> multi_transfer_s3_to_snowflake_goessor
            >> multi_dedup
            >> multi_dedupe_run_dq_is_null
            >> multi_run_dq_pk_not_dup_dedupe_task
            >> multi_stage_perm
            >> multi_stage_perm_run_dq_pk_not_dup
            >> end_insert_multi_inventory_task
        )

    # deprecating legacy stage
    # with TaskGroup(group_id="load_legacy_inventory") as load_legacy_inventory:
    #     # Need a begin dummy operator for branching
    #     begin_insert_legacy_inventory_task = DummyOperator(
    #         task_id="begin_insert_legacy_inventory_task"
    #     )
    #
    #     end_insert_legacy_inventory_task = DummyOperator(
    #         task_id="end_insert_legacy_inventory_task"
    #     )
    #
    #     create_stg_fact_amazon_inventory_legacy = PythonOperator(
    #         task_id="create_stg_fact_amazon_inventory_legacy",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={
    #             "connection": "Snowflake",
    #             "sql_file": "inventory/amazon/fba_inventory/legacy/create_stg_fact_amazon_inventory_legacy.sql",
    #             "wf_params": WF_PARAMS_EXPR,
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     run_dq_legacy_tests_pre_curated = PythonOperator(
    #         task_id="run_dq_legacy_tests_pre_curated",
    #         python_callable=tldq.run_dq_file,
    #         op_kwargs={
    #             "wk_name": DAG_ID,
    #             "run_id": "task_run_dq_tests_fact_inventory_stage",
    #             "tb_name": "$stage_db.STG_FACT_AMAZON_INVENTORY_LEGACY",
    #             "query_file": "inventory/amazon/fba_inventory/legacy/dq_stg_fact_amazon_inventory.yaml",
    #         },
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     merge_fact_amazon_inventory_legacy = PythonOperator(
    #         task_id="merge_fact_amazon_inventory_legacy",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={
    #             "connection": "Snowflake",
    #             "sql_file": "inventory/amazon/fba_inventory/legacy/merge_fact_amazon_inventory.sql",
    #             "wf_params": WF_PARAMS_EXPR,
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     run_dq_tests_legacy_curated = PythonOperator(
    #         task_id="run_dq_tests_curated",
    #         python_callable=tldq.run_dq_file,
    #         op_kwargs={
    #             "wk_name": DAG_ID,
    #             "run_id": "task_run_dq_tests_fact_amazon_inventory",
    #             "tb_name": "$curated_db.FACT_AMAZON_INVENTORY_LEGACY",
    #             "query_file": "inventory/amazon/fba_inventory/legacy/dq_fact_amazon_inventory.yaml",
    #         },
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     update_fact_amazon_inventory_legacy = PythonOperator(
    #         task_id="update_fact_amazon_inventory_legacy",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={
    #             "connection": "Snowflake",
    #             "sql_file": "inventory/amazon/fba_inventory/legacy/update_fact_amazon_inventory.sql",
    #             "wf_params": WF_PARAMS_EXPR,
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     (
    #         begin_insert_legacy_inventory_task
    #         >> create_stg_fact_amazon_inventory_legacy
    #         >> run_dq_legacy_tests_pre_curated
    #         >> merge_fact_amazon_inventory_legacy
    #         >> run_dq_tests_legacy_curated
    #         >> update_fact_amazon_inventory_legacy
    #         >> end_insert_legacy_inventory_task
    #     )

    chain(
        begin,
        get_workflow_params,
        [
            load_manage_health_inventory,
            load_restock_inventory,
            load_multi_inventory,
            load_manage_inventory,
            load_manage_archived_inventory,
            load_reserved_inventory,
        ],
        all_staging_loads_completed,
        create_stg_fact_amazon_inventory_driver,
        [join_stage_tables, join_all_reports_stage_tables],#load_legacy_inventory
        run_dq_tests_pre_curated,
        [merge, merge_fact_all_report],
        update_fact_amazon_inventory,
        update_narf_status,
        run_dq_tests_curated,
        update_workflow_params,
        run_audit,
        end,
    )

from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging
from airflow.sensors.external_task import ExternalTaskSensor
from tasklib import alerts

log = logging.getLogger(__name__)
DAG_ID = 'fact_amazon_ad_purchased_products'

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
        dag_id=DAG_ID,
        start_date=datetime(2022, 5, 8),
        schedule_interval='0 */4 * * *',
        catchup=False,
        max_active_runs=1,
        params={'workflow_name': DAG_ID,
                'author': 'sauvik'
                },
        tags=["Sauvik"]
) as dag:
    import tasklib.workflow as tlw
    import tasklib.s3 as tls3
    import tasklib.glue as tlg
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

    wait_on_amazon_sb_purchased_product_report = ExternalTaskSensor(task_id="wait_on_amazon_sb_purchased_product_report",
                                               external_dag_id="amazon_sb_purchased_product_report",
                                               external_task_id="end",
                                               allowed_states=["success"],
                                               timeout=60 * 60 * 4,
                                               on_failure_callback=alerts.send_failure_alert
                                               )


    create_dedup_all = PythonOperator(
        task_id="create_dedup_all",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/amazon/fact_amazon_ad_purchased_products/dedup_all.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_dq_dedupe_all = PythonOperator(
        task_id="run_dq_dedupe_all",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.STG_AMAZON_AD_PURCHASED_PRODUCTS",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.STG_AMAZON_AD_PURCHASED_PRODUCTS",
                field_list=['pk'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_null_check = PythonOperator(
        task_id="run_dq_null_check",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.STG_AMAZON_AD_PURCHASED_PRODUCTS",
            'test_name': 'null_check',
            'sql_query': tldq.gen_check_if_nulls(
                tb_name="$stage_db.STG_AMAZON_AD_PURCHASED_PRODUCTS",
                field_list=['budget_currency','campaign_name'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    merge_curated_amazon_ad_purchased_products = PythonOperator(
        task_id="merge_curated_amazon_ad_purchased_products",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/amazon/fact_amazon_ad_purchased_products/merge_all.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    update_fact_amazon_ad_purchased_products = PythonOperator(
        task_id="update_fact_amazon_ad_purchased_products",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/amazon/fact_amazon_ad_purchased_products/update_fact_amazon_ad_purchased_products.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    audit_curated_amazon_ad_purchased_products = PythonOperator(
        task_id="audit_curated_amazon_ad_purchased_products",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.fact_amazon_ad_purchased_products",
                   "wf_params": WF_PARAMS_EXPR,
                   "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                   "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                   },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    chain(
        begin,
        get_workflow_params,
        wait_on_amazon_sb_purchased_product_report,
        create_dedup_all,
        [run_dq_dedupe_all,run_dq_null_check],
        merge_curated_amazon_ad_purchased_products,
        update_fact_amazon_ad_purchased_products,
        audit_curated_amazon_ad_purchased_products,
        update_workflow_params,
        end,
    )
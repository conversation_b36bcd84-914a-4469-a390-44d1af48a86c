import logging
from datetime import datetime

from airflow import DAG
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.sensors.external_task import ExternalTaskSensor

import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts

DAG_ID = 'fact_amazon_ad_search_term'
TRANSFORM_BASE = 'ads/amazon/' + DAG_ID
RELEASE_DEF = '1'
BUILD_NUM = '01'

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"

log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2022, 5, 8),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'prashanjeet'},
    tags=['prashanjeet']
) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )


    # --- join tasks --------------

    join_all = PythonOperator(
        task_id="join_all",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/amazon/fact_amazon_ad_search_term/join_all.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_join_all = PythonOperator(
        task_id="run_dq_dedupe_join_all",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.dedup_fact_amazon_ad_search_term",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.dedup_fact_amazon_ad_search_term",
                field_list=['advertisement_pk'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_is_null_task = PythonOperator(
        task_id="run_dq_is_null",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': '$stage_db.dedup_fact_amazon_ad_search_term',
            'test_name': 'is_null',
            'sql_query': tldq.gen_check_if_nulls(
                tb_name='$stage_db.dedup_fact_amazon_ad_search_term',
                field_list=['report_date', 'ad_group_id', 'account_id', 'campaign_id'],
                hard_alert=True,
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    merge_all = PythonOperator(
        task_id="merge_all",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/amazon/fact_amazon_ad_search_term/merge_all.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_merge_all = PythonOperator(
        task_id="run_dq_merge_all",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$curated_db.fact_amazon_ad_search_terms",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$curated_db.fact_amazon_ad_search_terms",
                field_list=['advertisement_pk'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    # --- Audit the data checks ---

    import tasklib.audit as tla

    run_audit = PythonOperator(
        task_id="run_audit",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.fact_amazon_ad_search_terms",
                   "wf_params": WF_PARAMS_EXPR,
                   "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                   "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                   },
        on_failure_callback=alerts.send_failure_alert
    )

    # --- Update workflow status ---

    update_workflow_params = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )


begin = DummyOperator(task_id="begin")
end = DummyOperator(task_id="end")



wait_on_amazon_search_term_keyword_report = ExternalTaskSensor(task_id="wait_on_amazon_search_term_keyword_report",
                               external_dag_id="amazon_search_term_keyword_report",
                               external_task_id="end",
                               allowed_states=["success"],
                               poke_interval=60 * 3,
                               mode="reschedule",
                               timeout=60 * 60 * 4,
                               dag=dag
                               )

begin >> get_workflow_params >> [wait_on_amazon_search_term_keyword_report]   >> join_all >> [run_dq_join_all,run_dq_is_null_task] >> merge_all >> run_dq_merge_all >> run_audit >> update_workflow_params >> end

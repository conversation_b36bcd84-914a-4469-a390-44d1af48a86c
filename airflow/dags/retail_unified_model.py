from airflow import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.models import Variable
from airflow.models.baseoperator import chain
from airflow.sensors.external_task import ExternalTaskSensor
from datetime import datetime

import json
import logging
import tasklib.dq as tldq
import tasklib.sql as tlsql
from tasklib import alerts

log = logging.getLogger(__name__)

DAG_ID = 'retail_unified_model'
DQ_KEYS = ['snapshot_date', 'asin', 'marketplace', 'country_code']
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 */4 * * *') # Default to hourly runs
NUM_DAYS_TO_PROCESS = Variable.get("rum_num_days_to_process", 30) # Default to 30 days
WF_PARAMS_EXPR = "{}" # Dummy parameter passed
WF_PARAMS_EXPR_MOD = json.dumps({"num_days_to_process": NUM_DAYS_TO_PROCESS})


with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022, 11, 29),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name':DAG_ID.upper()
            ,'author':'ruchira, harshad'},
    tags=['Ruchira', 'Harshad'],
) as dag:

    #create a snapshot of the data delay table
    create_data_delay_snapshot = PythonOperator(
        task_id="create_data_delay_snapshot",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_data_delay_snapshot.sql",
            "wf_params":WF_PARAMS_EXPR_MOD,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    #fact_all_orders kpis
    create_stg_kpi_fact_all_orders = PythonOperator(
        task_id="create_stg_kpi_fact_all_orders",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_kpi_fact_all_orders.sql",
            "wf_params":WF_PARAMS_EXPR_MOD,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_uniqueness_check_stg_fact_orders = PythonOperator(
        task_id="run_uniqueness_check_stg_fact_orders",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_all_orders_base_kpi", 
                field_list=['asin', 'brand_code', 'country_code', 'marketplace', 'sku', 'snapshot_date'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    #fact_all_pricing kpis
    create_stg_kpi_fact_all_pricing = PythonOperator(
        task_id="create_stg_kpi_fact_all_pricing",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_kpi_fact_all_pricing.sql",
            "wf_params":WF_PARAMS_EXPR_MOD,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_uniqueness_check_stg_fact_all_pricing = PythonOperator(
        task_id="run_uniqueness_check_stg_fact_all_pricing",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_all_pricing_kpi", 
                field_list=['brand_code', 'country_code', 'marketplace', 'asin', 'sku', 'snapshot_date'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    #inventory kpis
    create_stg_kpi_fact_inventory = PythonOperator(
        task_id="create_stg_kpi_fact_inventory",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_kpi_fact_inventory.sql",
            "wf_params":WF_PARAMS_EXPR_MOD,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_uniqueness_check_stg_fact_inventory = PythonOperator(
        task_id="run_uniqueness_check_stg_fact_inventory",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_amazon_inventory_kpi", 
                field_list=['snapshot_date', 'asin', 'brand_code', 'marketplace', 'country_code'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    #bsr kpi
    create_stg_kpi_fact_amazon_bsr = PythonOperator(
        task_id="create_stg_kpi_fact_amazon_bsr",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_kpi_fact_amazon_bsr.sql",
            "wf_params":WF_PARAMS_EXPR_MOD,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_uniqueness_check_stg_fact_amazon_bsr = PythonOperator(
        task_id="run_uniqueness_check_stg_fact_amazon_bsr",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_amazon_bsr_kpi", 
                field_list=DQ_KEYS,
                hard_alert=True,
            )
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    #sales traffic report kpis
    create_stg_kpi_fact_all_item_sales_traffic_report = PythonOperator(
        task_id="create_stg_kpi_fact_all_item_sales_traffic_report",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_kpi_fact_all_item_sales_traffic_report.sql",
            "wf_params":WF_PARAMS_EXPR_MOD,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_uniqueness_check_stg_fact_all_item_sales_traffic_report = PythonOperator(
        task_id="run_uniqueness_check_stg_fact_all_item_sales_traffic_report",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_amazon_sales_traffic_report_kpi", 
                field_list=DQ_KEYS,
                hard_alert=True,
            )
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    #ad asins kpis
    create_stg_kpi_fact_amazon_ad_asins = PythonOperator(
        task_id="create_stg_kpi_fact_amazon_ad_asins",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_kpi_fact_amazon_ad_asins.sql",
            "wf_params":WF_PARAMS_EXPR_MOD,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_uniqueness_check_stg_fact_amazon_ad_asins = PythonOperator(
        task_id="run_uniqueness_check_stg_fact_amazon_ad_asins",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_amazon_ad_asins_kpi", 
                field_list=DQ_KEYS,
                hard_alert=True,
            )
        },
        on_failure_callback=alerts.send_failure_alert,
    )  

    #dos kpis
    create_stg_kpi_dos = PythonOperator(
        task_id="create_stg_kpi_dos",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_kpi_dos.sql",
            "wf_params":WF_PARAMS_EXPR_MOD,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_uniqueness_check_stg_dos = PythonOperator(
        task_id="run_uniqueness_check_stg_dos",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_dos_kpi", 
                field_list=['snapshot_date', 'item_number', 'marketplace', 'country_code'],
                hard_alert=True,
            )
        },
        on_failure_callback=alerts.send_failure_alert,
    )  

    #amazon avg ratings kpi
    create_stg_kpi_fact_amazon_avg_ratings = PythonOperator(
        task_id="create_stg_kpi_fact_amazon_avg_ratings",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_kpi_fact_amazon_avg_ratings.sql",
            "wf_params":WF_PARAMS_EXPR_MOD,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_uniqueness_check_stg_fact_amazon_avg_ratings = PythonOperator(
        task_id="run_uniqueness_check_stg_fact_amazon_avg_ratings",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_amazon_avg_ratings_kpi", 
                field_list=['snapshot_date', 'asin', 'marketplace', 'country_code', 'parent_asin'],
                hard_alert=True,
            )
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    #fact_all_cp kpis
    create_stg_kpi_fact_all_cp = PythonOperator(
        task_id="create_stg_kpi_fact_all_cp",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_kpi_fact_all_cp.sql",
            "wf_params":WF_PARAMS_EXPR_MOD,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_uniqueness_check_stg_fact_all_cp = PythonOperator(
        task_id="run_uniqueness_check_stg_fact_all_cp",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_all_cp_kpi", 
                field_list=['asin', 'country_code', 'marketplace', 'sku', 'snapshot_date'],
                hard_alert=True,
            )
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    #fact_amazon_promotions kpis
    create_stg_kpi_fact_amazon_promotions = PythonOperator(
        task_id="create_stg_kpi_fact_amazon_promotions",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_kpi_fact_amazon_promotions.sql",
            "wf_params":WF_PARAMS_EXPR_MOD,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_uniqueness_check_stg_fact_amazon_promotions = PythonOperator(
        task_id="run_uniqueness_check_stg_fact_amazon_promotions",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_amazon_promotions_kpi", 
                field_list=['country_code', 'marketplace', 'asin', 'snapshot_date'],
                hard_alert=True,
            )
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    #fact_amazon_listings
    create_stg_kpi_fact_amazon_listings = PythonOperator(
        task_id="create_stg_kpi_fact_amazon_listings",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_kpi_fact_amazon_listings.sql",
            "wf_params":WF_PARAMS_EXPR_MOD,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )
    
    run_uniqueness_check_stg_fact_amazon_listings = PythonOperator(
        task_id="run_uniqueness_check_stg_fact_amazon_listings",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_amazon_listings_kpi", 
                field_list=['country_code', 'marketplace', 'sku', 'asin','snapshot_date'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    #fact_amazon_deal_eligibility kpi
    create_stg_kpi_fact_amazon_deal_eligibility = PythonOperator(
        task_id="create_stg_kpi_fact_amazon_deal_eligibility",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_kpi_fact_amazon_deal_eligibility.sql",
            "wf_params":WF_PARAMS_EXPR_MOD,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_uniqueness_check_stg_fact_amazon_deal_eligibility = PythonOperator(
        task_id="run_uniqueness_check_stg_fact_amazon_deal_eligibility",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_amazon_deal_eligibility_kpi", 
                field_list=DQ_KEYS,
                hard_alert=True,
            )
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    #stg_fact_shopify_sku v1.0
    create_stg_fact_shopify_sku = PythonOperator(
        task_id="create_stg_fact_shopify_sku",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_fact_shopify_sku.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    create_stg_fact_all_sku = PythonOperator(
        task_id="create_stg_fact_all_sku",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_fact_all_sku.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_uniqueness_check_fact = PythonOperator(
        task_id="run_uniqueness_check_fact",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.STG_fact_all_sku", 
                field_list=['asin', 'brand_code_updated', 'country_code', 'marketplace', 'sku', 'snapshot_date'],
                hard_alert=True,
            )
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    run_dq_stg_fact_all_sku = PythonOperator(
        task_id="run_dq_stg_fact_all_sku",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'run_dq_stg_fact_all_sku',
            'query_file': "retail_unified_model/dq_stg_fact_all_sku.yaml"
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    merge_stg_fact_all_sku = PythonOperator(
        task_id="merge_stg_fact_all_sku",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/merge_stg_fact_all_sku.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    update_rolling_calculations_stg_fact_all_sku = PythonOperator(
        task_id="update_rolling_calculations_stg_fact_all_sku",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/update_rolling_calculations_stg_fact_all_sku.sql",
            "wf_params":WF_PARAMS_EXPR_MOD,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_dq_merge_stg_fact_all_sku = PythonOperator(
        task_id="run_dq_merge_stg_fact_all_sku",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'run_dq_merge_stg_fact_all_sku',
            'query_file': "retail_unified_model/dq_merge_stg_fact_all_sku.yaml"
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    create_stg_asin_rollup = PythonOperator(
        task_id="create_stg_asin_rollup",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/create_stg_asin_rollup.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    merge_fact_all_item_asin_rollup_daily = PythonOperator(
        task_id="merge_fact_all_item_asin_rollup_daily",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"retail_unified_model/merge_fact_all_item_asin_rollup_daily.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_dq_fact_all_item_rollup_daily = PythonOperator(
        task_id="run_dq_fact_all_item_rollup_daily",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'run_dq_fact_all_item_rollup_daily',
            'query_file': "retail_unified_model/dq_fact_all_item_rollup.yaml"
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    run_dq_fact_all_asin_rollup = PythonOperator(
        task_id="run_dq_fact_all_asin_rollup",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'run_dq_fact_all_asin_rollup',
            'query_file': "retail_unified_model/dq_fact_all_asin_rollup.yaml"
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    import tasklib.audit as tla
    run_audit = PythonOperator(
        task_id="run_audit",
        python_callable=tla.run_audit,
        op_kwargs={
            "table_name": "$curated_db.fact_all_item_rollup",
            "wf_params": WF_PARAMS_EXPR,
            "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
            "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
        }, 
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )
    
    ################ TODO:Remove block when other rollups are ready ################
    create_stg_brand_rollup = DummyOperator( 
        task_id="create_stg_brand_rollup",
    )

    merge_fact_all_item_brand_rollup_daily = DummyOperator(
        task_id="merge_fact_all_item_brand_rollup_daily",
    )

    create_stg_ns_item_number_rollup = DummyOperator(
        task_id="create_stg_ns_item_number_rollup",
    )

    merge_fact_all_item_ns_item_number_rollup_daily = DummyOperator(
        task_id="merge_fact_all_item_ns_item_number_rollup_daily",
    )

    merge_fact_product_aggregate_kpi = DummyOperator(
        task_id="merge_fact_product_aggregate_kpi",
    )
    ################################################################################

    ################ TODO:Enable block when other rollups are ready ################
    # create_stg_brand_rollup = PythonOperator(
    #     task_id="create_stg_brand_rollup",
    #     python_callable=tlsql.run_query_file,
    #     op_kwargs={"connection":"Snowflake","sql_file":"retail_unified_model/create_stg_brand_rollup.sql", "wf_params": WF_PARAMS_EXPR},
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    # )

    # merge_fact_all_item_brand_rollup_daily = PythonOperator(
    #     task_id="merge_fact_all_item_brand_rollup_daily",
    #     python_callable=tlsql.run_query_file,
    #     op_kwargs={"connection":"Snowflake","sql_file":"retail_unified_model/merge_fact_all_item_brand_rollup_daily.sql", "wf_params": WF_PARAMS_EXPR},
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    # )

    # create_stg_ns_item_number_rollup = PythonOperator(
    #     task_id="create_stg_ns_item_number_rollup",
    #     python_callable=tlsql.run_query_file,
    #     op_kwargs={"connection":"Snowflake","sql_file":"retail_unified_model/create_stg_ns_item_number_rollup.sql", "wf_params": WF_PARAMS_EXPR},
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    # )

    # merge_fact_all_item_ns_item_number_rollup_daily = PythonOperator(
    #     task_id="merge_fact_all_item_ns_item_number_rollup_daily",
    #     python_callable=tlsql.run_query_file,
    #     op_kwargs={"connection":"Snowflake","sql_file":"retail_unified_model/merge_fact_all_item_ns_item_number_rollup_daily.sql", "wf_params": WF_PARAMS_EXPR},
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    # )

    # merge_fact_product_aggregate_kpi = PythonOperator(
    #     task_id="task_merge_fact_product_aggregate_kpi",
    #     python_callable=tlsql.run_query_file,
    #     op_kwargs={"connection":"Snowflake","sql_file":"retail_unified_model/merge_fact_product_aggregate_kpi.sql", "wf_params": WF_PARAMS_EXPR},
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    # )
    #dq audits etc
    ################################################################################

    begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
    end = DummyOperator(task_id="end")
    end_stage_creation = DummyOperator(task_id="end_stage_creation",depends_on_past=True,wait_for_downstream=True)
    start_stage_creation = DummyOperator(task_id="start_stage_creation",depends_on_past=True,wait_for_downstream=True)
    

    #Assemble the dag
    chain(
        begin,  
        create_data_delay_snapshot,
        start_stage_creation,
        [
            create_stg_kpi_fact_all_orders, create_stg_kpi_fact_all_pricing, create_stg_kpi_fact_inventory,
            create_stg_kpi_fact_amazon_bsr, create_stg_kpi_fact_all_item_sales_traffic_report,
            create_stg_kpi_fact_amazon_ad_asins, create_stg_kpi_dos,
            create_stg_kpi_fact_amazon_avg_ratings, create_stg_kpi_fact_all_cp,
            create_stg_kpi_fact_amazon_promotions, create_stg_kpi_fact_amazon_listings,
            create_stg_kpi_fact_amazon_deal_eligibility,
        ],
        [
            run_uniqueness_check_stg_fact_orders, run_uniqueness_check_stg_fact_all_pricing, run_uniqueness_check_stg_fact_inventory,
            run_uniqueness_check_stg_fact_amazon_bsr, run_uniqueness_check_stg_fact_all_item_sales_traffic_report,
            run_uniqueness_check_stg_fact_amazon_ad_asins, run_uniqueness_check_stg_dos,
            run_uniqueness_check_stg_fact_amazon_avg_ratings, run_uniqueness_check_stg_fact_all_cp,
            run_uniqueness_check_stg_fact_amazon_promotions, run_uniqueness_check_stg_fact_amazon_listings,
            run_uniqueness_check_stg_fact_amazon_deal_eligibility,
        ],
        end_stage_creation,
        create_stg_fact_shopify_sku,
        create_stg_fact_all_sku,
        run_uniqueness_check_fact,
        run_dq_stg_fact_all_sku,
        merge_stg_fact_all_sku,
        update_rolling_calculations_stg_fact_all_sku,
        run_dq_merge_stg_fact_all_sku,
        [create_stg_asin_rollup, create_stg_brand_rollup, create_stg_ns_item_number_rollup],
        [merge_fact_all_item_asin_rollup_daily, merge_fact_all_item_brand_rollup_daily, merge_fact_all_item_ns_item_number_rollup_daily],
        run_dq_fact_all_item_rollup_daily,
        run_dq_fact_all_asin_rollup,
        merge_fact_product_aggregate_kpi,
        run_audit,
        end    
    )

"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'walmart_orders_and_returns'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@hourly')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2025, 1, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'ayush'},
    tags=['ayush', 'Raptor']
) as dag:
    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    with TaskGroup(group_id='load_walmart_orders_report') as tg_walmart_orders_report:
        list_s3_files_walmart_orders_report_task = PythonOperator(
            task_id="list_s3_files_walmart_orders_report",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "orders/walmart/list_s3_walmart_orders_report.yaml",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_walmart_orders_report_task = DummyOperator(task_id="begin_insert_walmart_orders_report")
        skip_insert_walmart_orders_report_task = DummyOperator(task_id="skip_insert_walmart_orders_report")
        end_insert_walmart_orders_report_task = DummyOperator(task_id="end_insert_walmart_orders_report")
        end_walmart_orders_report_task = DummyOperator(
            task_id="end_walmart_orders_report",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_walmart_orders_report_task = BranchPythonOperator(
            task_id='check_new_files_found_walmart_orders_report',
            python_callable=check_for_new_files,
            op_kwargs={
                "args_file": "orders/walmart/s3_to_snowflake_walmart_orders_report.yaml",
                "skip_task_id": "load_walmart_orders_report.skip_insert_walmart_orders_report",
                "next_task_id": "load_walmart_orders_report.begin_insert_walmart_orders_report"
            },
        )

        s3_to_snowflake_walmart_orders_report_task = PythonOperator(
            task_id="s3_to_snowflake_walmart_orders_report",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "orders/walmart/s3_to_sf_raw_walmart_orders_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_walmart_orders_report_task = PythonOperator(
            task_id="insert_log_walmart_orders_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/walmart/insert_log_walmart_orders_report.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_walmart_orders_report_task = PythonOperator(
            task_id="dedupe_walmart_orders_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/walmart/dedupe_walmart_orders_report.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_walmart_orders_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_walmart_orders_report',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_walmart_orders_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_walmart_orders_report',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_walmart_orders_report_task = PythonOperator(
            task_id="merge_stage_walmart_orders_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/walmart/merge_stage_walmart_orders_report.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_walmart_orders_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_walmart_orders_report',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_walmart_orders_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_walmart_orders_report',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        create_stg_walmart_orders_task = PythonOperator(
            task_id="create_stg_walmart_orders",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/walmart/create_stg_walmart_orders.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_stg_fact_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_stg_fact_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.stg_walmart_orders',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.stg_walmart_orders',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_fact_walmart_orders_task = PythonOperator(
            task_id="merge_fact_walmart_orders",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "orders/walmart/merge_fact_walmart_orders.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_fact_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_fact_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.fact_walmart_orders',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$curated_db.fact_walmart_orders',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_walmart_orders_report_task = PythonOperator(
            task_id="run_audit_walmart_orders_report",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_walmart_orders",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_walmart_orders_report_task >>
            check_new_files_found_walmart_orders_report_task >>
            [begin_insert_walmart_orders_report_task, skip_insert_walmart_orders_report_task]
        )

        (
            begin_insert_walmart_orders_report_task >>
            s3_to_snowflake_walmart_orders_report_task >>
            insert_log_walmart_orders_report_task >>
            dedupe_walmart_orders_report_task >>
            (run_dq_is_null_dedupe_pk_hard_task, run_dq_is_unique_dedupe_pk_hard_task) >>
            merge_stage_walmart_orders_report_task >>
            (run_dq_is_null_merge_pk_hard_task, run_dq_is_unique_merge_pk_hard_task) >>
            create_stg_walmart_orders_task >>
            run_dq_is_unique_stg_fact_pk_hard_task >>
            merge_fact_walmart_orders_task >>
            run_dq_is_unique_fact_pk_hard_task >>
            run_audit_walmart_orders_report_task >>
            end_insert_walmart_orders_report_task >>
            end_walmart_orders_report_task
        )

        skip_insert_walmart_orders_report_task >> end_walmart_orders_report_task

    #  --- Load 3pl_walmart_wfs_inventory_report ---
    with TaskGroup(group_id='load_walmart_returns_report') as tg_walmart_returns_report:
        list_s3_files_walmart_returns_report_task = PythonOperator(
            task_id="list_s3_files_walmart_returns_report",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "returns/walmart/list_s3_walmart_returns_report.yaml",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_walmart_returns_report_task = DummyOperator(task_id="begin_insert_walmart_returns_report")
        skip_insert_walmart_returns_report_task = DummyOperator(task_id="skip_insert_walmart_returns_report")
        end_insert_walmart_returns_report_task = DummyOperator(task_id="end_insert_walmart_returns_report")
        end_walmart_returns_report_task = DummyOperator(
            task_id="end_walmart_returns_report",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_walmart_returns_report_task = BranchPythonOperator(
            task_id='check_new_files_found_walmart_returns_report',
            python_callable=check_for_new_files,
            op_kwargs={
                "args_file": "returns/walmart/s3_to_snowflake_walmart_returns_report.yaml",
                "skip_task_id": "load_walmart_returns_report.skip_insert_walmart_returns_report",
                "next_task_id": "load_walmart_returns_report.begin_insert_walmart_returns_report"
            },
        )

        s3_to_snowflake_walmart_returns_report_task = PythonOperator(
            task_id="s3_to_snowflake_walmart_returns_report",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "returns/walmart/s3_to_sf_raw_walmart_returns_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_walmart_returns_report_task = PythonOperator(
            task_id="insert_log_walmart_returns_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/walmart/insert_log_walmart_returns_report.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_walmart_returns_report_task = PythonOperator(
            task_id="dedupe_walmart_returns_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/walmart/dedupe_walmart_returns_report.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_walmart_returns_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_walmart_returns_report',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_walmart_returns_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_walmart_returns_report',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_walmart_returns_report_task = PythonOperator(
            task_id="merge_stage_walmart_returns_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/walmart/merge_stage_walmart_returns_report.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_walmart_returns_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_walmart_returns_report',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_walmart_returns_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_walmart_returns_report',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        create_stg_walmart_returns_task = PythonOperator(
            task_id="create_stg_walmart_returns",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/walmart/create_stg_walmart_returns.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_stg_fact_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_stg_fact_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.stg_walmart_returns',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.stg_walmart_returns',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_fact_walmart_returns_task = PythonOperator(
            task_id="merge_fact_walmart_returns",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/walmart/merge_fact_walmart_returns.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_fact_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_fact_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.fact_walmart_returns',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$curated_db.fact_walmart_returns',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_walmart_returns_report_task = PythonOperator(
            task_id="run_audit_walmart_returns_report",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_walmart_returns",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_walmart_returns_report_task >>
            check_new_files_found_walmart_returns_report_task >>
            [begin_insert_walmart_returns_report_task, skip_insert_walmart_returns_report_task]
        )

        (
            begin_insert_walmart_returns_report_task >>
            s3_to_snowflake_walmart_returns_report_task >>
            insert_log_walmart_returns_report_task >>
            dedupe_walmart_returns_report_task >>
            (run_dq_is_null_dedupe_pk_hard_task, run_dq_is_unique_dedupe_pk_hard_task) >>
            merge_stage_walmart_returns_report_task >>
            (run_dq_is_null_merge_pk_hard_task, run_dq_is_unique_merge_pk_hard_task) >>
            create_stg_walmart_returns_task >>
            run_dq_is_unique_stg_fact_pk_hard_task >>
            merge_fact_walmart_returns_task >>
            run_dq_is_unique_fact_pk_hard_task >>
            run_audit_walmart_returns_report_task >>
            end_insert_walmart_returns_report_task >>
            end_walmart_returns_report_task
        )

        skip_insert_walmart_returns_report_task >> end_walmart_returns_report_task

 
    # ---- Main branch ----
    chain(
        begin,
        get_workflow_parameters,
        [
            tg_walmart_orders_report,
            tg_walmart_returns_report
        ],
        update_workflow_parameters,
        end
    )
import logging
import warnings
from datetime import datetime
from airflow import DAG
from airflow.models import Variable
from airflow.operators.python import Python<PERSON>perator, BranchPythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.utils.trigger_rule import TriggerRule
from tasklib import alerts

warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)

DAG_ID = 'asin_buy_box_status_event_based_replication'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 */4 * * *')  # Default to hourly runs
WF_PARAMS_EXPR = "{}"  # Dummy parameter passed

with DAG(
        dag_id=DAG_ID,
        start_date=datetime(2023, 10, 2),
        schedule_interval=DAG_SCHEDULE,
        catchup=False,
        max_active_runs=1,
        params={"author": "nagesh"},
        tags=['Nagesh'],
) as dag:
    import tasklib.replication as tlr

    replicate_asin_buy_box_status_event_based_pg_to_sf_task = PythonOperator(
        task_id="replicate_promotion_creation_pg_to_sf_task",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/pg_to_sf/asin_buy_box_status_event_based.yaml"}
    )

    begin_task = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end_task = DummyOperator(
        task_id="end",
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    begin_task >> replicate_asin_buy_box_status_event_based_pg_to_sf_task >> end_task

import logging
from airflow import DAG
from datetime import datetime
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON><PERSON>hon<PERSON>perator, PythonOperator
from airflow.models.baseoperator import chain
from airflow.sensors.external_task import ExternalTaskSensor
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

from tasklib.loader import check_for_new_files

log = logging.getLogger(__name__)

S3_TO_SNOWFLAKE_YAML = 'amazon_financial_events_refunds_shipments_hourly/s3_to_snowflake.yaml'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id="amazon_refunds_hourly",
    start_date=datetime(2022, 5, 8),
    schedule_interval="0 */4 * * *",
    catchup=False,
    max_active_runs=1,
    params={
        "workflow_name": "AMAZON_REFUNDS_HOURLY",
        "author": "ayush"
    },
    tags=['Ayush'],
) as dag:
    import tasklib.workflow as tlw
    import tasklib.s3 as tls3
    import tasklib.glue as tlg
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    list_s3_modified_files = PythonOperator(
        task_id="task_list_s3_modified_files",
        python_callable=tls3.list_s3_modified_files,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "args_file": "amazon_financial_events_refunds_shipments_hourly/s3_list_folders.yaml",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    skip_refunds = DummyOperator(task_id='skip_refunds')

    check_new_files_found_task = BranchPythonOperator(
        task_id='check_new_files_found',
        python_callable=check_for_new_files,
        op_kwargs={
            'args_file': S3_TO_SNOWFLAKE_YAML,
            'skip_task_id': 'skip_refunds',
            'next_task_id': 'amazon_financial_events_refunds_shipments.begin_ingestion_amazon_financial_events_refunds_shipments',
            'args_file_second': 'amazon_financial_events_refunds_shipments_hourly/s3_to_snowflake_goessor.yaml',
        }
    )

    list_s3_modified_files_goessor = PythonOperator(
        task_id="task_list_s3_modified_files_goessor",
        python_callable=tls3.list_s3_modified_files,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "args_file": "amazon_financial_events_refunds_shipments_hourly/s3_list_folders_goessor.yaml",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    with TaskGroup(
        group_id="amazon_financial_events_refunds_shipments"
    ) as amazon_financial_events_refunds_shipments:

        begin_ingestion_amazon_financial_events_refunds_shipments = DummyOperator(
            task_id="begin_ingestion_amazon_financial_events_refunds_shipments"
        )
        end_ingestion_amazon_financial_events_refunds_shipments = DummyOperator(
            task_id="end_ingestion_amazon_financial_events_refunds_shipments"
        )

        from tasklib.loader import S3ToSnowflake
        load_obj = S3ToSnowflake()

        # from data_transformations.s3_snowflake.s3_sf_helper import create_s3_snowflake_script
        transfer_s3_to_snowflake = PythonOperator(
            task_id="task_transfer_s3_to_snowflake",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": 'amazon_financial_events_refunds_shipments_hourly/s3_to_sf_raw_amazon_financialevents_refunds_shipments.yaml'},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        transfer_s3_to_snowflake_goessor = PythonOperator(
            task_id="task_transfer_s3_to_snowflake_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": 'amazon_financial_events_refunds_shipments_hourly/s3_to_sf_raw_amazon_financialevents_refunds_shipments_goessor.yaml'},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        create_dedup_amazon_financial_events_refunds_shipments = PythonOperator(
            task_id="create_dedup_amazon_financial_events_refunds_shipments",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "refunds/amazon/create_dedup_amazon_financial_events_refunds_shipments.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_stg_amazon_financial_events_refunds_shipments = PythonOperator(
            task_id="create_stg_amazon_financial_events_refunds_shipments",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "refunds/amazon/create_stg_amazon_financial_events_refunds_shipments.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        merge_amazon_financial_events_refunds_shipments = PythonOperator(
            task_id="merge_amazon_financial_events_refunds_shipments",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "refunds/amazon/merge_amazon_financial_events_refunds_shipments.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_tests_amazon_financial_events_refunds_shipments = PythonOperator(
            task_id="run_dq_tests_amazon_financial_events_refunds_shipments",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "323debe3-510d-4fa4-a976-3cf1a2eb1f93"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            begin_ingestion_amazon_financial_events_refunds_shipments
            >> transfer_s3_to_snowflake
            >> transfer_s3_to_snowflake_goessor
            >> create_dedup_amazon_financial_events_refunds_shipments
            >> create_stg_amazon_financial_events_refunds_shipments
            >> merge_amazon_financial_events_refunds_shipments
            >> run_dq_tests_amazon_financial_events_refunds_shipments
            >> end_ingestion_amazon_financial_events_refunds_shipments
        )

    with TaskGroup(group_id="amazon_fact_all_refunds") as amazon_fact_all_refunds:

        begin_fact_all_refunds = DummyOperator(
            task_id="begin_fact_all_refunds")
        end_fact_all_refunds = DummyOperator(task_id="end_fact_all_refunds")

        create_stg_amazon_refunds = PythonOperator(
            task_id="create_stg_amazon_refunds",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "refunds/amazon/create_stg_amazon_refunds.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_park_amazon_refunds_with_no_orders = PythonOperator(
            task_id="create_park_amazon_refunds_with_no_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "refunds/amazon/create_park_amazon_refunds_with_no_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_tests_parked_refunds = PythonOperator(
            task_id="run_dq_tests_parked_refunds",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "ce272b9d-8693-4808-8e67-e184ffb5298d"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        create_distinct_orders_with_refunds = PythonOperator(
            task_id="create_distinct_orders_with_refunds",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "refunds/amazon/create_distinct_orders_with_refunds.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_stg_amazon_fact_all_refunds = PythonOperator(
            task_id="create_stg_amazon_fact_all_refunds",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "refunds/amazon/create_stg_amazon_fact_all_refunds.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_tests_stg_amazon_fact_all_refunds = PythonOperator(
            task_id="run_dq_tests_stg_amazon_fact_all_refunds",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "0b0eebef-5eec-49a3-a03c-5ffd6c69c2cc"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_fact_all_refunds = PythonOperator(
            task_id="merge_fact_all_refunds",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "refunds/amazon/merge_fact_all_refunds.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        update_fact_all_refunds = PythonOperator(
            task_id="update_fact_all_refunds",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "refunds/amazon/update_fact_all_refunds.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_tests_fact_all_refunds = PythonOperator(
            task_id="run_dq_tests_fact_all_refunds",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "e592c6c4-639d-487c-a2ad-ca322314f11d"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_fact_all_refunds = PythonOperator(
            task_id="run_audit_fact_all_refunds",
            python_callable=tla.run_audit,
            op_kwargs={"table_name": "$curated_db.fact_all_refunds",
                       "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            begin_fact_all_refunds
            >> create_stg_amazon_refunds
            >> create_park_amazon_refunds_with_no_orders
            >> create_distinct_orders_with_refunds
            >> run_dq_tests_parked_refunds
            >> create_stg_amazon_fact_all_refunds
            >> run_dq_tests_stg_amazon_fact_all_refunds
            >> merge_fact_all_refunds
            >> update_fact_all_refunds
            >> run_dq_tests_fact_all_refunds
            >> run_audit_fact_all_refunds
            >> end_fact_all_refunds
        )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    wait_on_amazon_orders = ExternalTaskSensor(task_id="wait_on_amazon_orders",
                                               external_dag_id="amazon_orders",
                                               external_task_id="end",
                                               allowed_states=["success"],
                                               timeout=60 * 60 * 4,
                                               on_failure_callback=alerts.send_failure_alert
                                               )

    end = DummyOperator(task_id="end")

    chain(
        get_workflow_params,
        wait_on_amazon_orders,
        [list_s3_modified_files, list_s3_modified_files_goessor],
        check_new_files_found_task,
        [amazon_financial_events_refunds_shipments, skip_refunds],
    )
    amazon_financial_events_refunds_shipments >> amazon_fact_all_refunds
    [amazon_fact_all_refunds, skip_refunds] >> update_workflow_params >> end

from airflow import DAG
from datetime import datetime
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.utils.task_group import TaskGroup
import logging
from tasklib import alerts
from tasklib.loader import S3ToSnowflake
from airflow.utils.trigger_rule import TriggerRule
import tasklib.workflow as tlw
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.dq as tldq
import tasklib.audit as tla
import pendulum

log = logging.getLogger(__name__)
load_obj = S3ToSnowflake()

DAG_ID = "fact_amazon_promotions"

WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

def check_if_data_ready(wf_params:str=None,fact_task_id:str=None,skip_task_id:str=None,tb_name:str=None):
    '''
    Check if the scraper data is available for the day.
    If yes, run the next steps 
    If no, skip 
    Utilize utc timezone for deals table, convert to pst for coupons and peds
    '''
    from db_connectors.sf_connector import Snowflake
    from helpers.helper import resolve_db_variables
    import ast
    
    sf_client = Snowflake()
    wf_params_dict=ast.literal_eval(wf_params)
    snapshot_date = wf_params_dict.get('wf_start_ts_iso_utc')
    if "load_amazon_deals.task_create_stg_fact_amazon_deals" in fact_task_id:
        snapshot_date = snapshot_date
    else:
        snapshot_date = pendulum.parse(snapshot_date,tz = 'America/Los_Angeles')
    query = f"""SELECT * FROM {tb_name} WHERE report_fetched_and_loaded_at::date >='{snapshot_date}'::date LIMIT 10"""
    query = resolve_db_variables(query)
    _, num_rows = sf_client.get_data(query)
    if num_rows > 0:
        return fact_task_id
    return skip_task_id

with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022,10,6),
    schedule_interval='@hourly',
    catchup=False,
    max_active_runs=1,
    params={
            "workflow_name": DAG_ID.upper(),
            "author": "ruchira, himanshu_vijay"
           },
    tags=["Goessor"],
) as dag:  

    get_workflow_params=PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    update_brand_netsuite_item_number = PythonOperator(
            task_id="update_brand_netsuite_item_number",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "promotions/amazon/update_brand_netsuite_item_number.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
    )

    create_stg_fact_amazon_promotions = PythonOperator(
            task_id="create_stg_fact_amazon_promotions",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "promotions/amazon/create_stg_fact_amazon_promotions.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
    )

    run_uniqueness_check_stg_fact_amazon_promotions=PythonOperator(
        task_id="task_run_uniqueness_check_stg_fact_amazon_promotions",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.stg_fact_amazon_promotions",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.stg_fact_amazon_promotions", 
                field_list=['SELLER_ID','MARKETPLACE','PROMOTION_ID','ITEM_ID','SKU','START_DATE','END_DATE'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    merge_fact_all_promotions = PythonOperator(
        task_id="merge_fact_all_promotions",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake",
                    "sql_file": "promotions/amazon/merge_fact_all_promotions.sql",
                    "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
    end = DummyOperator(task_id="end")
    all_load_complete = DummyOperator(task_id="all_load_complete",trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS)

    with TaskGroup(group_id='load_amazon_deals') as load_amazon_deals:
        list_s3_modified_files_deals=PythonOperator(
            task_id="task_list_s3_modified_files_deals",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={"args_file": f"fact_amazon_deals/s3_list_folders.yaml", "wf_params":WF_PARAMS_EXPR },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # dummy operator for branching
        begin_insert_amazon_deals_task = DummyOperator(task_id="begin_insert_amazon_deals")
        end_insert_amazon_deals_task = DummyOperator(task_id="end_insert_amazon_deals")
        skip_fact_deals = DummyOperator(task_id="skip_fact_deals")

        transfer_s3_to_snowflake_deals = PythonOperator(
            task_id="task_transfer_s3_to_snowflake_deals",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": f"fact_amazon_deals/s3_to_sf_raw_scrapped_deals.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_modified_files_deals_goessor = PythonOperator(
            task_id="task_list_s3_modified_files_deals_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={"args_file": f"fact_amazon_deals/s3_list_folders_goessor.yaml", "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        transfer_s3_to_snowflake_deals_goessor = PythonOperator(
            task_id="task_transfer_s3_to_snowflake_deals_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": f"fact_amazon_deals/s3_to_sf_raw_scrapped_deals_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_null_checks_raw_deals = PythonOperator(
            task_id="task_run_null_checks_raw_deals",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$raw_db.RAW_SCRAPPED_DEALS",
                'test_name': 'identifiers_not_nulls',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name="$raw_db.RAW_SCRAPPED_DEALS",
                    field_list=['SELLER_ID', 'CAMPAIGN_ID', 'SKU', 'ASIN','CAMPAIGN_STATUS', 'DEAL_TYPE', 'COUNTRY'],
                    hard_alert=True
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_uniqueness_check_raw_deals = PythonOperator(
            task_id="task_run_uniqueness_check_raw_deals",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$raw_db.RAW_SCRAPPED_DEALS",
                'test_name': 'pk_not_duplicate',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$raw_db.RAW_SCRAPPED_DEALS", 
                    field_list=['SELLER_ID', 'CAMPAIGN_ID', 'SKU', 'ASIN', 'START_DATE_EPOCH_MILLIS', 'END_DATE_EPOCH_MILLIS', 
                                'REPORT_FETCHED_AND_LOADED_AT'],
                    hard_alert=False
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        insert_raw_scrapped_deals_log = PythonOperator(
            task_id="task_insert_raw_scrapped_deals_log",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake", 
                "sql_file": f"promotions/amazon/insert_raw_scrapped_deals_log.sql", 
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        merge_stg_scraped_deals = PythonOperator(
            task_id="task_merge_stg_scraped_deals",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": f"promotions/amazon/merge_stg_scraped_deals.sql", 
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_null_checks_stg_deals = PythonOperator(
            task_id="task_run_null_checks_stg_deals",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$scs_db.STG_SCRAPPED_DEALS",
                'test_name': 'start_end_time_not_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name="$scs_db.STG_SCRAPPED_DEALS",
                    field_list=['START_TIME', 'END_TIME'],
                    hard_alert=True
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_uniqueness_check_staging = PythonOperator(
            task_id="task_run_uniqueness_check_staging",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$scs_db.STG_SCRAPPED_DEALS",
                'test_name': 'pk_not_duplicate',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$scs_db.STG_SCRAPPED_DEALS", 
                    field_list=['SELLER_ID', 'CAMPAIGN_ID', 'SKU', 'ASIN', 'START_TIME', 'END_TIME', 
                                'REPORT_FETCHED_AND_LOADED_AT'],
                    hard_alert=True
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_business_logic_check_staging_deals = PythonOperator(
            task_id="task_run_business_logic_check_staging_deals",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": f"promotions/amazon/check_business_logic_staging_deals.sql", 
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback = alerts.send_failure_alert
        )

        check_business_logic_check_failure_staging_deals = PythonOperator(
            task_id="task_check_business_logic_check_failure_staging_deals",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'check_business_logic_check_failure_staging_deals',
                'query_file': "promotions/amazon/dq_stg_scrapped_deals.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        check_data_ready_deals = BranchPythonOperator(
            task_id='check_if_data_ready',
            python_callable=check_if_data_ready,
            op_kwargs={
                "wf_params": WF_PARAMS_EXPR,
                "fact_task_id": 'load_amazon_deals.task_create_stg_fact_amazon_deals',
                "skip_task_id": 'load_amazon_deals.skip_fact_deals',
                'tb_name': '$scs_db.stg_scrapped_deals'
            },
            provide_context=True,
        )

        create_stg_fact_amazon_deals = PythonOperator(
            task_id="task_create_stg_fact_amazon_deals",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": f"promotions/amazon/create_stg_fact_amazon_deals.sql", 
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        merge_fact_amazon_deals = PythonOperator(
            task_id="task_merge_fact_amazon_deals",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": f"promotions/amazon/merge_fact_amazon_deals.sql", 
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_fact_amazon_deals = PythonOperator(
            task_id="run_dq_fact_amazon_deals",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_fact_amazon_deals',
                'tb_name': "$curated_db.fact_amazon_deals",
                'query_file': "promotions/amazon/dq_fact_amazon_deals.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )
    
        run_audit_fact_amazon_deals = PythonOperator(
            task_id="task_run_audit_fact_amazon_deals",
            python_callable=tla.run_audit,
            op_kwargs={
                        "table_name": "$curated_db.FACT_AMAZON_DEALS", 
                        "wf_params": WF_PARAMS_EXPR,
                        "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                        "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC' 
                    },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        chain(
            [list_s3_modified_files_deals,list_s3_modified_files_deals_goessor],
            begin_insert_amazon_deals_task,
            transfer_s3_to_snowflake_deals,
            transfer_s3_to_snowflake_deals_goessor,
            run_null_checks_raw_deals,
            run_uniqueness_check_raw_deals,
            insert_raw_scrapped_deals_log,
            merge_stg_scraped_deals,
            run_null_checks_stg_deals,
            run_uniqueness_check_staging,
            run_business_logic_check_staging_deals,
            check_business_logic_check_failure_staging_deals,
            check_data_ready_deals,
            [create_stg_fact_amazon_deals,skip_fact_deals]
        )

        chain(
            create_stg_fact_amazon_deals,
            merge_fact_amazon_deals,
            run_dq_fact_amazon_deals,
            run_audit_fact_amazon_deals,
            end_insert_amazon_deals_task
        )

    ######### COUPONS ########
    with TaskGroup(group_id='load_amazon_coupons') as load_amazon_coupons:
        list_s3_files_amazon_coupons_task = PythonOperator(
            task_id="list_s3_files_amazon_coupons",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_coupons/list_s3_files.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            
        )

        # Need a begin dummy operator for branching
        begin_insert_amazon_coupons_task = DummyOperator(task_id="begin_insert_amazon_coupons")
        end_insert_amazon_coupons_task = DummyOperator(task_id="end_insert_amazon_coupons")
        skip_fact_coupons = DummyOperator(task_id="skip_fact_coupons")

        s3_to_snowflake_amazon_coupons_task = PythonOperator(
            task_id="s3_to_snowflake_amazon_coupon",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_coupons/s3_to_sf_raw_amazon_coupons.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_amazon_coupons_task_goessor = PythonOperator(
            task_id="list_s3_files_amazon_coupons_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_coupons/list_s3_files_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        s3_to_snowflake_amazon_coupons_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_amazon_coupon_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_coupons/s3_to_sf_raw_amazon_coupons_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amazon_coupons',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amazon_coupons', 
                    field_list=['coupon_id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        insert_raw_amazon_coupons_task = PythonOperator(
            task_id="insert_raw_amazon_coupons",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "promotions/amazon/insert_raw_amazon_coupons_log.sql",
                       "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        merge_stg_amazon_coupons_task = PythonOperator(
            task_id="merge_stg_amazon_coupons",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "promotions/amazon/merge_stg_amazon_coupons.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        run_dq_stg_amazon_coupons_task = PythonOperator(
            task_id="run_dq_stg_amazon_coupons",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_stg_amazon_coupons',
                'query_file': "promotions/amazon/dq_stg_amazon_coupons.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        check_data_ready_coupons = BranchPythonOperator(
            task_id='check_if_data_ready',
            python_callable=check_if_data_ready,
            op_kwargs={
                "wf_params": WF_PARAMS_EXPR,
                "fact_task_id": 'load_amazon_coupons.create_stg_fact_amazon_coupons',
                "skip_task_id": 'load_amazon_coupons.skip_fact_coupons',
                'tb_name': '$scs_db.stg_amazon_coupons'
            },
            provide_context=True,
        )

        create_stg_fact_amazon_coupons_task = PythonOperator(
            task_id="create_stg_fact_amazon_coupons",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "promotions/amazon/create_stg_fact_amazon_coupons.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        merge_fact_amazon_coupons_task = PythonOperator(
            task_id="merge_fact_amazon_coupons",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "promotions/amazon/merge_fact_amazon_coupons.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        update_fact_amazon_coupons_task = PythonOperator(
            task_id="update_fact_amazon_coupons",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "promotions/amazon/update_fact_amazon_coupons.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_fact_amazon_coupons_task = PythonOperator(
            task_id="run_dq_fact_amazon_coupons",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_fact_amazon_coupons',
                'tb_name': "$curated_db.fact_amazon_coupons",
                'query_file': "promotions/amazon/dq_fact_amazon_coupons.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_audit_fact_amazon_coupons_task = PythonOperator(
            task_id="run_audit_fact_amazon_coupons",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_amazon_coupons",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        chain(
            [list_s3_files_amazon_coupons_task, list_s3_files_amazon_coupons_task_goessor],
            begin_insert_amazon_coupons_task,
            s3_to_snowflake_amazon_coupons_task,
            s3_to_snowflake_amazon_coupons_task_goessor,
            run_dq_is_null_raw_task,
            insert_raw_amazon_coupons_task,
            merge_stg_amazon_coupons_task,
            run_dq_stg_amazon_coupons_task,
            check_data_ready_coupons,
            [create_stg_fact_amazon_coupons_task,skip_fact_coupons]
        )

        chain(            
            create_stg_fact_amazon_coupons_task,
            merge_fact_amazon_coupons_task,
            update_fact_amazon_coupons_task,
            run_dq_fact_amazon_coupons_task,
            run_audit_fact_amazon_coupons_task,
            end_insert_amazon_coupons_task
        )

    ######### PEDS ########
    with TaskGroup(group_id='load_amazon_peds') as load_amazon_peds:
        list_s3_modified_files_peds = PythonOperator(
        task_id="task_list_s3_modified_files_peds",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "seller_central_scraper/prime_exclusive_discounts_v1/s3_list_folders.yaml", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        )


        #dummy operator for branching
        begin_insert_amazon_peds_task = DummyOperator(task_id="begin_insert_amazon_peds")
        end_insert_amazon_peds_task = DummyOperator(task_id="end_insert_amazon_peds")
        skip_fact_ped = DummyOperator(task_id="skip_fact_ped")

        transfer_s3_to_snowflake_peds = PythonOperator(
            task_id="task_transfer_s3_to_snowflake_peds",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "seller_central_scraper/prime_exclusive_discounts_v1/s3_to_sf_raw_scraped_prime_exclusive_discounts.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        list_s3_modified_files_peds_goessor = PythonOperator(
            task_id="task_list_s3_modified_files_peds_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={"args_file": "seller_central_scraper/prime_exclusive_discounts_v1/s3_list_folders_goessor.yaml",
                       "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        transfer_s3_to_snowflake_peds_goessor = PythonOperator(
            task_id="task_transfer_s3_to_snowflake_peds_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "seller_central_scraper/prime_exclusive_discounts_v1/s3_to_sf_raw_scraped_prime_exclusive_discounts_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_tests_raw_peds = PythonOperator(
            task_id="run_dq_tests_raw_peds",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$raw_db.RAW_SCRAPED_PRIME_EXCLUSIVE_DISCOUNTS",
                "test_name": "Raw records should ideally not contain duplicates for a given day however sometimes they do appear.",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$raw_db.RAW_SCRAPED_PRIME_EXCLUSIVE_DISCOUNTS", 
                    field_list=['SELLER_ID', 'ID', 'PROMOTION_ROW_ID'],
                    hard_alert=False
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_raw_table_logs = PythonOperator(
            task_id="task_insert_raw_prime_exclusive_discounts_log",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "promotions/amazon/insert_raw_prime_exclusive_discounts_log.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stg_scraped_prime_exclusive_discounts = PythonOperator(
            task_id="task_merge_stg_scraped_prime_exclusive_discounts",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "promotions/amazon/merge_stg_scraped_prime_exclusive_discounts.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        #HERE
        check_data_ready_ped = BranchPythonOperator(
            task_id='check_if_data_ready',
            python_callable=check_if_data_ready,
            op_kwargs={
                "wf_params": WF_PARAMS_EXPR,
                "fact_task_id": 'load_amazon_peds.task_create_stg_fact_amazon_prime_exclusive_discounts',
                "skip_task_id": 'load_amazon_peds.skip_fact_ped',
                'tb_name': '$scs_db.stg_scraped_prime_exclusive_discounts'
            },
            provide_context=True,
        )

        create_stg_fact_amazon_prime_exclusive_discounts = PythonOperator(
            task_id="task_create_stg_fact_amazon_prime_exclusive_discounts",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "promotions/amazon/create_stg_fact_amazon_prime_exclusive_discounts.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        update_prime_calendar_fact_amazon_ped = PythonOperator(
            task_id="task_update_prime_calendar_fact_amazon_ped",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "promotions/amazon/update_prime_calendar_fact_amazon_ped.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        merge_fact_amazon_prime_exclusive_discounts = PythonOperator(
            task_id="task_merge_fact_amazon_prime_exclusive_discounts",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "promotions/amazon/merge_fact_amazon_prime_exclusive_discounts.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_fact_amazon_prime_exclusive_discounts = PythonOperator(
            task_id="run_dq_fact_amazon_prime_exclusive_discounts",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_fact_amazon_prime_exclusive_discounts',
                'tb_name': "$curated_db.fact_amazon_prime_exclusive_discounts",
                'query_file': "promotions/amazon/dq_fact_amazon_prime_exclusive_discounts.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_audit_ped = PythonOperator(
            task_id="task_run_audit_ped",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.FACT_AMAZON_PRIME_EXCLUSIVE_DISCOUNTS",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": "RECORD_CREATED_TIMESTAMP_UTC",
                "ts_updated_field": "RECORD_UPDATED_TIMESTAMP_UTC",
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        chain(
            [list_s3_modified_files_peds,list_s3_modified_files_peds_goessor],
            begin_insert_amazon_peds_task,
            transfer_s3_to_snowflake_peds,
            transfer_s3_to_snowflake_peds_goessor,
            run_dq_tests_raw_peds,
            insert_raw_table_logs,
            merge_stg_scraped_prime_exclusive_discounts,
            update_prime_calendar_fact_amazon_ped,
            check_data_ready_ped,
            [create_stg_fact_amazon_prime_exclusive_discounts, skip_fact_ped]
        )

        chain(    
            create_stg_fact_amazon_prime_exclusive_discounts,
            merge_fact_amazon_prime_exclusive_discounts,
            run_dq_fact_amazon_prime_exclusive_discounts,
            run_audit_ped,
            end_insert_amazon_peds_task
        )

    # Price Discounts
    with TaskGroup(group_id='load_amazon_price_discounts') as load_amazon_price_discounts:
        list_s3_modified_files_price_discounts = PythonOperator(
            task_id="task_list_s3_modified_files_price_discounts",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={"args_file": "seller_central_scraper/price_discounts/s3_list_folders.yaml",
                       "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # dummy operator for branching
        begin_insert_amazon_price_discounts_task = DummyOperator(task_id="begin_insert_amazon_price_discounts")
        end_insert_amazon_price_discounts_task = DummyOperator(task_id="end_insert_amazon_price_discounts")
        skip_fact_price_discounts = DummyOperator(task_id="skip_fact_price_discounts")

        transfer_s3_to_snowflake_price_discounts = PythonOperator(
            task_id="task_transfer_s3_to_snowflake_price_discounts",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "seller_central_scraper/price_discounts/s3_to_sf_raw_scraped_prime_exclusive_discounts.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_tests_raw_price_discounts = PythonOperator(
            task_id="run_dq_tests_raw_price_discounts",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$raw_db.raw_scraped_price_discounts",
                "test_name": "pk_not_duplicate.",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$raw_db.raw_scraped_price_discounts",
                    field_list=['id', 'seller_id', 'sku', 'report_fetched_and_loaded_at'],
                    hard_alert=False
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_raw_table_logs = PythonOperator(
            task_id="task_insert_raw_price_discounts_log",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "promotions/amazon/insert_raw_price_discounts_log.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stg_scraped_price_discounts = PythonOperator(
            task_id="task_merge_stg_scraped_price_discounts",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "promotions/amazon/merge_stg_scraped_price_discounts.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        check_data_ready_price_discounts = BranchPythonOperator(
            task_id='check_if_data_ready',
            python_callable=check_if_data_ready,
            op_kwargs={
                "wf_params": WF_PARAMS_EXPR,
                "fact_task_id": 'load_amazon_price_discounts.task_create_stg_fact_amazon_price_discounts',
                "skip_task_id": 'load_amazon_price_discounts.skip_fact_price_discounts',
                'tb_name': '$scs_db.stg_scraped_price_discounts'
            },
            provide_context=True,
        )

        create_stg_fact_amazon_price_discounts = PythonOperator(
            task_id="task_create_stg_fact_amazon_price_discounts",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "promotions/amazon/create_stg_fact_amazon_price_discounts.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # update_prime_calendar_fact_amazon_price_discounts = PythonOperator(
        #     task_id="task_update_prime_calendar_fact_amazon_price_discounts",
        #     python_callable=tlsql.run_query_file,
        #     op_kwargs={
        #         "connection": "Snowflake",
        #         "sql_file": "promotions/amazon/update_prime_calendar_fact_amazon_price_discounts.sql",
        #         "wf_params": WF_PARAMS_EXPR,
        #     },
        #     provide_context=True,
        #     on_failure_callback=alerts.send_failure_alert,
        #     trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        # )

        merge_fact_amazon_price_discounts = PythonOperator(
            task_id="task_merge_fact_amazon_price_discounts",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "promotions/amazon/merge_fact_amazon_price_discounts.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_fact_amazon_price_discounts = PythonOperator(
            task_id="run_dq_fact_amazon_price_discounts",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_fact_amazon_price_discounts',
                'tb_name': "$curated_db.fact_amazon_price_discounts",
                'query_file': "promotions/amazon/dq_fact_amazon_price_discounts.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_audit_price_discounts = PythonOperator(
            task_id="task_run_audit_price_discounts",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_amazon_price_discounts",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": "record_created_timestamp_utc",
                "ts_updated_field": "record_updated_timestamp_utc",
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        chain(
            list_s3_modified_files_price_discounts,
            begin_insert_amazon_price_discounts_task,
            transfer_s3_to_snowflake_price_discounts,
            run_dq_tests_raw_price_discounts,
            insert_raw_table_logs,
            merge_stg_scraped_price_discounts,
            # update_prime_calendar_fact_amazon_price_discounts,
            check_data_ready_price_discounts,
            [create_stg_fact_amazon_price_discounts, skip_fact_price_discounts]
        )

        chain(
            create_stg_fact_amazon_price_discounts,
            merge_fact_amazon_price_discounts,
            run_dq_fact_amazon_price_discounts,
            run_audit_price_discounts,
            end_insert_amazon_price_discounts_task
        )


    # ---- Main branch ----
    chain(
       begin,
       get_workflow_params,
       [load_amazon_deals,load_amazon_coupons,load_amazon_peds,load_amazon_price_discounts],
       all_load_complete,
       update_brand_netsuite_item_number,
       create_stg_fact_amazon_promotions,
       run_uniqueness_check_stg_fact_amazon_promotions,
       merge_fact_all_promotions,
       update_workflow_params,
       end
    )

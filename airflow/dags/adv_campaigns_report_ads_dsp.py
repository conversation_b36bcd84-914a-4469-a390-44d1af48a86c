import logging
from datetime import datetime

from airflow import DAG
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.models.baseoperator import chain

import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
import tasklib.audit as tla
import tasklib.dq as tldq
from tasklib.loader import S3ToSnowflake
load_obj = S3ToSnowflake()

DAG_ID = 'adv_campaigns_report_ads_dsp_v1'
RELEASE_DEF = '1'
BUILD_NUM = '01'

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"

log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2022, 7, 18),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'ADV_CAMPAIGN_REPORT_ADS_DSP',
            'author': 'sauvik'},
    tags=['Sauvik']
) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

    list_s3_modified_files_ads_dsp = PythonOperator(
        task_id="list_s3_modified_files_ads_dsp",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "adv_campaigns_report_ads_dsp/ads_dsp__list_s3.yaml",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake_ads_dsp = PythonOperator(
        task_id="transfer_s3_to_snowflake_ads_dsp",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "adv_campaigns_report_ads_dsp/s3_to_sf_raw_amz_adv_campaign_rpt_ads_dsp.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    dedup_stage_ads_dsp = PythonOperator(
        task_id="dedup_stage_ads_dsp",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/amazon/adv_campaigns_report_ads_dsp/dedup_ads_dsp.sql",
            "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    merge_ads_dsp = PythonOperator(
        task_id="merge_ads_dsp",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/amazon/adv_campaigns_report_ads_dsp/merge_ads_dsp.sql",
            "wf_params": WF_PARAMS_EXPR},
        provide_context=True
    )

    run_dq_tests_merge_ads_dsp_dupe = PythonOperator(
        task_id="run_dq_tests_merge_ads_dsp_dupe",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'adv_campaigns_report_ads_dsp',
            'test_name': 'ads dsp raw data duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.campaignreport_ads_dsp",
                field_list=['advertisement_pk'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_merge_ads_dsp_null_key = PythonOperator(
        task_id="run_dq_tests_merge_ads_dsp_null_key",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'adv_campaigns_report_ads_dsp',
            'test_name': 'ads dsp raw data pk null check',
            'sql_query': tldq.gen_check_if_nulls(
                tb_name="$stage_db.campaignreport_ads_dsp",
                field_list=['advertisement_pk'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    materialize_dl_amazon_dsp = PythonOperator(
        task_id="materialize_dl_amazon_dsp",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/amazon/adv_campaigns_report_ads_dsp/materialize_dl_amazon_dsp.sql",
            "wf_params": WF_PARAMS_EXPR},
        provide_context=True
    )

    run_dq_tests_dl_amazon_dsp = PythonOperator(
        task_id="run_dq_tests_dl_amazon_dsp",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'adv_campaigns_report_ads_dsp',
            'test_name': 'ads dsp staging pk duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.dl_amazon_dsp",
                field_list=['"advertisement_pk"'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    materialize_amazon_dsp = PythonOperator(
        task_id="materialize_amazon_dsp",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/amazon/adv_campaigns_report_ads_dsp/materialize_amazon_dsp.sql",
            "wf_params": WF_PARAMS_EXPR},
        provide_context=True
    )

    run_dq_tests_amazon_dsp = PythonOperator(
        task_id="run_dq_tests_amazon_dsp",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'adv_campaigns_report_ads_dsp',
            'test_name': 'ads dsp prod pk duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$curated_db.fact_amazon_ads_dsp",
                field_list=['"advertisement_pk"'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_amazon_dsp_brand_empty = PythonOperator(
        task_id="run_dq_tests_amazon_dsp_brand_empty",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'adv_campaigns_report_ads_dsp',
            'tb_name': "$curated_db.fact_amazon_ads_dsp",
            'test_name': 'brand is null - add new brands to dwh.staging.stg_amazon_dsp_brand_map table ',
            'sql_query': """
                SELECT 
                    CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END as "result"
                FROM (
                    SELECT 1
                    FROM $curated_db.fact_amazon_ads_dsp
                    WHERE "brand" is null
                    LIMIT 1
                ) T
            """
            },
        on_failure_callback=alerts.send_failure_alert
    )

    update_workflow_params = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_audit = PythonOperator(
        task_id="task_run_audit",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.fact_amazon_ads_dsp", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    # --- Assemble the dag ---
    chain(
        begin,
        get_workflow_params,
        list_s3_modified_files_ads_dsp,
        transfer_s3_to_snowflake_ads_dsp,
        dedup_stage_ads_dsp,
        merge_ads_dsp,
        [run_dq_tests_merge_ads_dsp_dupe, run_dq_tests_merge_ads_dsp_null_key],
        materialize_dl_amazon_dsp,
        run_dq_tests_dl_amazon_dsp,
        materialize_amazon_dsp,
        [run_dq_tests_amazon_dsp, run_dq_tests_amazon_dsp_brand_empty],
        update_workflow_params,
        run_audit,
        end
    )

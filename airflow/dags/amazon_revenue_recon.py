from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging

log = logging.getLogger(__name__)

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"
DAG_ID = "amazon_revenue_recon"
with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022, 12, 10),
    schedule_interval="@daily",
    catchup=False,
    max_active_runs=1,
    tags=['amazon', 'recon', 'Ruchira', 'Vikas', 'RETIRED', 'DO NOT ENABLE'],
    params={
        "workflow_name": "amazon_revenue_recon",
        "author": "vikas"
    },
) as dag:
    import tasklib.workflow as tlw
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    create_stg_revenue_recon_records = PythonOperator(
        task_id="create_stg_revenue_recon_records",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "revenue_recon/stg_fact_amazon_revenue_recon.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_fact_table_records = PythonOperator(
        task_id="create_fact_table_records",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "revenue_recon/insert_fact_amazon_revenue_recon.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_audit_amazon_revenue_recon = PythonOperator(
            task_id="run_audit_amazon_revenue_recon",
            python_callable=tla.run_audit,
            op_kwargs={"table_name": "$curated_db.fact_amazon_revenue_recon_deprecated_20230731",
                       "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
    )

    run_dq_tests_stg_revenue_recon = PythonOperator(
        task_id="run_dq_tests_stg_revenue_recon",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'run_dq_tests_stg_revenue_recon',
            'tb_name': "$stage_db.STG_AMAZON_REVENUE_RECON",
            'query_file': "revenue_recon/stage_revenue_dq.yaml"
        },
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin")

    end = DummyOperator(task_id="end")

    chain(
        begin,
        get_workflow_params,
        create_stg_revenue_recon_records,
        create_fact_table_records,
        run_dq_tests_stg_revenue_recon,
        update_workflow_params,
        run_audit_amazon_revenue_recon,
        end,
    )

from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging
from tasklib import alerts

log = logging.getLogger(__name__)

WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id="GA_CAMPAIGN_LEVEL_V4",
    start_date=datetime(2022,5,8),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name':'GA_CAMPAIGN_LEVEL_V4',
            'author':'prashanjeet'
           },
    tags=["RETIRED","GA"]
) as dag:

    import tasklib.workflow as tlw
    import tasklib.s3 as tls3
    import tasklib.glue as tlg
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts
    from tasklib.loader import S3ToSnowflake
    load_obj = S3ToSnowflake()

    get_workflow_params=PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )


    list_s3_modified_files=PythonOperator(
        task_id="task_list_s3_modified_files",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"ga_campaign_level_v4/s3_list_folders.yaml", "wf_params":WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake=PythonOperator(
        task_id="task_transfer_s3_to_snowflake",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file":"ga_campaign_level_v4/s3_to_sf_delta_ingestion_raw_google_analytics_campaign_level_v4.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_raw = PythonOperator(
        task_id="create_stg_raw",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/google/campaign_level_v4/create_stg_raw.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_dq_brand_must_not_be_null = PythonOperator(
        task_id="derived_brand_must_not_be_null",
        python_callable=tldq.run_dq_tests,
        op_kwargs={"dq_id": "cc95febd-9348-4b35-aa18-7c8d6ea95e7f"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    merge_raw = PythonOperator(
        task_id="merge_raw",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/google/campaign_level_v4/merge_raw.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )
    
    run_audit_raw=PythonOperator(
        task_id="run_audit_raw",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$raw_db.google_analytics_campaign_level_v4", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    stg_transformed = PythonOperator(
            task_id="stg_transformed",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "ads/google/campaign_level_v4/stg_transformed.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )
    
    merge_curated_ga_marketing_orders_revenue = PythonOperator(
            task_id="merge_curated_ga_marketing_orders_revenue",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "ads/google/campaign_level_v4/merge_curated_ga_marketing_orders_revenue.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )
    
    audit_curated_ga_marketing_orders_revenue=PythonOperator(
        task_id="audit_curated_ga_marketing_orders_revenue",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.google_analytics_marketing_orders_revenue", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_workflow_params=PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    chain(
        begin,
        get_workflow_params,
        list_s3_modified_files,
        transfer_s3_to_snowflake,
        create_stg_raw,
        run_dq_brand_must_not_be_null,
        merge_raw,
        run_audit_raw,
        stg_transformed,
        [merge_curated_ga_marketing_orders_revenue],
        [audit_curated_ga_marketing_orders_revenue],
        update_workflow_params,
        end,
    )

"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.utils.task_group import TaskGroup
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib.loader import check_for_new_files
from tasklib import alerts
from tasklib.loader import S3ToSnowflake
load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'amazon_ads_sp_search_targeting_reports'
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2021, 1, 1),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'AMAZON_ADS_SP_SEARCH_TARGETING_REPORTS',
            'author': 'sauvik'},
    tags=['Sauvik', 'Raptor', 'Goessor']
) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
        
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_params = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load amz_sp_targetingclause ---
    
    with TaskGroup(group_id='load_amz_sp_targetingclause') as tg_amz_sp_targetingclause:
        list_s3_files_amz_sp_targetingclause_task = PythonOperator(
            task_id="list_s3_files_amz_sp_targetingclause",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_sp_search_targeting_reports/list_s3_amz_sp_targetingclause.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_amz_sp_targetingclause_task_goessor = PythonOperator(
            task_id="list_s3_files_amz_sp_targetingclause_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_sp_search_targeting_reports/list_s3_amz_sp_targetingclause_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_amz_sp_targetingclause_task = DummyOperator(task_id="begin_insert_amz_sp_targetingclause")
        skip_insert_amz_sp_targetingclause_task = DummyOperator(task_id="skip_insert_amz_sp_targetingclause")
        end_insert_amz_sp_targetingclause_task = DummyOperator(task_id="end_insert_amz_sp_targetingclause")
        
        s3_to_snowflake_amz_sp_targetingclause_task = PythonOperator(
            task_id="s3_to_snowflake_amz_sp_targetingclause",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_sp_search_targeting_reports/s3_to_sf_raw_amz_sp_targetingclause.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_amz_sp_targetingclause_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_amz_sp_targetingclause_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_sp_search_targeting_reports/s3_to_sf_raw_amz_sp_targetingclause_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amz_sp_targetingclause_task = BranchPythonOperator(
            task_id='check_new_files_found_amz_sp_targetingclause',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_sp_search_targeting_reports/s3_to_snowflake_amz_sp_targetingclause.yaml",
                       "args_file_second": "amazon_ads_sp_search_targeting_reports/s3_to_snowflake_amz_sp_targetingclause_goessor.yaml",
                       "skip_task_id": "load_amz_sp_targetingclause.skip_insert_amz_sp_targetingclause",
                       "next_task_id": "load_amz_sp_targetingclause.begin_insert_amz_sp_targetingclause"
            },
        )

        insert_log_amz_sp_targetingclause_task = PythonOperator(
            task_id="insert_log_amz_sp_targetingclause",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_sp_search_targeting_reports/insert_log_amz_sp_targetingclause.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amz_sp_targetingclause_task = PythonOperator(
            task_id="dedupe_amz_sp_targetingclause",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_sp_search_targeting_reports/dedupe_amz_sp_targetingclause.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amz_sp_targetingclause_task = PythonOperator(
            task_id="merge_stage_amz_sp_targetingclause",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_sp_search_targeting_reports/merge_stage_amz_sp_targetingclause.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amz_sp_targetingclause',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amz_sp_targetingclause', 
                    field_list=['fetchdate', 'accountid', 'campaignid', 'targetid'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        run_dq_is_unique_dedupe_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sp_targetingclause',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amz_sp_targetingclause', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        run_dq_is_unique_merge_task = PythonOperator(
            task_id="run_dq_is_unique_merge",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sp_targetingclause',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amz_sp_targetingclause', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        run_dq_is_null_dedupe_task = PythonOperator(
            task_id="run_dq_is_null_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sp_targetingclause',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_sp_targetingclause', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        run_dq_is_null_merge_task = PythonOperator(
            task_id="run_dq_is_null_merge",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sp_targetingclause',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amz_sp_targetingclause', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amz_sp_targetingclause_task = PythonOperator(
            task_id="run_audit_amz_sp_targetingclause",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amz_sp_targetingclause", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_amz_sp_targetingclause_task, list_s3_files_amz_sp_targetingclause_task_goessor] >>
            check_new_files_found_amz_sp_targetingclause_task >>
            [begin_insert_amz_sp_targetingclause_task, skip_insert_amz_sp_targetingclause_task]
        )

        (
            begin_insert_amz_sp_targetingclause_task >> 
            s3_to_snowflake_amz_sp_targetingclause_task >>
            s3_to_snowflake_amz_sp_targetingclause_task_goessor >>
            insert_log_amz_sp_targetingclause_task >>
            ( run_dq_is_null_raw_task
                ) >> dedupe_amz_sp_targetingclause_task >>
            ( run_dq_is_unique_dedupe_task
              , run_dq_is_null_dedupe_task
              ) >> merge_stage_amz_sp_targetingclause_task >>
            ( run_dq_is_unique_merge_task
              , run_dq_is_null_merge_task
              ) >> run_audit_amz_sp_targetingclause_task >>
            end_insert_amz_sp_targetingclause_task
        )

    #  --- Load amz_sp_searchtermtargetingreport ---
    
    with TaskGroup(group_id='load_amz_sp_searchtermtargetingreport') as tg_amz_sp_searchtermtargetingreport:
        list_s3_files_amz_sp_searchtermtargetingreport_task = PythonOperator(
            task_id="list_s3_files_amz_sp_searchtermtargetingreport",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_sp_search_targeting_reports/list_s3_amz_sp_searchtermtargetingreport.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_amz_sp_searchtermtargetingreport_task_goessor = PythonOperator(
            task_id="list_s3_files_amz_sp_searchtermtargetingreport_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_sp_search_targeting_reports/list_s3_amz_sp_searchtermtargetingreport_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_amz_sp_searchtermtargetingreport_task = DummyOperator(task_id="begin_insert_amz_sp_searchtermtargetingreport")
        skip_insert_amz_sp_searchtermtargetingreport_task = DummyOperator(task_id="skip_insert_amz_sp_searchtermtargetingreport")
        end_insert_amz_sp_searchtermtargetingreport_task = DummyOperator(task_id="end_insert_amz_sp_searchtermtargetingreport")
        
        s3_to_snowflake_amz_sp_searchtermtargetingreport_task = PythonOperator(
            task_id="s3_to_snowflake_amz_sp_searchtermtargetingreport",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_sp_search_targeting_reports/s3_to_sf_raw_amz_sp_searchtermtargetingreport.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_amz_sp_searchtermtargetingreport_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_amz_sp_searchtermtargetingreport_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "amazon_ads_sp_search_targeting_reports/s3_to_sf_raw_amz_sp_searchtermtargetingreport_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amz_sp_searchtermtargetingreport_task = BranchPythonOperator(
            task_id='check_new_files_found_amz_sp_searchtermtargetingreport',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_sp_search_targeting_reports/s3_to_snowflake_amz_sp_searchtermtargetingreport.yaml",
                       "args_file_second": "amazon_ads_sp_search_targeting_reports/s3_to_snowflake_amz_sp_searchtermtargetingreport_goessor.yaml",
                       "skip_task_id": "load_amz_sp_searchtermtargetingreport.skip_insert_amz_sp_searchtermtargetingreport",
                       "next_task_id": "load_amz_sp_searchtermtargetingreport.begin_insert_amz_sp_searchtermtargetingreport"
            },
        )

        insert_log_amz_sp_searchtermtargetingreport_task = PythonOperator(
            task_id="insert_log_amz_sp_searchtermtargetingreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_sp_search_targeting_reports/insert_log_amz_sp_searchtermtargetingreport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amz_sp_searchtermtargetingreport_task = PythonOperator(
            task_id="dedupe_amz_sp_searchtermtargetingreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_sp_search_targeting_reports/dedupe_amz_sp_searchtermtargetingreport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amz_sp_searchtermtargetingreport_task = PythonOperator(
            task_id="merge_stage_amz_sp_searchtermtargetingreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_sp_search_targeting_reports/merge_stage_amz_sp_searchtermtargetingreport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amz_sp_searchtermtargetingreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amz_sp_searchtermtargetingreport', 
                    field_list=['reportdate', 'accountid', 'campaignid', 'adgroupid', 'coalesce(keywordid,targetid)', 'query'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        run_dq_is_unique_dedupe_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sp_searchtermtargetingreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amz_sp_searchtermtargetingreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        run_dq_is_unique_merge_task = PythonOperator(
            task_id="run_dq_is_unique_merge",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sp_searchtermtargetingreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amz_sp_searchtermtargetingreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        run_dq_is_null_dedupe_task = PythonOperator(
            task_id="run_dq_is_null_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sp_searchtermtargetingreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_sp_searchtermtargetingreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        run_dq_is_null_merge_task = PythonOperator(
            task_id="run_dq_is_null_merge",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sp_searchtermtargetingreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amz_sp_searchtermtargetingreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amz_sp_searchtermtargetingreport_task = PythonOperator(
            task_id="run_audit_amz_sp_searchtermtargetingreport",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amz_sp_searchtermtargetingreport", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_amz_sp_searchtermtargetingreport_task, list_s3_files_amz_sp_searchtermtargetingreport_task_goessor] >>
            check_new_files_found_amz_sp_searchtermtargetingreport_task >>
            [begin_insert_amz_sp_searchtermtargetingreport_task, skip_insert_amz_sp_searchtermtargetingreport_task]
        )

        (
            begin_insert_amz_sp_searchtermtargetingreport_task >> 
            s3_to_snowflake_amz_sp_searchtermtargetingreport_task >>
            s3_to_snowflake_amz_sp_searchtermtargetingreport_task_goessor >>
            insert_log_amz_sp_searchtermtargetingreport_task >>
            ( run_dq_is_null_raw_task
                ) >> dedupe_amz_sp_searchtermtargetingreport_task >>
            ( run_dq_is_unique_dedupe_task
              , run_dq_is_null_dedupe_task
              ) >> merge_stage_amz_sp_searchtermtargetingreport_task >>
            ( run_dq_is_unique_merge_task
              , run_dq_is_null_merge_task
              ) >> run_audit_amz_sp_searchtermtargetingreport_task >>
            end_insert_amz_sp_searchtermtargetingreport_task
        )

    # ---- Main branch ----
    begin >> get_workflow_params >> [tg_amz_sp_targetingclause , tg_amz_sp_searchtermtargetingreport ] >> update_workflow_params >> end
    
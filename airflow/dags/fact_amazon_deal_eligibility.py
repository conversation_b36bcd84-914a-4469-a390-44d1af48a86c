from airflow import DAG
from datetime import datetime
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON><PERSON>perator, PythonOperator
from airflow.operators.dummy_operator import DummyOperator
import logging
from tasklib import alerts
from tasklib.loader import check_for_new_files
from tasklib.loader import S3ToSnowflake
import tasklib.s3 as tls3
import tasklib.workflow as tlw
import tasklib.dq as tldq
import tasklib.sql as tlsql
import tasklib.audit as tla
from airflow.utils.trigger_rule import TriggerRule


log = logging.getLogger(__name__)
load_obj = S3ToSnowflake()

DAG_ID = "fact_amazon_deal_eligibility"

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
        dag_id=DAG_ID,
        start_date=datetime(2022, 9, 22),
        schedule_interval='@hourly',
        catchup=False,
        max_active_runs=1,
        params={'workflow_name': DAG_ID.upper(),
                'author': 'here'
                },
        tags=['Himanshu'],
) as dag:

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

    list_s3_modified_files = PythonOperator(
        task_id="task_list_s3_modified_files",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "fact_amazon_deal_eligibility/s3_list_folders.yaml", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    check_new_files_found_task = BranchPythonOperator(
        task_id='task_check_new_files_found',
        python_callable=check_for_new_files,
        op_kwargs={
            "args_file": "fact_amazon_deal_eligibility/s3_to_snowflake.yaml",
            "skip_task_id": "task_skip_when_no_data",
            "next_task_id": "task_transfer_s3_to_snowflake",
            "args_file_second": "fact_amazon_deal_eligibility/s3_to_snowflake_goessor.yaml",
        }
    )

    transfer_s3_to_snowflake = PythonOperator(
        task_id="task_transfer_s3_to_snowflake",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "fact_amazon_deal_eligibility/s3_to_sf_raw_scrapped_deal_eligibility.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_goessor = PythonOperator(
        task_id="task_list_s3_modified_files_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "fact_amazon_deal_eligibility/s3_list_folders_goessor.yaml", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake_goessor = PythonOperator(
        task_id="task_transfer_s3_to_snowflake_goessor",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "fact_amazon_deal_eligibility/s3_to_sf_raw_scrapped_deal_eligibility_goessor.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_raw = PythonOperator(
        task_id="task_run_dq_tests_raw",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$raw_db.raw_scrapped_deal_eligibility",
            'test_name': 'identifiers_not_nulls',
            'sql_query': tldq.gen_check_if_nulls(
                tb_name="$raw_db.raw_scrapped_deal_eligibility",
                field_list=['asin', 'deal_type', 'marketplace_id', 'parent_asin',
                            'recommendation_id', 'seller_id', 'sku', 'your_price'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    insert_raw_scrapped_deal_eligibility_log = PythonOperator(
        task_id="task_insert_raw_scrapped_deal_eligibility_log",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "fact_amazon_deal_eligibility/insert_raw_scrapped_deal_eligibility_log.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    merge_stg_scrapped_deal_eligibility = PythonOperator(
        task_id="task_merge_stg_scrapped_deal_eligibility",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "fact_amazon_deal_eligibility/merge_stg_scrapped_deal_eligibility.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    dq_stg_scrapped_deal_eligibility = PythonOperator(
        task_id="dq_stg_scrapped_deal_eligibility",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'dq_stg_scrapped_deal_eligibility',
            'query_file': "fact_amazon_deal_eligibility/dq_stg_scrapped_deal_eligibility.yaml"
        },
        on_failure_callback=alerts.send_failure_alert
    )

    merge_fact_amazon_deal_eligibility = PythonOperator(
        task_id="task_merge_fact_amazon_deal_eligibility",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "fact_amazon_deal_eligibility/merge_fact_amazon_deal_eligibility.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_audit = PythonOperator(
        task_id="task_run_audit",
        python_callable=tla.run_audit,
        op_kwargs={
            "table_name": "$curated_db.FACT_AMAZON_DEAL_ELIGIBILITY",
            "wf_params": WF_PARAMS_EXPR,
            "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
            "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    skip_no_data = DummyOperator(task_id="task_skip_when_no_data")
    end = DummyOperator(task_id="end")

    (
            begin >> get_workflow_params >>
            [list_s3_modified_files, list_s3_modified_files_goessor]>>
            check_new_files_found_task >>
            [transfer_s3_to_snowflake, skip_no_data]
    )

    (
            transfer_s3_to_snowflake >>
            transfer_s3_to_snowflake_goessor >>
            run_dq_tests_raw >>
            insert_raw_scrapped_deal_eligibility_log >>
            merge_stg_scrapped_deal_eligibility >>
            dq_stg_scrapped_deal_eligibility >>
            merge_fact_amazon_deal_eligibility >>
            update_workflow_params
    )

    (
            skip_no_data >> update_workflow_params
    )

    (
            update_workflow_params >>
            run_audit >>
            end
    )

from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging
from tasklib import alerts
import tasklib.sql as tlsql
import tasklib.dq as tldq

log = logging.getLogger(__name__)

BUILD_NUM = '1'
RELEASE_DEF = '1'
DAG_ID = 'amazon_ntb_ltv_calc'
WF_PARAMS_EXPR = "{'wf_name': '" + DAG_ID.upper() + "'}"

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2023, 5, 1),
    schedule_interval='@daily',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': '<PERSON><PERSON><PERSON>'},
    tags=['L4']
) as dag:

    create_fact_ntb_ad_attribution = PythonOperator(
        task_id="create_fact_ntb_ad_attribution",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "amazon_ntb_ltv_calc/ntb_ad_attribution.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_fact_ntb_dsp_attribution = PythonOperator(
        task_id="create_fact_ntb_dsp_attribution",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "amazon_ntb_ltv_calc/ntb_dsp_attribution.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_fact_ntb_all_attribution = PythonOperator(
        task_id="create_fact_ntb_all_attribution",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "amazon_ntb_ltv_calc/ntb_all_attribution.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_fact_ntb_monthly = PythonOperator(
        task_id="create_fact_ntb_monthly",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "amazon_ntb_ltv_calc/ntb_monthly.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_fact_ntb_weekly = PythonOperator(
        task_id="create_fact_ntb_weekly",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "amazon_ntb_ltv_calc/ntb_weekly.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_fact_ntb_daily = PythonOperator(
        task_id="create_fact_ntb_daily",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "amazon_ntb_ltv_calc/ntb_daily.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    tasks = [
        create_fact_ntb_monthly,
        create_fact_ntb_weekly,
        create_fact_ntb_daily,
        create_fact_ntb_ad_attribution,
        create_fact_ntb_dsp_attribution,
        create_fact_ntb_all_attribution
    ]

    chain(
        begin,
        tasks,
        end
    )
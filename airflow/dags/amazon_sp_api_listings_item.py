from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.models import Variable
import logging
from tasklib import alerts
import tasklib.sql as tlsql

log = logging.getLogger(__name__)

BUILD_NUM = '1'
RELEASE_DEF = '1'
DAG_ID = 'amazon_sp_api_listings_item'
WF_PARAMS_EXPR = "{}"

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2024, 6, 1),
    schedule_interval=Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 */4 * * *'),
    catchup=False,
    max_active_runs=1,
    params={'author': 'ayush'},
    tags=['Nagesh']
) as dag:

    create_stg_amazon_sp_api_listings_item = PythonOperator(
        task_id="create_stg_amazon_sp_api_listings_item",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "pricing/amazon/sp_api_listings/create_stg_amazon_sp_api_listings_item.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    delete_insert_fact_amazon_sp_api_listings_item = PythonOperator(
        task_id="delete_insert_fact_amazon_sp_api_listings_item",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "pricing/amazon/sp_api_listings/delete_insert_fact_amazon_sp_api_listings_item.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    chain(
        begin,
        create_stg_amazon_sp_api_listings_item,
        delete_insert_fact_amazon_sp_api_listings_item,
        end
    )

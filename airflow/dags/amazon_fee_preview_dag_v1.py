from airflow import DAG
from datetime import datetime, timedelta
from airflow.operators.python import PythonOperator
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging
from tasklib import alerts
import tasklib.workflow as tlw
import tasklib.dq as tldq
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.audit as tla
from tasklib.loader import S3ToSnowflake

log = logging.getLogger(__name__)

load_obj = S3ToSnowflake()

DAG_ID = 'amazon_fee_preview_hourly_v1'
BUILD_DEF = '1.00'
BUILD_REVISION = '04'

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"


with DAG(
    dag_id=DAG_ID,
    description='Build:{0}.r{1}'.format(BUILD_DEF, BUILD_REVISION),
    start_date=datetime(2022, 7, 6),
    schedule_interval='@hourly',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'AMAZON_FEE_PREVIEW_HOURLY',
    'default_args': {'retries': 2, 'retry_delay': timedelta(minutes=5)},
    'author': 'vikas'},
    tags=['Vikas', 'Goessor']

) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

    list_s3_modified_files = PythonOperator(
        task_id="list_s3_modified_files",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "amazon_fee_preview/s3_list_folders.yaml",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_goessor = PythonOperator(
        task_id="list_s3_modified_files_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "amazon_fee_preview/s3_list_folders_goessor.yaml",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake = PythonOperator(
        task_id="transfer_s3_to_snowflake_us",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_fee_preview/s3_to_sf_raw_amazon_fee_preview_stg.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake_goessor = PythonOperator(
        task_id="transfer_s3_to_snowflake_us_goessor",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_fee_preview/s3_to_sf_raw_amazon_fee_preview_stg_goessor.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    load_dedupe_table = PythonOperator(
        task_id="load_dedupe_table",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "amazon_fee_preview/dedupe_fee_preview.sql",
            "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    load_curated_table = PythonOperator(
        task_id="load_curated_table",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "amazon_fee_preview/merge_fee_preview.sql",
            "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    # ------- Null Check on Key fields ----------

    run_dq_null_check = PythonOperator(
        task_id="run_dq_null_check",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'amazon_fee_preview_hourly_v1',
            'tb_name': "$stage_db.dedupe_amazon_fee_preview",
            'test_name': 'identifiers_not_nulls',
            'sql_query': tldq.gen_check_if_nulls(
                tb_name="$stage_db.dedupe_amazon_fee_preview",
                field_list=[
                    'DERIVED_SELLER_ID',
                    'REPORTSTARTDATE', 'REPORTENDDATE', 'SKU', 'ASIN','BRAND_NAME'
                ],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_null_check_curated = PythonOperator(
        task_id="run_dq_null_check_curated",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'amazon_fee_preview_hourly_v1',
            'tb_name': "$curated_db.AMAZON_FEE_PREVIEW",
            'test_name': 'identifiers_not_nulls',
            'sql_query': tldq.gen_check_if_nulls(
                tb_name="$stage_db.dedupe_amazon_fee_preview",
                field_list=[
                    'DERIVED_SELLER_ID',
                    'REPORTSTARTDATE', 'REPORTENDDATE', 'SKU', 'ASIN','BRAND_NAME'
                ],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_dedupe_pk_unique_check = PythonOperator(
        task_id="run_dq_dedupe_pk_unique_check",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.dedupe_amazon_fee_preview",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.dedupe_amazon_fee_preview",
                field_list=['fee_pk'],
                hard_alert=False
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    update_workflow_params = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_audit = PythonOperator(
        task_id="task_run_audit",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.amazon_fee_preview", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    # Assemble the dag
    chain(
        begin,
        get_workflow_params,
        [list_s3_modified_files, list_s3_modified_files_goessor],
        transfer_s3_to_snowflake,
        transfer_s3_to_snowflake_goessor,
        load_dedupe_table,
        run_dq_null_check,
        load_curated_table,
        run_dq_null_check_curated,
        run_dq_dedupe_pk_unique_check,
        update_workflow_params,
        run_audit,
        end
    )

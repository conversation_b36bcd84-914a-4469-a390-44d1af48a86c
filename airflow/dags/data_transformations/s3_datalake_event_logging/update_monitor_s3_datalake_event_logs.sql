BEGIN TRANSACTION;

<PERSON>RGE INTO $infra_db.monitor_s3_datalake_event_logs AS tgt
USING (
    SELECT *
    FROM $stage_db.staging_monitor_s3_datalake_event_logs
    QUALIFY ROW_NUMBER() OVER (PARTITION BY s3_bucket, s3_key ORDER BY event_time_utc DESC) = 1
) AS src
    ON src.s3_bucket = tgt.s3_bucket
   AND src.s3_key = tgt.s3_key
WHEN MATCHED THEN
UPDATE SET
    tgt.event_time_utc = src.event_time_utc
  , tgt.size = src.size
  , tgt.message_id = src.message_id
  , tgt.receipt_handle = src.receipt_handle
  , tgt.configuration_id = src.configuration_id
  , tgt.arn = src.arn
  , tgt.principal_id = src.principal_id
  , tgt.sqs_queue_name = src.sqs_queue_name
  , tgt.record_updated_timestamp_utc = TRY_TO_TIMESTAMP('$execution_timestamp')
WHEN NOT MATCHED THEN
INSERT (
    s3_bucket
  , s3_key
  , event_time_utc
  , size
  , message_id
  , receipt_handle
  , configuration_id
  , arn
  , principal_id
  , sqs_queue_name
  , record_created_timestamp_utc
  , record_updated_timestamp_utc
)
VALUES (
    src.s3_bucket
  , src.s3_key
  , src.event_time_utc
  , src.size
  , src.message_id
  , src.receipt_handle
  , src.configuration_id
  , src.arn
  , src.principal_id
  , src.sqs_queue_name
  , TRY_TO_TIMESTAMP('$execution_timestamp')
  , TRY_TO_TIMESTAMP('$execution_timestamp')
);

TRUNCATE TABLE $stage_db.staging_monitor_s3_datalake_event_logs;

COMMIT;

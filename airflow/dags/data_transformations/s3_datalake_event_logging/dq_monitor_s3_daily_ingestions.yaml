---
test_id: "check_duplicate_daily_ingestions"
enabled: true
query: |
  WITH grouped AS (
    SELECT entity_name, integration_name, report_name, dependant_dag, snapshot_date
    FROM $infra_db.monitor_s3_daily_ingestions
    GROUP BY 1, 2, 3, 4, 5
    HAVING COUNT(1) > 1
  )
  SELECT CASE WHEN count(1) > 0 THEN 2 ELSE 0 END AS "result"
  FROM grouped;
---
test_id: "check_if_valid_brands"
enabled: true
query: |
  SELECT CASE WHEN count(1) > 0 THEN 1 ELSE 0 END AS "result"
  FROM $infra_db.monitor_s3_daily_ingestions
  WHERE s3_bucket = 'heyday-dataplatform-datalake'
    AND integration_name NOT IN ('HYDY_AMZN_REVIEWS', 'SELLER_CENTRAL')
    AND (entity_name IS NULL
      OR entity_name NOT IN (
        'BAM', 'BLC', 'BOK', 'BSY', 'CAV', 'CEL', 'CLK', 'DCZ', 'DBLY',
        'FCP', 'FCTY', 'GLC', 'HTS', 'HYDY', 'IRO', 'KAC', 'KWX', 'LEB', 'MRD', 
        'NUM', 'RIS', 'SDR', 'SMT', 'SSCB', 'SUR', 'SWI', 'TOL', 'TRH', 'ZSK'));

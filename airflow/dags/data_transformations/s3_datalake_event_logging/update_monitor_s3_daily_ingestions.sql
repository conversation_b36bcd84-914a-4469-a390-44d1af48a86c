/*
Update the fact_daily_ingestions with latest file counts and sizes for each report & integration name
*/

BEGIN TRANSACTION;

-- Delete records for last N days and re-insert
SET num_days = 30;
SET default_min_event_date = '2018-01-01'::DATE;
SET min_date = (SELECT COALESCE((MAX(snapshot_date) - $num_days), $default_min_event_date) FROM $infra_db.monitor_s3_daily_ingestions);

DELETE FROM $infra_db.monitor_s3_daily_ingestions WHERE snapshot_date >= $min_date;

CREATE OR REPLACE TEMPORARY TABLE $infra_db.monitor_s3_daily_ingestions_temp AS
    WITH event_logs AS (
        SELECT
            (CASE WHEN TRY_TO_NUMERIC(m.entity_name_val) IS NOT NULL THEN TRIM(UPPER(SPLIT_PART(s3_key, '/', m.entity_name_val)))
                  ELSE m.entity_name_val
            END) AS entity_name
          , (CASE WHEN TRY_TO_NUMERIC(m.integration_name_val) IS NOT NULL THEN TRIM(SPLIT_PART(s3_key, '/', m.integration_name_val))
                  ELSE m.integration_name_val
            END) AS integration_name
          , (CASE WHEN TRY_TO_NUMERIC(m.report_name_val) IS NOT NULL THEN TRIM(SPLIT_PART(s3_key, '/', m.report_name_val))
                  ELSE m.report_name_val
            END) AS report_name
          , (CASE WHEN TRY_TO_NUMERIC(m.source_type_val) IS NOT NULL THEN TRIM(UPPER(SPLIT_PART(s3_key, '/', m.source_type_val)))
                  ELSE m.source_type_val
            END) AS source_type
          , l.s3_bucket
          , (CASE WHEN m.s3_prefix_val IS NULL THEN TRIM(REGEXP_REPLACE(s3_key, '/[^/]+$', ''))
                  ELSE m.s3_prefix_val
            END) AS s3_prefix
          , CONVERT_TIMEZONE('UTC', 'America/Los_Angeles', l.event_time_utc) AS event_time
          , event_time::DATE AS event_date
          , l.size
          , l.record_updated_timestamp_utc
        FROM $infra_db.monitor_s3_datalake_event_logs AS l
        JOIN $infra_db.monitor_s3_prefix_mapping AS m
            ON l.s3_bucket = m.bucket
           AND l.s3_key LIKE CONCAT(m.prefix, '%')
        WHERE event_date >= $min_date
    )
    -- Get the daily metrics (file count and size)
    SELECT
        entity_name
      , integration_name
      , report_name
      , event_date
      , s3_bucket
      , s3_prefix
      , source_type
      , MAX(record_updated_timestamp_utc) AS record_updated_timestamp_utc
      , COUNT(1) AS total_files
      , SUM(size) AS total_file_size
      , MIN(event_time) AS earliest_event_time
      , MAX(event_time) AS latest_event_time
      , MIN(size) AS min_file_size
      , MAX(size) AS max_file_size
      , ROUND(PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY size), 0) AS p25_file_size
      , ROUND(PERCENTILE_CONT(0.50) WITHIN GROUP (ORDER BY size), 0) AS p50_file_size
      , ROUND(PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY size), 0) AS p75_file_size
    FROM event_logs
    GROUP BY 1, 2, 3, 4, 5, 6, 7;

INSERT INTO $infra_db.monitor_s3_daily_ingestions (
    pk
  , entity_name
  , integration_name
  , report_name
  , report_name_normalized
  , dependant_dag
  , snapshot_date
  , total_files
  , total_file_size
  , min_file_size
  , max_file_size
  , p25_file_size
  , p50_file_size
  , p75_file_size
  , s3_bucket
  , s3_prefix
  , earliest_event_time
  , latest_event_time
  , source
  , source_type
  , area_type
  , record_created_timestamp_utc
)
    SELECT
        MD5(CONCAT(
            COALESCE(g.entity_name, ''), '-',
            COALESCE(g.integration_name, ''), '-',
            COALESCE(g.report_name, ''), '-',
            COALESCE(m.dependant_dag, ''))) as pk
      , g.entity_name
      , g.integration_name
      , g.report_name
      , m.report_name_normalized
      , m.dependant_dag
      , g.event_date
      , g.total_files
      , g.total_file_size
      , g.min_file_size
      , g.max_file_size
      , g.p25_file_size
      , g.p50_file_size
      , p75_file_size
      , g.s3_bucket
      , g.s3_prefix
      , g.earliest_event_time
      , g.latest_event_time
      , m.source
      , g.source_type
      , m.area_type
      , g.record_updated_timestamp_utc
    FROM $infra_db.monitor_s3_daily_ingestions_temp AS g
    JOIN $infra_db.monitor_s3_ingestion_metadata AS m
        ON UPPER(g.report_name) = UPPER(m.report_name)
       AND UPPER(g.source_type) = UPPER(m.source_type);

COMMIT;

-- Insert into a table with the latest snapshot date available for each integration
BEGIN TRANSACTION;

DELETE FROM $infra_db.monitor_s3_latest_snapshot WHERE record_created_timestamp_utc = '$execution_timestamp';

INSERT INTO $infra_db.monitor_s3_latest_snapshot (
    pk
  , entity_name
  , integration_name
  , report_name
  , report_name_normalized
  , dependant_dag
  , latest_snapshot_date
  , total_files
  , total_file_size
  , s3_bucket
  , s3_prefix
  , source_type
  , record_created_timestamp_utc
)
    SELECT
        pk
      , entity_name
      , integration_name
      , report_name
      , report_name_normalized
      , dependant_dag
      , snapshot_date AS latest_snapshot_date
      , total_files
      , total_file_size
      , s3_bucket
      , s3_prefix
      , source_type
      , TO_TIMESTAMP('$execution_timestamp') AS record_created_timestamp_utc
    FROM $infra_db.monitor_s3_daily_ingestions
    QUALIFY ROW_NUMBER() OVER (
        PARTITION BY pk ORDER BY snapshot_date DESC) = 1;

COMMIT;

CREATE OR REPLACE TRANSIENT TABLE $stage_db.stg_amazon_organic_keyword_rank_extended AS
WITH parent_asin_map AS (
    SELECT
        country_code
        , child_asin AS asin
        , CASE
            WHEN TRIM(parent_asin) = '' THEN child_asin
            ELSE parent_asin
          END AS parent_asin
    FROM dwh.prod.parent_asin_child_asin_mapping
    WHERE child_asin IS NOT NULL
        AND trim(child_asin) != ''
        AND country_code IS NOT NULL
        AND record_date = (SELECT MAX(record_date) FROM dwh.prod.parent_asin_child_asin_mapping)
    QUALIFY ROW_NUMBER() OVER(PARTITION BY country_code, child_asin ORDER BY created_at DESC) = 1
)
, okr_dim AS (
    SELECT DISTINCT
        f.keyword
        , f.country_code
        , f.parent_asin
        , f.user_agent
        , f._task_id
        , m.asin
        , f.data_source
    FROM $stage_db.stg_amazon_organic_keyword_rank f
    JOIN parent_asin_map m
        ON f.country_code = m.country_code
        AND f.parent_asin = m.parent_asin
    WHERE f.organic_rank IS NOT NULL
)
, pre_min_pa_okr AS (
    SELECT
        _task_id
        , keyword
        , parent_asin
        , country_code
        , page
        , page_scrape_time_pst
        , report_fetched_and_loaded_at_pst
        , delivery_zip_code
        , organic_rank
        , MIN(organic_rank) OVER (PARTITION BY _task_id, keyword, parent_asin, country_code) AS min_okr
    FROM $stage_db.stg_amazon_organic_keyword_rank
    WHERE organic_rank IS NOT NULL
)
, min_pa_okr AS (
    SELECT
        _task_id
        , keyword
        , parent_asin
        , country_code
        , page
        , page_scrape_time_pst
        , report_fetched_and_loaded_at_pst
        , delivery_zip_code
        , min_okr
    FROM pre_min_pa_okr
    WHERE organic_rank = min_okr
    QUALIFY ROW_NUMBER() OVER(PARTITION BY _task_id, keyword, parent_asin, country_code ORDER BY page_scrape_time_pst DESC) = 1
)
, asin_catalog AS (
    SELECT DISTINCT
        asin,
        MAX(seller_id) AS seller_id,
        MAX(brand_code) AS brand_code,
    FROM dwh.prod.asin_sku_catalogue
    GROUP BY 1
)
SELECT
    COALESCE(f.keyword, dim.keyword) AS keyword
    , COALESCE(f.asin, dim.asin) AS asin
    , COALESCE(f.organic_rank, o.min_okr) AS organic_rank
    , COALESCE(f.page, o.page) AS page
    , COALESCE(f.country_code, dim.country_code) AS country_code
    , COALESCE(f.page_scrape_time_pst, o.page_scrape_time_pst) AS page_scrape_time_pst
    , COALESCE(f.report_fetched_and_loaded_at_pst, o.report_fetched_and_loaded_at_pst) AS report_fetched_and_loaded_at_pst
    , COALESCE(f.delivery_zip_code, o.delivery_zip_code) AS delivery_zip_code
    , COALESCE(f.parent_asin, dim.parent_asin) AS parent_asin
    , COALESCE(f.brand_code, bm.brand_code, ac.brand_code) AS brand_code
    , COALESCE(f.seller_id, ac.seller_id) AS seller_id
    , COALESCE(f.user_agent, dim.user_agent) AS user_agent
    , COALESCE(f._task_id, dim._task_id) AS _task_id
    , CASE WHEN f.keyword IS NULL THEN 'MANUAL' ELSE 'SCRAPED' END AS record_type
    , COALESCE(f.data_source, dim.data_source) AS data_source
FROM (
    SELECT *
    FROM $stage_db.stg_amazon_organic_keyword_rank
    WHERE organic_rank IS NOT NULL
) f
FULL OUTER JOIN okr_dim dim
	ON f.keyword = dim.keyword
	AND f.country_code = dim.country_code
	AND f.parent_asin = dim.parent_asin
	AND f._task_id = dim._task_id
	AND f.asin = dim.asin
LEFT JOIN min_pa_okr o
    ON COALESCE(f._task_id, dim._task_id) = o._task_id
    AND COALESCE(f.keyword, dim.keyword) = o.keyword
    AND COALESCE(f.country_code, dim.country_code) = o.country_code
    AND COALESCE(f.parent_asin, dim.parent_asin) = o.parent_asin
LEFT JOIN asin_catalog ac
    ON COALESCE(f.asin, dim.asin) = ac.asin
LEFT JOIN dwh.prod.marketplace_asin_brand_mapping bm
    ON COALESCE(f.asin, dim.asin) = bm.asin
    AND COALESCE(f.country_code, dim.country_code) = UPPER(bm.country_code);

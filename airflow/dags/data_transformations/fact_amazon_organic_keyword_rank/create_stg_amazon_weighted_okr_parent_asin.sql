SET max_snapshot_date = (
    SELECT MIN(page_scrape_time_pst::DATE)
    FROM dwh.prod.fact_amazon_organic_keyword_rank
    WHERE record_updated_timestamp_utc > (
        SELECT COALESCE(MAX(record_updated_timestamp_utc), '1900-01-01')
        FROM $curated_db.fact_amazon_weighted_okr_parent_asin
    )
);

CREATE OR REPLACE TRANSIENT TABLE $stage_db.stg_amazon_weighted_okr_parent_asin AS
WITH asin_kw_okr_data AS (
    SELECT
        parent_asin
        , asin
        , brand_code
        , LOWER(keyword) AS query
        , UPPER(country_code) AS country_code
        , page_scrape_time_pst::DATE AS snapshot_date
        , MEDIAN(organic_rank) AS okr
    FROM $curated_db.fact_amazon_organic_keyword_rank
    WHERE page_scrape_time_pst::DATE >= $max_snapshot_date
    GROUP BY 1,2,3,4,5,6
)
, sqp_cte_metadata AS (
    SELECT
        asin
        , LOWER(qp_asin_query) AS query
        , brand_code
        , UPPER(country_code) AS country_code
        , weekly_week_start_date AS week_start
        , MAX(qp_asin_count_impressions) AS qp_asin_count_impressions
        , MAX(qp_asin_count_clicks) AS qp_asin_count_clicks
        , MAX(qp_asin_count_purchases) AS qp_asin_count_purchases
    FROM dwh.prod.fact_asin_view_report
    WHERE weekly_week_start_date >= DATEADD('month', -2, $max_snapshot_date)
    GROUP BY 1,2,3,4,5
)
, sqp_metadata_last_2_week AS (
    SELECT
        asin
        , query
        , brand_code
        , country_code
        , week_start
        , qp_asin_count_impressions
        , qp_asin_count_clicks
        , qp_asin_count_purchases
    FROM sqp_cte_metadata
    QUALIFY DENSE_RANK() OVER (ORDER BY week_start DESC) <= 2
)
, okr_with_sqp_data AS (
    SELECT
        a.parent_asin
        , a.asin
        , a.query
        , a.country_code
        , a.snapshot_date
        , a.okr
        , MAX(COALESCE(a.brand_code, b.brand_code)) AS brand_code
        , COUNT(DISTINCT b.week_start) week_start_cnt
        , SUM(b.qp_asin_count_impressions) AS qp_asin_count_impressions
        , SUM(b.qp_asin_count_clicks) AS qp_asin_count_clicks
        , SUM(b.qp_asin_count_purchases) AS qp_asin_count_purchases
    FROM asin_kw_okr_data a
    JOIN sqp_metadata_last_2_week b
        ON a.asin = b.asin
        AND a.query = b.query
        AND a.country_code = b.country_code
        AND b.week_start <= a.snapshot_date
    GROUP BY 1,2,3,4,5,6
)
, sqp_with_total_weighing_factor AS (
    SELECT
        parent_asin
        , brand_code
        , country_code
        , snapshot_date
        , SUM(qp_asin_count_impressions) AS total_impressions
        , SUM(qp_asin_count_clicks) AS total_clicks
        , SUM(qp_asin_count_purchases) AS total_purchases
        , COUNT(query) AS tot_keyword_count
    FROM okr_with_sqp_data
    GROUP BY 1,2,3,4
)
, sqp_with_total_weighing_factor_asin AS (
    SELECT
        asin
        , brand_code
        , country_code
        , snapshot_date
        , SUM(qp_asin_count_impressions) AS total_impressions
        , SUM(qp_asin_count_clicks) AS total_clicks
        , SUM(qp_asin_count_purchases) AS total_purchases
        , COUNT(query) AS tot_keyword_count
    FROM okr_with_sqp_data
    GROUP BY 1,2,3,4
)
, sqp_okr_mapping AS (
    SELECT
        a.parent_asin
        , a.asin
        , a.query
        , a.country_code
        , a.snapshot_date
        , a.okr
        , a.brand_code
        , a.week_start_cnt
        , a.qp_asin_count_impressions
        , a.qp_asin_count_clicks
        , a.qp_asin_count_purchases
        , b.total_impressions
        , b.total_clicks
        , b.total_purchases
        , b.tot_keyword_count
        , DIV0(a.qp_asin_count_impressions, b.total_impressions) AS weighted_factor_impressions
        , DIV0(a.qp_asin_count_clicks, b.total_clicks) AS weighted_factor_clicks
        , DIV0(a.qp_asin_count_purchases, b.total_purchases) AS weighted_factor_purchases
        , DIV0(a.qp_asin_count_impressions, c.total_impressions) AS weighted_factor_impressions_asin
        , DIV0(a.qp_asin_count_clicks, c.total_clicks) AS weighted_factor_clicks_asin
        , DIV0(a.qp_asin_count_purchases, c.total_purchases) AS weighted_factor_purchases_asin
    FROM okr_with_sqp_data AS a
    LEFT JOIN sqp_with_total_weighing_factor AS b
        ON a.parent_asin = b.parent_asin
        AND a.country_code = b.country_code
        AND a.snapshot_date = b.snapshot_date
    LEFT JOIN sqp_with_total_weighing_factor_asin AS c
        ON a.asin = c.asin
        AND a.country_code = c.country_code
        AND a.snapshot_date = c.snapshot_date
)
, weighted_okr_asin AS (
    SELECT
        parent_asin
        , asin
        , brand_code
        , country_code
        , snapshot_date
        , AVG(okr) AS okr_avg
        , ROUND(SUM(okr*weighted_factor_impressions_asin), 2) AS weighted_okr_impressions
        , ROUND(SUM(okr*weighted_factor_clicks_asin), 2) AS weighted_okr_clicks
        , ROUND(SUM(okr*weighted_factor_purchases_asin), 2) AS weighted_okr_purchases
    FROM sqp_okr_mapping
    GROUP BY 1,2,3,4,5
)
, best_child_asin_okr_values AS (
    SELECT
        parent_asin
        , country_code
        , snapshot_date
        , MIN(NULLIF(weighted_okr_impressions, 0)) AS min_weighted_okr_impressions
        , MIN(NULLIF(weighted_okr_clicks, 0)) AS min_weighted_okr_clicks
        , MIN(NULLIF(weighted_okr_purchases, 0)) AS min_weighted_okr_purchases
    FROM weighted_okr_asin
    GROUP BY 1,2,3
)
, best_child_asin_okr_impressions AS (
    SELECT
        a.parent_asin
        , ARRAY_AGG(DISTINCT asin) WITHIN GROUP (ORDER BY asin) AS asin_list
        , a.country_code
        , a.snapshot_date
        , b.min_weighted_okr_impressions AS weighted_okr_impressions
    FROM weighted_okr_asin a
    JOIN best_child_asin_okr_values b
        ON a.parent_asin = b.parent_asin
        AND a.country_code = b.country_code
        AND a.snapshot_date = b.snapshot_date
        AND a.weighted_okr_impressions = b.min_weighted_okr_impressions
    GROUP BY 1,3,4,5
)
, best_child_asin_okr_clicks AS (
    SELECT
        a.parent_asin
        , ARRAY_AGG(DISTINCT asin) WITHIN GROUP (ORDER BY asin) AS asin_list
        , a.country_code
        , a.snapshot_date
        , ROUND(b.min_weighted_okr_clicks, 2) AS weighted_okr_clicks
    FROM weighted_okr_asin a
    JOIN best_child_asin_okr_values b
        ON a.parent_asin = b.parent_asin
        AND a.country_code = b.country_code
        AND a.snapshot_date = b.snapshot_date
        AND a.weighted_okr_clicks = b.min_weighted_okr_clicks
    GROUP BY 1,3,4,5
)
, best_child_asin_okr_purchases AS (
    SELECT
        a.parent_asin
        , ARRAY_AGG(DISTINCT asin) WITHIN GROUP (ORDER BY asin) AS asin_list
        , a.country_code
        , a.snapshot_date
        , ROUND(b.min_weighted_okr_purchases, 2) AS weighted_okr_purchases
    FROM weighted_okr_asin a
    JOIN best_child_asin_okr_values b
        ON a.parent_asin = b.parent_asin
        AND a.country_code = b.country_code
        AND a.snapshot_date = b.snapshot_date
        AND a.weighted_okr_purchases = b.min_weighted_okr_purchases
    GROUP BY 1,3,4,5
)
SELECT
    som.parent_asin
    , ARRAY_AGG(DISTINCT som.asin) WITHIN GROUP (ORDER BY asin) AS asin_list
    , ARRAY_AGG(DISTINCT som.query) WITHIN GROUP (ORDER BY query) AS query_list
    , som.brand_code
    , som.country_code
    , som.snapshot_date
    , ROUND(AVG(som.okr) ,2) AS okr_avg
    , ROUND(SUM(som.okr*som.weighted_factor_impressions) ,2) AS weighted_okr_impressions
    , AVG(som.total_impressions) AS total_impressions
    , oi.weighted_okr_impressions AS weighted_okr_impressions_best_child
    , oi.asin_list AS weighted_okr_impressions_best_child_asin_list
    , ROUND(SUM(som.okr*som.weighted_factor_clicks) ,2) AS weighted_okr_clicks
    , AVG(som.total_clicks) AS total_clicks
    , oc.weighted_okr_clicks AS weighted_okr_clicks_best_child
    , oc.asin_list AS weighted_okr_clicks_best_child_asin_list
    , ROUND(SUM(som.okr*som.weighted_factor_purchases) ,2) AS weighted_okr_purchases
    , AVG(som.total_purchases) AS total_purchases
    , op.weighted_okr_purchases AS weighted_okr_purchases_best_child
    , op.asin_list AS weighted_okr_purchases_best_child_asin_list
FROM sqp_okr_mapping som
LEFT JOIN best_child_asin_okr_impressions oi
    ON som.parent_asin = oi.parent_asin
    AND som.country_code = oi.country_code
    AND som.snapshot_date = oi.snapshot_date
LEFT JOIN best_child_asin_okr_clicks oc
    ON som.parent_asin = oc.parent_asin
    AND som.country_code = oc.country_code
    AND som.snapshot_date = oc.snapshot_date
LEFT JOIN best_child_asin_okr_purchases op
    ON som.parent_asin = op.parent_asin
    AND som.country_code = op.country_code
    AND som.snapshot_date = op.snapshot_date
GROUP BY 1,4,5,6,10,11,14,15,18,19;

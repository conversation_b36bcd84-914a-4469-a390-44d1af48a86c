BEGIN TRANSACTION;

DELETE FROM $curated_db.fact_amazon_organic_keyword_rank p
USING $stage_db.stg_amazon_organic_keyword_rank s
WHERE p._task_id = s._task_id;

INSERT INTO $curated_db.fact_amazon_organic_keyword_rank (
    keyword
    , asin
    , organic_rank
    , page
    , country_code
    , page_scrape_time_pst
    , report_fetched_and_loaded_at_pst
    , delivery_zip_code
    , parent_asin
    , brand_code
    , seller_id
    , user_agent
    , _task_id
    , record_type
    , data_source
)
SELECT
    keyword
    , asin
    , organic_rank
    , page
    , country_code
    , page_scrape_time_pst
    , report_fetched_and_loaded_at_pst
    , delivery_zip_code
    , parent_asin
    , brand_code
    , seller_id
    , user_agent
    , _task_id
    , record_type
    , data_source
FROM $stage_db.stg_amazon_organic_keyword_rank_extended;

COMMIT;
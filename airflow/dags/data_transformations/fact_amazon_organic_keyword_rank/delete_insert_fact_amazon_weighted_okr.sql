BEGIN TRANSACTION;

DELETE FROM $curated_db.fact_amazon_weighted_okr f
USING (SELECT DISTINCT snapshot_date, asin, country_code FROM $stage_db.stg_amazon_weighted_okr) s
WHERE f.snapshot_date = s.snapshot_date
    AND f.asin = s.asin
    AND f.country_code = s.country_code;

INSERT INTO $curated_db.fact_amazon_weighted_okr (
    asin
    , parent_asin
    , query_list
    , brand_code
    , country_code
    , snapshot_date
    , weighted_okr_impressions
    , total_impressions
    , weighted_okr_clicks
    , total_clicks
    , weighted_okr_purchases
    , total_purchases
)
SELECT
    asin
    , parent_asin
    , query_list
    , brand_code
    , country_code
    , snapshot_date
    , NULLIF(weighted_okr_impressions, 0)
    , total_impressions
    , NULLIF(weighted_okr_clicks, 0)
    , total_clicks
    , NULLIF(weighted_okr_purchases, 0)
    , total_purchases
FROM $stage_db.stg_amazon_weighted_okr;

COMMIT;
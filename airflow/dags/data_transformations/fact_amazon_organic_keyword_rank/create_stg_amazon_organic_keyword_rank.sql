CREATE OR REPLACE TRANSIENT TABLE $stage_db.stg_amazon_organic_keyword_rank AS
WITH processed_task_id AS (
    SELECT DISTINCT
        _task_id
    FROM $curated_db.fact_amazon_organic_keyword_rank
    UNION
    SELECT DISTINCT
        _task_id
    FROM $curated_db.fact_amazon_sponsored_keyword_rank
)
, asin_catalog AS (
    SELECT DISTINCT
        asin,
        MAX(seller_id) AS seller_id,
        MAX(brand_code) AS brand_code
    FROM dwh.prod.asin_sku_catalogue
    GROUP BY 1
)
, parent_asin AS (
    SELECT
        country_code
        , asin
        , parent_asin
    FROM dwh.prod.dim_parent_asin
    WHERE is_active = True
)
, parent_asin_2 AS (
    SELECT
        country_code
        , item_id AS asin
        , parent_item_id AS parent_asin
    FROM dwh.prod.fact_all_item_sales_traffic_report
    WHERE parent_item_id IS NOT NULL
        AND UPPER(item_type) = 'ASIN'
        AND UPPER(parent_item_type) = 'ASIN'
    QUALIFY ROW_NUMBER() OVER(PARTITION BY country_code, item_id ORDER BY snapshot_date DESC) = 1
)
, parent_asin_3 AS (
    SELECT
        country_code
        , child_asin AS asin
        , CASE
            WHEN TRIM(parent_asin) = '' THEN child_asin
            ELSE parent_asin
          END AS parent_asin
    FROM dwh.prod.parent_asin_child_asin_mapping
    WHERE child_asin IS NOT NULL
        AND trim(child_asin) != ''
        AND country_code IS NOT NULL
        AND record_date = (SELECT MAX(record_date) FROM dwh.prod.parent_asin_child_asin_mapping)
    QUALIFY ROW_NUMBER() OVER(PARTITION BY country_code, child_asin ORDER BY created_at DESC) = 1
)
-- task_id is unique for a keyword in a single scrape extract (Hash of scrape runtime and keyword)
, page_product_count AS (
    SELECT DISTINCT
        r.task_id
        , result_page_number
        , total_products_in_page
    FROM dwh.raw.raw_amazon_okr_data r
    LEFT JOIN processed_task_id p
        ON r.task_id = p._task_id
    WHERE p._task_id IS NULL
)
, prev_page_product_count AS (
    SELECT
        task_id
        , result_page_number
        , total_products_in_page
        , LAG(total_products_in_page) OVER (PARTITION BY task_id ORDER BY result_page_number) AS total_products_in_prev_page
    FROM page_product_count
)
, product_count_till_previous_page AS (
    SELECT
        task_id
        , result_page_number
        , total_products_in_page
        , SUM(COALESCE(total_products_in_prev_page, 0)) OVER (PARTITION BY task_id
            ORDER BY result_page_number ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS total_products_till_prev_page
    FROM prev_page_product_count
)
, heyday_okr_data AS (
    SELECT
        okr.keyword
        , okr.asin
        , (pc.total_products_till_prev_page + okr.okr) AS organic_rank
        , okr.result_page_number AS page
        , sponsored_okr AS sponsored_rank
        , NULL AS sponsored_ad_placement
        , UPPER(okr.country_code) AS country_code
        , NULL AS amazon_url
        , CONVERT_TIMEZONE('UTC','America/Los_Angeles', okr.scraping_time) AS page_scrape_time_pst
        , CONVERT_TIMEZONE('UTC','America/Los_Angeles', okr.created_at) AS report_fetched_and_loaded_at_pst
        , okr.region AS delivery_zip_code
        , COALESCE(pa3.parent_asin, pa.parent_asin, pa2.parent_asin, okr.asin) AS parent_asin
        , COALESCE(bm.brand_code, ac.brand_code) AS brand_code
        , ac.seller_id
        , 'Desktop' AS user_agent
        , okr.task_id AS _task_id
        , 'heyday' AS data_source
        , okr.is_sponsored
    FROM dwh.raw.raw_amazon_okr_data okr
    JOIN asin_catalog ac
        ON okr.asin = ac.asin
    LEFT JOIN product_count_till_previous_page pc
        ON okr.task_id = pc.task_id
        AND okr.result_page_number = pc.result_page_number
    LEFT JOIN dwh.prod.marketplace_asin_brand_mapping bm
        ON okr.asin = bm.asin
        AND UPPER(okr.country_code) = UPPER(bm.country_code)
    LEFT JOIN parent_asin pa
        ON okr.asin = pa.asin
        AND UPPER(okr.country_code) = UPPER(pa.country_code)
    LEFT JOIN parent_asin_2 pa2
        ON okr.asin = pa2.asin
        AND UPPER(okr.country_code) = UPPER(pa2.country_code)
    LEFT JOIN parent_asin_3 pa3
        ON okr.asin = pa3.asin
        AND UPPER(okr.country_code) = UPPER(pa3.country_code)
    WHERE pc.task_id IS NOT NULL
    QUALIFY ROW_NUMBER() OVER(PARTITION BY okr.keyword, okr.asin, is_sponsored, page_scrape_time_pst, okr.task_id, okr.country_code
        ORDER BY report_fetched_and_loaded_at_pst DESC) = 1
)
, branded_okr_data AS (
    SELECT
        okr.SEARCH_TERM as keyword
        , okr.asin
        , MIN(okr.ORGANIC_POSITION) OVER (PARTITION BY okr.SEARCH_TERM, okr.asin, okr.sponsored, okr.ingested_date, okr.MARKETPLACE_COUNTRY_CODE) AS organic_rank
        , okr.CURRENT_PAGE AS page
        , okr.SPONSORED_POSITION AS sponsored_rank
        , NULL AS sponsored_ad_placement
        , UPPER(okr.MARKETPLACE_COUNTRY_CODE) AS country_code
        , AMAZON_DOMAIN AS amazon_url
        , okr.PROCESSED_AT AS page_scrape_time_pst
        , CONVERT_TIMEZONE('UTC','America/Los_Angeles', okr.ingested_date::timestamp) AS report_fetched_and_loaded_at_pst
        , NULL AS delivery_zip_code
        , COALESCE(pa3.parent_asin, pa.parent_asin, pa2.parent_asin, okr.asin) AS parent_asin
        , COALESCE(bm.brand_code, ac.brand_code) AS brand_code
        , ac.seller_id
        , 'Desktop' AS user_agent
        , MD5(okr.SEARCH_TERM || '_' || okr.ingested_date) AS _task_id
        , 'branded' AS data_source
        , okr.sponsored as is_sponsored
    FROM dwh.raw.bq_amazon_okr_data_latest okr
    JOIN asin_catalog ac
        ON okr.asin = ac.asin
    LEFT JOIN dwh.prod.marketplace_asin_brand_mapping bm
        ON okr.asin = bm.asin
        AND UPPER(okr.MARKETPLACE_COUNTRY_CODE) = UPPER(bm.country_code)
    LEFT JOIN parent_asin pa
        ON okr.asin = pa.asin
        AND UPPER(okr.MARKETPLACE_COUNTRY_CODE) = UPPER(pa.country_code)
    LEFT JOIN parent_asin_2 pa2
        ON okr.asin = pa2.asin
        AND UPPER(okr.MARKETPLACE_COUNTRY_CODE) = UPPER(pa2.country_code)
    LEFT JOIN parent_asin_3 pa3
        ON okr.asin = pa3.asin
        AND UPPER(okr.MARKETPLACE_COUNTRY_CODE) = UPPER(pa3.country_code)
    QUALIFY ROW_NUMBER() OVER(PARTITION BY okr.SEARCH_TERM, okr.asin, okr.sponsored, okr.ingested_date, okr.MARKETPLACE_COUNTRY_CODE
        ORDER BY okr.PROCESSED_AT DESC) = 1
    )
select *
from heyday_okr_data
union all
select *
from branded_okr_data;
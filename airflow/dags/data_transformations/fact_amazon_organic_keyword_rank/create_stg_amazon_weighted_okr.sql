SET max_snapshot_date = (
    SELECT MIN(page_scrape_time_pst::DATE)
    FROM dwh.prod.fact_amazon_organic_keyword_rank
    WHERE record_updated_timestamp_utc > (
        SELECT COALESCE(MAX(record_updated_timestamp_utc), '1900-01-01')
        FROM $curated_db.fact_amazon_weighted_okr
    )
);

CREATE OR REPLACE TRANSIENT TABLE $stage_db.stg_amazon_weighted_okr AS
WITH asin_kw_okr_data AS (
    SELECT
        asin
        , brand_code
        , parent_asin
        , LOWER(keyword) AS query
        , UPPER(country_code) AS country_code
        , page_scrape_time_pst::DATE AS snapshot_date
        , MEDIAN(organic_rank) AS okr
    FROM $curated_db.fact_amazon_organic_keyword_rank
    WHERE page_scrape_time_pst::DATE >= $max_snapshot_date
    GROUP BY 1,2,3,4,5,6
)
, sqp_cte_metadata AS (
    SELECT
        asin
        , LOWER(qp_asin_query) AS query
        , brand_code
        , UPPER(country_code) AS country_code
        , weekly_week_start_date AS week_start
        , MAX(qp_asin_count_impressions) AS qp_asin_count_impressions
        , MAX(qp_asin_count_clicks) AS qp_asin_count_clicks
        , MAX(qp_asin_count_purchases) AS qp_asin_count_purchases
    FROM dwh.prod.fact_asin_view_report
    WHERE weekly_week_start_date >= DATEADD('month', -2, $max_snapshot_date)
    GROUP BY 1,2,3,4,5
)
, sqp_metadata_last_2_week AS (
    SELECT
        asin
        , query
        , brand_code
        , country_code
        , week_start
        , qp_asin_count_impressions
        , qp_asin_count_clicks
        , qp_asin_count_purchases
    FROM sqp_cte_metadata
    QUALIFY DENSE_RANK() OVER (ORDER BY week_start DESC) <= 2
)
, okr_with_sqp_data AS (
    SELECT
        a.asin
        , a.parent_asin
        , a.query
        , a.country_code
        , a.snapshot_date
        , a.okr
        , MAX(COALESCE(a.brand_code, b.brand_code)) AS brand_code
        , COUNT(DISTINCT b.week_start) week_start_cnt
        , SUM(b.qp_asin_count_impressions) AS qp_asin_count_impressions
        , SUM(b.qp_asin_count_clicks) AS qp_asin_count_clicks
        , SUM(b.qp_asin_count_purchases) AS qp_asin_count_purchases
    FROM asin_kw_okr_data a
    JOIN sqp_metadata_last_2_week b
        ON a.asin = b.asin
        AND a.query = b.query
        AND a.country_code = b.country_code
        AND b.week_start <= a.snapshot_date
    GROUP BY 1,2,3,4,5,6
)
, sqp_with_total_weighing_factor AS (
    SELECT
        asin
        , parent_asin
        , brand_code
        , country_code
        , snapshot_date
        , SUM(qp_asin_count_impressions) AS total_impressions
        , SUM(qp_asin_count_clicks) AS total_clicks
        , SUM(qp_asin_count_purchases) AS total_purchases
        , COUNT(query) AS tot_keyword_count
    FROM okr_with_sqp_data
    GROUP BY 1,2,3,4,5
)
, sqp_okr_mapping AS (
    SELECT
        a.asin
        , a.parent_asin
        , a.query
        , a.country_code
        , a.snapshot_date
        , a.okr
        , a.brand_code
        , a.week_start_cnt
        , a.qp_asin_count_impressions
        , a.qp_asin_count_clicks
        , a.qp_asin_count_purchases
        , b.total_impressions
        , b.total_clicks
        , b.total_purchases
        , b.tot_keyword_count
        , DIV0(a.qp_asin_count_impressions, b.total_impressions) AS weighted_factor_impressions
        , DIV0(a.qp_asin_count_clicks, b.total_clicks) AS weighted_factor_clicks
        , DIV0(a.qp_asin_count_purchases, b.total_purchases) AS weighted_factor_purchases
    FROM okr_with_sqp_data AS a
    LEFT JOIN sqp_with_total_weighing_factor AS b
        ON a.asin = b.asin
        AND a.country_code = b.country_code
        AND a.snapshot_date=b.snapshot_date
)
SELECT
    asin
    , parent_asin
    , ARRAY_AGG(DISTINCT query) WITHIN GROUP (ORDER BY query) AS query_list
    , brand_code
    , country_code
    , snapshot_date
    , ROUND(AVG(okr) ,2) AS okr_avg
    , ROUND(SUM(okr*weighted_factor_impressions) ,2) AS weighted_okr_impressions
    , AVG(total_impressions) AS total_impressions
    , ROUND(SUM(okr*weighted_factor_clicks) ,2) AS weighted_okr_clicks
    , AVG(total_clicks) AS total_clicks
    , ROUND(SUM(okr*weighted_factor_purchases) ,2) AS weighted_okr_purchases
    , AVG(total_purchases) AS total_purchases
FROM sqp_okr_mapping
GROUP BY 1,2,4,5,6;

CREATE OR REPLACE TABLE $curated_db.FACT_AMAZON_ESSOR_RMA_TEMP AS (
with daily_settlements_data as (
     SELECT to_date(report_date) 		    AS report_date
        , settlement_id		            AS settlement_id
        , seller_id		                AS seller_id
        , country_code                    AS country_code
        , type			                AS event_type
        , order_id		                AS order_id
        , CASE WHEN sku LIKE 'amzn.gr.%'
                THEN split_part(split_part(split_part(sku,'amzn.gr.',2),'-',1),'_',1)
                ELSE TRIM(TRANSLATE(COALESCE(REGEXP_REPLACE(sku, '^Amazon\\.Found\\.', ''), ''), ' ', ''))
          END		                    	AS sku
        , TRIM(TRANSLATE(description, ' ', ' ')) AS description
        , CASE
            WHEN marketplace IS NOT NULL AND TRIM(marketplace) <> '' THEN marketplace
            WHEN UPPER(country_code) = 'CA' AND order_id LIKE 'S0%' THEN 'si ca prod marketplace'
            WHEN UPPER(country_code) = 'CA' THEN 'amazon.ca'
            WHEN UPPER(country_code) = 'UK' AND order_id LIKE 'S0%' THEN 'si uk prod marketplace'
            WHEN UPPER(country_code) = 'UK' THEN 'amazon.co.uk'
            WHEN UPPER(country_code) = 'ES' AND order_id LIKE 'S0%' THEN 'si prod es marketplace'
            WHEN UPPER(country_code) = 'ES' THEN 'amazon.es'
            WHEN UPPER(country_code) = 'IT' AND order_id LIKE 'S0%' THEN 'si prod it marketplace'
            WHEN UPPER(country_code) = 'IT' THEN 'amazon.it'
            WHEN UPPER(country_code) = 'US' AND order_id LIKE 'S0%' THEN 'si prod us marketplace'
            WHEN UPPER(country_code) = 'US' THEN 'amazon.com'
            WHEN UPPER(country_code) = 'DE' AND order_id LIKE 'S0%' THEN 'si prod de marketplace'
            WHEN UPPER(country_code) = 'DE' THEN 'amazon.de'
            WHEN UPPER(country_code) = 'FR' AND order_id LIKE 'S0%' THEN 'si prod fr marketplace'
            WHEN UPPER(country_code) = 'FR' THEN 'amazon.fr'
            WHEN UPPER(country_code) = 'BR' THEN 'amazon.com.br'
            WHEN UPPER(country_code) = 'MX' THEN 'amazon.com.mx'
            WHEN UPPER(country_code) = 'PL' THEN 'amazon.pl'
            WHEN UPPER(country_code) = 'NL' THEN 'amazon.nl'
            WHEN UPPER(country_code) = 'SE' THEN 'amazon.se'
            WHEN UPPER(country_code) = 'TR' THEN 'amazon.com.tr'
            WHEN UPPER(country_code) = 'BE' THEN 'amazon.com.be'
            WHEN UPPER(country_code) = 'AE' THEN 'amazon.ae'
            WHEN UPPER(country_code) = 'AU' THEN 'amazon.com.au'
            ELSE marketplace
        END as marketplace
        , account_type		                AS account_type
        , fulfillment_channel 			    AS fulfillment_channel
        , order_city			            AS order_city
        , order_state			            AS order_state
        , order_postal		                AS order_postal
        , tax_collection_model			    AS tax_collection_model
        , 'drr' as source
        , CASE WHEN sku IS NOT NULL THEN 'Yes' ELSE 'No' END as is_sku_present
        , MAX(record_updated_timestamp_utc)	AS etl_batch_runtime
        , SUM(quantity)                     AS quantity
        , SUM(product_sales)			    AS product_sales
        , SUM(shipping_credits)			    AS shipping_credits
        , SUM(gift_wrap_credits)			AS gift_wrap_credits
        , SUM(product_sales_tax)			AS product_sales_tax
        , SUM(shipping_credits_tax)		    AS shipping_credits_tax
        , SUM(giftwrap_credits_tax)		    AS giftwrap_credits_tax
        , SUM(regulatory_fee)				AS regulatory_fee
        , SUM(tax_on_regulatory_fee)		AS tax_on_regulatory_fee
        , SUM(promotional_rebates)		    AS promotional_rebates
        , SUM(promotional_rebates_tax)	    AS promotional_rebates_tax
        , SUM(marketplace_withheld_tax)	    AS marketplace_withheld_tax
        , SUM(selling_fees)				    AS selling_fess
        , SUM(fba_fees)					    AS fba_fees
        , SUM(other_transaction_fees)		AS other_transaction_fees
        , SUM(other)						AS other
FROM DWH.PROD.FACT_AMAZON_SETTLEMENT_TRANSACTIONS_SCS t
    WHERE TRUE
    AND (record_updated_timestamp_utc > TO_TIMESTAMP_NTZ('$start_ts')
    OR to_date(report_date) IN (
            SELECT DISTINCT report_date
            FROM $curated_db.FACT_AMAZON_ESSOR_RMA
            WHERE posted_flag IN ('not ready to post', 'ready to post')
        )
    )
    group by all
)
, transalated_data as (
    select
        m.*,
        REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE( REPLACE( m.event_type,
                                'Ã¼','ü'),
                                'Ã³','ó'),
                                'Ã©','é'),
                                'Ãœ','Ü'),
                                'Ã–','Ö'),
                                'Ã¶','ö'),
                                'LogÃs', 'Logís'),
                                'lâ€™O','l’O'),
                                'pubblicitÃ','pubblicità'),
                                'Ã…','Å'),
                                'SipariÅŸ','Sipariş')
        AS type2,
        REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE( REPLACE( description,
                                'Ã¼','ü'),
                                'Ã³','ó'),
                                'Ã©','é'),
                                'Ãœ','Ü'),
                                'Ã–','Ö'),
                                'Ã¶','ö'),
                                'LogÃs', 'Logís'),
                                'lâ€™O','l’O'),
                                'pubblicitÃ','pubblicità'),
                                'Ã…','Å'),
                                'LogÃ­stica','Logística'),
                                'LogÃstica','Logística'),
                                'Coupon-Einlösungsgebühr', 'Coupon Redemption Fee'),
                                'Frais d''échange de coupon', 'Coupon Redemption Fee')
        AS description2,
        COALESCE(tt.translated_event_type, CASE
            WHEN type2 LIKE 'Tarifas de inventario de Log%' THEN 'FBA Inventory Fee'
            ELSE type2
            END
        ) as translated_type,
        COALESCE(dt.translated_event_description, CASE
            WHEN description2 LIKE 'Costo della pubblici%' THEN 'Cost of Advertising'
            WHEN description2 LIKE 'Frais de retour Expédié par Amazon%' THEN 'FBA Return Fee'
            WHEN description2 LIKE 'Frais de transport entran%' THEN 'Inbound Transportation Fee'
            WHEN description2 LIKE 'Frais de disposition Expédié par Amazon%' THEN 'FBA Disposal Fee'
            WHEN description2 LIKE 'Frais de Stockage de longue durée Expédié par Amazon%' THEN 'FBA Long-Term Storage Fee'
            WHEN description2 LIKE 'Tariffa di restituzione Logistica di Amazon%' THEN 'FBA Return Fee'
            WHEN description2 LIKE 'Frais d�étiquetage%' THEN 'FBA Prep Fee: Labeling'
            WHEN description2 LIKE 'Frais d’étiquetage' THEN 'FBA Prep Fee: Labeling'
            WHEN description2 LIKE 'Frais de dépassement de stock%' THEN 'Inventory Storage Overage Fee'
            WHEN description2 LIKE 'Frais de préparations supplémentaires%' THEN 'FBA Prep Fee: Labeling'
            WHEN description2 LIKE 'Gebühr für nicht eingeplante Serviceleistungen%' THEN 'FBA Prep Fee: Labeling'
            WHEN description2 LIKE 'Solicitud de retiro de inventario de Logística de Amazon: tarifa por baj%' THEN 'FBA Removal Fee'
            WHEN description2 LIKE 'CoÃ»t de la publicité%' THEN 'Cost of Advertising'
            ELSE description2
            END
        ) as translated_description
    from daily_settlements_data m
    left join dwh.raw.NRA_AMAZON_TYPES_TRANSLATION tt on type2 = tt.event_type
    left join dwh.raw.NRA_AMAZON_DESCRIPTIONS_TRANSLATION dt on description2 = dt.event_description
)
, metadata_mappings as (
    select td.*,
        ssm.netsuite_customer,
        ssm.netsuite_customer_internal_id,
        ssm.netsuite_subsidiary,
        ssm.netsuite_subsidiary_internal_id,
        ssm.netsuite_location,
        ssm.netsuite_location_internal_id,
        ssm.amazon_seller_account_name as seller_name,
        ssm.sales_channel,
        ssm.marketplace as geography,
        ssm.currency,
        replace(ssm.country_code, 'UK', 'GB') as c_country_code,
        nns.subsidiary_country as s_country_code,
        ng.geography_int_id as geography_id
    from transalated_data td
    left join DWH.RAW.NRA_AMAZON_SELLER_SUBSIDIARY_MAPPING ssm on td.seller_id=ssm.seller_id and td.country_code=ssm.country_code and ssm.fulfillment_type='FBA'
    left join netsuite.netsuite.netsuite_subsidiaries nns on nns.subsidiary_int_id=ssm.netsuite_subsidiary_internal_id
    left join netsuite.netsuite.netsuite_geographies ng on lower(ssm.marketplace)=lower(ng.geography_name) or lower(ssm.marketplace)=lower(ng.geography_iso_code)

)
, line_tax_mapping as (
    select mm.*,
        tcm.internal_id as tax_internal_id,
        tcm.name as tax_code
    from metadata_mappings mm
    left join DWH.RAW.NRA_AMAZON_TAX_CODES_MAPPING_LATEST tcm
       on lower(tcm.customer_country)=lower(mm.c_country_code) and lower(tcm.subsidiary_country)=lower(mm.s_country_code)
)
, sku_mapping as (
select ltm.*,
    coalesce(skum.brand, am.brand) as brand,
    coalesce(skum.class_id, am.class_id) as class_id,
    coalesce(skum.netsuite_item_type, am.netsuite_item_type) as netsuite_item_type,
    coalesce(skum.netsuite_item_number, am.netsuite_item_number) as ns_item_number,
    coalesce(skum.item_id, am.item_id) as item_id
from line_tax_mapping ltm
left join netsuite.netsuite.netsuite_postings_sku_mapping skum on ltm.sku=skum.sku
left join netsuite.netsuite.netsuite_postings_asin_as_sku_mapping as am on ltm.sku=am.sku and ltm.country_code=am.country_code
)
, unpivot_data as (
    select s.*
         , case
            when s.transaction_type = 'PRODUCT_SALES' then 'product sales'
            when s.transaction_type = 'SHIPPING_CREDITS' then 'shipping credits'
            when s.transaction_type = 'GIFT_WRAP_CREDITS' then 'gift wrap credits'
            when s.transaction_type = 'PRODUCT_SALES_TAX' then 'product sales tax'
            when s.transaction_type = 'SHIPPING_CREDITS_TAX' then 'shipping credits tax'
            when s.transaction_type = 'GIFTWRAP_CREDITS_TAX' then 'giftwrap credits tax'
            when s.transaction_type = 'REGULATORY_FEE' then 'regulatory fee'
            when s.transaction_type = 'TAX_ON_REGULATORY_FEE' then 'tax on regulatory fee'
            when s.transaction_type = 'PROMOTIONAL_REBATES' then 'promotional rebates'
            when s.transaction_type = 'PROMOTIONAL_REBATES_TAX' then 'promotional rebates tax'
            when s.transaction_type = 'MARKETPLACE_WITHHELD_TAX' then 'marketplace withheld tax'
            when s.transaction_type = 'SELLING_FESS' then 'selling fees'
            when s.transaction_type = 'FBA_FEES' then 'fba fees'
            when s.transaction_type = 'OTHER_TRANSACTION_FEES' then 'other transaction fees'
            when s.transaction_type = 'OTHER' then 'other'
            else s.transaction_type
         end as transaction_type_alias
        , NULL AS posted_flag
        , NULL AS netsuite_rma_sale_order_id
        , NULL AS netsuite_rma_sale_order_document_number
FROM sku_mapping s
    UNPIVOT (amount
        FOR transaction_type IN (
            PRODUCT_SALES
            ,SHIPPING_CREDITS
            ,GIFT_WRAP_CREDITS
            ,PRODUCT_SALES_TAX
            ,SHIPPING_CREDITS_TAX
            ,GIFTWRAP_CREDITS_TAX
            ,REGULATORY_FEE
            ,TAX_ON_REGULATORY_FEE
            ,PROMOTIONAL_REBATES
            ,PROMOTIONAL_REBATES_TAX
            ,MARKETPLACE_WITHHELD_TAX
            ,SELLING_FESS
            ,FBA_FEES
            ,OTHER_TRANSACTION_FEES
            ,OTHER
        )
    )
)
, gl_mapping as (
    select *
    from dwh.raw.NRA_AMAZON_GL_MAPPING_LATEST
    where transaction_type in (
        'product sales'
        ,'shipping credits'
        ,'gift wrap credits'
        ,'product sales tax'
        ,'shipping credits tax'
        ,'giftwrap credits tax'
        ,'regulatory fee'
        ,'tax on regulatory fee'
        ,'promotional rebates'
        ,'promotional rebates tax'
        ,'marketplace withheld tax'
        ,'selling fees'
        ,'fba fees'
        ,'other transaction fees'
        ,'other'
    )
)
, gl_mapped_cte as (
SELECT  md5(CAST(COALESCE(CAST(s.report_date 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.settlement_id 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.seller_id 			 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.country_code 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.translated_type 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.order_id 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.sku 		 	 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.translated_description 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.marketplace 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.account_type 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.order_city 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.order_state 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.order_postal 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.tax_collection_model  AS varchar), '') || '-' ||
                 COALESCE(CAST(s.fulfillment_channel 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.transaction_type 	 AS varchar), '')
            AS varchar)) 								 AS rma_pk
        ,s.report_date as posting_date
        ,s.settlement_id as custom_settlement_id
        ,s.seller_id
        ,s.seller_name
        ,case
            when coalesce(g.brand_allocation, g2.brand_allocation) = 'SKU' then s.class_id
            when coalesce(g.brand_allocation, g2.brand_allocation) = 'REVENUE' then (select class_int_id from netsuite.netsuite.netsuite_classes where lower(class_name)='no class')
            when coalesce(g.brand_allocation, g2.brand_allocation) = 'MULTIPLE_BRANDS' then (select class_int_id from netsuite.netsuite.netsuite_classes where lower(class_name)='multiple brands')
            when coalesce(g.brand_allocation, g2.brand_allocation) = 'Not used' then -1
            else null
        end as netsuite_brand_id
        ,case
            when coalesce(g.brand_allocation, g2.brand_allocation) = 'SKU' then s.brand
            when coalesce(g.brand_allocation, g2.brand_allocation) = 'REVENUE' then (select class_name from netsuite.netsuite.netsuite_classes where lower(class_name)='no class')
            when coalesce(g.brand_allocation, g2.brand_allocation) = 'MULTIPLE_BRANDS' then (select class_name from netsuite.netsuite.netsuite_classes where lower(class_name)='multiple brands')
            when coalesce(g.brand_allocation, g2.brand_allocation) = 'Not used' then 'Not used'
            else null
        end as brand
        ,s.geography
        ,s.geography_id
        ,s.netsuite_customer_internal_id
        ,s.netsuite_customer as netsuite_customer_name
        ,s.country_code
        ,s.netsuite_location
        ,s.netsuite_location_internal_id

        ,s.item_id
       ,CASE WHEN s.sku IS NOT NULL AND s.transaction_type_alias in ('product sales', 'shipping credits', 'gift wrap credits')
                and (lower(s.translated_type) != 'order' or lower(s.translated_type) like '%adjustment%')
            THEN s.ns_item_number
            ELSE coalesce(trim(g.NONINVENTORY_ITEM), trim(g2.NONINVENTORY_ITEM))
        END as netsuite_item_number
        ,CASE WHEN s.sku IS NOT NULL AND s.transaction_type_alias in ('product sales', 'shipping credits', 'gift wrap credits')
                and (lower(s.translated_type) != 'order' or lower(s.translated_type) like '%adjustment%')
            THEN s.item_id::varchar
            ELSE coalesce(g.NONINVENTORY_ITEM_INTERNAL_ID, g2.NONINVENTORY_ITEM_INTERNAL_ID)
         END as netsuite_id
        ,CASE WHEN s.sku IS NOT NULL AND s.transaction_type_alias in ('product sales', 'shipping credits', 'gift wrap credits')
                and (lower(s.translated_type) != 'order' or lower(s.translated_type) like '%adjustment%')
            THEN concat(s.netsuite_item_type, ' : ', coalesce(s.ns_item_number, ''))
            ELSE concat('Non-Inventory : ', coalesce(g.NONINVENTORY_ITEM, g2.NONINVENTORY_ITEM, ''))
         END as item
        ,split_part(item, ' : ', 1) as item_type

        ,s.netsuite_subsidiary_internal_id
        ,s.netsuite_subsidiary
        ,s.tax_code
        ,s.tax_internal_id

        ,md5(CAST( s.source || COALESCE(CAST(s.report_date	                    AS varchar), '') || '-' ||
                  COALESCE(CAST(s.country_code		            AS varchar), '') || '-' ||
                  COALESCE(CAST(s.seller_id		            AS varchar), '') || '-' ||
                  COALESCE(CAST((case
                    when s.order_id like 'S0%' and s.transaction_type_alias = 'fba fees' and lower(s.translated_type) = 'order'
                    then (select SALES_CHANNEL_INT_ID from netsuite.netsuite.netsuite_sales_channels where lower(SALES_CHANNEL_CODE) = 'no sales channel')
                    else (select SALES_CHANNEL_INT_ID from netsuite.netsuite.netsuite_sales_channels where lower(SALES_CHANNEL_CODE) = 'amazon')
                  end) AS VARCHAR), '') || '-' ||
                  COALESCE(CAST(s.netsuite_customer_internal_id     AS varchar), '') || '-' ||
                  COALESCE(CAST(s.netsuite_customer_internal_id   AS varchar), '')
                AS varchar)) 								        AS netsuite_rma_sale_order_post_key

        ,md5(CAST( s.source || COALESCE(CAST(s.report_date	            AS varchar), '') || '-' ||
                    COALESCE(CAST(s.country_code 		        AS varchar), '') || '-' ||
                    COALESCE(CAST(s.seller_id 	        AS varchar), '') || '-' ||
                    COALESCE(CAST(s.class_id 	        AS varchar), '') || '-' ||
                    COALESCE(CAST(s.netsuite_customer_internal_id AS varchar), '') || '-' ||
                    COALESCE(CAST(s.netsuite_location_internal_id AS varchar), '') || '-' ||
                    COALESCE(CAST(coalesce(netsuite_item_number, '') 		    AS varchar), '')
                AS varchar)) 								        AS rma_key
        ,s.netsuite_rma_sale_order_id
        ,s.netsuite_rma_sale_order_document_number
        ,s.order_id
        ,s.sku
        ,s.transaction_type_alias as transaction_type
        ,s.translated_description as description
        ,s.translated_type as event_type
        ,sum(s.quantity) as quantity
        ,sum(s.amount) as amount

        -- ,listagg(distinct s.event_type, '||') as og_event_type
        -- ,listagg(distinct s.type2, '||') as og_event_type_formatted

        -- ,listagg(distinct s.description, '||') as og_description
        -- ,listagg(distinct s.description2, '||') as og_description_formatted

        ,s.currency
        ,case
            when s.order_id like 'S0%' and s.transaction_type_alias = 'fba fees' and lower(s.translated_type) = 'order'
            then (select SALES_CHANNEL_INT_ID from netsuite.netsuite.netsuite_sales_channels where lower(SALES_CHANNEL_CODE) = 'no sales channel')
            else (select SALES_CHANNEL_INT_ID from netsuite.netsuite.netsuite_sales_channels where lower(SALES_CHANNEL_CODE) = 'amazon')
        end as sales_channel_id

        ,s.posted_flag

        ,SYSDATE()        			AS etl_batch_runtime
        ,s.source
        ,s.marketplace
        ,s.is_sku_present
        , coalesce(g.brand_allocation, g2.brand_allocation) as brand_allocation
        ,SYSDATE()        			AS record_created_timestamp_utc
        ,NULL 						AS record_updated_timestamp_utc
        ,NULL 						AS rma_sale_order_post_updated_timestamp_utc
FROM unpivot_data s
left join gl_mapping g
on lower(s.transaction_type_alias) = lower(g.transaction_type)
and lower(coalesce(s.translated_type, '')) = lower(coalesce(g.event_type, ''))
and lower(s.is_sku_present) = lower(g.is_sku_present)
AND trim(lower(g.description))  = CASE
                                            WHEN
                                                lower(s.translated_description) LIKE 'angebote%'
                                                OR lower(s.translated_description) LIKE 'deals-%'
                                                OR lower(s.translated_description) LIKE 'ofertas-%'
                                                OR lower(s.translated_description) LIKE 'offerte-%'
                                                OR lower(s.translated_description) LIKE 'offres-%'
                                            THEN 'deals'
                                            WHEN
                                                lower(s.translated_description) LIKE 'blitzangebot%'
                                                OR lower(s.translated_description) LIKE 'lightning deal-%'
                                                OR lower(s.translated_description) LIKE 'offerta lampo-%'
                                                OR lower(s.translated_description) LIKE 'vente flash-%'
                                                OR lower(s.translated_description) LIKE 'oferta flash%'
                                            THEN 'lightning deals'
                                            WHEN
                                                lower(s.translated_description) LIKE 'price discount%'
                                            THEN 'price discount'
                                            WHEN
                                                lower(s.translated_description) LIKE 'retrocharge for orderid%'
                                            THEN 'retrocharge for order'
                                            WHEN
                                                lower(s.translated_description) LIKE 'retrocharge reversal for orderid%'
                                            THEN 'retrocharge reversal for order'
                                            WHEN
                                                lower(s.translated_description) LIKE 'cross-account debt adjustment against%'
                                                OR lower(s.translated_description) LIKE 'cross-account debt adjustment for%'
                                            THEN 'cross-account debt adjustment'
                                            WHEN
                                                lower(s.translated_description) LIKE 'a la cuenta que finaliza en%'
                                                OR lower(s.translated_description) LIKE 'a la cuenta acabada en%'
                                                OR lower(s.translated_description) LIKE 'al conto che termina con%'
                                                OR lower(s.translated_description) LIKE 'an konto mit der endung%'
                                                OR lower(s.translated_description) LIKE 'au compte se terminant parâ%'
                                                OR lower(s.translated_description) LIKE 'au compte se terminant par%'
                                                OR lower(s.translated_description) LIKE 'numarayla biten hesa%'
                                                OR lower(s.translated_description) LIKE 'en la cuenta que termina en%'
                                                OR lower(s.translated_description) LIKE 'für konten mit folgenden endziffern%'
                                                OR lower(s.translated_description) LIKE 'naar bankrekening die eindigt met%'
                                                OR lower(s.translated_description) LIKE 'naar rekening met als laatste cijfers%'
                                                OR lower(s.translated_description) LIKE 'na rachunek bankowy%'
                                                OR lower(s.translated_description) LIKE 'na rachunek o numerze%'
                                                OR lower(s.translated_description) LIKE 'para la cuenta que termina en%'
                                                OR lower(s.translated_description) LIKE 'per il conto che termina con%'
                                                OR lower(s.translated_description) LIKE 'sur le compte se terminant par%'
                                                OR lower(s.translated_description) LIKE 'till bankkontot som slutar p%'
                                                OR lower(s.translated_description) LIKE 'till konto som slutar med%'
                                                OR lower(s.translated_description) LIKE 'to account ending in%'
                                                OR lower(s.translated_description) LIKE 'to account ending with%'
                                                OR lower(s.translated_description) LIKE 'to your account ending in%'
                                                OR lower(s.translated_description) LIKE 'vers le compte finissant en%'
                                                OR lower(s.translated_description) LIKE 'para conta terminando com%'
                                                OR lower(s.translated_description) LIKE 'conta bancÃ¡ria com final%'
                                                OR lower(s.translated_description) LIKE '%ria com final%'
                                                OR lower(s.translated_description) LIKE '%numarayla biten hesaba%'
                                                OR lower(s.translated_description) LIKE '%numarayla biten hesap%'
                                            THEN 'account number'
                                            WHEN lower(s.translated_description) LIKE 'coupon redemption fee%' THEN 'coupon redemption fee'
                                            WHEN lower(s.translated_description) LIKE 'fba customer returns fee (non-apparel and non-shoes)%' THEN 'fba customer returns fee (non-apparel and non-shoes)'
                                            WHEN lower(s.translated_description) LIKE 'amzn1.cam.v1.sgid%' THEN 'amzn1.cam.v1.sgid.xxxxxxxxxxx'
                                            ELSE trim(lower(s.translated_description))
                                        END
left join gl_mapping g2
on lower(s.transaction_type_alias) = lower(g2.transaction_type)
and lower(coalesce(s.translated_type, '')) = lower(coalesce(g2.event_type, ''))
and lower(s.is_sku_present) = lower(g2.is_sku_present)
AND trim(lower(g2.description))='not used'
group by all
)
select f.*, coalesce(p.rw, -1) as rw, coalesce(p.group_id, -1) as group_id
from gl_mapped_cte as f
left join (
select
    distinct
    netsuite_rma_sale_order_post_key,
    rma_key,
    dense_rank() over (partition by netsuite_rma_sale_order_post_key order by rma_key) as rw,
    floor(rw/400)+1 as group_id
from gl_mapped_cte
where amount<>0
) as p on p.netsuite_rma_sale_order_post_key=f.netsuite_rma_sale_order_post_key
and p.rma_key=f.rma_key
);

CREATE OR REPLACE TEMPORARY TABLE $curated_db.FACT_AMAZON_ESSOR_RMA_GROUPING_TEMP AS (
with rma_posted_group_info as (
       select
            distinct netsuite_rma_sale_order_post_key,
            rma_key,
            CAST(split_part(uuid, '_', 2) AS int) as group_id,
       from $curated_db.FACT_AMAZON_RMA_AGGREGATE
       where posting_status IS NOT NULL
)
, newly_available_records as (
       select a.*
       from $curated_db.FACT_AMAZON_ESSOR_RMA_TEMP a
       left join rma_posted_group_info b
       on a.netsuite_rma_sale_order_post_key=b.netsuite_rma_sale_order_post_key
       and a.rma_key=b.rma_key
       and a.group_id=b.group_id
       where b.netsuite_rma_sale_order_post_key is null
)
select f.* EXCLUDE(rw, group_id)
       , coalesce(p.rw, -1) as rw
       , coalesce(p.group_id + coalesce(pa.max_group_id, 0), -1) as group_id
       from newly_available_records as f
left join (
   select
       distinct
       netsuite_rma_sale_order_post_key,
       rma_key,
       dense_rank() over (partition by netsuite_rma_sale_order_post_key order by rma_key) as rw,
       floor(rw/400)+1 as group_id
   from newly_available_records
   where amount<>0
) as p on p.netsuite_rma_sale_order_post_key=f.netsuite_rma_sale_order_post_key
and p.rma_key=f.rma_key
left join (
   select
       netsuite_rma_sale_order_post_key, max(group_id) as max_group_id
   from rma_posted_group_info
   group by all
) pa on pa.netsuite_rma_sale_order_post_key=f.netsuite_rma_sale_order_post_key
);

--assign new group id for newly records found in the settlements table
UPDATE $curated_db.FACT_AMAZON_ESSOR_RMA_TEMP tgt
SET rw = src.rw, group_id = src.group_id
FROM $curated_db.FACT_AMAZON_ESSOR_RMA_GROUPING_TEMP src
WHERE tgt.rma_pk=src.rma_pk;

--assign flags for all the rows based on the mapping data
UPDATE $curated_db.FACT_AMAZON_ESSOR_RMA_TEMP
SET posted_flag = CASE  WHEN amount= 0 THEN 'do not post'
                        WHEN netsuite_id IS NULL OR netsuite_brand_id IS NULL OR netsuite_item_number IS NULL OR netsuite_customer_internal_id IS NULL OR netsuite_subsidiary_internal_id IS NULL OR netsuite_location_internal_id IS NULL OR geography_id is NULL OR tax_internal_id IS NULL
                        THEN 'not ready to post'
                        WHEN (lower(netsuite_item_number) = 'not used') THEN 'do not post'
                        ELSE 'ready to post'
                    END;

--ignore the non-heyday skus that are sent by amazon
UPDATE $curated_db.FACT_AMAZON_ESSOR_RMA_TEMP  T
SET posted_flag = 'do not post'
FROM dwh.staging.invalid_skus S
WHERE T.sku = S."sku" ;

INSERT into $curated_db.REVENUE_SPLIT_BY_BRAND
with target_dates as (
    select distinct posting_date from $curated_db.FACT_AMAZON_ESSOR_RMA_TEMP
)
,brand_revenue as (
    select
        td.posting_date as posting_date,
        f.seller_id,
        f.country_code,
        f.brand,
        f.netsuite_brand_id,
        sum(f.amount) as brand_amount
    from target_dates td
    join dwh.prod.fact_amazon_subledger_essor f
      on f.posting_date between td.posting_date - interval '30 days' and td.posting_date
    group by td.posting_date, f.seller_id, f.country_code, f.brand, f.netsuite_brand_id
)
,total_revenue as (
    select
        td.posting_date as posting_date,
        f.seller_id,
        f.country_code,
        sum(f.amount) as total_amount
    from target_dates td
    join dwh.prod.fact_amazon_subledger_essor f
      on f.posting_date between td.posting_date - interval '30 days' and td.posting_date
    group by td.posting_date, f.seller_id, f.country_code
)
, revenue_split_info as (
    select
    br.seller_id,
    br.country_code,
    br.posting_date,
    br.brand,
    br.netsuite_brand_id,
    br.brand_amount,
    tr.total_amount,
    div0(br.brand_amount, tr.total_amount) as brand_ratio
    from brand_revenue br
    join total_revenue tr
      on br.seller_id = tr.seller_id
      and br.country_code = tr.country_code
      and br.posting_date = tr.posting_date
)
select *, sysdate() as snapshot_date from revenue_split_info;

BEGIN TRANSACTION;

--insert only when record is not already present 
INSERT INTO $curated_db.FACT_AMAZON_ESSOR_RMA (
    RMA_PK,
    POSTING_DATE,
    CUSTOM_SETTLEMENT_ID,
    SELLER_ID,
    SELLER_NAME,
    NETSUITE_BRAND_ID,
    BRAND,
    GEOGRAPHY,
    GEOGRAPHY_ID,
    NETSUITE_CUSTOMER_INTERNAL_ID,
    NETSUITE_CUSTOMER_NAME,
    COUNTRY_CODE,
    NETSUITE_LOCATION,
    NETSUITE_LOCATION_INTERNAL_ID,
    ITEM_ID,
    NETSUITE_ITEM_NUMBER,
    NETSUITE_ID,
    ITEM,
    ITEM_TYPE,
    NETSUITE_SUBSIDIARY_INTERNAL_ID,
    NETSUITE_SUBSIDIARY,
    TAX_CODE,
    TAX_INTERNAL_ID,
    NETSUITE_RMA_SALE_ORDER_POST_KEY,
    RMA_KEY,
    NETSUITE_RMA_SALE_ORDER_ID,
    NETSUITE_RMA_SALE_ORDER_DOCUMENT_NUMBER,
    ORDER_ID,
    SKU,
    TRANSACTION_TYPE,
    DESCRIPTION,
    EVENT_TYPE,
    QUANTITY,
    AMOUNT,
    CURRENCY,
    SALES_CHANNEL_ID,
    POSTED_FLAG,
    ETL_BATCH_RUNTIME,
    SOURCE,
    MARKETPLACE,
    IS_SKU_PRESENT,
    BRAND_ALLOCATION,
    RECORD_CREATED_TIMESTAMP_UTC,
    RECORD_UPDATED_TIMESTAMP_UTC,
    RMA_SALE_ORDER_POST_UPDATED_TIMESTAMP_UTC,
    RW,
    GROUP_ID
)
SELECT s.* 
FROM $curated_db.FACT_AMAZON_ESSOR_RMA_TEMP s
LEFT JOIN $curated_db.FACT_AMAZON_ESSOR_RMA tgt
ON s.rma_pk = tgt.rma_pk 
WHERE tgt.rma_pk IS NULL; 

--update only when the mapping or amounts have changed 
UPDATE $curated_db.FACT_AMAZON_ESSOR_RMA S
SET  netsuite_customer_internal_id        = T.netsuite_customer_internal_id     
    ,netsuite_customer_name               = T.netsuite_customer_name
    ,netsuite_location                    = T.netsuite_location                 
    ,netsuite_location_internal_id        = T.netsuite_location_internal_id
    ,netsuite_subsidiary_internal_id      = T.netsuite_subsidiary_internal_id
    ,netsuite_subsidiary                  = T.netsuite_subsidiary
    ,seller_name                          = T.seller_name
    ,tax_internal_id                      = T.tax_internal_id
    ,tax_code                             = T.tax_code
    ,item_type                            = T.item_type                         
    ,netsuite_item_number                 = T.netsuite_item_number             
    ,netsuite_id                          = T.netsuite_id                       
    ,item                                 = T.item
    ,netsuite_rma_sale_order_post_key     = T.netsuite_rma_sale_order_post_key
    ,rma_key                              = T.rma_key                     
    ,quantity                             = T.quantity                          
    ,amount                               = T.amount                            
    ,currency                             = T.currency      
    ,marketplace                          = T.marketplace
    ,is_sku_present                       = T.is_sku_present
    ,brand_allocation                     = T.brand_allocation
    ,sales_channel_id                     = T.sales_channel_id
    ,posted_flag					      = T.posted_flag
    ,netsuite_brand_id					  = T.netsuite_brand_id
    ,brand					  = T.brand
    ,geography					  = T.geography
    ,geography_id				  = T.geography_id
    ,group_id					  = T.group_id
    ,rw					  = T.rw
    ,record_updated_timestamp_utc         = SYSDATE()
FROM (
    SELECT T.*
    FROM $curated_db.FACT_AMAZON_ESSOR_RMA_TEMP T
    JOIN $curated_db.FACT_AMAZON_ESSOR_RMA S
        ON S.rma_pk  = T.rma_pk
    LEFT JOIN  $curated_db.FACT_AMAZON_RMA_AGGREGATE A
		ON S.rma_key = A.rma_key
    WHERE   S.posted_flag IN ('ready to post','not ready to post','do not post','DISPUTED_TRANSACTION')
        AND A.posting_status IS NULL --avoid updates if cashsales are in the middle of posting
        AND(COALESCE(S.netsuite_customer_internal_id,0) != COALESCE(T.netsuite_customer_internal_id,0)  OR
            COALESCE(S.netsuite_subsidiary_internal_id,0) != COALESCE(T.netsuite_subsidiary_internal_id,0)  OR
            COALESCE(S.netsuite_location_internal_id,0) != COALESCE(T.netsuite_location_internal_id,0) OR
            COALESCE(S.geography_id,0) != COALESCE(T.geography_id,0) OR
            COALESCE(S.tax_internal_id,0) != COALESCE(T.tax_internal_id,0) OR
            COALESCE(S.netsuite_item_number,'')	  != COALESCE(T.netsuite_item_number,'')   	 OR
            COALESCE(S.brand,'')   			  != COALESCE(T.brand,'')   			 OR
            COALESCE(S.netsuite_brand_id,0)   			  != COALESCE(T.netsuite_brand_id,0)   			 OR
            to_numeric(COALESCE(S.amount,0),18,2)	  != to_numeric(COALESCE(T.amount,0),18,2)      	 OR
            COALESCE(S.quantity,0)			  != COALESCE(T.quantity,0) OR
            COALESCE(S.posted_flag,'')   			  != COALESCE(T.posted_flag,'')
        )
    ) T
WHERE S.rma_pk  = T.rma_pk;

--this update is to get the additional mapping updates that are just used for reference and do not impact netsuite posting. this could happen after a transaction is posted and we do not want to reprocess
UPDATE $curated_db.FACT_AMAZON_ESSOR_RMA S
SET  netsuite_customer_name	    = T.netsuite_customer_name
    ,netsuite_subsidiary        = T.netsuite_subsidiary
    ,netsuite_location          = T.netsuite_location
    ,seller_name                = T.seller_name
    ,geography                  = T.geography
    ,tax_code                   = T.tax_code
    ,netsuite_item_number       = T.netsuite_item_number -- ??
FROM $curated_db.FACT_AMAZON_ESSOR_RMA_TEMP T
WHERE  S.rma_pk  = T.rma_pk
AND   (COALESCE(S.netsuite_customer_name,'')  != COALESCE(T.netsuite_customer_name,'')  OR
        COALESCE(S.netsuite_subsidiary,'') 		!= COALESCE(T.netsuite_subsidiary,'')   		OR
        COALESCE(S.netsuite_location,'') 		!= COALESCE(T.netsuite_location,'')   		OR
        COALESCE(S.seller_name,'') 		!= COALESCE(T.seller_name,'')   		OR
        COALESCE(S.geography,'') 		!= COALESCE(T.geography,'')   		OR
        COALESCE(S.tax_code,'') 		!= COALESCE(T.tax_code,'')   		OR
        COALESCE(S.netsuite_id,'')			!= COALESCE(T.netsuite_id,'')
        );

-- Update flag for late arriving transactions
CREATE OR REPLACE TEMPORARY TABLE $stage_db.netsuite_rma_amazon_disputed_sale_orders AS
SELECT  aggr.netsuite_rma_sale_order_post_key,
        aggr.group_id,
        aggr.rma_key,
        aggr.posting_status
FROM (
    SELECT  netsuite_rma_sale_order_post_key,
            rma_key,
            posting_status,
            MIN(CAST(split_part(uuid, '_', 2) AS int)) as group_id,
            NVL(SUM(amount), 0) AS tot_amount
    	FROM $curated_db.FACT_AMAZON_RMA_AGGREGATE
    	WHERE posting_date >= DATEADD(DAY, -61, TO_DATE(CURRENT_TIMESTAMP()))
    	AND posting_status is not null
    	GROUP BY netsuite_rma_sale_order_post_key, rma_key, posting_status
) aggr
LEFT JOIN (
	SELECT	netsuite_rma_sale_order_post_key,
	        rma_key,
	        MIN(group_id) AS group_id,
            NVL(SUM(amount), 0) AS tot_amount
    	FROM $curated_db.FACT_AMAZON_ESSOR_RMA
	WHERE posted_flag <> 'do not post'
		AND posting_date >= DATEADD(DAY, -61, TO_DATE(CURRENT_TIMESTAMP()))
		AND posting_date <= current_date() - 2
	    AND UPPER(netsuite_item_number) <> 'NOT USED' -- ??
	GROUP BY netsuite_rma_sale_order_post_key, rma_key
) sl ON aggr.netsuite_rma_sale_order_post_key = sl.netsuite_rma_sale_order_post_key
    AND aggr.rma_key = sl.rma_key
WHERE TO_NUMERIC(COALESCE(aggr.tot_amount, 0), 18, 2) != TO_NUMERIC(COALESCE(sl.tot_amount, 0), 18, 2)
OR TO_NUMERIC(COALESCE(aggr.group_id, 0), 18, 2) != TO_NUMERIC(COALESCE(sl.group_id, 0), 18, 2);

CREATE OR REPLACE TEMPORARY TABLE $stage_db.netsuite_rma_new_records_same_key_already_posted as
(with next_group AS (
  SELECT
    netsuite_rma_sale_order_post_key,
    MAX(group_id) + 1 AS new_group_id
  FROM $curated_db.FACT_AMAZON_ESSOR_RMA
  GROUP BY netsuite_rma_sale_order_post_key
)
select c.new_group_id, a.* from $curated_db.FACT_AMAZON_ESSOR_RMA a
join $stage_db.netsuite_rma_amazon_disputed_sale_orders b
on a.NETSUITE_RMA_SALE_ORDER_POST_KEY=b.NETSUITE_RMA_SALE_ORDER_POST_KEY
and a.GROUP_ID=b.GROUP_ID
and a.RMA_KEY=b.RMA_KEY
left join next_group c on
a.NETSUITE_RMA_SALE_ORDER_POST_KEY=c.NETSUITE_RMA_SALE_ORDER_POST_KEY
where a.posted_flag='ready to post')
;

UPDATE $curated_db.FACT_AMAZON_ESSOR_RMA tgt
SET group_id = src.new_group_id
FROM $stage_db.netsuite_rma_new_records_same_key_already_posted src
WHERE tgt.rma_pk=src.rma_pk;

CREATE OR REPLACE TEMPORARY TABLE $stage_db.netsuite_rma_new_records_same_key_difference_amount_already_posted as
SELECT T.* EXCLUDE(amount, quantity, posted_flag),
    (T.amount - S.amount)   AS amount,
    (T.quantity - S.quantity)   AS quantity,
    CASE WHEN (T.amount - S.amount) < 0 OR (T.quantity - S.quantity) < 0
            THEN 'DISPUTED_TRANSACTION'
         WHEN (T.amount - S.amount) = 0
            THEN 'do not post'
            ELSE 'ready to post'
    END as posted_flag
    FROM $curated_db.FACT_AMAZON_ESSOR_RMA_TEMP T
    JOIN $curated_db.FACT_AMAZON_ESSOR_RMA S
        ON S.rma_pk  = T.rma_pk
    LEFT JOIN $curated_db.FACT_AMAZON_RMA_AGGREGATE A
		ON S.rma_key = A.rma_key
    WHERE S.posted_flag NOT IN ('ready to post','not ready to post','do not post','DISPUTED_TRANSACTION')
        AND A.posting_status IS NOT NULL
        AND(
            to_numeric(COALESCE(S.amount,0),18,2)	  != to_numeric(COALESCE(T.amount,0),18,2)      	 OR
            COALESCE(S.quantity,0)			  != COALESCE(T.quantity,0)
        )
        ;

UPDATE $stage_db.netsuite_rma_new_records_same_key_difference_amount_already_posted tgt
SET group_id = src.new_group_id
FROM (
   SELECT
    netsuite_rma_sale_order_post_key,
    MAX(group_id) + 1 AS new_group_id
  FROM $curated_db.FACT_AMAZON_ESSOR_RMA
  GROUP BY netsuite_rma_sale_order_post_key) src
WHERE tgt.netsuite_rma_sale_order_post_key=src.netsuite_rma_sale_order_post_key;

INSERT INTO $curated_db.FACT_AMAZON_ESSOR_RMA (
    RMA_PK,
    POSTING_DATE,
    CUSTOM_SETTLEMENT_ID,
    SELLER_ID,
    SELLER_NAME,
    NETSUITE_BRAND_ID,
    BRAND,
    GEOGRAPHY,
    GEOGRAPHY_ID,
    NETSUITE_CUSTOMER_INTERNAL_ID,
    NETSUITE_CUSTOMER_NAME,
    COUNTRY_CODE,
    NETSUITE_LOCATION,
    NETSUITE_LOCATION_INTERNAL_ID,
    ITEM_ID,
    NETSUITE_ITEM_NUMBER,
    NETSUITE_ID,
    ITEM,
    ITEM_TYPE,
    NETSUITE_SUBSIDIARY_INTERNAL_ID,
    NETSUITE_SUBSIDIARY,
    TAX_CODE,
    TAX_INTERNAL_ID,
    NETSUITE_RMA_SALE_ORDER_POST_KEY,
    RMA_KEY,
    NETSUITE_RMA_SALE_ORDER_ID,
    NETSUITE_RMA_SALE_ORDER_DOCUMENT_NUMBER,
    ORDER_ID,
    SKU,
    TRANSACTION_TYPE,
    DESCRIPTION,
    EVENT_TYPE,
    CURRENCY,
    SALES_CHANNEL_ID,
    ETL_BATCH_RUNTIME,
    SOURCE,
    MARKETPLACE,
    IS_SKU_PRESENT,
    BRAND_ALLOCATION,
    RECORD_CREATED_TIMESTAMP_UTC,
    RECORD_UPDATED_TIMESTAMP_UTC,
    RMA_SALE_ORDER_POST_UPDATED_TIMESTAMP_UTC,
    RW,
    GROUP_ID,
    AMOUNT,
    QUANTITY,
    POSTED_FLAG
)
SELECT s.*
FROM $stage_db.netsuite_rma_new_records_same_key_difference_amount_already_posted s;

CREATE OR REPLACE TABLE $stage_db.netsuite_rma_amazon_disputed_sale_order_final AS
SELECT  aggr.netsuite_rma_sale_order_post_key,
        aggr.group_id,
        aggr.rma_key,
        aggr.posting_status
FROM (
    SELECT  netsuite_rma_sale_order_post_key,
            rma_key,
            posting_status,
            (CAST(split_part(uuid, '_', 2) AS int)) as group_id,
            NVL(SUM(amount), 0) AS tot_amount
    	FROM $curated_db.FACT_AMAZON_RMA_AGGREGATE
    	WHERE posting_date >= DATEADD(DAY, -61, TO_DATE(CURRENT_TIMESTAMP()))
    	AND posting_status is not null
    	GROUP BY all
) aggr
LEFT JOIN (
	SELECT	netsuite_rma_sale_order_post_key,
	        rma_key,
	        group_id AS group_id,
            NVL(SUM(amount), 0) AS tot_amount
    	FROM $curated_db.FACT_AMAZON_ESSOR_RMA
	WHERE posted_flag <> 'do not post'
		AND posting_date >= DATEADD(DAY, -61, TO_DATE(CURRENT_TIMESTAMP()))
		AND posting_date <= current_date() - 2
	    AND UPPER(netsuite_item_number) <> 'NOT USED' -- ??
	GROUP BY all
) sl ON aggr.netsuite_rma_sale_order_post_key = sl.netsuite_rma_sale_order_post_key
    AND aggr.rma_key = sl.rma_key
    AND aggr.group_id = sl.group_id
WHERE TO_NUMERIC(COALESCE(aggr.tot_amount, 0), 18, 2) != TO_NUMERIC(COALESCE(sl.tot_amount, 0), 18, 2);

UPDATE $curated_db.FACT_AMAZON_ESSOR_RMA
SET posted_flag = 'DISPUTED_TRANSACTION',
    record_updated_timestamp_utc = SYSDATE()
WHERE concat(netsuite_rma_sale_order_post_key, '_', group_id) IN (
        SELECT DISTINCT concat(netsuite_rma_sale_order_post_key, '_', group_id)
        FROM $stage_db.netsuite_rma_amazon_disputed_sale_order_final
        WHERE UPPER(posting_status) IN ('POSTED', 'MANUAL_POST','SUBMITTED_TO_NETSUITE')
    )
    AND LOWER(posted_flag) != 'do not post';

DROP TABLE $curated_db.FACT_AMAZON_ESSOR_RMA_TEMP;
DROP TABLE $curated_db.FACT_AMAZON_ESSOR_RMA_GROUPING_TEMP;
DROP TABLE $stage_db.netsuite_rma_amazon_disputed_sale_orders;
DROP TABLE $stage_db.netsuite_rma_amazon_disputed_sale_order_final;
DROP TABLE $stage_db.netsuite_rma_new_records_same_key_difference_amount_already_posted;

COMMIT; 
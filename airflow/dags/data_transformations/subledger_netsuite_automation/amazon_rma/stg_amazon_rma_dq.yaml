---
test_id: "fact_amazon_rma_dup_check"
enabled: true
query: |
  with daily_settlements_data as (
    SELECT to_date(report_date) AS report_date
      , settlement_id AS settlement_id
      , seller_id AS seller_id
      , country_code AS country_code
      , type AS event_type
      , order_id AS order_id
      , CASE 
          WHEN sku LIKE 'amzn.gr.%'
            THEN split_part(split_part(split_part(sku,'amzn.gr.',2),'-',1),'_',1)
          ELSE TRIM(TRANSLATE(COALESCE(REGEXP_REPLACE(sku, '^Amazon\\.Found\\.', ''), ''), ' ', ''))
        END AS sku
      , TRIM(TRANSLATE(description, ' ', ' ')) AS description
      , CASE
          WHEN marketplace IS NOT NULL AND TRIM(marketplace) <> '' THEN marketplace
          WHEN UPPER(country_code) = 'CA' AND order_id LIKE 'S0%' THEN 'si ca prod marketplace'
          WHEN UPPER(country_code) = 'CA' THEN 'amazon.ca'
          WHEN UPPER(country_code) = 'UK' AND order_id LIKE 'S0%' THEN 'si uk prod marketplace'
          WHEN UPPER(country_code) = 'UK' THEN 'amazon.co.uk'
          WHEN UPPER(country_code) = 'ES' AND order_id LIKE 'S0%' THEN 'si prod es marketplace'
          WHEN UPPER(country_code) = 'ES' THEN 'amazon.es'
          WHEN UPPER(country_code) = 'IT' AND order_id LIKE 'S0%' THEN 'si prod it marketplace'
          WHEN UPPER(country_code) = 'IT' THEN 'amazon.it'
          WHEN UPPER(country_code) = 'US' AND order_id LIKE 'S0%' THEN 'si prod us marketplace'
          WHEN UPPER(country_code) = 'US' THEN 'amazon.com'
          WHEN UPPER(country_code) = 'DE' AND order_id LIKE 'S0%' THEN 'si prod de marketplace'
          WHEN UPPER(country_code) = 'DE' THEN 'amazon.de'
          WHEN UPPER(country_code) = 'FR' AND order_id LIKE 'S0%' THEN 'si prod fr marketplace'
          WHEN UPPER(country_code) = 'FR' THEN 'amazon.fr'
          WHEN UPPER(country_code) = 'BR' THEN 'amazon.com.br'
          WHEN UPPER(country_code) = 'MX' THEN 'amazon.com.mx'
          WHEN UPPER(country_code) = 'PL' THEN 'amazon.pl'
          WHEN UPPER(country_code) = 'NL' THEN 'amazon.nl'
          WHEN UPPER(country_code) = 'SE' THEN 'amazon.se'
          WHEN UPPER(country_code) = 'TR' THEN 'amazon.com.tr'
          WHEN UPPER(country_code) = 'BE' THEN 'amazon.com.be'
          WHEN UPPER(country_code) = 'AE' THEN 'amazon.ae'
          WHEN UPPER(country_code) = 'AU' THEN 'amazon.com.au'
          ELSE marketplace
        END as marketplace
      , account_type AS account_type
      , fulfillment_channel AS fulfillment_channel
      , order_city AS order_city
      , order_state AS order_state
      , order_postal AS order_postal
      , tax_collection_model AS tax_collection_model
      , 'drr' as source
      , CASE WHEN sku IS NOT NULL THEN 'Yes' ELSE 'No' END as is_sku_present
      , MAX(record_updated_timestamp_utc) AS etl_batch_runtime
      , SUM(quantity) AS quantity
      , SUM(product_sales) AS product_sales
      , SUM(shipping_credits) AS shipping_credits
      , SUM(gift_wrap_credits) AS gift_wrap_credits
      , SUM(product_sales_tax) AS product_sales_tax
      , SUM(shipping_credits_tax) AS shipping_credits_tax
      , SUM(giftwrap_credits_tax) AS giftwrap_credits_tax
      , SUM(regulatory_fee) AS regulatory_fee
      , SUM(tax_on_regulatory_fee) AS tax_on_regulatory_fee
      , SUM(promotional_rebates) AS promotional_rebates
      , SUM(promotional_rebates_tax) AS promotional_rebates_tax
      , SUM(marketplace_withheld_tax) AS marketplace_withheld_tax
      , SUM(selling_fees) AS selling_fess
      , SUM(fba_fees) AS fba_fees
      , SUM(other_transaction_fees) AS other_transaction_fees
      , SUM(other) AS other
    FROM DWH.PROD.FACT_AMAZON_SETTLEMENT_TRANSACTIONS_SCS t
    WHERE TRUE
      AND to_date(report_date) >= DATEADD(DAY,-5,TO_DATE(CURRENT_TIMESTAMP()))  
    group by all
  )
  , transalated_data as (
    select
      m.*,
      REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(m.event_type,
        'Ã¼','ü'),
        'Ã³','ó'),
        'Ã©','é'),
        'Ãœ','Ü'),
        'Ã–','Ö'),
        'Ã¶','ö'),
        'LogÃs', 'Logís'),
        'lâ€™O','l’O'),
        'pubblicitÃ','pubblicità'),
        'Ã…','Å'),
        'SipariÅŸ','Sipariş')
      AS type2,
      REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(description,
        'Ã¼','ü'),
        'Ã³','ó'),
        'Ã©','é'),
        'Ãœ','Ü'),
        'Ã–','Ö'),
        'Ã¶','ö'),
        'LogÃs', 'Logís'),
        'lâ€™O','l’O'),
        'pubblicitÃ','pubblicità'),
        'Ã…','Å'),
        'LogÃ­stica','Logística'),
        'LogÃstica','Logística'),
        'Coupon-Einlösungsgebühr', 'Coupon Redemption Fee'),
        'Frais d''échange de coupon', 'Coupon Redemption Fee')
      AS description2,
      COALESCE(tt.translated_event_type, CASE
        WHEN type2 LIKE 'Tarifas de inventario de Log%' THEN 'FBA Inventory Fee'
        ELSE type2
        END
      ) as translated_type,
      COALESCE(dt.translated_event_description, CASE
        WHEN description2 LIKE 'Costo della pubblici%' THEN 'Cost of Advertising'
        WHEN description2 LIKE 'Frais de retour Expédié par Amazon%' THEN 'FBA Return Fee'
        WHEN description2 LIKE 'Frais de transport entran%' THEN 'Inbound Transportation Fee'
        WHEN description2 LIKE 'Frais de disposition Expédié par Amazon%' THEN 'FBA Disposal Fee'
        WHEN description2 LIKE 'Frais de Stockage de longue durée Expédié par Amazon%' THEN 'FBA Long-Term Storage Fee'
        WHEN description2 LIKE 'Tariffa di restituzione Logistica di Amazon%' THEN 'FBA Return Fee'
        WHEN description2 LIKE 'Frais d�étiquetage%' THEN 'FBA Prep Fee: Labeling'
        WHEN description2 LIKE 'Frais d’étiquetage' THEN 'FBA Prep Fee: Labeling'
        WHEN description2 LIKE 'Frais de dépassement de stock%' THEN 'Inventory Storage Overage Fee'
        WHEN description2 LIKE 'Frais de préparations supplémentaires%' THEN 'FBA Prep Fee: Labeling'
        WHEN description2 LIKE 'Gebühr für nicht eingeplante Serviceleistungen%' THEN 'FBA Prep Fee: Labeling'
        WHEN description2 LIKE 'Solicitud de retiro de inventario de Logística de Amazon: tarifa por baj%' THEN 'FBA Removal Fee'
        WHEN description2 LIKE 'CoÃ»t de la publicité%' THEN 'Cost of Advertising'
        ELSE description2
        END
      ) as translated_description
    from daily_settlements_data m
    left join dwh.raw.NRA_AMAZON_TYPES_TRANSLATION tt on type2 = tt.event_type
    left join dwh.raw.NRA_AMAZON_DESCRIPTIONS_TRANSLATION dt on description2 = dt.event_description
  )
  , metadata_mappings as (
    select td.*,
      ssm.netsuite_customer,
      ssm.netsuite_customer_internal_id,
      ssm.netsuite_subsidiary,
      ssm.netsuite_subsidiary_internal_id,
      ssm.netsuite_location,
      ssm.netsuite_location_internal_id,
      ssm.amazon_seller_account_name as seller_name,
      ssm.sales_channel,
      ssm.marketplace as geography,
      ssm.currency,
      replace(ssm.country_code, 'UK', 'GB') as c_country_code,
      nns.subsidiary_country as s_country_code,
      ng.geography_int_id as geography_id
    from transalated_data td
    left join DWH.RAW.NRA_AMAZON_SELLER_SUBSIDIARY_MAPPING ssm on td.seller_id=ssm.seller_id and td.country_code=ssm.country_code and ssm.fulfillment_type='FBA'
    left join netsuite.netsuite.netsuite_subsidiaries nns on nns.subsidiary_int_id=ssm.netsuite_subsidiary_internal_id
    left join netsuite.netsuite.netsuite_geographies ng on lower(ssm.marketplace)=lower(ng.geography_name) or lower(ssm.marketplace)=lower(ng.geography_iso_code)
  )
  , line_tax_mapping as (
    select mm.*,
      tcm.internal_id as tax_internal_id,
      tcm.name as tax_code
    from metadata_mappings mm
    left join DWH.RAW.NRA_AMAZON_TAX_CODES_MAPPING_LATEST tcm
      on lower(tcm.customer_country)=lower(mm.c_country_code) and lower(tcm.subsidiary_country)=lower(mm.s_country_code)
  )
  , sku_mapping as (
    select ltm.*,
      coalesce(skum.brand, am.brand) as brand,
      coalesce(skum.class_id, am.class_id) as class_id,
      coalesce(skum.netsuite_item_type, am.netsuite_item_type) as netsuite_item_type,
      coalesce(skum.netsuite_item_number, am.netsuite_item_number) as ns_item_number,
      coalesce(skum.item_id, am.item_id) as item_id
    from line_tax_mapping ltm
    left join netsuite.netsuite.netsuite_postings_sku_mapping skum on ltm.sku=skum.sku
    left join netsuite.netsuite.netsuite_postings_asin_as_sku_mapping as am on ltm.sku=am.sku and ltm.country_code=am.country_code
  )
  , a as (
    select count(*) as cnt
    from daily_settlements_data a
  )
  , b as (
    select count(*) as cnt 
    from metadata_mappings b
  )
  , c as (
    select count(*) as cnt 
    from line_tax_mapping c
  )
  , d as (
    select count(*) as cnt 
    from sku_mapping d
  )
  select 
    (case when a.cnt = b.cnt
      and a.cnt = c.cnt
      and a.cnt = d.cnt
      then 0 else 2 end) as "result"
  from a 
  join b on 1=1
  join c on 1=1
  join d on 1=1
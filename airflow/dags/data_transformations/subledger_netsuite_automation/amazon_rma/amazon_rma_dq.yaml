---
test_id: "fact_amazon_rma_dup_check"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS "result" FROM 
  (
  SELECT rma_pk, group_id 
  FROM $curated_db.FACT_AMAZON_ESSOR_RMA  
  GROUP BY 1,2
  HAVING COUNT(1) >1 
  LIMIT 1
  )

#---
#test_id: "amazon_rma_and_settlement_comparison"
#enabled: true
#query: |
#  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result"
#  FROM (
#    SELECT
#      COALESCE(stg.seller_id, fact.seller_id) AS seller_id,
#      stg.tot_amount  AS stg_tot_amount,
#      fact.tot_amount AS fact_tot_amount
#    FROM (
#      SELECT
#        s.seller_id
#        , sum(s.amount) AS tot_amount
#      FROM (
#        SELECT
#          TO_DATE(report_date) AS report_date
#          , settlement_id AS settlement_id
#          , seller_id AS seller_id
#          , country_code AS country_code
#          , type AS event_type
#          , order_id AS order_id
#          , CASE
#              WHEN sku LIKE 'amzn.gr.%' THEN split_part(split_part(split_part(sku,'amzn.gr.',2),'-',1),'_',1)
#              ELSE TRIM(TRANSLATE(COALESCE(REGEXP_REPLACE(sku, '^Amazon\\.Found\\.', ''), ''), ' ', ''))
#            END AS sku
#        FROM dwh.prod.fact_amazon_settlement_transactions_scs
#        WHERE 1=1 and TO_DATE(report_date) = DATEADD(Day ,-65, current_date)
#      ) s
#      UNPIVOT (amount FOR transaction_type IN (
#          PRODUCT_SALES
#          , SHIPPING_CREDITS
#          , GIFT_WRAP_CREDITS
#          , product_sales_tax
#          , shipping_credits_tax
#          , giftwrap_credits_tax
#          , regulatory_fee
#          , tax_on_regulatory_fee
#          , promotional_rebates
#          , promotional_rebates_tax
#          , marketplace_withheld_tax
#          , selling_fess
#          , fba_fees
#          , other_transaction_fees
#          , other
#      ))
#      LEFT JOIN (
#        SELECT *
#        FROM $curated_db.FACT_AMAZON_ESSOR_RMA
#        WHERE posted_flag = 'not ready to post'
#          OR IFNULL(UPPER(netsuite_item_number),'') = 'NOT USED'
#      ) f
#        ON to_date(s.report_date) = f.posting_date
#        AND s.settlement_id = f.custom_settlement_id
#        AND s.seller_id = f.seller_id
#        AND s.country_code 	= f.country_code
#        AND COALESCE(s.event_type,'')	= COALESCE(f.event_type,'')
#        AND COALESCE(s.order_id,'')	= COALESCE(f.order_id,'')
#        AND COALESCE(s.sku,'') = COALESCE(f.sku,'')
#        AND COALESCE(s.transaction_type,'') = COALESCE(f.transaction_type	,'')
#      WHERE f.seller_id IS NULL
#      GROUP BY s.seller_id
#    ) stg
#  FULL OUTER JOIN (
#    SELECT
#      sum(amount) tot_amount
#      , seller_id
#    FROM $curated_db.FACT_AMAZON_ESSOR_RMA
#    WHERE 1=1 AND posting_date = DATEADD(Day ,-65, current_date)
#      AND IFNULL(UPPER(netsuite_item_number),'') <> 'NOT USED'
#      AND posted_flag <> 'not ready to post'
#    GROUP BY seller_id
#  ) fact
#    ON stg.seller_id = fact.seller_id
#  WHERE abs(to_numeric(coalesce(stg.tot_amount,0),18,2) - to_numeric(coalesce(fact.tot_amount,0),18,2)) > 0.01
#  LIMIT 1
#  ) T

---
test_id: "additional_transactions_on_posted_rma"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
    SELECT   DISTINCT netsuite_rma_sale_order_post_key
    FROM $curated_db.FACT_AMAZON_ESSOR_RMA
    WHERE lower(posted_flag) <> 'do not post' 
    GROUP BY netsuite_rma_sale_order_post_key
    HAVING COUNT(DISTINCT CASE WHEN lower(posted_flag) = 'manual_post' THEN 'posted' 
                              WHEN lower(posted_flag) IN  ('ready to post','not ready to post') THEN 'not posted' 
                  ELSE lower(posted_flag) 
                END) > 1
    LIMIT 1
  )

---
test_id: "amazon_rma_missing_netsuite_brand_id"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT 1
   FROM $curated_db.FACT_AMAZON_ESSOR_RMA 
   WHERE class_id IS NULL 
   AND posted_flag = 'not ready to post'
   LIMIT 1
  )

---
test_id: "amazon_rma_missing_netsuite_customer_location_id"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT DISTINCT brand
   FROM $curated_db.FACT_AMAZON_ESSOR_RMA 
   WHERE (netsuite_customer_internal_id IS NULL
   OR netsuite_subsidiary_internal_id IS NULL 
   OR geography_id IS NULL 
   OR netsuite_location_internal_id IS NULL)
   AND posted_flag = 'not ready to post'
   LIMIT 1
  )

---
test_id: "amazon_rma_missing_netsuite_item_number"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT DISTINCT sku
   FROM $curated_db.FACT_AMAZON_ESSOR_RMA 
   WHERE netsuite_item_number IS NULL 
   AND posted_flag = 'not ready to post'
   LIMIT 1
  )

---
test_id: "amazon_rma_amounts_on_do_not_post_entries"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
    SELECT 1
    FROM $curated_db.FACT_AMAZON_ESSOR_RMA 
    WHERE LOWER(posted_flag) = 'do not post'
    AND amount <> 0
    AND posting_date >= DATEADD(D,-30,current_date)
    LIMIT 1
  );

---
test_id: "amazon_settlements_data_missing_from_rma"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
    WITH dates AS (
      SELECT m."date_actual" AS report_date
      FROM dwh.prod.calendar_dimension c
      JOIN dwh.prod.calendar_dimension m 
        ON m."date_actual" BETWEEN c."first_day_of_month" AND current_date-2
      WHERE c."date_actual" = (SELECT current_date-2)
    ), seller AS (
      SELECT D.report_date, s.seller_id 
      FROM dwh.staging.stg_seller_info s
      CROSS JOIN dates d
    ), settlements AS (
      SELECT distinct 
         to_date(report_date) AS snapshot_date
        ,seller_id AS seller_id
      FROM DWH.PROD.FACT_AMAZON_SETTLEMENT_TRANSACTIONS_SCS f 
      JOIN dates D 
        ON snapshot_date = D.report_date
    ), missing_dates AS (
      SELECT s.*
      from seller s 
      LEFT JOIN DWH.PROD.FACT_AMAZON_SETTLEMENT_TRANSACTIONS_SCS st 
      ON TO_DATE(st.report_date) = s.report_date
      AND st.seller_id = s.seller_id
      WHERE st.report_date IS NULL 
    )
    SELECT m.*
    FROM missing_dates m 
    JOIN settlements o
    ON m.report_date = o.snapshot_date
    AND m.seller_id = o.seller_id
    LIMIT 1
  ); 
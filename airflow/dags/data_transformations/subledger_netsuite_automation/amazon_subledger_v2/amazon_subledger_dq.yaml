---
test_id: "fact_amazon_subledger_dup_check"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS "result" FROM 
  (
  SELECT subledger_pk, group_id 
  FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR  
  GROUP BY 1,2
  HAVING COUNT(1) >1 
  LIMIT 1
  )

---
test_id: "amazon_subledger_and_shipment_comparison"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS "result"
  FROM 
  (
  SELECT 
    COALESCE(stg.seller_id, fact.seller_id) AS seller_id,
    stg.tot_amount  AS stg_tot_amount,
    fact.tot_amount AS fact_tot_amount
  FROM 
  (
    SELECT 	s.seller_id ,sum(s.amount) AS tot_amount
    FROM (
    select
      "shipment_id" as shipment_id,
      "shipment_item_id" as shipment_item_id,
      TO_DATE("shipment_date_local") as shipment_date,
      "seller_id" as seller_id,
      REPLACE(coalesce(CASE
          WHEN LOWER("sales_channel") = 'amazon.com' THEN 'US'
          WHEN LOWER("sales_channel") IN ('amazon.ca','si ca prod marketplace') THEN 'CA'
          WHEN LOWER("sales_channel") = 'amazon.com.br' THEN 'BR'
          WHEN LOWER("sales_channel") = 'amazon.com.mx' THEN 'MX'
          WHEN LOWER("sales_channel") IN ('amazon.co.uk','si uk prod marketplace') THEN 'UK'
          WHEN LOWER("sales_channel") = 'amazon.fr' THEN 'FR'
          WHEN LOWER("sales_channel") = 'amazon.de' THEN 'DE'
          WHEN LOWER("sales_channel") IN ('amazon.es','si prod es marketplace') THEN 'ES'
          WHEN LOWER("sales_channel") IN ('amazon.it','si prod it marketplace') THEN 'IT'
          WHEN LOWER("sales_channel") = 'amazon.pl' THEN 'PL'
          WHEN LOWER("sales_channel") = 'amazon.nl' THEN 'NL'
          WHEN LOWER("sales_channel") = 'amazon.se' THEN 'SE'
          WHEN LOWER("sales_channel") = 'amazon.com.tr' THEN 'TR'
          WHEN LOWER("sales_channel") = 'amazon.com.be' THEN 'BE'
          WHEN LOWER("sales_channel") = 'amazon.ae' THEN 'AE'
          WHEN LOWER("sales_channel") = 'amazon.com.au' THEN 'AU'
        END, "ship_country"), 'GB', 'UK') as country_code,
      "amazon_order_id" as order_id,
      "currency" as currency,
      TRIM(TRANSLATE(COALESCE(REGEXP_REPLACE("sku", '^Amazon\\.Found\\.', ''), ''), ' ', '')) AS sku,
      "product_name" as product_name,
      LOWER("sales_channel") as marketplace,
      'Order' as event_type,
      "quantity_shipped" as quantity,
      "item_price" as "product sales",
      "shipping_price" as "shipping credits",
      "gift_wrap_price" as "gift wrap credits"
  from dwh.staging.amazon_fba_fulfilled_shipments
      WHERE 1=1 and TO_DATE("shipment_date_local") = DATEADD(Day ,-65, current_date)
      ) s
      UNPIVOT (amount FOR transaction_type IN ("product sales", "shipping credits", "gift wrap credits" ))
    LEFT JOIN (
      (SELECT * FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR WHERE posted_flag ='not ready to post' OR IFNULL(UPPER(netsuite_item_number),'') = 'NOT USED')) f
      ON  to_date(s.shipment_date) = f.posting_date 		
      AND CONCAT(s.shipment_id, '_',s.shipment_item_id) = f.custom_settlement_id 		
      AND s.seller_id = f.seller_id 			
      AND s.country_code 	= f.country_code
      AND COALESCE(s.event_type,'')	= COALESCE(f.event_type,'')	 		
      AND COALESCE(s.order_id,'')	= COALESCE(f.order_id,'')	
      AND COALESCE(s.sku,'') = COALESCE(f.sku,'')		
      AND COALESCE(s.transaction_type,'') = COALESCE(f.transaction_type	,'')
    WHERE   f.seller_id IS NULL
    GROUP BY s.seller_id	
  )stg
  full outer join 
  (
    SELECT sum(amount) tot_amount,seller_id 
    FROM  $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR 
    WHERE 1=1 and posting_date = DATEADD(Day ,-65, current_date)
    AND IFNULL(UPPER(netsuite_item_number),'') <> 'NOT USED'
    AND posted_flag <> 'not ready to post'
    GROUP BY seller_id	
  )fact
    ON stg.seller_id = fact.seller_id
  where abs(to_numeric(coalesce(stg.tot_amount,0),18,2) - to_numeric(coalesce(fact.tot_amount,0),18,2)) > 0.01
  limit 1
  ) T

---
test_id: "additional_transactions_on_posted_cashsales"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
    SELECT   DISTINCT netsuite_rma_sale_order_post_key
    FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR
    WHERE lower(posted_flag) <> 'do not post' 
    GROUP BY netsuite_rma_sale_order_post_key
    HAVING COUNT(DISTINCT CASE WHEN lower(posted_flag) = 'manual_post' THEN 'posted' 
                              WHEN lower(posted_flag) IN  ('ready to post','not ready to post') THEN 'not posted' 
                  ELSE lower(posted_flag) 
                END) > 1
    LIMIT 1
  )

---
test_id: "amazon_subledger_missing_netsuite_brand_id"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT 1
   FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR 
   WHERE netsuite_brand_id IS NULL 
   AND posted_flag = 'not ready to post'
   LIMIT 1
  )

---
test_id: "amazon_subledger_missing_netsuite_customer_location_id"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT DISTINCT brand
   FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR 
   WHERE (netsuite_customer_internal_id IS NULL
   OR netsuite_subsidiary_internal_id IS NULL 
   OR geography_id IS NULL 
   OR netsuite_location_internal_id IS NULL)
   AND posted_flag = 'not ready to post'
   LIMIT 1
  )

---
test_id: "amazon_subledger_missing_netsuite_item_number"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT DISTINCT sku
   FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR 
   WHERE netsuite_item_number IS NULL 
   AND posted_flag = 'not ready to post'
   LIMIT 1
  )

---
test_id: "amazon_subledger_amounts_on_do_not_post_entries"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
    SELECT 1
    FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR 
    WHERE LOWER(posted_flag) = 'do not post'
    AND amount <> 0
    AND posting_date >= DATEADD(D,-30,current_date)
    LIMIT 1
  );

---
test_id: "amazon_settlements_data_missing_from_shipments"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
    WITH dates AS (
      SELECT m."date_actual" AS report_date
      FROM dwh.prod.calendar_dimension c
      JOIN dwh.prod.calendar_dimension m 
        ON m."date_actual" BETWEEN c."first_day_of_month" AND current_date-2
      WHERE c."date_actual" = (SELECT current_date-2)
    ), seller AS (
      SELECT D.report_date, s.seller_id 
      FROM dwh.staging.stg_seller_info s
      CROSS JOIN dates d
    ), orders AS (
      SELECT distinct 
         CONVERT_TIMEZONE('UTC','America/Los_Angeles', TO_DATE("shipment_date_local"))::DATE AS snapshot_date
        ,"seller_id" AS seller_id
      FROM dwh.staging.amazon_fba_fulfilled_shipments f 
      JOIN dates D 
        ON snapshot_date = D.report_date
    ), missing_dates AS (
      SELECT s.*
      from seller s 
      LEFT JOIN dwh.staging.amazon_fba_fulfilled_shipments st 
      ON TO_DATE(st."shipment_date_local") = s.report_date
      AND st."seller_id" = s.seller_id
      WHERE st."shipment_date_local" IS NULL 
    )
    SELECT m.*
    FROM missing_dates m 
    JOIN orders o
    ON m.report_date = o.snapshot_date
    AND m.seller_id = o.seller_id
    LIMIT 1
  );

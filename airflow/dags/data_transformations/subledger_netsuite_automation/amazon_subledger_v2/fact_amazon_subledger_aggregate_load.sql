BEGIN TRANSACTION;

SET min_posting_date = (
    SELECT MIN(posting_date) AS posting_date
    FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR
    WHERE record_updated_timestamp_utc >= TO_TIMESTAMP_NTZ('$start_ts')
    OR record_created_timestamp_utc  >= TO_TIMESTAMP_NTZ('$start_ts')
    OR posting_date >= current_date() - 3
);

--SLA for updates is 60 days so the incremental is set to that number
CREATE OR REPLACE TEMPORARY TABLE $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_AGGREGATE_TEMP AS
WITH exclude_rma_sale_order AS (
    --exclude all rma and sale orders if any transaction within that rma and sale order is not ready to post
    SELECT DISTINCT netsuite_rma_sale_order_post_key, group_id
    FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR
    WHERE 	lower(posted_flag)  IN ('not ready to post','posted','manual_post')
),
exclude_seller_id_country_code as (
    SELECT DISTINCT seller_id, country_code, posting_date
    FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR
    WHERE lower(posted_flag)  IN ('not ready to post')
)
SELECT
    (select class_int_id from netsuite.netsuite.netsuite_classes where lower(class_name) = 'multiple brands' )  as class
    , (select SALES_CHANNEL_INT_ID from netsuite.netsuite.netsuite_sales_channels where lower(SALES_CHANNEL_CODE) = 'amazon') as sales_channel_id
    , 82 as department_id
    , split_part(S.item, ' : ', 1) as item_type
    , CONCAT( 'AMZ_SO_', S.posting_date, '_', S.seller_id, '_', S.geography, '_', S.seller_name, '_', S.group_id) AS external_id
    , S.seller_id
    , S.posting_date
    , S.currency
    , concat(S.posting_date, '_', S.seller_name, '_', S.country_code) as memo
    , S.geography
    , S.geography_id
    , S.item
    , S.netsuite_id
    , S.netsuite_item_number
    , concat(S.posting_date, '_', S.seller_name, '_', S.country_code) as description_line
    , S.brand
    , S.netsuite_brand_id
    , S.netsuite_subsidiary_internal_id as subsidiary_internal_id
    , S.netsuite_subsidiary as netsuite_subsidiary_name
    , S.netsuite_customer_internal_id as customer_internal_id
    , S.netsuite_customer_name
    , S.netsuite_location_internal_id as location_internal_id
    , S.netsuite_location
    , S.tax_internal_id
    , S.tax_code
    , S.netsuite_rma_sale_order_post_key
    , concat(S.netsuite_rma_sale_order_post_key, '_', S.group_id) as uuid
    , S.subledger_key
    , S.source
    , SUM(CASE WHEN S.transaction_type = 'product sales' THEN S.quantity ELSE 0 END) + 
      MAX(CASE WHEN S.transaction_type IN ('shipping credits', 'gift wrap credits') THEN 1 ELSE 0 END) as quantity
    , SUM(S.amount) as amount
    , SYSDATE()          			AS record_created_timestamp_utc
    , NULL 						AS record_updated_timestamp_utc
FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR S
LEFT JOIN exclude_rma_sale_order C
    ON S.netsuite_rma_sale_order_post_key = C.netsuite_rma_sale_order_post_key
    AND S.group_id = C.group_id
LEFT JOIN exclude_seller_id_country_code D
    ON S.seller_id = D.seller_id
    AND S.country_code = D.country_code
    AND S.posting_date = D.posting_date
WHERE 	S.posted_flag  <> 'do not post'
AND S.posting_date >= $min_posting_date
AND S.posting_date <= current_date() - 3 -- T-3 day lag for posting
AND C.netsuite_rma_sale_order_post_key IS NULL
AND D.seller_id IS NULL
AND UPPER(s.netsuite_item_number) <> 'NOT USED' -- ??
GROUP BY  class
        , sales_channel_id
        , department_id
        , S.item
        , S.posting_date
        , S.seller_id
        , S.geography
        , S.seller_name
        , S.currency
        , S.country_code
        , S.geography_id
        , S.netsuite_id
        , S.netsuite_item_number
        , S.brand
        , S.netsuite_brand_id
        , S.netsuite_subsidiary_internal_id
        , S.netsuite_subsidiary
        , S.netsuite_customer_internal_id
        , S.netsuite_customer_name
        , S.netsuite_location_internal_id
        , S.netsuite_location
        , S.tax_internal_id
        , S.tax_code
        , S.netsuite_rma_sale_order_post_key
        , S.group_id
        , S.subledger_key
        , S.source;

--avoid updating any records that are in the middle of posting
UPDATE $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_AGGREGATE A
SET  A.quantity	   					= T.quantity
    ,A.amount		   					= T.amount
    ,A.item_type						= T.item_type
    ,A.netsuite_customer_name			= T.netsuite_customer_name
    ,A.netsuite_subsidiary_name			= T.netsuite_subsidiary_name
    ,A.netsuite_location				= T.netsuite_location
    ,A.external_id				        = T.external_id
    ,A.geography				        = T.geography
    ,A.tax_code				            = T.tax_code
    ,A.netsuite_id					    = T.netsuite_id
    ,A.record_updated_timestamp_utc  	= SYSDATE()
FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_AGGREGATE_TEMP T
WHERE 	A.subledger_key 		 				 =  T.subledger_key
    AND  A.posting_status IS NULL
    AND	(A.quantity	  						        <> T.quantity  							    OR
            to_numeric(A.amount,18,2)			    <> to_numeric(T.amount,18,2)				OR
            coalesce(A.item_type,'') 			    <> coalesce(T.item_type,'') 				OR
            coalesce(A.netsuite_customer_name,'')   <> coalesce(T.netsuite_customer_name,'')    OR
            coalesce(A.netsuite_subsidiary_name,'') <> coalesce(T.netsuite_subsidiary_name,'')  OR
            coalesce(A.netsuite_location,'') 	    <> coalesce(T.netsuite_location,'')  		OR
            coalesce(A.geography,'') 	            <> coalesce(T.geography,'')  		        OR
            coalesce(A.tax_code,'') 	            <> coalesce(T.tax_code,'')  		        OR
            coalesce(A.netsuite_item_number,'')		<> coalesce(T.netsuite_item_number,'')
        ); -- ??

-- Delete disputed transaction from the aggregate table
DELETE FROM $curated_db.fact_amazon_subledger_essor_aggregate
WHERE uuid IN (
            SELECT DISTINCT concat(netsuite_rma_sale_order_post_key, '_', group_id)
            FROM $curated_db.fact_amazon_subledger_essor
            WHERE posted_flag = 'DISPUTED_TRANSACTION'
                AND posting_date >= $min_posting_date
       );

--insert only when subledger_key does not exist in the aggr table	
INSERT INTO $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_AGGREGATE (
class, sales_channel_id, department_id, item_type, external_id, seller_id, posting_date,
currency, memo, geography, geography_id, item, netsuite_id, netsuite_item_number, description_line, brand, netsuite_brand_id,
subsidiary_internal_id, netsuite_subsidiary_name, customer_internal_id, netsuite_customer_name, location_internal_id,
netsuite_location, tax_internal_id, tax_code, netsuite_rma_sale_order_post_key, uuid, subledger_key, source, quantity,
amount, record_created_timestamp_utc, record_updated_timestamp_utc
)
SELECT s.*
FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_AGGREGATE_TEMP s
LEFT JOIN $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_AGGREGATE a
    ON s.subledger_key = a.subledger_key 
    AND s.uuid = a.uuid
WHERE  a.subledger_key IS NULL;


-- Update posting status for disputed rma and sale orders
UPDATE $curated_db.fact_amazon_subledger_essor_aggregate
SET posting_status = 'DISPUTED_TRANSACTION',
    record_updated_timestamp_utc = SYSDATE()
WHERE uuid IN (
            SELECT DISTINCT concat(netsuite_rma_sale_order_post_key, '_', group_id)
            FROM $curated_db.fact_amazon_subledger_essor
            WHERE posted_flag = 'DISPUTED_TRANSACTION'
            AND posting_date >= $min_posting_date
       );


/*remove any records that are present in the aggregate tables but not in subledger. This case could happen when an incorrect mapping information is entered for records and updated later. 
The subledger_key would change in that case and the old one will no longer be valid for posting */
DELETE 
FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_AGGREGATE S
USING (
    SELECT S.* 
    FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_AGGREGATE S
    LEFT JOIN  $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR T
    on S.subledger_key = T.subledger_key 
    WHERE  T.subledger_key IS NULL
    AND S.posting_status IS NULL)  c
WHERE S.subledger_key = c.subledger_key ;

/*edge case: when a transaction is marked from 'ready to post' to 'do not post' or 'not ready to post' , remove the entire rma and sale order from aggr table to prevent it from posting.
the rma and sale order will get added back automatically after all the transactions for it are ready to post again*/
DELETE 
FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_AGGREGATE S
USING (
    SELECT DISTINCT concat(netsuite_rma_sale_order_post_key, '_', group_id) as uuid
    FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR
    WHERE 	posted_flag  = 'not ready to post'
) T
WHERE S.uuid = T.uuid
AND S.posting_status IS NULL;

COMMIT;

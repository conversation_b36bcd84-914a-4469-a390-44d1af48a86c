CREATE OR REPLACE TEMPORARY TABLE $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_TEMP AS (
with daily_shipments_data as (
    select
        "shipment_id" as shipment_id,
        "shipment_item_id" as shipment_item_id,
        TO_DATE("shipment_date_local") as shipment_date,
        "seller_id" as seller_id,
        REPLACE(coalesce(CASE
            WHEN LOWER("sales_channel") = 'amazon.com' THEN 'US'
            WHEN LOWER("sales_channel") IN ('amazon.ca','si ca prod marketplace') THEN 'CA'
            WHEN LOWER("sales_channel") = 'amazon.com.br' THEN 'BR'
            WHEN LOWER("sales_channel") = 'amazon.com.mx' THEN 'MX'
            WHEN LOWER("sales_channel") IN ('amazon.co.uk','si uk prod marketplace') THEN 'UK'
            WHEN LOWER("sales_channel") = 'amazon.fr' THEN 'FR'
            WHEN LOWER("sales_channel") = 'amazon.de' THEN 'DE'
            WHEN LOWER("sales_channel") IN ('amazon.es','si prod es marketplace') THEN 'ES'
            WHEN LOWER("sales_channel") IN ('amazon.it','si prod it marketplace') THEN 'IT'
            WHEN LOWER("sales_channel") = 'amazon.pl' THEN 'PL'
            WHEN LOWER("sales_channel") = 'amazon.nl' THEN 'NL'
            WHEN LOWER("sales_channel") = 'amazon.se' THEN 'SE'
            WHEN LOWER("sales_channel") = 'amazon.com.tr' THEN 'TR'
            WHEN LOWER("sales_channel") = 'amazon.com.be' THEN 'BE'
            WHEN LOWER("sales_channel") = 'amazon.ae' THEN 'AE'
            WHEN LOWER("sales_channel") = 'amazon.com.au' THEN 'AU'
          END, "ship_country"), 'GB', 'UK') as country_code,
        "amazon_order_id" as order_id,
        "currency" as currency,
        TRIM(TRANSLATE(COALESCE(REGEXP_REPLACE("sku", '^Amazon\\.Found\\.', ''), ''), ' ', '')) AS sku,
        "product_name" as product_name,
        LOWER("sales_channel") as marketplace,
        'Order' as event_type,
        sum("quantity_shipped") as quantity,
        sum("item_price") as product_amount,
        sum("shipping_price") as shipping_amount,
        sum("gift_wrap_price") as gift_amount
    from dwh.staging.amazon_fba_fulfilled_shipments
    where (TO_DATE("shipment_date_local") >= TO_TIMESTAMP_NTZ('$start_ts')::date - 60)
    group by all
)
, metadata_mappings as (
    select td.*,
        ssm.netsuite_customer,
        ssm.netsuite_customer_internal_id,
        ssm.netsuite_subsidiary,
        ssm.netsuite_subsidiary_internal_id,
        ssm.netsuite_location,
        ssm.netsuite_location_internal_id,
        ssm.amazon_seller_account_name as seller_name,
        ssm.sales_channel,
        ssm.marketplace as geography,
        replace(ssm.country_code, 'UK', 'GB') as c_country_code,
        nns.subsidiary_country as s_country_code,
        ng.geography_int_id as geography_id
    from daily_shipments_data td
    left join DWH.RAW.NRA_AMAZON_SELLER_SUBSIDIARY_MAPPING ssm on td.seller_id=ssm.seller_id and td.country_code=ssm.country_code and ssm.fulfillment_type='FBA'
    left join netsuite.netsuite.netsuite_subsidiaries nns on nns.subsidiary_int_id=ssm.netsuite_subsidiary_internal_id
    left join netsuite.netsuite.netsuite_geographies ng on lower(ssm.marketplace)=lower(ng.geography_name) or lower(ssm.marketplace)=lower(ng.geography_iso_code)
)
, line_tax_mapping as (
    select mm.*,
        tcm.internal_id as tax_internal_id,
        tcm.name as tax_code
    from metadata_mappings mm
    left join DWH.RAW.NRA_AMAZON_TAX_CODES_MAPPING_LATEST tcm
       on lower(tcm.customer_country)=lower(mm.c_country_code) and lower(tcm.subsidiary_country)=lower(mm.s_country_code)
)
, sku_mapping as (
select ltm.*,
    coalesce(skum.brand, am.brand) as brand,
    coalesce(skum.class_id, am.class_id) as class_id,
    coalesce(skum.netsuite_item_type, am.netsuite_item_type) as netsuite_item_type,
    coalesce(skum.netsuite_item_number, am.netsuite_item_number) as ns_item_number,
    coalesce(skum.item_id, am.item_id) as item_id
from line_tax_mapping ltm
left join netsuite.netsuite.netsuite_postings_sku_mapping skum on ltm.sku=skum.sku
left join netsuite.netsuite.netsuite_postings_asin_as_sku_mapping as am on ltm.sku=am.sku and ltm.country_code=am.country_code
)
, unpivot_data as (
    select s.*
        , case
            when s.transaction_type = 'PRODUCT_AMOUNT' then 'product sales'
            when s.transaction_type = 'SHIPPING_AMOUNT' then  'shipping credits'
            when s.transaction_type = 'GIFT_AMOUNT' then 'gift wrap credits'
            else s.transaction_type
         end as transaction_type_alias
        , NULL AS posted_flag
        , NULL AS netsuite_rma_sale_order_id
        , NULL AS netsuite_rma_sale_order_document_number
FROM sku_mapping s
    UNPIVOT (amount
        FOR transaction_type IN
        ( product_amount, shipping_amount, gift_amount )
    )
),
gl_mapping as (
    select * from dwh.raw.NRA_AMAZON_GL_MAPPING
    where lower(event_type) = 'order'
    and transaction_type in ('product sales','gift wrap credits','shipping credits')
)
, final_data as (
SELECT  md5(CAST(
                 COALESCE(CAST(s.shipment_date 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.shipment_id 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.shipment_item_id 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.seller_id			 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.country_code 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.event_type 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.order_id 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.sku 		 	 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.product_name 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.marketplace 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.transaction_type 	 AS varchar), '') AS varchar
            )) 								 AS subledger_pk
        ,s.shipment_date as posting_date
        ,CONCAT(s.shipment_id, '_',s.shipment_item_id) as custom_settlement_id
        ,s.seller_id
        ,s.seller_name
        ,s.class_id as netsuite_brand_id
        ,s.brand
        ,s.geography
        ,s.geography_id
        ,s.netsuite_customer_internal_id
        ,s.netsuite_customer as netsuite_customer_name
        ,s.country_code
        ,s.netsuite_location
        ,s.netsuite_location_internal_id
        ,CASE WHEN g.NONINVENTORY_ITEM_INTERNAL_ID IS NULL AND s.sku IS NOT NULL AND s.transaction_type_alias = 'product sales'
            THEN s.ns_item_number
            ELSE trim(g.NONINVENTORY_ITEM_)
        END as netsuite_item_number
        ,s.item_id
        ,CASE WHEN g.NONINVENTORY_ITEM_INTERNAL_ID IS NULL AND s.sku IS NOT NULL AND s.transaction_type_alias = 'product sales'
            THEN s.item_id
            ELSE g.NONINVENTORY_ITEM_INTERNAL_ID
         END as netsuite_id
        ,CASE WHEN g.NONINVENTORY_ITEM_INTERNAL_ID IS NULL AND s.sku IS NOT NULL AND s.transaction_type_alias = 'product sales'
            THEN concat(s.netsuite_item_type, ' : ', coalesce(s.ns_item_number, ''))
            ELSE concat('Non-Inventory : ', coalesce(g.NONINVENTORY_ITEM_, ''))
         END as item
        ,split_part(item, ' : ', 1) as item_type
        ,s.netsuite_subsidiary_internal_id
        ,s.netsuite_subsidiary
        ,s.tax_code
        ,s.tax_internal_id
        ,md5(CAST( 'shipment-' || COALESCE(CAST(s.shipment_date	                    AS varchar), '') || '-' ||
                  COALESCE(CAST(s.country_code		            AS varchar), '') || '-' ||
                  COALESCE(CAST(s.seller_id		            AS varchar), '') || '-' ||
                  COALESCE(CAST(s.netsuite_customer_internal_id     AS varchar), '') || '-' ||
                  COALESCE(CAST(s.netsuite_customer_internal_id   AS varchar), '')
                AS varchar)) 								        AS netsuite_rma_sale_order_post_key
        ,md5(CAST( 'shipment-' || COALESCE(CAST(s.shipment_date	            AS varchar), '') || '-' ||
                    COALESCE(CAST(s.country_code 		        AS varchar), '') || '-' ||
                    COALESCE(CAST(s.seller_id 	        AS varchar), '') || '-' ||
                    COALESCE(CAST(s.class_id 	        AS varchar), '') || '-' ||
                    COALESCE(CAST(s.netsuite_customer_internal_id AS varchar), '') || '-' ||
                    COALESCE(CAST(s.netsuite_location_internal_id AS varchar), '') || '-' ||
                    COALESCE(CAST(coalesce(netsuite_item_number, '') 		    AS varchar), '')
                AS varchar)) 								        AS subledger_key
        ,s.netsuite_rma_sale_order_id
        ,s.netsuite_rma_sale_order_document_number
        ,s.event_type
        ,s.order_id
        ,s.sku
        ,CASE WHEN g.NONINVENTORY_ITEM_INTERNAL_ID IS NULL AND s.sku IS NOT NULL AND s.transaction_type_alias = 'product sales'
            THEN s.product_name
            ELSE concat('Non-Inventory : ', coalesce(g.NONINVENTORY_ITEM_, ''), '_', s.shipment_date, '_', s.brand)
         END as description
        ,s.quantity
        ,s.transaction_type_alias as transaction_type
        ,s.amount
        ,s.currency
        ,s.posted_flag
        ,SYSDATE()        			AS etl_batch_runtime
        , 'shipment' as source
        ,SYSDATE()        			AS record_created_timestamp_utc
        ,NULL 						AS record_updated_timestamp_utc
        ,NULL 						AS rma_sale_order_post_updated_timestamp_utc
FROM unpivot_data s
left join gl_mapping g
on s.marketplace = g.marketplace
and s.transaction_type_alias = g.transaction_type
and lower(s.event_type) = lower(g.event_type)
)
select f.*, coalesce(p.rw, -1) as rw, coalesce(p.group_id, -1) as group_id
from final_data as f
left join (
select
    distinct
    netsuite_rma_sale_order_post_key,
    subledger_key,
    dense_rank() over (partition by netsuite_rma_sale_order_post_key order by subledger_key) as rw,
    floor(rw/400)+1 as group_id
from final_data
where amount>0
) as p on p.netsuite_rma_sale_order_post_key=f.netsuite_rma_sale_order_post_key
and p.subledger_key=f.subledger_key
);

CREATE OR REPLACE TEMPORARY TABLE $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_GROUPING_TEMP AS (
with shipment_posted_group_info as (
       select
            distinct netsuite_rma_sale_order_post_key,
            subledger_key,
            CAST(split_part(uuid, '_', 2) AS int) as group_id,
       from $curated_db.fact_amazon_subledger_essor_aggregate
       where posting_status IS NOT NULL
)
, newly_available_records as (
       select a.*
       from $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_TEMP a
       left join shipment_posted_group_info b
       on a.netsuite_rma_sale_order_post_key=b.netsuite_rma_sale_order_post_key
       and a.subledger_key=b.subledger_key
       and a.group_id=b.group_id
       where b.netsuite_rma_sale_order_post_key is null
)
select f.* EXCLUDE(rw, group_id)
       , coalesce(p.rw, -1) as rw
       , coalesce(p.group_id + coalesce(pa.max_group_id, 0), -1) as group_id
       from newly_available_records as f
left join (
   select
       distinct
       netsuite_rma_sale_order_post_key,
       subledger_key,
       dense_rank() over (partition by netsuite_rma_sale_order_post_key order by subledger_key) as rw,
       floor(rw/400)+1 as group_id
   from newly_available_records
   where amount>0
) as p on p.netsuite_rma_sale_order_post_key=f.netsuite_rma_sale_order_post_key
and p.subledger_key=f.subledger_key
left join (
   select
       netsuite_rma_sale_order_post_key, max(group_id) as max_group_id
   from shipment_posted_group_info
   group by all
) pa on pa.netsuite_rma_sale_order_post_key=f.netsuite_rma_sale_order_post_key
);

--assign new group id for newly records found in the shipments table
UPDATE $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_TEMP tgt
SET rw = src.rw, group_id = src.group_id
FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_GROUPING_TEMP src
WHERE tgt.subledger_pk=src.subledger_pk;


--assign flags for all the rows based on the mapping data
UPDATE $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_TEMP  
SET posted_flag = CASE WHEN lower(netsuite_item_number) = 'not used' OR amount <= 0
                            THEN 'do not post'
                            WHEN netsuite_id IS NULL OR netsuite_item_number IS NULL OR netsuite_customer_internal_id IS NULL OR netsuite_subsidiary_internal_id IS NULL OR netsuite_location_internal_id IS NULL OR geography_id is NULL OR tax_internal_id IS NULL
                            THEN 'not ready to post' 
                            ELSE 'ready to post'
                    END;

--ignore the non-heyday skus that are sent by amazon
UPDATE $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_TEMP  T
SET posted_flag = 'do not post'
FROM dwh.staging.invalid_skus S
WHERE T.sku = S."sku" ;

BEGIN TRANSACTION;

--insert only when record is not already present 
INSERT INTO $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR (
    SUBLEDGER_PK,
    POSTING_DATE,
    CUSTOM_SETTLEMENT_ID,
    SELLER_ID,
    SELLER_NAME,
    NETSUITE_BRAND_ID,
    BRAND,
    GEOGRAPHY,
    GEOGRAPHY_ID,
    NETSUITE_CUSTOMER_INTERNAL_ID,
    NETSUITE_CUSTOMER_NAME,
    COUNTRY_CODE,
    NETSUITE_LOCATION,
    NETSUITE_LOCATION_INTERNAL_ID,
    NETSUITE_ITEM_NUMBER,
    ITEM_ID,
    NETSUITE_ID,
    ITEM,
    ITEM_TYPE,
    NETSUITE_SUBSIDIARY_INTERNAL_ID,
    NETSUITE_SUBSIDIARY,
    TAX_CODE,
    TAX_INTERNAL_ID,
    NETSUITE_RMA_SALE_ORDER_POST_KEY,
    SUBLEDGER_KEY,
    NETSUITE_RMA_SALE_ORDER_ID,
    NETSUITE_RMA_SALE_ORDER_DOCUMENT_NUMBER,
    EVENT_TYPE,
    ORDER_ID,
    SKU,
    DESCRIPTION,
    QUANTITY,
    TRANSACTION_TYPE,
    AMOUNT,
    CURRENCY,
    POSTED_FLAG,
    ETL_BATCH_RUNTIME,
    SOURCE,
    RECORD_CREATED_TIMESTAMP_UTC,
    RECORD_UPDATED_TIMESTAMP_UTC,
    RMA_SALE_ORDER_POST_UPDATED_TIMESTAMP_UTC,
    RW,
    GROUP_ID
)
SELECT s.* 
FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_TEMP s
LEFT JOIN $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR tgt
ON s.subledger_pk = tgt.subledger_pk 
WHERE tgt.subledger_pk IS NULL; 

--update only when the mapping or amounts have changed 
UPDATE $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR S
SET  netsuite_customer_internal_id        = T.netsuite_customer_internal_id     
    ,netsuite_customer_name               = T.netsuite_customer_name
    ,netsuite_location                    = T.netsuite_location                 
    ,netsuite_location_internal_id        = T.netsuite_location_internal_id
    ,netsuite_subsidiary_internal_id      = T.netsuite_subsidiary_internal_id
    ,netsuite_subsidiary                  = T.netsuite_subsidiary
    ,seller_name                          = T.seller_name
    ,tax_internal_id                      = T.tax_internal_id
    ,tax_code                             = T.tax_code
    ,item_type                            = T.item_type                         
    ,netsuite_item_number                 = T.netsuite_item_number             
    ,netsuite_id                          = T.netsuite_id                       
    ,item                                 = T.item
    ,netsuite_rma_sale_order_post_key     = T.netsuite_rma_sale_order_post_key
    ,subledger_key                        = T.subledger_key                     
    ,quantity                             = T.quantity                          
    ,amount                               = T.amount                            
    ,currency                             = T.currency      
    ,posted_flag						  = T.posted_flag
    ,netsuite_brand_id					  = T.netsuite_brand_id
    ,brand								  = T.brand
    ,geography							  = T.geography
    ,geography_id						  = T.geography_id
    ,group_id							  = T.group_id
    ,rw						              = T.rw
    ,record_updated_timestamp_utc         = SYSDATE()
FROM (
    SELECT T.*
    FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_TEMP T
    JOIN $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR S
        ON S.subledger_pk  = T.subledger_pk
    LEFT JOIN  $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_AGGREGATE A
		ON S.subledger_key = A.subledger_key
    WHERE   S.posted_flag IN ('ready to post','not ready to post','do not post','DISPUTED_TRANSACTION')
        AND A.posting_status IS NULL --avoid updates if cashsales are in the middle of posting
        AND(COALESCE(S.netsuite_customer_internal_id,0) != COALESCE(T.netsuite_customer_internal_id,0)  OR
            COALESCE(S.netsuite_subsidiary_internal_id,0) != COALESCE(T.netsuite_subsidiary_internal_id,0)  OR
            COALESCE(S.netsuite_location_internal_id,0) != COALESCE(T.netsuite_location_internal_id,0) OR
            COALESCE(S.geography_id,0) != COALESCE(T.geography_id,0) OR
            COALESCE(S.tax_internal_id,0) != COALESCE(T.tax_internal_id,0) OR
            COALESCE(S.netsuite_item_number,'')		  != COALESCE(T.netsuite_item_number,'')  		 OR 
            COALESCE(S.brand,'')		  				  != COALESCE(T.brand,'')  		 				 OR
            COALESCE(S.netsuite_brand_id,0)		  	  != COALESCE(T.netsuite_brand_id,0)  		 	 OR
            to_numeric(COALESCE(S.amount,0),18,2)		  != to_numeric(COALESCE(T.amount,0),18,2)     	 OR
            COALESCE(S.quantity,0)					  != COALESCE(T.quantity,0) OR 
            COALESCE(S.posted_flag,'')		  		  != COALESCE(T.posted_flag,'')  
        )
    ) T
WHERE S.subledger_pk  = T.subledger_pk;

--this update is to get the additional mapping updates that are just used for reference and do not impact netsuite posting. this could happen after a transaction is posted and we do not want to reprocess
UPDATE $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR S
SET  netsuite_customer_name	    = T.netsuite_customer_name
    ,netsuite_subsidiary        = T.netsuite_subsidiary
    ,netsuite_location          = T.netsuite_location
    ,seller_name                = T.seller_name
    ,geography                  = T.geography
    ,tax_code                   = T.tax_code
    ,netsuite_item_number       = T.netsuite_item_number -- ??
FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_TEMP T
WHERE  S.subledger_pk  = T.subledger_pk
AND   (COALESCE(S.netsuite_customer_name,'')  != COALESCE(T.netsuite_customer_name,'')  OR
        COALESCE(S.netsuite_subsidiary,'') 		!= COALESCE(T.netsuite_subsidiary,'')  		OR
        COALESCE(S.netsuite_location,'') 		!= COALESCE(T.netsuite_location,'')  		OR
        COALESCE(S.seller_name,'') 		!= COALESCE(T.seller_name,'')  		OR
        COALESCE(S.geography,'') 		!= COALESCE(T.geography,'')  		OR
        COALESCE(S.tax_code,'') 		!= COALESCE(T.tax_code,'')  		OR
        COALESCE(S.netsuite_id,0)		  		!= COALESCE(T.netsuite_id,0)
        );


-- Update flag for late arriving transactions
CREATE OR REPLACE TEMPORARY TABLE $stage_db.netsuite_essor_amazon_disputed_sale_orders AS
SELECT  aggr.netsuite_rma_sale_order_post_key,
        aggr.group_id,
        aggr.subledger_key,
        aggr.posting_status
FROM (
    SELECT  netsuite_rma_sale_order_post_key,
            subledger_key,
            posting_status,
            MIN(CAST(split_part(uuid, '_', 2) AS int)) as group_id,
            NVL(SUM(amount), 0) AS tot_amount
   	FROM $curated_db.fact_amazon_subledger_essor_aggregate
   	WHERE posting_date >= DATEADD(DAY, -61, TO_DATE(CURRENT_TIMESTAMP()))
   	AND posting_status is not null
   	GROUP BY netsuite_rma_sale_order_post_key, subledger_key, posting_status
) aggr
LEFT JOIN (
	SELECT	netsuite_rma_sale_order_post_key,
	        subledger_key,
	        MIN(group_id) AS group_id,
            NVL(SUM(amount), 0) AS tot_amount
   	FROM $curated_db.fact_amazon_subledger_essor
	WHERE posted_flag <> 'do not post'
		AND posting_date >= DATEADD(DAY, -61, TO_DATE(CURRENT_TIMESTAMP()))
		AND posting_date <= current_date() - 2
	    AND UPPER(netsuite_item_number) <> 'NOT USED' -- ??
	GROUP BY netsuite_rma_sale_order_post_key, subledger_key
) sl ON aggr.netsuite_rma_sale_order_post_key = sl.netsuite_rma_sale_order_post_key
    AND aggr.subledger_key = sl.subledger_key
WHERE TO_NUMERIC(COALESCE(aggr.tot_amount, 0), 18, 2) != TO_NUMERIC(COALESCE(sl.tot_amount, 0), 18, 2)
OR TO_NUMERIC(COALESCE(aggr.group_id, 0), 18, 2) != TO_NUMERIC(COALESCE(sl.group_id, 0), 18, 2);


CREATE OR REPLACE TEMPORARY TABLE $stage_db.netsuite_essor_new_records_same_key_already_posted as
(with next_group AS (
  SELECT
    netsuite_rma_sale_order_post_key,
    MAX(group_id) + 1 AS new_group_id
  FROM $curated_db.fact_amazon_subledger_essor
  GROUP BY netsuite_rma_sale_order_post_key
)
select c.new_group_id, a.* from $curated_db.fact_amazon_subledger_essor a 
join $stage_db.netsuite_essor_amazon_disputed_sale_orders b
on a.NETSUITE_RMA_SALE_ORDER_POST_KEY=b.NETSUITE_RMA_SALE_ORDER_POST_KEY
and a.GROUP_ID=b.GROUP_ID
and a.SUBLEDGER_KEY=b.SUBLEDGER_KEY
left join next_group c on 
a.NETSUITE_RMA_SALE_ORDER_POST_KEY=c.NETSUITE_RMA_SALE_ORDER_POST_KEY
where a.posted_flag='ready to post')
;

UPDATE $curated_db.fact_amazon_subledger_essor tgt
SET group_id = src.new_group_id
FROM $stage_db.netsuite_essor_new_records_same_key_already_posted src
WHERE tgt.subledger_pk=src.subledger_pk;

CREATE OR REPLACE TEMPORARY TABLE $stage_db.netsuite_essor_new_records_same_key_difference_amount_already_posted as
SELECT T.* EXCLUDE(amount, quantity, posted_flag),
    (T.amount - S.amount)   AS amount,
    (T.quantity - S.quantity)   AS quantity,
    CASE WHEN (T.amount - S.amount) < 0 OR (T.quantity - S.quantity) < 0
            THEN 'DISPUTED_TRANSACTION'
         WHEN (T.amount - S.amount) = 0
            THEN 'do not post'
            ELSE 'ready to post'
    END as posted_flag
    FROM $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_TEMP T
    JOIN $curated_db.fact_amazon_subledger_essor S
        ON S.subledger_pk  = T.subledger_pk
    LEFT JOIN $curated_db.fact_amazon_subledger_essor_aggregate A
		ON S.subledger_key = A.subledger_key
    WHERE S.posted_flag NOT IN ('ready to post','not ready to post','do not post','DISPUTED_TRANSACTION')
        AND A.posting_status IS NOT NULL
        AND(
            to_numeric(COALESCE(S.amount,0),18,2)		  != to_numeric(COALESCE(T.amount,0),18,2)     	 OR
            COALESCE(S.quantity,0)					  != COALESCE(T.quantity,0)
        )
        ;

UPDATE $stage_db.netsuite_essor_new_records_same_key_difference_amount_already_posted tgt
SET group_id = src.new_group_id
FROM (
   SELECT
    netsuite_rma_sale_order_post_key,
    MAX(group_id) + 1 AS new_group_id
  FROM $curated_db.fact_amazon_subledger_essor
  GROUP BY netsuite_rma_sale_order_post_key) src
WHERE tgt.netsuite_rma_sale_order_post_key=src.netsuite_rma_sale_order_post_key;


INSERT INTO $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR (
    SUBLEDGER_PK,
    POSTING_DATE,
    CUSTOM_SETTLEMENT_ID,
    SELLER_ID,
    SELLER_NAME,
    NETSUITE_BRAND_ID,
    BRAND,
    GEOGRAPHY,
    GEOGRAPHY_ID,
    NETSUITE_CUSTOMER_INTERNAL_ID,
    NETSUITE_CUSTOMER_NAME,
    COUNTRY_CODE,
    NETSUITE_LOCATION,
    NETSUITE_LOCATION_INTERNAL_ID,
    NETSUITE_ITEM_NUMBER,
    ITEM_ID,
    NETSUITE_ID,
    ITEM,
    ITEM_TYPE,
    NETSUITE_SUBSIDIARY_INTERNAL_ID,
    NETSUITE_SUBSIDIARY,
    TAX_CODE,
    TAX_INTERNAL_ID,
    NETSUITE_RMA_SALE_ORDER_POST_KEY,
    SUBLEDGER_KEY,
    NETSUITE_RMA_SALE_ORDER_ID,
    NETSUITE_RMA_SALE_ORDER_DOCUMENT_NUMBER,
    EVENT_TYPE,
    ORDER_ID,
    SKU,
    DESCRIPTION,
    TRANSACTION_TYPE,
    CURRENCY,
    ETL_BATCH_RUNTIME,
    SOURCE,
    RECORD_CREATED_TIMESTAMP_UTC,
    RECORD_UPDATED_TIMESTAMP_UTC,
    RMA_SALE_ORDER_POST_UPDATED_TIMESTAMP_UTC,
    RW,
    GROUP_ID,
    AMOUNT,
    QUANTITY,
    POSTED_FLAG
)
SELECT s.*
FROM $stage_db.netsuite_essor_new_records_same_key_difference_amount_already_posted s;


CREATE OR REPLACE TABLE $stage_db.netsuite_essor_amazon_disputed_sale_order_final AS
SELECT  aggr.netsuite_rma_sale_order_post_key,
        aggr.group_id,
        aggr.subledger_key,
        aggr.posting_status
FROM (
    SELECT  netsuite_rma_sale_order_post_key,
            subledger_key,
            posting_status,
            (CAST(split_part(uuid, '_', 2) AS int)) as group_id,
            NVL(SUM(amount), 0) AS tot_amount
   	FROM $curated_db.fact_amazon_subledger_essor_aggregate
   	WHERE posting_date >= DATEADD(DAY, -61, TO_DATE(CURRENT_TIMESTAMP()))
   	AND posting_status is not null
   	GROUP BY all
) aggr
LEFT JOIN (
	SELECT	netsuite_rma_sale_order_post_key,
	        subledger_key,
	        group_id AS group_id,
            NVL(SUM(amount), 0) AS tot_amount
   	FROM $curated_db.fact_amazon_subledger_essor
	WHERE posted_flag <> 'do not post'
		AND posting_date >= DATEADD(DAY, -61, TO_DATE(CURRENT_TIMESTAMP()))
		AND posting_date <= current_date() - 2
	    AND UPPER(netsuite_item_number) <> 'NOT USED' -- ??
	GROUP BY all
) sl ON aggr.netsuite_rma_sale_order_post_key = sl.netsuite_rma_sale_order_post_key
    AND aggr.subledger_key = sl.subledger_key
    AND aggr.group_id = sl.group_id
WHERE TO_NUMERIC(COALESCE(aggr.tot_amount, 0), 18, 2) != TO_NUMERIC(COALESCE(sl.tot_amount, 0), 18, 2);


UPDATE $curated_db.fact_amazon_subledger_essor
SET posted_flag = 'DISPUTED_TRANSACTION',
    record_updated_timestamp_utc = SYSDATE()
WHERE concat(netsuite_rma_sale_order_post_key, '_', group_id) IN (
        SELECT DISTINCT concat(netsuite_rma_sale_order_post_key, '_', group_id)
        FROM $stage_db.netsuite_essor_amazon_disputed_sale_order_final
        WHERE UPPER(posting_status) IN ('POSTED', 'MANUAL_POST','SUBMITTED_TO_NETSUITE')
    )
    AND LOWER(posted_flag) != 'do not post';

DROP TABLE $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_TEMP;
DROP TABLE $curated_db.FACT_AMAZON_SUBLEDGER_ESSOR_GROUPING_TEMP;
DROP TABLE $stage_db.netsuite_essor_amazon_disputed_sale_orders;
DROP TABLE $stage_db.netsuite_essor_amazon_disputed_sale_order_final;
DROP TABLE $stage_db.netsuite_essor_new_records_same_key_difference_amount_already_posted;

COMMIT;

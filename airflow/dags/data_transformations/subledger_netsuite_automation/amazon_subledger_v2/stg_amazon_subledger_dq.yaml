---
test_id: "fact_amazon_subledger_dup_check"
enabled: true
query: |
  with daily_shipments_data as (
  select
  "shipment_id" as shipment_id,
  "shipment_item_id" as shipment_item_id,
  TO_DATE("shipment_date_local") as shipment_date,
  "seller_id" as seller_id,
  REPLACE(coalesce(CASE
  WHEN LOWER("sales_channel") = 'amazon.com' THEN 'US'
  WHEN LOWER("sales_channel") IN ('amazon.ca','si ca prod marketplace') THEN 'CA'
  WHEN LOWER("sales_channel") = 'amazon.com.br' THEN 'BR'
  WHEN LOWER("sales_channel") = 'amazon.com.mx' THEN 'MX'
  WHEN LOWER("sales_channel") IN ('amazon.co.uk','si uk prod marketplace') THEN 'UK'
  WHEN LOWER("sales_channel") = 'amazon.fr' THEN 'FR'
  WHEN LOWER("sales_channel") = 'amazon.de' THEN 'DE'
  WHEN LOWER("sales_channel") IN ('amazon.es','si prod es marketplace') THEN 'ES'
  WHEN LOWER("sales_channel") IN ('amazon.it','si prod it marketplace') THEN 'IT'
  WHEN LOWER("sales_channel") = 'amazon.pl' THEN 'PL'
  WHEN LOWER("sales_channel") = 'amazon.nl' THEN 'NL'
  WHEN LOWER("sales_channel") = 'amazon.se' THEN 'SE'
  WHEN LOWER("sales_channel") = 'amazon.com.tr' THEN 'TR'
  WHEN LOWER("sales_channel") = 'amazon.com.be' THEN 'BE'
  WHEN LOWER("sales_channel") = 'amazon.ae' THEN 'AE'
  WHEN LOWER("sales_channel") = 'amazon.com.au' THEN 'AU'
  END, "ship_country"), 'GB', 'UK') as country_code,
  "amazon_order_id" as order_id,
  "currency" as currency,
  COALESCE(REGEXP_REPLACE("sku", '^Amazon\\.Found\\.', ''), '') AS sku,
  "product_name" as product_name,
  LOWER("sales_channel") as marketplace,
  'Order' as event_type,
  sum("quantity_shipped") as quantity,
  sum("item_price") as product_amount,
  sum("shipping_price") as shipping_amount,
  sum("gift_wrap_price") as gift_amount
  from dwh.staging.amazon_fba_fulfilled_shipments
  where TO_DATE("shipment_date_local") >= DATEADD(DAY,-5,TO_DATE(CURRENT_TIMESTAMP()))  
  group by all
  )
  , metadata_mappings as (
  select td.*,
  ssm.netsuite_customer,
  ssm.netsuite_customer_internal_id,
  ssm.netsuite_subsidiary,
  ssm.netsuite_subsidiary_internal_id,
  ssm.netsuite_location,
  ssm.netsuite_location_internal_id,
  ssm.amazon_seller_account_name as seller_name,
  ssm.sales_channel,
  ssm.marketplace as geography,
  replace(ssm.country_code, 'UK', 'GB') as c_country_code,
  nns.subsidiary_country as s_country_code,
  ng.geography_int_id as geography_id
  from daily_shipments_data td
  left join DWH.RAW.NRA_AMAZON_SELLER_SUBSIDIARY_MAPPING ssm on td.seller_id=ssm.seller_id and td.country_code=ssm.country_code and ssm.fulfillment_type='FBA'
  left join netsuite.netsuite.netsuite_subsidiaries nns on nns.subsidiary_int_id=ssm.netsuite_subsidiary_internal_id
  left join netsuite.netsuite.netsuite_geographies ng on lower(ssm.marketplace)=lower(ng.geography_name) or lower(ssm.marketplace)=lower(ng.geography_iso_code)
  )
  , line_tax_mapping as (
  select mm.*,
  tcm.internal_id as tax_internal_id,
  tcm.name as tax_code
  from metadata_mappings mm
  left join DWH.RAW.NRA_AMAZON_TAX_CODES_MAPPING_LATEST tcm
     on lower(tcm.customer_country)=lower(mm.c_country_code) and lower(tcm.subsidiary_country)=lower(mm.s_country_code)
  )
  , sku_mapping as (
  select ltm.*,
  coalesce(skum.brand, am.brand) as brand,
  coalesce(skum.class_id, am.class_id) as class_id,
  coalesce(skum.netsuite_item_type, am.netsuite_item_type) as netsuite_item_type,
  coalesce(skum.netsuite_item_number, am.netsuite_item_number) as netsuite_item_number,
  coalesce(skum.item_id, am.item_id) as item_id
  from line_tax_mapping ltm
  left join netsuite.netsuite.netsuite_postings_sku_mapping skum on ltm.sku=skum.sku
  left join netsuite.netsuite.netsuite_postings_asin_as_sku_mapping as am on ltm.sku=am.sku and ltm.country_code=am.country_code
  ),
  a as  (
  select count(*) as cnt
  from daily_shipments_data a
  ), 
  b as (select count(*) as cnt from metadata_mappings b
  ),
  c as (
  select count(*) as cnt from line_tax_mapping c
  ),
  d as ( select count(*) as cnt from 
  sku_mapping d )
  select 
  (case when a.cnt = b.cnt
  and a.cnt = c.cnt
  and a.cnt = c.cnt
  then 0 else 2 end) as "result"
  from a 
  join b on 1=1
  join c on 1=1
  join d on 1=1




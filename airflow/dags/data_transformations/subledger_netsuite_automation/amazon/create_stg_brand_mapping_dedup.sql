CREATE OR R<PERSON>LACE TRANSIENT TABLE $stage_db.brand_mapping
AS
 WITH latest_raw_load AS (
    SELECT MAX(ETL_BATCH_RUN_TIME) AS "last_sync_time"
    FROM $raw_db.BRAND_MAPPING_STG 
)
 SELECT DISTINCT 
          internal_ID        AS "netsuite_brand_id"
        , BRAND                    AS "brand"
        , FILE_NAME                AS "file_name"
        , ETL_BATCH_RUN_TIME       AS "etl_batch_runtime"
FROM $raw_db.BRAND_MAPPING_STG S
JOIN latest_raw_load l
        ON l."last_sync_time" = S.ETL_BATCH_RUN_TIME
QUALIFY ROW_NUMBER() OVER (PARTITION BY "netsuite_brand_id","brand"
                           ORDER BY "etl_batch_runtime" desc) = 1 
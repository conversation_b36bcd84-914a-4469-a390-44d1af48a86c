CREATE OR REPLACE TEMPORARY TABLE $curated_db.FACT_AMAZON_SUBLEDGER_TEMP AS
WITH settlements AS (
    --sum up to get at daily level
    SELECT to_date("report_date") 		    AS "report_date"
        , "settlement_id"		            AS "settlement_id"
        , "seller_id"		                AS "seller_id"
        , "country_code"                    AS "country_code"
        , "type"			                AS "event_type"
        , "order_id"		                AS "order_id"
        , CASE WHEN "sku" LIKE 'amzn.gr.%' 
                THEN split_part(split_part(split_part("sku",'amzn.gr.',2),'-',1),'_',1) 
                ELSE "sku"
          END		                    	AS "sku_new"
        , "description"			            AS "description"
        , "marketplace"			            AS "marketplace"
        , "account_type"		            AS "account_type"
        , "fulfillment_channel" 			AS "fulfillment_channel"
        , "order_city"			            AS "order_city"
        , "order_state"			            AS "order_state"
        , "order_postal"		            AS "order_postal"
        , "tax_collection_model"			AS "tax_collection_model"	
        , MAX("etl_batch_runtime")			AS "etl_batch_runtime"
        , SUM("quantity")                   AS "quantity"            
        , SUM("product sales")				AS "product sales"
        , SUM("product sales tax")			AS "product sales tax"
        , SUM("shipping credits")			AS "shipping credits"
        , SUM("shipping credits tax")		AS "shipping credits tax"
        , SUM("gift wrap credits")			AS "gift wrap credits"
        , SUM("giftwrap credits tax")		AS "giftwrap credits tax"
        , SUM("regulatory fee")				AS "regulatory fee"
        , SUM("tax on regulatory fee")		AS "tax on regulatory fee"
        , SUM("promotional rebates")		AS "promotional rebates"
        , SUM("promotional rebates tax")	AS "promotional rebates tax"
        , SUM("marketplace withheld tax")	AS "marketplace withheld tax"
        , SUM("selling fees")				AS "selling fees"
        , SUM("fba fees")					AS "fba fees"
        , SUM("other transaction fees")		AS "other transaction fees"
        , SUM("other")						AS "other"
        , SUM("total")						AS "total"
FROM $stage_db.AMAZON_SETTLEMENTS_TRANSACTIONS t
LEFT JOIN $stage_db.stg_seller_info s
    ON s.seller_id = t."seller_id"
WHERE s.seller_type != 'BRANDED'
AND ("etl_batch_runtime" > TO_TIMESTAMP_NTZ('$start_ts')
    OR to_date("report_date") IN (
            SELECT DISTINCT "report_date"
            FROM $curated_db.FACT_AMAZON_SUBLEDGER
            WHERE "posted_flag" IN ('not ready to post', 'ready to post')
        )
    )
GROUP BY  to_date("report_date") 
        , "settlement_id"
        , "seller_id"
        , "country_code"
        , "event_type"
        , "order_id"
        , "sku_new"
        , "description"
        , "marketplace"
        , "account_type"
        , "fulfillment_channel"
        , "order_city"
        , "order_state"
        , "order_postal"
        , "tax_collection_model"
),settlement_customer AS (
    --assign customer and location mapping. do it before unpivoting the data so there are less number of rows to process
    SELECT s."report_date" 					AS "report_date"
            , s."settlement_id"				AS "settlement_id"
            , s."seller_id"					AS "seller_id"
            , c.brand						AS "brand"
            , c.customer_internal_id::varchar AS "netsuite_customer_internal_id"
            , c.customer_name   			AS "netsuite_customer_name"
            , s."country_code" 				AS "country_code"
            , decode(s."country_code",
                'US', 'USD',
                'CA', 'CAD',
                'UK', 'GBP',
                'MX', 'MXN',
                'BR', 'BRL',
                'PL', 'PLN',
                'DE', 'EUR',
                'ES', 'EUR',
                'FR', 'EUR',
                'IT', 'EUR')				    AS "currency"
            , c.location 					    AS "netsuite_location"
            , c.location_internal_id::varchar	AS "netsuite_location_internal_id"
            , s."event_type"					AS "event_type"
            , s."order_id"						AS "order_id"
            , s."sku_new"						AS "sku"
            , s."description"					AS "description"
            , s."quantity"						AS "quantity"
            , s."marketplace"					AS "marketplace"
            , s."account_type"					AS "account_type"
            , CASE WHEN s."fulfillment_channel" = 'Amazon' OR s."description" LIKE '%FBA%' OR s."fulfillment_channel" IS NULL THEN 'FBA' ELSE 'FBM' END AS "fulfillment_channel"
            , s."order_city"					AS "order_city"
            , s."order_state"					AS "order_state"
            , s."order_postal"					AS "order_postal"
            , s."tax_collection_model"			AS "tax_collection_model"
            , s."product sales"				    AS "product sales"
            , s."product sales tax"			    AS "product sales tax"
            , s."shipping credits"				AS "shipping credits"
            , s."shipping credits tax"			AS "shipping credits tax"
            , s."gift wrap credits"			    AS "gift wrap credits"
            , s."giftwrap credits tax"			AS "giftwrap credits tax"
            , s."regulatory fee"				AS "regulatory fee"
            , s."tax on regulatory fee"		    AS "tax on regulatory fee"
            , s."promotional rebates"			AS "promotional rebates"
            , s."promotional rebates tax"		AS "promotional rebates tax"
            , s."marketplace withheld tax"		AS "marketplace withheld tax"
            , s."selling fees"					AS "selling fees"
            , s."fba fees"						AS "fba fees"
            , s."other transaction fees"		AS "other transaction fees"
            , s."other"						    AS "other"
            , s."total"						    AS "total"
            , s."etl_batch_runtime"			    AS "etl_batch_runtime"
    FROM settlements s 
    LEFT JOIN dwh.netsuite.customer_location c
        ON  c.amazon_seller_id = s."seller_id" 
        AND c.geo = s."country_code" 
        AND c.fulfillment_channel = CASE WHEN LOWER(s."fulfillment_channel") = 'amazon' OR s."description" LIKE '%FBA%' OR s."fulfillment_channel" IS NULL THEN 'FBA' ELSE 'FBM' END 
        AND LOWER(c.category) = 'amazon'  --the base file is from amazon
),sl AS (
--assign gl mapping
SELECT   s."report_date"
        ,s."settlement_id"        
        ,s."seller_id"
        ,b.internal_id::varchar AS "netsuite_brand_id"
        ,CASE WHEN s."sku" IS NOT NULL AND s."event_type" NOT LIKE '%Liquidation%' THEN COALESCE(im.brand_code, i.brand_code, si.seller_code, sb.brand_code) ELSE trim(s."brand") END AS "brand"
        ,s."netsuite_customer_internal_id"
        ,s."netsuite_customer_name"
        ,s."country_code"       
        ,s."netsuite_location"
        ,s."netsuite_location_internal_id"
        ,CASE WHEN g."netsuite_item_number" LIKE '%Cypher%' OR (g."netsuite_item_number" IS NULL AND s."sku" IS NOT NULL AND s."transaction_type" = 'product sales')
                THEN 'INV_ITEM'
                ELSE 'GL_ITEM'
            END 											 AS "item_type"		
        ,CASE WHEN g."netsuite_item_number" LIKE '%Cypher%' OR (g."netsuite_item_number" IS NULL AND s."sku" IS NOT NULL AND s."transaction_type" = 'product sales')
                THEN COALESCE(im.netsuite_item_number, i.netsuite_item_number)
                ELSE g."netsuite_item_number" 
            END 											 AS "netsuite_item_number"
        ,CASE WHEN g."netsuite_item_number" LIKE '%Cypher%' OR (g."netsuite_item_number" IS NULL AND s."sku" IS NOT NULL AND s."transaction_type" = 'product sales') 
                THEN COALESCE(im.netsuite_internal_id, i.netsuite_internal_id)
                ELSE g."netsuite_id" 
            END											 AS "netsuite_id"
        ,g."netsuite_gl" 								 AS "netsuite_gl"
        ,NULL               							 AS "netsuite_cash_sale_id"
        ,NULL               							 AS "netsuite_cash_sale_document_number"
        ,s."event_type"
        ,s."order_id"
        ,s."sku"
        ,s."description"
        ,s."quantity"
        ,s."marketplace"
        ,s."account_type"
        ,s."fulfillment_channel"
        ,s."order_city"
        ,s."order_state"
        ,s."order_postal"
        ,s."tax_collection_model"
        ,s."transaction_type"	
        ,s."amount"
        ,s."currency"
        ,NULL AS "posted_flag"
        ,s."etl_batch_runtime" 
FROM settlement_customer s
UNPIVOT ("amount" FOR "transaction_type" IN ("product sales","product sales tax","shipping credits","shipping credits tax","gift wrap credits",
                                                "giftwrap credits tax","regulatory fee","tax on regulatory fee","promotional rebates","promotional rebates tax","marketplace withheld tax",
                                                "selling fees","fba fees","other transaction fees","other","total"))
LEFT JOIN $stage_db.AMAZON_CASH_SALE_GL_ITEM_MAPPING g
    ON  trim(lower(g."type")) = CASE WHEN s."event_type" IS NULL THEN 'not used' ELSE trim(lower(s."event_type")) END
    AND trim(lower(g."marketplace")) = CASE WHEN s."marketplace" IS NULL THEN 'not used' ELSE trim(lower(s."marketplace")) END
    AND trim(lower(g."transaction_type")) = trim(lower(s."transaction_type"))
    AND trim(lower(g."description"))  = CASE
                                            WHEN lower(g."description") = 'not used' THEN 'not used'
                                            WHEN lower(s."description") LIKE 'coupon redemption fee%' THEN 'coupon redemption fee'
                                            WHEN lower(s."description") LIKE 'fba customer returns fee (non-apparel and non-shoes)%' THEN 'fba customer returns fee (non-apparel and non-shoes)'
                                            ELSE trim(lower(s."description"))
                                        END
LEFT JOIN dwh.prod.seller_sku_item_mapping im
    ON im.seller_id = s."seller_id"
    AND im.sku = s."sku"
    AND im.country_code = s."country_code"
LEFT JOIN (
    SELECT
        brand_code,
        sku,
        netsuite_item_number,
        netsuite_internal_id,
        country_code
    FROM dwh.prod.sku_item_mapping
    QUALIFY ROW_NUMBER() OVER(PARTITION BY sku, country_code ORDER BY netsuite_item_number NULLS LAST, brand_code NULLS LAST) = 1
) i
    ON i.sku = s."sku"
    AND i.country_code = s."country_code"
LEFT JOIN dwh.staging.stg_seller_info si ON
    si.seller_id = s."seller_id"
    AND UPPER(is_multi_brand) = 'NO'
-- Low priority join to get brands in case of Adjustment
LEFT JOIN (
    SELECT
        brand_code,
        seller_id,
        sku
    FROM dwh.prod.seller_sku_item_mapping
    QUALIFY ROW_NUMBER() OVER(PARTITION BY sku ORDER BY country_code NULLS LAST ) = 1
) sb
    ON sb.seller_id = s."seller_id"
    AND sb.sku = s."sku"
    AND LOWER(s."event_type") = 'adjustment'
LEFT JOIN dwh.netsuite.brand_mapping b
    ON trim(b.brand) = CASE WHEN s."sku" IS NOT NULL AND s."event_type" NOT LIKE '%Liquidation%' THEN COALESCE(im.brand_code, i.brand_code, si.seller_code, sb.brand_code) ELSE trim(s."brand") END
)
SELECT  md5(CAST(COALESCE(CAST(s."report_date" 		 	 AS varchar), '') || '-' || 
                 COALESCE(CAST(s."settlement_id" 		 AS varchar), '') || '-' || 
                 COALESCE(CAST(s."seller_id" 			 AS varchar), '') || '-' || 
                 COALESCE(CAST(s."country_code" 		 AS varchar), '') || '-' || 
                 COALESCE(CAST(s."event_type" 		 	 AS varchar), '') || '-' || 
                 COALESCE(CAST(s."order_id" 		 	 AS varchar), '') || '-' || 
                 COALESCE(CAST(s."sku" 		 	 		 AS varchar), '') || '-' || 
                 COALESCE(CAST(s."description" 		 	 AS varchar), '') || '-' || 
                 COALESCE(CAST(s."marketplace" 		 	 AS varchar), '') || '-' || 
                 COALESCE(CAST(s."account_type" 		 AS varchar), '') || '-' || 
                 COALESCE(CAST(s."order_city" 		 	 AS varchar), '') || '-' || 
                 COALESCE(CAST(s."order_state" 		 	 AS varchar), '') || '-' || 
                 COALESCE(CAST(s."order_postal" 		 AS varchar), '') || '-' || 
                 COALESCE(CAST(s."tax_collection_model"  AS varchar), '') || '-' || 
                 COALESCE(CAST(s."fulfillment_channel" 	 AS varchar), '') || '-' || 
                 COALESCE(CAST(s."transaction_type" 	 AS varchar), '')
            AS varchar)) 								 AS "subledger_pk"
        ,s."report_date"
        ,s."settlement_id"    
        ,s."seller_id"
        ,s."netsuite_brand_id"
        ,s."brand"
        ,s."netsuite_customer_internal_id"
        ,s."netsuite_customer_name"
        ,s."country_code"       
        ,s."netsuite_location"
        ,s."netsuite_location_internal_id"
        ,s."item_type"
        ,s."netsuite_item_number"
        ,s."netsuite_id"
        ,s."netsuite_gl"
        ,md5(CAST(COALESCE(CAST(s."report_date" 		            AS varchar), '') || '-' || 
                  COALESCE(CAST(s."settlement_id" 		            AS varchar), '') || '-' || 
                  COALESCE(CAST(s."country_code" 		            AS varchar), '') || '-' || 
                  COALESCE(CAST(s."netsuite_customer_internal_id"   AS varchar), '') || '-' || 
                  COALESCE(CAST(s."netsuite_location_internal_id"   AS varchar), '')
                AS varchar)) 								        AS "netsuite_cash_sale_post_key"
        ,md5(CAST(COALESCE(CAST(s."report_date" 		            AS varchar), '') || '-' || 
                    COALESCE(CAST(s."settlement_id" 		        AS varchar), '') || '-' || 
                    COALESCE(CAST(s."netsuite_brand_id" 	        AS varchar), '') || '-' || 
                    COALESCE(CAST(s."netsuite_customer_internal_id" AS varchar), '') || '-' || 
                    COALESCE(CAST(s."netsuite_location_internal_id" AS varchar), '') || '-' || 
                    COALESCE(CAST(s."netsuite_item_number" 		    AS varchar), '')
                AS varchar)) 								        AS "subledger_key"
        ,s."netsuite_cash_sale_id"
        ,s."netsuite_cash_sale_document_number"
        ,s."event_type"
        ,s."order_id"
        ,s."sku"
        ,s."description"
        ,s."quantity"
        ,s."marketplace"
        ,s."account_type"
        ,s."fulfillment_channel"
        ,s."order_city"
        ,s."order_state"
        ,s."order_postal"
        ,s."tax_collection_model"
        ,s."transaction_type"         
        ,s."amount"
        ,s."currency"
        ,s."posted_flag"
        ,s."etl_batch_runtime"
        ,SYSDATE()        			AS "record_created_timestamp_utc"
        ,NULL 						AS "record_updated_timestamp_utc"
        ,NULL 						AS "cash_sale_post_updated_timestamp_utc"
FROM sl s;

--assign flags for all the rows based on the mapping data
UPDATE $curated_db.FACT_AMAZON_SUBLEDGER_TEMP  
SET "posted_flag" = CASE WHEN lower("netsuite_item_number") = 'not used'
                            THEN 'do not post'
                            WHEN "netsuite_item_number" IS NULL OR "netsuite_customer_internal_id" IS NULL OR "netsuite_location_internal_id" IS NULL OR "netsuite_brand_id" IS NULL
                            THEN 'not ready to post' 
                            ELSE 'ready to post'
                    END;

--ignore the non-heyday skus that are sent by amazon
UPDATE $curated_db.FACT_AMAZON_SUBLEDGER_TEMP  T
SET "posted_flag" = 'do not post'
FROM dwh.staging.invalid_skus S
WHERE T."sku" = S."sku" ;

BEGIN TRANSACTION;

--insert only when record is not already present 
INSERT INTO $curated_db.FACT_AMAZON_SUBLEDGER (
    "subledger_pk", "report_date", "settlement_id", "seller_id", "netsuite_brand_id", "brand", "netsuite_customer_internal_id", "netsuite_customer_name", "country_code", "netsuite_location", "netsuite_location_internal_id", "item_type"
,"netsuite_item_number", "netsuite_id", "netsuite_gl", "netsuite_cash_sale_post_key", "subledger_key", "netsuite_cash_sale_id", "netsuite_cash_sale_document_number", "event_type", "order_id", "sku", "description", "quantity", "marketplace", "account_type"
,"fulfillment_channel", "order_city", "order_state", "order_postal", "tax_collection_model", "transaction_type", "amount", "currency", "posted_flag", "etl_batch_runtime","record_created_timestamp_utc" 
,"record_updated_timestamp_utc" ,"cash_sale_post_updated_timestamp_utc" 
)
SELECT s.* 
FROM $curated_db.FACT_AMAZON_SUBLEDGER_TEMP s
LEFT JOIN $curated_db.FACT_AMAZON_SUBLEDGER tgt
ON s."subledger_pk" = tgt."subledger_pk" 
WHERE tgt."subledger_pk" IS NULL; 

--update only when the mapping or amounts have changed 
UPDATE $curated_db.FACT_AMAZON_SUBLEDGER S
SET  "netsuite_customer_internal_id"        = T."netsuite_customer_internal_id"     
    ,"netsuite_customer_name"               = T."netsuite_customer_name"            
    ,"netsuite_location"                    = T."netsuite_location"                 
    ,"netsuite_location_internal_id"        = T."netsuite_location_internal_id"     
    ,"item_type"                            = T."item_type"                         
    ,"netsuite_item_number"                 = T."netsuite_item_number"             
    ,"netsuite_id"                          = T."netsuite_id"                       
    ,"netsuite_gl"                          = T."netsuite_gl"                       
    ,"netsuite_cash_sale_post_key"          = T."netsuite_cash_sale_post_key"                
    ,"subledger_key"                        = T."subledger_key"                     
    ,"quantity"                             = T."quantity"                          
    ,"amount"                               = T."amount"                            
    ,"currency"                             = T."currency"      
    ,"posted_flag"							= T."posted_flag"
    ,"netsuite_brand_id"					= T."netsuite_brand_id"
    ,"brand"								= T."brand"
    ,"record_updated_timestamp_utc"         = SYSDATE()              
FROM (
    SELECT T.*
    FROM $curated_db.FACT_AMAZON_SUBLEDGER_TEMP T
    JOIN $curated_db.FACT_AMAZON_SUBLEDGER S
        ON S."subledger_pk"  = T."subledger_pk"
    LEFT JOIN  $curated_db.FACT_AMAZON_SUBLEDGER_AGGREGATE A
		ON S."subledger_key" = A."subledger_key"
    WHERE   S."posted_flag" IN ('ready to post','not ready to post','do not post','DISPUTED_TRANSACTION')
        AND A."posting_status" IS NULL --avoid updates if cashsales are in the middle of posting
        AND(COALESCE(S."netsuite_customer_internal_id",'') != COALESCE(T."netsuite_customer_internal_id",'')  OR
            COALESCE(S."netsuite_location_internal_id",'') != COALESCE(T."netsuite_location_internal_id",'') OR
            COALESCE(S."netsuite_item_number",'')		  != COALESCE(T."netsuite_item_number",'')  		 OR
            COALESCE(S."brand",'')		  				  != COALESCE(T."brand",'')  		 				 OR
            COALESCE(S."netsuite_brand_id",0)		  	  != COALESCE(T."netsuite_brand_id",0)  		 	 OR
            to_numeric(COALESCE(S."amount",0),18,2)		  != to_numeric(COALESCE(T."amount",0),18,2)     	 OR
            COALESCE(S."quantity",0)					  != COALESCE(T."quantity",0) OR 
            COALESCE(S."posted_flag",'')		  		  != COALESCE(T."posted_flag",'')  
            )
    ) T
WHERE S."subledger_pk"  = T."subledger_pk";

--this update is to get the additional mapping updates that are just used for reference and do not impact netsuite posting. this could happen after a transaction is posted and we do not want to reprocess
UPDATE $curated_db.FACT_AMAZON_SUBLEDGER S
SET  "netsuite_customer_name"	= T."netsuite_customer_name"
    ,"netsuite_location"		= T."netsuite_location"
    ,"netsuite_id"				= T."netsuite_id"
    ,"netsuite_gl"				= T."netsuite_gl"
FROM $curated_db.FACT_AMAZON_SUBLEDGER_TEMP T
WHERE  S."subledger_pk"  = T."subledger_pk"
AND   (COALESCE(S."netsuite_customer_name",'')  != COALESCE(T."netsuite_customer_name",'')  OR
        COALESCE(S."netsuite_location",'') 		!= COALESCE(T."netsuite_location",'')  		OR
        COALESCE(S."netsuite_id",0)		  		!= COALESCE(T."netsuite_id",0)  		 	OR
        COALESCE(S."netsuite_gl",'')		  	!= COALESCE(T."netsuite_gl",'') 
        );


-- Update flag for late arriving transactions
CREATE OR REPLACE TEMPORARY TABLE $stage_db.netsuite_amazon_disputed_cash_sales AS
SELECT  aggr."netsuite_cash_sale_post_key",
        aggr."subledger_key",
        aggr."posting_status"
FROM (
    SELECT  "netsuite_cash_sale_post_key",
            "subledger_key",
            "posting_status",
            NVL(SUM("amount"), 0) AS "tot_amount"
   	FROM $curated_db.fact_amazon_subledger_aggregate
   	WHERE "report_date" >= DATEADD(DAY, -61, TO_DATE(CURRENT_TIMESTAMP()))
   	GROUP BY "netsuite_cash_sale_post_key", "subledger_key", "posting_status"
) aggr
LEFT JOIN (
	SELECT	"netsuite_cash_sale_post_key",
	        "subledger_key",
            NVL(SUM("amount"), 0) AS "tot_amount"
   	FROM $curated_db.fact_amazon_subledger
	WHERE "posted_flag" <> 'do not post'
		AND "report_date" >= DATEADD(DAY, -61, TO_DATE(CURRENT_TIMESTAMP()))
		AND "report_date" <= current_date() - 2
	    AND UPPER("netsuite_item_number") <> 'NOT USED'
	GROUP BY "netsuite_cash_sale_post_key", "subledger_key"
) sl ON aggr."netsuite_cash_sale_post_key" = sl."netsuite_cash_sale_post_key"
    AND aggr."subledger_key" = sl."subledger_key"
WHERE TO_NUMERIC(COALESCE(aggr."tot_amount", 0), 18, 2) != TO_NUMERIC(COALESCE(sl."tot_amount", 0), 18, 2);


UPDATE $curated_db.fact_amazon_subledger
SET "posted_flag" = 'DISPUTED_TRANSACTION',
    "record_updated_timestamp_utc" = SYSDATE()
WHERE "netsuite_cash_sale_post_key" IN (
        SELECT DISTINCT "netsuite_cash_sale_post_key"
        FROM $stage_db.netsuite_amazon_disputed_cash_sales
        WHERE UPPER("posting_status") IN ('POSTED', 'MANUAL_POST','SUBMITTED_TO_NETSUITE')
    )
    AND LOWER("posted_flag") != 'do not post';

DROP TABLE $curated_db.FACT_AMAZON_SUBLEDGER_TEMP;
DROP TABLE $stage_db.netsuite_amazon_disputed_cash_sales;

COMMIT;

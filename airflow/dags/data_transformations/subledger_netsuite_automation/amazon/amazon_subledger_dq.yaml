---
test_id: "fact_amazon_subledger_dup_check"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS "result" FROM 
  (
  SELECT "subledger_pk" 
  FROM $curated_db.FACT_AMAZON_SUBLEDGER  
  GROUP BY 1
  HAVING COUNT(1) >1 
  LIMIT 1
  )

---
test_id: "amazon_subledger_and_settlement_comparison"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS "result"
  FROM 
  (
  SELECT 
    COALESCE(stg."seller_id", fact."seller_id") AS "seller_id",
    stg."tot_amount"  AS stg_tot_amount,
    fact."tot_amount" AS fact_tot_amount
  FROM 
  (
    SELECT 	s."seller_id" ,sum(s."amount") AS "tot_amount"
    FROM (SELECT "report_date","settlement_id","seller_id","country_code","type" ,"order_id" ,"sku" ,"description","marketplace","account_type" ,
                "order_city" ,"order_state" ,"order_postal" ,"tax_collection_model","fulfillment_channel" , 
                "product sales","product sales tax","shipping credits","shipping credits tax","gift wrap credits",
                "giftwrap credits tax","regulatory fee","tax on regulatory fee","promotional rebates","promotional rebates tax",
                CASE WHEN "type" NOT IN ('Order_Retrocharge','Refund_Retrocharge') THEN "marketplace withheld tax" END AS "marketplace withheld tax",
                "selling fees","fba fees","other transaction fees","other",
                CASE WHEN "type" IN ('Order_Retrocharge','Refund_Retrocharge') THEN "total" END AS "total"
      FROM $stage_db.AMAZON_SETTLEMENTS_TRANSACTIONS
      WHERE 1=1 and to_date("report_date") = DATEADD(Day ,-75, current_date)
      AND COALESCE("type",'') NOT IN ('Transfer','Debt')) s
      UNPIVOT ("amount" FOR "transaction_type" IN ("product sales","product sales tax","shipping credits","shipping credits tax","gift wrap credits",
                                                      "giftwrap credits tax","regulatory fee","tax on regulatory fee","promotional rebates","promotional rebates tax","marketplace withheld tax",
                                                      "selling fees","fba fees","other transaction fees","other","total"))
    LEFT JOIN ((SELECT * FROM $curated_db.FACT_AMAZON_SUBLEDGER WHERE "posted_flag" ='not ready to post' OR IFNULL(UPPER("netsuite_item_number"),'') = 'NOT USED')) f
      ON  to_date(s."report_date") = f."report_date" 		
      AND s."settlement_id" = f."settlement_id" 		
      AND s."seller_id" = f."seller_id" 			
      AND s."country_code" 	= f."country_code"
      AND COALESCE(s."type",'')	= COALESCE(f."event_type",'')	 		
      AND COALESCE(s."order_id",'')	= COALESCE(f."order_id",'')	
      AND COALESCE(s."sku",'') = COALESCE(f."sku",'')	
      AND COALESCE(s."description",'') = COALESCE(f."description",'')	
      AND COALESCE(s."marketplace",'') = COALESCE(f."marketplace",'')	
      AND COALESCE(s."account_type",'')	= COALESCE(f."account_type",'')	
      AND COALESCE(s."order_city",'')	= COALESCE(f."order_city",'')	
      AND COALESCE(s."order_state",'') = COALESCE(f."order_state",'')	
      AND COALESCE(s."order_postal",'')	= COALESCE(f."order_postal",'')	
      AND COALESCE(s."tax_collection_model",'')	= COALESCE(f."tax_collection_model"	,'')	
      AND COALESCE(f."fulfillment_channel",'') = CASE WHEN s."fulfillment_channel" = 'Amazon' OR s."description" LIKE '%FBA%' OR s."fulfillment_channel" IS NULL THEN 'FBA' ELSE 'FBM' END 	
      AND COALESCE(s."transaction_type",'') = COALESCE(f."transaction_type"	,'')
    WHERE   f."seller_id" IS NULL
    GROUP BY s."seller_id"	
  )stg
  full outer join 
  (
    SELECT sum("amount") "tot_amount","seller_id" 
    FROM  $curated_db.FACT_AMAZON_SUBLEDGER 
    WHERE 1=1 and "report_date" = DATEADD(Day ,-75, current_date)
    AND IFNULL(UPPER("netsuite_item_number"),'') <> 'NOT USED'
    AND "posted_flag" <> 'not ready to post'
    GROUP BY "seller_id"	
  )fact
    ON stg."seller_id" = fact."seller_id"
  where abs(to_numeric(coalesce(stg."tot_amount",0),18,2) - to_numeric(coalesce(fact."tot_amount",0),18,2)) > 0.01
  limit 1
  ) T

---
test_id: "additional_transactions_on_posted_cashsales"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
    SELECT   DISTINCT "netsuite_cash_sale_post_key"
    FROM $curated_db.FACT_AMAZON_SUBLEDGER
    WHERE lower("posted_flag") <> 'do not post' 
    GROUP BY "netsuite_cash_sale_post_key"
    HAVING COUNT(DISTINCT CASE WHEN lower("posted_flag") = 'manual_post' THEN 'posted' 
                              WHEN lower("posted_flag") IN  ('ready to post','not ready to post') THEN 'not posted' 
                  ELSE lower("posted_flag") 
                END) > 1
    LIMIT 1
  )

---
test_id: "amazon_subledger_missing_netsuite_brand_id"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT 1
   FROM $curated_db.FACT_AMAZON_SUBLEDGER 
   WHERE "netsuite_brand_id" IS NULL 
   AND "posted_flag" = 'not ready to post'
   LIMIT 1
  )

---
test_id: "amazon_subledger_missing_netsuite_customer_location_id"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT DISTINCT "brand"
   FROM $curated_db.FACT_AMAZON_SUBLEDGER 
   WHERE ("netsuite_customer_internal_id" IS NULL 
   OR "netsuite_location_internal_id" IS NULL)
   AND "posted_flag" = 'not ready to post'
   LIMIT 1
  )

---
test_id: "amazon_subledger_missing_netsuite_item_number"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT DISTINCT "sku"
   FROM $curated_db.FACT_AMAZON_SUBLEDGER 
   WHERE "netsuite_item_number" IS NULL 
   AND "posted_flag" = 'not ready to post'
   LIMIT 1
  )

---
test_id: "amazon_subledger_amounts_on_do_not_post_entries"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
    SELECT 1
    FROM $curated_db.FACT_AMAZON_SUBLEDGER 
    WHERE LOWER("posted_flag") = 'do not post'
    AND LOWER("transaction_type") <> 'total'
    AND "amount" <> 0
    AND LOWER("event_type") NOT IN ('order_retrocharge','refund_retrocharge','transfer','adjustment')
    AND LOWER("description") != 'failed disbursement'
    AND "report_date" >= DATEADD(D,-30,current_date)
    LIMIT 1
  );

---
test_id: "amazon_settlements_data_missing_from_scraper"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
    WITH dates AS (
      SELECT m."date_actual" AS report_date
      FROM dwh.prod.calendar_dimension c
      JOIN dwh.prod.calendar_dimension m 
        ON m."date_actual" BETWEEN c."first_day_of_month" AND current_date-2
      WHERE c."date_actual" = (SELECT current_date-2)
    ), seller AS (
      SELECT D.report_date, s.seller_id 
      FROM dwh.staging.stg_seller_info s
      CROSS JOIN dates d
      WHERE seller_region = 'US'
      AND seller_id NOT IN ('A4JJJW02U1KYI','A2LYC6SBE3PD90','A11400YOJSIQOM','A1FQZE1BK9TZ18','A33GEHI6Y6M3GW','A3RLZWEAGHJXJT')
    ), orders AS (
      SELECT distinct 
         CONVERT_TIMEZONE('UTC','America/Los_Angeles', "purchase_date_utc")::DATE AS snapshot_date
        ,"seller_id" AS seller_id
      FROM dwh.prod.fact_all_orders f 
      JOIN dates D 
        ON snapshot_date = D.report_date
      WHERE lower("sales_channel") <> 'non-amazon'
    ), missing_dates AS (
      SELECT s.*
      from seller s 
      LEFT JOIN $stage_db.amazon_settlements_transactions st 
      ON st."report_date"::date = s.report_date
      AND st."seller_id" = s.seller_id
      WHERE st."report_date" IS NULL 
    )
    SELECT m.*
    FROM missing_dates m 
    JOIN orders o
    ON m.report_date = o.snapshot_date
    AND m.seller_id = o.seller_id
    LIMIT 1
  );

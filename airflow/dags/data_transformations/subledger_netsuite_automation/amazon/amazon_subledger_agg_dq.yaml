---
test_id: "amazon_subledger_aggregate_dup_check"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS "result" FROM 
  (
  SELECT "subledger_key" FROM $curated_db.FACT_AMAZON_SUBLEDGER_AGGREGATE  
  WHERE "report_date" <> '2022-10-04' --handle cypher edge case FOR 10/4 not a true dup
  GROUP BY 1
  HAVING COUNT(1) >1 
  LIMIT 1
  )

---
test_id: "amazon_subledger_and_aggregate_comparison"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS "result"
  FROM (
      SELECT  COALESCE(aggr."seller_id", sl."seller_id") AS "seller_id",
              aggr."tot_amount"  AS stg_tot_amount,
              sl."tot_amount" AS fact_tot_amount
      FROM (
          SELECT  S."seller_id",
                  IFNULL(SUM(S."amount"),0) AS "tot_amount"
          FROM $curated_db.FACT_AMAZON_SUBLEDGER S
          LEFT JOIN (
              SELECT DISTINCT "netsuite_cash_sale_post_key"
              FROM $curated_db.FACT_AMAZON_SUBLEDGER
              WHERE "posted_flag" = 'not ready to post'
          ) C ON S."netsuite_cash_sale_post_key" = C."netsuite_cash_sale_post_key"
          LEFT JOIN (
              SELECT DISTINCT "netsuite_cash_sale_post_key"
              FROM $curated_db.FACT_AMAZON_SUBLEDGER_AGGREGATE
              WHERE UPPER("posting_status") IN ('PROCESSING', 'SUBMITTED_TO_NETSUITE')
          ) A ON S."netsuite_cash_sale_post_key" = A."netsuite_cash_sale_post_key"
      WHERE S."posted_flag" NOT IN ('do not post')
          AND "report_date" >= DATEADD(DAY,-61,TO_DATE(CURRENT_TIMESTAMP()))
          AND "report_date" <= current_date() - 2
          AND C."netsuite_cash_sale_post_key" IS NULL
          AND UPPER(s."netsuite_item_number") <> 'NOT USED'
          AND A."netsuite_cash_sale_post_key" IS NULL
      GROUP BY S."seller_id"
      ) aggr
      FULL OUTER JOIN (
          SELECT "seller_id" ,sum("amount") "tot_amount"
      FROM $curated_db.FACT_AMAZON_SUBLEDGER_AGGREGATE
      WHERE "report_date" >= DATEADD(DAY,-61,TO_DATE(CURRENT_TIMESTAMP()))
          AND ("posting_status" IS NULL OR UPPER("posting_status") IN ('POSTED', 'MANUAL_POST', 'DISPUTED_TRANSACTION'))
      GROUP BY "seller_id"
      ) sl
          ON aggr."seller_id" = sl."seller_id"
    WHERE to_numeric(coalesce(aggr."tot_amount",0),18,2) <> to_numeric(coalesce(sl."tot_amount",0),18,2)
    LIMIT 1
  ) T
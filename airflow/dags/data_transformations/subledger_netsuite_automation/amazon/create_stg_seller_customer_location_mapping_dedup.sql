CREATE OR REPLACE TRANSIENT TABLE $stage_db.amazon_seller_customer_location_mapping
AS
WITH latest_raw_load AS (
    SELECT MAX(ETL_BATCH_RUN_TIME) AS "last_sync_time"
    FROM $raw_db.AMAZON_SELLER_CUSTOMER_LOCATION_MAPPING_STG 
)
SELECT DISTINCT
          CUSTOMER_NAME            AS "customer_name"
        , CUSTOMER_INTERNAL_ID     AS "customer_internal_id"
        , amazon_seller_id                AS "seller_id"
        , geo             AS "country_code"
        , CATEGORY                 AS "category"
        , FULFILLMENT_CHANNEL      AS "fulfillment_channel"
        , "LOCATION"               AS "location"
        , LOCATION_INTERNAL_ID     AS "location_internal_id"
        , BRAND                    AS "brand" 
        , FILE_NAME                AS "file_name"
        , ETL_BATCH_RUN_TIME       AS "etl_batch_runtime"
FROM $raw_db.AMAZON_SELLER_CUSTOMER_LOCATION_MAPPING_STG S
JOIN latest_raw_load l
        ON l."last_sync_time" = S.ETL_BATCH_RUN_TIME
QUALIFY ROW_NUMBER() OVER (PARTITION BY "customer_name","customer_internal_id","seller_id","country_code","category","fulfillment_channel","location","location_internal_id","brand" 
                           ORDER BY "etl_batch_runtime" desc) = 1 
SET min_processing_date = (
	SELECT MIN("report_date") AS "report_date" FROM 
	(
		SELECT MIN("report_date") AS "report_date" 
		FROM $curated_db.FACT_SHOPIFY_SUBLEDGER
		WHERE "posted_flag" IN ('not ready to post')
		
		UNION ALL 

		SELECT MIN(CONVERT_TIMEZONE('UTC','America/Los_Angeles', "shipment_date_utc")::DATE)
		FROM DWH.PROD.FACT_ALL_REVENUE
		WHERE "record_updated_timestamp_utc" >= TO_TIMESTAMP_NTZ('$start_ts')
            AND CONVERT_TIMEZONE('UTC','America/Los_Angeles', "shipment_date_utc")::DATE >= DATEADD(d,-40,current_date)
            AND "marketplace" = 'SHOPIFY'
            AND "country_code" = 'US'

		UNION ALL

		SELECT MIN(CONVERT_TIMEZONE('UTC','America/Los_Angeles', "refund_date_utc")::DATE)
		FROM DWH.PROD.FACT_ALL_REFUNDS
		WHERE "record_updated_timestamp_utc" >= TO_TIMESTAMP_NTZ('$start_ts')
            AND CONVERT_TIMEZONE('UTC','America/Los_Angeles', "refund_date_utc")::DATE >= DATEADD(d,-40,current_date)
            AND "marketplace" = 'SHOPIFY'
            AND "country_code" = 'US'
	)
);

CREATE OR REPLACE TEMPORARY TABLE $stage_db.STG_SHOPIFY_REVENUE_REFUNDS AS
WITH revenue AS (
    --aggregate at date level
    SELECT
        CONVERT_TIMEZONE('UTC','America/Los_Angeles', "shipment_date_utc") :: DATE AS "report_date"
        ,r."store_id" AS "store_id"
        ,b.internal_id::varchar AS "netsuite_brand_id"
        ,r."brand" AS "brand"
        ,c.customer_internal_id::varchar AS "netsuite_customer_internal_id"
        ,c.customer_name AS "netsuite_customer_name"
        ,r."country_code" AS "country_code"
        ,r."currency" AS "currency"
        ,c.location AS "netsuite_location"
        ,c.location_internal_id::varchar AS "netsuite_location_internal_id"
        ,r."order_id" AS "order_id"
        ,r."external_order_id" AS "external_order_id"
        ,r."sku" AS "sku"
        ,CONVERT_TIMEZONE('UTC','America/Los_Angeles', "purchase_date_utc") :: DATE AS "purchase_date"
        ,'shipment' AS "event_type"
        ,r."product_name" AS "product_name"
        ,r."order_status" AS "order_status"
        ,r."order_item_status" AS "order_item_status"
        ,r."tags" AS "tags"
        ,r."fulfillment_channel" AS "fulfillment_channel"
        ,CASE WHEN r."payment_gateway" IS NULL OR r."payment_gateway" = ''
			  THEN CASE WHEN lower(r."tags") LIKE '%subscription%'
					    THEN 'recharge'
					    ELSE 'unknown gateway'
				   END
			  ELSE lower(r."payment_gateway")
	     END AS "payment_gateway_updated"
        ,MAX(r."quantity_ordered") AS "quantity_ordered"
        ,SUM(r."quantity_shipped") AS "quantity"
        ,MAX(r."sell_price_per_unit_lc") AS "sell_price_per_unit_lc"
        ,MAX(r."gift_wrap_price_per_unit_lc") AS "gift_wrap_price_per_unit_lc"
        ,MAX(r."shipping_price_per_unit_lc") AS "shipping_price_per_unit_lc"
        ,MAX(r."item_promotion_discount_per_unit_lc") * -1 AS "item_promotion_discount_per_unit_lc"
        ,MAX(r."ship_promotion_discount_per_unit_lc") * -1 AS "ship_promotion_discount_per_unit_lc"
        ,MAX(r."gift_wrap_price_lc") AS "gift_wrap_price_lc"
        ,MAX(r."shipping_price_lc") AS "shipping_price_lc"
        ,MAX(r."item_promotion_discount_lc") * -1 AS "item_promotion_discount_lc"
        ,MAX(r."ship_promotion_discount_lc") * -1 AS "ship_promotion_discount_lc"
        ,MAX(r."item_tax_lc") AS "item_tax_lc"
        ,MAX(r."shipping_tax_lc") AS "shipping_tax_lc"
        ,MAX(r."gift_wrap_tax_lc") AS "gift_wrap_tax_lc"
        ,MAX(r."sell_price_lc") AS "sell_price_lc"
        ,MAX("record_created_timestamp_utc") AS "record_created_timestamp_utc"
        ,MAX("record_updated_timestamp_utc") AS "record_updated_timestamp_utc"
    FROM DWH.PROD.FACT_ALL_REVENUE r
    LEFT JOIN dwh.netsuite.brand_mapping b
        ON r."brand" = b.brand
    LEFT JOIN dwh.netsuite.shopify_customer_location c
        ON  r."store_id" = c.shopify_store_id
        AND r."country_code" = c.geo
        AND r."fulfillment_location_id" = c.shopify_location_id
        AND LOWER(c.category) = 'shopify'
    WHERE   "marketplace" = 'SHOPIFY'
        AND "report_date" IS NOT NULL
        AND r."country_code" = 'US'
        AND r."brand" NOT IN ('FUL','PNT','HHR','AQU','OTO','DRE','BPR','PUR','KNT','ONA','OBT','POP','VIK','RGR','TAP',
            'CCR','NUV','DCY','EKN','ALB','LUC','LCF','CPR','LLF','ADA','FHG','RST','ECL','MAL','ACU','HPS','BLO','IMA','ZZZ')
        AND CONVERT_TIMEZONE('UTC','America/Los_Angeles', "shipment_date_utc") :: DATE  >= '2022-11-01'
        AND CONVERT_TIMEZONE('UTC','America/Los_Angeles', "shipment_date_utc") :: DATE  >= CASE WHEN $min_processing_date IS NULL THEN '1900-01-01 00:00:00.000' ELSE $min_processing_date END
    GROUP BY "report_date"
            ,r."store_id"
            ,"netsuite_brand_id"
            ,r."brand"
            ,"netsuite_customer_internal_id"
            ,"netsuite_customer_name"
            ,r."country_code"
            ,r."currency"
            ,"netsuite_location"
            ,"netsuite_location_internal_id"
            ,r."order_id"               
            ,r."external_order_id"      
            ,r."sku"					
            ,"purchase_date"
            ,"event_type"
            ,r."product_name"				
            ,r."order_status"              
            ,r."order_item_status"         
            ,r."tags"                      
            ,r."fulfillment_channel"       
            ,"payment_gateway_updated"           
),refund_location_mapping AS (
 	SELECT   l."store_id"
	        ,l."brand"
	        ,l."country_code"
	        ,l."customer_internal_id"
	        ,l."customer_name"
	        ,l."location_internal_id"
	        ,MAX(c.location) AS "location"
 	FROM (
		SELECT 	 shopify_store_id::varchar AS "store_id"
		        ,brand AS "brand"
		        ,geo AS "country_code"
		        ,customer_internal_id AS "customer_internal_id"
		        ,customer_name AS "customer_name"
		        ,MAX(location_internal_id) AS "location_internal_id"
		FROM dwh.netsuite.shopify_customer_location 
		WHERE LOWER(category) = 'shopify'
		GROUP BY "store_id"
		        ,"brand"
		        ,"country_code"
		        ,"customer_internal_id"
		        ,"customer_name"
	) l
	JOIN dwh.netsuite.shopify_customer_location c
		ON  l."location_internal_id"	= c.location_internal_id
	GROUP BY l."store_id"
	        ,l."brand"
	        ,l."country_code"
	        ,l."customer_internal_id"
	        ,l."customer_name"
	        ,l."location_internal_id"
 ),refunds AS (
    --aggregate at date level
    SELECT 
        CONVERT_TIMEZONE('UTC','America/Los_Angeles', "refund_date_utc") :: DATE AS "report_date"
        ,r."store_id" AS "store_id"
        ,b.internal_id::varchar AS "netsuite_brand_id"
        ,r."brand" AS "brand"
        ,l."customer_internal_id"::varchar AS "netsuite_customer_internal_id"
        ,l."customer_name" AS "netsuite_customer_name"
        ,r."country_code" AS "country_code"
        ,r."currency" AS "currency"
        ,l."location_internal_id"::varchar AS "netsuite_location_internal_id"
        ,l."location" AS "netsuite_location"
        ,r."order_id" AS "order_id"
        ,r."external_order_id" AS "external_order_id"
        ,CASE WHEN r."sku" = 'ZERO_OR_MULTI_SKU' THEN NULL ELSE r."sku" END AS "sku_updated"
        ,CONVERT_TIMEZONE('UTC','America/Los_Angeles', "purchase_date_utc") :: DATE AS "purchase_date"
        ,'refunds' AS "event_type"
        ,NULL AS "product_name"
        ,NULL AS "order_status"
        ,NULL AS "order_item_status"
        ,r."tags" AS "tags"
        ,r."fulfillment_channel" AS "fulfillment_channel"
        ,CASE WHEN r."payment_gateway" IS NULL OR r."payment_gateway" = '' 
			  THEN CASE WHEN lower(r."tags") LIKE '%subscription%' 
					    THEN 'recharge'
					    ELSE 'unknown gateway'
				   END
			  ELSE lower(r."payment_gateway")
	     END AS "payment_gateway_updated" 
        ,SUM(r."quantity") AS "quantity"    
        ,NULL AS "quantity_ordered"                                                    
        ,SUM(r."product_refund") AS "product_refund"                               
        ,SUM(r."tax_refund") AS "tax_refund"                                   
        ,SUM(r."shipping_refund") AS "shipping_refund"                                  
        ,SUM(r."other_refund") AS "other_refund"        
        ,MAX("record_created_timestamp_utc") AS "record_created_timestamp_utc"
        ,MAX("record_updated_timestamp_utc") AS "record_updated_timestamp_utc"                                 
    FROM DWH.PROD.FACT_ALL_REFUNDS r
    LEFT JOIN dwh.netsuite.brand_mapping b
        ON r."brand" = b.brand
    LEFT JOIN refund_location_mapping l
	    ON  r."store_id" = l."store_id"
	    AND r."brand" = l."brand"
	    AND r."country_code" = l."country_code"	   
    WHERE   "marketplace" = 'SHOPIFY'
        AND r."country_code" = 'US'
        AND r."brand" NOT IN ('FUL','PNT','HHR','AQU','OTO','DRE','BPR','PUR','KNT','ONA','OBT','POP','VIK','RGR','TAP',
            'CCR','NUV','DCY','EKN','ALB','LUC','LCF','CPR','LLF','ADA','FHG','RST','ECL','MAL','ACU','HPS','BLO','IMA','ZZZ')
        AND CONVERT_TIMEZONE('UTC','America/Los_Angeles', "refund_date_utc") :: DATE >= '2022-11-01'
        AND CONVERT_TIMEZONE('UTC','America/Los_Angeles', "refund_date_utc") :: DATE  >= CASE WHEN $min_processing_date IS NULL THEN '1900-01-01 00:00:00.000' ELSE $min_processing_date END
    GROUP BY "report_date"
            ,r."store_id"				
            ,"netsuite_brand_id"		
            ,r."brand"					
            ,l."customer_internal_id"	
            ,l."customer_name"			
            ,r."country_code"		
            ,r."currency"	
            ,l."location_internal_id"
            ,l."location"				
            ,r."order_id"               
            ,r."external_order_id"      
            ,"sku_updated"				
            ,"purchase_date"
            ,"event_type"
            ,"product_name"				
            ,"order_status"              
            ,"order_item_status"         
            ,r."tags"                      
            ,r."fulfillment_channel"       
            ,"payment_gateway_updated"     
)
SELECT   "report_date"
        ,"store_id"
        ,"netsuite_brand_id"
        ,"brand"
        ,"netsuite_customer_internal_id"
        ,"netsuite_customer_name"
        ,"country_code"
        ,"currency"
        ,"netsuite_location"
        ,"netsuite_location_internal_id"
        ,"order_id"
        ,"external_order_id"
        ,"sku"
        ,"purchase_date"
        ,"event_type"
        ,"product_name"
        ,"order_status"
        ,"order_item_status"
        ,"tags"
        ,"fulfillment_channel"
        ,"payment_gateway_updated" AS "payment_gateway"  
        ,"quantity_ordered"     
        ,"quantity"   
        ,"transaction_type"
        ,"amount"
        ,"record_created_timestamp_utc"
        ,"record_updated_timestamp_utc"   
FROM revenue
UNPIVOT ("amount" FOR "transaction_type" IN ("sell_price_per_unit_lc","gift_wrap_price_per_unit_lc","shipping_price_per_unit_lc", 
                                             "item_promotion_discount_per_unit_lc","ship_promotion_discount_per_unit_lc",
                                             "gift_wrap_price_lc","shipping_price_lc" ,"item_promotion_discount_lc" ,
                                             "ship_promotion_discount_lc" ,"item_tax_lc","shipping_tax_lc" ,"gift_wrap_tax_lc","sell_price_lc"))
UNION ALL
SELECT   "report_date"
        ,"store_id"
        ,"netsuite_brand_id"
        ,"brand"
        ,"netsuite_customer_internal_id"
        ,"netsuite_customer_name"
        ,"country_code"
        ,"currency"
        ,"netsuite_location"
        ,"netsuite_location_internal_id"
        ,"order_id"
        ,"external_order_id"
        ,"sku_updated" AS "sku"
        ,"purchase_date"
        ,"event_type"
        ,"product_name"
        ,"order_status"
        ,"order_item_status"
        ,"tags"
        ,"fulfillment_channel"
        ,"payment_gateway_updated" AS "payment_gateway"   
        ,"quantity"   
        ,"quantity_ordered"
        ,"transaction_type"
        ,"amount"
        ,"record_created_timestamp_utc"
        ,"record_updated_timestamp_utc"   
FROM refunds
UNPIVOT ("amount" FOR "transaction_type" IN ("product_refund","tax_refund","shipping_refund","other_refund"));

CREATE OR REPLACE TEMPORARY TABLE $curated_db.FACT_SHOPIFY_SUBLEDGER_TEMP AS
WITH sku_im_wo_brand AS (
    SELECT
        sku
        ,country_code
        ,netsuite_item_number
        ,netsuite_internal_id
        ,IFF(is_inactive, 1, 0) AS inactive_rank
        ,IFF(is_discontinued, 1, 0) AS discontinued_rank
    FROM dwh.prod.sku_item_mapping
    QUALIFY ROW_NUMBER() OVER(PARTITION BY sku, country_code ORDER BY inactive_rank, discontinued_rank, brand_code) = 1
),
gl_mapping AS (
    SELECT   
         "report_date"
        ,"store_id"
        ,"netsuite_brand_id"
        ,"brand"
        ,"netsuite_customer_internal_id"
        ,"netsuite_customer_name"
        ,"country_code"
        ,"currency"
        ,"netsuite_location"
        ,"netsuite_location_internal_id"
        ,"order_id"
        ,"external_order_id"
        ,r."sku"
        ,"purchase_date"
        ,"event_type"
        ,"product_name"
        ,"order_status"
        ,"order_item_status"
        ,"tags"
        ,"fulfillment_channel"
        ,"payment_gateway"  
        ,CASE
            WHEN s."sku" IS NOT NULL THEN 'GL_ITEM'
            WHEN g."netsuite_item_number" LIKE '%Cypher%' THEN 'INV_ITEM'
            ELSE 'GL_ITEM'
         END AS "item_type"
        ,CASE
            WHEN s."sku" IS NOT NULL THEN s."netsuite_item_number"
            WHEN g."netsuite_item_number" LIKE '%Cypher%' THEN COALESCE(i.netsuite_item_number, im2.netsuite_item_number)
            ELSE g."netsuite_item_number"
         END AS "netsuite_item_number"
        ,CASE
            WHEN s."sku" IS NOT NULL THEN s."netsuite_id"
            WHEN g."netsuite_item_number" LIKE '%Cypher%' THEN COALESCE(i.netsuite_internal_id, im2.netsuite_internal_id)
            ELSE g."netsuite_id"
         END AS "netsuite_id"
        ,CASE
            WHEN s."sku" IS NOT NULL THEN s."netsuite_gl"
            ELSE g."netsuite_gl"
         END AS "netsuite_gl"
        ,"quantity_ordered"     
        ,"quantity"   
        ,r."transaction_type"
        ,"amount"
        ,"record_created_timestamp_utc"
        ,"record_updated_timestamp_utc"   
    FROM $stage_db.STG_SHOPIFY_REVENUE_REFUNDS r
    LEFT JOIN $stage_db.SHOPIFY_CASH_SALE_GL_ITEM_MAPPING g
        ON  lower(g."type")             = r."event_type"
        AND lower(g."transaction_type") = r."transaction_type"
    LEFT JOIN $stage_db.SHOPIFY_CASH_SALE_SKU_GL_ITEM_MAPPING s
        ON  s."sku" = COALESCE(r."sku",'NULLSKU')
        AND lower(s."transaction_type") = r."transaction_type"
    LEFT JOIN dwh.prod.sku_item_mapping i
        ON i.sku = r."sku"
        AND i.country_code = r."country_code"
        AND i.brand_code = r."brand"
    LEFT JOIN sku_im_wo_brand im2   -- For brand mismatch due to cross brand promotions (eg: ZSK's M1005 in FCP)
        ON im2.sku = r."sku"
        AND im2.country_code = r."country_code"
)
SELECT md5(CAST(COALESCE(CAST("report_date" AS varchar), '') || '-' || 
                COALESCE(CAST("store_id" AS varchar), '') || '-' || 
                COALESCE(CAST("brand" AS varchar), '') || '-' || 
                COALESCE(CAST("event_type" AS varchar), '') || '-' || 
                COALESCE(CAST("order_id" AS varchar), '') || '-' || 
                COALESCE(CAST("external_order_id" AS varchar), '') || '-' || 
                COALESCE(CAST("sku" AS varchar), '') || '-' || 
                COALESCE(CAST("product_name" AS varchar), '') || '-' || 
                COALESCE(CAST("transaction_type" AS varchar), '')
            AS varchar)) AS "subledger_pk"
        ,"report_date"
        ,"store_id"
        ,"netsuite_brand_id"
        ,"brand"
        ,"netsuite_customer_internal_id"
        ,"netsuite_customer_name"
        ,"country_code"
        ,"currency"
        ,"netsuite_location"
        ,"netsuite_location_internal_id"
        ,"order_id"
        ,"external_order_id"
        ,"sku"
        ,"purchase_date"
        ,"event_type"
        ,"product_name"
        ,"order_status"
        ,"order_item_status"
        ,"tags"
        ,"fulfillment_channel"
        ,"payment_gateway"  
        ,"item_type"
        ,"netsuite_item_number"
        ,"netsuite_id"
        ,"netsuite_gl"
        ,md5(CAST(COALESCE(CAST("report_date" AS varchar), '') || '-' || 
                  COALESCE(CAST('SHOPIFY' AS varchar), '') || '-' || 
                  COALESCE(CAST("payment_gateway" AS varchar), '') || '-' || 
                  COALESCE(CAST("netsuite_customer_internal_id" AS varchar), '') || '-' || 
                  COALESCE(CAST("netsuite_location_internal_id" AS varchar), '')
                AS varchar)) AS "netsuite_cash_sale_post_key"
        ,md5(CAST(COALESCE(CAST("report_date" AS varchar), '') || '-' || 
                  COALESCE(CAST('SHOPIFY' AS varchar), '') || '-' || 
                  COALESCE(CAST("payment_gateway" AS varchar), '') || '-' || 
                  COALESCE(CAST("netsuite_brand_id" AS varchar), '') || '-' || 
                  COALESCE(CAST("netsuite_customer_internal_id" AS varchar), '') || '-' || 
                  COALESCE(CAST("netsuite_location_internal_id" AS varchar), '') || '-' || 
                  COALESCE(CAST("netsuite_item_number" AS varchar), '')
                AS varchar)) AS "subledger_key" 
        ,NULL AS "netsuite_cash_sale_id"
        ,NULL AS "netsuite_cash_sale_document_number"
        ,"quantity_ordered"     
        ,"quantity"   
        ,"transaction_type"
        ,"amount"
        ,NULL AS "posted_flag"
        ,'shopify_subledger_netsuite_automation' AS "created_by"
        ,'shopify_subledger_netsuite_automation' AS "updated_by"
        ,"record_created_timestamp_utc"
        ,"record_updated_timestamp_utc"   
        ,NULL AS "mapping_record_updated_timestamp_utc"
        ,NULL AS "cash_sale_post_updated_timestamp_utc"
FROM gl_mapping;

--assign flags for all the rows based on the mapping data
UPDATE $curated_db.FACT_SHOPIFY_SUBLEDGER_TEMP  
    SET "posted_flag"   = CASE WHEN lower("netsuite_item_number") = 'not used'
                               THEN 'do not post'
                               WHEN "netsuite_item_number" IS NULL OR "netsuite_customer_internal_id" IS NULL OR "netsuite_location_internal_id" IS NULL OR "netsuite_brand_id" IS NULL
                               THEN 'not ready to post' 
                               ELSE 'ready to post'
                          END;


-- Remove unchanged and already posted cash sales from the temp table
CREATE OR REPLACE TEMPORARY TABLE $stage_db.shopify_subledger_changed_or_new_cash_sales AS
WITH fact AS (
	SELECT 	"netsuite_cash_sale_post_key",
            "external_order_id",
            "event_type",
            sum("amount") AS "subledger_amount"
	FROM $curated_db.fact_shopify_subledger
	WHERE "posted_flag" IN ('POSTED', 'MANUAL_POST')
	GROUP BY 1,2,3
),
stg AS (
	SELECT 	"netsuite_cash_sale_post_key",
	        "external_order_id",
	        "event_type",
            sum("amount") AS "subledger_amount"
	FROM $curated_db.fact_shopify_subledger_temp
	WHERE LOWER("posted_flag") != 'do not post'
	GROUP BY 1,2,3
)
SELECT DISTINCT stg."netsuite_cash_sale_post_key"
FROM stg
LEFT JOIN fact
	ON stg."netsuite_cash_sale_post_key" = fact."netsuite_cash_sale_post_key"
	AND stg."external_order_id" = fact."external_order_id"
	AND stg."event_type" = fact."event_type"
WHERE ABS(COALESCE(stg."subledger_amount",0) - COALESCE(fact."subledger_amount",0)) > 0.1;

DELETE FROM $curated_db.fact_shopify_subledger_temp tm
WHERE tm."netsuite_cash_sale_post_key" NOT IN (
    SELECT "netsuite_cash_sale_post_key"
    FROM $stage_db.shopify_subledger_changed_or_new_cash_sales
);


BEGIN TRANSACTION;

--insert only when record is not already present 
INSERT INTO $curated_db.FACT_SHOPIFY_SUBLEDGER (
    "subledger_pk","report_date","store_id","netsuite_brand_id","brand","netsuite_customer_internal_id","netsuite_customer_name","country_code",
    "currency","netsuite_location","netsuite_location_internal_id","order_id","external_order_id","sku","purchase_date","event_type","product_name",
    "order_status","order_item_status","tags","fulfillment_channel","payment_gateway","item_type","netsuite_item_number","netsuite_id","netsuite_gl",
    "netsuite_cash_sale_post_key","subledger_key","netsuite_cash_sale_id","netsuite_cash_sale_document_number","quantity_ordered","quantity",
    "transaction_type","amount","posted_flag","created_by","updated_by","record_created_timestamp_utc","record_updated_timestamp_utc","mapping_record_updated_timestamp_utc",
    "cash_sale_post_updated_timestamp_utc"
)
SELECT s.*
FROM $curated_db.FACT_SHOPIFY_SUBLEDGER_TEMP s
LEFT JOIN $curated_db.FACT_SHOPIFY_SUBLEDGER tgt
ON s."subledger_pk" = tgt."subledger_pk" 
WHERE tgt."subledger_pk" IS NULL; 


--update only when the mapping or amounts have changed (this is to not update any posted transactions), qty update does not need to be here
--update query, copy amz
UPDATE $curated_db.FACT_SHOPIFY_SUBLEDGER S
SET  "netsuite_customer_internal_id" = T."netsuite_customer_internal_id"     
    ,"netsuite_customer_name" = T."netsuite_customer_name"            
    ,"netsuite_location" = T."netsuite_location"                 
    ,"netsuite_location_internal_id" = T."netsuite_location_internal_id"     
    ,"item_type" = T."item_type"                         
    ,"netsuite_item_number" = T."netsuite_item_number"             
    ,"netsuite_id" = T."netsuite_id"                       
    ,"netsuite_gl" = T."netsuite_gl"                       
    ,"netsuite_cash_sale_post_key" = T."netsuite_cash_sale_post_key"                
    ,"subledger_key" = T."subledger_key"                     
    ,"quantity" = T."quantity"                          
    ,"amount" = T."amount"                            
    ,"currency" = T."currency"      
    ,"posted_flag" = CASE
                        WHEN S."posted_flag" = 'DISPUTED_TRANSACTION' AND T."posted_flag" = 'ready to post'
                            THEN 'DISPUTED_TRANSACTION'
                        ELSE T."posted_flag"
                     END
    ,"netsuite_brand_id" = T."netsuite_brand_id"
    ,"payment_gateway" = T."payment_gateway"
    ,"mapping_record_updated_timestamp_utc" = SYSDATE()     
    ,"updated_by" = 'shopify_subledger_netsuite_automation'         
FROM (
    SELECT T.*
    FROM $curated_db.FACT_SHOPIFY_SUBLEDGER_TEMP T
    JOIN $curated_db.FACT_SHOPIFY_SUBLEDGER S
        ON S."subledger_pk"  = T."subledger_pk"
    LEFT JOIN  $curated_db.FACT_ALL_SUBLEDGER_AGGREGATE A
		ON S."subledger_key" = A."subledger_key"
        AND A."marketplace" = 'SHOPIFY'
    WHERE   S."posted_flag" IN ('ready to post','not ready to post','do not post', 'DISPUTED_TRANSACTION')
        AND (A."posting_status" IS NULL OR A."posting_status" = 'DISPUTED_TRANSACTION') --avoid updates if cashsales are in the middle of posting
        AND(COALESCE(S."netsuite_customer_internal_id",'') != COALESCE(T."netsuite_customer_internal_id",'') OR
            COALESCE(S."netsuite_location_internal_id",'') != COALESCE(T."netsuite_location_internal_id",'') OR
            COALESCE(S."netsuite_item_number",'') != COALESCE(T."netsuite_item_number",'') OR
            COALESCE(S."netsuite_brand_id",0) != COALESCE(T."netsuite_brand_id",0) OR
            COALESCE(S."payment_gateway",'') != COALESCE(T."payment_gateway",'') OR
            to_numeric(COALESCE(S."amount",0),18,2) != to_numeric(COALESCE(T."amount",0),18,2) OR
            COALESCE(S."quantity",0) != COALESCE(T."quantity",0) OR
            (COALESCE(S."posted_flag", '') != 'DISPUTED_TRANSACTION'
                AND COALESCE(S."posted_flag", '') != COALESCE(T."posted_flag", '')) OR
            (COALESCE(S."posted_flag", '') = 'DISPUTED_TRANSACTION' AND COALESCE(T."posted_flag", '') != 'ready to post')
            )
    ) T
WHERE S."subledger_pk"  = T."subledger_pk";

--this update is to get the additional mapping updates that are just used for reference and do not impact netsuite posting. this could happen after a transaction is posted and we do not want to reprocess
UPDATE $curated_db.FACT_SHOPIFY_SUBLEDGER S
SET  "netsuite_customer_name" = T."netsuite_customer_name" 
    ,"country_code" = T."country_code"           
    ,"currency" = T."currency"               
    ,"netsuite_location" = T."netsuite_location"              
    ,"purchase_date" = T."purchase_date"              
    ,"product_name" = T."product_name"           
    ,"order_status" = T."order_status"           
    ,"order_item_status" = T."order_item_status"                  
    ,"tags" = T."tags"                   
    ,"fulfillment_channel" = T."fulfillment_channel"    
    ,"netsuite_gl" = T."netsuite_gl"            
    ,"quantity_ordered" = T."quantity_ordered"  
    ,"record_updated_timestamp_utc" = T."record_updated_timestamp_utc"   
    ,"updated_by" = 'shopify_subledger_netsuite_automation'         
FROM $curated_db.FACT_SHOPIFY_SUBLEDGER_TEMP T
WHERE  S."subledger_pk"  = T."subledger_pk"
    AND (S."netsuite_customer_name" != T."netsuite_customer_name" OR  
         S."country_code" != T."country_code" OR
         S."currency" != T."currency" OR
         S."netsuite_location" != T."netsuite_location" OR          
         S."purchase_date" != T."purchase_date" OR      
         S."product_name" != T."product_name" OR 
         S."order_status" != T."order_status" OR 
         S."order_item_status" != T."order_item_status" OR              
         S."tags" != T."tags" OR
         S."fulfillment_channel" != T."fulfillment_channel" OR
         S."netsuite_gl" != T."netsuite_gl" OR
         S."quantity_ordered" != T."quantity_ordered"       
        );


CREATE OR REPLACE TEMPORARY TABLE $stage_db.netsuite_shopify_disputed_cash_sales AS
WITH aggr AS (
	SELECT  "netsuite_cash_sale_post_key",
            "subledger_key",
            NVL(SUM("amount"), 0) AS "tot_amount"
    FROM $curated_db.fact_all_subledger_aggregate
    WHERE "report_date" >= DATEADD(DAY,-31,TO_DATE(CURRENT_TIMESTAMP()))
    	AND "marketplace" = 'SHOPIFY'
    	AND UPPER("posting_status") IN ('POSTED', 'MANUAL_POST','SUBMITTED_TO_NETSUITE', 'DISPUTED_TRANSACTION')
    GROUP BY "netsuite_cash_sale_post_key", "subledger_key"
),
sl AS (
	SELECT  "netsuite_cash_sale_post_key",
            "subledger_key",
            NVL(SUM("amount"), 0) AS "tot_amount"
    FROM $curated_db.fact_shopify_subledger
    WHERE "netsuite_cash_sale_post_key" IN (SELECT DISTINCT "netsuite_cash_sale_post_key" FROM aggr)
        AND "posted_flag" != 'do not post'
    GROUP BY "netsuite_cash_sale_post_key", "subledger_key"
)
SELECT  sl."netsuite_cash_sale_post_key",
        sl."subledger_key",
        sl."tot_amount" AS "sl_tot_amount",
        aggr."tot_amount" AS "aggr_tot_amount"
FROM aggr
FULL OUTER JOIN sl
	ON aggr."netsuite_cash_sale_post_key" = sl."netsuite_cash_sale_post_key"
    AND aggr."subledger_key" = sl."subledger_key"
WHERE ABS(to_numeric(coalesce(aggr."tot_amount",0),18,2) - to_numeric(coalesce(sl."tot_amount",0),18,2)) > 0.01;


UPDATE $curated_db.fact_shopify_subledger
SET "posted_flag" = 'DISPUTED_TRANSACTION',
    "record_updated_timestamp_utc" = SYSDATE()
WHERE "netsuite_cash_sale_post_key" IN (
        SELECT DISTINCT "netsuite_cash_sale_post_key"
        FROM $stage_db.netsuite_shopify_disputed_cash_sales
    )
    AND LOWER("posted_flag") IN ('ready to post', 'posted', 'manual_post', 'disputed_transaction');


DROP TABLE $curated_db.FACT_SHOPIFY_SUBLEDGER_TEMP;
DROP TABLE $stage_db.STG_SHOPIFY_REVENUE_REFUNDS;
DROP TABLE $stage_db.netsuite_shopify_disputed_cash_sales;

COMMIT;




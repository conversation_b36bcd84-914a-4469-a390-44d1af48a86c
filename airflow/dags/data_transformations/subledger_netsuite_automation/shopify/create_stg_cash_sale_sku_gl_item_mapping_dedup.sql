CREATE OR REPLACE TRANSIENT TABLE $stage_db.shopify_cash_sale_sku_gl_item_mapping
AS
SELECT DISTINCT 
          SKU AS "sku"
        , TRANSACTION_TYPE AS "transaction_type"
        , NETSUITE_ITEM_MAPPING AS "netsuite_item_number"
        , NULLIF(TRIM(NETSUITE_ID), '') AS "netsuite_id"
        , NETSUITE_GL AS "netsuite_gl"
        , FILE_NAME AS "file_name"
        , ETL_BATCH_RUN_TIME AS "etl_batch_runtime"
FROM $raw_db.SHOPIFY_CASH_SALE_SKU_GL_ITEM_MAPPING_STG S
QUALIFY ROW_NUMBER() OVER (PARTITION BY "sku","transaction_type","netsuite_item_number","netsuite_id","netsuite_gl"
                           ORDER BY "etl_batch_runtime" desc, "file_name" desc) = 1
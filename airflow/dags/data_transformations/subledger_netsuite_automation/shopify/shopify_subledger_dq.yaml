---
test_id: "fact_shopify_subledger_dup_check"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS result 
  FROM 
  (
   SELECT "subledger_pk" 
   FROM $curated_db.FACT_SHOPIFY_SUBLEDGER 
   GROUP BY 1
   HAVING COUNT(1) >1 
   LIMIT 1
  )

---
test_id: "shopify_subledger_and_revenue_refund_comparison"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS "result"
  FROM 
  (
  SELECT 
    COALESCE(stg."brand", fact."brand") AS "brand",
    stg."tot_amount"  AS stg_tot_amount,
    fact."tot_amount" AS fact_tot_amount
  FROM 
  (
    SELECT sum(r."amount") "tot_amount" ,r."brand" 
    FROM (
      SELECT 	 "brand" 
          ,SUM(COALESCE("gift_wrap_price_lc",0) + COALESCE("shipping_price_lc" ,0) + 
              COALESCE("item_promotion_discount_lc" ,0) + COALESCE("ship_promotion_discount_lc" ,0) + 
              COALESCE("item_tax_lc",0) + COALESCE("shipping_tax_lc" ,0) + 
              COALESCE("gift_wrap_tax_lc",0) + COALESCE("sell_price_lc",0)) AS "amount"
      FROM (
        SELECT 	 CONVERT_TIMEZONE('UTC','America/Los_Angeles', "shipment_date_utc") :: DATE AS "report_date"		 
            ,"brand" 		 
            ,"order_id" 		 
            ,"external_order_id"
            ,"sku" 		 	 
            ,"product_name" 
            ,MAX(r."gift_wrap_price_lc") AS "gift_wrap_price_lc"                            
            ,MAX(r."shipping_price_lc") AS "shipping_price_lc"                         
            ,MAX(r."item_promotion_discount_lc") * -1 AS "item_promotion_discount_lc"                            
            ,MAX(r."ship_promotion_discount_lc") * -1 AS "ship_promotion_discount_lc"                            
            ,MAX(r."item_tax_lc") AS "item_tax_lc"                               
            ,MAX(r."shipping_tax_lc") AS "shipping_tax_lc"                                   
            ,MAX(r."gift_wrap_tax_lc") AS "gift_wrap_tax_lc"                                  
            ,MAX(r."sell_price_lc") AS "sell_price_lc"    
        FROM DWH.PROD.FACT_ALL_REVENUE r
        WHERE "report_date" >= CASE WHEN UPPER("brand") = 'FCP' THEN '2023-07-21' ELSE '2022-11-01' END
        AND "brand" NOT IN ('FUL','PNT','HHR','AQU','OTO','DRE','BPR','PUR','KNT','ONA','OBT','POP','VIK','RGR','TAP',
            'CCR','NUV','DCY','EKN','ALB','LUC','LCF','CPR','LLF','ADA','FHG','RST','ECL','MAL','ACU','HPS','BLO','IMA','ZZZ')
        AND "marketplace" = 'SHOPIFY'
        AND "country_code" = 'US'
        GROUP BY 1,2,3,4,5,6
      )
      GROUP BY 1
      UNION ALL
      SELECT 	"brand" 	
          ,SUM(COALESCE("product_refund",0) + COALESCE("tax_refund" ,0) + COALESCE("shipping_refund" ,0) + COALESCE("other_refund" ,0)) AS "amount"
      FROM DWH.PROD.FACT_ALL_REFUNDS
      WHERE CONVERT_TIMEZONE('UTC','America/Los_Angeles', "refund_date_utc") :: DATE >= CASE WHEN UPPER("brand") = 'FCP' THEN '2023-07-21' ELSE '2022-11-01' END
      AND "marketplace" = 'SHOPIFY'
      AND "brand" NOT IN ('FUL','PNT','HHR','AQU','OTO','DRE','BPR','PUR','KNT','ONA','OBT','POP','VIK','RGR','TAP',
            'CCR','NUV','DCY','EKN','ALB','LUC','LCF','CPR','LLF','ADA','FHG','RST','ECL','MAL','ACU','HPS','BLO','IMA','ZZZ')
      AND "country_code" = 'US'
      GROUP BY 1
    ) r
    GROUP BY 2
  )stg
  full outer join 
  (
  SELECT sum("amount") "tot_amount","brand" 
  FROM  $curated_db.FACT_SHOPIFY_SUBLEDGER 
  WHERE 1=1
  AND "posted_flag" <> 'do not post'
  AND  ("external_order_id" <>  '4846121713707' OR "report_date" <> '2023-07-20')
  GROUP BY "brand"
  )fact
    ON stg."brand" = fact."brand"
  where abs(coalesce(stg."tot_amount",0) - coalesce(fact."tot_amount",0)) > 0.1
  limit 1
  ) T

---
test_id: "shopify_subledger_missing_netsuite_brand_id"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS result 
  FROM 
  (
   SELECT 1
   FROM $curated_db.FACT_SHOPIFY_SUBLEDGER 
   WHERE "netsuite_brand_id" IS NULL 
   AND "posted_flag" = 'not ready to post'
   AND "report_date" >= '2022-12-01'
   LIMIT 1
  )

---
test_id: "shopify_subledger_missing_netsuite_customer_location_id"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT DISTINCT "brand"
   FROM $curated_db.FACT_SHOPIFY_SUBLEDGER 
   WHERE ("netsuite_customer_internal_id" IS NULL 
   OR "netsuite_location_internal_id" IS NULL)
   AND "posted_flag" = 'not ready to post'
   AND "report_date" >= '2022-12-01'
   LIMIT 1
  )

---
test_id: "shopify_subledger_missing_netsuite_item_number"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT DISTINCT "sku"
   FROM $curated_db.FACT_SHOPIFY_SUBLEDGER 
   WHERE "netsuite_item_number" IS NULL 
   AND "posted_flag" = 'not ready to post'
   AND "report_date" >= '2022-12-01'
   LIMIT 1
  )

---
test_id: "additional_transactions_on_posted_cashsales"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS result 
  FROM 
  (
    SELECT   DISTINCT "netsuite_cash_sale_post_key"
    FROM $curated_db.FACT_SHOPIFY_SUBLEDGER
    WHERE lower("posted_flag") <> 'do not post' 
    GROUP BY "netsuite_cash_sale_post_key"
    HAVING COUNT(DISTINCT CASE WHEN lower("posted_flag") = 'manual_post' THEN 'posted' 
                              WHEN lower("posted_flag") IN  ('ready to post','not ready to post') THEN 'not posted' 
                  ELSE lower("posted_flag") 
                END) > 1
    LIMIT 1
  )
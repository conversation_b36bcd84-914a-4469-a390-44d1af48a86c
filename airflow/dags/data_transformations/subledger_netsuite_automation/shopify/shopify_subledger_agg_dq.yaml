---
test_id: "fact_all_subledger_aggregate_dup_check"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT "subledger_key" 
   FROM $curated_db.FACT_ALL_SUBLEDGER_AGGREGATE  
   WHERE "marketplace" = 'SHOPIFY'
   GROUP BY 1
   HAVING COUNT(1) >1 
   LIMIT 1
  )

---
test_id: "shopify_subledger_and_aggregate_comparison"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS "result"
  FROM 
  (
   SELECT 
     COALESCE(aggr."brand", sl."brand") AS "brand",
     aggr."tot_amount"  AS stg_tot_amount,
     sl."tot_amount" AS fact_tot_amount
   FROM 
  (
  	SELECT 
  		   S."brand"
  	     , IFNULL(SUM(S."amount"),0)	AS "tot_amount"
  	FROM $curated_db.FACT_SHOPIFY_SUBLEDGER S
  	LEFT JOIN (SELECT DISTINCT "netsuite_cash_sale_post_key" 
  			   FROM $curated_db.FACT_SHOPIFY_SUBLEDGER 
  			   WHERE "posted_flag"  = 'not ready to post') C
  		ON S."netsuite_cash_sale_post_key" = C."netsuite_cash_sale_post_key"
    LEFT JOIN (SELECT DISTINCT "netsuite_cash_sale_post_key" 
  			   FROM $curated_db.FACT_ALL_SUBLEDGER_AGGREGATE 
  			   WHERE UPPER("posting_status") IN ('PROCESSING', 'SUBMITTED_TO_NETSUITE')) A
  		ON S."netsuite_cash_sale_post_key" = A."netsuite_cash_sale_post_key"
  	WHERE S."posted_flag"  <> 'do not post'
  	AND "report_date" >= DATEADD(DAY,-31,TO_DATE(CURRENT_TIMESTAMP()))
    AND "report_date" <= current_date() - 2
  	AND C."netsuite_cash_sale_post_key" IS NULL
  	AND UPPER(s."netsuite_item_number") <> 'NOT USED'
    AND A."netsuite_cash_sale_post_key" IS NULL
  	GROUP BY S."brand"
  ) aggr
  FULL OUTER JOIN 
  (
    SELECT "brand" ,sum("amount") "tot_amount"
    FROM  $curated_db.FACT_ALL_SUBLEDGER_AGGREGATE 
    WHERE "report_date" >= DATEADD(DAY,-31,TO_DATE(CURRENT_TIMESTAMP()))
    AND "marketplace" = 'SHOPIFY'
      AND ("posting_status" IS NULL OR UPPER("posting_status") IN ('POSTED', 'MANUAL_POST', 'DISPUTED_TRANSACTION'))
    GROUP BY "brand"	
  ) sl
  	ON aggr."brand" = sl."brand"
  WHERE ABS(to_numeric(coalesce(aggr."tot_amount",0),18,2) - to_numeric(coalesce(sl."tot_amount",0),18,2)) > 0.01
   LIMIT 1
  ) T
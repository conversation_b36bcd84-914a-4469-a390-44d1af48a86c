BEGIN TRANSACTION;

SET max_report_date    = (
    SELECT MIN("report_date") FROM
    (
        SELECT MIN("report_date") AS "report_date" 
        FROM $curated_db.FACT_SHOPIFY_SUBLEDGER 
        WHERE "record_updated_timestamp_utc" >= TO_TIMESTAMP_NTZ('$start_ts')
        OR "report_date" >= current_date() - 2 
        UNION ALL
        SELECT COALESCE(MIN("report_date"),'1900-01-01') AS "report_date" --coalesce is for the initial load
        FROM $curated_db.FACT_SHOPIFY_SUBLEDGER 
        WHERE "mapping_record_updated_timestamp_utc" >= TO_TIMESTAMP_NTZ('$start_ts')
        OR "report_date" >= current_date() - 2 
    )
);

CREATE OR REPLACE TEMPORARY TABLE $curated_db.FACT_SHOPIFY_SUBLEDGER_AGGREGATE_TEMP AS
WITH exclude_cash_sale AS (
    --exclude all cash sales if any transaction within that cash sale is not ready to post
    SELECT DISTINCT "netsuite_cash_sale_post_key" 
    FROM $curated_db.FACT_SHOPIFY_SUBLEDGER 
    WHERE lower("posted_flag")  IN ('not ready to post','posted','manual_post')
)
SELECT 
          S."subledger_key"
        , S."netsuite_cash_sale_post_key"
        , S."report_date"
        , 'SHOPIFY' AS "marketplace"
        , S."netsuite_brand_id"
        , S."brand"
        , S."country_code"
        , S."payment_gateway"
        , S."netsuite_customer_internal_id"
        , S."netsuite_customer_name"
        , S."netsuite_location"
        , S."netsuite_location_internal_id"
        , S."netsuite_item_number"
        , S."netsuite_id"  
        , S."item_type"
        , IFNULL(SUM(S."quantity"),0) AS "quantity"
        , IFNULL(SUM(S."amount"),0) AS "amount"
        , S."currency"
        , 'shopify_subledger_netsuite_automation' AS "created_by"
        , 'shopify_subledger_netsuite_automation' AS "updated_by"
        , SYSDATE() AS "record_created_timestamp_utc"
        , NULL AS "record_updated_timestamp_utc"
FROM $curated_db.FACT_SHOPIFY_SUBLEDGER S
LEFT JOIN exclude_cash_sale C
    ON S."netsuite_cash_sale_post_key" = C."netsuite_cash_sale_post_key"
WHERE 	S."posted_flag"  <> 'do not post'
AND "report_date" >= CASE WHEN $max_report_date IS NULL THEN '1900-01-01 00:00:00.000' ELSE $max_report_date END 
AND "report_date" <= current_date() - 2 -- T-2 day lag for posting
AND C."netsuite_cash_sale_post_key" IS NULL
AND UPPER(s."netsuite_item_number") <> 'NOT USED'
GROUP BY  S."subledger_key"
        , S."netsuite_cash_sale_post_key"
        , S."report_date"
        , S."netsuite_brand_id"
        , S."brand"
        , S."country_code"
        , S."payment_gateway"
        , S."netsuite_customer_internal_id"
        , S."netsuite_customer_name"
        , S."netsuite_location"
        , S."netsuite_location_internal_id"
        , S."netsuite_item_number"
        , S."netsuite_id" 
        , S."item_type"
        , S."currency";

--avoid updating any records that are in the middle of posting
UPDATE $curated_db.FACT_ALL_SUBLEDGER_AGGREGATE A
SET  A."quantity" = T."quantity"
    ,A."amount"	= T."amount"
    ,A."item_type" = T."item_type"
    ,A."netsuite_customer_name" = T."netsuite_customer_name"
    ,A."netsuite_location" = T."netsuite_location"
    ,A."netsuite_id" = T."netsuite_id"
    ,A."record_updated_timestamp_utc" = SYSDATE()
    ,A."updated_by" = 'shopify_subledger_netsuite_automation'
FROM $curated_db.FACT_SHOPIFY_SUBLEDGER_AGGREGATE_TEMP T 
WHERE 	A."subledger_key" =  T."subledger_key"
    AND A."posting_status" IS NULL
    AND	(A."quantity" <> T."quantity" OR
            to_numeric(A."amount",18,2) <> to_numeric(T."amount",18,2) OR
            COALESCE(A."item_type",'') <> COALESCE(T."item_type",'') OR
            COALESCE(A."netsuite_customer_name",'') <> COALESCE(T."netsuite_customer_name",'') OR
            COALESCE(A."netsuite_location",'') <> COALESCE(T."netsuite_location",'') OR
            COALESCE(A."netsuite_id",0)	<> COALESCE(T."netsuite_id",0)
        );

-- Delete disputed transaction from the aggregate table
DELETE FROM $curated_db.fact_all_subledger_aggregate
WHERE "netsuite_cash_sale_post_key" IN (
            SELECT DISTINCT "netsuite_cash_sale_post_key"
            FROM $curated_db.fact_shopify_subledger
            WHERE "posted_flag" = 'DISPUTED_TRANSACTION'
                AND "report_date" >= $max_report_date
       );

--insert only when subledger_key does not exist in the aggr table	
INSERT INTO $curated_db.FACT_ALL_SUBLEDGER_AGGREGATE (
"subledger_key", "netsuite_cash_sale_post_key", "report_date", "marketplace",
"netsuite_brand_id", "brand", "country_code", "payment_gateway", "netsuite_customer_internal_id",
"netsuite_customer_name", "netsuite_location", "netsuite_location_internal_id", "netsuite_item_number", "netsuite_id",
"item_type", "quantity", "amount", "currency", "created_by",  "updated_by","record_created_timestamp_utc", "record_updated_timestamp_utc"
)
SELECT s.*
FROM $curated_db.FACT_SHOPIFY_SUBLEDGER_AGGREGATE_TEMP s
LEFT JOIN $curated_db.FACT_ALL_SUBLEDGER_AGGREGATE a
    ON s."subledger_key" = a."subledger_key" 
WHERE  a."subledger_key" IS NULL;


-- Update posting status for disputed cash sales
UPDATE $curated_db.fact_all_subledger_aggregate
SET "posting_status" = 'DISPUTED_TRANSACTION',
    "record_updated_timestamp_utc" = SYSDATE()
WHERE "netsuite_cash_sale_post_key" IN (
            SELECT DISTINCT "netsuite_cash_sale_post_key"
            FROM $curated_db.fact_shopify_subledger
            WHERE "posted_flag" = 'DISPUTED_TRANSACTION'
                AND "report_date" >= $max_report_date
       );


-- /*remove any records that are present in the aggregate tables but not in subledger. This case could happen when an
-- incorrect mapping information is entered for records and updated later.
-- The "subledger_key" would change in that case and the old one will no longer be valid for posting */
DELETE 
FROM $curated_db.FACT_ALL_SUBLEDGER_AGGREGATE S
USING (
    SELECT S.* 
    FROM $curated_db.FACT_ALL_SUBLEDGER_AGGREGATE S
    LEFT JOIN  $curated_db.FACT_SHOPIFY_SUBLEDGER T 
    on S."subledger_key" = T."subledger_key" 
    WHERE  T."subledger_key" IS NULL
    AND S."posting_status" IS NULL
    AND S."marketplace" = 'SHOPIFY')  c
WHERE S."subledger_key" = c."subledger_key" ;

-- /*edge case: when a transaction is marked from 'ready to post' to 'do not post' or 'not ready to post' ,
-- remove the entire cash sale from aggr table to prevent it from posting.
-- the cash sale will get added back automatically after all the transactions for it are ready to post again*/
DELETE
FROM $curated_db.FACT_ALL_SUBLEDGER_AGGREGATE S
USING (
    SELECT DISTINCT "netsuite_cash_sale_post_key"
    FROM $curated_db.FACT_SHOPIFY_SUBLEDGER
    WHERE 	"posted_flag"  = 'not ready to post'
) T
WHERE   S."netsuite_cash_sale_post_key" = T."netsuite_cash_sale_post_key" 
    AND S."marketplace" = 'SHOPIFY'
    AND S."posting_status" IS NULL;

COMMIT;
CREATE OR REPLACE TRANSIENT TABLE $stage_db.shopify_store_customer_location_mapping
AS
SELECT 
          CUSTOMER_NAME AS "customer_name"
        , CUSTOMER_INTERNAL_ID AS "customer_internal_id"
        , shopify_store_id_ AS "store_id"
        , geo AS "country_code"
        , CATEGORY AS "category"
        , FULFILLMENT_CHANNEL AS "fulfillment_channel"
        , LOCATION AS "location"
        , LOCATION_INTERNAL_ID AS "location_internal_id"
        , BRAND AS "brand" 
        , SHOPIFY_LOCATION_ID AS "shopify_location_id"
        , FILE_NAME AS "file_name"
        , ETL_BATCH_RUN_TIME AS "etl_batch_runtime"
FROM $raw_db.SHOPIFY_STORE_CUSTOMER_LOCATION_MAPPING_STG S
QUALIFY ROW_NUMBER() OVER (PARTITION BY "customer_name","customer_internal_id","store_id","country_code","category","fulfillment_channel","location","location_internal_id","brand","shopify_location_id"
                           ORDER BY "etl_batch_runtime" desc) = 1 
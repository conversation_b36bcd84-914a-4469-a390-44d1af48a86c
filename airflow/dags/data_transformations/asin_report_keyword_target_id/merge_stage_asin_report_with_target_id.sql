CREATE TABLE IF NOT EXISTS $stage_db.merge_asin_report_with_target_id AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_asin_report_with_target_id
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_asin_report_with_target_id AS tgt
USING
    $stage_db.dedupe_asin_report_with_target_id AS src
    
        ON 1 = 1
       AND src.pk = tgt.pk
       
WHEN MATCHED AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt.RequestTime = src.RequestTime,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.accountId = src.accountId,
    tgt.accountName = src.accountName,
    tgt.adGroupId = src.adGroupId,
    tgt.adGroupName = src.adGroupName,
    tgt.asin = src.asin,
    tgt.attributedConversions14d = src.attributedConversions14d,
    tgt.attributedConversions14dOtherSKU = src.attributedConversions14dOtherSKU,
    tgt.attributedConversions1d = src.attributedConversions1d,
    tgt.attributedConversions1dOtherSKU = src.attributedConversions1dOtherSKU,
    tgt.attributedConversions30d = src.attributedConversions30d,
    tgt.attributedConversions30dOtherSKU = src.attributedConversions30dOtherSKU,
    tgt.attributedConversions7d = src.attributedConversions7d,
    tgt.attributedConversions7dOtherSKU = src.attributedConversions7dOtherSKU,
    tgt.attributedKindleEditionNormalizedPagesRead14d = src.attributedKindleEditionNormalizedPagesRead14d,
    tgt.attributedKindleEditionNormalizedPagesRoyalties14d = src.attributedKindleEditionNormalizedPagesRoyalties14d,
    tgt.attributedSales14d = src.attributedSales14d,
    tgt.attributedSales14dOtherSKU = src.attributedSales14dOtherSKU,
    tgt.attributedSales1d = src.attributedSales1d,
    tgt.attributedSales1dOtherSKU = src.attributedSales1dOtherSKU,
    tgt.attributedSales30d = src.attributedSales30d,
    tgt.attributedSales30dOtherSKU = src.attributedSales30dOtherSKU,
    tgt.attributedSales7d = src.attributedSales7d,
    tgt.attributedSales7dOtherSKU = src.attributedSales7dOtherSKU,
    tgt.attributedUnitsOrdered14d = src.attributedUnitsOrdered14d,
    tgt.attributedUnitsOrdered14dOtherSKU = src.attributedUnitsOrdered14dOtherSKU,
    tgt.attributedUnitsOrdered1d = src.attributedUnitsOrdered1d,
    tgt.attributedUnitsOrdered1dOtherSKU = src.attributedUnitsOrdered1dOtherSKU,
    tgt.attributedUnitsOrdered30d = src.attributedUnitsOrdered30d,
    tgt.attributedUnitsOrdered30dOtherSKU = src.attributedUnitsOrdered30dOtherSKU,
    tgt.attributedUnitsOrdered7d = src.attributedUnitsOrdered7d,
    tgt.attributedUnitsOrdered7dOtherSKU = src.attributedUnitsOrdered7dOtherSKU,
    tgt.campaignId = src.campaignId,
    tgt.campaignName = src.campaignName,
    tgt.countryName = src.countryName,
    tgt.currency = src.currency,
    tgt.keywordText = src.keywordText,
    tgt.matchType = src.matchType,
    tgt.otherAsin = src.otherAsin,
    tgt.portfolioId = src.portfolioId,
    tgt.profileId = src.profileId,
    tgt.reportDate = src.reportDate,
    tgt.sku = src.sku,
    tgt.targetId = src.targetId,
    tgt.targetingExpression = src.targetingExpression,
    tgt.targetingText = src.targetingText,
    tgt.targetingType = src.targetingType,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    RequestTime,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    accountId,
    accountName,
    adGroupId,
    adGroupName,
    asin,
    attributedConversions14d,
    attributedConversions14dOtherSKU,
    attributedConversions1d,
    attributedConversions1dOtherSKU,
    attributedConversions30d,
    attributedConversions30dOtherSKU,
    attributedConversions7d,
    attributedConversions7dOtherSKU,
    attributedKindleEditionNormalizedPagesRead14d,
    attributedKindleEditionNormalizedPagesRoyalties14d,
    attributedSales14d,
    attributedSales14dOtherSKU,
    attributedSales1d,
    attributedSales1dOtherSKU,
    attributedSales30d,
    attributedSales30dOtherSKU,
    attributedSales7d,
    attributedSales7dOtherSKU,
    attributedUnitsOrdered14d,
    attributedUnitsOrdered14dOtherSKU,
    attributedUnitsOrdered1d,
    attributedUnitsOrdered1dOtherSKU,
    attributedUnitsOrdered30d,
    attributedUnitsOrdered30dOtherSKU,
    attributedUnitsOrdered7d,
    attributedUnitsOrdered7dOtherSKU,
    campaignId,
    campaignName,
    countryName,
    currency,
    keywordText,
    matchType,
    otherAsin,
    portfolioId,
    profileId,
    reportDate,
    sku,
    targetId,
    targetingExpression,
    targetingText,
    targetingType,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src.RequestTime, 
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.accountId, 
    src.accountName, 
    src.adGroupId, 
    src.adGroupName, 
    src.asin, 
    src.attributedConversions14d, 
    src.attributedConversions14dOtherSKU, 
    src.attributedConversions1d, 
    src.attributedConversions1dOtherSKU, 
    src.attributedConversions30d, 
    src.attributedConversions30dOtherSKU, 
    src.attributedConversions7d, 
    src.attributedConversions7dOtherSKU, 
    src.attributedKindleEditionNormalizedPagesRead14d, 
    src.attributedKindleEditionNormalizedPagesRoyalties14d, 
    src.attributedSales14d, 
    src.attributedSales14dOtherSKU, 
    src.attributedSales1d, 
    src.attributedSales1dOtherSKU, 
    src.attributedSales30d, 
    src.attributedSales30dOtherSKU, 
    src.attributedSales7d, 
    src.attributedSales7dOtherSKU, 
    src.attributedUnitsOrdered14d, 
    src.attributedUnitsOrdered14dOtherSKU, 
    src.attributedUnitsOrdered1d, 
    src.attributedUnitsOrdered1dOtherSKU, 
    src.attributedUnitsOrdered30d, 
    src.attributedUnitsOrdered30dOtherSKU, 
    src.attributedUnitsOrdered7d, 
    src.attributedUnitsOrdered7dOtherSKU, 
    src.campaignId, 
    src.campaignName, 
    src.countryName, 
    src.currency, 
    src.keywordText, 
    src.matchType, 
    src.otherAsin, 
    src.portfolioId, 
    src.profileId, 
    src.reportDate, 
    src.sku, 
    src.targetId, 
    src.targetingExpression, 
    src.targetingText, 
    src.targetingType, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
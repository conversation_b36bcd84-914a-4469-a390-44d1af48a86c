CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_asin_report_with_keyword_id AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(requesttime AS VARCHAR), ''), '-',
            COALESCE(CAST(accountid AS VARCHAR), ''), '-',
            COALESCE(CAST(campaignid AS VARCHAR), ''), '-',
            COALESCE(CAST(adgroupid AS VARCHAR), ''), '-',
            COALESCE(CAST(asin AS VARCHAR), ''), '-',
            COALESCE(CAST(keywordtext AS VARCHAR), '')
            )) AS pk,
        RequestTime,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountId,
        accountName,
        adGroupId,
        adGroupName,
        asin,
        attributedConversions14d,
        attributedConversions14dOtherSKU,
        attributedConversions1d,
        attributedConversions1dOtherSKU,
        attributedConversions30d,
        attributedConversions30dOtherSKU,
        attributedConversions7d,
        attributedConversions7dOtherSKU,
        attributedKindleEditionNormalizedPagesRead14d,
        attributedKindleEditionNormalizedPagesRoyalties14d,
        attributedSales14d,
        attributedSales14dOtherSKU,
        attributedSales1d,
        attributedSales1dOtherSKU,
        attributedSales30d,
        attributedSales30dOtherSKU,
        attributedSales7d,
        attributedSales7dOtherSKU,
        attributedUnitsOrdered14d,
        attributedUnitsOrdered14dOtherSKU,
        attributedUnitsOrdered1d,
        attributedUnitsOrdered1dOtherSKU,
        attributedUnitsOrdered30d,
        attributedUnitsOrdered30dOtherSKU,
        attributedUnitsOrdered7d,
        attributedUnitsOrdered7dOtherSKU,
        campaignId,
        campaignName,
        countryName,
        currency,
        keywordId,
        keywordText,
        matchType,
        otherAsin,
        portfolioId,
        profileId,
        reportDate,
        sku,
        targetId,
        targetingType,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_asin_report_with_keyword_id
    WHERE 1=1 AND accountid IS NOT NULL 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY asin, accountid, campaignid, adgroupid, asin, keywordtext
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);
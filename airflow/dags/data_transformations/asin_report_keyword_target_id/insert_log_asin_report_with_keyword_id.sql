CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_asin_report_with_keyword_id AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_asin_report_with_keyword_id
    WHERE 1 = 0;

INSERT INTO $raw_db.log_asin_report_with_keyword_id (
    RequestTime,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    accountId,
    accountName,
    adGroupId,
    adGroupName,
    asin,
    attributedConversions14d,
    attributedConversions14dOtherSKU,
    attributedConversions1d,
    attributedConversions1dOtherSKU,
    attributedConversions30d,
    attributedConversions30dOtherSKU,
    attributedConversions7d,
    attributedConversions7dOtherSKU,
    attributedKindleEditionNormalizedPagesRead14d,
    attributedKindleEditionNormalizedPagesRoyalties14d,
    attributedSales14d,
    attributedSales14dO<PERSON><PERSON><PERSON><PERSON>,
    attributedSales1d,
    attributedSales1dOtherSKU,
    attributedSales30d,
    attributedSales30dOtherSKU,
    attributedSales7d,
    attributedSales7dOtherSKU,
    attributedUnitsOrdered14d,
    attributedUnitsOrdered14dOtherSKU,
    attributedUnitsOrdered1d,
    attributedUnitsOrdered1dOtherSKU,
    attributedUnitsOrdered30d,
    attributedUnitsOrdered30dOtherSKU,
    attributedUnitsOrdered7d,
    attributedUnitsOrdered7dOtherSKU,
    campaignId,
    campaignName,
    countryName,
    currency,
    keywordId,
    keywordText,
    matchType,
    otherAsin,
    portfolioId,
    profileId,
    reportDate,
    sku,
    targetId,
    targetingType,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        RequestTime,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountId,
        accountName,
        adGroupId,
        adGroupName,
        asin,
        attributedConversions14d,
        attributedConversions14dOtherSKU,
        attributedConversions1d,
        attributedConversions1dOtherSKU,
        attributedConversions30d,
        attributedConversions30dOtherSKU,
        attributedConversions7d,
        attributedConversions7dOtherSKU,
        attributedKindleEditionNormalizedPagesRead14d,
        attributedKindleEditionNormalizedPagesRoyalties14d,
        attributedSales14d,
        attributedSales14dOtherSKU,
        attributedSales1d,
        attributedSales1dOtherSKU,
        attributedSales30d,
        attributedSales30dOtherSKU,
        attributedSales7d,
        attributedSales7dOtherSKU,
        attributedUnitsOrdered14d,
        attributedUnitsOrdered14dOtherSKU,
        attributedUnitsOrdered1d,
        attributedUnitsOrdered1dOtherSKU,
        attributedUnitsOrdered30d,
        attributedUnitsOrdered30dOtherSKU,
        attributedUnitsOrdered7d,
        attributedUnitsOrdered7dOtherSKU,
        campaignId,
        campaignName,
        countryName,
        currency,
        keywordId,
        keywordText,
        matchType,
        otherAsin,
        portfolioId,
        profileId,
        reportDate,
        sku,
        targetId,
        targetingType,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_asin_report_with_keyword_id;
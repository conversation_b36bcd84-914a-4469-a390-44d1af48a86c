CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amazon_global_fba_inventory AS
    SELECT
    r.report_start_date_time::date as snapshot_date,
    r.merchant_sku AS sku,
    r.fnsku,
    r.asin,
    r.product_name,
    r.marketplace AS country_code,
    r.merchant_id,
    'New' as condition,
    to_decimal(r.inbound_quantity, 10, 2) AS inbound_quantity,
    to_decimal(r.prime_available_quantity, 10, 2) AS prime_available_quantity,
    to_decimal(r.fc_transfer_quantity, 10, 2) AS fc_transfer_quantity,
    to_decimal(r.fc_processing_quantity, 10, 2) AS fc_processing_quantity,
    to_decimal(r.customer_order_reserved_quantity, 10, 2) AS customer_order_reserved_quantity,
    to_decimal(r.future_supply_buyable_quantity, 10, 2) AS future_supply_buyable_quantity,
    to_decimal(r.reserved_future_supply_quantity, 10, 2) AS reserved_future_supply_quantity,
    to_decimal(r.unsellable_quantity, 10, 2) AS unsellable_quantity,
    to_decimal(r.inventory_aged_0_90_days, 10, 2) AS inventory_aged_0_90_days,
    to_decimal(r.inventory_aged_91_180_days, 10, 2) AS inventory_aged_91_180_days,
    to_decimal(r.inventory_aged_181_365_days, 10, 2) AS inventory_aged_181_365_days,
    to_decimal(r.inventory_aged_365plus_days, 10, 2) AS inventory_aged_365plus_days,
    to_decimal(r.units_shipped_last_7_days, 10, 2) AS units_shipped_last_7_days,
    to_decimal(r.units_shipped_last_30_days, 10, 2) AS units_shipped_last_30_days,
    to_decimal(r.units_shipped_last_60_days, 10, 2) AS units_shipped_last_60_days,
    to_decimal(r.units_shipped_last_90_days, 10, 2) AS units_shipped_last_90_days,
    to_decimal(r.days_of_supply, 10, 2) AS days_of_supply,
    to_decimal(r.sell_through, 10, 2) AS sell_through,
    to_decimal(r.upstream_storage_available_quantity, 10, 2) AS upstream_storage_available_quantity,
    to_decimal(r.upstream_storage_reserved_quantity, 10, 2) AS upstream_storage_reserved_quantity,
    r.report_start_date_time,
    r.report_end_date_time,
    r.report_fetched_and_loaded_at,
    r.scraper_id,
    r.scraper_seller_id AS sc_seller_id,
    r.file_name,
    r.etl_batch_run_time,
    split_part(FILE_NAME, '/',  7) as connector_region
    FROM $raw_db.raw_amazon_global_fba_inventory r
    WHERE 1=1 AND r.marketplace IS NOT NULL AND r.merchant_id IS NOT NULL AND r.asin IS NOT NULL
    AND r.report_start_date_time IS NOT NULL AND r.report_end_date_time IS NOT NULL
    QUALIFY ROW_NUMBER() OVER(
  PARTITION BY
    report_start_date_time :: DATE
    , sc_seller_id
    , country_code
    , asin
    , fnsku
    , sku
    , condition ORDER BY report_fetched_and_loaded_at  desc) = 1;


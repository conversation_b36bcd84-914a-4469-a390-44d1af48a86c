CREATE OR REPLACE TRANSIENT TABLE $stage_db.stage_fba_global_inventory AS
with cr_flags AS
   (
   SELECT *
    ,CASE WHEN PRIME_AVAILABLE_QUANTITY = max(PRIME_AVAILABLE_QUANTITY) over(PARTITION BY sc_seller_id,
                 SNAPSHOT_DATE, country_code, asin, fnsku, CONDITION)
            THEN 1
            ELSE 0 END AS flag_quantity
   ,CASE  WHEN ( ((substring(sku,0,3) in (select distinct seller_code FROM dwh.STAGING.STG_SELLER_INFO)) OR substring(sku,0,3) = 'KAC')
   	 	   		  and substring(sku,4,1) ='-')
          THEN 1 ELSE 0 END AS flag_hydy_sku
    FROM $stage_db.dedupe_amazon_global_fba_inventory
 )
,get_best_sku AS (
		SELECT *
            ,CASE   WHEN (upper(sku) LIKE '%_CA' OR upper(sku) LIKE '%-CA' OR upper(sku) LIKE '%-MX' OR upper(sku) LIKE '%_MX'
					    OR upper(sku) LIKE '%-UK' OR upper(sku) LIKE '%_UK' OR upper(sku) LIKE '%_VIT' OR upper(sku) LIKE '%-VIT' )
                    THEN 1 ELSE 0 END as ca_mx_flag
            ,CASE   WHEN (upper(sku) LIKE '%-TEST' OR upper(sku) LIKE '%_TEST') THEN 1 ELSE 0 END AS test_sku_flag
            ,CASE   WHEN upper(COUNTRY_CODE) ='US'
                    THEN FIRST_value(sku) over(PARTITION BY sc_seller_id, SNAPSHOT_DATE ,country_code,asin,fnsku,CONDITION
                                ORDER BY flag_quantity desc, FLAG_HYDY_SKU DESC, ca_mx_flag ,test_sku_flag , LENGTH(sku) DESC, sku desc)
                    ELSE FIRST_value(sku) over(PARTITION BY sc_seller_id, SNAPSHOT_DATE , country_code,asin,fnsku,CONDITION
                            ORDER BY flag_quantity desc, FLAG_HYDY_SKU DESC,ca_mx_flag desc,test_sku_flag , LENGTH(sku) DESC, sku desc)
            END AS final_sku
    FROM cr_flags
  )
,filter_sku as (
    SELECT
        md5(cast(
          coalesce(cast(sc_seller_id as varchar ), '') || '-' ||
          coalesce(cast(snapshot_date as varchar ), '') || '-' ||
          coalesce(cast(country_code as varchar ), '') || '-' ||
          coalesce(cast(asin as varchar ), '') || '-' ||
          coalesce(cast(fnsku as varchar ), '') || '-' ||
          coalesce(cast(condition as varchar), '') as varchar)
          ) as inventory_pk,
          *
    FROM get_best_sku
    WHERE final_sku=sku
),
inv_with_sku_mapping as(
        SELECT
        f.inventory_pk,
        f.snapshot_date,
        f.sku,
        f.fnsku,
        f.asin,
        COALESCE(CASE WHEN slr.is_multi_brand='NO' THEN slr.seller_code END, sabm.brand_code) as brand_code,
        product_name,
        f.country_code,
        azm.marketplace_id,
        f.merchant_id,
        condition,
        inbound_quantity,
        prime_available_quantity,
        fc_transfer_quantity,
        fc_processing_quantity,
        customer_order_reserved_quantity,
        future_supply_buyable_quantity,
        reserved_future_supply_quantity,
        unsellable_quantity,
        inventory_aged_0_90_days,
        inventory_aged_91_180_days,
        inventory_aged_181_365_days,
        inventory_aged_365plus_days,
        units_shipped_last_7_days,
        units_shipped_last_30_days,
        units_shipped_last_60_days,
        units_shipped_last_90_days,
        days_of_supply,
        sell_through,
        upstream_storage_available_quantity,
        upstream_storage_reserved_quantity,
        report_start_date_time,
        report_end_date_time,
        report_fetched_and_loaded_at,
        scraper_id,
        f.sc_seller_id,
        file_name,
        etl_batch_run_time,
        f.connector_region
        FROM filter_sku f
        LEFT JOIN dwh.STAGING.STG_SELLER_INFO slr
        ON slr.seller_id=f.sc_seller_id
        LEFT JOIN $curated_db.marketplace_asin_brand_mapping sabm
        ON sabm.country_code=f.country_code
        AND sabm.asin=f.asin
        LEFT OUTER JOIN dwh.prod.AMAZON_MARKETPLACES azm
        ON azm.country_code = f.country_code
)
select * from inv_with_sku_mapping
  QUALIFY ROW_NUMBER() OVER(
  PARTITION BY
    sc_seller_id
    , snapshot_date
    , country_code
    , asin
    , fnsku
    , condition ORDER BY report_fetched_and_loaded_at desc ) = 1;
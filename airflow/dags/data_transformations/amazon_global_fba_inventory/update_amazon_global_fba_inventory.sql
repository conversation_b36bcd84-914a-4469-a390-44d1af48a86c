-- update brand_code
UPDATE $stage_db.merge_amazon_global_fba_inventory T
   SET brand_code = S.brand_code,
       record_updated_timestamp_utc = SYSDATE()
   FROM
       $curated_db.marketplace_asin_brand_mapping S
   WHERE
      T.country_code=S.country_code
      AND T.asin=S.asin
      AND T.brand_code IS NULL
;

-- update ns_item_number
UPDATE $stage_db.merge_amazon_global_fba_inventory T
   SET netsuite_item_number = S.netsuite_item_number,
       netsuite_item_name = S.netsuite_item_name,
       record_updated_timestamp_utc = SYSDATE()
   FROM
       dwh.prod.sku_asin_item_mapping S
   WHERE
      T.sku=S.sku
      AND T.asin=S.asin
      AND T.country_code=S.country_code
      and T.brand_code = S.brand_code
      AND T.netsuite_item_number IS NULL
;
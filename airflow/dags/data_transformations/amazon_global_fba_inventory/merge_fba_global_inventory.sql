CREATE TABLE IF NOT EXISTS $stage_db.merge_amazon_global_fba_inventory AS
    SELECT
         *
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc

    FROM $stage_db.stage_fba_global_inventory

    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_amazon_global_fba_inventory AS tgt
USING

    $stage_db.stage_fba_global_inventory AS src

    ON 1 = 1
    AND src.inventory_pk = tgt.inventory_pk
WHEN MATCHED
    AND src.report_fetched_and_loaded_at >= tgt.report_fetched_and_loaded_at THEN
UPDATE SET

    tgt.snapshot_date=src.snapshot_date,
    tgt.sku=src.sku,
    tgt.fnsku=src.fnsku,
    tgt.asin=src.asin,
    tgt.brand_code=src.brand_code,
    tgt.marketplace_id=src.marketplace_id,
    tgt.product_name=src.product_name,
    tgt.country_code=src.country_code,
    tgt.merchant_id=src.merchant_id,
    tgt.condition=src.condition,
    tgt.inbound_quantity=src.inbound_quantity,
    tgt.prime_available_quantity=src.prime_available_quantity,
    tgt.fc_transfer_quantity=src.fc_transfer_quantity,
    tgt.fc_processing_quantity=src.fc_processing_quantity,
    tgt.customer_order_reserved_quantity=src.customer_order_reserved_quantity,
    tgt.future_supply_buyable_quantity=src.future_supply_buyable_quantity,
    tgt.reserved_future_supply_quantity=src.reserved_future_supply_quantity,
    tgt.unsellable_quantity=src.unsellable_quantity,
    tgt.inventory_aged_0_90_days=src.inventory_aged_0_90_days,
    tgt.inventory_aged_91_180_days=src.inventory_aged_91_180_days,
    tgt.inventory_aged_181_365_days=src.inventory_aged_181_365_days,
    tgt.inventory_aged_365plus_days=src.inventory_aged_365plus_days,
    tgt.units_shipped_last_7_days=src.units_shipped_last_7_days,
    tgt.units_shipped_last_30_days=src.units_shipped_last_30_days,
    tgt.units_shipped_last_60_days=src.units_shipped_last_60_days,
    tgt.units_shipped_last_90_days=src.units_shipped_last_90_days,
    tgt.days_of_supply=src.days_of_supply, tgt.sell_through=src.sell_through,
    tgt.upstream_storage_available_quantity=src.upstream_storage_available_quantity,
    tgt.upstream_storage_reserved_quantity=src.upstream_storage_reserved_quantity,
    tgt.report_start_date_time=src.report_start_date_time,
    tgt.report_end_date_time=src.report_end_date_time,
    tgt.report_fetched_and_loaded_at=src.report_fetched_and_loaded_at,
    tgt.scraper_id=src.scraper_id,
    tgt.sc_seller_id=src.sc_seller_id,
    tgt.file_name=src.file_name,
    tgt.etl_batch_run_time=src.etl_batch_run_time,
    tgt.connector_region=src.connector_region,
    tgt.record_updated_timestamp_utc=SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
inventory_pk,
snapshot_date, sku,
fnsku, asin,
brand_code,
marketplace_id,
product_name, country_code, merchant_id,
condition, inbound_quantity,
prime_available_quantity, fc_transfer_quantity,
fc_processing_quantity, customer_order_reserved_quantity, future_supply_buyable_quantity,
reserved_future_supply_quantity,
unsellable_quantity, inventory_aged_0_90_days, inventory_aged_91_180_days,
inventory_aged_181_365_days, inventory_aged_365plus_days, units_shipped_last_7_days, units_shipped_last_30_days,
units_shipped_last_60_days, units_shipped_last_90_days, days_of_supply, sell_through, upstream_storage_available_quantity,
upstream_storage_reserved_quantity, report_start_date_time, report_end_date_time, report_fetched_and_loaded_at,
scraper_id, sc_seller_id, file_name,
etl_batch_run_time, connector_region,
record_created_timestamp_utc,
record_updated_timestamp_utc
)
VALUES
(
src.inventory_pk,
src.snapshot_date,
src.sku, src.fnsku,
src.asin,
src.brand_code,
src.marketplace_id,
src.product_name, src.country_code,
src.merchant_id,
src.condition, src.inbound_quantity,
src.prime_available_quantity, src.fc_transfer_quantity,
src.fc_processing_quantity, src.customer_order_reserved_quantity,
src.future_supply_buyable_quantity,
src.reserved_future_supply_quantity,
src.unsellable_quantity, src.inventory_aged_0_90_days,
src.inventory_aged_91_180_days,
src.inventory_aged_181_365_days, src.inventory_aged_365plus_days,
src.units_shipped_last_7_days,
src.units_shipped_last_30_days,
src.units_shipped_last_60_days,
src.units_shipped_last_90_days, src.days_of_supply,
src.sell_through, src.upstream_storage_available_quantity,
src.upstream_storage_reserved_quantity,
src.report_start_date_time, src.report_end_date_time,
src.report_fetched_and_loaded_at, src.scraper_id, src.sc_seller_id,
src.file_name, src.etl_batch_run_time,
src.connector_region,
SYSDATE(),
SYSDATE()
);

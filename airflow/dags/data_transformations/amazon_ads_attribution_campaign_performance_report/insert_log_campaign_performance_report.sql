CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_campaign_performance_report AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_campaign_performance_report
    WHERE 1 = 0;

INSERT INTO $raw_db.log_campaign_performance_report (
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    accountId,
    accountName,
    adGroupId,
    advertiserName,
    attributedAddToCartClicks14d,
    attributedDetailPageViewsClicks14d,
    attributedPurchases14d,
    attributedSales14d,
    attributedTotalAddToCartClicks14d,
    attributedTotalDetailPageViewsClicks14d,
    attributedTotalPurchases14d,
    brb_bonus_amount,
    campaignId,
    click_throughs,
    countryName,
    date,
    profileId,
    publisher,
    reportDate,
    totalAttributedSales14d,
    totalUnitsSold14d,
    unitsSold14d,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountId,
        accountName,
        adGroupId,
        advertiserName,
        attributedAddToCartClicks14d,
        attributedDetailPageViewsClicks14d,
        attributedPurchases14d,
        attributedSales14d,
        attributedTotalAddToCartClicks14d,
        attributedTotalDetailPageViewsClicks14d,
        attributedTotalPurchases14d,
        brb_bonus_amount,
        campaignId,
        click_throughs,
        countryName,
        date,
        profileId,
        publisher,
        reportDate,
        totalAttributedSales14d,
        totalUnitsSold14d,
        unitsSold14d,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_campaign_performance_report;
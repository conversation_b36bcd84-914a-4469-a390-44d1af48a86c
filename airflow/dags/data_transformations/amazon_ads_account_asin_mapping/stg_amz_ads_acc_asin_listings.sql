create or replace table $stage_db.stg_amz_ads_acc_asin_listings as
SELECT 
    "snapshot_date" as dt,
    "asin" as asin,
    "status" as listing_status,
    "seller_sku" as sku,
    country_code,
    IFF(UPPER(COALESCE(listing_status,'')) = 'ACTIVE',0,1) AS listing_status_rank
FROM $curated_db.FACT_AMAZON_LISTINGS a
where 1=1
    QUALIFY ROW_NUMBER() OVER (PARTITION BY "asin","seller_sku", country_code ORDER BY "snapshot_date" desc nulls last, listing_status_rank) = 1
;
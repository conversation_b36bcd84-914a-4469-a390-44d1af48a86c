CREATE OR REPLACE TRANSIENT TABLE $stage_db.stg_amz_ads_acc_profile_mapping AS 
select
  accountid as account_id,
  accountname as account_name,
  profileid as profile_id,
  country.country_code,
  comb.countryname as country_name,
  SYSDATE() AS record_updated_timestamp_utc,
  SYSDATE() AS record_created_timestamp_utc
from
$stage_db.merge_amz_sp_campaign comb
left join dwh.staging.stg_amazon_ads_country_code_map country
    ON lower(comb.countryname) = lower(country.KEY)
where lower(state) = 'enabled'
qualify row_number() over (partition by accountid,countryname order by fetchdate desc nulls last) = 1
;
CREATE OR REPLACE TRANSIENT TABLE $stage_db.stg_amz_ads_acc_asin_product_ad AS 
select
  accountid as account_id,
  accountname as account_name,
  asin,
  sku,
  country.country_code,
  comb.countryname as country_name
from
$stage_db.merge_amazon_ads_sp_product_adex comb
left join dwh.staging.stg_amazon_ads_country_code_map country
    ON lower(comb.countryname) = lower(country.KEY)
where asin is not null
qualify row_number() over (partition by sku, asin, accountid, countryname order by fetchdate desc nulls last) = 1
;
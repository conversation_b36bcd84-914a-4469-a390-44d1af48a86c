create or replace table $stage_db.stg_amz_ads_acc_asin_join_all as
with comb as (
    select
        pa.account_id,
        pa.account_name,
        pa.asin,
        pa.sku,
        pa.country_code,
        pa.country_name,
        sa.spend_7d,
        pm.parent_asin,
        la.listing_status,
        iff(lower(la.listing_status)='active',1,0) as ls_rn,
        row_number() over (partition by pa.asin, pa.country_code 
                order by sa.spend_7d desc nulls last, ls_rn desc, pa.account_id) as asin_rn,
        sum(ls_rn) 
            over (partition by pm.parent_asin, pa.country_code, pa.account_id) as sum_ls_active_parent,
        sum(coalesce(sa.spend_7d,0)) 
            over (partition by pm.parent_asin, pa.country_code, pa.account_id) as sum_spend7d_parent
    from
        $stage_db.stg_amz_ads_acc_asin_product_ad pa
    left join $stage_db.stg_amz_ads_acc_asin_parent_map pm
    on pa.country_code=pm.country_code
        and pa.asin=pm.asin
    left join $stage_db.stg_amz_ads_acc_asin_spend_agg sa
    on pa.country_code=sa.country_code
        and pa.account_id=sa.account_id
        and pa.asin=sa.asin
        and pa.sku=sa.sku
    left join $stage_db.stg_amz_ads_acc_asin_listings la
    on pa.country_code=la.country_code
        and pa.sku=la.sku
        and pa.asin=la.asin
),

comb_rn as (
    select
    *,
    row_number() over (partition by parent_asin, country_code 
                    order by 
                    sum_spend7d_parent desc nulls last, 
                    sum_ls_active_parent desc
                    )
                    as parent_asin_rn
from
    comb
)

select
    *,
    SYSDATE() AS record_updated_timestamp_utc,
    SYSDATE() AS record_created_timestamp_utc
from
    comb_rn
;
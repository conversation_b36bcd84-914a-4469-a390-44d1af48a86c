CREATE OR REPLACE TRANSIENT TABLE $stage_db.stg_amz_ads_acc_asin_parent_map AS 
    SELECT 
        item_id AS asin
      , country_code
      , IFF(UPPER(COALESCE(listing_status, '')) = 'ACTIVE', 0, 1) AS listing_status_rank
      , FIRST_VALUE(parent_item_id) OVER (
            PARTITION BY asin, country_code
            ORDER BY listing_status_rank
                   , snapshot_date DESC NULLS LAST
                   , ordered_products_sales_amount DESC NULLS LAST) AS parent_asin
    FROM $curated_db.fact_all_item_sales_traffic_report
    WHERE UPPER(item_type) = 'ASIN'
      AND UPPER(parent_item_type) = 'ASIN'
    QUALIFY ROW_NUMBER() OVER (PARTITION BY asin, country_code ORDER BY snapshot_date) = 1;

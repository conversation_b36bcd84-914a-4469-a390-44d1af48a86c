CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_fba_shipping_performance AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(defect_id AS VARCHAR), '')
            )) AS pk,
        surface_timestamp::timestamp as surface_timestamp,
        defect_group,
        CASE WHEN defect_group = 'DAMAGED_PRODUCTS' THEN 'Damaged products'
         WHEN defect_group = 'ENFORCE_ANNOUNCEMENT'THEN 'Enforce announcement'
         WHEN defect_group =    'EXPIRATION_PROBLEMS'THEN 'Expiration-dated product-related problems'
         WHEN defect_group =    'FBA_RESTRCITED_ITEMS'THEN 'FBA restricted product'
         WHEN defect_group =    'FC_PLACEMENT_PROBLEM'THEN 'Shipment placement issues'
         WHEN defect_group =    'GENERIC_EVENT_ITEM'THEN 'Other product related problems'
         WHEN defect_group =    'GENERIC_EVENT_SHIPMENT'THEN 'Other shipment related problems'
         WHEN defect_group =    'INACCURATE_CARTON_COUNT'THEN 'Inaccurate box count'
         WHEN defect_group =    'INACCURATE_QUANTITIES_IN_CARTON'THEN 'Inaccurate product quantities in box'
         WHEN defect_group =    'INACCURATE_QUANTITIES_IN_SHIPMENT'THEN 'Inaccurate product quantities in shipment'
         WHEN defect_group =    'INCORRECT_LABEL_CARTON_RELATED'THEN 'Incorrect label - box related'
         WHEN defect_group =    'INCORRECT_LABEL_ITEM_RELATED'THEN 'Incorrect label - product related'
         WHEN defect_group =    'INCORRECT_LABEL_PALLET_RELATED'THEN 'Incorrect label - pallet related'
         WHEN defect_group =    'INCORRECT_TRACKING_INFORMATION'THEN 'Incorrect tracking information'
         WHEN defect_group =    'LABEL_MISSING_CARTON_RELATED'THEN 'Label missing - box related'
         WHEN defect_group =    'LABEL_MISSING_ITEM_RELATED'THEN 'Label missing - product related'
         WHEN defect_group =    'LEGAL_NONCOMPLIANCE_PROBLEM'THEN 'Legal compliance problem'
         WHEN defect_group =    'NON_PID_CONVEYABLE_CARTON_RELATED'THEN 'Nonstandard box packaging'
         WHEN defect_group =   'NON_SORT_FC_PLACEMENT_PROBLEM'THEN 'Shipment placement issues - Oversize product related'
         WHEN defect_group =    'OTHER_LABEL_PROBLEMS_ITEM_RELATED'THEN 'Other label problems - product related'
         WHEN defect_group =    'PRODUCT_LISTING_PROBLEMS'THEN 'Product listing problems'
         WHEN defect_group =    'SAFETY_DEFECTS_CARTON_RELATED'THEN 'Safety issues - box related'
         WHEN defect_group =    'SAFETY_DEFECTS_ITEM_RELATED'THEN 'Safety issues - product related'
         WHEN defect_group =    'SAFETY_DEFECTS_PALLET_RELATED'THEN 'Safety issues - pallet related'
         WHEN defect_group =    'SHIPMENT_NO_INVOICE_RELATED'THEN 'Shipment issues - Printed invoice (DANFe) not attached to the shipping box'
         WHEN defect_group =    'SHIPMENT_WINDOW'THEN 'On-time arrival'
         WHEN defect_group =   'SORT_FC_PLACEMENT_PROBLEM'THEN 'Shipment placement issues - Standard-size product related'
         WHEN defect_group =   'TRACKING_INFO_NOT_PROVIDED'THEN 'Missing tracking information'
         WHEN defect_group =   'UNEXPECTED_ASINS_IN_SHIPMENT'THEN 'Unexpected products in shipment'
         WHEN defect_group =   'UNEXPECTED_ASIN_IN_CARTON'THEN 'Unexpected product in box'
         WHEN defect_group =   'UNPLAN_PREP_BAGGING'THEN 'Unplanned prep - bagging'
         WHEN defect_group =   'UNPLAN_PREP_BUBBLE_WRAP'THEN 'Unplanned prep - bubble wrap'
         WHEN defect_group =   'UNPLAN_PREP_CARTON_RELATED'THEN 'Unplanned prep - box related'
         WHEN defect_group =   'UNPLAN_PREP_ITEM_RELATED'THEN 'Unplanned prep - product related'
         WHEN defect_group =   'WRONG_DC'THEN 'Misrouted shipment problems'
         ELSE defect_group END as defect_group_alias,
        defect_id,
        coaching_region,
        country_code,
        s.SELLER_CODE,
        merchant_cust_id,
        fba_shipment_id,
        fba_shipment_name,
        carton_id,
        asin,
        fnsku,
        msku,
        apr,
        cpr,
        cpr_measurement_unit,
        defect_type,
        revoke_timestamp::timestamp as revoke_timestamp,
        acknowledgement_type,
        acknowledge_timestamp::timestamp as acknowledge_timestamp,
        measurement_unit,
        problem_quantity,
        defect_status,
        coaching_level,
        andon_event,
        defect_fee,
        quiz_id,
        dispute_status,
        report_start_date_time,
        report_end_date_time,
        report_fetched_and_loaded_at,
        scraper_id,
        scraper_seller_id,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_fba_shipping_performance src
    LEFT JOIN DWH.STAGING.STG_SELLER_INFO s
    on src.merchant_cust_id = s.SELLER_ID
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY report_fetched_and_loaded_at DESC NULLS LAST) = 1
);



CREATE TABLE IF NOT EXISTS $curated_db.fact_fba_shipping_performance AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_fba_shipping_performance
    WHERE 1 = 0;

MERGE INTO
    $curated_db.fact_fba_shipping_performance AS tgt
USING
    $stage_db.dedupe_fba_shipping_performance AS src
    
        ON 1 = 1
       AND src.pk = tgt.pk
       
WHEN MATCHED AND src.report_fetched_and_loaded_at >= tgt.report_fetched_and_loaded_at THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt.defect_id = src.defect_id,
    tgt.coaching_region = src.coaching_region,
    tgt.country_code = src.country_code,
    tgt.SELLER_CODE = src.SELLER_CODE,
    tgt.merchant_cust_id = src.merchant_cust_id,
    tgt.fba_shipment_id = src.fba_shipment_id,
    tgt.fba_shipment_name = src.fba_shipment_name,
    tgt.carton_id = src.carton_id,
    tgt.asin = src.asin,
    tgt.fnsku = src.fnsku,
    tgt.msku = src.msku,
    tgt.defect_group = src.defect_group,
    tgt.defect_group_alias = src.defect_group_alias,
    tgt.apr = src.apr,
    tgt.cpr = src.cpr,
    tgt.cpr_measurement_unit = src.cpr_measurement_unit,
    tgt.defect_type = src.defect_type,
    tgt.surface_timestamp = src.surface_timestamp,
    tgt.revoke_timestamp = src.revoke_timestamp,
    tgt.acknowledgement_type = src.acknowledgement_type,
    tgt.acknowledge_timestamp = src.acknowledge_timestamp,
    tgt.measurement_unit = src.measurement_unit,
    tgt.problem_quantity = src.problem_quantity,
    tgt.defect_status = src.defect_status,
    tgt.coaching_level = src.coaching_level,
    tgt.andon_event = src.andon_event,
    tgt.defect_fee = src.defect_fee,
    tgt.quiz_id = src.quiz_id,
    tgt.dispute_status = src.dispute_status,
    tgt.report_start_date_time = src.report_start_date_time,
    tgt.report_end_date_time = src.report_end_date_time,
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.scraper_id = src.scraper_id,
    tgt.scraper_seller_id = src.scraper_seller_id,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    defect_id,
    coaching_region,
    country_code,
    SELLER_CODE,
    merchant_cust_id,
    fba_shipment_id,
    fba_shipment_name,
    carton_id,
    asin,
    fnsku,
    msku,
    defect_group,
    defect_group_alias,
    apr,
    cpr,
    cpr_measurement_unit,
    defect_type,
    surface_timestamp,
    revoke_timestamp,
    acknowledgement_type,
    acknowledge_timestamp,
    measurement_unit,
    problem_quantity,
    defect_status,
    coaching_level,
    andon_event,
    defect_fee,
    quiz_id,
    dispute_status,
    report_start_date_time,
    report_end_date_time,
    report_fetched_and_loaded_at,
    scraper_id,
    scraper_seller_id,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src.defect_id, 
    src.coaching_region, 
    src.country_code,
    src.SELLER_CODE,
    src.merchant_cust_id,
    src.fba_shipment_id, 
    src.fba_shipment_name, 
    src.carton_id, 
    src.asin, 
    src.fnsku, 
    src.msku, 
    src.defect_group,
    src.defect_group_alias,
    src.apr, 
    src.cpr, 
    src.cpr_measurement_unit, 
    src.defect_type, 
    src.surface_timestamp, 
    src.revoke_timestamp, 
    src.acknowledgement_type, 
    src.acknowledge_timestamp, 
    src.measurement_unit, 
    src.problem_quantity, 
    src.defect_status, 
    src.coaching_level, 
    src.andon_event, 
    src.defect_fee, 
    src.quiz_id, 
    src.dispute_status, 
    src.report_start_date_time, 
    src.report_end_date_time, 
    src.report_fetched_and_loaded_at, 
    src.scraper_id, 
    src.scraper_seller_id, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);

UPDATE $curated_db.fact_fba_shipping_performance tgt
SET dispute_status = 'RESOLVED',
record_created_timestamp_utc = SYSDATE()
FROM (
        select defect_id
        from $curated_db.fact_fba_shipping_performance
        where surface_timestamp::date >= current_date -120
        --and dispute_status ='PROCESSING'
        minus
        select defect_id
        from $stage_db.dedupe_fba_shipping_performance) src
WHERE tgt.surface_timestamp::date >= current_date -120
AND src.defect_id = tgt.defect_id;
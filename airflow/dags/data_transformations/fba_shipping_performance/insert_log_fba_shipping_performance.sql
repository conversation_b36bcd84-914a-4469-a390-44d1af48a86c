CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_fba_shipping_performance AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_fba_shipping_performance
    WHERE 1 = 0;

INSERT INTO $raw_db.log_fba_shipping_performance (
    defect_id,
    coaching_region,
    country_code,
    merchant_cust_id,
    fba_shipment_id,
    fba_shipment_name,
    carton_id,
    asin,
    fnsku,
    msku,
    defect_group,
    apr,
    cpr,
    cpr_measurement_unit,
    defect_type,
    surface_timestamp,
    revoke_timestamp,
    acknowledgement_type,
    acknowledge_timestamp,
    measurement_unit,
    problem_quantity,
    defect_status,
    coaching_level,
    andon_event,
    defect_fee,
    quiz_id,
    dispute_status,
    report_start_date_time,
    report_end_date_time,
    report_fetched_and_loaded_at,
    scraper_id,
    scraper_seller_id,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        defect_id,
        coaching_region,
        country_code,
        merchant_cust_id,
        fba_shipment_id,
        fba_shipment_name,
        carton_id,
        asin,
        fnsku,
        msku,
        defect_group,
        apr,
        cpr,
        cpr_measurement_unit,
        defect_type,
        surface_timestamp,
        revoke_timestamp,
        acknowledgement_type,
        acknowledge_timestamp,
        measurement_unit,
        problem_quantity,
        defect_status,
        coaching_level,
        andon_event,
        defect_fee,
        quiz_id,
        dispute_status,
        report_start_date_time,
        report_end_date_time,
        report_fetched_and_loaded_at,
        scraper_id,
        scraper_seller_id,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_fba_shipping_performance;
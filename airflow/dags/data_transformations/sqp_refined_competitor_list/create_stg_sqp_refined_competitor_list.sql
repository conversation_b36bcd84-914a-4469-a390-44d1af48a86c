CREATE OR REPLACE TABLE $stage_db.stg_sqp_refined_competitor_list AS
WITH search_query_rank AS (
    SELECT DISTINCT
        market_place_id
        , asin
        , seller_id
        , weekly_week_start_date
        , qp_asin_query
        , qp_asin_query_rank
    FROM dwh.staging.merge_asin_view_report
)
, heyday_asins AS (
    SELECT DISTINCT asin
    FROM dwh.staging.merge_asin_view_report
)
, item_mapping_asin_name AS (
    SELECT
        marketplace_id_1_value AS asin,
        marketplace__country AS country_code,
        netsuite_entry__item_name AS asin_name
    FROM dwh.prod.item_mapping
    WHERE LOWER(marketplace_id_1_label) = 'asin'
    QUALIFY ROW_NUMBER() OVER(PARTITION BY marketplace_id_1_value, marketplace__country
        ORDER BY netsuite_entry__item_name NULLS LAST) = 1
)
, detailed_report AS (
    SELECT
        COALESCE(bm.brand_code, im.brand_code) AS brand_code
        , kdr.asin AS heyday_asin
        , COALESCE(aim.netsuite_item_name, im.netsuite_item_name, ima.asin_name) AS heyday_asin_name
        , kdr.seller_id
        , si.seller_name
        , kdr.marketplace_id
        , kdr.country_code
        , kdr.weekly_week_start_date AS snapshot_week
        , kdr.qd_brand AS competitor_brand
        , kdr.qd_product_title AS competitor_product_title
        , kdr.qd_asin AS competitor_asin
        , kdr.qd_price AS competitor_price
        , kdr.qd_click_count AS competitor_click_count
        , qr.qp_asin_query AS asin_query
        , DENSE_RANK() OVER(PARTITION BY kdr.weekly_week_start_date, kdr.asin, kdr.seller_id, kdr.marketplace_id
            ORDER BY qr.qp_asin_query_rank) AS query_rank
    FROM dwh.staging.merge_asins_keyword_details_report AS kdr
    JOIN search_query_rank AS qr
        ON qr.asin = kdr.asin
        AND qr.seller_id = kdr.seller_id
        AND qr.weekly_week_start_date = kdr.weekly_week_start_date
        AND qr.qp_asin_query = kdr.qd_query_metrics_search_query
        AND qr.market_place_id = kdr.marketplace_id
    LEFT JOIN dwh.prod.seller_asin_item_mapping AS im
        ON kdr.asin = im.asin
        AND kdr.seller_id = im.seller_id
        AND kdr.country_code = im.country_code
    LEFT JOIN dwh.staging.stg_seller_info AS si
        ON si.seller_id = kdr.seller_id
    LEFT JOIN dwh.prod.marketplace_asin_brand_mapping bm
        ON kdr.asin = bm.asin
        AND kdr.country_code = bm.country_code
    LEFT JOIN dwh.prod.asin_item_mapping aim
        ON kdr.asin = aim.asin
        AND kdr.country_code = aim.country_code
        AND bm.brand_code = aim.brand_code
    LEFT JOIN item_mapping_asin_name ima
        ON ima.asin = kdr.asin
        AND ima.country_code = kdr.country_code
    WHERE LOWER(kdr.qd_query_metrics_search_query) NOT ILIKE concat('%',qd_brand, '%')
        AND LOWER(kdr.qd_query_metrics_search_query) NOT LIKE '%tree%'
        AND LOWER(kdr.qd_query_metrics_search_query) NOT LIKE '%boka%'
        AND LOWER(kdr.qd_query_metrics_search_query) NOT LIKE '%cave%'
        AND LOWER(kdr.qd_query_metrics_search_query) NOT LIKE '%cast%'
        AND LOWER(kdr.qd_query_metrics_search_query) NOT LIKE '%hatha%'
        AND LOWER(kdr.qd_query_metrics_search_query) NOT LIKE '%iron%'
        AND LOWER(kdr.qd_query_metrics_search_query) NOT LIKE '%kolua%'
        AND LOWER(kdr.qd_query_metrics_search_query) NOT LIKE '%lee%'
        AND LOWER(kdr.qd_query_metrics_search_query) NOT LIKE '%num%'
        AND LOWER(kdr.qd_query_metrics_search_query) NOT LIKE '%surviveware%'
        AND LOWER(kdr.qd_query_metrics_search_query) NOT LIKE '%swiss%'
        AND LOWER(kdr.qd_query_metrics_search_query) NOT LIKE '%zit%'
        AND kdr.qd_asin NOT IN (SELECT asin FROM heyday_asins)
)
SELECT
    brand_code
    , heyday_asin
    , heyday_asin_name
    , seller_id
    , seller_name
    , marketplace_id
    , country_code
    , snapshot_week
    , competitor_brand
    , competitor_product_title
    , competitor_asin
    , competitor_price
    , competitor_click_count
    , asin_query
    , query_rank
    ,'DAG: sqp_refined_competitor_list' AS created_by
    ,'DAG: sqp_refined_competitor_list' AS updated_by
    ,SYSDATE() AS record_created_timestamp_utc
    ,SYSDATE() AS record_updated_timestamp_utc
FROM detailed_report
WHERE query_rank <= 5;

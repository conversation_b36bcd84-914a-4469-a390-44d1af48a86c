BEGIN TRANSACTION;

DELETE FROM $curated_db.sqp_refined_competitor_list;

INSERT INTO $curated_db.sqp_refined_competitor_list
WITH comp_list_brand_unique AS (
    SELECT
        brand_code
        , heyday_asin
        , heyday_asin_name
        , country_code
        , snapshot_week
        , competitor_brand
        , competitor_asin
        , competitor_product_title
        , competitor_price
        , competitor_click_count
        , asin_query
        , query_rank
    FROM $stage_db.stg_sqp_refined_competitor_list
    QUALIFY ROW_NUMBER() OVER(PARTITION BY heyday_asin, country_code, snapshot_week, competitor_asin, asin_query,
        query_rank ORDER BY brand_code NULLS LAST, heyday_asin_name NULLS LAST, competitor_brand NULLS LAST) = 1
)
SELECT
    brand_code
    , heyday_asin
    , heyday_asin_name
    , country_code
    , snapshot_week
    , competitor_brand
    , competitor_asin
    , competitor_product_title
    , AVG(competitor_price) AS competitor_price
    , SUM(competitor_click_count) AS competitor_click_count
    , 'DAG: sqp_refined_competitor_list' AS created_by
    , 'DAG: sqp_refined_competitor_list' AS updated_by
    , SYSDATE() AS record_created_timestamp_utc
    , SYSDATE() AS record_updated_timestamp_utc
FROM comp_list_brand_unique
GROUP BY 1,2,3,4,5,6,7,8;

COMMIT;
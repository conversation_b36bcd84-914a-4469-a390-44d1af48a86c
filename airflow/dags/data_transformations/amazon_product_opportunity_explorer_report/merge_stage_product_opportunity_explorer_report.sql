CREATE TABLE IF NOT EXISTS $stage_db.merge_product_opportunity_explorer_report AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_product_opportunity_explorer_report
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_product_opportunity_explorer_report AS tgt
USING
    $stage_db.dedupe_product_opportunity_explorer_report AS src

        ON 1 = 1
       AND src.pk = tgt.pk

WHEN MATCHED AND src.report_fetched_and_loaded_at >= tgt.report_fetched_and_loaded_at THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt.asin = src.asin,
    tgt.marketplace_id = src.marketplace_id,
    tgt.currency_code = src.currency_code,
    tgt.scraper_id = src.scraper_id,
    tgt.country_code = src.country_code,
    tgt.topic_type = src.topic_type,
    tgt.topic = src.topic,
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.niche_id = src.niche_id,
    tgt.niche_click_count = src.niche_click_count,
    tgt.niche_click_share_T90 = src.niche_click_share_T90,
    tgt.niche_click_count_percentage_T90 = src.niche_click_count_percentage_T90,
    tgt.niche_id_search_volume_growth_T90 = src.niche_id_search_volume_growth_T90,
    tgt.niche_title = src.niche_title,
    tgt.niche_search_volume_T90 = src.niche_search_volume_T90,
    tgt.niche_search_volume_growth_T90 = src.niche_search_volume_growth_T90,
    tgt.asin_positive_review_no_of_mentions = src.asin_positive_review_no_of_mentions,
    tgt.asin_positive_review_verbatim1 = src.asin_positive_review_verbatim1,
    tgt.asin_positive_review_verbatim2 = src.asin_positive_review_verbatim2,
    tgt.asin_positive_review_verbatim3 = src.asin_positive_review_verbatim3,
    tgt.asin_negative_review_no_of_mentions = src.asin_negative_review_no_of_mentions,
    tgt.asin_negative_review_verbatim1 = src.asin_negative_review_verbatim1,
    tgt.asin_negative_review_verbatim2 = src.asin_negative_review_verbatim2,
    tgt.asin_negative_review_verbatim3 = src.asin_negative_review_verbatim3,
    tgt.asin_star_rating_impact = src.asin_star_rating_impact,
    tgt.niche_positive_review_percent_of_mentions = src.niche_positive_review_percent_of_mentions,
    tgt.niche_positive_review_verbatim1 = src.niche_positive_review_verbatim1,
    tgt.niche_positive_review_verbatim2 = src.niche_positive_review_verbatim2,
    tgt.niche_positive_review_verbatim3 = src.niche_positive_review_verbatim3,
    tgt.niche_negative_review_percent_of_mentions = src.niche_negative_review_percent_of_mentions,
    tgt.niche_negative_review_verbatim1 = src.niche_negative_review_verbatim1,
    tgt.niche_negative_review_verbatim2 = src.niche_negative_review_verbatim2,
    tgt.niche_negative_review_verbatim3 = src.niche_negative_review_verbatim3,
    tgt.niche_star_rating_impact_top_25_percent_products = src.niche_star_rating_impact_top_25_percent_products,
    tgt.niche_star_rating_impact_all_products = src.niche_star_rating_impact_all_products,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    asin,
    marketplace_id,
    currency_code,
    scraper_id,
    country_code,
    topic_type,
    topic,
    report_fetched_and_loaded_at,
    niche_id,
    niche_click_count,
    niche_click_share_T90,
    niche_click_count_percentage_T90,
    niche_id_search_volume_growth_T90,
    niche_title,
    niche_search_volume_T90,
    niche_search_volume_growth_T90,
    asin_positive_review_no_of_mentions,
    asin_positive_review_verbatim1,
    asin_positive_review_verbatim2,
    asin_positive_review_verbatim3,
    asin_negative_review_no_of_mentions,
    asin_negative_review_verbatim1,
    asin_negative_review_verbatim2,
    asin_negative_review_verbatim3,
    asin_star_rating_impact,
    niche_positive_review_percent_of_mentions,
    niche_positive_review_verbatim1,
    niche_positive_review_verbatim2,
    niche_positive_review_verbatim3,
    niche_negative_review_percent_of_mentions,
    niche_negative_review_verbatim1,
    niche_negative_review_verbatim2,
    niche_negative_review_verbatim3,
    niche_star_rating_impact_top_25_percent_products,
    niche_star_rating_impact_all_products,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src.asin, 
    src.marketplace_id, 
    src.currency_code, 
    src.scraper_id, 
    src.country_code, 
    src.topic_type, 
    src.topic, 
    src.report_fetched_and_loaded_at, 
    src.niche_id, 
    src.niche_click_count, 
    src.niche_click_share_T90, 
    src.niche_click_count_percentage_T90, 
    src.niche_id_search_volume_growth_T90, 
    src.niche_title, 
    src.niche_search_volume_T90, 
    src.niche_search_volume_growth_T90, 
    src.asin_positive_review_no_of_mentions, 
    src.asin_positive_review_verbatim1, 
    src.asin_positive_review_verbatim2, 
    src.asin_positive_review_verbatim3, 
    src.asin_negative_review_no_of_mentions, 
    src.asin_negative_review_verbatim1, 
    src.asin_negative_review_verbatim2, 
    src.asin_negative_review_verbatim3, 
    src.asin_star_rating_impact, 
    src.niche_positive_review_percent_of_mentions, 
    src.niche_positive_review_verbatim1, 
    src.niche_positive_review_verbatim2, 
    src.niche_positive_review_verbatim3, 
    src.niche_negative_review_percent_of_mentions, 
    src.niche_negative_review_verbatim1, 
    src.niche_negative_review_verbatim2, 
    src.niche_negative_review_verbatim3, 
    src.niche_star_rating_impact_top_25_percent_products, 
    src.niche_star_rating_impact_all_products, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
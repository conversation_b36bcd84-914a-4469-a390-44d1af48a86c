CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_product_opportunity_explorer_report AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_product_opportunity_explorer_report
    WHERE 1 = 0;

INSERT INTO $raw_db.log_product_opportunity_explorer_report (
    asin,
    marketplace_id,
    currency_code,
    scraper_id,
    country_code,
    topic_type,
    topic,
    report_fetched_and_loaded_at,
    niche_id,
    niche_click_count,
    niche_click_share_T90,
    niche_click_count_percentage_T90,
    niche_id_search_volume_growth_T90,
    niche_title,
    niche_search_volume_T90,
    niche_search_volume_growth_T90,
    asin_positive_review_no_of_mentions,
    asin_positive_review_verbatim1,
    asin_positive_review_verbatim2,
    asin_positive_review_verbatim3,
    asin_negative_review_no_of_mentions,
    asin_negative_review_verbatim1,
    asin_negative_review_verbatim2,
    asin_negative_review_verbatim3,
    asin_star_rating_impact,
    niche_positive_review_percent_of_mentions,
    niche_positive_review_verbatim1,
    niche_positive_review_verbatim2,
    niche_positive_review_verbatim3,
    niche_negative_review_percent_of_mentions,
    niche_negative_review_verbatim1,
    niche_negative_review_verbatim2,
    niche_negative_review_verbatim3,
    niche_star_rating_impact_top_25_percent_products,
    niche_star_rating_impact_all_products,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        asin,
        marketplace_id,
        currency_code,
        scraper_id,
        country_code,
        topic_type,
        topic,
        report_fetched_and_loaded_at,
        niche_id,
        niche_click_count,
        niche_click_share_T90,
        niche_click_count_percentage_T90,
        niche_id_search_volume_growth_T90,
        niche_title,
        niche_search_volume_T90,
        niche_search_volume_growth_T90,
        asin_positive_review_no_of_mentions,
        asin_positive_review_verbatim1,
        asin_positive_review_verbatim2,
        asin_positive_review_verbatim3,
        asin_negative_review_no_of_mentions,
        asin_negative_review_verbatim1,
        asin_negative_review_verbatim2,
        asin_negative_review_verbatim3,
        asin_star_rating_impact,
        niche_positive_review_percent_of_mentions,
        niche_positive_review_verbatim1,
        niche_positive_review_verbatim2,
        niche_positive_review_verbatim3,
        niche_negative_review_percent_of_mentions,
        niche_negative_review_verbatim1,
        niche_negative_review_verbatim2,
        niche_negative_review_verbatim3,
        niche_star_rating_impact_top_25_percent_products,
        niche_star_rating_impact_all_products,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_product_opportunity_explorer_report;
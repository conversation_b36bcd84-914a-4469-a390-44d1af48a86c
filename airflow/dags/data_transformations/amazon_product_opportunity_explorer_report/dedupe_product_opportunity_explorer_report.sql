CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_product_opportunity_explorer_report AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(asin AS VARCHAR), ''), '-',
            COALESCE(CAST(marketplace_id AS VARCHAR), ''), '-',
            COALESCE(CAST(topic_type AS VARCHAR), ''), '-',
            COALESCE(CAST(topic AS VARCHAR), '')
            )) AS pk,
        asin,
        marketplace_id,
        currency_code,
        scraper_id,
        country_code,
        topic_type,
        topic,
        report_fetched_and_loaded_at,
        niche_id,
        TRY_TO_NUMBER(niche_click_count) AS niche_click_count,
        TRY_TO_DECIMAL(niche_click_share_T90, 18, 15) AS niche_click_share_T90,
        TRY_TO_DECIMAL(niche_click_count_percentage_T90, 18, 15) AS niche_click_count_percentage_T90,
        TRY_TO_DECIMAL(niche_id_search_volume_growth_T90, 18, 15) AS niche_id_search_volume_growth_T90,
        niche_title,
        TRY_TO_NUMBER(niche_search_volume_T90) AS niche_search_volume_T90,
        TRY_TO_DECIMAL(niche_search_volume_growth_T90, 18, 15) AS niche_search_volume_growth_T90,
        TRY_TO_NUMBER(asin_positive_review_no_of_mentions) AS asin_positive_review_no_of_mentions,
        asin_positive_review_verbatim1,
        asin_positive_review_verbatim2,
        asin_positive_review_verbatim3,
        TRY_TO_NUMBER(asin_negative_review_no_of_mentions) AS asin_negative_review_no_of_mentions,
        asin_negative_review_verbatim1,
        asin_negative_review_verbatim2,
        asin_negative_review_verbatim3,
        TRY_TO_DECIMAL(asin_star_rating_impact, 10, 2) AS asin_star_rating_impact,
        TRY_TO_DECIMAL(niche_positive_review_percent_of_mentions, 10, 2) AS niche_positive_review_percent_of_mentions,
        niche_positive_review_verbatim1,
        niche_positive_review_verbatim2,
        niche_positive_review_verbatim3,
        TRY_TO_DECIMAL(niche_negative_review_percent_of_mentions, 10, 2) AS niche_negative_review_percent_of_mentions,
        niche_negative_review_verbatim1,
        niche_negative_review_verbatim2,
        niche_negative_review_verbatim3,
        TRY_TO_DECIMAL(niche_star_rating_impact_top_25_percent_products, 10, 2) AS niche_star_rating_impact_top_25_percent_products,
        TRY_TO_DECIMAL(niche_star_rating_impact_all_products, 10, 2) AS niche_star_rating_impact_all_products,
        file_name,
        etl_batch_run_time

    FROM $raw_db.raw_product_opportunity_explorer_report
    WHERE 1=1
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY asin, marketplace_id, topic_type, topic
        ORDER BY report_fetched_and_loaded_at DESC NULLS LAST) = 1
);
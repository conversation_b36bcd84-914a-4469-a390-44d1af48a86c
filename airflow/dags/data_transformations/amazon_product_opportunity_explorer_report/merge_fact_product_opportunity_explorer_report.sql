MERGE INTO
    $curated_db.fact_product_opportunity_explorer_report AS tgt
USING (
    SELECT 
          f.pk
        , f.asin
        , f.currency_code
        , f.country_code
        , b.brand_code
        , f.topic_type
        , f.topic
        , f.report_fetched_and_loaded_at
        , f.niche_id
        , f.niche_click_count
        , f.niche_click_share_T90
        , f.niche_click_count_percentage_T90
        , f.niche_id_search_volume_growth_T90
        , f.niche_title
        , f.niche_search_volume_T90
        , f.niche_search_volume_growth_T90
        , f.asin_positive_review_no_of_mentions
        , f.asin_positive_review_verbatim1
        , f.asin_positive_review_verbatim2
        , f.asin_positive_review_verbatim3
        , f.asin_negative_review_no_of_mentions
        , f.asin_negative_review_verbatim1
        , f.asin_negative_review_verbatim2
        , f.asin_negative_review_verbatim3
        , f.asin_star_rating_impact
        , f.niche_positive_review_percent_of_mentions
        , f.niche_positive_review_verbatim1
        , f.niche_positive_review_verbatim2
        , f.niche_positive_review_verbatim3
        , f.niche_negative_review_percent_of_mentions
        , f.niche_negative_review_verbatim1
        , f.niche_negative_review_verbatim2
        , f.niche_negative_review_verbatim3
        , f.niche_star_rating_impact_top_25_percent_products
        , f.niche_star_rating_impact_all_products
        , f.file_name
        , '$stage_db.merge_product_opportunity_explorer_report' AS data_source
        , 'amazon_product_opportunity_explorer_report dag' AS created_by
        , 'amazon_product_opportunity_explorer_report dag' AS updated_by  
FROM  $stage_db.merge_product_opportunity_explorer_report f
LEFT JOIN dwh.prod.marketplace_asin_brand_mapping b 
    ON  f.asin = b.asin 
    AND f.country_code = b.country_code
WHERE f.record_updated_timestamp_utc > TO_TIMESTAMP_NTZ('$start_ts') 
) src
  ON src.pk = tgt.pk 
WHEN MATCHED THEN 
UPDATE SET
      tgt.asin = src.asin
    , tgt.currency_code = src.currency_code
    , tgt.country_code = src.country_code
    , tgt.topic_type = src.topic_type
    , tgt.topic = src.topic
    , tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at
    , tgt.niche_id = src.niche_id
    , tgt.niche_click_count = src.niche_click_count
    , tgt.niche_click_share_T90 = src.niche_click_share_T90
    , tgt.niche_click_count_percentage_T90 = src.niche_click_count_percentage_T90
    , tgt.niche_id_search_volume_growth_T90 = src.niche_id_search_volume_growth_T90
    , tgt.niche_title = src.niche_title
    , tgt.niche_search_volume_T90 = src.niche_search_volume_T90
    , tgt.niche_search_volume_growth_T90 = src.niche_search_volume_growth_T90
    , tgt.asin_positive_review_no_of_mentions = src.asin_positive_review_no_of_mentions
    , tgt.asin_positive_review_verbatim1 = src.asin_positive_review_verbatim1
    , tgt.asin_positive_review_verbatim2 = src.asin_positive_review_verbatim2
    , tgt.asin_positive_review_verbatim3 = src.asin_positive_review_verbatim3
    , tgt.asin_negative_review_no_of_mentions = src.asin_negative_review_no_of_mentions
    , tgt.asin_negative_review_verbatim1 = src.asin_negative_review_verbatim1
    , tgt.asin_negative_review_verbatim2 = src.asin_negative_review_verbatim2
    , tgt.asin_negative_review_verbatim3 = src.asin_negative_review_verbatim3
    , tgt.asin_star_rating_impact = src.asin_star_rating_impact
    , tgt.niche_positive_review_percent_of_mentions = src.niche_positive_review_percent_of_mentions
    , tgt.niche_positive_review_verbatim1 = src.niche_positive_review_verbatim1
    , tgt.niche_positive_review_verbatim2 = src.niche_positive_review_verbatim2
    , tgt.niche_positive_review_verbatim3 = src.niche_positive_review_verbatim3
    , tgt.niche_negative_review_percent_of_mentions = src.niche_negative_review_percent_of_mentions
    , tgt.niche_negative_review_verbatim1 = src.niche_negative_review_verbatim1
    , tgt.niche_negative_review_verbatim2 = src.niche_negative_review_verbatim2
    , tgt.niche_negative_review_verbatim3 = src.niche_negative_review_verbatim3
    , tgt.niche_star_rating_impact_top_25_percent_products = src.niche_star_rating_impact_top_25_percent_products
    , tgt.niche_star_rating_impact_all_products = src.niche_star_rating_impact_all_products
    , tgt.file_name = src.file_name
    , tgt.data_source = src.data_source
    , tgt.updated_by = src.updated_by
    , tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT 
(
      pk
    , asin
    , currency_code
    , country_code
    , brand_code
    , topic_type
    , topic
    , report_fetched_and_loaded_at
    , niche_id
    , niche_click_count
    , niche_click_share_T90
    , niche_click_count_percentage_T90
    , niche_id_search_volume_growth_T90
    , niche_title
    , niche_search_volume_T90
    , niche_search_volume_growth_T90
    , asin_positive_review_no_of_mentions
    , asin_positive_review_verbatim1
    , asin_positive_review_verbatim2
    , asin_positive_review_verbatim3
    , asin_negative_review_no_of_mentions
    , asin_negative_review_verbatim1
    , asin_negative_review_verbatim2
    , asin_negative_review_verbatim3
    , asin_star_rating_impact
    , niche_positive_review_percent_of_mentions
    , niche_positive_review_verbatim1
    , niche_positive_review_verbatim2
    , niche_positive_review_verbatim3
    , niche_negative_review_percent_of_mentions
    , niche_negative_review_verbatim1
    , niche_negative_review_verbatim2
    , niche_negative_review_verbatim3
    , niche_star_rating_impact_top_25_percent_products
    , niche_star_rating_impact_all_products
    , file_name
    , data_source
    , created_by
    , updated_by
    , record_created_timestamp_utc
    , record_updated_timestamp_utc  
)
VALUES 
(
      src.pk
    , src.asin
    , src.currency_code
    , src.country_code
    , src.brand_code
    , src.topic_type
    , src.topic
    , src.report_fetched_and_loaded_at
    , src.niche_id
    , src.niche_click_count
    , src.niche_click_share_T90
    , src.niche_click_count_percentage_T90
    , src.niche_id_search_volume_growth_T90
    , src.niche_title
    , src.niche_search_volume_T90
    , src.niche_search_volume_growth_T90
    , src.asin_positive_review_no_of_mentions
    , src.asin_positive_review_verbatim1
    , src.asin_positive_review_verbatim2
    , src.asin_positive_review_verbatim3
    , src.asin_negative_review_no_of_mentions
    , src.asin_negative_review_verbatim1
    , src.asin_negative_review_verbatim2
    , src.asin_negative_review_verbatim3
    , src.asin_star_rating_impact
    , src.niche_positive_review_percent_of_mentions
    , src.niche_positive_review_verbatim1
    , src.niche_positive_review_verbatim2
    , src.niche_positive_review_verbatim3
    , src.niche_negative_review_percent_of_mentions
    , src.niche_negative_review_verbatim1
    , src.niche_negative_review_verbatim2
    , src.niche_negative_review_verbatim3
    , src.niche_star_rating_impact_top_25_percent_products
    , src.niche_star_rating_impact_all_products
    , src.file_name
    , src.data_source
    , src.created_by
    , src.updated_by
    , SYSDATE()
    , SYSDATE() 
);

UPDATE $curated_db.fact_product_opportunity_explorer_report f
SET brand_code = b.brand_code
FROM dwh.prod.marketplace_asin_brand_mapping b 
WHERE   f.asin = b.asin 
	AND f.country_code = b.country_code
	AND f.brand_code IS NULL 
	AND b.brand_code IS NOT NULL;

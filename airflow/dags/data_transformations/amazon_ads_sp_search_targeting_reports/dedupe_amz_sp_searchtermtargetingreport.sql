
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amz_sp_searchtermtargetingreport AS (
    SELECT
        
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(reportdate AS VARCHAR), ''), '-',
        COALESCE(CAST(accountid AS VARCHAR), ''), '-',
        COALESCE(CAST(campaignid AS VARCHAR), ''), '-',
        COALESCE(CAST(adgroupid AS VARCHAR), ''), '-',
        COALESCE(CAST(coalesce(keywordid,targetid) AS VARCHAR), ''), '-',
        COALESCE(CAST(query AS VARCHAR), '')
        )) AS pk,
        
        requesttime,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountid,
        accountname,
        adgroupid,
        adgroupname,
        api,
        attributedconversions14d,
        attributedconversions14dsamesku,
        attributedconversions1d,
        attributedconversions1dsamesku,
        attributedconversions30d,
        attributedconversions30dsamesku,
        attributedconversions7d,
        attributedconversions7dsamesku,
        attributedsales14d,
        attributedsales14dsamesku,
        attributedsales1d,
        attributedsales1dsamesku,
        attributedsales30d,
        attributedsales30dsamesku,
        attributedsales7d,
        attributedsales7dsamesku,
        attributedunitsordered14d,
        attributedunitsordered14dsamesku,
        attributedunitsordered1d,
        attributedunitsordered1dsamesku,
        attributedunitsordered30d,
        attributedunitsordered30dsamesku,
        attributedunitsordered7d,
        attributedunitsordered7dsamesku,
        campaignid,
        campaignname,
        clicks,
        cost,
        countryname,
        impressions,
        keywordid,
        keywordtext,
        matchtype,
        profileid,
        query,
        reportdate,
        targetid,
        targeting,
        targetingexpression,
        targetingtext,
        targetingtype,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_amz_sp_searchtermtargetingreport
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY reportdate, accountid, campaignid, adgroupid, coalesce(keywordid,targetid), query
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);


CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amz_sp_targetingclause AS (
    SELECT
        
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(fetchdate AS VARCHAR), ''), '-',
        COALESCE(CAST(accountid AS VARCHAR), ''), '-',
        COALESCE(CAST(campaignid AS VARCHAR), ''), '-',
        COALESCE(CAST(targetid AS VARCHAR), '')
        )) AS pk,
        
        requesttime,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountid,
        accountname,
        adgroupid,
        bid,
        campaignid,
        countryname,
        expression,
        expressiontype,
        fetchdate,
        profileid,
        resolvedexpression,
        state,
        targetid,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_amz_sp_targetingclause
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY fetchdate, accountid, campaignid, targetid
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);

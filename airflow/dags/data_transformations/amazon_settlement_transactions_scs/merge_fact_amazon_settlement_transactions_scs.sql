CREATE TABLE IF NOT EXISTS $curated_db.fact_amazon_settlement_transactions_scs (
    settlement_transaction_pk VARCHAR(32),
    report_date TIMESTAMP,
    report_date_pst TIMESTAMP,
    report_date_utc TIMESTAMP,
    report_date_utc_offset VARCHAR,
    settlement_id VARCHAR,
    seller_id VARCHAR,
    country_code VARCHAR,
    type VA<PERSON><PERSON><PERSON>,
    order_id VARCHAR,
    sku VARCHAR,
    description VARCHAR,
    marketplace VARCHAR,
    account_type VARCHAR,
    fulfillment_channel VARCHAR,
    order_city VARCHAR,
    order_state VARCHAR,
    order_postal VARCHAR,
    tax_collection_model VARCHAR,
    quantity NUMBER(38,0),
    product_sales FLOAT,
    product_sales_tax FLOAT,
    shipping_credits FLOAT,
    shipping_credits_tax FLOAT,
    gift_wrap_credits FLOAT,
    giftwrap_credits_tax FLOAT,
    regulatory_fee FLOAT,
    tax_on_regulatory_fee FLOAT,
    promotional_rebates FLOAT,
    promotional_rebates_tax FLOAT,
    marketplace_withheld_tax FLOAT,
    selling_fees FLOAT,
    fba_fees FLOAT,
    other_transaction_fees FLOAT,
    other FLOAT,
    total FLOAT,
    report_fetched_and_loaded_at TIMESTAMP,
    seller_code VARCHAR,
    data_source VARCHAR
);


MERGE INTO $curated_db.fact_amazon_settlement_transactions_scs t
USING (
    SELECT
        st.*
        , si.seller_code
        , 'SCS' AS data_source
    FROM $stage_db.amazon_settlements_transactions st
    LEFT JOIN dwh.staging.stg_seller_info si
        ON st."seller_id" = si.seller_id
    UNION ALL
    SELECT
        st.*
        , si.seller_code
        , 'GCS - Airbyte' AS data_source
    FROM $stage_db.amazon_settlement_transactions_branded st
    LEFT JOIN dwh.staging.stg_seller_info si
        ON st.seller_id = si.seller_id
    WHERE NOT EXISTS (
            SELECT 1
            FROM $stage_db.amazon_settlements_transactions f
            WHERE f."report_date" = st.report_date
                AND f."settlement_id" = st.settlement_id
                AND NVL(f."order_id", '') = NVL(st.order_id, '')
                AND NVL(f."sku", '') = NVL(st.sku, '')
                AND NVL(f."type", '') = NVL(st."TYPE", '')
                AND TRIM(NVL(f."tax_collection_model", '')) = TRIM(NVL(st.tax_collection_model, ''))
                AND NVL(f."description", '') = NVL(st.description, '')
        )
) s
    ON t.settlement_transaction_pk = s."settlement_transaction_pk"
WHEN MATCHED THEN UPDATE SET
    t.quantity = s."quantity"
    , t.product_sales = s."product sales"
    , t.product_sales_tax = s."product sales tax"
    , t.shipping_credits = s."shipping credits"
    , t.shipping_credits_tax = s."shipping credits tax"
    , t.gift_wrap_credits = s."gift wrap credits"
    , t.giftwrap_credits_tax = s."giftwrap credits tax"
    , t.regulatory_fee = s."regulatory fee"
    , t.tax_on_regulatory_fee = s."tax on regulatory fee"
    , t.promotional_rebates = s."promotional rebates"
    , t.promotional_rebates_tax = s."promotional rebates tax"
    , t.marketplace_withheld_tax = s."marketplace withheld tax"
    , t.selling_fees = s."selling fees"
    , t.fba_fees = s."fba fees"
    , t.other_transaction_fees = s."other transaction fees"
    , t.other = s."other"
    , t.total = s."total"
    , t.report_fetched_and_loaded_at = s."report_fetched_and_loaded_at"
    , t.seller_code = s.seller_code
    , t.data_source = s.data_source
WHEN NOT MATCHED THEN INSERT
(
    settlement_transaction_pk
    , report_date
    , report_date_pst
    , report_date_utc
    , report_date_utc_offset
    , settlement_id
    , seller_id
    , country_code
    , type
    , order_id
    , sku
    , description
    , marketplace
    , account_type
    , fulfillment_channel
    , order_city
    , order_state
    , order_postal
    , tax_collection_model
    , quantity
    , product_sales
    , product_sales_tax
    , shipping_credits
    , shipping_credits_tax
    , gift_wrap_credits
    , giftwrap_credits_tax
    , regulatory_fee
    , tax_on_regulatory_fee
    , promotional_rebates
    , promotional_rebates_tax
    , marketplace_withheld_tax
    , selling_fees
    , fba_fees
    , other_transaction_fees
    , other
    , total
    , report_fetched_and_loaded_at
    , seller_code
    , data_source
)
VALUES (
    s."settlement_transaction_pk"
    , s."report_date"
    , s."report_date_pst"
    , s."report_date_utc"
    , s."report_date_utc_offset"
    , s."settlement_id"
    , s."seller_id"
    , s."country_code"
    , s."type"
    , s."order_id"
    , s."sku"
    , s."description"
    , s."marketplace"
    , s."account_type"
    , s."fulfillment_channel"
    , s."order_city"
    , s."order_state"
    , s."order_postal"
    , s."tax_collection_model"
    , s."quantity"
    , s."product sales"
    , s."product sales tax"
    , s."shipping credits"
    , s."shipping credits tax"
    , s."gift wrap credits"
    , s."giftwrap credits tax"
    , s."regulatory fee"
    , s."tax on regulatory fee"
    , s."promotional rebates"
    , s."promotional rebates tax"
    , s."marketplace withheld tax"
    , s."selling fees"
    , s."fba fees"
    , s."other transaction fees"
    , s."other"
    , s."total"
    , s."report_fetched_and_loaded_at"
    , s.seller_code
    , s.data_source
);

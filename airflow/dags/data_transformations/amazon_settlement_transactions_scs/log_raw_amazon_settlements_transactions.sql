CREATE TABLE IF NOT EXISTS $raw_db.log_raw_amazon_settlements_transactions (
    snapshot_date TIMESTAMP
    , settlement_id VARCHAR
    , type VARCHAR
    , order_id VARCHAR
    , sku VARCHAR
    , description VARCHAR
    , quantity NUMBER
    , marketplace VARCHAR
    , account_type VARCHAR
    , fulfillment VARCHAR
    , order_city VARCHAR
    , order_state VARCHAR
    , order_postal VARCHAR
    , tax_collection_model VARCHAR
    , product_sales FLOAT
    , product_sales_tax FLOAT
    , shipping_credits FLOAT
    , shipping_credits_tax FLOAT
    , gift_wrap_credits FLOAT
    , giftwrap_credits_tax FLOAT
    , regulatory_fee FLOAT
    , tax_on_regulatory_fee FLOAT
    , promotional_rebates FLOAT
    , promotional_rebates_tax FLOAT
    , marketplace_withheld_tax FLOAT
    , selling_fees FLOAT
    , fba_fees FLOAT
    , other_transaction_fees FLOAT
    , other FLOAT
    , total FLOAT
    , country VARCHAR
    , scraper_id VARCHAR
    , report_fetched_and_loaded_at TIMESTAMP
    , seller_id VARCHAR
    , file_name VARCHAR
    , etl_batch_run_time TIMESTAMP
    , log_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);

DELETE FROM $raw_db.log_raw_amazon_settlements_transactions
WHERE file_name IN (
    SELECT DISTINCT
        file_name
    FROM $raw_db.amazon_settlements_transactions_stg
);


INSERT INTO $raw_db.log_raw_amazon_settlements_transactions
SELECT
    TO_TIMESTAMP(snapshot_date) AS snapshot_date
    , TO_VARCHAR(settlement_id) AS settlement_id
    , type
    , order_id
    , sku
    , description
    , TO_NUMBER(NULLIF(quantity, '')) AS quantity
    , marketplace
    , account_type
    , fulfillment
    , order_city
    , order_state
    , order_postal
    , tax_collection_model
    , TO_DOUBLE(REPLACE(product_sales, ',', '')) AS product_sales
    , TO_DOUBLE(REPLACE(product_sales_tax, ',', '')) AS product_sales_tax
    , TO_DOUBLE(REPLACE(shipping_credits, ',', '')) AS shipping_credits
    , TO_DOUBLE(REPLACE(shipping_credits_tax, ',', '')) AS shipping_credits_tax
    , TO_DOUBLE(REPLACE(gift_wrap_credits, ',', '')) AS gift_wrap_credits
    , TO_DOUBLE(REPLACE(giftwrap_credits_tax, ',', '')) AS giftwrap_credits_tax
    , TO_DOUBLE(REPLACE(regulatory_fee, ',', '')) AS regulatory_fee
    , TO_DOUBLE(REPLACE(tax_on_regulatory_fee, ',', '')) AS tax_on_regulatory_fee
    , TO_DOUBLE(REPLACE(promotional_rebates, ',', '')) AS promotional_rebates
    , TO_DOUBLE(REPLACE(promotional_rebates_tax, ',', '')) AS promotional_rebates_tax
    , TO_DOUBLE(REPLACE(marketplace_withheld_tax, ',', '')) AS marketplace_withheld_tax
    , TO_DOUBLE(REPLACE(selling_fees, ',', '')) AS selling_fees
    , TO_DOUBLE(REPLACE(fba_fees, ',', '')) AS fba_fees
    , TO_DOUBLE(REPLACE(other_transaction_fees, ',', '')) AS other_transaction_fees
    , TO_DOUBLE(REPLACE(other, ',', '')) AS other
    , TO_DOUBLE(REPLACE(total, ',', '')) AS total
    , country
    , scraper_id
    , report_fetched_and_loaded_at
    , seller_id
    , file_name
    , etl_batch_run_time
    , SYSDATE()
FROM $raw_db.amazon_settlements_transactions_stg;


DELETE FROM $raw_db.log_raw_amazon_settlements_transactions
WHERE log_timestamp_utc <= DATEADD('day', -90, SYSDATE());

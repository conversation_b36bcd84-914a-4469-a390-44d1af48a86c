MERGE INTO $stage_db.amazon_settlements_transactions tgt
USING (
    WITH settlements AS (
        --sum up to get multiple transactions for same attributes with same report_date. This is to avoid eliminating any necessary row during deduping
        SELECT 
            TO_TIMESTAMP(snapshot_date)                                            AS "report_date"
            , TO_TIMESTAMP(CONVERT_TIMEZONE('America/Los_Angeles', TO_TIMESTAMP_TZ(snapshot_date))) AS "report_date_pst"
            , TO_TIMESTAMP(CONVERT_TIMEZONE('UTC', TO_TIMESTAMP_TZ(snapshot_date))) AS "report_date_utc"
            , RIGHT(snapshot_date, 6)                                              AS "report_date_utc_offset"
            , TO_VARCHAR(settlement_id)                                            AS "settlement_id"
            , seller_id                                                            AS "seller_id"
            , country                                                              AS "country_code"
            , "TYPE"                                                               AS "type"
            , order_id                                                             AS "order_id"
            , sku                                                                  AS "sku"
            , description                                                          AS "description"
            , marketplace                                                          AS "marketplace"
            , account_type                                                         AS "account_type"
            , fulfillment                                                          AS "fulfillment_channel"
            , order_city                                                           AS "order_city"
            , order_state                                                          AS "order_state"
            , order_postal                                                         AS "order_postal"
            , tax_collection_model                                                 AS "tax_collection_model"
            , file_name                                                            AS "file_name"
            , etl_batch_run_time                                                   AS "etl_batch_runtime"
            , report_fetched_and_loaded_at                                         AS "report_fetched_and_loaded_at"
            , SUM(TO_NUMBER(CASE WHEN quantity = '' THEN NULL ELSE quantity END))  AS "quantity"
            , SUM(TO_DOUBLE(REPLACE(product_sales,',','')))                        AS "product sales"
            , SUM(TO_DOUBLE(REPLACE(product_sales_tax,',','')))                    AS "product sales tax"
            , SUM(TO_DOUBLE(REPLACE(shipping_credits,',','')))                     AS "shipping credits"
            , SUM(TO_DOUBLE(REPLACE(shipping_credits_tax,',','')))                 AS "shipping credits tax"
            , SUM(TO_DOUBLE(REPLACE(gift_wrap_credits,',','')))                    AS "gift wrap credits"
            , SUM(TO_DOUBLE(REPLACE(giftwrap_credits_tax,',','')))                 AS "giftwrap credits tax"
            , SUM(TO_DOUBLE(REPLACE(regulatory_fee,',','')))                       AS "regulatory fee"
            , SUM(TO_DOUBLE(REPLACE(tax_on_regulatory_fee,',','')))                AS "tax on regulatory fee"
            , SUM(TO_DOUBLE(REPLACE(promotional_rebates,',','')))                  AS "promotional rebates"
            , SUM(TO_DOUBLE(REPLACE(promotional_rebates_tax,',','')))              AS "promotional rebates tax"
            , SUM(TO_DOUBLE(REPLACE(marketplace_withheld_tax,',','')))             AS "marketplace withheld tax"
            , SUM(TO_DOUBLE(REPLACE(selling_fees,',','')))                         AS "selling fees"
            , SUM(TO_DOUBLE(REPLACE(fba_fees,',','')))                             AS "fba fees"
            , SUM(TO_DOUBLE(REPLACE(other_transaction_fees,',','')))               AS "other transaction fees"
            , SUM(TO_DOUBLE(REPLACE(other,',','')))                                AS "other"
            , SUM(TO_DOUBLE(REPLACE(total,',','')))                                AS "total"
        FROM $raw_db.amazon_settlements_transactions_stg
        GROUP BY  "report_date"
                , "report_date_pst"
                , "report_date_utc"
                , "report_date_utc_offset"
                , "settlement_id"
                , "seller_id"
                , "country_code"
                , "type"
                , "order_id"
                , "sku"
                , "description"
                , "marketplace"
                , "account_type"
                , "fulfillment_channel"
                , "order_city"
                , "order_state"
                , "order_postal"
                , "tax_collection_model"
                , "file_name"
                , "etl_batch_runtime"
                , "report_fetched_and_loaded_at"
    )
    SELECT
        md5(CAST(COALESCE(CAST("report_date"            AS VARCHAR), '') || '-' ||
                 COALESCE(CAST("settlement_id"          AS VARCHAR), '') || '-' ||
                 COALESCE(CAST("seller_id"              AS VARCHAR), '') || '-' ||
                 COALESCE(CAST("country_code"           AS VARCHAR), '') || '-' ||
                 COALESCE(CAST("type"                   AS VARCHAR), '') || '-' ||
                 COALESCE(CAST("order_id"               AS VARCHAR), '') || '-' ||
                 COALESCE(CAST("sku"                    AS VARCHAR), '') || '-' ||
                 COALESCE(CAST("description"            AS VARCHAR), '') || '-' ||
                 COALESCE(CAST("marketplace"            AS VARCHAR), '') || '-' ||
                 COALESCE(CAST("account_type"           AS VARCHAR), '') || '-' ||
                 COALESCE(CAST("fulfillment_channel"    AS VARCHAR), '') || '-' ||
                 COALESCE(CAST("order_city"             AS VARCHAR), '') || '-' ||
                 COALESCE(CAST("order_state"            AS VARCHAR), '') || '-' ||
                 COALESCE(CAST("order_postal"           AS VARCHAR), '') || '-' ||
                 COALESCE(CAST("tax_collection_model"   AS VARCHAR), '') AS VARCHAR))   AS "settlement_transaction_pk"
        ,*
    FROM(
        SELECT    "report_date"
                , "report_date_pst"
                , "report_date_utc"
                , "report_date_utc_offset"
                , "settlement_id"
                , "seller_id"
                , "country_code"
                , "type"
                , "order_id"
                , "sku"
                , "description"
                , "marketplace"
                , "account_type"
                , "fulfillment_channel"
                , "order_city"
                , "order_state"
                , "order_postal"
                , "tax_collection_model"
                , "quantity"
                , "product sales"
                , "product sales tax"
                , "shipping credits"
                , "shipping credits tax"
                , "gift wrap credits"
                , "giftwrap credits tax"
                , "regulatory fee"
                , "tax on regulatory fee"
                , "promotional rebates"
                , "promotional rebates tax"
                , "marketplace withheld tax"
                , "selling fees"
                , "fba fees"
                , "other transaction fees"
                , "other"
                , "total"
                , "file_name"
                , "etl_batch_runtime"
                , "report_fetched_and_loaded_at"
        FROM settlements
        QUALIFY ROW_NUMBER() OVER(PARTITION BY   "report_date"
                                                ,"settlement_id"
                                                ,IFNULL("seller_id",'')
                                                ,IFNULL("country_code",'')
                                                ,IFNULL("type",'')
                                                ,IFNULL("order_id",'')
                                                ,IFNULL("sku",'')
                                                ,IFNULL("description",'')
                                                ,IFNULL("marketplace",'')
                                                ,IFNULL("account_type",'')
                                                ,IFNULL("fulfillment_channel",'')
                                                ,IFNULL("order_city",'')
                                                ,IFNULL("order_state",'')
                                                ,IFNULL("order_postal",'')
                                                ,IFNULL("tax_collection_model",'')
                                ORDER BY "report_fetched_and_loaded_at" desc) = 1
        )
) src
ON tgt."settlement_transaction_pk" = src."settlement_transaction_pk"
WHEN MATCHED THEN UPDATE SET  tgt."quantity"                    = src."quantity"
                            , tgt."product sales"               = src."product sales"
                            , tgt."product sales tax"           = src."product sales tax"
                            , tgt."shipping credits"            = src."shipping credits"
                            , tgt."shipping credits tax"        = src."shipping credits tax"
                            , tgt."gift wrap credits"           = src."gift wrap credits"
                            , tgt."giftwrap credits tax"        = src."giftwrap credits tax"
                            , tgt."regulatory fee"              = src."regulatory fee"
                            , tgt."tax on regulatory fee"       = src."tax on regulatory fee"
                            , tgt."promotional rebates"         = src."promotional rebates"
                            , tgt."promotional rebates tax"     = src."promotional rebates tax"
                            , tgt."marketplace withheld tax"    = src."marketplace withheld tax"
                            , tgt."selling fees"                = src."selling fees"
                            , tgt."fba fees"                    = src."fba fees"
                            , tgt."other transaction fees"      = src."other transaction fees"
                            , tgt."other"                       = src."other"
                            , tgt."total"                       = src."total"
                            , tgt."file_name"                   = src."file_name"
                            , tgt."etl_batch_runtime"           = src."etl_batch_runtime"
                            , tgt."report_fetched_and_loaded_at"= src."report_fetched_and_loaded_at"
WHEN NOT MATCHED THEN INSERT
(
      "settlement_transaction_pk"
    , "report_date"
    , "report_date_pst"
    , "report_date_utc"
    , "report_date_utc_offset"
    , "settlement_id"
    , "seller_id"
    , "country_code"
    , "type"
    , "order_id"
    , "sku"
    , "description"
    , "marketplace"
    , "account_type"
    , "fulfillment_channel"
    , "order_city"
    , "order_state"
    , "order_postal"
    , "tax_collection_model"
    , "quantity"
    , "product sales"
    , "product sales tax"
    , "shipping credits"
    , "shipping credits tax"
    , "gift wrap credits"
    , "giftwrap credits tax"
    , "regulatory fee"
    , "tax on regulatory fee"
    , "promotional rebates"
    , "promotional rebates tax"
    , "marketplace withheld tax"
    , "selling fees"
    , "fba fees"
    , "other transaction fees"
    , "other"
    , "total"
    , "file_name"
    , "etl_batch_runtime"
    , "report_fetched_and_loaded_at"
)
VALUES (
      src."settlement_transaction_pk"
    , src."report_date"
    , src."report_date_pst"
    , src."report_date_utc"
    , src."report_date_utc_offset"
    , src."settlement_id"
    , src."seller_id"
    , src."country_code"
    , src."type"
    , src."order_id"
    , src."sku"
    , src."description"
    , src."marketplace"
    , src."account_type"
    , src."fulfillment_channel"
    , src."order_city"
    , src."order_state"
    , src."order_postal"
    , src."tax_collection_model"
    , src."quantity"            
    , src."product sales"
    , src."product sales tax"
    , src."shipping credits"
    , src."shipping credits tax"
    , src."gift wrap credits"
    , src."giftwrap credits tax"
    , src."regulatory fee"
    , src."tax on regulatory fee"
    , src."promotional rebates"
    , src."promotional rebates tax"
    , src."marketplace withheld tax"
    , src."selling fees"
    , src."fba fees"
    , src."other transaction fees"
    , src."other"
    , src."total"
    , src."file_name"
    , src."etl_batch_runtime"
    , src."report_fetched_and_loaded_at"
);

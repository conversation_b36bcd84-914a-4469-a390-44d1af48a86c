CREATE OR REPLACE TRANSIENT TABLE $stage_db.amazon_settlement_transactions_branded AS
WITH agg_settlement AS (
    SELECT
        md5(
            CAST(
                COALESCE(CAST(TO_TIMESTAMP(posted_datetime_local) AS VARCHAR), '') || '-' ||
                COALESCE(CAST(NULLIF(TRIM(settlement_id), '') AS VARCHAR), '') || '-' ||
                COALESCE(CAST(NULLIF(TRIM(seller_id), '') AS VARCHAR), '') || '-' ||
                COALESCE(CAST(NULLIF(TRIM(country_code), '') AS VARCHAR), '') || '-' ||
                COALESCE(CAST(NULLIF(TRIM("TYPE"), '') AS VARCHAR), '') || '-' ||
                COALESCE(CAST(NULLIF(TRIM(order_id), '') AS VARCHAR), '') || '-' ||
                COALESCE(CAST(NULLIF(TRIM(sku), '') AS VARCHAR), '') || '-' ||
                COALESCE(CAST(NULLIF(TRIM(description), '') AS VARCHAR), '') || '-' ||
                COALESCE(CAST(NULLIF(TRIM(amazon_domain), '') AS VARCHAR), '') || '-' ||
                COALESCE(CAST(NULLIF(TRIM(account_type), '') AS VARCHAR), '') || '-' ||
                COALESCE(CAST(NULLIF(TRIM(fulfillment), '') AS VARCHAR), '') || '-' ||
                COALESCE(CAST(NULLIF(TRIM(order_city), '') AS VARCHAR), '') || '-' ||
                COALESCE(CAST(NULLIF(TRIM(order_state), '') AS VARCHAR), '') || '-' ||
                COALESCE(CAST(NULLIF(TRIM(order_postal), '') AS VARCHAR), '') || '-' ||
                COALESCE(CAST(NULLIF(TRIM(tax_collection_model), '') AS VARCHAR), '')
            AS VARCHAR)
        ) AS settlement_transaction_pk
        , TO_TIMESTAMP(posted_datetime_local) AS report_date
        , CONVERT_TIMEZONE('UTC', 'America/Los_Angeles', TO_TIMESTAMP(posted_at)) AS report_date_pst
        , TO_TIMESTAMP(posted_at) AS report_date_utc
        , DATEDIFF('minute', report_date_utc, report_date) AS utc_local_diff
        , TO_VARCHAR(FLOOR(ABS(utc_local_diff)/60)) AS diff_hour
        , TO_VARCHAR(ABS(utc_local_diff)%60) AS diff_min
        , DECODE(SIGN(utc_local_diff), -1, '-', '+') || LEFT(TO_TIME(diff_hour||':'||diff_min, 'HH24:MI')::VARCHAR, 5) AS report_date_utc_offset
        , NULLIF(TRIM(settlement_id), '') AS settlement_id
        , NULLIF(TRIM(seller_id), '') AS seller_id
        , NULLIF(TRIM(country_code), '') AS country_code
        , NULLIF(TRIM("TYPE"), '') AS "TYPE"
        , NULLIF(TRIM(order_id), '') AS order_id
        , NULLIF(TRIM(sku), '') AS sku
        , NULLIF(TRIM(description), '') AS description
        , NULLIF(TRIM(amazon_domain), '') AS marketplace
        , NULLIF(TRIM(account_type), '') AS account_type
        , NULLIF(TRIM(fulfillment), '') AS fulfillment_channel
        , NULLIF(TRIM(order_city), '') AS order_city
        , NULLIF(TRIM(order_state), '') AS order_state
        , NULLIF(TRIM(order_postal), '') AS order_postal
        , NULLIF(TRIM(tax_collection_model), '') AS tax_collection_model
        , SUM(quantity) AS quantity
        , SUM(product_sales) AS product_sales
        , SUM(product_sales_tax) AS product_sales_tax
        , SUM(shipping_credits) AS shipping_credits
        , SUM(shipping_credits_tax) AS shipping_credits_tax
        , SUM(gift_wrap_credits) AS gift_wrap_credits
        , SUM(gift_wrap_credits_tax) AS gift_wrap_credits_tax
        , SUM(regulatory_fee) AS regulatory_fee
        , SUM(tax_on_regulation_fee) AS tax_on_regulation_fee
        , SUM(promotional_rebate) AS promotional_rebate
        , SUM(promotional_rebate_tax) AS promotional_rebate_tax
        , SUM(marketplace_withheld_tax) AS marketplace_withheld_tax
        , SUM(selling_fees) AS selling_fees
        , SUM(fba_fees) AS fba_fees
        , SUM(other_transaction_fees) AS other_transaction_fees
        , SUM(other) AS other
        , SUM(total) AS total
        , MAX(file_name) AS file_name
        , SYSDATE() AS etl_batch_runtime
        , _airbyte_extracted_at AS report_fetched_and_loaded_at
    FROM dwh.raw.bq_sp_api_amazon_drr_consolidated_cleaned_incremental
    WHERE UPPER(country_code) NOT IN ('US', 'UK', 'DE', 'FR', 'IT', 'ES', 'SE', 'BE', 'NL', 'PL', 'TR', 'AE', 'CA')
    GROUP BY ALL
)
SELECT
    settlement_transaction_pk
    , report_date
    , settlement_id
    , seller_id
    , country_code
    , "TYPE"
    , order_id
    , sku
    , description
    , marketplace
    , account_type
    , fulfillment_channel
    , order_city
    , order_state
    , order_postal
    , tax_collection_model
    , quantity
    , product_sales
    , product_sales_tax
    , shipping_credits
    , shipping_credits_tax
    , gift_wrap_credits
    , gift_wrap_credits_tax
    , regulatory_fee
    , tax_on_regulation_fee
    , promotional_rebate
    , promotional_rebate_tax
    , marketplace_withheld_tax
    , selling_fees
    , fba_fees
    , other_transaction_fees
    , other
    , total
    , file_name
    , etl_batch_runtime
    , report_fetched_and_loaded_at
    , report_date_pst
    , report_date_utc
    , report_date_utc_offset
FROM agg_settlement
QUALIFY ROW_NUMBER() OVER(PARTITION BY report_date, settlement_id, IFNULL(seller_id, ''), IFNULL(country_code, ''),
    IFNULL(type, ''), IFNULL(order_id, ''), IFNULL(sku, ''), IFNULL(description, ''), IFNULL(marketplace, ''),
    IFNULL(account_type, ''), IFNULL(fulfillment_channel, ''), IFNULL(order_city, ''), IFNULL(order_state, ''),
    IFNULL(order_postal, ''), IFNULL(tax_collection_model, '')
    ORDER BY report_fetched_and_loaded_at desc) = 1;

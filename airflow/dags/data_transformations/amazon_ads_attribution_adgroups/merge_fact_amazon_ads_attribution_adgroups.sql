MERGE INTO
    $curated_db.fact_amazon_ads_attribution_adgroups AS tgt
USING
    $stage_db.stg_amazon_ads_attribution_adgroups AS src
    
        ON 1 = 1
       AND src.pk = tgt.pk
       
WHEN MATCHED AND src.daton_batch_runtime >= tgt.daton_batch_runtime THEN
UPDATE SET
    tgt.daton_batch_id=src.daton_batch_id,
    tgt.daton_batch_runtime=src.daton_batch_runtime,
    tgt.daton_user_id=src.daton_user_id,
    tgt.account_id=src.account_id,
    tgt.account_name=src.account_name,
    tgt.ad_group_id=src.ad_group_id,
    tgt.advertiser_name=src.advertiser_name,
    tgt.add_to_cart_clicks14d=src.add_to_cart_clicks14d,
    tgt.detail_page_views_clicks14d=src.detail_page_views_clicks14d,
    tgt.purchases14d=src.purchases14d,
    tgt.sales14d=src.sales14d,
    tgt.total_add_to_cart_clicks14d=src.total_add_to_cart_clicks14d,
    tgt.total_detail_page_views_clicks14d=src.total_detail_page_views_clicks14d,
    tgt.total_purchases14d=src.total_purchases14d,
    tgt.brb_bonus_amount=src.brb_bonus_amount,
    tgt.campaign_id=src.campaign_id,
    tgt.click_throughs=src.click_throughs,
    tgt.country_name=src.country_name,
    tgt.creative_id=src.creative_id,
    tgt.profile_id=src.profile_id,
    tgt.publisher=src.publisher,
    tgt.report_date=src.report_date,
    tgt.total_sales14d=src.total_sales14d,
    tgt.total_units_sold14d=src.total_units_sold14d,
    tgt.units_sold14d=src.units_sold14d,
    tgt.country_code=src.country_code,
    tgt.currency=src.currency,
    tgt.sales14d_usd=src.sales14d_usd,
    tgt.total_sales14d_usd=src.total_sales14d_usd,
    tgt.file_name=src.file_name,
    tgt.etl_batch_run_time=src.etl_batch_run_time,
    tgt.updated_by = 'amazon_ads_attribution_adgroups',
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    daton_batch_id,
    daton_batch_runtime,
    daton_user_id,
    account_id,
    account_name,
    ad_group_id,
    advertiser_name,
    add_to_cart_clicks14d,
    detail_page_views_clicks14d,
    purchases14d,
    sales14d,
    total_add_to_cart_clicks14d,
    total_detail_page_views_clicks14d,
    total_purchases14d,
    brb_bonus_amount,
    campaign_id,
    click_throughs,
    country_name,
    creative_id,
    profile_id,
    publisher,
    report_date,
    total_sales14d,
    total_units_sold14d,
    units_sold14d,
    country_code,
    currency,
    sales14d_usd,
    total_sales14d_usd,
    file_name,
    etl_batch_run_time,
    created_by,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src.daton_batch_id,
    src.daton_batch_runtime,
    src.daton_user_id,
    src.account_id,
    src.account_name,
    src.ad_group_id,
    src.advertiser_name,
    src.add_to_cart_clicks14d,
    src.detail_page_views_clicks14d,
    src.purchases14d,
    src.sales14d,
    src.total_add_to_cart_clicks14d,
    src.total_detail_page_views_clicks14d,
    src.total_purchases14d,
    src.brb_bonus_amount,
    src.campaign_id,
    src.click_throughs,
    src.country_name,
    src.creative_id,
    src.profile_id,
    src.publisher,
    src.report_date,
    src.total_sales14d,
    src.total_units_sold14d,
    src.units_sold14d,
    src.country_code,
    src.currency,
    src.sales14d_usd,
    src.total_sales14d_usd,
    src.file_name,
    src.etl_batch_run_time,
    'amazon_ads_attribution_adgroups',
    SYSDATE(),
    SYSDATE()
);
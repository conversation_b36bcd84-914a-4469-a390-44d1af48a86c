CREATE OR REPLACE TABLE $stage_db.stg_amazon_ads_attribution_adgroups AS (
    with adgroups as (
     SELECT
        pk,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountId,
        accountName,
        adGroupId,
        advertiserName,
        attributedAddToCartClicks14d,
        attributedDetailPageViewsClicks14d,
        attributedPurchases14d,
        attributedSales14d,
        attributedTotalAddToCartClicks14d,
        attributedTotalDetailPageViewsClicks14d,
        attributedTotalPurchases14d,
        brb_bonus_amount,
        campaignId,
        click_throughs,
        countryName,
        creativeId,
        profileId,
        publisher,
        TO_DATE(date, 'YYYYMMDD') as reportDate,
        totalAttributedSales14d,
        totalUnitsSold14d,
        unitsSold14d,
        file_name,
        etl_batch_run_time
    FROM $stage_db.dedupe_amazon_ads_attribution_adgroups
    )

    SELECT
        pk,
        _daton_batch_id as  daton_batch_id,
        _daton_batch_runtime as daton_batch_runtime,
        _daton_user_id as daton_user_id,
        accountId as account_id,
        accountName as account_name,
        adGroupId as ad_group_id,
        advertiserName as advertiser_name,
        attributedAddToCartClicks14d as add_to_cart_clicks14d,
        attributedDetailPageViewsClicks14d as detail_page_views_clicks14d,
        attributedPurchases14d as purchases14d,
        attributedSales14d as sales14d,
        attributedTotalAddToCartClicks14d as total_add_to_cart_clicks14d,
        attributedTotalDetailPageViewsClicks14d as total_detail_page_views_clicks14d,
        attributedTotalPurchases14d as total_purchases14d,
        brb_bonus_amount,
        campaignId as campaign_id,
        click_throughs,
        countryName as country_name,
        creativeId as creative_id,
        profileId as profile_id,
        publisher,
        reportDate as report_date,
        totalAttributedSales14d as total_sales14d,
        totalUnitsSold14d as total_units_sold14d,
        unitsSold14d  as units_sold14d,
        cc.country_code,
        cc.currency,
        round(IFF(lower(currency) = 'usd', all_data.attributedSales14d,
        all_data.attributedSales14d * all_rates."exchange_rate"),2) AS sales14d_usd,
        round(IFF(lower(currency) = 'usd', all_data.totalAttributedSales14d,
        all_data.totalAttributedSales14d * all_rates."exchange_rate"),2) AS total_sales14d_usd,
        file_name,
        etl_batch_run_time
    FROM
        adgroups all_data
    left join dwh.staging.stg_amazon_ads_country_code_map cc
        on lower(all_data.countryname) = lower(cc.key)
    left join dwh.prod.netsuite_fx_rates as all_rates
        on currency = all_rates."transactional_currency"
        and UPPER(all_rates."base_currency") = 'USD'
        and all_data.reportDate between all_rates."effective_start_date" 
        and all_rates."effective_end_date"
);
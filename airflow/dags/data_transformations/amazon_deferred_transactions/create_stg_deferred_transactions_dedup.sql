CREATE OR REPLACE TABLE $stage_db.amazon_deferred_transactions AS
WITH deferred_transactions AS (
    --sum up to get multiple transactions for same attributes with same report_date. This is to avoid eliminating any necessary row during deduping
    SELECT
        TO_TIMESTAMP(snapshot_date) AS report_date
        , settlement_id
        , seller_id
        , country AS country_code
        , type
        , order_id
        , sku
        , description
        , marketplace_id
        , marketplace
        , account_type
        , fulfillment AS fulfillment_channel
        , order_city
        , order_state
        , order_postal
        , tax_collection_model
        , SUM(to_number(CASE WHEN TRIM(quantity) = '' THEN NULL ELSE quantity END)) AS quantity
        , SUM(to_double(REPLACE(product_sales,',',''))) AS product_sales
        , SUM(to_double(REPLACE(product_sales_tax,',',''))) AS product_sales_tax
        , SUM(to_double(REPLACE(shipping_credits,',',''))) AS shipping_credits
        , SUM(to_double(REPLACE(shipping_credits_tax,',',''))) AS shipping_credits_tax
        , SUM(to_double(REPLACE(gift_wrap_credits,',',''))) AS gift_wrap_credits
        , SUM(to_double(REPLACE(giftwrap_credits_tax,',',''))) AS giftwrap_credits_tax
        , SUM(to_double(REPLACE(regulatory_fee,',',''))) AS regulatory_fee
        , SUM(to_double(REPLACE(tax_on_regulatory_fee,',',''))) AS tax_on_regulatory_fee
        , SUM(to_double(REPLACE(promotional_rebates,',',''))) AS promotional_rebates
        , SUM(to_double(REPLACE(promotional_rebates_tax,',',''))) AS promotional_rebates_tax
        , SUM(to_double(REPLACE(marketplace_withheld_tax,',',''))) AS marketplace_withheld_tax
        , SUM(to_double(REPLACE(selling_fees,',',''))) AS selling_fees
        , SUM(to_double(REPLACE(fba_fees,',',''))) AS fba_fees
        , SUM(to_double(REPLACE(other_transaction_fees,',',''))) AS other_transaction_fees
        , SUM(to_double(REPLACE(other,',',''))) AS other
        , SUM(to_double(REPLACE(total,',',''))) AS total
        , report_requested_time
        , report_requested_time::DATE AS report_requested_date
        , file_name
        , etl_batch_run_time
        , report_fetched_and_loaded_at
    FROM $raw_db.raw_amazon_deferred_transactions
    GROUP BY ALL
)
SELECT
    md5(
        CAST(
            COALESCE(CAST(report_date AS VARCHAR), '') || '-' ||
            COALESCE(CAST(settlement_id AS VARCHAR), '') || '-' ||
            COALESCE(CAST(seller_id AS VARCHAR), '') || '-' ||
            COALESCE(CAST(country_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(type AS VARCHAR), '') || '-' ||
            COALESCE(CAST(order_id AS VARCHAR), '') || '-' ||
            COALESCE(CAST(sku AS VARCHAR), '') || '-' ||
            COALESCE(CAST(description AS VARCHAR), '') || '-' ||
            COALESCE(CAST(marketplace AS VARCHAR), '') || '-' ||
            COALESCE(CAST(account_type AS VARCHAR), '') || '-' ||
            COALESCE(CAST(fulfillment_channel AS VARCHAR), '') || '-' ||
            COALESCE(CAST(order_city AS VARCHAR), '') || '-' ||
            COALESCE(CAST(order_state AS VARCHAR), '') || '-' ||
            COALESCE(CAST(order_postal AS VARCHAR), '') || '-' ||
            COALESCE(CAST(tax_collection_model AS VARCHAR), '') AS VARCHAR
        )
    )   AS deferred_transaction_key
    , report_date
    , settlement_id
    , seller_id
    , country_code
    , type
    , order_id
    , sku
    , description
    , marketplace_id
    , marketplace
    , account_type
    , fulfillment_channel
    , order_city
    , order_state
    , order_postal
    , tax_collection_model
    , quantity
    , product_sales
    , product_sales_tax
    , shipping_credits
    , shipping_credits_tax
    , gift_wrap_credits
    , giftwrap_credits_tax
    , regulatory_fee
    , tax_on_regulatory_fee
    , promotional_rebates
    , promotional_rebates_tax
    , marketplace_withheld_tax
    , selling_fees
    , fba_fees
    , other_transaction_fees
    , other
    , total
    , report_requested_time
    , report_requested_date
    , file_name
    , etl_batch_run_time
    , report_fetched_and_loaded_at
FROM deferred_transactions
QUALIFY ROW_NUMBER() OVER(
    PARTITION BY report_date
        , settlement_id
        , IFNULL(seller_id, '')
        , IFNULL(country_code, '')
        , IFNULL(type, '')
        , IFNULL(order_id, '')
        , IFNULL(sku, '')
        , IFNULL(description, '')
        , IFNULL(marketplace, '')
        , IFNULL(account_type, '')
        , IFNULL(fulfillment_channel, '')
        , IFNULL(order_city, '')
        , IFNULL(order_state, '')
        , IFNULL(order_postal, '')
        , IFNULL(tax_collection_model, '')
        , report_requested_date
    ORDER BY report_fetched_and_loaded_at desc) = 1;

CREATE TABLE IF NOT EXISTS $curated_db.fact_amazon_deferred_transactions (
    deferred_transaction_key VARCHAR
    , report_date TIMESTAMP
    , settlement_id VARCHAR
    , seller_id VARCHAR
    , country_code VARCHAR
    , type VARCHAR
    , order_id VARCHAR
    , sku VARCHAR
    , description VARCHAR
    , marketplace_id VARCHAR
    , marketplace VARCHAR
    , account_type VA<PERSON>HA<PERSON>
    , fulfillment_channel VARCHAR
    , order_city VARCHAR
    , order_state VARCHAR
    , order_postal VARCHAR
    , tax_collection_model VARCHAR
    , quantity NUMBER
    , product_sales FLOAT
    , product_sales_tax FLOAT
    , shipping_credits FLOAT
    , shipping_credits_tax FLOAT
    , gift_wrap_credits FLOAT
    , giftwrap_credits_tax FLOAT
    , regulatory_fee FLOAT
    , tax_on_regulatory_fee FLOAT
    , promotional_rebates FLOAT
    , promotional_rebates_tax FLOAT
    , marketplace_withheld_tax FLOAT
    , selling_fees FLOAT
    , fba_fees FLOAT
    , other_transaction_fees FLOAT
    , other FLOAT
    , total FLOAT
    , report_fetched_and_loaded_at TIMESTAMP
    , report_requested_time TIMESTAMP
    , report_requested_date DATE
    , seller_code VARCHAR
    , data_source VARCHAR
    , record_created_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
    , record_updated_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);


BEGIN TRANSACTION;

DELETE FROM $curated_db.fact_amazon_deferred_transactions t
USING $stage_db.amazon_deferred_transactions s
WHERE t.deferred_transaction_key = s.deferred_transaction_key
    AND t.report_requested_date = s.report_requested_date;

INSERT INTO $curated_db.fact_amazon_deferred_transactions (
    deferred_transaction_key
    , report_date
    , settlement_id
    , seller_id
    , country_code
    , type
    , order_id
    , sku
    , description
    , marketplace_id
    , marketplace
    , account_type
    , fulfillment_channel
    , order_city
    , order_state
    , order_postal
    , tax_collection_model
    , quantity
    , product_sales
    , product_sales_tax
    , shipping_credits
    , shipping_credits_tax
    , gift_wrap_credits
    , giftwrap_credits_tax
    , regulatory_fee
    , tax_on_regulatory_fee
    , promotional_rebates
    , promotional_rebates_tax
    , marketplace_withheld_tax
    , selling_fees
    , fba_fees
    , other_transaction_fees
    , other
    , total
    , report_fetched_and_loaded_at
    , report_requested_time
    , report_requested_date
    , seller_code
    , data_source
)
SELECT
    s.deferred_transaction_key
    , s.report_date
    , s.settlement_id
    , s.seller_id
    , s.country_code
    , s.type
    , s.order_id
    , s.sku
    , s.description
    , s.marketplace_id
    , s.marketplace
    , s.account_type
    , s.fulfillment_channel
    , s.order_city
    , s.order_state
    , s.order_postal
    , s.tax_collection_model
    , s.quantity
    , s.product_sales
    , s.product_sales_tax
    , s.shipping_credits
    , s.shipping_credits_tax
    , s.gift_wrap_credits
    , s.giftwrap_credits_tax
    , s.regulatory_fee
    , s.tax_on_regulatory_fee
    , s.promotional_rebates
    , s.promotional_rebates_tax
    , s.marketplace_withheld_tax
    , s.selling_fees
    , s.fba_fees
    , s.other_transaction_fees
    , s.other
    , s.total
    , s.report_fetched_and_loaded_at
    , s.report_requested_time
    , s.report_requested_date
    , si.seller_code
    , 'SCS' AS data_source
FROM $stage_db.amazon_deferred_transactions s
LEFT JOIN dwh.staging.stg_seller_info si
    ON s.seller_id = si.seller_id;


DELETE FROM $curated_db.fact_amazon_deferred_transactions
WHERE report_requested_date < DATE_TRUNC('month', (SELECT MAX(report_requested_date) FROM $curated_db.fact_amazon_deferred_transactions) - 90);

COMMIT;

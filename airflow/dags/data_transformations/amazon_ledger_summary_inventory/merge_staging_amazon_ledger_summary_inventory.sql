MERGE INTO
    $stage_db.amazon_ledger_summary_inventory_v2 AS tgt
USING
    (   SELECT
        -- Updated pk - added seller id to pk on 2023-12-14 (EI-739)
        md5(CAST(COALESCE(CAST(connector_region AS VARCHAR ), '') || '-' ||
            COALESCE(CAST(date AS VARCHAR ), '') || '-' ||
            COALESCE(CAST(sku AS VARCHAR ), '') || '-' ||
            COALESCE(CAST(fnsku AS VARCHAR ), '') || '-' ||
            COALESCE(CAST(asin AS VARCHAR ), '') || '-' ||
            COALESCE(CAST(disposition AS VARCHAR ), '') || '-' ||
            COALESCE(CAST(country AS VARCHAR ), '') || '-' ||
            COALESCE(CAST(seller_id AS VARCHAR ), '') AS VARCHAR )
        ) as inventory_pk,
        *
        from $stage_db.dedupe_amazon_ledger_summary_inventory
    ) src
    ON 1 = 1
    AND src.inventory_pk = tgt.inventory_pk
WHEN MATCHED
    AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    tgt.asin = src.asin,
    tgt.customer_returns = src.customer_returns,
    tgt.customer_shipments = src.customer_shipments,
    tgt.damaged = src.damaged,
    tgt.date = src.date,
    tgt.disposed = src.disposed,
    tgt.disposition = src.disposition,
    tgt.ending_warehouse_balance = src.ending_warehouse_balance,
    tgt.fnsku = src.fnsku,
    tgt.found = src.found,
    tgt.in_transit_between_warehouses = src.in_transit_between_warehouses,
    tgt.country = src.country,
    tgt.lost = src.lost,
    tgt.sku = src.sku,
    tgt.other_events = src.other_events,
    tgt.receipts = src.receipts,
    tgt.reportrequesttime = src.reportrequesttime,
    tgt.reportenddate = src.reportenddate,
    tgt.reportstartdate = src.reportstartdate,
    tgt.starting_warehouse_balance = src.starting_warehouse_balance,
    tgt.title = src.title,
    tgt.unknown_events = src.unknown_events,
    tgt.vendor_returns = src.vendor_returns,
    tgt.warehouse_transfer_in_out = src.warehouse_transfer_in_out,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.marketplaceid = src.marketplaceid,
    tgt.marketplacename = src.marketplacename,
    tgt.seller_id = src.seller_id,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    inventory_pk,
    asin,
    customer_returns,
    customer_shipments,
    damaged,
    date,
    disposed,
    disposition,
    ending_warehouse_balance,
    fnsku,
    found,
    in_transit_between_warehouses,
    country,
    lost,
    sku,
    other_events,
    receipts,
    reportrequesttime,
    reportenddate,
    reportstartdate,
    starting_warehouse_balance,
    title,
    unknown_events,
    vendor_returns,
    warehouse_transfer_in_out,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    marketplaceid,
    marketplacename,
    seller_id,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc,
    connector_region,
    created_by,
    updated_by
)
VALUES
(
    src.inventory_pk,
    src.asin,
    src.customer_returns,
    src.customer_shipments,
    src.damaged,
    src.date,
    src.disposed,
    src.disposition,
    src.ending_warehouse_balance,
    src.fnsku,
    src.found,
    src.in_transit_between_warehouses,
    src.country,
    src.lost,
    src.sku,
    src.other_events,
    src.receipts,
    src.reportrequesttime,
    src.reportenddate,
    src.reportstartdate,
    src.starting_warehouse_balance,
    src.title,
    src.unknown_events,
    src.vendor_returns,
    src.warehouse_transfer_in_out,
    src._daton_batch_id,
    src._daton_batch_runtime,
    src._daton_user_id,
    src.marketplaceid,
    src.marketplacename,
    src.seller_id,
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE(),
    connector_region,
    'amazon_ledger_summary_inventory',
    'ORI-3185'
);

-- update netsuite_item_number and brand_code
UPDATE $stage_db.amazon_ledger_summary_inventory_v2 AS tgt
SET
netsuite_item_number = src.netsuite_item_number,
brand = src.BRAND_CODE,
record_updated_timestamp_utc = SYSDATE()
FROM
    (
    with ledger_detailed_report as (
            SELECT
            *
            from $stage_db.amazon_ledger_summary_inventory_v2
            WHERE NETSUITE_ITEM_NUMBER IS NULL
            OR BRAND IS NULL
    ),
    ledger_detailed_report_with_brand as (
        select ledger_detailed_report.inventory_pk
        ,ledger_detailed_report.sku
        ,ledger_detailed_report.seller_id
        ,ledger_detailed_report.country
        ,CASE WHEN LOWER(TRIM(SELLER.IS_MULTI_BRAND))='no' THEN SELLER.SELLER_CODE
            ELSE brand_mapping.brand_code END AS BRAND_CODE
        FROM
        ledger_detailed_report
        LEFT JOIN
            $stage_db.STG_SELLER_INFO seller
            ON ledger_detailed_report.seller_id = seller.SELLER_ID
        LEFT JOIN
            $curated_db.marketplace_asin_brand_mapping brand_mapping
            ON ledger_detailed_report.asin = brand_mapping.asin
            and ledger_detailed_report.country = brand_mapping.country_code
    )
    select l.inventory_pk
    ,l.sku
    ,l.brand_code
    ,sku_dimension.NETSUITE_ITEM_NUMBER  as netsuite_item_number
    FROM
    ledger_detailed_report_with_brand l
    LEFT JOIN
        $curated_db.sku_item_mapping sku_dimension
        ON l.sku = sku_dimension.sku
        and l.brand_code = sku_dimension.brand_code
        and (CASE WHEN l.country = 'GB' THEN 'UK' ELSE l.country END) = sku_dimension.country_code
    ) src
WHERE
tgt.inventory_pk = src.inventory_pk;


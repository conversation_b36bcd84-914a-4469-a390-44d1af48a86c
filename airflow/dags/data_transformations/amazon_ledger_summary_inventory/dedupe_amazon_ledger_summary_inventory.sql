CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amazon_ledger_summary_inventory AS (
    SELECT
        --  primary key  --
        -- Updated pk - added seller id to pk on 2023-12-14 (EI-739)
        MD5(CONCAT(COALESCE(CAST(date AS VARCHAR), ''), '-',
            COALESCE(CAST(msku AS VARCHAR), ''), '-',
            COALESCE(CAST(fnsku AS VARCHAR), ''), '-',
            COALESCE(CAST(asin AS VARCHAR), ''), '-',
            COALESCE(CAST(disposition AS VARCHAR), ''), '-',
            COALESCE(CAST(location AS VARCHAR), ''), '-',
            COALESCE(CAST(sellingpartnerid AS VARCHAR), '')
        )) AS pk,
        asin,
        customer_returns,
        customer_shipments,
        damaged,
        date,
        disposed,
        disposition,
        ending_warehouse_balance,
        fnsku,
        found,
        in_transit_between_warehouses,
        location as country,
        lost,
        case when msku  like 'amzn.gr.%'
        then split_part(split_part(split_part(msku,'amzn.gr.',2),'-',1),'_',1)
        else msku end as sku,
        other_events,
        receipts,
        reportrequesttime,
        reportenddate,
        reportstartdate,
        starting_warehouse_balance,
        title,
        unknown_events,
        vendor_returns,
        warehouse_transfer_in_out,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        marketplaceid,
        marketplacename,
        sellingpartnerid as seller_id,
        file_name,
        etl_batch_run_time,
        split_part(split_part(file_name, '/',  7), '_', 4) as connector_region
    FROM $raw_db.raw_amazon_ledger_summary_inventory
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY date, sellingpartnerid, msku, fnsku, asin, disposition, location
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);
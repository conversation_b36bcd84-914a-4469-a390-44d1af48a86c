CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_AMAZON_LEDGER_SUMMARY_INVENTORY AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.RAW_AMAZON_LEDGER_SUMMARY_INVENTORY
    WHERE 1 = 0;

INSERT INTO $raw_db.log_AMAZON_LEDGER_SUMMARY_INVENTORY (
ASIN, CUSTOMER_RETURNS, CUSTOMER_SHIPMENTS, DAMAGED, "DATE", DISPOSED, DISPOSITION, ENDING_WAREHOUSE_BALANCE, FNSK<PERSON>, "FOUND", IN_TRANSIT_BETWEEN_WAREHOUSES, "LOCATION", LOST, MSKU, OTHER_EVENTS, RECEIPTS, REPORTREQUESTTIME, REPORTENDDATE, R<PERSON>OR<PERSON><PERSON>RTDA<PERSON>, STARTING_WAREHOUSE_BALANCE, TITLE, UNKNOWN_EVENTS, VEND<PERSON>_RETURNS, WAREHOUSE_TRANSFER_IN_OUT, "_DATON_BATCH_ID", "_DATON_BATCH_RUNTIME", "_DATON_USER_ID", MARKETPLACEID, MARKETPLACENAME, SELLINGPARTNERID, FILE_NAME, ETL_BATCH_RUN_TIME,
    log_timestamp_utc 
)
SELECT ASIN, CUSTOMER_RETURNS, CUSTOMER_SHIPMENTS, DAMAGED, "DATE", DISPOSED, DISPOSITION, ENDING_WAREHOUSE_BALANCE, FNSKU, "FOUND", IN_TRANSIT_BETWEEN_WAREHOUSES, "LOCATION", LOST, MSKU, OTHER_EVENTS, RECEIPTS, REPORTREQUESTTIME, REPORTENDDATE, REPORTSTARTDATE, STARTING_WAREHOUSE_BALANCE, TITLE, UNKNOWN_EVENTS, VENDOR_RETURNS, WAREHOUSE_TRANSFER_IN_OUT, "_DATON_BATCH_ID", "_DATON_BATCH_RUNTIME", "_DATON_USER_ID", MARKETPLACEID, MARKETPLACENAME, SELLINGPARTNERID, FILE_NAME, ETL_BATCH_RUN_TIME,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.RAW_AMAZON_LEDGER_SUMMARY_INVENTORY;
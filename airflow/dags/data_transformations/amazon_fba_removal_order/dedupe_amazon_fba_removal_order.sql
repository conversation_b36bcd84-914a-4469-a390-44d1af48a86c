CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amazon_fba_removal_order AS (
    SELECT
        order_id as pk,
        ReportRequestTime,
        ReportendDate,
        ReportstartDate,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        cancelled_quantity,
        currency,
        disposed_quantity,
        disposition,
        fnsku,
        in_process_quantity,
        last_updated_date,
        marketplaceId,
        marketplaceName,
        order_id,
        order_status,
        order_type,
        removal_fee,
        request_date,
        requested_quantity,
        sellingPartnerId,
        shipped_quantity,
        sku,
        file_name,
        etl_batch_run_time
    FROM $raw_db.raw_amazon_fba_removal_order
    WHERE 1=1
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY last_updated_date DESC NULLS LAST) = 1
);
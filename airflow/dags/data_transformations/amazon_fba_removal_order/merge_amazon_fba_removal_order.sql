CREATE TABLE IF NOT EXISTS $stage_db.merge_amazon_fba_removal_order AS
    SELECT

        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_amazon_fba_removal_order
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_amazon_fba_removal_order AS tgt
USING
    $stage_db.dedupe_amazon_fba_removal_order AS src

        ON 1 = 1
       AND src.pk = tgt.pk

WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt.ReportRequestTime = src.ReportRequestTime ,
    tgt.ReportendDate = src.ReportendDate ,
    tgt.ReportstartDate = src.ReportstartDate ,
    tgt._daton_batch_id = src._daton_batch_id ,
    tgt._daton_batch_runtime = src._daton_batch_runtime ,
    tgt._daton_user_id = src._daton_user_id ,
    tgt.cancelled_quantity = src.cancelled_quantity ,
    tgt.currency = src.currency ,
    tgt.disposed_quantity = src.disposed_quantity ,
    tgt.disposition = src.disposition ,
    tgt.fnsku = src.fnsku ,
    tgt.in_process_quantity = src.in_process_quantity ,
    tgt.last_updated_date = src.last_updated_date ,
    tgt.marketplaceId = src.marketplaceId ,
    tgt.marketplaceName = src.marketplaceName ,
    tgt.order_id = src.order_id ,
    tgt.order_status = src.order_status ,
    tgt.order_type = src.order_type ,
    tgt.removal_fee = src.removal_fee ,
    tgt.request_date = src.request_date ,
    tgt.requested_quantity = src.requested_quantity ,
    tgt.sellingPartnerId = src.sellingPartnerId ,
    tgt.shipped_quantity = src.shipped_quantity ,
    tgt.sku = src.sku ,
    tgt.file_name = src.file_name ,
    tgt.etl_batch_run_time =  src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    ReportRequestTime,
    ReportendDate,
    ReportstartDate,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    cancelled_quantity,
    currency,
    disposed_quantity,
    disposition,
    fnsku,
    in_process_quantity,
    last_updated_date,
    marketplaceId,
    marketplaceName,
    order_id,
    order_status,
    order_type,
    removal_fee,
    request_date,
    requested_quantity,
    sellingPartnerId,
    shipped_quantity,
    sku,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    ReportRequestTime,
    ReportendDate,
    ReportstartDate,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    cancelled_quantity,
    currency,
    disposed_quantity,
    disposition,
    fnsku,
    in_process_quantity,
    last_updated_date,
    marketplaceId,
    marketplaceName,
    order_id,
    order_status,
    order_type,
    removal_fee,
    request_date,
    requested_quantity,
    sellingPartnerId,
    shipped_quantity,
    sku,
    file_name,
    etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);

CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_amazon_fba_removal_order AS
    SELECT
         *
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_amazon_fba_removal_order
    WHERE 1 = 0;

INSERT INTO $raw_db.log_amazon_fba_removal_order (
    ReportRequestTime,
    ReportendDate,
    ReportstartDate,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    cancelled_quantity,
    currency,
    disposed_quantity,
    disposition,
    fnsku,
    in_process_quantity,
    last_updated_date,
    marketplaceId,
    marketplaceName,
    order_id,
    order_status,
    order_type,
    removal_fee,
    request_date,
    requested_quantity,
    sellingPartnerId,
    shipped_quantity,
    sku,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc
)
    SELECT
        ReportRequestTime,
        ReportendDate,
        ReportstartDate,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        cancelled_quantity,
        currency,
        disposed_quantity,
        disposition,
        fnsku,
        in_process_quantity,
        last_updated_date,
        marketplaceId,
        marketplaceName,
        order_id,
        order_status,
        order_type,
        removal_fee,
        request_date,
        requested_quantity,
        sellingPartnerId,
        shipped_quantity,
        sku,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM
    $raw_db.raw_amazon_fba_removal_order;
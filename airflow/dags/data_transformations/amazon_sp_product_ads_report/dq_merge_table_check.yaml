---
test_id: "check_pk_merge_amazon_sp_product_ads_report"
enabled: true
query: |
  SELECT 
  CASE WHEN COUNT(1) > 0 THEN 2 ELSE 0 END as "result"
  FROM (
  SELECT 1
  FROM $stage_db.merge_amazon_sp_product_ads_report
  WHERE RECORD_UPDATED_TIMESTAMP_UTC > CURRENT_DATE() - 15
  GROUP BY pk
  HAVING COUNT(1) > 1
  LIMIT 1
  ) T;
---
test_id: "check_not_null_merge_amazon_sp_product_ads_report"
enabled: true
query: |
  SELECT 
  CASE
  WHEN count(*) = count(pk)
  THEN 0 ELSE 2
  END AS "result"
  FROM $stage_db.merge_amazon_sp_product_ads_report 
  WHERE RECORD_UPDATED_TIMESTAMP_UTC > CURRENT_DATE() - 15;

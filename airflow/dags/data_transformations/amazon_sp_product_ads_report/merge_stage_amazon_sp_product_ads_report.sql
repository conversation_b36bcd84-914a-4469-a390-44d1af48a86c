CREATE TABLE IF NOT EXISTS $stage_db.merge_amazon_sp_product_ads_report AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    
    FROM $stage_db.dedupe_amazon_sp_product_ads_report
    
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_amazon_sp_product_ads_report AS tgt
USING
    
    $stage_db.dedupe_amazon_sp_product_ads_report AS src
    
    ON 1 = 1
    AND src.pk = tgt.pk 
WHEN MATCHED
    AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    
    tgt.pk = src.pk,
    tgt.requesttime = src.requesttime,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.accountid = src.accountid,
    tgt.accountname = src.accountname,
    tgt.adgroupid = src.adgroupid,
    tgt.adgroupname = src.adgroupname,
    tgt.adid = src.adid,
    tgt.asin = src.asin,
    tgt.attributedconversions14d = src.attributedconversions14d,
    tgt.attributedconversions14dsamesku = src.attributedconversions14dsamesku,
    tgt.attributedconversions1d = src.attributedconversions1d,
    tgt.attributedconversions1dsamesku = src.attributedconversions1dsamesku,
    tgt.attributedconversions30d = src.attributedconversions30d,
    tgt.attributedconversions30dsamesku = src.attributedconversions30dsamesku,
    tgt.attributedconversions7d = src.attributedconversions7d,
    tgt.attributedconversions7dsamesku = src.attributedconversions7dsamesku,
    tgt.attributedsales14d = src.attributedsales14d,
    tgt.attributedsales14dsamesku = src.attributedsales14dsamesku,
    tgt.attributedsales1d = src.attributedsales1d,
    tgt.attributedsales1dsamesku = src.attributedsales1dsamesku,
    tgt.attributedsales30d = src.attributedsales30d,
    tgt.attributedsales30dsamesku = src.attributedsales30dsamesku,
    tgt.attributedsales7d = src.attributedsales7d,
    tgt.attributedsales7dsamesku = src.attributedsales7dsamesku,
    tgt.attributedunitsordered14d = src.attributedunitsordered14d,
    tgt.attributedunitsordered14dsamesku = src.attributedunitsordered14dsamesku,
    tgt.attributedunitsordered1d = src.attributedunitsordered1d,
    tgt.attributedunitsordered1dsamesku = src.attributedunitsordered1dsamesku,
    tgt.attributedunitsordered30d = src.attributedunitsordered30d,
    tgt.attributedunitsordered30dsamesku = src.attributedunitsordered30dsamesku,
    tgt.attributedunitsordered7d = src.attributedunitsordered7d,
    tgt.attributedunitsordered7dsamesku = src.attributedunitsordered7dsamesku,
    tgt.campaignid = src.campaignid,
    tgt.campaignname = src.campaignname,
    tgt.clicks = src.clicks,
    tgt.cost = src.cost,
    tgt.countryname = src.countryname,
    tgt.currency = src.currency,
    tgt.impressions = src.impressions,
    tgt.profileid = src.profileid,
    tgt.reportdate = src.reportdate,
    tgt.sku = src.sku,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    
    pk,
    requesttime,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    accountid,
    accountname,
    adgroupid,
    adgroupname,
    adid,
    asin,
    attributedconversions14d,
    attributedconversions14dsamesku,
    attributedconversions1d,
    attributedconversions1dsamesku,
    attributedconversions30d,
    attributedconversions30dsamesku,
    attributedconversions7d,
    attributedconversions7dsamesku,
    attributedsales14d,
    attributedsales14dsamesku,
    attributedsales1d,
    attributedsales1dsamesku,
    attributedsales30d,
    attributedsales30dsamesku,
    attributedsales7d,
    attributedsales7dsamesku,
    attributedunitsordered14d,
    attributedunitsordered14dsamesku,
    attributedunitsordered1d,
    attributedunitsordered1dsamesku,
    attributedunitsordered30d,
    attributedunitsordered30dsamesku,
    attributedunitsordered7d,
    attributedunitsordered7dsamesku,
    campaignid,
    campaignname,
    clicks,
    cost,
    countryname,
    currency,
    impressions,
    profileid,
    reportdate,
    sku,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
     
    src.pk,
    src.requesttime, 
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.accountid, 
    src.accountname, 
    src.adgroupid, 
    src.adgroupname, 
    src.adid, 
    src.asin, 
    src.attributedconversions14d, 
    src.attributedconversions14dsamesku, 
    src.attributedconversions1d, 
    src.attributedconversions1dsamesku, 
    src.attributedconversions30d, 
    src.attributedconversions30dsamesku, 
    src.attributedconversions7d, 
    src.attributedconversions7dsamesku, 
    src.attributedsales14d, 
    src.attributedsales14dsamesku, 
    src.attributedsales1d, 
    src.attributedsales1dsamesku, 
    src.attributedsales30d, 
    src.attributedsales30dsamesku, 
    src.attributedsales7d, 
    src.attributedsales7dsamesku, 
    src.attributedunitsordered14d, 
    src.attributedunitsordered14dsamesku, 
    src.attributedunitsordered1d, 
    src.attributedunitsordered1dsamesku, 
    src.attributedunitsordered30d, 
    src.attributedunitsordered30dsamesku, 
    src.attributedunitsordered7d, 
    src.attributedunitsordered7dsamesku, 
    src.campaignid, 
    src.campaignname, 
    src.clicks, 
    src.cost, 
    src.countryname, 
    src.currency, 
    src.impressions, 
    src.profileid, 
    src.reportdate, 
    src.sku, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);

CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amazon_sp_product_ads_report AS (
    SELECT
        
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(adid AS VARCHAR), ''), '-',
        COALESCE(CAST(asin AS VARCHAR), ''), '-',
        COALESCE(CAST(reportdate AS VARCHAR), ''), '-',
        COALESCE(CAST(campaignid AS VARCHAR), ''), '-',
        COALESCE(CAST(adgroupid AS VARCHAR), '')
        )) AS pk,
        
        requesttime,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountid,
        accountname,
        adgroupid,
        adgroupname,
        adid,
        asin,
        attributedconversions14d,
        attributedconversions14dsamesku,
        attributedconversions1d,
        attributedconversions1dsamesku,
        attributedconversions30d,
        attributedconversions30dsamesku,
        attributedconversions7d,
        attributedconversions7dsamesku,
        attributedsales14d,
        attributedsales14dsamesku,
        attributedsales1d,
        attributedsales1dsamesku,
        attributedsales30d,
        attributedsales30dsamesku,
        attributedsales7d,
        attributedsales7dsamesku,
        attributedunitsordered14d,
        attributedunitsordered14dsamesku,
        attributedunitsordered1d,
        attributedunitsordered1dsamesku,
        attributedunitsordered30d,
        attributedunitsordered30dsamesku,
        attributedunitsordered7d,
        attributedunitsordered7dsamesku,
        campaignid,
        campaignname,
        clicks,
        cost,
        countryname,
        currency,
        impressions,
        profileid,
        reportdate,
        sku,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_amazon_sp_product_ads_report
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY adid, asin, reportdate, campaignid, adgroupid
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);

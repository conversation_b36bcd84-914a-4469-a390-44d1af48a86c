CREATE OR REPLACE TABLE DWH_DEV.SANDBOX.SP_API_AMAZON_DRR_CONSOLIDATED_CLEAN AS
WITH raw_settlements as (
    SELECT
        "report_date"		                AS "report_date" -- TODO: parse this and get timezone from the same
        , "settlement_id"		            AS "settlement_id"
        , "seller_id"		                AS "seller_id"   -- TODO: check if this is null
        , "country_code"                    AS "country_code"
        , "order_id"		                AS "order_id"
        , "sku"                             AS "sku"
        ,"etl_batch_runtime" AS "etl_batch_runtime"
        , "type" AS "actual_type"
        , REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE( REPLACE( "type",
                                'Ã¼','ü'),
                                'Ã³','ó'),
                                'Ã©','é'),
                                'Ãœ','Ü'),
                                'Ã–','Ö'),
                                'Ã¶','ö'),
                                'LogÃs', 'Logís'),
                                'lâ€™O','l’O'),
                                'pubblicitÃ','pubblicità'),
                                'Ã…','Å'),
                                'SipariÅŸ','Sipariş')
           AS "type2"
       , COALESCE(CASE
         WHEN "type2" LIKE 'Tarifas de inventario de Log%' THEN 'FBA Inventory Fee'
         WHEN "type2" LIKE 'Tariffa di reso cliente di Logistica di Amazon' THEN 'FBA Customer Return Fee'
         WHEN "type2" LIKE 'Bestellung_Wiedereinzug' THEN 'Order_Recovery'
         WHEN "type2" LIKE 'Liquidationen' THEN 'Liquidations'
         WHEN "type2" LIKE 'Cargo retroactivo' THEN 'Retroactive Charge'
        WHEN "type2"
          IN ('Order','Pedido','Commande','Bestellung','Ordine','Pedido','Bestelling' ,'Zamówienie','Sipariş') THEN
               'Order'
          WHEN "type2" IN
          ('Refund','Reembolso','Remboursement','Erstattung','Rimborso','Reembolso','Återbetalning','Terugbetaling' ,
            'Geriödeme','Erstattung_Wiedereinzug','Zwrot kosztów','Para İadesi','Para Ä°adesi') THEN
               'Refund'
          WHEN "type2" IN
          ('Adjustment','Ajuste','Ajustement','Anpassung','Modifica','Ajuste','Adjustment','Aanpassing','Korekta',
          'Justering','Düzeltme') THEN
               'Adjustment'
          WHEN "type2" IN
          ('Tarifa de inventario FBA','Frais de stock Expédié par Amazon','Versand durch Amazon Lagergebühr',
          'Costo di stoccaggio Logistica di Amazon','Tarifas de inventario de Logística de Amazon','Lageravgift för FBA') THEN
               'FBA Inventory Fee'
          WHEN "type2" IN
          ('Service fee','Tarifa de servicio','Frais de service','Service Fee',
          'Servicegebühr','Commissione di servizio','Tarifa de prestación de servicio','Serviceavgift','Servicekosten') THEN
               'Service Fee'
          WHEN "type2" IN
          ('Transfer','Trasferir','Transfert','Übertrag','Transférer',
          'Trasferimento','Transferir','Överföring','Overboeking','Przelew') THEN
               'Transfer'
          WHEN "type2" IN
          ('Debt', 'Deuda', 'Saldo descubierto', 'Solde négatif', 'Solde nÃ©gatif',
               'Saldo negativo', 'Skuld', 'Verbindlichkeit', 'Schuld','BorÃ§','Borç','BorÃƒÂ§','DÅ‚ug') THEN
               'Debt'
          WHEN "type2"
          IN ('Tarif de la Vente Flash','Lightning Deal Fee','Blitzangebotsgebühr', 'Tarifa de Oferta flash', 'Tariffa dell’Offerta Lampo') THEN
               'Lightning Deal Fee'
          ELSE "type2"
        END, '')                                   AS "event_type"
           ,"description" AS "actual_description"
           ,REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE( REPLACE( "description",
                                'Ã¼','ü'),
                                'Ã³','ó'),
                                'Ã©','é'),
                                'Ãœ','Ü'),
                                'Ã–','Ö'),
                                'Ã¶','ö'),
                                'LogÃs', 'Logís'),
                                'lâ€™O','l’O'),
                                'pubblicitÃ','pubblicità'),
                                'Ã…','Å'),
                                'LogÃ­stica','Logística'),
                                'LogÃstica','Logística'),
                                'Coupon-Einlösungsgebühr', 'Coupon Redemption Fee'),
                                'Frais d\'échange de coupon', 'Coupon Redemption Fee')
           AS "description2"
       , COALESCE(CASE
            WHEN "description2" LIKE 'Costo della pubblici%' THEN 'Cost of Advertising'
            WHEN "description2" LIKE 'Frais de retour Expédié par Amazon%' THEN 'FBA Return Fee'
            WHEN "description2" LIKE 'Frais de transport entran%' THEN 'Inbound Transportation Fee'
            WHEN "description2" LIKE 'Frais de disposition Expédié par Amazon%' THEN 'FBA Disposal Fee'
            WHEN "description2" LIKE 'Frais de Stockage de longue durée Expédié par Amazon%' THEN 'FBA Long-Term Storage Fee'
            WHEN "description2" LIKE 'Tariffa di restituzione Logistica di Amazon%' THEN 'FBA Return Fee'
            WHEN "description2" LIKE 'Frais d�étiquetage%' THEN 'FBA Prep Fee: Labeling'
            WHEN "description2" LIKE 'Frais d’étiquetage' THEN 'FBA Prep Fee: Labeling'
            WHEN "description2" LIKE 'Frais de dépassement de stock%' THEN 'Inventory Storage Overage Fee'
            WHEN "description2" LIKE 'Frais de préparations supplémentaires%' THEN 'FBA Prep Fee: Labeling'
            WHEN "description2" LIKE 'Gebühr für nicht eingeplante Serviceleistungen%' THEN 'FBA Prep Fee: Labeling'
            WHEN "description2" LIKE 'Solicitud de retiro de inventario de Logística de Amazon: tarifa por baj%' THEN 'FBA Removal Fee'
            WHEN "description2" LIKE 'CoÃ»t de la publicité%' THEN 'Cost of Advertising'
            WHEN "description2"
          IN ('Frais de stockage Expédié par Amazon','Versand durch Amazon Lagergebühr',
              'Tariffa di stoccaggio Logistica di Amazon','Tarifa de almacenamiento de Logística de Amazon',
              'Tarifas de almacenamiento de Logística de Amazon',
              'FBA storage fee','Avgift för FBA-lagerförvaring','FBA Inventory Storage Fee') THEN
                  'FBA Inventory Storage Fee'
          WHEN "description2"
          IN ('Cost of Advertising','Prix de la publicité',
              'Werbekosten','Costo della pubblicità','Costo della pubblicità','Gastos de publicidad','Kosten van reclame',
              'Remboursement pour le publicitaire','Refund for Advertiser','Costo de la publicidad','Kostnad för reklam') THEN
                  'Cost of Advertising'
          WHEN "description2"
          IN ('Frais de dépassement de stock','Inventory Storage Overage Fee',
              'Lagergebühr für Überbestand','Tarifa por exceso de almacenamiento de inventario',
              'Tariffa di stoccaggio inventario in eccesso') THEN
                  'Inventory Storage Overage Fee'
          WHEN "description2"
          IN ('Frais de Stockage de longue durée Expédié par Amazon','Tarifa por almacenamiento prolongado',
              'Tariffa di stoccaggio a lungo termine Logistica di Amazon','Versand durch Amazon Langzeitlagergebühr') THEN
                  'FBA Long-Term Storage Fee'
          WHEN "description2"
          IN ('FBA Amazon-Partnered Carrier Shipment Fee','Frachtkosten für den Transport zum Amazon-Versandzentrum',
              'Gebühr für die Teilnahme am Amazon Transportpartner-Programm') THEN
                  'FBA Amazon-Partnered Carrier Shipment Fee'
          WHEN "description2"
          IN ('FBA Disposal Fee','Frais de disposition Expédié par Amazon','Tarifa por eliminación de inventario',
          'Tariffa di smaltimento Logistica di Amazon','Versand durch Amazon Gebühr für Entsorgung') THEN
                  'FBA Disposal Fee'
          WHEN "description2"
          IN ('FBA Prep Fee: Labeling','Frais de préparations supplémentairesÂ : code-barres Amazon manquant...','Frais d’étiquetage',
          'Gebühr für nicht eingeplante Serviceleistungen â€“ Amazon-Strichcode fehlt...','Gebühr für Etikettierung',
          'Commissione per servizio non previsto - Mancanza del codice a barre di Amazon') THEN
                  'FBA Prep Fee: Labeling'
          WHEN "description2"
          IN ('FBA Removal Order: Disposal Fee','Solicitud de retiro de inventario de Logística de Amazon: tarifa por baja (eliminación)...') THEN
                  'FBA Removal Order: Disposal Fee'
          WHEN "description2"
          IN ('FBA Return Fee','Frais de retour Expédié par Amazon','Tarifa por devolución de inventario',
          'Versand durch Amazon Gebühr für Rücksendung','Amazon Tariffa di restituzione Logistica di Amazon') THEN
                  'FBA Return Fee'
          WHEN "description2"
          IN ('Inbound Transportation Fee','Frais de transport entrant','Frais de transport entrantÂ','Tarifa de transporte de envíos a Amazon',
          'Tarifa de transporte de envíos a Amazon','Tariffa di spedizione in entrata') THEN
                  'Inbound Transportation Fee'
          WHEN "description2"
          IN ('Inbound Transportation Program Fee','Tariffa programma trasporti in entrata','Frais du service de transport entrant') THEN
                  'Inbound Transportation Program Fee'
          WHEN "description2"
          IN ('Desembolso fallido') THEN 'Failed Disbursement'
            ELSE "description2"
        END,'')                                AS "description"
        , "marketplace"			            AS "marketplace" -- domanin
        , "account_type"		            AS "account_type"
        , "fulfillment_channel" 			AS "fulfillment_channel"
        , "order_city"			            AS "order_city"
        , "order_state"			            AS "order_state"
        , "order_postal"		            AS "order_postal"
        , "tax_collection_model"			AS "tax_collection_model"
        , "quantity"                        AS "quantity"
        ,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("product sales") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("product sales", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "product sales" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("product sales") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("product sales", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("product sales") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("product sales", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "product sales" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("product sales") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("product sales", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("product sales", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("product sales", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("product sales", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("product sales", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("product sales" AS DOUBLE)
END, 0.0) AS "product sales"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("product sales tax") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("product sales tax", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "product sales tax" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("product sales tax") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("product sales tax", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("product sales tax") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("product sales tax", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "product sales tax" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("product sales tax") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("product sales tax", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("product sales tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("product sales tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("product sales tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("product sales tax", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("product sales tax" AS DOUBLE)
END, 0.0) AS "product sales tax"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("shipping credits") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("shipping credits", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "shipping credits" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("shipping credits") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("shipping credits", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("shipping credits") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("shipping credits", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "shipping credits" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("shipping credits") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("shipping credits", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("shipping credits", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("shipping credits", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("shipping credits", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("shipping credits", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("shipping credits" AS DOUBLE)
END, 0.0) AS "shipping credits"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("shipping credits tax") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("shipping credits tax", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "shipping credits tax" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("shipping credits tax") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("shipping credits tax", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("shipping credits tax") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("shipping credits tax", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "shipping credits tax" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("shipping credits tax") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("shipping credits tax", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("shipping credits tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("shipping credits tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("shipping credits tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("shipping credits tax", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("shipping credits tax" AS DOUBLE)
END, 0.0) AS "shipping credits tax"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("gift wrap credits") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("gift wrap credits", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "gift wrap credits" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("gift wrap credits") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("gift wrap credits", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("gift wrap credits") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("gift wrap credits", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "gift wrap credits" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("gift wrap credits") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("gift wrap credits", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("gift wrap credits", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("gift wrap credits", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("gift wrap credits", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("gift wrap credits", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("gift wrap credits" AS DOUBLE)
END, 0.0) AS "gift wrap credits"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("giftwrap credits tax") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("giftwrap credits tax", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "giftwrap credits tax" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("giftwrap credits tax") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("giftwrap credits tax", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("giftwrap credits tax") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("giftwrap credits tax", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "giftwrap credits tax" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("giftwrap credits tax") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("giftwrap credits tax", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("giftwrap credits tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("giftwrap credits tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("giftwrap credits tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("giftwrap credits tax", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("giftwrap credits tax" AS DOUBLE)
END, 0.0) AS "giftwrap credits tax"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("regulatory fee") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("regulatory fee", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "regulatory fee" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("regulatory fee") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("regulatory fee", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("regulatory fee") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("regulatory fee", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "regulatory fee" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("regulatory fee") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("regulatory fee", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("regulatory fee", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("regulatory fee", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("regulatory fee", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("regulatory fee", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("regulatory fee" AS DOUBLE)
END, 0.0) AS "regulatory fee"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("tax on regulatory fee") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("tax on regulatory fee", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "tax on regulatory fee" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("tax on regulatory fee") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("tax on regulatory fee", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("tax on regulatory fee") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("tax on regulatory fee", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "tax on regulatory fee" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("tax on regulatory fee") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("tax on regulatory fee", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("tax on regulatory fee", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("tax on regulatory fee", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("tax on regulatory fee", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("tax on regulatory fee", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("tax on regulatory fee" AS DOUBLE)
END, 0.0) AS "tax on regulatory fee"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("promotional rebates") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("promotional rebates", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "promotional rebates" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("promotional rebates") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("promotional rebates", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("promotional rebates") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("promotional rebates", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "promotional rebates" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("promotional rebates") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("promotional rebates", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("promotional rebates", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("promotional rebates", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("promotional rebates", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("promotional rebates", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("promotional rebates" AS DOUBLE)
END, 0.0) AS "promotional rebates"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("promotional rebates tax") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("promotional rebates tax", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "promotional rebates tax" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("promotional rebates tax") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("promotional rebates tax", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("promotional rebates tax") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("promotional rebates tax", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "promotional rebates tax" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("promotional rebates tax") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("promotional rebates tax", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("promotional rebates tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("promotional rebates tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("promotional rebates tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("promotional rebates tax", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("promotional rebates tax" AS DOUBLE)
END, 0.0) AS "promotional rebates tax"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("marketplace withheld tax") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("marketplace withheld tax", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "marketplace withheld tax" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("marketplace withheld tax") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("marketplace withheld tax", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("marketplace withheld tax") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("marketplace withheld tax", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "marketplace withheld tax" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("marketplace withheld tax") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("marketplace withheld tax", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("marketplace withheld tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("marketplace withheld tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("marketplace withheld tax", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("marketplace withheld tax", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("marketplace withheld tax" AS DOUBLE)
END, 0.0) AS "marketplace withheld tax"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("selling fees") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("selling fees", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "selling fees" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("selling fees") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("selling fees", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("selling fees") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("selling fees", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "selling fees" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("selling fees") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("selling fees", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("selling fees", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("selling fees", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("selling fees", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("selling fees", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("selling fees" AS DOUBLE)
END, 0.0) AS "selling fees"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("fba fees") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("fba fees", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "fba fees" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("fba fees") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("fba fees", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("fba fees") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("fba fees", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "fba fees" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("fba fees") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("fba fees", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("fba fees", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("fba fees", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("fba fees", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("fba fees", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("fba fees" AS DOUBLE)
END, 0.0) AS "fba fees"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("other transaction fees") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("other transaction fees", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "other transaction fees" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("other transaction fees") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("other transaction fees", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("other transaction fees") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("other transaction fees", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "other transaction fees" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("other transaction fees") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("other transaction fees", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("other transaction fees", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("other transaction fees", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("other transaction fees", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("other transaction fees", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("other transaction fees" AS DOUBLE)
END, 0.0) AS "other transaction fees"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("other") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("other", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "other" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("other") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("other", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("other") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("other", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "other" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("other") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("other", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("other", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("other", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("other", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("other", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("other" AS DOUBLE)
END, 0.0) AS "other"
,
COALESCE(CASE
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("total") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("total", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                                   ,2, LENGTH( "total" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('DE', 'IT', 'NL', 'PL', 'ES', 'SE', 'TR') AND UNICODE("total") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("total", '.', ''),'?', '-'),',','.'),'~?','-'),'−','-'),'âˆ’','-')
                     AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("total") = 8722  THEN
              -  TRY_CAST( SUBSTR( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("total", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                                   ,2, LENGTH( "total" ) - 1 )
                            AS DOUBLE)
       WHEN "country_code" in ('BE', 'FR') AND UNICODE("total") != 8722  THEN
              TRY_CAST( REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("total", '.', ''),'?', ''),',','.'),'~?',''),'−','-')
                     AS DOUBLE)
       WHEN "country_code" in ('US', 'MX', 'CA') THEN
              TRY_CAST(REPLACE(REPLACE("total", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('UK') THEN
              TRY_CAST(REPLACE(REPLACE("total", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" in ('AE') THEN
              TRY_CAST(REPLACE(REPLACE("total", ',', ''),'?', '-') AS DOUBLE)
       WHEN "country_code" = 'BR' THEN
              TRY_CAST(REPLACE("total", ',', '.') AS DOUBLE)
       ELSE TRY_CAST("total" AS DOUBLE)
END, 0.0) AS "total"
FROM DWH_DEV.SANDBOX.AMAZON_SETTLEMENTS_TRANSACTIONS_AIRBYTE
-- FROM dwh.staging.AMAZON_SETTLEMENTS_TRANSACTIONS t
-- where to_date("report_date") between '2024-09-01' and '2024-09-30'
)
, settlements AS (
    --sum up to get at daily level
    SELECT
        -- to_date("report_date") 		    AS "report_date"
        "report_date" 		    AS "report_date"
        , "settlement_id"		            AS "settlement_id"
        , "seller_id"		                AS "seller_id"
        , "country_code"                    AS "country_code"
        , "event_type"
        , "order_id"		                AS "order_id"
        , "sku"                             AS "sku"
        , "description"
        ,
        CASE
            WHEN LENGTH("marketplace") > 0
                THEN "marketplace"
            ELSE 'amazon'
        END             		            AS "marketplace"
        , "account_type"		            AS "account_type"
        , "fulfillment_channel" 			AS "fulfillment_channel"
        , "order_city"			            AS "order_city"
        , "order_state"			            AS "order_state"
        , "order_postal"		            AS "order_postal"
        , "tax_collection_model"			AS "tax_collection_model"
        , LISTAGG(distinct "actual_type", ',')       AS "actual_type"
        , LISTAGG(distinct "actual_description", ',') AS "actual_description"
        , MAX("etl_batch_runtime")			AS "etl_batch_runtime"
        , SUM("quantity")                   AS "quantity"
        , SUM("product sales")				AS "product sales"
        , SUM("product sales tax")			AS "product sales tax"
        , SUM("shipping credits")			AS "shipping credits"
        , SUM("shipping credits tax")		AS "shipping credits tax"
        , SUM("gift wrap credits")			AS "gift wrap credits"
        , SUM("giftwrap credits tax")		AS "giftwrap credits tax"
        , SUM("regulatory fee")				AS "regulatory fee"
        , SUM("tax on regulatory fee")		AS "tax on regulatory fee"
        , SUM("promotional rebates")		AS "promotional rebates"
        , SUM("promotional rebates tax")	AS "promotional rebates tax"
        , SUM("marketplace withheld tax")	AS "marketplace withheld tax"
        , SUM("selling fees")				AS "selling fees"
        , SUM("fba fees")					AS "fba fees"
        , SUM("other transaction fees")		AS "other transaction fees"
        , SUM("other")						AS "other"
        , SUM("total")						AS "total"
FROM raw_settlements
GROUP BY
-- to_date("report_date")
        "report_date"
        , "settlement_id"
        , "seller_id"
        , "country_code"
        , "event_type"
        , "order_id"
        , "sku"
        , "description"
        , "marketplace"
        , "account_type"
        , "fulfillment_channel"
        , "order_city"
        , "order_state"
        , "order_postal"
        , "tax_collection_model"
)
select * from settlements;
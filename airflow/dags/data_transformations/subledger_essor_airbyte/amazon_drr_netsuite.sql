CREATE OR REPLACE TABLE DWH_DEV.SANDBOX.AMAZON_DRR_NETSUITE AS
with
settlements_with_3pl_data as (  --  TODO: Make this gsheet data to table
    select *,
    CASE
        WHEN "sku" in ('OT873_B2B','PLD48FL-6_B2B','PHS48-6_B2B','PMSCC16-1_B2B','PHS48-6-B2B') then '3PL'
        ELSE 'FBA'
    END AS "fulfillment_type"
    from DWH_DEV.SANDBOX.SP_API_AMAZON_DRR_CONSOLIDATED_CLEAN
)
, branded_sku_brand_item_id_mapping as (
    SELECT DISTINCT
        brand,
        item_id,
        LOWER(alias_sku) AS alias_sku,
        netsuite_item_type,
        netsuite_item_number,
        class_id
    FROM dwh.raw.bq_netsuite_items
    QUALIFY ROW_NUMBER() OVER (PARTITION BY brand, LOWER(alias_sku) ORDER BY item_id DESC) = 1
)
-- , heyday_sku_brand_item_id_mapping as (
--     SELECT DISTINCT
--         brand_code,
--         netsuite_internal_id,
--         netsuite_item_number,
--         seller_id,
--         country_code,
--         LOWER(sku) AS alias_sku
--     FROM dwh.prod.seller_sku_item_mapping
-- )
, settlement_brand as (
    select drr.*,
        COALESCE(sku_brand_mapping.brand, '') AS "brand",
        COALESCE(CAST(sku_brand_mapping.item_id AS STRING), '') AS "sku_internal_id",
        sku_brand_mapping.netsuite_item_type AS "netsuite_item_type",
        sku_brand_mapping.netsuite_item_number AS "netsuite_item_number",
        sku_brand_mapping.class_id AS "netsuite_brand_id",
    FROM settlements_with_3pl_data AS drr
    LEFT JOIN branded_sku_brand_item_id_mapping AS sku_brand_mapping
        ON LOWER(drr."sku") = LOWER(sku_brand_mapping.alias_sku)
    -- LEFT JOIN branded_sku_brand_item_id_mapping AS hydy_sku_brand_mapping
    --     ON LOWER(drr."sku") = LOWER(hydy_sku_brand_mapping.alias_sku)
)
, branded_customers_mapping as ( -- TODO: Create the brand code, seller_name and country code, marketplace id columns
    select
        currency as "currency",
        marketplace AS "country",
        netsuite_subsidiary as "netsuite_subsidiary_name",
        netsuite_subsidiary_internal_id as "netsuite_subsidiary_internal_id",
        netsuite_customer AS "netsuite_customer_name",
        netsuite_customer_internal_id as "netsuite_customer_internal_id",
        netsuite_location as "netsuite_location",
        netsuite_location_internal_id as "netsuite_location_internal_id",
        seller_name as "seller_name",
        amz_marketplace_id as "marketplace_id",
        REPLACE(country_code, 'GB', 'UK') as "country_code",
        seller_id as "seller_id",
        fulfillment_type as "fulfillment_type"
    FROM DWH.RAW.BQ_SELLER_SUBSIDIARY_MAPPING
    QUALIFY ROW_NUMBER() OVER (PARTITION BY seller_id, amz_marketplace_id ORDER BY _airbyte_extracted_at DESC) = 1
)
-- , heyday_customer_mapping as (
-- )
, settlement_customer as (
    select
        drr."report_date"
        ,drr."settlement_id"
        ,drr."seller_id"
        ,drr."country_code"
        ,drr."event_type"
        ,drr."order_id"
        ,drr."sku"
        ,drr."description"
        ,drr."marketplace"
        ,drr."account_type"
        ,drr."fulfillment_channel"
        ,drr."order_city"
        ,drr."order_state"
        ,drr."order_postal"
        ,drr."tax_collection_model"
        ,drr."actual_type"
        ,drr."actual_description"
        ,drr."etl_batch_runtime"
        ,drr."quantity"
        ,drr."product sales"
        ,drr."product sales tax"
        ,drr."shipping credits"
        ,drr."shipping credits tax"
        ,drr."gift wrap credits"
        ,drr."giftwrap credits tax"
        ,drr."regulatory fee"
        ,drr."tax on regulatory fee"
        ,drr."promotional rebates"
        ,drr."promotional rebates tax"
        ,drr."marketplace withheld tax"
        ,drr."selling fees"
        ,drr."fba fees"
        ,drr."other transaction fees"
        ,drr."other"
        ,drr."total"
        ,drr."fulfillment_type"
        ,case
            when drr."brand" is null or drr."brand"='' then si.seller_code
            else drr."brand"
        end as "brand"
        ,"sku_internal_id"
        ,"netsuite_item_type"
        ,"netsuite_item_number"
        ,"netsuite_brand_id",
        seller."currency",
        seller."country",
        seller."netsuite_subsidiary_name",
        seller."netsuite_subsidiary_internal_id",
        seller."netsuite_customer_name",
        seller."netsuite_customer_internal_id",
        seller."netsuite_location",
        seller."netsuite_location_internal_id",
        seller."seller_name",
        seller."marketplace_id"
    from settlement_brand as drr
    left join branded_customers_mapping as seller
    ON
        seller."seller_id" = drr."seller_id"
        AND LOWER(seller."fulfillment_type") = LOWER(drr."fulfillment_type")
        AND seller."country_code" = drr."country_code"
    left join dwh.STAGING.STG_SELLER_INFO as si
    ON
        si.seller_id = drr."seller_id"
)
select * from settlement_customer;

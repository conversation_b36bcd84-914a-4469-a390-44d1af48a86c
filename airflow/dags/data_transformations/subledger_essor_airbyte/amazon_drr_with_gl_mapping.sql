CREATE OR REPLACE TABLE DWH_DEV.SANDBOX.AMAZON_DRR_WITH_GL_MAPPING AS
with
settlement_gl_mapping as (
select s."report_date"
        ,s."settlement_id"
        ,s."seller_id"
        ,s."seller_name"
        ,s."marketplace_id"
        ,s."netsuite_subsidiary_internal_id"
        ,s."netsuite_subsidiary_name"
        ,s."netsuite_customer_internal_id"
        ,s."netsuite_customer_name"
        ,trim(s."brand") AS "brand"
        ,s."netsuite_brand_id"
        ,s."country"
        ,s."country_code"
        ,s."netsuite_location"
        ,s."netsuite_location_internal_id"
        ,CASE
            WHEN g."netsuite_item_number" LIKE '%Cypher%' OR (g."netsuite_item_number" IS NULL AND s."sku" IS NOT NULL AND s."transaction_type" = 'product sales')
                THEN 'INV_ITEM'
            ELSE 'GL_ITEM'
         END AS "item_type"
        ,CASE
            WHEN g."netsuite_item_number" LIKE '%Cypher%' OR (g."netsuite_item_number" IS NULL AND s."sku" IS NOT NULL AND s."transaction_type" = 'product sales')
                THEN s."netsuite_item_number"
            ELSE g."netsuite_item_number"
         END 											 AS "netsuite_item_number"
        ,CASE WHEN g."netsuite_item_number" LIKE '%Cypher%' OR (g."netsuite_item_number" IS NULL AND s."sku" IS NOT NULL AND s."transaction_type" = 'product sales')
                THEN s."sku_internal_id"
                ELSE g."netsuite_id"
            END											 AS "netsuite_id"
        ,g."netsuite_gl" 								 AS "netsuite_gl"
        ,NULL               							 AS "netsuite_cash_sale_id"
        ,NULL               							 AS "netsuite_cash_sale_document_number"
        , CASE
            WHEN (
                COALESCE(s."order_id", '') NOT LIKE 'S0%'
                OR LOWER(COALESCE(s."marketplace", '')) LIKE 'amazon.%'
            )
                THEN 'amazon'
            WHEN (
                COALESCE(s."order_id", '') LIKE 'S0%'
                OR LOWER(COALESCE(s."marketplace", '')) LIKE 'si%'
            )
                THEN 'shopify'
            ELSE 'amazon'
        END AS "sales_channel"
        , CASE
            WHEN (
                COALESCE(s."order_id", '') NOT LIKE 'S0%'
                OR LOWER(COALESCE(s."marketplace", '')) LIKE 'amazon.%'
            )
                THEN 1
            WHEN (
                COALESCE(s."order_id", '') LIKE 'S0%'
                OR LOWER(COALESCE(s."marketplace", '')) LIKE 'si%'
            )
                THEN 4
            ELSE 1
        END AS "sales_channel_id"
        ,s."event_type"
        ,s."order_id"
        ,s."sku"
        ,s."description"
        ,s."quantity"
        ,s."marketplace"
        ,s."account_type"
        ,s."fulfillment_channel"
        ,s."order_city"
        ,s."order_state"
        ,s."order_postal"
        ,s."tax_collection_model"
        ,s."transaction_type"
        ,s."amount"
        ,s."currency"
        ,NULL AS "posted_flag"
        ,s."etl_batch_runtime"
FROM DWH_DEV.SANDBOX.AMAZON_DRR_NETSUITE s
    UNPIVOT ("amount"
        FOR "transaction_type" IN
        ("product sales","product sales tax","shipping credits","shipping credits tax","gift wrap credits",
        "giftwrap credits tax","regulatory fee","tax on regulatory fee","promotional rebates","promotional rebates tax","marketplace withheld tax",
        "selling fees","fba fees","other transaction fees","other","total"
        )
    )
LEFT JOIN dwh.staging.AMAZON_CASH_SALE_GL_ITEM_MAPPING g
    ON  trim(lower(g."type")) = CASE WHEN s."event_type" IS NULL THEN 'not used' ELSE trim(lower(s."event_type")) END
    AND trim(lower(g."marketplace")) = CASE WHEN s."marketplace" IS NULL THEN 'not used' ELSE trim(lower(s."marketplace")) END
    AND trim(lower(g."transaction_type")) = trim(lower(s."transaction_type"))
    AND trim(lower(g."description"))  = CASE
                                            WHEN lower(g."description") = 'not used'
                                                THEN 'not used'
                                            WHEN lower(s."description") LIKE 'coupon redemption fee%'
                                                THEN 'coupon redemption fee'
                                            WHEN lower(s."description") LIKE 'fba customer returns fee (non-apparel and non-shoes)%'
                                                THEN 'fba customer returns fee (non-apparel and non-shoes)'
                                            ELSE trim(lower(s."description"))
                                        END
)
select * from settlement_gl_mapping;
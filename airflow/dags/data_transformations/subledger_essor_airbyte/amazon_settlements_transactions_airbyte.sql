CREATE OR REPLACE TABLE DWH_DEV.SANDBOX.AMAZON_DRR_WITH_SUBLEDGER AS
SELECT  md5(CAST(COALESCE(CAST(s."report_date" 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s."settlement_id" 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s."seller_id" 			 AS varchar), '') || '-' ||
                 COALESCE(CAST(s."country_code" 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s."event_type" 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s."order_id" 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s."sku" 		 	 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s."description" 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s."marketplace" 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s."account_type" 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s."order_city" 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s."order_state" 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s."order_postal" 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s."tax_collection_model"  AS varchar), '') || '-' ||
                 COALESCE(CAST(s."fulfillment_channel" 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s."transaction_type" 	 AS varchar), '')
            AS varchar)) 								 AS "subledger_pk"
        ,s."report_date"
        ,s."settlement_id"
        ,s."seller_id"
        ,s."netsuite_brand_id"
        ,s."brand"
        ,s."seller_name"
        ,s."marketplace_id"
        ,s."netsuite_subsidiary_internal_id"
        ,s."netsuite_subsidiary_name"
        ,s."netsuite_customer_internal_id"
        ,s."netsuite_customer_name"
        ,s."country"
        ,s."country_code"
        ,s."netsuite_location"
        ,s."netsuite_location_internal_id"
        ,s."item_type"
        ,s."netsuite_item_number"
        ,s."netsuite_id"
        ,s."netsuite_gl"
        ,md5(CAST(COALESCE(CAST(s."report_date" 		            AS varchar), '') || '-' ||
                  COALESCE(CAST(s."settlement_id" 		            AS varchar), '') || '-' ||
                  COALESCE(CAST(s."country_code" 		            AS varchar), '') || '-' ||
                  COALESCE(CAST(s."netsuite_customer_internal_id"   AS varchar), '') || '-' ||
                  COALESCE(CAST(s."netsuite_location_internal_id"   AS varchar), '')
                AS varchar)) 								        AS "netsuite_cash_sale_post_key"
        ,md5(CAST(COALESCE(CAST(s."report_date" 		            AS varchar), '') || '-' ||
                    COALESCE(CAST(s."settlement_id" 		        AS varchar), '') || '-' ||
                    COALESCE(CAST(s."netsuite_brand_id" 	        AS varchar), '') || '-' ||
                    COALESCE(CAST(s."netsuite_customer_internal_id" AS varchar), '') || '-' ||
                    COALESCE(CAST(s."netsuite_location_internal_id" AS varchar), '') || '-' ||
                    COALESCE(CAST(s."netsuite_item_number" 		    AS varchar), '')
                AS varchar)) 								        AS "subledger_key"
        ,s."netsuite_cash_sale_id"
        ,s."netsuite_cash_sale_document_number"
        ,s."event_type"
        ,s."order_id"
        ,s."sku"
        ,s."description"
        ,s."quantity"
        ,s."marketplace"
        ,s."account_type"
        ,s."fulfillment_channel"
        ,s."order_city"
        ,s."order_state"
        ,s."order_postal"
        ,s."tax_collection_model"
        ,s."transaction_type"
        ,s."amount"
        ,s."currency"
        ,s."posted_flag"
        ,s."etl_batch_runtime"
        ,SYSDATE()        			AS "record_created_timestamp_utc"
        ,NULL 						AS "record_updated_timestamp_utc"
        ,NULL 						AS "cash_sale_post_updated_timestamp_utc"
FROM DWH_DEV.SANDBOX.AMAZON_DRR_WITH_GL_MAPPING s;
CREATE TABLE IF NOT EXISTS $curated_db.inventory_capacity AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_inventory_capacity
    WHERE 1 = 0;

MERGE INTO
    $curated_db.inventory_capacity AS tgt
USING
    $stage_db.dedupe_inventory_capacity AS src
    
        ON 1 = 1
       AND src.pk = tgt.pk
       
WHEN MATCHED AND src.report_fetched_and_loaded_at >= tgt.report_fetched_and_loaded_at THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.scraper_id = src.scraper_id,
    tgt.seller_id = src.seller_id,
    tgt.country = src.country,
    tgt.currency_code = src.currency_code,
    tgt.marketplace_id = src.marketplace_id,
    tgt.start_date = src.start_date,
    tgt.end_date = src.end_date,
    tgt.storage_type = src.storage_type,
    tgt.period_id = src.period_id,
    tgt.period_time_status = src.period_time_status,
    tgt.request_window_end_date = src.request_window_end_date,
    tgt.default_storage_type = src.default_storage_type,
    tgt.display_unit = src.display_unit,
    tgt.is_individual_merchant = src.is_individual_merchant,
    tgt.current_usage_timestamp = src.current_usage_timestamp,
    tgt.used_percent = src.used_percent,
    tgt.used_volume = src.used_volume,
    tgt.on_hand_and_open_volume = src.on_hand_and_open_volume,
    tgt.on_hand_and_open_quantity = src.on_hand_and_open_quantity,
    tgt.on_hand_volume = src.on_hand_volume,
    tgt.on_hand_quantity = src.on_hand_quantity,
    tgt.open_shipment_volume = src.open_shipment_volume,
    tgt.open_shipment_quantity = src.open_shipment_quantity,
    tgt.maximum_shipment_volume = src.maximum_shipment_volume,
    tgt.maximum_shipment_quantity = src.maximum_shipment_quantity,
    tgt.overage_volume = src.overage_volume,
    tgt.overage_quantity = src.overage_quantity,
    tgt.displayed_estimate_overage_fee = src.displayed_estimate_overage_fee,
    tgt.total_limit = src.total_limit,
    tgt.base_limit = src.base_limit,
    tgt.base_limit_allocation_type = src.base_limit_allocation_type,
    tgt.is_transition_period = src.is_transition_period,
    tgt.mcf_limit = src.mcf_limit,
    tgt.capacity_manager_limit = src.capacity_manager_limit,
    tgt.fba_allocation_source_type_mcf = src.fba_allocation_source_type_mcf,
    tgt.fba_allocation_source_type_baseline = src.fba_allocation_source_type_baseline,
    tgt.storage_type_limit_increase_upper_bound = src.storage_type_limit_increase_upper_bound,
    tgt.is_seller_elligible = src.is_seller_elligible,
    tgt.max_deposit = src.max_deposit,
    tgt.max_pending_requests = src.max_pending_requests,
    tgt.is_request_window_closed = src.is_request_window_closed,
    tgt.fba_allocation_type = src.fba_allocation_type,
    tgt.is_charge_period = src.is_charge_period,
    tgt.your_requests_limit_increase_request_item = src.your_requests_limit_increase_request_item,
    tgt.your_requests_reservation_fee_by_storage_data = src.your_requests_reservation_fee_by_storage_data,
    tgt.your_requests_partial_cancel_fulfilled_quantity_data = src.your_requests_partial_cancel_fulfilled_quantity_data,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    currency_code,
    marketplace_id,
    start_date,
    end_date,
    storage_type,
    period_id,
    period_time_status,
    request_window_end_date,
    default_storage_type,
    display_unit,
    is_individual_merchant,
    current_usage_timestamp,
    used_percent,
    used_volume,
    on_hand_and_open_volume,
    on_hand_and_open_quantity,
    on_hand_volume,
    on_hand_quantity,
    open_shipment_volume,
    open_shipment_quantity,
    maximum_shipment_volume,
    maximum_shipment_quantity,
    overage_volume,
    overage_quantity,
    displayed_estimate_overage_fee,
    total_limit,
    base_limit,
    base_limit_allocation_type,
    is_transition_period,
    mcf_limit,
    capacity_manager_limit,
    fba_allocation_source_type_mcf,
    fba_allocation_source_type_baseline,
    storage_type_limit_increase_upper_bound,
    is_seller_elligible,
    max_deposit,
    max_pending_requests,
    is_request_window_closed,
    fba_allocation_type,
    is_charge_period,
    your_requests_limit_increase_request_item,
    your_requests_reservation_fee_by_storage_data,
    your_requests_partial_cancel_fulfilled_quantity_data,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src.report_fetched_and_loaded_at, 
    src.scraper_id, 
    src.seller_id, 
    src.country, 
    src.currency_code, 
    src.marketplace_id, 
    src.start_date, 
    src.end_date, 
    src.storage_type, 
    src.period_id, 
    src.period_time_status, 
    src.request_window_end_date, 
    src.default_storage_type, 
    src.display_unit, 
    src.is_individual_merchant, 
    src.current_usage_timestamp, 
    src.used_percent, 
    src.used_volume, 
    src.on_hand_and_open_volume, 
    src.on_hand_and_open_quantity, 
    src.on_hand_volume, 
    src.on_hand_quantity, 
    src.open_shipment_volume, 
    src.open_shipment_quantity, 
    src.maximum_shipment_volume, 
    src.maximum_shipment_quantity, 
    src.overage_volume, 
    src.overage_quantity, 
    src.displayed_estimate_overage_fee, 
    src.total_limit, 
    src.base_limit, 
    src.base_limit_allocation_type, 
    src.is_transition_period, 
    src.mcf_limit, 
    src.capacity_manager_limit, 
    src.fba_allocation_source_type_mcf, 
    src.fba_allocation_source_type_baseline, 
    src.storage_type_limit_increase_upper_bound, 
    src.is_seller_elligible, 
    src.max_deposit, 
    src.max_pending_requests, 
    src.is_request_window_closed, 
    src.fba_allocation_type, 
    src.is_charge_period, 
    src.your_requests_limit_increase_request_item, 
    src.your_requests_reservation_fee_by_storage_data, 
    src.your_requests_partial_cancel_fulfilled_quantity_data, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
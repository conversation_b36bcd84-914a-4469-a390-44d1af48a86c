CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_sourcing_cost AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(seller_id AS VARCHAR), ''), '-',
            COALESCE(CAST(country AS VARCHAR), ''), '-',
            COALESCE(CAST(asin AS VARCHAR), ''), '-',
            COALESCE(CAST(fnsku AS VARCHAR), ''), '-',
            COALESCE(CAST(msku AS VARCHAR), ''),
            COALESCE(CAST(report_fetched_and_loaded_at::date AS VARCHAR), '')
        )) AS pk,
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        country,
        asin,
        fnsku,
        msku,
        is_low_confidence_asp_cost,
        last_updated_date::text AS last_updated_date,
        rms_cost,
        rms_cost_currency_code,
        rms_cost_provided_by,
        latest_cost_status_currency_code,
        latest_cost_status_dispute_id,
        latest_cost_status_disputed_cost,
        latest_cost_status_status,
        latest_cost_status_status_reason_code,
        file_name,
        etl_batch_run_time

    FROM $raw_db.raw_sourcing_cost
    WHERE 1=1
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY report_fetched_and_loaded_at::date DESC NULLS LAST) = 1
);
CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_historical_reservation_fees AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_historical_reservation_fees
    WHERE 1 = 0;

INSERT INTO $raw_db.log_historical_reservation_fees (
    start_date,
    end_date,
    reservation_date,
    standard_size_fee,
    oversize_fee,
    extra_large_fee,
    apparel_fee,
    footwear_fee,
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    marketplace_id,
    country,
    currency_code,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        start_date,
        end_date,
        reservation_date,
        standard_size_fee,
        oversize_fee,
        extra_large_fee,
        apparel_fee,
        footwear_fee,
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        marketplace_id,
        country,
        currency_code,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_historical_reservation_fees;
CREATE TABLE IF NOT EXISTS $curated_db.sourcing_cost AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_sourcing_cost
    WHERE 1 = 0;

MERGE INTO
    $curated_db.sourcing_cost AS tgt
USING
    $stage_db.dedupe_sourcing_cost AS src

        ON 1 = 1
       AND src.pk = tgt.pk

WHEN MATCHED AND src.report_fetched_and_loaded_at >= tgt.report_fetched_and_loaded_at THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.scraper_id = src.scraper_id,
    tgt.seller_id = src.seller_id,
    tgt.country = src.country,
    tgt.asin = src.asin,
    tgt.fnsku = src.fnsku,
    tgt.msku = src.msku,
    tgt.is_low_confidence_asp_cost = src.is_low_confidence_asp_cost,
    tgt.last_updated_date = src.last_updated_date,
    tgt.rms_cost = src.rms_cost,
    tgt.rms_cost_currency_code = src.rms_cost_currency_code,
    tgt.rms_cost_provided_by = src.rms_cost_provided_by,
    tgt.latest_cost_status_currency_code = src.latest_cost_status_currency_code,
    tgt.latest_cost_status_dispute_id = src.latest_cost_status_dispute_id,
    tgt.latest_cost_status_disputed_cost = src.latest_cost_status_disputed_cost,
    tgt.latest_cost_status_status = src.latest_cost_status_status,
    tgt.latest_cost_status_status_reason_code = src.latest_cost_status_status_reason_code,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    asin,
    fnsku,
    msku,
    is_low_confidence_asp_cost,
    last_updated_date,
    rms_cost,
    rms_cost_currency_code,
    rms_cost_provided_by,
    latest_cost_status_currency_code,
    latest_cost_status_dispute_id,
    latest_cost_status_disputed_cost,
    latest_cost_status_status,
    latest_cost_status_status_reason_code,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src.report_fetched_and_loaded_at,
    src.scraper_id,
    src.seller_id,
    src.country,
    src.asin,
    src.fnsku,
    src.msku,
    src.is_low_confidence_asp_cost,
    src.last_updated_date,
    src.rms_cost,
    src.rms_cost_currency_code,
    src.rms_cost_provided_by,
    src.latest_cost_status_currency_code,
    src.latest_cost_status_dispute_id,
    src.latest_cost_status_disputed_cost,
    src.latest_cost_status_status,
    src.latest_cost_status_status_reason_code,
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_historical_reservation_fees AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(start_date AS VARCHAR), ''), '-',
            COALESCE(CAST(end_date AS VARCHAR), ''), '-',
            COALESCE(CAST(reservation_date AS VARCHAR), ''), '-'
            )) AS pk,
        start_date,
        end_date,
        reservation_date,
        TRY_TO_DECIMAL(standard_size_fee, 10, 2) AS standard_size_fee,
        TRY_TO_DECIMAL(oversize_fee, 10, 2) AS oversize_fee,
        TRY_TO_DECIMAL(extra_large_fee, 10, 2) AS extra_large_fee,
        TRY_TO_DECIMAL(apparel_fee, 10, 2) AS apparel_fee,
        TRY_TO_DECIMAL(footwear_fee, 10, 2) as footwear_fee,
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        marketplace_id,
        country,
        currency_code,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_historical_reservation_fees
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY report_fetched_and_loaded_at::date DESC NULLS LAST) = 1
);
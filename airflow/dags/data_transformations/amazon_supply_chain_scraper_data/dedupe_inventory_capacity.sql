CREATE OR <PERSON><PERSON>LACE TRANSIENT TABLE $stage_db.dedupe_inventory_capacity AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(storage_type AS VARCHAR), ''), '-',
            COALESCE(CAST(start_date AS VARCHAR), ''), '-',
            COALESCE(CAST(end_date AS VARCHAR), ''), '-',
            COALESCE(CAST(seller_id AS VARCHAR), '')
            )) AS pk,
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        country,
        currency_code,
        marketplace_id,
        start_date,
        end_date,
        storage_type,
        period_id,
        period_time_status,
        request_window_end_date,
        default_storage_type,
        display_unit,
        is_individual_merchant,
        current_usage_timestamp::text::timestamp_ntz AS current_usage_timestamp,
        used_percent,
        used_volume,
        on_hand_and_open_volume,
        on_hand_and_open_quantity,
        on_hand_volume,
        on_hand_quantity,
        open_shipment_volume,
        open_shipment_quantity,
        maximum_shipment_volume,
        maximum_shipment_quantity,
        overage_volume,
        overage_quantity,
        displayed_estimate_overage_fee,
        total_limit,
        base_limit,
        base_limit_allocation_type,
        is_transition_period,
        mcf_limit,
        capacity_manager_limit,
        fba_allocation_source_type_mcf,
        fba_allocation_source_type_baseline,
        storage_type_limit_increase_upper_bound,
        is_seller_elligible,
        max_deposit,
        max_pending_requests,
        is_request_window_closed,
        fba_allocation_type,
        is_charge_period,
        your_requests_limit_increase_request_item,
        your_requests_reservation_fee_by_storage_data,
        your_requests_partial_cancel_fulfilled_quantity_data,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_inventory_capacity
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY report_fetched_and_loaded_at::date DESC NULLS LAST) = 1
);
CREATE TABLE IF NOT EXISTS $curated_db.historical_reservation_fees AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_historical_reservation_fees
    WHERE 1 = 0;

MERGE INTO
    $curated_db.historical_reservation_fees AS tgt
USING
    $stage_db.dedupe_historical_reservation_fees AS src
    
        ON 1 = 1
       AND src.pk = tgt.pk
       
WHEN MATCHED AND src.report_fetched_and_loaded_at >= tgt.report_fetched_and_loaded_at THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt.start_date = src.start_date,
    tgt.end_date = src.end_date,
    tgt.reservation_date = src.reservation_date,
    tgt.standard_size_fee = src.standard_size_fee,
    tgt.oversize_fee = src.oversize_fee,
    tgt.extra_large_fee = src.extra_large_fee,
    tgt.apparel_fee = src.apparel_fee,
    tgt.footwear_fee = src.footwear_fee,
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.scraper_id = src.scraper_id,
    tgt.seller_id = src.seller_id,
    tgt.marketplace_id = src.marketplace_id,
    tgt.country = src.country,
    tgt.currency_code = src.currency_code,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    start_date,
    end_date,
    reservation_date,
    standard_size_fee,
    oversize_fee,
    extra_large_fee,
    apparel_fee,
    footwear_fee,
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    marketplace_id,
    country,
    currency_code,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src.start_date, 
    src.end_date, 
    src.reservation_date, 
    src.standard_size_fee, 
    src.oversize_fee, 
    src.extra_large_fee, 
    src.apparel_fee, 
    src.footwear_fee, 
    src.report_fetched_and_loaded_at, 
    src.scraper_id, 
    src.seller_id, 
    src.marketplace_id, 
    src.country, 
    src.currency_code, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_sourcing_cost AS
    SELECT
         *
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_sourcing_cost
    WHERE 1 = 0;

INSERT INTO $raw_db.log_sourcing_cost (
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    asin,
    fnsku,
    msku,
    is_low_confidence_asp_cost,
    last_updated_date,
    rms_cost,
    rms_cost_currency_code,
    rms_cost_provided_by,
    latest_cost_status_currency_code,
    latest_cost_status_dispute_id,
    latest_cost_status_disputed_cost,
    latest_cost_status_status,
    latest_cost_status_status_reason_code,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc
)
    SELECT
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        country,
        asin,
        fnsku,
        msku,
        is_low_confidence_asp_cost,
        last_updated_date,
        rms_cost,
        rms_cost_currency_code,
        rms_cost_provided_by,
        latest_cost_status_currency_code,
        latest_cost_status_dispute_id,
        latest_cost_status_disputed_cost,
        latest_cost_status_status,
        latest_cost_status_status_reason_code,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM
    $raw_db.raw_sourcing_cost;
CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.raw_fba_reserved_inventory_report_log 
AS
SELECT *, '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
FROM $raw_db.raw_fba_reserved_inventory_report
WHERE 1 = 0;

DELETE FROM $raw_db.raw_fba_reserved_inventory_report_log tgt
WHERE EXISTS (SELECT 1 
              FROM $raw_db.raw_fba_reserved_inventory_report src 
              WHERE tgt.file_name=src.file_name
             )
; 

INSERT INTO $raw_db.raw_fba_reserved_inventory_report_log
(
  reportrequesttime,
  reportenddate,
  reportstartdate,
  _daton_batch_id,
  _daton_batch_runtime,
  _daton_user_id,
  asin,
  fnsku,
  marketplaceid,
  marketplacename,
  product_name,
  reserved_customerorders,
  reserved_fc_processing,
  reserved_fc_transfers,
  reserved_qty,
  sellingpartnerid,
  sku,
  file_name,
  etl_batch_run_time,
  log_timestamp_utc
)
SELECT
  reportrequesttime,
  reportenddate,
  reportstartdate,
  _daton_batch_id,
  _daton_batch_runtime,
  _daton_user_id,
  asin,
  fnsku,
  marketplaceid,
  marketplacename,
  product_name,
  reserved_customerorders,
  reserved_fc_processing,
  reserved_fc_transfers,
  reserved_qty,
  sellingpartnerid,
  sku,
  file_name,
  etl_batch_run_time, 
  SYSDATE() AS log_timestamp_utc
FROM
  $raw_db.raw_fba_reserved_inventory_report;

CREATE TABLE IF NOT EXISTS $stage_db.fba_reserved_inventory_report_p AS
SELECT
  *
  , '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc
  , '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
FROM 
  $stage_db.dedupe_fba_reserved_inventory_report
WHERE 
  1 = 0;

MERGE INTO $stage_db.fba_reserved_inventory_report_p AS tgt
USING $stage_db.dedupe_fba_reserved_inventory_report AS src
   ON 1 = 1
  AND src.snapshot_date = tgt.snapshot_date
  AND src.seller_id = tgt.seller_id
  AND src.marketplace_id = tgt.marketplace_id
  AND src.asin = tgt.asin
  AND src.fnsku = tgt.fnsku
  AND src.sku = tgt.sku
WHEN MATCHED AND src.daton_batch_runtime >= tgt.daton_batch_runtime THEN
UPDATE SET
  tgt.connector_region = src.connector_region,
  tgt.report_start_date = src.report_start_date,
  tgt.report_end_date = src.report_end_date,
  tgt.report_request_time = src.report_request_time,
  tgt.snapshot_date = src.snapshot_date,
  tgt.daton_batch_id = src.daton_batch_id,
  tgt.daton_batch_runtime = src.daton_batch_runtime,
  tgt.daton_user_id = src.daton_user_id,
  tgt.asin = src.asin,
  tgt.fnsku = src.fnsku,
  tgt.marketplace_id = src.marketplace_id,
  tgt.marketplace_name = src.marketplace_name,
  tgt.country = src.country,
  tgt.product_name = src.product_name,
  tgt.reserved_customerorders = src.reserved_customerorders,
  tgt.reserved_fc_processing = src.reserved_fc_processing,
  tgt.reserved_fc_transfers = src.reserved_fc_transfers,
  tgt.reserved_qty = src.reserved_qty,
  tgt.seller_id = src.seller_id,
  tgt.sku = src.sku,
  tgt.file_name = src.file_name,
  tgt.etl_batch_run_time = src.etl_batch_run_time,
  tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
  connector_region,
  report_start_date,
  report_end_date,
  report_request_time,
  snapshot_date,
  daton_batch_id,
  daton_batch_runtime,
  daton_user_id,
  asin,
  fnsku,
  marketplace_id,
  marketplace_name,
  country,
  product_name,
  reserved_customerorders,
  reserved_fc_processing,
  reserved_fc_transfers,
  reserved_qty,
  seller_id,
  sku,
  file_name,
  etl_batch_run_time,
  record_created_timestamp_utc,
  record_updated_timestamp_utc
)
VALUES
(
  src.connector_region,
  src.report_start_date,
  src.report_end_date,
  src.report_request_time,
  src.snapshot_date,
  src.daton_batch_id,
  src.daton_batch_runtime,
  src.daton_user_id,
  src.asin,
  src.fnsku,
  src.marketplace_id,
  src.marketplace_name,
  src.country,
  src.product_name,
  src.reserved_customerorders,
  src.reserved_fc_processing,
  src.reserved_fc_transfers,
  src.reserved_qty,
  src.seller_id,
  src.sku,
  src.file_name,
  src.etl_batch_run_time,
  SYSDATE(),
  SYSDATE()
);

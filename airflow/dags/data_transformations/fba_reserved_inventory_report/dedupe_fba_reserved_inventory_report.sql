CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_fba_reserved_inventory_report
AS 
SELECT *
FROM
( 
  SELECT
    SPLIT_PART(SPLIT_PART(file_name, '/',  7), '_', 4) as connector_region,
    CASE WHEN connector_region = 'UK' THEN 'GB' ELSE connector_region END AS country,
    sellingpartnerid as seller_id,
    reportrequesttime as report_request_time,
    reportstartdate AS report_start_date,
    reportenddate as report_end_date,
    reportrequesttime::DATE as snapshot_date,
    asin,
    fnsku,
    REPLACE(sku,'amp\073') as sku,
    marketplaceid as marketplace_id,
    marketplacename as marketplace_name,
    product_name,
    reserved_customerorders,
    reserved_fc_processing,
    reserved_fc_transfers,
    reserved_qty,
    file_name,
    etl_batch_run_time,
    _daton_batch_id AS daton_batch_id,
    _daton_batch_runtime AS daton_batch_runtime,
    _daton_user_id AS daton_user_id
  FROM 
    $raw_db.raw_fba_reserved_inventory_report
) T
QUALIFY 
  ROW_NUMBER()OVER(PARTITION BY 
                     snapshot_date, 
                     seller_id, 
                     marketplace_id,
                     asin, 
                     fnsku, 
                     sku
           ORDER BY daton_batch_runtime DESC NULLS LAST) = 1
;

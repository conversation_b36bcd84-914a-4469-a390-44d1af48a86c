MERGE INTO $curated_db.fact_amazon_deal_eligibility AS tgt
USING (
    WITH netsuite_items AS (
        SELECT DISTINCT
            asin
            , alias_market_country AS country_code
            , brand AS brand_code
        FROM netsuite.netsuite.netsuite_items
        WHERE asin IS NOT NULL
            AND LENGTH(brand) = 3
    )
    SELECT
        de.recommendation_id
      , de.seller_id
      , de.asin
      , de.sku
      , de.included
      , de.start_time
      , de.end_time
      , de.start_time_utc
      , de.end_time_utc
      , de.deal_type
      , de.min_deal_price
      , de.max_deal_price
      , de.deal_price
      , de.your_price
      , de.min_deal_quantity
      , de.discount_per_unit
      , de.min_deal_fee / NULLIFZERO(COUNT(DISTINCT de.asin) OVER (
            PARTITION BY de.seller_id, de.marketplace_id, de.deal_type, de.recommendation_id, de.start_time, de.end_time)) AS min_deal_fee
      , de.max_deal_fee / NULLIFZERO(COUNT(DISTINCT de.asin) OVER (
            PARTITION BY de.seller_id, de.marketplace_id, de.deal_type, de.recommendation_id, de.start_time, de.end_time)) AS max_deal_fee
      , de.ineligibility_reasons
      , de.featured_asin
      , de.parent_asin
      , de.country
      , de.marketplace_id
      , de.scraper_id
      , de.event_name
      , de.file_name
      , de.report_fetched_and_loaded_at
      , de.etl_batch_run_time
      , de.record_created_timestamp_utc
      , de.record_updated_timestamp_utc
      , seller.seller_name
      , (CASE WHEN UPPER(seller.is_multi_brand) = 'NO'
              THEN seller.seller_code
              ELSE COALESCE(bm.brand_code, ni.brand_code)
        END) AS brand_code_updated
      , im.netsuite_item_number
      , eligibility_audience
      , max_duration
    FROM $stage_db.stg_scrapped_deal_eligibility AS de
    LEFT JOIN dwh.staging.stg_seller_info AS seller
        ON de.seller_id = seller.seller_id
    LEFT JOIN dwh.prod.marketplace_asin_brand_mapping AS bm
        ON de.asin = bm.asin
       AND UPPER(de.country) = UPPER(bm.country_code)
    LEFT JOIN netsuite_items ni
        ON ni.asin = de.asin
        AND UPPER(ni.country_code) = CASE
                                        WHEN UPPER(de.country) IN ('BE', 'DE', 'ES', 'FR', 'IT', 'NL', 'PL', 'SE') THEN 'EU'
                                        ELSE UPPER(de.country)
                                     END
    LEFT JOIN dwh.prod.asin_item_mapping AS im
        ON de.asin = im.asin
       AND UPPER(de.country) = UPPER(im.country_code)
       AND UPPER(brand_code_updated) = UPPER(im.brand_code)
    WHERE de.record_updated_timestamp_utc > TO_TIMESTAMP_NTZ('$start_ts')
) AS src
    ON src.seller_id = tgt.seller_id
   AND src.marketplace_id = tgt.marketplace_id
   AND src.deal_type = tgt.deal_type
   AND src.sku = tgt.sku
   AND src.asin = tgt.asin
   AND src.parent_asin = tgt.parent_asin
   AND src.start_time = tgt.start_time
   AND src.end_time = tgt.end_time
WHEN MATCHED AND tgt.report_fetched_and_loaded_at < src.report_fetched_and_loaded_at THEN
UPDATE SET
    tgt.brand_code = src.brand_code_updated
  , tgt.seller_name = src.seller_name
  , tgt.recommendation_id = src.recommendation_id
  , tgt.sku = src.sku
  , tgt.asin = src.asin
  , tgt.included = src.included
  , tgt.netsuite_item_number = src.netsuite_item_number
  , tgt.deal_type = src.deal_type
  , tgt.start_time = src.start_time
  , tgt.end_time = src.end_time
  , tgt.start_time_utc = src.start_time_utc
  , tgt.end_time_utc = src.end_time_utc
  , tgt.min_deal_price = src.min_deal_price
  , tgt.max_deal_price = src.max_deal_price
  , tgt.deal_price = src.deal_price
  , tgt.your_price = src.your_price
  , tgt.min_deal_quantity = src.min_deal_quantity
  , tgt.discount_per_unit = src.discount_per_unit
  , tgt.min_deal_fee = src.min_deal_fee
  , tgt.max_deal_fee = src.max_deal_fee
  , tgt.ineligibility_reasons = src.ineligibility_reasons
  , tgt.featured_asin = src.featured_asin
  , tgt.parent_asin = src.parent_asin
  , tgt.country = src.country
  , tgt.marketplace_id = src.marketplace_id
  , tgt.seller_id = src.seller_id
  , tgt.scraper_id = src.scraper_id
  , tgt.event_name = src.event_name
  , tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at
  , tgt.etl_batch_run_time = src.etl_batch_run_time
  , tgt.record_updated_timestamp_utc = SYSDATE()
  , tgt.eligibility_audience = src.eligibility_audience
  , tgt.max_duration = src.max_duration
WHEN NOT MATCHED THEN
INSERT
(
    brand_code
  , seller_name
  , recommendation_id
  , asin
  , sku
  , included
  , netsuite_item_number
  , deal_type
  , start_time
  , end_time
  , start_time_utc
  , end_time_utc
  , min_deal_price
  , max_deal_price
  , deal_price
  , your_price
  , min_deal_quantity
  , discount_per_unit
  , min_deal_fee
  , max_deal_fee
  , ineligibility_reasons
  , featured_asin
  , parent_asin
  , country
  , marketplace_id
  , seller_id
  , scraper_id
  , event_name
  , report_fetched_and_loaded_at
  , etl_batch_run_time
  , record_created_timestamp_utc
  , record_updated_timestamp_utc
  , eligibility_audience
  , max_duration
)
VALUES
(
    src.brand_code_updated
  , src.seller_name
  , src.recommendation_id
  , src.asin
  , src.sku
  , src.included
  , src.netsuite_item_number
  , src.deal_type
  , src.start_time
  , src.end_time
  , src.start_time_utc
  , src.end_time_utc
  , src.min_deal_price
  , src.max_deal_price
  , src.deal_price
  , src.your_price
  , src.min_deal_quantity
  , src.discount_per_unit
  , src.min_deal_fee
  , src.max_deal_fee
  , src.ineligibility_reasons
  , src.featured_asin
  , src.parent_asin
  , src.country
  , src.marketplace_id
  , src.seller_id
  , src.scraper_id
  , src.event_name
  , src.report_fetched_and_loaded_at
  , src.etl_batch_run_time
  , SYSDATE()
  , SYSDATE()
  , src.eligibility_audience
  , src.max_duration
);

DELETE FROM $curated_db.fact_amazon_deal_eligibility
WHERE CONCAT(seller_id, marketplace_id) IN (SELECT DISTINCT CONCAT(seller_id, marketplace_id) FROM $raw_db.raw_scrapped_deal_eligibility)
  AND recommendation_id NOT IN (SELECT DISTINCT recommendation_id FROM $raw_db.raw_scrapped_deal_eligibility);

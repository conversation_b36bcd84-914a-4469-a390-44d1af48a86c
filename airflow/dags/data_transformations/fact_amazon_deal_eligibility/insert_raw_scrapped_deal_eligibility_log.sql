INSERT INTO $raw_db.raw_scrapped_deal_eligibility_log (
	recommendation_id
  , sku
  , featured_asin
  , parent_asin
  , asin
  , included
  , deal_type
  , epoch_ms_start_date
  , epoch_ms_end_date
  , min_deal_price
  , max_deal_price
  , deal_price
  , your_price
  , min_deal_quantity
  , min_deal_fee
  , max_deal_fee
  , ineligibility_reasons
  , scraper_id
  , seller_id
  , report_fetched_and_loaded_at
  , country
  , marketplace_id
  , file_name
  , etl_batch_run_time
  , log_timestamp_utc
  , event_name
  , eligibility_audience
  , max_duration
)
SELECT
	recommendation_id
  , sku
  , featured_asin
  , parent_asin
  , asin
  , included
  , deal_type
  , epoch_ms_start_date
  , epoch_ms_end_date
  , min_deal_price
  , max_deal_price
  , deal_price
  , your_price
  , min_deal_quantity
  , min_deal_fee
  , max_deal_fee
  , ineligibility_reasons
  , scraper_id
  , seller_id
  , report_fetched_and_loaded_at
  , country
  , marketplace_id
  , file_name
  , etl_batch_run_time
  , SYSDATE() AS log_timestamp_utc 
  , event_name
  , eligibility_audience
  , max_duration
FROM $raw_db.raw_scrapped_deal_eligibility;

DELETE FROM $raw_db.raw_scrapped_deal_eligibility_log WHERE DATE(log_timestamp_utc) < CURRENT_DATE - 90;

MERGE INTO $stage_db.stg_scrapped_deal_eligibility AS tgt
USING (
    WITH events_calendar AS (
        SELECT
            o.value::TEXT AS event_name
          , country_code  
          , event_start_timestamp
          , event_end_timestamp
        FROM $curated_db.events_calendar AS e
          , LATERAL FLATTEN(input=>other_event_names) AS o
        UNION
        SELECT
            event_name
          , country_code
          , event_start_timestamp
          , event_end_timestamp
        FROM $curated_db.events_calendar
    )
    SELECT
        recommendation_id
      , sku
      , featured_asin
      , parent_asin
      , asin
      , included
      , deal_type
      , CASE
            WHEN e.event_name IS NOT NULL AND epoch_ms_start_date IS NULL
                THEN e.event_start_timestamp
            ELSE CONVERT_TIMEZONE('UTC', $curated_db.get_timezone_name(r.country), TO_TIMESTAMP_NTZ(TO_NUMBER(epoch_ms_start_date), 3))
        END AS start_time
      , CASE
            WHEN e.event_name IS NOT NULL AND epoch_ms_end_date IS NULL
                THEN e.event_end_timestamp
            ELSE CONVERT_TIMEZONE('UTC', $curated_db.get_timezone_name(r.country), TO_TIMESTAMP_NTZ(TO_NUMBER(epoch_ms_end_date), 3))
        END AS end_time
      , CASE
            WHEN e.event_name IS NOT NULL AND epoch_ms_start_date IS NULL
                THEN CONVERT_TIMEZONE($curated_db.get_timezone_name(e.country_code), 'UTC', e.event_start_timestamp)
            ELSE TO_TIMESTAMP_NTZ(TO_NUMBER(epoch_ms_start_date), 3)
        END AS start_time_utc
      , CASE
            WHEN e.event_name IS NOT NULL AND epoch_ms_end_date IS NULL
                THEN CONVERT_TIMEZONE($curated_db.get_timezone_name(e.country_code), 'UTC', e.event_end_timestamp)
            ELSE TO_TIMESTAMP_NTZ(TO_NUMBER(epoch_ms_end_date), 3)
        END AS end_time_utc
      , min_deal_price
      , max_deal_price
      , deal_price
      , your_price
      , min_deal_quantity
      , min_deal_fee
      , max_deal_fee
      , ineligibility_reasons::VARIANT AS ineligibility_reasons
      , scraper_id
      , seller_id
      , report_fetched_and_loaded_at
      , country
      , marketplace_id
      , file_name
      , etl_batch_run_time
      , r.event_name
      , eligibility_audience
      , max_duration
    FROM $raw_db.raw_scrapped_deal_eligibility AS r
    LEFT JOIN events_calendar AS e 
        ON UPPER(r.event_name) = UPPER(e.event_name)
       AND UPPER(r.country) = UPPER(e.country_code)
       AND YEAR(r.report_fetched_and_loaded_at) = YEAR(e.event_start_timestamp)
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY seller_id, marketplace_id, recommendation_id, deal_type, sku, asin, start_time, end_time::DATE
        ORDER BY report_fetched_and_loaded_at DESC) = 1
    ) AS src
        ON src.seller_id = tgt.seller_id
       AND src.marketplace_id = tgt.marketplace_id
       AND src.recommendation_id = tgt.recommendation_id
       AND src.deal_type = tgt.deal_type
       AND src.sku = tgt.sku
       AND src.asin = tgt.asin
       AND src.start_time = tgt.start_time
       AND src.end_time = tgt.end_time
WHEN MATCHED AND src.report_fetched_and_loaded_at > tgt.report_fetched_and_loaded_at THEN
UPDATE SET
    tgt.recommendation_id = src.recommendation_id
  , tgt.seller_id = src.seller_id
  , tgt.asin = src.asin
  , tgt.included = src.included
  , tgt.sku = src.sku
  , tgt.start_time = src.start_time
  , tgt.end_time = src.end_time
  , tgt.start_time_utc = src.start_time_utc
  , tgt.end_time_utc = src.end_time_utc
  , tgt.deal_type = src.deal_type
  , tgt.min_deal_price = TO_DECIMAL(src.min_deal_price, 10, 2)
  , tgt.max_deal_price = TO_DECIMAL(src.max_deal_price, 10, 2)
  , tgt.deal_price = TO_DECIMAL(src.deal_price, 10, 2)
  , tgt.your_price = TO_DECIMAL(src.your_price, 10, 2)
  , tgt.min_deal_quantity = TO_NUMBER(src.min_deal_quantity)
  , tgt.discount_per_unit = TO_DECIMAL(src.your_price, 10, 2) - TO_DECIMAL(src.deal_price, 10, 2)
  , tgt.min_deal_fee = TO_DECIMAL(src.min_deal_fee, 10, 2)
  , tgt.max_deal_fee = TO_DECIMAL(src.max_deal_fee, 10, 2)
  , tgt.ineligibility_reasons = tgt.ineligibility_reasons
  , tgt.featured_asin = src.featured_asin
  , tgt.parent_asin = src.parent_asin
  , tgt.country = src.country
  , tgt.marketplace_id = src.marketplace_id
  , tgt.scraper_id = src.scraper_id
  , tgt.event_name = src.event_name
  , tgt.file_name = src.file_name
  , tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at
  , tgt.etl_batch_run_time = src.etl_batch_run_time
  , tgt.record_updated_timestamp_utc = SYSDATE()
  , tgt.eligibility_audience = src.eligibility_audience
  , tgt.max_duration = src.max_duration
WHEN NOT MATCHED THEN 
INSERT (
    recommendation_id
  , seller_id
  , asin
  , included
  , sku
  , start_time
  , end_time
  , start_time_utc
  , end_time_utc
  , deal_type
  , min_deal_price
  , max_deal_price
  , deal_price
  , your_price
  , min_deal_quantity
  , discount_per_unit
  , min_deal_fee
  , max_deal_fee
  , ineligibility_reasons
  , featured_asin
  , parent_asin
  , country
  , marketplace_id
  , scraper_id
  , event_name
  , file_name
  , report_fetched_and_loaded_at
  , etl_batch_run_time
  , record_created_timestamp_utc
  , record_updated_timestamp_utc
  , eligibility_audience
  , max_duration
)
VALUES
(
    src.recommendation_id
  , src.seller_id
  , src.asin
  , src.included
  , src.sku
  , src.start_time
  , src.end_time
  , src.start_time_utc
  , src.end_time_utc
  , src.deal_type
  , TO_DECIMAL(src.min_deal_price, 10, 2)
  , TO_DECIMAL(src.max_deal_price, 10, 2)
  , TO_DECIMAL(src.deal_price, 10, 2)
  , TO_DECIMAL(src.your_price, 10, 2)
  , TO_NUMBER(src.min_deal_quantity)
  , TO_DECIMAL(src.your_price, 10, 2) - TO_DECIMAL(src.deal_price, 10, 2)
  , TO_DECIMAL(src.min_deal_fee, 10, 2)
  , TO_DECIMAL(src.max_deal_fee, 10, 2)
  , src.ineligibility_reasons
  , src.featured_asin
  , src.parent_asin
  , src.country
  , src.marketplace_id
  , src.scraper_id
  , src.event_name
  , src.file_name
  , src.report_fetched_and_loaded_at
  , src.etl_batch_run_time
  , SYSDATE()
  , SYSDATE()
  , src.eligibility_audience
  , src.max_duration
);

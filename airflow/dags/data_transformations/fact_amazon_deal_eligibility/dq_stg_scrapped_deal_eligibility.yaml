---
test_id: "check_pk_duplicate"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1) > 0 THEN 2 ELSE 0 END as "result"
  FROM (
    SELECT 1
    FROM $stage_db.stg_scrapped_deal_eligibility
    GROUP BY seller_id, marketplace_id, recommendation_id, deal_type, sku, asin, parent_asin, start_time, end_time
    HAVING COUNT(1) > 1
    LIMIT 1
  ) T;
---
test_id: "check_business_logic"
enabled: true
query: |
  SELECT CASE WHEN (COUNT(*) = 0) THEN 0 ELSE 2 END AS "result" 
  FROM $stage_db.stg_scrapped_deal_eligibility
  WHERE start_time > end_time
     OR max_deal_price < min_deal_price
     OR max_deal_fee < min_deal_fee;
---
test_id: "check_null"
enabled: true
query: |
  SELECT CASE WHEN (COUNT(*) = 0) THEN 0 ELSE 2 END AS "result" 
  FROM $stage_db.stg_scrapped_deal_eligibility
  WHERE start_time IS NULL
     OR end_time IS NULL;

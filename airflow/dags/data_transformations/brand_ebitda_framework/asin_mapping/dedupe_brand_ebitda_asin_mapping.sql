CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_brand_ebitda_asin_mapping AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(child_asin AS VARCHAR), ''), '-',
            COALESCE(CAST(parent_asin AS VARCHAR), ''), '-',
            COALESCE(CAST(country_code AS VARCHAR), ''), '-',
            COALESCE(CAST(sku AS VARCHAR), '')
            )) AS pk,
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        sku,
        link,
        brand,
        niche,
        source,
        category,
        child_asin,
        npd_status,
        parent_asin,
        country_code,
        product_name,
        product_type,
        account_title,
        brand_manager,
        brand_grouping,
        product_status,
        inventory_status,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_brand_ebitda_asin_mapping
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY _airbyte_extracted_at DESC NULLS LAST) = 1
);
CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_brand_ebitda_asin_mapping AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_brand_ebitda_asin_mapping
    WHERE 1 = 0;

INSERT INTO $raw_db.log_brand_ebitda_asin_mapping (
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    sku,
    link,
    brand,
    niche,
    source,
    category,
    child_asin,
    npd_status,
    parent_asin,
    country_code,
    product_name,
    product_type,
    account_title,
    brand_manager,
    brand_grouping,
    product_status,
    inventory_status,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        sku,
        link,
        brand,
        niche,
        source,
        category,
        child_asin,
        npd_status,
        parent_asin,
        country_code,
        product_name,
        product_type,
        account_title,
        brand_manager,
        brand_grouping,
        product_status,
        inventory_status,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_brand_ebitda_asin_mapping;
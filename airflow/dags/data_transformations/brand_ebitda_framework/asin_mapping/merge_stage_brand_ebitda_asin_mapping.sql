CREATE TABLE IF NOT EXISTS $stage_db.merge_brand_ebitda_asin_mapping AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_brand_ebitda_asin_mapping
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_brand_ebitda_asin_mapping AS tgt
USING
    $stage_db.dedupe_brand_ebitda_asin_mapping AS src
    
        ON 1 = 1
       AND src.pk = tgt.pk
       
WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._airbyte_raw_id = src._airbyte_raw_id,
    tgt._airbyte_extracted_at = src._airbyte_extracted_at,
    tgt._airbyte_generation_id = src._airbyte_generation_id,
    tgt._airbyte_meta = src._airbyte_meta,
    tgt.sku = src.sku,
    tgt.link = src.link,
    tgt.brand = src.brand,
    tgt.niche = src.niche,
    tgt.source = src.source,
    tgt.category = src.category,
    tgt.child_asin = src.child_asin,
    tgt.npd_status = src.npd_status,
    tgt.parent_asin = src.parent_asin,
    tgt.country_code = src.country_code,
    tgt.product_name = src.product_name,
    tgt.product_type = src.product_type,
    tgt.account_title = src.account_title,
    tgt.brand_manager = src.brand_manager,
    tgt.brand_grouping = src.brand_grouping,
    tgt.product_status = src.product_status,
    tgt.inventory_status = src.inventory_status,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    sku,
    link,
    brand,
    niche,
    source,
    category,
    child_asin,
    npd_status,
    parent_asin,
    country_code,
    product_name,
    product_type,
    account_title,
    brand_manager,
    brand_grouping,
    product_status,
    inventory_status,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._airbyte_raw_id, 
    src._airbyte_extracted_at, 
    src._airbyte_generation_id, 
    src._airbyte_meta, 
    src.sku, 
    src.link, 
    src.brand, 
    src.niche, 
    src.source, 
    src.category, 
    src.child_asin, 
    src.npd_status, 
    src.parent_asin, 
    src.country_code, 
    src.product_name, 
    src.product_type, 
    src.account_title, 
    src.brand_manager, 
    src.brand_grouping, 
    src.product_status, 
    src.inventory_status, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
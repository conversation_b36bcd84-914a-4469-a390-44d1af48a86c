CREATE OR REPLACE TABLE $stage_db.brand_ebitda_all_channel_markets_brand_level_ops_daily AS


WITH sku_brand_map AS (
SELECT 
    distinct
    "sku", 
    "brand"
FROM SANDBOX.OPS.SKU_ITEM_MAPPING
QUALIFY  ROW_NUMBER() OVER( PARTITION BY "sku" ORDER BY "brand" DESC) =1
),

sku_item_number_map AS (
SELECT 
    distinct
    "sku", 
    "item_number",
    "item_name"
FROM SANDBOX.OPS.SKU_ITEM_MAPPING
QUALIFY  ROW_NUMBER() OVER( PARTITION BY "sku" ORDER BY "brand" DESC) =1
),

daily_channel_data_aggregation AS (
    SELECT 
        "marketplace", 
        combine_brand,


        "country_code",
        channel,
        
        DATE_TRUNC('DAY', purchase_hour_datetime) AS purchase_date,
        
        SUM(total_quantity) AS total_quantity,
        SUM(total_item_price_lc) AS total_item_price_lc,
        SUM(total_item_price_usd) AS total_item_price_usd,
        SUM(total_shipping_price_lc) AS total_shipping_price_lc,
        SUM(total_shipping_price_usd) AS total_shipping_price_usd,
        SUM(total_item_promo_discount) AS total_item_promo_discount,
        SUM(total_item_promo_discount_usd) AS total_item_promo_discount_usd,
        SUM(total_ship_promo_discount) AS total_ship_promo_discount,
        SUM(total_ship_promo_discount_usd) AS total_ship_promo_discount_usd,
        SUM(total_giftwrap_price_lc) AS total_giftwrap_price_lc,
        SUM(total_giftwrap_price_usd) AS total_giftwrap_price_usd,
        SUM(total_ordered_product_sales_lc) AS total_ordered_product_sales_lc,
        SUM(total_ordered_product_sales_usd) AS total_ordered_product_sales_usd,
        SUM(cancelled_order_count) AS cancelled_order_count, 
        SUM(total_order_count) AS total_order_count

    FROM DWH_DEV.STAGING.brand_ebitda_all_markets_item_level_ops_hourly
    GROUP BY 
        "marketplace", 
        combine_brand,
        "country_code",
        channel,
        DATE_TRUNC('DAY', purchase_hour_datetime)
), 


time_based_daily_aggregation AS (
    SELECT 
        *,

        -- 7 Days Average Total Ordered Product Sales (USD)
        COALESCE(AVG(total_ordered_product_sales_usd) 
                 OVER (PARTITION BY combine_brand, channel, "marketplace", "country_code" 
                       ORDER BY purchase_date 
                       ROWS BETWEEN 6 PRECEDING AND CURRENT ROW), 0) AS avg_7_days_sales_usd,
     

 -- MTD (Month-to-Date) Ordered Product Sales (USD)
        COALESCE(SUM(total_ordered_product_sales_usd) 
                 OVER (PARTITION BY combine_brand, channel, "marketplace", "country_code", 
                              DATE_TRUNC('month', purchase_date)
                       ORDER BY purchase_date), 0) AS mtd_ordered_product_sales_usd,

        -- QTD (Quarter-to-Date) Ordered Product Sales (USD)
        COALESCE(SUM(total_ordered_product_sales_usd) 
                 OVER (PARTITION BY combine_brand, channel, "marketplace", "country_code", 
                              DATE_TRUNC('quarter', purchase_date)
                       ORDER BY purchase_date), 0) AS qtd_ordered_product_sales_usd

    FROM daily_channel_data_aggregation
    
),


last_year_aggregation AS (
SELECT 
b1.*,

-- Last Year Same Day Sales (USD)
        COALESCE(b2.total_ordered_product_sales_usd,0) AS last_year_same_day_sales_usd,
   -- 7 Days Average of Last Year Same Date Total Ordered Product Sales (USD)
        COALESCE(AVG(b2.total_ordered_product_sales_usd) 
                 OVER (PARTITION BY b2.combine_brand, b2.channel, b2. "marketplace", b2."country_code" 
                       ORDER BY b1.purchase_date 
                       ROWS BETWEEN 6 PRECEDING AND CURRENT ROW), 0) AS avg_7_days_last_year_sales_usd, 

 -- MTD (Month-to-Date) Last Year Same Date Ordered Product Sales (USD)
        COALESCE(SUM(b2.total_ordered_product_sales_usd) 
                 OVER (PARTITION BY b2.combine_brand, b2.channel, b2."marketplace", b2."country_code", 
                              DATE_TRUNC('month', b2.purchase_date)
                       ORDER BY b2.purchase_date), 0) AS mtd_last_year_sales_usd,

        -- QTD (Quarter-to-Date) Last Year Same Date Ordered Product Sales (USD)
        COALESCE(SUM(b2.total_ordered_product_sales_usd) 
                 OVER (PARTITION BY b2.combine_brand, b2.channel, b2."marketplace", b2."country_code", 
                              DATE_TRUNC('quarter', b2.purchase_date)
                       ORDER BY b2.purchase_date), 0) AS qtd_last_year_sales_usd

        
FROM time_based_daily_aggregation b1
        LEFT JOIN time_based_daily_aggregation b2
        ON b1.combine_brand = b2.combine_brand
       AND b1."marketplace" = b2."marketplace"
       AND b1."country_code" = b2."country_code"
       AND b1.channel = b2.channel
       AND b1.purchase_date = DATEADD(year, 1, b2.purchase_date)

QUALIFY row_number() OVER(PARTITION BY b1.combine_brand, b1."marketplace",b1."country_code", b1.channel,  b1.purchase_date ORDER BY b1.combine_brand desc) = 1
),

-- channel level aggregation 
channel_level_data_aggregation AS (

SELECT 
    "marketplace", 
    channel, 
    combine_brand,

    purchase_date,
    
    -- Aggregated metrics
    SUM(total_quantity) AS total_quantity,
    SUM(total_item_price_lc) AS total_item_price_lc,
    SUM(total_item_price_usd) AS total_item_price_usd,
    SUM(total_shipping_price_lc) AS total_shipping_price_lc,
    SUM(total_shipping_price_usd) AS total_shipping_price_usd,
    SUM(total_item_promo_discount) AS total_item_promo_discount,
    SUM(total_item_promo_discount_usd) AS total_item_promo_discount_usd,
    SUM(total_ship_promo_discount) AS total_ship_promo_discount,
    SUM(total_ship_promo_discount_usd) AS total_ship_promo_discount_usd,
    SUM(total_giftwrap_price_lc) AS total_giftwrap_price_lc,
    SUM(total_giftwrap_price_usd) AS total_giftwrap_price_usd,
    SUM(total_ordered_product_sales_lc) AS total_ordered_product_sales_lc,
    SUM(total_ordered_product_sales_usd) AS total_ordered_product_sales_usd,

    -- Aggregated calculated metrics
    SUM(avg_7_days_sales_usd) AS avg_7_days_sales_usd,
    SUM(mtd_ordered_product_sales_usd) AS mtd_ordered_product_sales_usd,
    SUM(qtd_ordered_product_sales_usd) AS qtd_ordered_product_sales_usd,
    
    SUM(last_year_same_day_sales_usd) AS last_year_same_day_sales_usd,
    SUM(avg_7_days_last_year_sales_usd) AS avg_7_days_last_year_sales_usd,
    SUM(mtd_last_year_sales_usd) AS mtd_last_year_sales_usd,
    SUM(qtd_last_year_sales_usd) AS qtd_last_year_sales_usd, 

    SUM(cancelled_order_count) AS cancelled_order_count, 
    SUM(total_order_count) AS total_order_count

FROM last_year_aggregation AS ly
GROUP BY 
    "marketplace", 
    channel, 
    combine_brand,
    purchase_date
),



ops_with_tagrets_brand_category_map AS (

SELECT 
opst.*, 
cat_map.brand AS brand_name, 
cat_map.category AS brand_category

FROM channel_level_data_aggregation AS opst
LEFT JOIN DWH_DEV.STAGING.BRAND_EBITDA_BRAND_CODE_CATEGORY_MAP AS cat_map
ON opst.combine_brand = cat_map.brand_code

)


SELECT *
FROM ops_with_tagrets_brand_category_map


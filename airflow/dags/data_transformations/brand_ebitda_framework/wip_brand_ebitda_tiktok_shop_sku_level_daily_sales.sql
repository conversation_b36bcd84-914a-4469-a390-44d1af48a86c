CREATE OR REPLACE TABLE $stage_db.wip_brand_ebitda_tiktok_shop_sku_level_daily_sales AS

WITH tiktok_orders AS (
    SELECT 
        o.*,
        CONVERT_TIMEZONE('UTC','America/Sitka', o.PAID_AT)::DATE AS purchase_date,
        --COALESCE(-c.LAST_ITEM_RATE, 0.35 * COALESCE(o.SKU_SALE_PRICE, 0),0) AS PER_UNIT_COGS,
        CASE
            WHEN IS_SAMPLE_ORDER = FALSE THEN 
                CASE 
                    WHEN (QUANTITY - COALESCE(RETURN_QUANTITY, 0)) < 0 THEN 0 
                    ELSE (QUANTITY - COALESCE(RETURN_QUANTITY, 0)) 
                END
            ELSE 0
        END AS TOTAL_UNITS_WITHOUT_SAMPLE,

          CASE 
        WHEN IS_SAMPLE_ORDER = TRUE THEN 0 
        ELSE (QUANTITY * SKU_SALE_PRICE) + SHIPPING_FEE
    END AS SALES,

        CONVERT_TIMEZONE('UTC','America/Sitka',"PAID_AT")::DATE || '-' || "BRAND_CODE" || '-' || "SELLER_SKU" || '-' || "QUANTITY" AS UNIQUE_ID
    FROM dwh.prod.fact_tiktok_orders o
    --LEFT JOIN COGS_DATA c ON c.ITEM_CLEANED = o.SELLER_SKU
    WHERE CONVERT_TIMEZONE('UTC','America/Sitka', o.PAID_AT)::DATE >= '2025-01-01'
)

SELECT  
    o.purchase_date,
    'Tiktok Shop' AS marketplace, 
    'Tiktok Shop' AS channel,
    o.marketplace_country_code AS country_code,
    o.brand_code,
    COALESCE(o.asin, m.asin) AS asin,
    m.product_name,
    o.seller_sku AS sku, 
    sum(o.TOTAL_UNITS_WITHOUT_SAMPLE) AS total_quantity, 
    sum(o.sales) AS total_ordered_product_sales_usd
FROM tiktok_orders o
LEFT JOIN DWH_DEV.STAGING.stg_brand_ebitda_sku_asin_map_orders_source_table m
ON o.seller_sku = m.sku
AND o.marketplace_country_code = m.country_code
GROUP BY  o.purchase_date, marketplace, channel, o.marketplace_country_code, o.brand_code, COALESCE(o.asin, m.asin), m.product_name, o.seller_sku
ORDER by 1 desc
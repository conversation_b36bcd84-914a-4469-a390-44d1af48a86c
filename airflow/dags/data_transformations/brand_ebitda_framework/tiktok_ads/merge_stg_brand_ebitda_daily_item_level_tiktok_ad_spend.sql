CREATE TABLE IF NOT EXISTS $stage_db.merge_stg_brand_ebitda_daily_item_level_tiktok_ad_spend AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.create_stg_brand_ebitda_daily_item_level_tiktok_ad_spend
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_stg_brand_ebitda_daily_item_level_tiktok_ad_spend AS tgt
USING
    (
        SELECT
            *
        FROM $stage_db.create_stg_brand_ebitda_daily_item_level_tiktok_ad_spend
        QUALIFY ROW_NUMBER() OVER (PARTITION BY pk ORDER BY date_day DESC) = 1 -- dedupe
    ) AS src
        ON tgt.pk = src.pk

WHEN MATCHED THEN
    -- Update the record if it already exists
    UPDATE SET
        tgt.perc_share = src.perc_share,
        tgt.total_tiktok_ad_spend = src.total_tiktok_ad_spend,
        tgt.record_updated_timestamp_utc = SYSDATE()  -- Update the timestamp

WHEN NOT MATCHED THEN
    -- Insert a new record if it does not exist
    INSERT (
        pk,
        date_day,
        brand_code,
        asin,
        country_code,
        channel,
        perc_share,
        total_tiktok_ad_spend,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        src.pk,
        src.date_day,
        src.brand_code,
        src.asin,
        src.country_code,
        src.channel,
        src.perc_share,
        src.total_tiktok_ad_spend,
        SYSDATE(),
        SYSDATE()
    );

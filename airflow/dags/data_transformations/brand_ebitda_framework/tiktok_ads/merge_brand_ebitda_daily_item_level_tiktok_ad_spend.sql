CREATE TABLE IF NOT EXISTS $curated_db.brand_ebitda_daily_item_level_tiktok_ad_spend (
    pk VARCHAR,
    report_date DATE,
    brand_code VARCHAR,
    asin VARCHAR,
    country_code VARCHAR,
    channel VARCHAR,
    daily_tiktok_ad_spend FLOAT,
    record_created_timestamp_utc TIMESTAMP DEFAULT SYSDATE(),
    record_updated_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);

BEGIN TRANSACTION;

MERGE INTO $curated_db.brand_ebitda_daily_item_level_tiktok_ad_spend AS t
USING $stage_db.merge_stg_brand_ebitda_daily_item_level_tiktok_ad_spend AS s
ON
    t.pk = s.pk

WHEN MATCHED THEN
    UPDATE SET
        t.daily_tiktok_ad_spend = s.total_tiktok_ad_spend
        , t.record_updated_timestamp_utc = SYSDATE()

WHEN NOT MATCHED THEN
    INSERT (
        pk
        , report_date
        , brand_code
        , asin
        , country_code
        , channel
        , daily_tiktok_ad_spend
        , record_created_timestamp_utc
        , record_updated_timestamp_utc
    ) VALUES (
        s.pk
        , s.date_day
        , s.brand_code
        , s.asin
        , s.country_code
        , s.channel
        , s.total_tiktok_ad_spend
        , SYSDATE()
        , SYSDATE()
    );

DELETE FROM $curated_db.brand_ebitda_daily_item_level_tiktok_ad_spend
WHERE (pk, record_updated_timestamp_utc) NOT IN (
    SELECT pk, MAX(record_updated_timestamp_utc)
    FROM $curated_db.brand_ebitda_daily_item_level_tiktok_ad_spend
    GROUP BY pk
);

COMMIT;

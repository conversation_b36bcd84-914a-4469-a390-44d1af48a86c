CREATE OR REPLACE TRANSIENT TABLE $stage_db.create_stg_brand_ebitda_daily_item_level_tiktok_ad_spend AS

-- all accounts have USD as currency so no fx conversion needed
WITH raw_data_with_marketing_spend_allocation AS (
    SELECT
        tiktok.date_day
        , tiktok.brand_code
        , tiktok.campaign_name
        , mktg_split.channel
        , mktg_split.perc_share
        , SUM(COALESCE(tiktok.spend, 0)) * mktg_split.perc_share AS total_spend
    FROM
        DWH.PROD.FACT_TIKTOK_ADS_REPORT_DAILY AS tiktok
    LEFT JOIN
        $stage_db.merge_brand_ebitda_marketing_split AS mktg_split
            ON tiktok.brand_code = mktg_split.brand_code
            AND DATE_TRUNC("month", tiktok.date_day) = mktg_split.month_start_date
    WHERE
        mktg_split.marketing_activity = 'Paid Social Media TikTok'
    GROUP BY ALL
),

brand_spend_allocation_non_tts AS (
    SELECT
        date_day
        , brand_code
        , channel
        , perc_share
        , SUM(COALESCE(total_spend, 0)) AS total_spend
    FROM
        raw_data_with_marketing_spend_allocation
    WHERE
        campaign_name NOT ILIKE '%tts%' -- remove filter when adding tiktok shop channel
    GROUP BY ALL
),

brand_spend_allocation_tts AS (
    SELECT
        date_day
        , brand_code
        , channel
        , perc_share
        , SUM(COALESCE(total_spend, 0)) AS total_spend
    FROM
        raw_data_with_marketing_spend_allocation
    WHERE
        campaign_name ILIKE '%tts%' -- filter only tiktok shop campaigns that should be attributed to tiktok
    GROUP BY ALL
),

-- amz us
tiktok_spend_with_asin_attribution_amz_us AS (
    SELECT
        raw_data.date_day
        , raw_data.brand_code
        , asin_revenue.asin
        , asin_revenue.country_code
        , raw_data.channel
        , raw_data.perc_share
        , COALESCE(raw_data.total_spend * asin_revenue.asin_brand_revenue_share, 0) AS total_tiktok_ad_spend
    FROM
        brand_spend_allocation_non_tts AS raw_data
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON raw_data.date_day = asin_revenue.report_date
            AND raw_data.brand_code = asin_revenue.brand_code
    WHERE
        LOWER(raw_data.channel) = 'amz-us'
        AND asin_revenue.country_code = 'US'
),

-- amazon int
-- add CTE similar to this for other channels
tiktok_spend_with_asin_attribution_amz_int AS (
    SELECT
        raw_data.date_day
        , raw_data.brand_code
        , asin_revenue.asin
        , asin_revenue.country_code
        , raw_data.channel
        , raw_data.perc_share
        , COALESCE(raw_data.total_spend * asin_revenue.asin_brand_revenue_share, 0) AS total_tiktok_ad_spend
    FROM
        brand_spend_allocation_non_tts AS raw_data
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON raw_data.date_day = asin_revenue.report_date
            AND raw_data.brand_code = asin_revenue.brand_code
    WHERE
        LOWER(raw_data.channel) = 'amz-int'
        AND asin_revenue.country_code != 'US'
),

all_channels_consolidated AS (
    SELECT *
    FROM tiktok_spend_with_asin_attribution_amz_us

    UNION ALL

    SELECT *
    FROM tiktok_spend_with_asin_attribution_amz_int
)

SELECT
    MD5(
        CAST(
            COALESCE(CAST(date_day AS VARCHAR), '') || '-' ||
            COALESCE(CAST(brand_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(asin AS VARCHAR), '') || '-' ||
            COALESCE(CAST(country_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(channel AS VARCHAR), '') AS VARCHAR
        )
    ) AS pk,
    *
FROM
    all_channels_consolidated
WHERE
    total_tiktok_ad_spend > 0
;
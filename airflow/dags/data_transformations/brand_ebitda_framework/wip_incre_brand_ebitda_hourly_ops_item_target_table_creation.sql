CREATE TABLE IF NOT EXISTS $stage_db.wip_incre_merge_brand_ebitda_all_markets_item_level_ops_hourly_merge (
    marketplace VARCHAR,
    channel VARCHAR, 
    brand_code VARCHAR, 
    brand_name VA<PERSON>HA<PERSON>, 
    brand_category VARCHAR, 
    sku VARCHAR,
    asin VA<PERSON>HAR,
    product_name <PERSON><PERSON><PERSON><PERSON>,
    country_code VARCHAR,
    purchase_hour_datetime TIMESTAMP,
    total_quantity FLOAT,
    item_price_lc FLOAT,
    item_price_usd FLOAT,
    shipping_price_lc FLOAT,
    shipping_price_usd FLOAT,
    item_promo_discount_lc FLOAT,
    item_promo_discount_usd FLOAT,
    ship_promo_discount_lc FLOAT,
    ship_promo_discount_usd FLOAT,
    giftwrap_price_lc FLOAT,
    giftwrap_price_usd FLOAT,
    cancelled_order_count FLOAT,
    total_order_count FLOAT,
    brand_cancellation_rate FLOAT,
    ordered_product_sales_lc FLOAT,
    ordered_product_sales_usd FLOAT, 
    last_year_total_ordered_product_sales_usd FLOAT
);

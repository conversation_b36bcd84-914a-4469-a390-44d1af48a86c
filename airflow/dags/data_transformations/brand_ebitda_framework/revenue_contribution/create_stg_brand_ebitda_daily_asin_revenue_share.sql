-- Create a target table to hold the interim data
CREATE OR REPLACE TRANSIENT TABLE $stage_db.create_stg_brand_ebitda_daily_asin_revenue_share AS

WITH netsuite_brand_mapping AS (
    SELECT DISTINCT
        asin,
        brand AS brand_code,
    FROM netsuite.netsuite.netsuite_items
    QUALIFY ROW_NUMBER() OVER (PARTITION BY asin ORDER BY netsuite_updated_at DESC) = 1
),

daily_sales AS (
    SELECT
        country_code
        , country_code AS region_code
        , brand_code
        , asin
        , report_date
        , SUM(net_revenue) AS daily_net_revenue
    FROM dwh_dev.staging.stg_brand_ebitda_amazon_asin_level_margins_all_components-- change to AMZ US prod table once promoted
    WHERE country_code = 'US'
    GROUP BY ALL

    UNION

    SELECT
        sales.country_code
        , sales.marketplace AS region_code
        , nbm.brand_code
        , sales.asin
        , sales.report_date::DATE AS report_date
        , SUM(net_revenue) AS daily_net_revenue
    FROM dwh_dev.staging.brandebitda_wbr_international_asin_level_margins_hybrid AS sales -- change to prod table once promoted
    LEFT JOIN netsuite_brand_mapping AS nbm
        ON sales.asin = nbm.asin
    WHERE sales.country_code != 'US'
    GROUP BY ALL
),

revenue_share AS (
    SELECT
        country_code
        , region_code
        , brand_code
        , asin
        , report_date
        , daily_net_revenue
        , SUM(CASE WHEN daily_net_revenue > 0 THEN daily_net_revenue ELSE 0 END) OVER (PARTITION BY asin, brand_code, report_date) AS asin_revenue
        , SUM(CASE WHEN daily_net_revenue > 0 THEN daily_net_revenue ELSE 0 END) OVER (PARTITION BY asin, brand_code, report_date, country_code) AS asin_revenue_country
        , SUM(CASE WHEN daily_net_revenue > 0 THEN daily_net_revenue ELSE 0 END) OVER (PARTITION BY asin, brand_code, report_date, region_code) AS asin_revenue_region
        , SUM(CASE WHEN daily_net_revenue > 0 THEN daily_net_revenue ELSE 0 END) OVER (PARTITION BY brand_code, report_date) AS brand_revenue
        , SUM(CASE WHEN daily_net_revenue > 0 THEN daily_net_revenue ELSE 0 END) OVER (PARTITION BY brand_code, report_date, country_code) AS brand_revenue_country
        , SUM(CASE WHEN daily_net_revenue > 0 THEN daily_net_revenue ELSE 0 END) OVER (PARTITION BY brand_code, report_date, region_code) AS brand_revenue_region

    FROM daily_sales
    QUALIFY ROW_NUMBER() OVER (
        PARTITION BY
            asin
            , brand_code
            , report_date
            , country_code
            , region_code
        ORDER BY
            report_date
    ) = 1
),

asin_brand_revenue_share AS (
    SELECT
        country_code
        , region_code
        , brand_code
        , asin
        , report_date
        , asin_revenue
        , asin_revenue_country
        , asin_revenue_region
        , brand_revenue
        , brand_revenue_country
        , brand_revenue_region
        , CASE
            WHEN region_code != 'EU' THEN DIV0NULL(asin_revenue_country, brand_revenue_country)
            ELSE DIV0NULL(asin_revenue_country, brand_revenue_region)
        END AS asin_brand_revenue_share
    FROM revenue_share
)

SELECT
    MD5(
        CAST(
            COALESCE(CAST(country_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(region_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(brand_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(asin AS VARCHAR), '') || '-' ||
            COALESCE(CAST(report_date AS VARCHAR), '') AS VARCHAR
        )
    ) AS pk
    , *
FROM asin_brand_revenue_share
WHERE ABS(asin_revenue) > 0
;

CREATE OR REPLACE TABLE $stage_db.merge_stg_brand_ebitda_daily_asin_revenue_share AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.create_stg_brand_ebitda_daily_asin_revenue_share
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_stg_brand_ebitda_daily_asin_revenue_share AS tgt
USING
    (
        SELECT
            *
        FROM $stage_db.create_stg_brand_ebitda_daily_asin_revenue_share
        QUALIFY ROW_NUMBER() OVER (PARTITION BY pk ORDER BY report_date DESC) = 1 -- dedupe
    ) AS src
        ON tgt.pk = src.pk

WHEN MATCHED THEN
    -- Update the record if it already exists
    UPDATE SET
        tgt.asin_revenue = src.asin_revenue  -- Update daily revenue
        , tgt.asin_revenue_country = src.asin_revenue_country
        , tgt.asin_revenue_region = src.asin_revenue_region
        , tgt.brand_revenue = src.brand_revenue
        , tgt.brand_revenue_country = src.brand_revenue_country
        , tgt.brand_revenue_region = src.brand_revenue_region
        , tgt.asin_brand_revenue_share = src.asin_brand_revenue_share  -- Update revenue share
        , tgt.record_updated_timestamp_utc = SYSDATE()  -- Update the timestamp

WHEN NOT MATCHED THEN
    -- Insert a new record if it does not exist
    INSERT (
        pk
        , country_code
        , region_code
        , brand_code
        , asin
        , report_date
        , asin_revenue
        , asin_revenue_country
        , asin_revenue_region
        , brand_revenue
        , brand_revenue_country
        , brand_revenue_region
        , asin_brand_revenue_share
        , record_created_timestamp_utc
        , record_updated_timestamp_utc
    ) VALUES (
        src.pk
        , src.country_code
        , src.region_code
        , src.brand_code
        , src.asin
        , src.report_date
        , src.asin_revenue
        , src.asin_revenue_country
        , src.asin_revenue_region
        , src.brand_revenue
        , src.brand_revenue_country
        , src.brand_revenue_region
        , src.asin_brand_revenue_share
        , SYSDATE()
        , SYSDATE()
    );

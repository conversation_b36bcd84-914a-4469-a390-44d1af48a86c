CREATE OR REPLACE TABLE $curated_db.brand_ebitda_daily_asin_revenue_share (
    pk VARCHAR,
    country_code VARCHAR,
    region_code VARCHAR,
    brand_code VARCHAR,
    asin VARCHAR,
    report_date DATE,
    asin_revenue FLOAT,
    asin_revenue_country FLOAT,
    asin_revenue_region FLOAT,
    brand_revenue FLOAT,
    brand_revenue_country FLOAT,
    brand_revenue_region FLOAT,
    asin_brand_revenue_share FLOAT,
    record_created_timestamp_utc TIMESTAMP DEFAULT SYSDATE(),
    record_updated_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);

BEGIN TRANSACTION;

MERGE INTO $curated_db.brand_ebitda_daily_asin_revenue_share AS t
USING $stage_db.merge_stg_brand_ebitda_daily_asin_revenue_share AS s
ON
    t.pk = s.pk

WHEN MATCHED THEN
    UPDATE SET
        t.asin_revenue = s.asin_revenue
        , t.asin_revenue_country = s.asin_revenue_country
        , t.asin_revenue_region = s.asin_revenue_region
        , t.brand_revenue = s.brand_revenue
        , t.brand_revenue_country = s.brand_revenue_country
        , t.brand_revenue_region = s.brand_revenue_region
        , t.asin_brand_revenue_share = s.asin_brand_revenue_share
        , t.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
    INSERT (
        pk
        , country_code
        , region_code
        , brand_code
        , asin
        , report_date
        , asin_revenue
        , asin_revenue_country
        , asin_revenue_region
        , brand_revenue
        , brand_revenue_country
        , brand_revenue_region
        , asin_brand_revenue_share
        , record_created_timestamp_utc
        , record_updated_timestamp_utc
    ) VALUES (
        s.pk
        , s.country_code
        , s.region_code
        , s.brand_code
        , s.asin
        , s.report_date
        , s.asin_revenue
        , s.asin_revenue_country
        , s.asin_revenue_region
        , s.brand_revenue
        , s.brand_revenue_country
        , s.brand_revenue_region
        , s.asin_brand_revenue_share
        , SYSDATE()
        , SYSDATE()
    );

DELETE FROM $curated_db.brand_ebitda_daily_asin_revenue_share
WHERE (pk, record_updated_timestamp_utc) NOT IN (
    SELECT pk, MAX(record_updated_timestamp_utc)
    FROM $curated_db.brand_ebitda_daily_asin_revenue_share
    GROUP BY pk
);

COMMIT;

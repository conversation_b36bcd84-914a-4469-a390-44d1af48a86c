CREATE OR REPLACE TABLE  $stage_db.stg_brand_ebitda_amazon_asin_level_margins AS 

WITH 
 map_latest AS (
    SELECT *
    FROM (
        SELECT
            child_asin,
            country_code,
            brand,
            category,
            niche,
            product_type,
            brand_grouping,
            brand_manager,
            account_title,
            ROW_NUMBER() OVER (
                PARTITION BY child_asin, country_code
                ORDER BY record_updated_timestamp_utc DESC,
                         record_created_timestamp_utc DESC
            ) AS rn
        FROM DWH_DEV.STAGING.merge_brand_ebitda_asin_mapping
        WHERE child_asin IS NOT NULL
    )
    WHERE rn = 1
),





asin_rev AS (
    SELECT
        s.report_date,
        s.brand_code,
        s.asin,
        s.country_code,
        m.brand                             AS brand_name,

        /* units */
        SUM(s.quantity)                              AS quantity,
        
        SUM(ABS(total_net_revenue))                           AS total_net_revenue,

        SUM(ABS(s.total_fulfillment_fees)) AS total_fulfillment_fees, 
        SUM(ABS(s.total_selling_fees)) AS total_selling_fees, 
        SUM(ABS(s.total_cogs)) AS total_cogs_fufillment_part

    FROM DWH_DEV.STAGING.stg_brandebitda_revenue_asin_level_revenue_fba_all_components AS s
    LEFT JOIN map_latest AS m 
    ON s.asin = m.child_asin 
    AND s.country_code = m.country_code 
    GROUP BY s.report_date, s.brand_code, s.asin, s.country_code, m.brand
)

/* ───────────────────────── ② ops & media cost CTEs ─────────────────── */
, amazon_storage_fees AS (
    SELECT
        report_date, country_code, asin,
        SUM( ABS(total_daily_storage_fee_usd) ) AS amazon_storage_fees
    FROM DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_AMAZON_STORAGE_FEES
    GROUP BY report_date, country_code, asin
)
, daily_3pl_fees AS (
    SELECT
        report_date, country_code, asin,
        SUM( ABS(daily_amz_3pl_fee) ) AS daily_3pl_fees
    FROM DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_3PL_FEES
    GROUP BY report_date, country_code, asin
)
, per_asin_daily_cogs AS (
    SELECT
        month,
        geography AS country_code,
        asin,
        AVG( ABS(unit_rate) ) AS unit_rate
    FROM DWH_DEV.STAGING.merge_brand_ebitda_cogs
    GROUP BY month, geography, asin
)

, map_latest AS (
    SELECT *
    FROM (
        SELECT
            child_asin,
            country_code,
            brand,
            category,
            niche,
            product_type,
            brand_grouping,
            brand_manager,
            account_title,
            ROW_NUMBER() OVER (
                PARTITION BY child_asin, country_code
                ORDER BY record_updated_timestamp_utc DESC,
                         record_created_timestamp_utc DESC
            ) AS rn
        FROM DWH_DEV.STAGING.merge_brand_ebitda_asin_mapping
        WHERE child_asin IS NOT NULL
    )
    WHERE rn = 1
)

-- Latest COGS for each ASIN‑country pair
, per_asin_latest_cogs AS (
    SELECT
        geography   AS country_code,
        asin,
        unit_rate,
        month        -- keeps the month of the COGS record (optional)
    FROM (
        SELECT
            geography,
            asin,
            unit_rate,
            month,
            ROW_NUMBER() OVER (
                PARTITION BY geography, asin
                ORDER BY month DESC
            ) AS rn
        FROM DWH_DEV.STAGING.merge_brand_ebitda_cogs
        WHERE unit_rate IS NOT NULL
    )
    WHERE rn = 1
)

/* brand-level cogs ÷ net revenue percent, month-by-month */
, brand_monthly_cogs_pct AS (
    SELECT
        cogs.month,
        map.country_code,
        map.brand,

        SUM(cogs.unit_rate * COALESCE(r.quantity, 0))        AS brand_cogs_value,
        SUM(r.total_net_revenue)                          AS brand_net_revenue,

        SUM(cogs.unit_rate * COALESCE(r.quantity, 0))
        / NULLIF(SUM(r.total_net_revenue), 0)             AS brand_cogs_pct
    FROM map_latest   AS map          -- month+asin unit cogs
    JOIN per_asin_daily_cogs AS cogs
     ON map.child_asin   = cogs.asin
     AND map.country_code = cogs.country_code
    LEFT JOIN (                       -- daily asin qty & revenue
        SELECT
            report_date,
            asin,
            country_code,
            SUM(quantity)      AS quantity,
            SUM(total_net_revenue)   AS total_net_revenue
        FROM DWH_DEV.STAGING.stg_brandebitda_revenue_asin_level_wbr_totals
        GROUP BY report_date, asin, country_code
    ) r
     ON map.child_asin         = r.asin
     AND map.country_code = r.country_code
     AND  cogs.month   = DATE_TRUNC('month', r.report_date)
    WHERE cogs.unit_rate IS NOT NULL
    GROUP BY cogs.month, map.country_code, map.brand
)

/* latest cogs-percent per brand-country */
, brand_latest_cogs_pct AS (
    SELECT country_code,
           brand,
           brand_cogs_pct
    FROM (
        SELECT
            country_code,
            brand,
            brand_cogs_pct,
            ROW_NUMBER() OVER (
                PARTITION BY country_code, brand
                ORDER BY month DESC
            ) AS rn
        FROM brand_monthly_cogs_pct
    )
    WHERE rn = 1
)




, tiktok_ads_spend AS (
    SELECT
        report_date, country_code, asin,
        SUM( ABS(daily_tiktok_ad_spend) ) AS tiktok_ads_spend
    FROM DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_TIKTOK_AD_SPEND
    GROUP BY report_date, country_code, asin
)
, daily_ugc_cost AS (
    SELECT
        report_date, country_code, asin,
        SUM( ABS(daily_ugc_cost) ) AS daily_ugc_cost
    FROM DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_UGC
    GROUP BY report_date, country_code, asin
)
, ads_cte AS (
    SELECT
        purchase_date AS report_date,
        country_code,
        asin,
        SUM(ABS(sp_ppc_spend))               AS sp_ppc_spend,
        SUM(ABS(sv_ppc_spend))              AS sv_ppc_spend,
        SUM(ABS(sbv_ppc_spend))             AS sbv_ppc_spend,
        SUM(ABS(sd_ppc_spend))               AS sd_ppc_spend,
        SUM(ABS(total_ppc_spend))            AS total_ppc_spend,
        SUM(ABS(total_asin_google_ads_spend)) AS google_ads_spend,
        SUM(ABS(total_asin_fb_ads_spend))     AS facebook_ads_spend
    FROM DWH_DEV.STAGING.brand_ebitda_daily_item_level_all_ads_cost_consolidated
    GROUP BY 1,2,3
)

, dsp_cost AS (                   -- Daily Amazon DSP spend
    SELECT
        report_date,
        country_code,
        asin,
        SUM(ABS(daily_dsp_cost_usd))   AS daily_dsp_cost_usd,
        SUM(ABS(daily_dsp_sales_usd))  AS daily_dsp_sales_usd      -- keep if needed
    FROM DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_DSP
    GROUP BY report_date, country_code, asin
)
, affiliates_cost AS (            -- Affiliate / Influencer payouts
    SELECT
        report_date,
        country_code,
        asin,
        SUM(ABS(daily_affiliate_cost)) AS daily_affiliate_cost
    FROM DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_AFFILIATES_COST
    GROUP BY report_date, country_code, asin
)
, publications_cost AS (          -- Publications / PR spend
    SELECT
        report_date,
        country_code,
        asin,
        SUM(ABS(daily_publications_cost)) AS daily_publications_cost
    FROM DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_PUBLICATIONS
    GROUP BY report_date, country_code, asin
),

daily_influencer_cost AS (
    select 
        REPORT_DATE, 
        country_code,
        asin, 
        SUM(ABS(daily_influencer_cost)) AS daily_influencer_cost
    FROM DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_INFLUENCER
        WHERE channel = 'AMZ-US'
        GROUP BY REPORT_DATE, 
        country_code,
        asin
),

deal_and_coupon_fees AS (
    select 
        purchased_date, 
        country_code,
        asin, 
        SUM(ABS(coupon_fee)) AS coupon_fee, 
        SUM(ABS(deal_fee)) AS deal_fee, 
        SUM(ABS(deal_and_coupon_fee)) AS deal_and_coupon_fee
    FROM DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_DEAL_COUPON
    GROUP BY 
        purchased_date, 
        country_code,
        asin
), 

royalty_cost AS (

    select 
        REPORT_DATE, 
        country_code, 
        ASIN, 
        SUM(ABS(daily_royalty_cost)) AS daily_royalty_cost
    FROM DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_ROYALTIES
    GROUP BY 
        REPORT_DATE, 
        country_code, 
        ASIN
),



/* ───────────────────────── ③ final calculation ─────────────────────── */
final AS (SELECT
    r.report_date,
    r.country_code,
    r.brand_code,
    r.brand_name,
    r.asin,

    /* units & revenue */
    r.quantity,
    r.total_net_revenue,

    /* Amazon fee detail */
    r.total_fulfillment_fees,
    r.total_selling_fees,

    /* ops costs */
    COALESCE(s.amazon_storage_fees, 0)                       AS amazon_storage_fees,
    COALESCE(l.daily_3pl_fees,     0)                        AS daily_3pl_fees,
    --COALESCE(c.unit_rate, lp.unit_rate, brand_avg.brand_avg_unit_cogs, 0) * r.quantity  AS total_cogs,

    COALESCE(
            c.unit_rate,                              -- asin cogs for the month
            lp.unit_rate,                             -- latest asin cogs
            CASE                                       -- brand % for the month
                WHEN ABS(r.total_net_revenue) > 0
                    AND (c.unit_rate IS NULL AND lp.unit_rate IS NULL)
                THEN (r.total_net_revenue / NULLIF(r.quantity, 0)) * bm.brand_cogs_pct
            END,
            CASE                                       -- latest brand %
                WHEN ABS(r.total_net_revenue) > 0
                    AND (c.unit_rate IS NULL AND lp.unit_rate IS NULL)
                THEN (r.total_net_revenue / NULLIF(r.quantity, 0)) * bl.brand_cogs_pct
            END,
            0
    ) * r.quantity   
    + r.total_cogs_fufillment_part AS total_cogs,

    /* media & PPC costs */
    COALESCE(a.google_ads_spend,   0)                        AS google_ads_spend,
    COALESCE(a.facebook_ads_spend, 0)                        AS facebook_ads_spend,
    COALESCE(a.sp_ppc_spend,  0)                             AS sp_ppc_spend,
    COALESCE(a.sv_ppc_spend,  0)                             AS sv_ppc_spend,
    COALESCE(a.sbv_ppc_spend, 0)                             AS sbv_ppc_spend,
    COALESCE(a.sd_ppc_spend,  0)                             AS sd_ppc_spend,
    COALESCE(a.total_ppc_spend, 0)                           AS total_ppc_spend,
    COALESCE(t.tiktok_ads_spend, 0)                          AS tiktok_ads_spend,
    COALESCE(u.daily_ugc_cost,  0)                           AS ugc_cost,


    COALESCE(d.daily_dsp_cost_usd,    0)     AS daily_dsp_cost_usd,
    COALESCE(f.daily_affiliate_cost,  0)     AS daily_affiliate_cost,
    COALESCE(p.daily_publications_cost,0)    AS daily_publications_cost,

    COALESCE(influ.daily_influencer_cost, 0) AS daily_influencer_cost, 
    COALESCE (deal.deal_fee, 0) AS deal_fee, 
    COALESCE(deal.coupon_fee, 0) AS coupon_fee, 
    COALESCE(deal.deal_and_coupon_fee, 0) AS deal_and_coupon_fee, 

    COALESCE(royalty.daily_royalty_cost, 0 ) AS daily_royalty_cost,

    /* -------- total cost -------- */
    (   COALESCE(r.total_fulfillment_fees,0)
      + COALESCE(r.total_selling_fees,0)
      + COALESCE(s.amazon_storage_fees, 0)
      + COALESCE(l.daily_3pl_fees,     0)
      + COALESCE(
        c.unit_rate,                              -- asin cogs for the month
        lp.unit_rate,                             -- latest asin cogs
        CASE                                       -- brand % for the month
            WHEN ABS(r.total_net_revenue) > 0
                 AND (c.unit_rate IS NULL AND lp.unit_rate IS NULL)
            THEN (r.total_net_revenue / NULLIF(r.quantity, 0)) * bm.brand_cogs_pct
        END,
        CASE                                       -- latest brand %
            WHEN ABS(r.total_net_revenue) > 0
                 AND (c.unit_rate IS NULL AND lp.unit_rate IS NULL)
            THEN (r.total_net_revenue / NULLIF(r.quantity, 0)) * bl.brand_cogs_pct
        END,
        0
        ) * r.quantity      


      + COALESCE(a.google_ads_spend,   0)
      + COALESCE(a.facebook_ads_spend, 0)
      + COALESCE(a.sp_ppc_spend,  0) 
      + COALESCE(a.sv_ppc_spend,  0) 
      + COALESCE(a.sbv_ppc_spend, 0) 
      + COALESCE(a.sd_ppc_spend,  0)  
      + COALESCE(t.tiktok_ads_spend,   0)
      + COALESCE(u.daily_ugc_cost,     0)
      + COALESCE(d.daily_dsp_cost_usd, 0)       
      + COALESCE(f.daily_affiliate_cost,0)       
      + COALESCE(p.daily_publications_cost,0)
      + COALESCE(influ.daily_influencer_cost, 0)
      + COALESCE(deal.deal_and_coupon_fee, 0)
      + COALESCE(royalty.daily_royalty_cost, 0 )
    ) AS total_costs,

    /* -------- contribution margin -------- */
    r.total_net_revenue
  - (  COALESCE(r.total_fulfillment_fees,0)
      + COALESCE(r.total_selling_fees,0)
      + COALESCE(s.amazon_storage_fees, 0)
      + COALESCE(l.daily_3pl_fees,     0)
      + 
            COALESCE(
        c.unit_rate,                              -- asin cogs for the month
        lp.unit_rate,                             -- latest asin cogs
        CASE                                       -- brand % for the month
            WHEN ABS(r.total_net_revenue) > 0
                 AND (c.unit_rate IS NULL AND lp.unit_rate IS NULL)
            THEN (r.total_net_revenue / NULLIF(r.quantity, 0)) * bm.brand_cogs_pct
        END,
        CASE                                       -- latest brand %
            WHEN ABS(r.total_net_revenue) > 0
                 AND (c.unit_rate IS NULL AND lp.unit_rate IS NULL)
            THEN (r.total_net_revenue / NULLIF(r.quantity, 0)) * bl.brand_cogs_pct
        END,
        0
        ) * r.quantity 
      + COALESCE(total_cogs_fufillment_part,0)  
      + COALESCE(a.google_ads_spend,   0)
      + COALESCE(a.facebook_ads_spend, 0)
      + COALESCE(a.sp_ppc_spend,  0) 
      + COALESCE(a.sv_ppc_spend,  0) 
      + COALESCE(a.sbv_ppc_spend, 0) 
      + COALESCE(a.sd_ppc_spend,  0)
      + COALESCE(t.tiktok_ads_spend,   0)
      + COALESCE(u.daily_ugc_cost,     0)
      + COALESCE(d.daily_dsp_cost_usd, 0)      
      + COALESCE(f.daily_affiliate_cost,0)    
      + COALESCE(p.daily_publications_cost,0)
      + COALESCE(influ.daily_influencer_cost, 0)
      + COALESCE(deal.deal_and_coupon_fee, 0)
      + COALESCE(royalty.daily_royalty_cost, 0 )
    ) AS contribution_margin

FROM asin_rev r

LEFT JOIN amazon_storage_fees s
       ON r.report_date   = s.report_date
      AND r.country_code  = s.country_code
      AND r.asin          = s.asin

LEFT JOIN daily_3pl_fees l
       ON r.report_date   = l.report_date
      AND r.country_code  = l.country_code
      AND r.asin          = l.asin

LEFT JOIN per_asin_daily_cogs c
       ON DATE_TRUNC('MONTH', r.report_date) = c.month
      AND r.country_code = c.country_code
      AND r.asin         = c.asin

LEFT JOIN per_asin_latest_cogs lp
       ON r.country_code = lp.country_code
      AND r.asin         = lp.asin

LEFT JOIN brand_monthly_cogs_pct    bm  -- brand % for that month
       ON DATE_TRUNC('month', r.report_date) = bm.month
      AND r.country_code = bm.country_code
      AND r.brand_name  = bm.brand

LEFT JOIN brand_latest_cogs_pct     bl  -- latest brand %
       ON r.country_code = bl.country_code
      AND r.brand_name   = bl.brand


LEFT JOIN tiktok_ads_spend t
       ON r.report_date   = t.report_date
      AND r.country_code  = t.country_code
      AND r.asin          = t.asin

LEFT JOIN daily_ugc_cost u
       ON r.report_date   = u.report_date
      AND r.country_code  = u.country_code
      AND r.asin          = u.asin

LEFT JOIN ads_cte a
       ON r.report_date   = a.report_date
      AND r.country_code  = a.country_code
      AND r.asin          = a.asin
      
LEFT JOIN dsp_cost d
       ON r.report_date  = d.report_date
      AND r.country_code = d.country_code
      AND r.asin         = d.asin

LEFT JOIN affiliates_cost f
       ON r.report_date  = f.report_date
      AND r.country_code = f.country_code
      AND r.asin         = f.asin

LEFT JOIN publications_cost p
       ON r.report_date  = p.report_date
      AND r.country_code = p.country_code
      AND r.asin         = p.asin

LEFT JOIN daily_influencer_cost influ
    ON r.report_date = influ.report_date
    AND r.country_code = influ.country_code
    AND r.asin = influ.asin

LEFT JOIN deal_and_coupon_fees deal
    ON r.report_date = deal.purchased_date
    AND r.asin  = deal.asin 
    AND r.country_code = deal.country_code

LEFT JOIN royalty_cost royalty 
    ON r.report_date = royalty.report_date 
    AND r.asin = royalty.asin
    AND r.country_code = royalty.country_code
)

select *
from final
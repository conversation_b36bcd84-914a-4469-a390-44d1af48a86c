CREATE OR R<PERSON>LACE TABLE  $stage_db.stg_brand_ebitda_amazon_asin_level_margins_all_components AS



/* ────────── CTE: day-asin-brand aggregate ────────── */
WITH asin_amazon_data_agg AS (

    SELECT
        /* ─── grouping keys ─── */
        REPORT_DAY AS report_date,
        MAPPED_ASIN AS asin,
        'US' AS country_code,

        /* ─── volumes & revenue ─── */
        SUM(QUANTITY_FROM_ORDERS)                             AS QUANTITY_FROM_ORDERS,
        SUM(QUANTITY_FROM_RETURNS)                            AS QUANTITY_FROM_RETURNS,
        SUM(NET_QUANTITY)                                     AS NET_QUANTITY,
        SUM(PRODUCT_SALES_FROM_ORDERS)                        AS PRODUCT_SALES_FROM_ORDERS,
        SUM(SHIPPING_CREDITS_FROM_ORDERS)                     AS SHIPPING_CREDITS_FROM_ORDERS,
        SUM(GIFTWRAP_CREDITS_FROM_ORDERS)                     AS GIFTWRAP_CREDITS_FROM_ORDERS,
        <PERSON><PERSON>(PROMO_REBATES_FROM_ORDERS)                        AS PROMO_REBATES_FROM_ORDERS,
        SUM(NET_REV_FROM_RETURNS)                             AS NET_REV_FROM_RETURNS,
        SUM(NET_REV_FROM_LIQUIDATIONS)                        AS NET_REV_FROM_LIQUIDATIONS,
        SUM(NET_REVENUE)                                      AS NET_REVENUE,

        /* ─── fulfilment fees ─── */
        SUM(FULFILLMENT_FEES_FROM_ORDERS)                              AS FULFILLMENT_FEES_FROM_ORDERS,
        SUM(FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS) AS FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
        SUM(FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS)     AS FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS,
        SUM(FULFILLMENT_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED)         AS FULFILLMENT_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED,
        SUM(TOTAL_FULFILLMENT_FEES)                                    AS TOTAL_FULFILLMENT_FEES,

        /* ─── selling fees & plugs ─── */
        SUM(SELLING_FEES_FROM_ORDERS)                                  AS SELLING_FEES_FROM_ORDERS,
        SUM(SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS)     AS SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
        SUM(SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS)         AS SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS,
        SUM(SELLING_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED_EXCL_MBS)    AS SELLING_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED_EXCL_MBS,
        SUM(ALLOCATED_MBS)                                             AS ALLOCATED_MBS,
        SUM(ALLOCATED_MONTHLY_SELLING_FEES_PLUG)                       AS ALLOCATED_MONTHLY_SELLING_FEES_PLUG,
        SUM(TOTAL_SELLING_FEES)                                        AS TOTAL_SELLING_FEES,

        /* ─── COGS & related plugs ─── */
        MAX(MAX_LATEST_NS_COGS)                        AS MAX_LATEST_NS_COGS,
       -- MAX(MAX_MONTH_LATEST_NS_COGS)                  AS MAX_MONTH_LATEST_NS_COGS,
        MAX(MAX_AVG_BRAND_DAY_NS_COGS)                 AS MAX_AVG_BRAND_DAY_NS_COGS,
        MAX(max_unit_cogs_excl_service_fees)           AS max_unit_cogs_excl_service_fees,
        MAX(max_unit_service_fees)                     AS max_unit_service_fees,
        SUM(CALCULATED_COGS_NS)                        AS CALCULATED_COGS_NS,
        SUM(COGS_EXCL_SERVICE_FEES)                    AS COGS_EXCL_SERVICE_FEES,
        SUM(COGS_SERVICE_FEES)                         AS COGS_SERVICE_FEES,
        SUM(TOOLING_PLUG)                              AS TOOLING_PLUG,
        SUM(COGS_FROM_UNSELLABLE_RETURNS)              AS COGS_FROM_UNSELLABLE_RETURNS,
        SUM(COGS_REFUNDS_FROM_SELLABLE_RETURNS)        AS COGS_REFUNDS_FROM_SELLABLE_RETURNS,
        SUM(BOKA_MONTHLY_AIR_FREIGHT_PLUG)             AS BOKA_MONTHLY_AIR_FREIGHT_PLUG,
        SUM(COGS_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS) AS COGS_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
        SUM(COGS_FROM_NON_ORDER_NON_SKU_ALLOCATED)         AS COGS_FROM_NON_ORDER_NON_SKU_ALLOCATED,
        SUM(TOTAL_COGS_EXCL_THREE_PL_FREIGHT)                                     AS TOTAL_COGS_EXCL_THREE_PL_FREIGHT,

        /* ─── other plugs ─── */
        SUM(ALLOCATED_LONG_TERM_STORAGE_FEE)           AS ALLOCATED_LONG_TERM_STORAGE_FEE,
        SUM(ALLOCATED_VINE)                            AS ALLOCATED_VINE

    FROM  DWH_DEV.STAGING.stg_brandebitda_revenue_sku_level_order_non_order_revenue_fba_all_components
    /* add any date / brand filters here if required */
    GROUP BY REPORT_DAY, MAPPED_ASIN, country_code
),

asin_brand_map AS (
SELECT
DISTINCT
MAPPED_ASIN AS asin,
MAPPED_BRAND_CODE AS brand_code
FROM DWH_DEV.STAGING.stg_brandebitda_revenue_sku_level_order_non_order_revenue_fba_all_components

),


 wbr_asin_map_latest AS (
    SELECT *
    FROM (
        SELECT
            child_asin,
            country_code,
            brand,
            category,
            niche,
            product_type,
            brand_grouping,
            brand_manager,
            account_title,
            ROW_NUMBER() OVER (
                PARTITION BY child_asin, country_code
                ORDER BY record_updated_timestamp_utc DESC,
                         record_created_timestamp_utc DESC
            ) AS rn
        FROM DWH_DEV.STAGING.merge_brand_ebitda_asin_mapping
        WHERE child_asin IS NOT NULL
    )
    WHERE rn = 1
    AND country_code = 'US'
),


amz_3pl AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(CASE WHEN activity = 'storage' THEN daily_amz_3pl_fee ELSE 0 END) AS daily_3pl_storage,
        SUM(CASE WHEN activity = 'inbound' THEN daily_amz_3pl_fee ELSE 0 END) AS daily_3pl_inbound,
        SUM(CASE WHEN activity = 'outbound' THEN daily_amz_3pl_fee ELSE 0 END) AS daily_3pl_outbound,
        SUM(CASE WHEN activity = 'amazon_freight' THEN daily_amz_3pl_fee ELSE 0 END) AS daily_3pl_freight,
        SUM(CASE WHEN activity = 'air_freight' THEN daily_amz_3pl_fee ELSE 0 END) AS daily_3pl_airfreight,
        COALESCE(daily_3pl_storage,0) + COALESCE(daily_3pl_inbound,0) + COALESCE(daily_3pl_outbound,0) AS daily_3pl_total
    FROM

      DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_3PL_FEES
    WHERE
        country_code = 'US'
    GROUP BY ALL
),

amz_storage AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(actual_storage_fee_usd) AS daily_amz_storage_fee_actual,
        SUM(estimated_storage_fee_usd) AS daily_amz_storage_fee_estimate,
        SUM(total_daily_storage_fee_usd) AS daily_amz_storage_fee,
        0 AS plug,
        daily_amz_storage_fee + plug AS total_amz_storage_fee
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_AMAZON_STORAGE_FEES
    WHERE
        country_code = 'US'
    GROUP BY ALL
),

google_ads AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(google_ad_spend) AS google_ad_spend_api,
        SUM(misfit_agency_spend) AS misfit_agency_cost,
        google_ad_spend_api + misfit_agency_cost AS daily_google_ad_spend
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_GOOGLE_ADS_SPEND
    WHERE
        channel = 'AMZ-US'
    GROUP BY ALL
),

facebook_ads AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(fb_ad_spend) AS facebook_ad_spend_api,
        SUM(misfit_agency_cost) AS misfit_agency_cost,
        COALESCE(SUM(fb_ad_spend),0) + COALESCE(SUM(misfit_agency_cost),0) AS daily_fb_ad_spend
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_FACEBOOK_SPEND
    WHERE
        channel = 'AMZ-US'
    GROUP BY ALL
),

amz_dsp AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(daily_dsp_cost_usd) AS daily_dsp_cost,
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_DSP
    WHERE
        country_code = 'US'
    GROUP BY ALL
),

affiliates AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(daily_affiliate_cost) AS daily_affiliate_cost
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_AFFILIATES_COST
    WHERE
        country_code = 'US'
        AND channel = 'AMAZON'
    GROUP BY ALL
),

influencers AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(daily_influencer_cost) AS daily_influencer_cost
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_INFLUENCER
    WHERE
        channel = 'AMZ-US'
    GROUP BY ALL
),

deal_and_coupon AS (
    SELECT
        purchased_date,
        asin,
        country_code,
        SUM(coupon_fee) AS coupon,
        SUM(deal_fee) AS deal,
        coupon + deal AS daily_deal_and_coupon_fee
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_DEAL_COUPON
    WHERE
country_code = 'US'
    GROUP BY ALL
),

publications AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(daily_publications_cost) AS daily_publications_cost
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_PUBLICATIONS
    WHERE
        channel = 'AMZ-US'
    GROUP BY ALL
),

royalties AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(daily_royalty_cost) AS daily_royalty_cost
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_ROYALTIES
    WHERE
        country_code = 'US'
    GROUP BY ALL
),

tiktok AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(daily_tiktok_ad_spend) AS daily_tiktok_ad_spend
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_TIKTOK_AD_SPEND
    WHERE
        channel = 'AMZ-US'
    GROUP BY ALL
),

ugc AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(daily_ugc_cost) AS daily_ugc_cost
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_UGC
    WHERE
        channel = 'AMZ-US'
    GROUP BY ALL
)


, ads_cte AS (
    SELECT
        purchase_date AS report_date,
        country_code,
        asin,
        SUM(ABS(sp_ppc_spend))               AS sp_ppc_spend,
        SUM(ABS(sv_ppc_spend))              AS sv_ppc_spend,
        SUM(ABS(sbv_ppc_spend))             AS sbv_ppc_spend,
        SUM(ABS(sd_ppc_spend))               AS sd_ppc_spend,
        SUM(ABS(total_ppc_spend))            AS total_ppc_spend,
        SUM(ABS(total_asin_google_ads_spend)) AS google_ads_spend,
        SUM(ABS(total_asin_fb_ads_spend))     AS facebook_ads_spend
    FROM DWH_DEV.STAGING.brand_ebitda_daily_item_level_all_ads_cost_consolidated
    WHERE country_code = 'US'
    GROUP BY purchase_date, country_code, asin
),

giveaways AS (

    SELECT
        report_date,
        country_code,
        child_asin,
        SUM(total_giveaways_daily) AS total_giveaways
    FROM DWH_DEV.STAGING.STG_BRANDEBITDA_GIVEAWAYS_CONSOLIDATED_US_DAILY
    GROUP BY
    report_date,
    country_code,
    child_asin
),

certifications AS (
    SELECT
        report_date,
        country_code,
        asin,
        SUM(daily_certification_cost) AS daily_certification_cost
    FROM DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_CERTIFICATIONS
    WHERE channel = 'AMZ-US'
    GROUP BY
        report_date,
        country_code,
        asin
),

consolidated_mapping AS (

    SELECT DISTINCT
    report_date,
    asin,
    country_code
    FROM asin_amazon_data_agg

    UNION

    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM amz_3pl

    UNION

    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM google_ads

    UNION

    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM facebook_ads

    UNION

    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM amz_dsp

    UNION

    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM affiliates

    UNION

    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM influencers

    UNION

    SELECT DISTINCT
        purchased_date,
        asin,
        country_code
    FROM deal_and_coupon

    UNION

    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM publications

    UNION

    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM royalties

    UNION

    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM tiktok

    UNION

    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM ugc
),


final AS (
    SELECT

    cm.report_date,
    cm.country_code,
    cm.asin,
    brand_map.brand_code,



    amz_agg.QUANTITY_FROM_ORDERS,
    amz_agg.QUANTITY_FROM_RETURNS,
    amz_agg.NET_QUANTITY,
    amz_agg.PRODUCT_SALES_FROM_ORDERS,
    amz_agg.SHIPPING_CREDITS_FROM_ORDERS,
    amz_agg.GIFTWRAP_CREDITS_FROM_ORDERS,
    amz_agg.PROMO_REBATES_FROM_ORDERS,
    amz_agg.NET_REV_FROM_RETURNS,
    amz_agg.NET_REV_FROM_LIQUIDATIONS,
    amz_agg.NET_REVENUE,

        /* ─── fulfilment fees ─── */
    amz_agg.FULFILLMENT_FEES_FROM_ORDERS,
    amz_agg.FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
    amz_agg.FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS,
    amz_agg.FULFILLMENT_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED,
    amz_agg.TOTAL_FULFILLMENT_FEES,

        /* ─── selling fees & plugs ─── */
    amz_agg.SELLING_FEES_FROM_ORDERS,
    amz_agg.SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
    amz_agg.SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS,
    amz_agg.SELLING_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED_EXCL_MBS,
    amz_agg.ALLOCATED_MBS,
    amz_agg.ALLOCATED_MONTHLY_SELLING_FEES_PLUG,
    amz_agg.TOTAL_SELLING_FEES,

        /* ─── COGS & related plugs ─── */
     amz_agg.MAX_LATEST_NS_COGS,
     --amz_agg.MAX_MONTH_LATEST_NS_COGS,
     amz_agg.MAX_AVG_BRAND_DAY_NS_COGS,
     amz_agg.max_unit_cogs_excl_service_fees,
     amz_agg.max_unit_service_fees,
     amz_agg.COGS_EXCL_SERVICE_FEES,
     amz_agg.COGS_SERVICE_FEES,
     amz_agg.CALCULATED_COGS_NS,
     amz_agg.TOOLING_PLUG,
     amz_agg.COGS_FROM_UNSELLABLE_RETURNS,
     amz_agg.COGS_REFUNDS_FROM_SELLABLE_RETURNS,
     amz_agg.BOKA_MONTHLY_AIR_FREIGHT_PLUG,
     amz_agg.COGS_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
     amz_agg.COGS_FROM_NON_ORDER_NON_SKU_ALLOCATED,
     amz_agg.TOTAL_COGS_EXCL_THREE_PL_FREIGHT,

     amz_3pl.daily_3pl_freight,

     (COALESCE(amz_3pl.daily_3pl_freight,0)
     + COALESCE(amz_agg.TOTAL_COGS_EXCL_THREE_PL_FREIGHT,0)) AS TOTAL_COGS,

     ads_cte.sp_ppc_spend,
     ads_cte.sv_ppc_spend,
     ads_cte.sbv_ppc_spend,
     ads_cte.sd_ppc_spend,
     ALLOCATED_VINE,
     (COALESCE(ads_cte.sp_ppc_spend,0)
     + COALESCE(ads_cte.sv_ppc_spend,0)
     + COALESCE(ads_cte.sbv_ppc_spend,0)
     + COALESCE(ads_cte.sd_ppc_spend,0)
     + COALESCE(amz_agg.ALLOCATED_VINE,0))
     AS TOTAL_PPC,




    amz_dsp.daily_dsp_cost AS TOTAL_DSP,
    deal_and_coupon.coupon,
    deal_and_coupon.deal,
    deal_and_coupon.daily_deal_and_coupon_fee AS TOTAL_DEAL_FEES,
    google_ads.google_ad_spend_api,
    google_ads.misfit_agency_cost AS google_ads_misfit_agency_cost,
    google_ads.daily_google_ad_spend AS TOTAL_GOOGLE,
    facebook_ads.facebook_ad_spend_api,
    facebook_ads.misfit_agency_cost AS fb_ads_misfit_agency_cost,
    ugc.daily_ugc_cost,
    COALESCE(facebook_ads.daily_fb_ad_spend,0) + COALESCE(ugc.daily_ugc_cost,0) AS TOTAL_FACEBOOK_UGC,
    tiktok.daily_tiktok_ad_spend,
    influencers.daily_influencer_cost,
    COALESCE(tiktok.daily_tiktok_ad_spend,0) + COALESCE(influencers.daily_influencer_cost,0) AS tiktok_and_influencer_cost,
    affiliates.daily_affiliate_cost AS TOTAL_AFFILIATES,
    amz_3pl.daily_3pl_storage,
    amz_3pl.daily_3pl_inbound,
    amz_3pl.daily_3pl_outbound,

    amz_3pl.daily_3pl_total AS TOTAL_3PL_EXCL_FREIGHT,
    amz_storage.daily_amz_storage_fee_actual,
    amz_storage.daily_amz_storage_fee_estimate,
    amz_storage.plug,
    amz_agg.ALLOCATED_LONG_TERM_STORAGE_FEE,

    COALESCE(amz_storage.total_amz_storage_fee,0)
    + COALESCE(amz_agg.ALLOCATED_LONG_TERM_STORAGE_FEE, 0)
    AS total_amz_storage_fee,

    royalties.daily_royalty_cost,
    publications.daily_publications_cost AS total_publication_cost,
    certifications.daily_certification_cost AS total_daily_certification_cost,
    giveaways.total_giveaways AS total_daily_giveaways_cost
FROM
    consolidated_mapping AS cm

LEFT JOIN asin_amazon_data_agg AS amz_agg
    ON cm.report_date = amz_agg.report_date
    AND cm.asin = amz_agg.asin
    AND cm.country_code = amz_agg.country_code

LEFT JOIN asin_brand_map AS brand_map
    ON cm.asin = brand_map.asin

LEFT JOIN
    amz_3pl
    ON cm.report_date = amz_3pl.report_date
    AND cm.asin = amz_3pl.asin
    AND cm.country_code = amz_3pl.country_code
LEFT JOIN
    amz_storage
    ON cm.report_date = amz_storage.report_date
    AND cm.asin = amz_storage.asin
    AND cm.country_code = amz_storage.country_code
LEFT JOIN
    google_ads
    ON cm.report_date = google_ads.report_date
    AND cm.asin = google_ads.asin
    AND cm.country_code = google_ads.country_code
LEFT JOIN
    facebook_ads
    ON cm.report_date = facebook_ads.report_date
    AND cm.asin = facebook_ads.asin
    AND cm.country_code = facebook_ads.country_code
LEFT JOIN
    amz_dsp
    ON cm.report_date = amz_dsp.report_date
    AND cm.asin = amz_dsp.asin
    AND cm.country_code = amz_dsp.country_code
LEFT JOIN
    affiliates
    ON cm.report_date = affiliates.report_date
    AND cm.asin = affiliates.asin
    AND cm.country_code = affiliates.country_code
LEFT JOIN
    influencers
    ON cm.report_date = influencers.report_date
    AND cm.asin = influencers.asin
    AND cm.country_code = influencers.country_code
LEFT JOIN
    deal_and_coupon
    ON cm.report_date = deal_and_coupon.purchased_date
    AND cm.asin = deal_and_coupon.asin
    AND cm.country_code = deal_and_coupon.country_code
LEFT JOIN
    publications
    ON cm.report_date = publications.report_date
    AND cm.asin = publications.asin
    AND cm.country_code = publications.country_code
LEFT JOIN
    royalties
    ON cm.report_date = royalties.report_date
    AND cm.asin = royalties.asin
    AND cm.country_code = royalties.country_code
LEFT JOIN
    tiktok
    ON cm.report_date = tiktok.report_date
    AND cm.asin = tiktok.asin
    AND cm.country_code = tiktok.country_code
LEFT JOIN
    ugc
    ON cm.report_date = ugc.report_date
    AND cm.asin = ugc.asin
    AND cm.country_code = ugc.country_code

LEFT JOIN ads_cte
    ON cm.report_date = ads_cte.report_date
    AND cm.asin = ads_cte.asin
    AND cm.country_code = ads_cte.country_code

LEFT JOIN giveaways
    ON cm.report_date = giveaways.report_date
    AND cm.asin = giveaways.child_asin
    AND cm.country_code = giveaways.country_code

LEFT JOIN certifications
    ON cm.report_date = certifications.report_date
    AND cm.asin = certifications.asin
    AND cm.country_code = certifications.country_code



)

SELECT *
FROM final ;
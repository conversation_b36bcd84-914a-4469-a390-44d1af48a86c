CREATE OR REPLACE TABLE $stage_db.stage_brand_ebitda_asin_level_shipment_revenue AS

WITH shipments_data AS (
    SELECT
        "amazon_order_id" AS order_id,
        "sales_channel" AS channel,
        CASE
          WHEN LOWER("sales_channel") LIKE 'amazon.com.tr%' THEN 'TR'
          WHEN LOWER("sales_channel") LIKE 'amazon.com.mx%' THEN 'MX'
          WHEN LOWER("sales_channel") LIKE 'amazon.com.be%' THEN 'BE'
          WHEN LOWER("sales_channel") LIKE 'amazon.com.br%' THEN 'BR'
          WHEN LOWER("sales_channel") LIKE 'amazon.ca%' THEN 'CA'
          WHEN LOWER("sales_channel") LIKE 'amazon.ae%' THEN 'AE'
          WHEN LOWER("sales_channel") LIKE 'amazon.co.uk%' THEN 'UK'
          WHEN LOWER("sales_channel") LIKE 'amazon.de%' THEN 'DE'
          WHEN LOWER("sales_channel") LIKE 'amazon.fr%' THEN 'FR'
          WHEN LOWER("sales_channel") LIKE 'amazon.it%' THEN 'IT'
          WHEN LOWER("sales_channel") LIKE 'amazon.pl%' THEN 'PL'
          WHEN LOWER("sales_channel") LIKE 'amazon.nl%' THEN 'NL'
          WHEN LOWER("sales_channel") LIKE 'amazon.ie%' THEN 'IE'
          WHEN LOWER("sales_channel") LIKE 'amazon.es%' THEN 'ES'
          WHEN LOWER("sales_channel") LIKE 'amazon.se%' THEN 'SE'
          WHEN LOWER("sales_channel") LIKE 'amazon.com%' THEN 'US'
          ELSE 'OTHER'
        END AS country_code,
        "seller_id" AS seller_id_shipment,
        "sku" AS sku,
        TRY_TO_DATE("shipment_date") AS shipment_date,
        SUM("quantity_shipped") AS quantity,
        SUM("item_price") AS product_sales,
        SUM("shipping_price") AS shipping_credits,
        SUM("gift_wrap_price") AS giftwrap_credits,
        SUM("item_price") + SUM("shipping_price") + SUM("gift_wrap_price") AS total_sales
    FROM dwh.staging.amazon_fba_fulfilled_shipments
    WHERE TRY_TO_DATE("shipment_date") BETWEEN '2024-01-01' AND '2025-03-29'
    AND "sales_channel" = 'amazon.com'
    GROUP BY "amazon_order_id", "sales_channel", "seller_id", "sku", TRY_TO_DATE("shipment_date")
),
drr_data AS (
    SELECT
        SELLER_ID,
        COUNTRY_CODE,
        TYPE,
        ORDER_ID,
        SKU,
        MARKETPLACE,
        ACCOUNT_TYPE,
        SUM(QUANTITY) AS quantity,
        SUM(PRODUCT_SALES) AS product_sales,
        SUM(SHIPPING_CREDITS) AS shipping_credits,
        SUM(GIFT_WRAP_CREDITS) AS giftwrap_credits,
        SUM(PROMOTIONAL_REBATES) AS promo_rebates,
        SUM(SELLING_FEES) AS selling_fees,
        SUM(FBA_FEES) AS fba_fees,
        SUM(OTHER_TRANSACTION_FEES) AS other_transac,
        SUM(OTHER) AS other_fees,
        'drr' AS source,
        COUNT(*) AS cnt
    FROM dwh.PROD.FACT_AMAZON_SETTLEMENT_TRANSACTIONS_SCS
    WHERE type = 'Order'
      --AND LOWER(MARKETPLACE) = 'amazon.com'
      AND report_date::date BETWEEN '2024-01-01' AND '2025-03-29'
    GROUP BY SELLER_ID, COUNTRY_CODE, TYPE, ORDER_ID, SKU, MARKETPLACE, ACCOUNT_TYPE
),
deferred_data AS (
    SELECT
        SELLER_ID,
        COUNTRY_CODE,
        TYPE,
        ORDER_ID,
        SKU,
        MARKETPLACE,
        ACCOUNT_TYPE,
        SUM(QUANTITY) AS quantity,
        SUM(PRODUCT_SALES) AS product_sales,
        SUM(SHIPPING_CREDITS) AS shipping_credits,
        SUM(GIFT_WRAP_CREDITS) AS giftwrap_credits,
        SUM(PROMOTIONAL_REBATES) AS promo_rebates,
        SUM(SELLING_FEES) AS selling_fees,
        SUM(FBA_FEES) AS fba_fees,
        SUM(OTHER_TRANSACTION_FEES) AS other_transac,
        SUM(OTHER) AS other_fees,
        'deferred' AS source,
        COUNT(*) AS cnt
    FROM dwh_dev.prod.fact_amazon_deferred_transactions
    WHERE type = 'Order'
     -- AND LOWER(MARKETPLACE) = 'amazon.com'
      AND report_requested_time::date = '2025-03-27'
    GROUP BY SELLER_ID, COUNTRY_CODE, TYPE, ORDER_ID, SKU, MARKETPLACE, ACCOUNT_TYPE
),
drr_deferred_data AS (
  SELECT
    COALESCE(a.SELLER_ID, b.SELLER_ID) AS SELLER_ID,
    COALESCE(a.COUNTRY_CODE, b.COUNTRY_CODE) AS COUNTRY_CODE,
    COALESCE(a.TYPE, b.TYPE) AS TYPE,
    COALESCE(a.ORDER_ID, b.ORDER_ID) AS ORDER_ID,
    COALESCE(a.SKU, b.SKU) AS SKU,
    COALESCE(a.MARKETPLACE, b.MARKETPLACE) AS MARKETPLACE,
    COALESCE(a.ACCOUNT_TYPE, b.ACCOUNT_TYPE) AS ACCOUNT_TYPE,
    a.cnt AS DRR_CNT,
    b.cnt AS DEFERRED_CNT,
    CASE WHEN a.cnt > 0 THEN 'drr' WHEN b.cnt > 0 THEN 'deferred' ELSE 'NA' END AS source,
    CASE WHEN a.cnt > 0 THEN a.quantity ELSE b.quantity END AS source_quantity,
    CASE WHEN a.cnt > 0 THEN a.product_sales ELSE b.product_sales END AS source_product_sales,
    CASE WHEN a.cnt > 0 THEN a.shipping_credits ELSE b.shipping_credits END AS source_shipping_credits,
    CASE WHEN a.cnt > 0 THEN a.giftwrap_credits ELSE b.giftwrap_credits END AS source_giftwrap_credits,
    CASE WHEN a.cnt > 0 THEN a.promo_rebates ELSE b.promo_rebates END AS source_promo_rebates,
    CASE WHEN a.cnt > 0 THEN a.selling_fees ELSE b.selling_fees END AS source_selling_fees,
    CASE WHEN a.cnt > 0 THEN a.fba_fees ELSE b.fba_fees END AS source_fba_fees,
    CASE WHEN a.cnt > 0 THEN a.other_transac ELSE b.other_transac END AS source_other_transac,
    CASE WHEN a.cnt > 0 THEN a.other_fees ELSE b.other_fees END AS source_other_fees,
    (CASE WHEN a.cnt > 0 THEN a.product_sales ELSE b.product_sales END +
     CASE WHEN a.cnt > 0 THEN a.shipping_credits ELSE b.shipping_credits END +
     CASE WHEN a.cnt > 0 THEN a.giftwrap_credits ELSE b.giftwrap_credits END) AS source_total_sales
  FROM drr_data a
  FULL JOIN deferred_data b
    ON a.SELLER_ID = b.SELLER_ID
    AND a.COUNTRY_CODE = b.COUNTRY_CODE
    AND a.ORDER_ID = b.ORDER_ID
    AND a.SKU = b.SKU
    AND a.TYPE = b.TYPE
    AND a.MARKETPLACE = b.MARKETPLACE
    AND a.ACCOUNT_TYPE = b.ACCOUNT_TYPE
),
joined_shipments_to_drr AS (
  SELECT
    a.*,
    b.source_total_sales,
    b.source_product_sales,
    b.source_shipping_credits,
    b.source_giftwrap_credits,
    b.source_promo_rebates,
    b.source_selling_fees,
    b.source_fba_fees,
    b.source_other_transac,
    b.source_other_fees,
    (b.source_total_sales - a.total_sales) AS gap_total_sales,
    b.source_quantity
  FROM shipments_data a
  LEFT JOIN drr_deferred_data b
    ON a.seller_id_shipment = b.SELLER_ID
    AND a.order_id = b.ORDER_ID
    AND a.sku = b.SKU
    AND a.country_code = b.COUNTRY_CODE
),
joined_shipments_with_proration AS (
  SELECT
    *,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_product_sales / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_product_sales END AS pro_rated_source_product_sales,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_shipping_credits / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_shipping_credits END AS pro_rated_source_shipping_credits,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_giftwrap_credits / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_giftwrap_credits END AS pro_rated_source_giftwrap_credits,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_promo_rebates / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_promo_rebates END AS pro_rated_source_promo_rebates,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_selling_fees / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_selling_fees END AS pro_rated_source_selling_fees,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_fba_fees / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_fba_fees END AS pro_rated_source_fba_fees,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_other_transac / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_other_transac END AS pro_rated_source_other_transac,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_other_fees / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_other_fees END AS pro_rated_source_other_fees,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN (CAST(source_product_sales / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2)) +
               CAST(source_shipping_credits / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2)) +
               CAST(source_giftwrap_credits / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2)))
         ELSE source_total_sales END AS pro_rated_total_sales
  FROM joined_shipments_to_drr
),
-- Non-order section: select raw non‑order records.
non_order_raw AS (
  SELECT
      DATE(REPORT_DATE) AS transac_date,
      SELLER_ID,
      -- We remove the original COUNTRY_CODE to avoid ambiguity.
      TYPE,
      SKU,
      MARKETPLACE,
      DESCRIPTION,
      PRODUCT_SALES,
      SHIPPING_CREDITS,
      GIFT_WRAP_CREDITS,
      PROMOTIONAL_REBATES,
      SELLING_FEES,
      FBA_FEES,
      OTHER_TRANSACTION_FEES,
      OTHER
  FROM DWH.PROD.FACT_AMAZON_SETTLEMENT_TRANSACTIONS_SCS
  WHERE type NOT IN ('Order','Transfer','Debt')
    AND report_date::date BETWEEN '2024-01-01' AND '2025-03-29'
),
-- Compute calculated fields and derive country from MARKETPLACE.
non_order_calculated AS (
  SELECT
    transac_date,
    SELLER_ID,
    TYPE,
    SKU,
    LOWER(MARKETPLACE) AS marketplace,
    DESCRIPTION,
    CASE
        WHEN SKU IS NOT NULL AND DESCRIPTION LIKE 'FBA Inventory Reimbursement%' THEN 'FBA Inventory Reimbursement'
        WHEN SKU IS NOT NULL AND DESCRIPTION LIKE 'FBA Customer Return Per Unit Fee%' THEN 'FBA Customer Return Per Unit Fee'
        WHEN SKU IS NOT NULL AND DESCRIPTION LIKE 'Inbound Defect Fee%' THEN 'FBA Inventory Reimbursement'
        WHEN DESCRIPTION LIKE 'FBA Inventory Reimbursement - %' THEN 'FBA Inventory Reimbursement'
        WHEN DESCRIPTION LIKE 'FBA Prep Fee%' THEN 'FBA Prep Fee'
        WHEN DESCRIPTION LIKE 'FBA Removal Order%' THEN 'FBA Removal Order'
        WHEN DESCRIPTION LIKE 'AWD %' THEN 'AWD'
        WHEN DESCRIPTION LIKE 'Deals-%' OR DESCRIPTION LIKE 'Lightning Deal-%' OR DESCRIPTION LIKE 'Coupon %' THEN 'Deals / Coupons'
        WHEN DESCRIPTION LIKE 'FBA Customer Returns Fee (Non-Apparel %' THEN 'FBA Customer Returns Fee (Non-Apparel and Non-Shoes)'
        WHEN SKU IS NOT NULL AND DESCRIPTION NOT LIKE 'FBA Inventory Reimbursement%' THEN 'product_description'
        ELSE DESCRIPTION
    END AS simplified_description,
    CASE
        WHEN SKU IS NOT NULL THEN 'yes'
        ELSE 'no'
    END AS sku_specified,
       CASE
        WHEN LOWER(MARKETPLACE) IS NOT NULL THEN LOWER(MARKETPLACE)
        ELSE 'null_marketplace'
    END AS revised_marketplace,
    CASE
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.com.tr%' THEN 'TR'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.com.mx%' THEN 'MX'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.com.be%' THEN 'BE'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.com.br%' THEN 'BR'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.ca%' THEN 'CA'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.ae%' THEN 'AE'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.co.uk%' THEN 'UK'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.de%' THEN 'DE'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.fr%' THEN 'FR'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.it%' THEN 'IT'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.pl%' THEN 'PL'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.nl%' THEN 'NL'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.ie%' THEN 'IE'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.es%' THEN 'ES'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.se%' THEN 'SE'
      WHEN LOWER(MARKETPLACE) LIKE 'amazon.com%' THEN 'US'
      ELSE 'OTHER'
    END AS country_code,
    PRODUCT_SALES,
    SHIPPING_CREDITS,
    GIFT_WRAP_CREDITS,
    PROMOTIONAL_REBATES,
    SELLING_FEES,
    FBA_FEES,
    OTHER_TRANSACTION_FEES,
    OTHER
  FROM non_order_raw
),
non_order_unpivoted AS (
  SELECT
    transac_date,
    SELLER_ID,
    -- Use the computed country_code only.
    country_code,
    TYPE,
    SKU,
    marketplace,
    simplified_description,
    sku_specified,
    revised_marketplace,
    SUM(PRODUCT_SALES) AS PRODUCT_SALES,
    SUM(SHIPPING_CREDITS) AS SHIPPING_CREDITS,
    SUM(GIFT_WRAP_CREDITS) AS GIFTWRAP_CREDITS,
    SUM(PROMOTIONAL_REBATES) AS PROMOTIONAL_REBATES,
    SUM(SELLING_FEES) AS SELLING_FEES,
    SUM(FBA_FEES) AS FBA_FEES,
    SUM(OTHER_TRANSACTION_FEES) AS OTHER_TRANSACTION_FEES,
    SUM(OTHER) AS OTHER
  FROM non_order_calculated
  GROUP BY transac_date, SELLER_ID, country_code, TYPE, SKU, marketplace, simplified_description, sku_specified, revised_marketplace
),
non_order_pivoted AS (
  SELECT *
  FROM non_order_unpivoted
  UNPIVOT (
    AMOUNT FOR category IN (PRODUCT_SALES, SHIPPING_CREDITS, GIFTWRAP_CREDITS, PROMOTIONAL_REBATES, SELLING_FEES, FBA_FEES, OTHER_TRANSACTION_FEES, OTHER)
  )
  WHERE AMOUNT != 0
),
adapted_bi_mapping AS (
  SELECT *,
    CASE
      WHEN marketplace IS NOT NULL THEN marketplace
      ELSE 'null_marketplace'
    END AS revised_marketplace
  FROM DWH.RAW.NRA_AMAZON_BI_GL_MAPPING
),
non_order_with_bi_mapping AS (
  SELECT
    a.*,
    b.bi_mapping
  FROM non_order_pivoted a
  LEFT JOIN adapted_bi_mapping b
    ON LOWER(a.TYPE) = LOWER(b.TYPE)
    AND LOWER(a.simplified_description) = LOWER(b.simplified_description)
    AND LOWER(a.category) = LOWER(b.category)
    AND LOWER(a.sku_specified) = LOWER(b.sku_specified)
    AND LOWER(a.revised_marketplace) = LOWER(b.revised_marketplace)
),
non_order_aggregates AS (
  SELECT
    transac_date AS DAY,
    SELLER_ID,
    SKU,
    country_code,
    SUM(AMOUNT) AS non_order_total
  FROM non_order_with_bi_mapping
  GROUP BY transac_date, SELLER_ID, SKU, country_code
),
revenue_contrib_at_product_account_level AS (
  SELECT
    sku_shipment,
    seller_id_shipment,
    shipment_date,
    sku_account_sales,
    account_sales,
    sku_account_sales / NULLIF(account_sales, 0) AS sku_account_contrib
  FROM (
    SELECT
      sku AS sku_shipment,
      seller_id_shipment,
      shipment_date,
      SUM(total_sales) AS sku_account_sales,
      SUM(SUM(total_sales)) OVER (PARTITION BY seller_id_shipment, shipment_date) AS account_sales
    FROM shipments_data
    GROUP BY sku, seller_id_shipment, shipment_date
  )
),
day_sku_account AS (
  SELECT DISTINCT shipment_date AS DAY, seller_id_shipment AS SELLER_ID, sku, country_code
  FROM shipments_data
  UNION
  SELECT DISTINCT transac_date AS DAY, SELLER_ID, SKU, country_code
  FROM non_order_with_bi_mapping
),
day_sku_account_with_contrib AS (
  SELECT
    d.DAY,
    d.sku,
    d.SELLER_ID,
    d.country_code,
    r.sku_account_contrib
  FROM day_sku_account d
  LEFT JOIN revenue_contrib_at_product_account_level r
    ON d.DAY = r.shipment_date
    AND d.SELLER_ID = r.seller_id_shipment
    AND d.sku = r.sku_shipment
),
order_aggregates AS (
  SELECT
    shipment_date AS DAY,
    seller_id_shipment AS SELLER_ID,
    sku,
    country_code,
    SUM(source_quantity) AS quantity, 
    SUM(total_sales) AS order_total_sales,
    SUM(pro_rated_total_sales) AS order_pro_rated_total_sales,
    SUM(pro_rated_source_fba_fees) AS order_fba_fees,
    SUM(pro_rated_source_selling_fees) AS order_selling_fees,
    SUM(pro_rated_source_other_transac) AS order_other_transac,
    SUM(pro_rated_source_other_fees) AS order_other_fees
  FROM joined_shipments_with_proration
  GROUP BY shipment_date, seller_id_shipment, sku, country_code
),

sku_level_revenue_metrics AS (
SELECT
  d.DAY AS report_date,
  d.SELLER_ID AS seller_id,
  d.sku,
  d.country_code,
  COALESCE(o.quantity, 0) quantity, 
  COALESCE(o.order_total_sales, 0) AS order_total_sales,
  COALESCE(o.order_pro_rated_total_sales, 0) AS order_pro_rated_total_sales,
  COALESCE(n.non_order_total, 0) AS non_order_total,
  (COALESCE(o.order_total_sales, 0) + COALESCE(n.non_order_total, 0)) AS final_total_sales,
  COALESCE(o.order_fba_fees, 0) AS order_fba_fees,
  COALESCE(o.order_selling_fees, 0) AS order_selling_fees,
  COALESCE(o.order_other_transac, 0) AS order_other_transac,
  COALESCE(o.order_other_fees, 0) AS order_other_fees,
  (COALESCE(o.order_fba_fees, 0) + COALESCE(o.order_selling_fees, 0) + COALESCE(o.order_other_transac, 0) + COALESCE(o.order_other_fees, 0)) AS order_total_costs,
  ((COALESCE(o.order_total_sales, 0) + COALESCE(n.non_order_total, 0)) -
   (COALESCE(o.order_fba_fees, 0) + COALESCE(o.order_selling_fees, 0) + COALESCE(o.order_other_transac, 0) + COALESCE(o.order_other_fees, 0))) AS final_net_revenue
FROM day_sku_account_with_contrib d
LEFT JOIN order_aggregates o
  ON d.DAY = o.DAY
  AND d.SELLER_ID = o.SELLER_ID
  AND d.sku = o.sku
  AND d.country_code = o.country_code
LEFT JOIN non_order_aggregates n
  ON d.DAY = n.DAY
  AND d.SELLER_ID = n.SELLER_ID
  AND d.sku = n.SKU
  AND d.country_code = n.country_code
ORDER BY d.DAY, d.SELLER_ID, d.sku, d.country_code
)

--asin_level_revenue_metrics AS (
SELECT
  sku.report_date,
  sku.seller_id,
  
  sim.asin AS asin,
  sim.brand_code,
  sku.country_code,

  SUM(sku.quantity) AS quantity, 
  SUM(sku.order_total_sales) AS order_total_sales,
  SUM(sku.order_pro_rated_total_sales) AS order_pro_rated_total_sales,
  SUM(sku.non_order_total) AS non_order_total,
  SUM(sku.final_total_sales) AS final_total_sales,
  SUM(sku.order_fba_fees) AS order_fba_fees,
  SUM(sku.order_selling_fees) AS order_selling_fees,
  SUM(sku.order_other_transac) AS order_other_transac,
  SUM(sku.order_other_fees) AS order_other_fees,
  SUM(sku.order_total_costs) AS order_total_costs,
  SUM(sku.final_net_revenue) AS final_net_revenue
FROM sku_level_revenue_metrics sku
LEFT JOIN DWH_DEV.STAGING.stg_brand_ebitda_sku_asin_map_orders_source_table sim
  ON sku.sku = sim.sku
  AND sku.country_code = sim.country_code
GROUP BY
  sku.report_date,
  sku.seller_id,
  sim.brand_code,
  sim.asin,
  sku.country_code


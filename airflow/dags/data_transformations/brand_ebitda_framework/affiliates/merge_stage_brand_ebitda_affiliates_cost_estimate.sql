CREATE OR REPLACE TABLE $stage_db.merge_brand_ebitda_affiliates_cost_estimate AS
    SELECT

        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_brand_ebitda_affiliates_cost_estimate
    WHERE 1 = 0;

BEGIN TRANSACTION;

MERGE INTO
    $stage_db.merge_brand_ebitda_affiliates_cost_estimate AS tgt
USING
    $stage_db.dedupe_brand_ebitda_affiliates_cost_estimate AS src

        ON 1 = 1
       AND src.pk = tgt.pk

WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._airbyte_raw_id = src._airbyte_raw_id,
    tgt._airbyte_extracted_at = src._airbyte_extracted_at,
    tgt._airbyte_generation_id = src._airbyte_generation_id,
    tgt._airbyte_meta = src._airbyte_meta,
    tgt.year = src.year,
    tgt.brand = src.brand,
    tgt.quarter = src.quarter,
    tgt.brand_code = src.brand_code,
    tgt.has_budget = src.has_budget,
    tgt.affiliate_spend_perc = src.affiliate_spend_perc,
    tgt.marketplace_country_code = src.marketplace_country_code,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    year,
    brand,
    quarter,
    brand_code,
    has_budget,
    affiliate_spend_perc,
    marketplace_country_code,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._airbyte_raw_id,
    src._airbyte_extracted_at,
    src._airbyte_generation_id,
    src._airbyte_meta,
    src.year,
    src.brand,
    src.quarter,
    src.brand_code,
    src.has_budget,
    src.affiliate_spend_perc,
    src.marketplace_country_code,
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);

-- delete all entries that are not from the latest airbyte update
DELETE FROM $stage_db.merge_brand_ebitda_affiliates_cost_estimate
WHERE (record_updated_timestamp_utc) NOT IN (
    SELECT MAX(record_updated_timestamp_utc)
    FROM $stage_db.merge_brand_ebitda_affiliates_cost_estimate
);

COMMIT;

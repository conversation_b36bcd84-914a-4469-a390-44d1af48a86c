CREATE OR REPLACE TABLE $stage_db.merge_stg_brand_ebitda_daily_item_level_affiliates_cost AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.create_stg_brand_ebitda_daily_item_level_affiliates_cost
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_stg_brand_ebitda_daily_item_level_affiliates_cost AS tgt
USING
    (
        SELECT
            *
        FROM $stage_db.create_stg_brand_ebitda_daily_item_level_affiliates_cost
        QUALIFY ROW_NUMBER() OVER (PARTITION BY pk ORDER BY report_date DESC) = 1
    ) AS src
        ON src.pk = tgt.pk

WHEN MATCHED THEN
    -- Update the record if it already exists
    UPDATE SET
        tgt.report_date = src.report_date,
        tgt.month_start_date = src.month_start_date,
        tgt.brand_code = src.brand_code,
        tgt.channel = src.channel,
        tgt.country_code = src.country_code,
        tgt.asin = src.asin,
        tgt.daily_affiliate_cost = src.daily_affiliate_cost,
        tgt.record_updated_timestamp_utc = SYSDATE()

WHEN NOT MATCHED THEN
    -- Insert a new record if it does not exist
    INSERT (
        pk,
        report_date,
        month_start_date,
        brand_code,
        channel,
        asin,
        country_code,
        daily_affiliate_cost,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        src.pk,
        src.report_date,
        src.month_start_date,
        src.brand_code,
        src.channel,
        src.asin,
        src.country_code,
        src.daily_affiliate_cost,
        SYSDATE(),
        SYSDATE()
    );

CREATE OR REPLACE TABLE $curated_db.brand_ebitda_daily_item_level_affiliates_cost (
    pk VARCHAR,
    report_date DATE,
    month_start_date DATE,
    asin VARCHAR,
    brand_code VARCHAR,
    channel VARCHAR,
    country_code VARCHAR,
    daily_affiliate_cost FLOAT,
    record_created_timestamp_utc TIMESTAMP DEFAULT SYSDATE(),
    record_updated_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);

BEGIN TRANSACTION;

MERGE INTO $curated_db.brand_ebitda_daily_item_level_affiliates_cost AS t
USING $stage_db.merge_stg_brand_ebitda_daily_item_level_affiliates_cost AS s
ON
    t.pk = s.pk

WHEN MATCHED THEN
    UPDATE SET
        t.report_date = s.report_date,
        t.month_start_date = s.month_start_date,
        t.asin = s.asin,
        t.brand_code = s.brand_code,
        t.channel = s.channel,
        t.country_code = s.country_code,
        t.daily_affiliate_cost = s.daily_affiliate_cost,
        t.record_updated_timestamp_utc = SYSDATE()

WHEN NOT MATCHED THEN
    INSERT (
        pk,
        report_date,
        month_start_date,
        asin,
        brand_code,
        channel,
        country_code,
        daily_affiliate_cost,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        CONCAT(s.brand_code, s.asin, s.channel, s.country_code, s.report_date),
        s.report_date,
        s.month_start_date,
        s.asin,
        s.brand_code,
        s.channel,
        s.country_code,
        s.daily_affiliate_cost,
        s.record_created_timestamp_utc,
        s.record_updated_timestamp_utc
    );

-- keep only 1 primary key (the one with latest updated TS)
DELETE FROM $curated_db.brand_ebitda_daily_item_level_affiliates_cost t1
WHERE EXISTS (
    SELECT 1
    FROM $curated_db.brand_ebitda_daily_item_level_affiliates_cost t2
    WHERE t1.pk = t2.pk
    AND t1.record_updated_timestamp_utc < t2.record_updated_timestamp_utc
);

COMMIT;

CREATE OR REPLACE TABLE $stage_db.create_stg_brand_ebitda_daily_item_level_affiliates_cost AS

WITH calendar_dimension AS (
    SELECT
        "date_actual" AS report_date,
    FROM dwh.prod.calendar_dimension
),

daily_affiliates_cost_actual AS (
    SELECT
        calendar_dimension.report_date,
        affiliate.month_start_date,
        affiliate.brand_code,
        affiliate.channel,
        affiliate.asin,
        affiliate.marketplace_country_code AS country_code,
        DIV0(SUM(affiliate.affiliate_cost), EXTRACT(DAY FROM LAST_DAY(affiliate.month_start_date, 'month'))) AS daily_affiliate_cost
    FROM $stage_db.merge_brand_ebitda_affiliates_cost AS affiliate
    LEFT JOIN calendar_dimension
        ON affiliate.month_start_date = DATE_TRUNC("month", calendar_dimension.report_date)
    GROUP BY ALL
),

-- Attribute daily affiliate spend to ASINs based on their revenue contribution, for affiliate costs without asin post-2025
daily_affiliates_cost_actual_with_asin_attribution_2025 AS (
    SELECT
        actual.report_date,
        actual.month_start_date,
        actual.brand_code,
        actual.channel,
        asin_revenue.asin,
        actual.country_code,
        COALESCE(actual.daily_affiliate_cost * asin_revenue.asin_brand_revenue_share, 0) AS daily_affiliate_cost
    FROM
        daily_affiliates_cost_actual AS actual
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON actual.report_date = asin_revenue.report_date
            AND actual.brand_code = asin_revenue.brand_code
            AND actual.country_code = asin_revenue.country_code
    WHERE EXTRACT(year FROM actual.month_start_date) >= 2025
    AND actual.channel = 'AMAZON'
    AND actual.asin IS NULL
    AND actual.daily_affiliate_cost > 0
),

-- Attribute daily affiliate spend to ASINs based on their revenue contribution, as pre-2025 affiliate data is only available at the brand level.
daily_affiliates_cost_actual_with_asin_attribution AS (
    SELECT
        actual.report_date,
        actual.month_start_date,
        actual.brand_code,
        actual.channel,
        asin_revenue.asin,
        actual.country_code,
        COALESCE(actual.daily_affiliate_cost * asin_revenue.asin_brand_revenue_share, 0) AS daily_affiliate_cost
    FROM
        daily_affiliates_cost_actual AS actual
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON actual.report_date = asin_revenue.report_date
            AND actual.brand_code = asin_revenue.brand_code
            AND actual.country_code = asin_revenue.country_code
    WHERE EXTRACT(year FROM actual.month_start_date) < 2025
    AND actual.channel = 'AMAZON'
    AND actual.asin IS NULL
    AND actual.daily_affiliate_cost > 0
),

daily_affiliates_cost_estimate AS (
    -- amazon cost estimate
    SELECT
        asin_revenue.report_date AS report_date,
        DATE_TRUNC("month", asin_revenue.report_date) AS month_start_date,
        affiliate_estimate.brand_code,
        'AMAZON' AS channel,
        asin_revenue.asin,
        affiliate_estimate.marketplace_country_code AS country_code,
        asin_revenue.asin_revenue_country,
        affiliate_estimate.affiliate_spend_perc,
        affiliate_estimate.has_budget,
        CASE
            WHEN has_budget = 1 THEN affiliate_estimate.affiliate_spend_perc * asin_revenue.asin_revenue_country
            ELSE 0
        END AS daily_affiliate_cost
    FROM
        $stage_db.merge_brand_ebitda_affiliates_cost_estimate AS affiliate_estimate
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON affiliate_estimate.brand_code = asin_revenue.brand_code
            AND affiliate_estimate.marketplace_country_code = asin_revenue.country_code
            AND REPLACE(affiliate_estimate.quarter, 'Q', '') = EXTRACT(quarter FROM asin_revenue.report_date)
            AND TRY_TO_NUMBER(affiliate_estimate.year) = EXTRACT(year FROM asin_revenue.report_date)
),

actual_data_max_month AS (
    SELECT
        MAX(month_start_date) AS latest_month_with_actual
    FROM
        daily_affiliates_cost_actual
),

consolidated AS (
    SELECT
        *
    FROM
        daily_affiliates_cost_actual
    WHERE
        EXTRACT(year FROM month_start_date) >= 2025
        AND asin IS NOT NULL
        AND daily_affiliate_cost > 0

    UNION ALL

    SELECT
        report_date,
        month_start_date,
        brand_code,
        channel,
        asin,
        country_code,
        daily_affiliate_cost
    FROM
        daily_affiliates_cost_actual_with_asin_attribution

    UNION ALL

    SELECT
        report_date,
        month_start_date,
        brand_code,
        channel,
        asin,
        country_code,
        daily_affiliate_cost
    FROM
        daily_affiliates_cost_actual_with_asin_attribution_2025

    UNION ALL

    SELECT
        report_date,
        month_start_date,
        brand_code,
        channel,
        asin,
        country_code,
        daily_affiliate_cost
    FROM
        daily_affiliates_cost_estimate AS estimate
    WHERE month_start_date > (SELECT latest_month_with_actual FROM actual_data_max_month)
),

aggregated_affiliate_cost AS (
    SELECT
        report_date,
        month_start_date,
        brand_code,
        channel,
        asin,
        country_code,
        SUM(daily_affiliate_cost) AS daily_affiliate_cost
    FROM
        consolidated
    GROUP BY ALL
    HAVING SUM(daily_affiliate_cost) > 0
)

SELECT
    MD5(
        CAST(
            COALESCE(CAST(report_date AS VARCHAR), '') || '-' ||
            COALESCE(CAST(asin AS VARCHAR), '') || '-' ||
            COALESCE(CAST(brand_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(channel AS VARCHAR), '') || '-' ||
            COALESCE(CAST(country_code AS VARCHAR), '') AS VARCHAR
        )
    ) AS pk,
    *
FROM aggregated_affiliate_cost
;

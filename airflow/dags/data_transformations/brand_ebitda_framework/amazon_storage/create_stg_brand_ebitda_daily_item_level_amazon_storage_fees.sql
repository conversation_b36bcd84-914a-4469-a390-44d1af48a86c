CREATE or REPLACE TRANSIENT TABLE $stage_db.create_stg_brand_ebitda_daily_item_level_amazon_storage_fees AS

WITH base_product_data AS (
    SELECT
        "asin" AS asin,
        "sku" AS sku,
        "product_size_tier" AS product_size_tier,
        "country_code" AS country_code,
        "measurement_units" AS measurement_units,
        "weight_units" AS weight_units,
        "volume_units" AS volume_units,
        "shortest_side" AS shortest_side,
        "median_side" AS median_side,
        "longest_side" AS longest_side,
        "item_volume" AS item_volume,
    FROM
        DWH.PROD.FACT_AMAZON_FBA_STORAGE_FEES
    QUALIFY
        ROW_NUMBER() OVER (PARTITION BY "asin", "sku", "country_code" ORDER BY "record_updated_timestamp_utc" DESC) = 1
),

-- If product size tier information is missing for a specific country and SKU,
-- we prioritize using the product size tier from the same ASIN in another country with the highest item volume
products_missing_size_tier AS (
    SELECT
        asin,
        MAX(item_volume) AS max_item_volume,
    FROM
        base_product_data
    GROUP BY asin
),

size_tier_by_max_volume_fill AS (
    SELECT
        bpd.asin,
        bpd.product_size_tier,
    FROM
        base_product_data AS bpd
    LEFT OUTER JOIN
        products_missing_size_tier AS pmst
        ON
            bpd.asin = pmst.asin
    WHERE
        bpd.item_volume = pmst.max_item_volume
),

-- If storage fee report does not contain dimensional data, retrieve giftbox dimensions from the netsuite_items table for calculation.
netsuite_dimension_fallback AS (
    SELECT
        brand,
        asin,
        giftbox_l_cm,
        giftbox_w_cm,
        giftbox_h_cm,
        giftbox_gross_w_kg,
        giftbox_l_inch,
        giftbox_w_inch,
        giftbox_h_inch,
        giftbox_gross_w_lb,
    FROM
        NETSUITE.NETSUITE.NETSUITE_ITEMS
    WHERE
        asin IS NOT NULL
        AND giftbox_l_inch > 0
        AND giftbox_w_inch > 0
        AND giftbox_h_inch > 0
    QUALIFY
        ROW_NUMBER() OVER (PARTITION BY brand, asin ORDER BY giftbox_gross_w_kg DESC) = 1
),

unpivoted_data AS (
    SELECT
        brand,
        asin,
        'giftbox_l_inch' AS dimension_metric_inch,
        giftbox_l_inch AS dimension_value_inch
    FROM
        netsuite_dimension_fallback

    UNION ALL

    SELECT
        brand,
        asin,
        'giftbox_w_inch' AS dimension_metric_inch,
        giftbox_w_inch AS dimension_value_inch
    FROM
        netsuite_dimension_fallback

    UNION ALL

    SELECT
        brand,
        asin,
        'giftbox_h_inch' AS dimension_metric_inch,
        giftbox_h_inch AS dimension_value_inch
    FROM
        netsuite_dimension_fallback
),

ranked_data AS (
    SELECT
        brand,
        asin,
        dimension_metric_inch,
        dimension_value_inch,
        ROW_NUMBER() OVER (PARTITION BY brand, asin ORDER BY dimension_value_inch DESC) AS dimension_rank,
        CASE
            WHEN dimension_rank = 1 THEN 'longest_side'
            WHEN dimension_rank = 2 THEN 'median_side'
            WHEN dimension_rank = 3 THEN 'shortest_side'
        END AS dimension_side
    FROM
        unpivoted_data
),

asin_by_marketplace_country AS (
    SELECT DISTINCT "connector_region" AS country,
    FROM
        DWH.PROD.FACT_AMAZON_ORDERS
),

netsuite_dimension_fallback_by_country AS (
    SELECT
        netsuite.brand,
        netsuite.asin,
        mkp.country AS marketplace_country_code,
        MAX(CASE WHEN calc.dimension_side = 'longest_side' THEN calc.dimension_value_inch END) AS longest_side_inch,
        MAX(CASE WHEN calc.dimension_side = 'median_side' THEN calc.dimension_value_inch END) AS median_side_inch,
        MAX(CASE WHEN calc.dimension_side = 'shortest_side' THEN calc.dimension_value_inch END) AS shortest_side_inch,
        -- Converts the following 3 calculated side lengths from inch to cm
        MAX(CASE WHEN calc.dimension_side = 'longest_side' THEN calc.dimension_value_inch * 2.54 END) AS longest_side_cm,
        MAX(CASE WHEN calc.dimension_side = 'median_side' THEN calc.dimension_value_inch * 2.54 END) AS median_side_cm,
        MAX(CASE WHEN calc.dimension_side = 'shortest_side' THEN calc.dimension_value_inch * 2.54 END) AS shortest_side_cm,
        MAX(netsuite.giftbox_gross_w_kg) AS giftbox_gross_w_kg,
        MAX(netsuite.giftbox_gross_w_lb) AS giftbox_gross_w_lb,
    FROM
        netsuite_dimension_fallback AS netsuite
    LEFT OUTER JOIN
        ranked_data AS calc
        ON
            netsuite.brand = calc.brand
            AND netsuite.asin = calc.asin
    CROSS JOIN
        asin_by_marketplace_country AS mkp
    GROUP BY ALL
),

combined_product_dimension AS (
    SELECT *
    FROM (
    SELECT
        bpd.asin,
        'storage_fee_report' AS dimension_source,
        1 AS priority,
        COALESCE(bpd.product_size_tier, stmv.product_size_tier) AS product_size_tier,
        bpd.country_code,
        bpd.measurement_units,
        bpd.volume_units,
        bpd.weight_units,
        bpd.shortest_side,
        bpd.median_side,
        bpd.longest_side,
        bpd.item_volume,
    FROM
        base_product_data AS bpd
    LEFT OUTER JOIN
        size_tier_by_max_volume_fill AS stmv
        ON
            bpd.asin = stmv.asin

    UNION

    SELECT
        asin,
        'dimension_fallback' AS dimension_source,
        2 AS priority,
        CASE
            WHEN
                (median_side_inch <= 14 AND shortest_side_inch <= 8 AND longest_side_inch <= 18)
                AND giftbox_gross_w_lb <= 20 THEN 'Standard-Size' -- If the item weighs more than 20 pounds, regardless of its dimensions, it's considered oversize.
            ELSE 'Oversize'
        END AS product_size_tier, -- based on this amazon guide: https://sellercentral.amazon.com/help/hub/reference/external/GG5KW835AHDJCH8W
        marketplace_country_code,
        CASE
            WHEN marketplace_country_code IN ('US', 'UK') THEN 'inches'
            WHEN marketplace_country_code NOT IN ('US', 'UK') THEN 'centimeters'
        END AS measurement_units,
        CASE
            WHEN marketplace_country_code IN ('US', 'UK') THEN 'cubic feet'
            WHEN marketplace_country_code NOT IN ('US', 'UK') THEN 'cubic meters'
        END AS volume_units,
        NULL AS weight_units,
        CASE
            WHEN marketplace_country_code IN ('US', 'UK') THEN nid.shortest_side_inch
            WHEN marketplace_country_code NOT IN ('US', 'UK') THEN nid.shortest_side_cm
        END AS shortest_side,
        CASE
            WHEN marketplace_country_code IN ('US', 'UK') THEN nid.median_side_inch
            WHEN marketplace_country_code NOT IN ('US', 'UK') THEN nid.median_side_cm
        END AS median_side,
        CASE
            WHEN marketplace_country_code IN ('US', 'UK') THEN nid.longest_side_inch
            WHEN marketplace_country_code NOT IN ('US', 'UK') THEN nid.longest_side_cm
        END AS longest_side,
        CASE
            WHEN marketplace_country_code IN ('US', 'UK') THEN (nid.shortest_side_inch * nid.median_side_inch * nid.longest_side_inch) * 0.000578704 -- converts cubic inch to cubic feet
            WHEN marketplace_country_code NOT IN ('US', 'UK') THEN (nid.shortest_side_cm * nid.median_side_cm * nid.longest_side_cm) * 0.000001 -- converts cubic cm to cubic meter
        END AS item_volume,
    FROM
        netsuite_dimension_fallback_by_country AS nid
    )
QUALIFY ROW_NUMBER() OVER (PARTITION BY asin, country_code ORDER BY priority) = 1

),

generate_date AS (
SELECT
    '2023-01-01'::date + row_number() over(order by 0) AS report_date,
    DATE_TRUNC("month", report_date) AS month_start_date,
FROM table(generator(rowcount => 1500))
),

final_product_dimension AS (
    SELECT
        gd.report_date,
        gd.month_start_date,
        MONTH(gd.report_date) AS month,
        cpd.asin,
        cpd.dimension_source,
        cpd.product_size_tier,
        cpd.country_code,
        cpd.measurement_units,
        cpd.volume_units,
        cpd.weight_units,
        cpd.shortest_side,
        cpd.median_side,
        cpd.longest_side,
        cpd.item_volume,
    FROM
        combined_product_dimension AS cpd
    CROSS JOIN
        generate_date AS gd
    WHERE gd.report_date <= CURRENT_DATE()
),

est_storage_fee_monthly AS (
    SELECT
        "snapshot_date" AS snapshot_date,
        "month_of_charge" AS month_of_charge,
        "asin" AS asin,
        "country_code" AS country_code,
        "brand" AS brand,
        "currency" AS local_currency,
        SUM("estimated_monthly_storage_fee") AS estimated_monthly_storage_fee_local,
    FROM
        DWH.PROD.FACT_AMAZON_FBA_STORAGE_FEES AS storage
    GROUP BY ALL
),

est_storage_fee_daily AS (
    SELECT DISTINCT
        snapshot_date,
        month_of_charge,
        TO_DATE(month_of_charge || '-01') AS adj_month_of_charge,
        gd.report_date,
        asin,
        country_code,
        brand,
        local_currency,
        SUM(DIV0NULL(estimated_monthly_storage_fee_local, EXTRACT(DAY FROM LAST_DAY(adj_month_of_charge)))) AS estimated_daily_storage_fee_local,
        MAX(COALESCE(forex.exchange_rate,1)) AS fx_rate,
        estimated_daily_storage_fee_local * fx_rate AS  estimated_daily_storage_fee_usd
    FROM
        est_storage_fee_monthly AS storage
    LEFT JOIN
        generate_date AS gd
        ON TO_DATE(storage.month_of_charge || '-01') = DATE_TRUNC("month", gd.report_date)
    LEFT JOIN
        DWH.NETSUITE.FOREIGN_EXCHANGE AS forex
        ON gd.report_date = forex.effective_date
        AND storage.local_currency = forex.transactional_currency
        AND forex.base_currency = 'USD'
    GROUP BY ALL
),

daily_inventory_level AS (
    SELECT
        "snapshot_date" AS snapshot_date,
        "asin" AS asin,
        REPLACE("country_code", 'GB', 'UK') AS country_code,
        SUM(COALESCE("afn_inbound_receiving_quantity", 0)) AS afn_inbound_receiving_quantity, -- receiving
        SUM(COALESCE("afn_fulfillable_quantity", 0)) AS afn_fulfillable_quantity, -- available
        SUM(COALESCE("fc_transfer_restock", 0)) AS fc_transfer_restock, -- reserved
        SUM(COALESCE("fc_processing_restock", 0)) AS fc_processing_restock, -- reserved
        afn_inbound_receiving_quantity + afn_fulfillable_quantity + fc_transfer_restock + fc_processing_restock AS total_onhand_inventory,
    FROM
        DWH.PROD.FACT_AMAZON_INVENTORY
    GROUP BY ALL
),

country_inventory_level AS (
    SELECT
        snapshot_date,
        country_code,
        SUM(total_onhand_inventory) AS total_onhand_inventory_country
    FROM daily_inventory_level
    GROUP BY ALL
),

sku_storage_fee AS (
    SELECT
        fpd.report_date,
        fpd.month,
        fpd.dimension_source,
        COALESCE(dil.asin, fpd.asin) AS asin,
        COALESCE(fpd.country_code, dil.country_code) AS country_code,
        fpd.product_size_tier,
        fpd.measurement_units,
        fpd.weight_units,
        fpd.volume_units,
        MAX(fpd.shortest_side) AS shortest_side,
        MAX(fpd.median_side) AS median_side,
        MAX(fpd.longest_side) AS longest_side,
        MAX(fpd.item_volume) AS item_volume,
        SUM(COALESCE(dil.afn_fulfillable_quantity, 0)) AS available_inventory,
        SUM(COALESCE(dil.fc_transfer_restock, 0) + COALESCE(dil.fc_processing_restock, 0)) AS reserved_inventory,
        SUM(COALESCE(dil.afn_inbound_receiving_quantity, 0)) AS receiving_inventory,
        SUM(COALESCE(dil.total_onhand_inventory, 0)) AS total_onhand_inventory,
        CASE WHEN fpd.item_volume IS NULL AND dil.total_onhand_inventory > 0 THEN TRUE ELSE FALSE END AS missing_item_volume,
        MAX(COALESCE(rate.total_monthly_storage_fee, 0)) AS total_monthly_storage_fee,
        SUM(DIV0NULL(rate.total_monthly_storage_fee, EXTRACT(DAY FROM LAST_DAY(fpd.report_date)))) AS total_daily_storage_fee,
        CASE
            WHEN DATE_TRUNC("month", fpd.report_date) <= DATEADD(month, -2, DATE_TRUNC("month", CURRENT_DATE())) THEN 'actual' ELSE 'estimate' END AS calc_type,
        -- CASE 1: For reports <= 2 months old, the storage fee is estimated based on item_volume and stock_level, plus a plug, to align with MBR reporting.
        -- CASE 2: When the report date is > 2 months, the storage fee cost is derived directly from the Amazon FBA storage fee report, with the addition of a plug.
        SUM(CASE
            WHEN DATE_TRUNC("month", fpd.report_date) <= DATEADD(month, -2, DATE_TRUNC("month", CURRENT_DATE())) THEN sf.estimated_daily_storage_fee_local -- actual cost coming from SP API
            ELSE DIV0NULL(rate.total_monthly_storage_fee, EXTRACT(DAY FROM LAST_DAY(fpd.report_date))) * COALESCE(dil.total_onhand_inventory, 0) * fpd.item_volume
        END) AS total_daily_storage_fee_local,
        SUM(CASE
            WHEN DATE_TRUNC("month", fpd.report_date) <= DATEADD(month, -2, DATE_TRUNC("month", CURRENT_DATE())) THEN sf.estimated_daily_storage_fee_usd -- actual cost coming from SP API
            ELSE DIV0NULL(rate.total_monthly_storage_fee, EXTRACT(DAY FROM LAST_DAY(fpd.report_date))) * COALESCE(fx.exchange_rate, 1) * COALESCE(dil.total_onhand_inventory, 0) * fpd.item_volume
        END) AS total_daily_storage_fee_usd,
        SUM(COALESCE(sf.estimated_daily_storage_fee_usd, 0)) AS actual_storage_fee_usd,
        SUM(DIV0NULL(rate.total_monthly_storage_fee, EXTRACT(DAY FROM LAST_DAY(fpd.report_date))) * COALESCE(fx.exchange_rate, 1) * COALESCE(dil.total_onhand_inventory, 0) * fpd.item_volume) AS estimated_storage_fee_usd,
    FROM
        final_product_dimension AS fpd
    FULL OUTER JOIN
        daily_inventory_level AS dil
        ON
            fpd.asin = dil.asin
            AND fpd.country_code = dil.country_code
            AND fpd.report_date = dil.snapshot_date
    LEFT OUTER JOIN
        DWH_DEV.RAW.AMAZON_STORAGE_FEE_RATE_CARD AS rate
        ON
            fpd.country_code = rate.country
            AND fpd.month = rate.month
            AND fpd.product_size_tier = rate.size
    LEFT OUTER JOIN
        est_storage_fee_daily AS sf
        ON
            fpd.country_code = sf.country_code
            AND fpd.asin = sf.asin
            AND fpd.report_date = sf.report_date
    LEFT OUTER JOIN
        DWH.NETSUITE.FOREIGN_EXCHANGE AS fx
        ON
            fpd.report_date = fx.effective_date
            AND rate.currency = fx.transactional_currency
            AND fx.base_currency = 'USD'
    WHERE
        rate.storage_utilization_ratio IN ('Below 26 weeks', 'Below 22 weeks')
    GROUP BY ALL
),

asin_storage_fee_with_plug AS (
    SELECT
        storage.report_date,
        storage.month,
        storage.dimension_source,
        storage.calc_type,
        storage.asin,
        storage.country_code,
        storage.product_size_tier,
        storage.volume_units,
        storage.item_volume,
        storage.measurement_units,
        storage.shortest_side,
        storage.median_side,
        storage.longest_side,
        storage.available_inventory,
        storage.reserved_inventory,
        storage.receiving_inventory,
        storage.total_onhand_inventory,
        country.total_onhand_inventory_country,
        DIV0NULL(storage.total_onhand_inventory, country.total_onhand_inventory_country) AS asin_inventory_perc_country,
        storage.total_daily_storage_fee_usd,
        CASE
            WHEN storage.country_code = 'US' THEN COALESCE(storage.total_daily_storage_fee_usd, 0) + (DIV0NULL(200000, EXTRACT(DAY FROM LAST_DAY(storage.report_date))) * DIV0NULL(storage.total_onhand_inventory, country.total_onhand_inventory_country))
            ELSE COALESCE(storage.total_daily_storage_fee_usd, 0) + (DIV0NULL(6000, EXTRACT(DAY FROM LAST_DAY(storage.report_date))) * DIV0NULL(storage.total_onhand_inventory, country.total_onhand_inventory_country))
        END AS total_daily_storage_fee_usd_with_plug,
        storage.actual_storage_fee_usd,
        storage.estimated_storage_fee_usd,
    FROM sku_storage_fee AS storage
    LEFT JOIN country_inventory_level AS country
        ON storage.country_code = country.country_code
        AND storage.report_date = country.snapshot_date
)

SELECT
    MD5(
        CAST(
            COALESCE(CAST(REPORT_DATE AS VARCHAR), '') || '-' ||
            COALESCE(CAST(ASIN AS VARCHAR), '') || '-' ||
            COALESCE(CAST(COUNTRY_CODE AS VARCHAR), '') AS VARCHAR
        )
    ) AS pk,
    *
FROM asin_storage_fee_with_plug

CREATE TABLE IF NOT EXISTS $curated_db.brand_ebitda_daily_item_amazon_storage_fees (
    pk VARCHAR
    , report_date DATE
    , month INTEGER
    , dimension_source VARCHAR
    , calc_type VARCHAR
    , asin VARCHAR
    , country_code VARCHAR
    , product_size_tier VARCHAR
    , volume_units VARCHAR
    , item_volume FLOAT
    , measurement_units VARCHAR
    , shortest_side FLOAT
    , median_side FLOAT
    , longest_side FLOAT
    , available_inventory INT
    , reserved_inventory INT
    , receiving_inventory INT
    , total_onhand_inventory INT
    , total_onhand_inventory_country INT
    , asin_inventory_perc_country FLOAT
    , total_daily_storage_fee_usd FLOAT
    , total_daily_storage_fee_usd_with_plug FLOAT
    , actual_storage_fee_usd FLOAT
    , estimated_storage_fee_usd FLOAT
    , record_created_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
    , record_updated_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);

BEGIN TRANSACTION;

MERGE INTO $curated_db.brand_ebitda_daily_item_amazon_storage_fees t
USING $stage_db.merge_stg_brand_ebitda_daily_item_level_amazon_storage_fees s
ON t.pk = s.pk
WHEN MATCHED THEN
    UPDATE SET
        report_date = s.report_date
        , month = s.month
        , dimension_source = s.dimension_source
        , calc_type = s.calc_type
        , asin = s.asin
        , country_code = s.country_code
        , product_size_tier = s.product_size_tier
        , volume_units = s.volume_units
        , item_volume = s.item_volume
        , measurement_units = s.measurement_units
        , shortest_side = s.shortest_side
        , median_side = s.median_side
        , longest_side = s.longest_side
        , available_inventory = s.available_inventory
        , reserved_inventory = s.reserved_inventory
        , receiving_inventory = s.receiving_inventory
        , total_onhand_inventory = s.total_onhand_inventory
        , total_onhand_inventory_country = s.total_onhand_inventory_country
        , asin_inventory_perc_country = s.asin_inventory_perc_country
        , total_daily_storage_fee_usd = s.total_daily_storage_fee_usd
        , total_daily_storage_fee_usd_with_plug = s.total_daily_storage_fee_usd_with_plug
        , actual_storage_fee_usd = s.actual_storage_fee_usd
        , estimated_storage_fee_usd = s.estimated_storage_fee_usd
        , record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
    INSERT (
        pk
        , report_date
        , month
        , dimension_source
        , calc_type
        , asin
        , country_code
        , product_size_tier
        , volume_units
        , item_volume
        , measurement_units
        , shortest_side
        , median_side
        , longest_side
        , available_inventory
        , reserved_inventory
        , receiving_inventory
        , total_onhand_inventory
        , total_onhand_inventory_country
        , asin_inventory_perc_country
        , total_daily_storage_fee_usd
        , total_daily_storage_fee_usd_with_plug
        , actual_storage_fee_usd
        , estimated_storage_fee_usd
        , record_created_timestamp_utc
        , record_updated_timestamp_utc
    )
    VALUES (
        s.pk
        , s.report_date
        , s.month
        , s.dimension_source
        , s.calc_type
        , s.asin
        , s.country_code
        , s.product_size_tier
        , s.volume_units
        , s.item_volume
        , s.measurement_units
        , s.shortest_side
        , s.median_side
        , s.longest_side
        , s.available_inventory
        , s.reserved_inventory
        , s.receiving_inventory
        , s.total_onhand_inventory
        , s.total_onhand_inventory_country
        , s.asin_inventory_perc_country
        , s.total_daily_storage_fee_usd
        , s.total_daily_storage_fee_usd_with_plug
        , s.actual_storage_fee_usd
        , s.estimated_storage_fee_usd
        , SYSDATE()
        , SYSDATE()
    );

DELETE FROM $curated_db.brand_ebitda_daily_item_amazon_storage_fees
WHERE (pk, record_updated_timestamp_utc) NOT IN (
    SELECT pk, MAX(record_updated_timestamp_utc)
    FROM $curated_db.brand_ebitda_daily_item_amazon_storage_fees
    GROUP BY pk
);

COMMIT;
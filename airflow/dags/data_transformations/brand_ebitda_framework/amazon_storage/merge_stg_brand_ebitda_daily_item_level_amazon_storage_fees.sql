CREATE TABLE IF NOT EXISTS $stage_db.merge_stg_brand_ebitda_daily_item_level_amazon_storage_fees AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.create_stg_brand_ebitda_daily_item_level_amazon_storage_fees
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_stg_brand_ebitda_daily_item_level_amazon_storage_fees AS tgt
USING
    (
        SELECT
            *
        FROM $stage_db.create_stg_brand_ebitda_daily_item_level_amazon_storage_fees
        WHERE report_date >= CURRENT_DATE - INTERVAL '60 day'
        QUALIFY ROW_NUMBER() OVER (PARTITION BY pk ORDER BY report_date DESC) = 1 -- dedupe
    ) AS src
ON src.pk = tgt.pk

WHEN MATCHED THEN
    UPDATE SET
        tgt.report_date = src.report_date,
        tgt.month = src.month,
        tgt.dimension_source = src.dimension_source,
        tgt.calc_type = src.calc_type,
        tgt.asin = src.asin,
        tgt.country_code = src.country_code,
        tgt.product_size_tier = src.product_size_tier,
        tgt.volume_units = src.volume_units,
        tgt.item_volume = src.item_volume,
        tgt.measurement_units = src.measurement_units,
        tgt.shortest_side = src.shortest_side,
        tgt.median_side = src.median_side,
        tgt.longest_side = src.longest_side,
        tgt.available_inventory = src.available_inventory,
        tgt.reserved_inventory = src.reserved_inventory,
        tgt.receiving_inventory = src.receiving_inventory,
        tgt.total_onhand_inventory = src.total_onhand_inventory,
        tgt.total_onhand_inventory_country = src.total_onhand_inventory_country,
        tgt.asin_inventory_perc_country = src.asin_inventory_perc_country,
        tgt.total_daily_storage_fee_usd = src.total_daily_storage_fee_usd,
        tgt.total_daily_storage_fee_usd_with_plug = src.total_daily_storage_fee_usd_with_plug,
        tgt.actual_storage_fee_usd = src.actual_storage_fee_usd,
        tgt.estimated_storage_fee_usd = src.estimated_storage_fee_usd
WHEN NOT MATCHED THEN
    INSERT (
        pk,
        report_date,
        month,
        dimension_source,
        calc_type,
        asin,
        country_code,
        product_size_tier,
        volume_units,
        item_volume,
        measurement_units,
        shortest_side,
        median_side,
        longest_side,
        available_inventory,
        reserved_inventory,
        receiving_inventory,
        total_onhand_inventory,
        total_onhand_inventory_country,
        asin_inventory_perc_country,
        total_daily_storage_fee_usd,
        total_daily_storage_fee_usd_with_plug,
        actual_storage_fee_usd,
        estimated_storage_fee_usd,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        src.pk,
        src.report_date,
        src.month,
        src.dimension_source,
        src.calc_type,
        src.asin,
        src.country_code,
        src.product_size_tier,
        src.volume_units,
        src.item_volume,
        src.measurement_units,
        src.shortest_side,
        src.median_side,
        src.longest_side,
        src.available_inventory,
        src.reserved_inventory,
        src.receiving_inventory,
        src.total_onhand_inventory,
        src.total_onhand_inventory_country,
        src.asin_inventory_perc_country,
        src.total_daily_storage_fee_usd,
        src.total_daily_storage_fee_usd_with_plug,
        src.actual_storage_fee_usd,
        src.estimated_storage_fee_usd,
        SYSDATE(),
        SYSDATE()
    );

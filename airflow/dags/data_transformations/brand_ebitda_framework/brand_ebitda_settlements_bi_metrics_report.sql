CREATE OR REPLACE TABLE $stage_db.brand_ebitda_sku_settlements_metrics AS 
WITH clean_settlement_data AS (
SELECT 
    DATE_TRUNC('day', "report_date") AS "report_date",
    "settlement_id",
    "seller_id",
    "country_code",
    "type", 
    "order_id",
    "sku",
    CASE WHEN 
    REGEXP_LIKE(LOWER("description"), '.*countyline.*|.*flask.*|.*numnum.*|.*sleepmantra.*|.*hathaspace.*|.*doca.*|.*moonshine.*|.*bambody.*|.*surviveware.*|.*swiss.*|.*boka.*|.*zitsticka.*|.*freshcap.*|.*bliss.*|.*ballsy.*|.*shroomies.*|.*treeoflife.*|.*cavetools.*|.*organic.*|.*absorbent.*|.*risa.*|.*seedr.*|.*trava.*|.*logmouse.*|.*mulberry.*|.*elegance.*|.*bike.*|.*kolua.*|.*leebeauty.*|.*hyaluronic.*|.*glitch.*|.*harmony.*|.*serum.*|.*ridekac.*|.*flapdisc.*|.*wax.*|.*water.*|.*mattress.*|.*wine.*|.*rain.*|.*knife.*|.*spice.*|.*mouth.*|.*chisel*') THEN 'product_description' 
    
    WHEN REGEXP_LIKE(LOWER("description"),  '.*deals.*|.*lightning deal.*') THEN 'deal_fees_description'
    WHEN REGEXP_LIKE(LOWER("description"),  '.*coupon.*') THEN 'coupon_fees_description'
    WHEN REGEXP_LIKE(LOWER("description"),  '.*fba customer returns.*') THEN 'fba_customer_return_fees_description'
    
    ELSE 'other_description' END description_category,
    LOWER("description") AS description,
    "marketplace",
    "fulfillment_channel",
    "order_city", 
    "order_state", 
    "quantity",
    "product sales",
    "product sales tax", 
    "shipping credits",
    "shipping credits tax",
    "gift wrap credits",
    "giftwrap credits tax",
    "regulatory fee",
    "tax on regulatory fee",
    "promotional rebates",
    "promotional rebates tax",
    "marketplace withheld tax",
    "selling fees",
    "fba fees",
    "other transaction fees",
    "other",
    "total",
    "tax_collection_model",
    "etl_batch_runtime",
    "order_postal",
    "account_type"

FROM DWH.STAGING.AMAZON_SETTLEMENTS_TRANSACTIONS
),

seller_sku_contribution AS (
  SELECT 
    DATE_TRUNC('day', shipments."shipment_date_utc") AS shipping_date, 
    settlements."country_code",
    settlements."seller_id",
    settlements."sku", 
    SUM(settlements."product sales") / NULLIF(SUM(SUM(settlements."product sales")) OVER(PARTITION BY settlements."seller_id", DATE_TRUNC('day', shipments."shipment_date_utc")), 0) AS daily_sku_contribution
FROM clean_settlement_data AS settlements
LEFT JOIN DWH.PROD.fact_all_shipments AS shipments
    ON settlements."order_id" = shipments."external_order_id"
GROUP BY 
    DATE_TRUNC('day', shipments."shipment_date_utc"), 
    settlements."country_code", 
    settlements."sku",
    settlements."seller_id"
),

disntinct_sku_country AS (
SELECT DISTINCT
    DATE_TRUNC('day', "report_date") AS "report_date",
    "seller_id",
    "country_code",
    "sku"
    FROM clean_settlement_data
), 

distinct_sku_brand_map AS (
 SELECT 
    ASIN, 
    SKU, 
    country_code,
    BRAND_CODE
FROM DWH.PROD.SKU_ASIN_ITEM_MAPPING
WHERE country_code IS NOT NULL
QUALIFY ROW_NUMBER() OVER (PARTITION BY SKU, country_code ORDER BY ASIN) = 1
),

base_aggregation_data AS (
 SELECT 
 cd."report_date",
 cd."seller_id",
 cd."country_code",
 cd."sku",
 map."BRAND_CODE",
 map."ASIN"
 FROM disntinct_sku_country AS cd
LEFT JOIN distinct_sku_brand_map AS map
    ON cd."sku" = map."SKU"
    AND cd."country_code" =map."COUNTRY_CODE"
    
),

shipment_details AS (
select 
DATE_TRUNC('day',"shipment_date_utc") AS shipping_date, 
"external_order_id"
from DWH.PROD.fact_all_shipments
WHERE "shipment_date_utc" is not null
QUALIFY ROW_NUMBER() OVER (PARTITION BY "external_order_id" ORDER BY "shipment_date_utc") = 1
),

amz_gross_revenue AS (
SELECT 
shipments.shipping_date AS shipping_date,
settlements."seller_id",
settlements."country_code", 
settlements."sku",
SUM(settlements."product sales") AS revenue_sales_orders_sku_allocated,
SUM(settlements."quantity") AS quantity_gross_amz_sku, 
SUM(settlements."promotional rebates") AS revenue_discounts_orders_sku_allocated,
SUM(settlements."shipping credits") AS revenue_shipping_orders_sku_allocated, 
SUM(settlements."gift wrap credits") AS revenue_giftwrap_orders_sku_allocated, 
SUM(settlements."selling fees") AS sellingfees_orders_sku_allocated,
SUM(settlements."fba fees") AS fbafees_orders_sku_allocated,
SUM(settlements."other") AS revenue_other_orders_sku_allocated
FROM clean_settlement_data as settlements
LEFT JOIN shipment_details AS shipments
ON settlements."order_id" = shipments."external_order_id"
WHERE "type" ='Order'
AND settlements."sku" is not null 
and description_category ='product_description'
and LOWER(settlements."marketplace") ILIKE '%amazon%'

GROUP BY 1,2,3,4

), 

amz_sku_refund AS (
SELECT 
DATE_TRUNC('day', settlements."report_date") AS report_date, 
settlements."country_code", 
"sku",
SUM(settlements."product sales") AS revenue_sales_refunds_sku_allocated,
SUM(settlements."quantity") AS quantity_amz_sku_refund,
SUM(settlements."promotional rebates") AS revenue_discounts_refunds_sku_allocated,
SUM(settlements."shipping credits") AS revenue_shipping_refunds_sku_allocated,
SUM(settlements."gift wrap credits") AS revenue_giftwrap_refunds_sku_allocated,
SUM(settlements."selling fees") AS sellingfees_refunds_sku_allocated,
SUM(settlements."fba fees") AS fbafees_refunds_sku_allocated,
SUM(settlements."other") AS revenue_other_refunds_sku_allocated,
FROM clean_settlement_data as settlements
WHERE "type" ='Refund'
AND "sku" is not null 
and description_category ='product_description'
and LOWER("marketplace") ILIKE '%amazon%'
GROUP BY 1,2,3

), 


amz_sku_inventory_reimb AS (
SELECT 
DATE_TRUNC('day', settlements."report_date") AS report_date, 
settlements."country_code", 
"sku",
SUM(settlements."other") AS revenue_reimbursement_adjustment_sku_allocated
FROM clean_settlement_data as settlements
WHERE "type" ='Adjustment'
AND "sku" is not null 
and description ILIKE LOWER('FBA Inventory Reimbursement%')
GROUP BY 1,2,3

),




amz_sku_liquidation AS (
SELECT 
DATE_TRUNC('day', settlements."report_date") AS report_date, 
settlements."country_code", 
"sku",
SUM(settlements."product sales") AS revenue_sales_liquidations_sku_allocated,
SUM(settlements."other") AS revenue_other_liquidations_sku_allocated
FROM clean_settlement_data as settlements
WHERE "type" ILIKE 'Liquidation%'
AND "sku" is not null 

GROUP BY 1,2,3
), 

mcf_sku_metrics AS (
SELECT 
DATE_TRUNC('day', settlements."report_date") AS report_date, 
settlements."country_code", 
"sku",
SUM(settlements."quantity") AS quanity_mcf_orders_sku_allocated,
SUM(settlements."fba fees") AS fbafees_mcf_orders_sku_allocated,
SUM(settlements."other") AS other_fees_mcf_orders_sku_allocated,
FROM clean_settlement_data as settlements
WHERE  "sku" is not null 
AND LOWER("marketplace") LIKE 'sim1%'
GROUP BY 1,2,3
),

fba_return_fees AS (
SELECT 
DATE_TRUNC('day', settlements."report_date") AS report_date, 
settlements."country_code", 
"sku",
SUM(settlements."fba fees") AS fbafees_returns__sku_allocated,
SUM(settlements."other") AS other_fees_returns__sku_allocated,
FROM clean_settlement_data as settlements
WHERE "type" ='FBA Customer Return Fee'
AND "sku" is not null 
AND LOWER("marketplace") LIKE 'amazon%'
GROUP BY 1,2,3
),


amz_non_sku_reim_adjustment AS (
SELECT 
DATE_TRUNC('day', settlements."report_date") AS report_date, 
settlements."country_code", 
"seller_id",
SUM(settlements."other") AS revenue_reimbursement_adjustment_non_sku_allocated
FROM clean_settlement_data as settlements
WHERE "type" ='Adjustment'
AND "sku" is null 
GROUP BY 1,2,3

),

amz_non_sku_service_fees AS (
SELECT 
DATE_TRUNC('day', settlements."report_date") AS report_date, 
settlements."country_code", 
"seller_id",
SUM(settlements."other") AS advertisingfees_subscription__non_sku_allocated
FROM clean_settlement_data as settlements
WHERE "type" ='Service Fee'
AND "sku" is null 
AND description IN ('vine enrollment fee','subscription')
GROUP BY 1,2,3

),


amz_non_sku_storage_other_fees AS (
SELECT 
DATE_TRUNC('day', settlements."report_date") AS report_date, 
settlements."country_code", 
"seller_id",
SUM(settlements."other") AS storagefees_other__non_sku_allocated
FROM clean_settlement_data as settlements
WHERE
 "sku" is null 
AND LOWER("marketplace") LIKE 'amazon%'
AND REGEXP_LIKE(LOWER(description),  '.*fba long_term storage fee.*|.*fba storage fee.*|.*fba prep fee.*|.*fba removal order.*|.*awd.*')
GROUP BY 1,2,3

),

amz_non_sku_storage_fees AS (
SELECT 
DATE_TRUNC('day', settlements."report_date") AS report_date, 
settlements."country_code", 
"seller_id",
SUM(settlements."other") AS storagefees_storage_non_sku_allocated
FROM clean_settlement_data as settlements
WHERE "sku" is null 
AND LOWER("marketplace") LIKE 'amazon%'
AND REGEXP_LIKE(LOWER(description),  'fba inventory storage fee')
GROUP BY 1,2,3

),

amz_non_sku_premium_fees AS (
SELECT 
DATE_TRUNC('day', settlements."report_date") AS report_date, 
settlements."country_code", 
"seller_id",
SUM(settlements."other") AS mbsfees__non_sku_allocated
FROM clean_settlement_data as settlements
WHERE "sku" is null 
AND LOWER("marketplace") LIKE 'amazon%'
AND REGEXP_LIKE(LOWER(description),  'premium services fee')
GROUP BY 1,2,3

),


amz_non_sku_inbound_placement_fees AS (
SELECT 
DATE_TRUNC('day', settlements."report_date") AS report_date, 
settlements."country_code", 
"seller_id",
SUM(settlements."other") AS cogs_placement__non_sku_allocated
FROM clean_settlement_data as settlements
WHERE  "sku" is null 
AND LOWER("marketplace") LIKE 'amazon%'
AND REGEXP_LIKE(LOWER(description),  'fba inbound placement service fee')
GROUP BY 1,2,3

),


amz_non_sku_cogs_other_fees AS (
SELECT 
DATE_TRUNC('day', settlements."report_date") AS report_date, 
settlements."country_code", 
"seller_id",
SUM(settlements."other") AS cogs_other_non_sku_allocated
FROM clean_settlement_data as settlements
WHERE  "sku" is null 
AND LOWER("marketplace") LIKE 'amazon%'
AND REGEXP_LIKE(LOWER(description),  'fba international freight shipping charge | fba amazon_partnered carrier shipment fee')
GROUP BY 1,2,3

),


amz_non_sku_advertising_fees AS (
SELECT 
DATE_TRUNC('day', settlements."report_date") AS report_date, 
settlements."country_code", 
"seller_id",
SUM(settlements."other transaction fees") AS advertisingfees_deals__non_sku_allocated
FROM clean_settlement_data as settlements
WHERE "sku" is null 
AND LOWER("marketplace") LIKE 'amazon%'
AND REGEXP_LIKE(LOWER(description),  '.*deals.*|.*coupon redemption.*|.*lightning deal.*')
GROUP BY 1,2,3
)


SELECT 
    gross.shipping_date,
    base."report_date",
    base."seller_id",
    base."country_code",
    base."sku",
    base."BRAND_CODE",
    base."ASIN",
    
    gross.revenue_sales_orders_sku_allocated,
    gross.quantity_gross_amz_sku,
    gross.revenue_discounts_orders_sku_allocated,
    gross.revenue_shipping_orders_sku_allocated, 
    gross.revenue_giftwrap_orders_sku_allocated, 
    gross.sellingfees_orders_sku_allocated,
    gross.fbafees_orders_sku_allocated,
    gross.revenue_other_orders_sku_allocated,



    refund.revenue_sales_refunds_sku_allocated,
    refund.quantity_amz_sku_refund,
    refund.revenue_discounts_refunds_sku_allocated,
    refund.revenue_shipping_refunds_sku_allocated,
    refund.revenue_giftwrap_refunds_sku_allocated,
    refund.sellingfees_refunds_sku_allocated,
    refund.fbafees_refunds_sku_allocated,
    refund.revenue_other_refunds_sku_allocated,


    
    reimb.revenue_reimbursement_adjustment_sku_allocated,
    liquidation.revenue_sales_liquidations_sku_allocated,
    liquidation.revenue_other_liquidations_sku_allocated,
    
    mcf.quanity_mcf_orders_sku_allocated,
    mcf.fbafees_mcf_orders_sku_allocated,
    mcf.other_fees_mcf_orders_sku_allocated,
    
    returns.fbafees_returns__sku_allocated,
    returns.other_fees_returns__sku_allocated,
    
    sku_contribution.daily_sku_contribution,
    --Allocate non_SKU level costs using daily SKU revenue contribution
    non_sku_reim.revenue_reimbursement_adjustment_non_sku_allocated * sku_contribution.daily_sku_contribution AS revenue_reimbursement_adjustment_non_sku_allocated,
    non_sku_service.advertisingfees_subscription__non_sku_allocated * sku_contribution.daily_sku_contribution AS advertisingfees_subscription__non_sku_allocated,
    non_sku_storage.storagefees_other__non_sku_allocated * sku_contribution.daily_sku_contribution AS storagefees_other__non_sku_allocated,
    non_sku_storage_fees.storagefees_storage_non_sku_allocated * sku_contribution.daily_sku_contribution AS storagefees_storage_non_sku_allocated,
    non_sku_premium.mbsfees__non_sku_allocated * sku_contribution.daily_sku_contribution AS mbsfees__non_sku_allocated,
    non_sku_inbound.cogs_placement__non_sku_allocated * sku_contribution.daily_sku_contribution AS cogs_placement__non_sku_allocated,
    non_sku_cogs.cogs_other_non_sku_allocated * sku_contribution.daily_sku_contribution AS cogs_other_non_sku_allocate,
    non_sku_ad.advertisingfees_deals__non_sku_allocated * sku_contribution.daily_sku_contribution AS advertisingfees_deals__non_sku_allocated
    
FROM base_aggregation_data AS base
LEFT JOIN amz_gross_revenue AS gross
    ON base."report_date" = gross.shipping_date
    AND base."country_code" = gross."country_code"
    AND base."sku" = gross."sku"
LEFT JOIN amz_sku_refund AS refund
    ON gross.shipping_date = refund.report_date
    AND gross."country_code" = refund."country_code"
    AND gross."sku" = refund."sku"
LEFT JOIN amz_sku_inventory_reimb AS reimb
    ON gross.shipping_date = reimb.report_date
    AND gross."country_code" = reimb."country_code"
    AND gross."sku" = reimb."sku"
LEFT JOIN amz_sku_liquidation AS liquidation
    ON base."report_date" = liquidation.report_date
    AND base."country_code" = liquidation."country_code"
    AND base."sku" = liquidation."sku"
LEFT JOIN mcf_sku_metrics AS mcf
    ON gross.shipping_date = mcf.report_date
    AND gross."country_code" = mcf."country_code"
    AND gross."sku" = mcf."sku"
LEFT JOIN fba_return_fees AS returns
    ON base."report_date" = returns.report_date
    AND base."country_code" = returns."country_code"
    AND base."ASIN" = returns."sku"
LEFT JOIN seller_sku_contribution AS sku_contribution
    ON gross.shipping_date = sku_contribution.shipping_date
    AND gross."country_code" = sku_contribution."country_code"
    AND gross."seller_id" = sku_contribution."seller_id"
    AND gross."sku" = sku_contribution."sku"
LEFT JOIN amz_non_sku_reim_adjustment AS non_sku_reim
    ON base."report_date" = non_sku_reim.report_date
    AND base."country_code" = non_sku_reim."country_code"
    AND base."seller_id" = non_sku_reim."seller_id"
LEFT JOIN amz_non_sku_service_fees AS non_sku_service
    ON base."report_date"  = non_sku_service.report_date
    AND base."country_code" = non_sku_service."country_code"
    AND base."seller_id" = non_sku_service."seller_id"
LEFT JOIN amz_non_sku_storage_other_fees AS non_sku_storage
    ON base."report_date"  = non_sku_storage.report_date
    AND base."country_code" = non_sku_storage."country_code"
    AND base."seller_id" = non_sku_storage."seller_id"
LEFT JOIN amz_non_sku_storage_fees AS non_sku_storage_fees
    ON base."report_date"  = non_sku_storage_fees.report_date
    AND base."country_code" = non_sku_storage_fees."country_code"
    AND base."seller_id" = non_sku_storage_fees."seller_id"
LEFT JOIN amz_non_sku_premium_fees AS non_sku_premium
    ON base."report_date" = non_sku_premium.report_date
    AND base."country_code" = non_sku_premium."country_code"
    AND base."seller_id" = non_sku_premium."seller_id"
LEFT JOIN amz_non_sku_inbound_placement_fees AS non_sku_inbound
    ON base."report_date"  = non_sku_inbound.report_date
    AND base."country_code" = non_sku_inbound."country_code"
    AND base."seller_id" = non_sku_inbound."seller_id"
LEFT JOIN amz_non_sku_cogs_other_fees AS non_sku_cogs
    ON base."report_date"  = non_sku_cogs.report_date
    AND base."country_code" = non_sku_cogs."country_code"
    AND base."seller_id" = non_sku_cogs."seller_id"
LEFT JOIN amz_non_sku_advertising_fees AS non_sku_ad
    ON base."report_date"  = non_sku_ad.report_date
    AND base."country_code" = non_sku_ad."country_code"
    AND base."seller_id" = non_sku_ad."seller_id";





MERGE INTO $stage_db.wip_incre_merge_brand_ebitda_all_markets_item_level_ops_hourly_merge AS tgt
USING (
  SELECT * FROM $stage_db.wip_incre_stg_brand_ebitda_all_markets_item_level_ops_hourly_stg
) AS src
ON tgt.marketplace = src.marketplace
   AND tgt.channel = src.channel
   AND tgt.sku = src.sku
   AND COALESCE(tgt.asin, 'NO_ASIN') = COALESCE(src.asin, 'NO_ASIN')
   AND COALESCE(tgt.brand_code, 'NO_BRAND') = COALESCE(src.brand_code, 'NO_BRAND')
   AND COALESCE(tgt.brand_name, 'NO_BRAND') = COALESCE(src.brand_name, 'NO_BRAND')
   AND tgt.country_code = src.country_code
   AND tgt.purchase_hour_datetime = src.purchase_hour_datetime
WHEN MATCHED THEN UPDATE SET
  tgt.total_quantity = src.total_quantity,
    tgt.item_price_lc = src.item_price_lc,
    tgt.item_price_usd = src.item_price_usd,
    tgt.shipping_price_lc = src.shipping_price_lc,
    tgt.shipping_price_usd = src.shipping_price_usd,
    tgt.item_promo_discount_lc = src.item_promo_discount_lc,
    tgt.item_promo_discount_usd = src.item_promo_discount_usd,
    tgt.ship_promo_discount_lc = src.ship_promo_discount_lc,
    tgt.ship_promo_discount_usd = src.ship_promo_discount_usd,
    tgt.giftwrap_price_lc = src.giftwrap_price_lc,
    tgt.giftwrap_price_usd = src.giftwrap_price_usd,
    tgt.cancelled_order_count = src.cancelled_order_count,
    tgt.total_order_count = src.total_order_count,
    tgt.ordered_product_sales_lc = src.ordered_product_sales_lc,
    tgt.ordered_product_sales_usd = src.ordered_product_sales_usd,
    tgt.last_year_total_ordered_product_sales_usd = src.last_year_total_ordered_product_sales_usd
    -- include other columns as needed
WHEN NOT MATCHED THEN INSERT (
    marketplace,
    channel, 
    brand_code, 
    brand_name, 
    brand_category,
    sku,
    asin,
    product_name, 
    country_code,
    purchase_hour_datetime,
    total_quantity,
    item_price_lc,
    item_price_usd,
    shipping_price_lc,
    shipping_price_usd,
    giftwrap_price_lc,
    giftwrap_price_usd,
    item_promo_discount_lc,
    item_promo_discount_usd,
    ship_promo_discount_lc,
    ship_promo_discount_usd,
    cancelled_order_count,
    total_order_count,
    ordered_product_sales_lc,
    ordered_product_sales_usd, 
    last_year_total_ordered_product_sales_usd
    -- include other columns as needed
)
VALUES (
    src.marketplace,
    src.channel, 
    src.brand_code, 
    src.brand_name,
    src.brand_category,
    src.sku,
    src.asin,
    src.product_name, 
    src.country_code,
    src.purchase_hour_datetime,
    src.total_quantity,
    src.item_price_lc,
    src.item_price_usd,
    src.shipping_price_lc,
    src.shipping_price_usd,
    src.giftwrap_price_lc,
    src.giftwrap_price_usd,
    src.item_promo_discount_lc,
    src.item_promo_discount_usd,
    src.ship_promo_discount_lc,
    src.ship_promo_discount_usd,
    
    src.cancelled_order_count,
    src.total_order_count,
    src.ordered_product_sales_lc,
    src.ordered_product_sales_usd, 
    src.last_year_total_ordered_product_sales_usd
    -- match the columns defined above
);
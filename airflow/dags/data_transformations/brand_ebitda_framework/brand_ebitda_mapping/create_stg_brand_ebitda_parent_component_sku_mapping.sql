-- This query constructs a full SKU mapping by combining various sources such as sku from fact_all_orders and sku, alias_sku, and netsuite_item_number from netsuite_items
-- It then identifies parent-component relationships for 'kit' items

CREATE OR REPLACE TABLE $stage_db.create_stg_brand_ebitda_parent_component_sku_mapping AS

WITH full_sku_mapping AS (
    -- Combines SKU, alias SKU, and NetSuite item numbers from orders and NetSuite for a full mapping.
    -- Deduplicates to retain the latest updated record for each unique SKU.
    SELECT *
    FROM (
        SELECT
            "brand" AS brand,
            "sku" AS sku,
            'order - sku' AS product_identifier, -- Identifies source (table - column)
            "netsuite_item_number" AS ns_item_number,
            "product_name" AS item_name,
            MAX(TO_TIMESTAMP_NTZ("purchase_date_utc")) AS updated_at
        FROM dwh.prod.fact_all_orders
        WHERE
            "marketplace" = 'SHOPIFY'
            AND "sku" IS NOT NULL
            AND "product_name" NOT ILIKE '%subscri%'
            AND "product_name" NOT ILIKE '%auto renew%'
            AND "product_name" NOT ILIKE '%ships%'
        GROUP BY ALL

        UNION

        SELECT
            brand,
            sku,
            'netsuite - sku' AS product_identifier,
            netsuite_item_number AS ns_item_number,
            description AS item_name,
            TO_TIMESTAMP_NTZ(netsuite_updated_at) AS updated_at
        FROM NETSUITE.NETSUITE.NETSUITE_ITEMS

        UNION

        SELECT
            brand,
            alias_sku AS sku,
            'netsuite - alias_sku' AS product_identifier,
            netsuite_item_number AS ns_item_number,
            description AS item_name,
            TO_TIMESTAMP_NTZ(netsuite_updated_at) AS updated_at
        FROM NETSUITE.NETSUITE.NETSUITE_ITEMS

        UNION

        SELECT
            brand,
            netsuite_item_number AS sku,
            'netsuite - netsuite_item_number' AS product_identifier,
            netsuite_item_number AS ns_item_number,
            description AS item_name,
            TO_TIMESTAMP_NTZ(netsuite_updated_at) AS updated_at
        FROM NETSUITE.NETSUITE.NETSUITE_ITEMS
    )
    QUALIFY ROW_NUMBER() OVER (PARTITION BY sku ORDER BY updated_at DESC) = 1 -- dedupe by SKU and keeps the latest updated entry
),

ns_kit_mapping AS (
    -- Extracts distinct parent-component relationships for 'kit' items from NetSuite.
    SELECT DISTINCT
        parent_item_brand AS brand,
        parent_item_sku AS parent_sku,
        component_item_sku AS component_sku,
        component_item_quantity
    FROM NETSUITE.NETSUITE.NETSUITE_ITEMS_COMPONENTS AS kit
    WHERE LOWER(parent_item_type) = 'kit' -- filter for parent items that are kits
        AND LOWER(component_item_product_type) = 'product' -- filter for components that are not samples
),

parent_component_sku_mapping AS (
    -- Links the full SKU mapping with NetSuite kit component details.
    -- Identifies if a SKU is part of a kit and associates its components.
    SELECT
        sm.brand,
        sm.sku AS parent_sku, -- SKU from full_sku_mapping is considered as the parent_sku
        sm.ns_item_number AS parent_ns_item_number,
        CASE WHEN kit.component_sku IS NULL THEN 'No kit' ELSE 'Kit' END AS product_type, -- identify if the parent_sku is a kit or not
        kit.component_sku,
        kit.component_sku AS component_ns_item_number, -- assume component_sku can also serve as component_ns_item_number
        kit.component_item_quantity
    FROM full_sku_mapping AS sm
    LEFT JOIN ns_kit_mapping AS kit
        ON sm.sku = kit.parent_sku
)

SELECT
    MD5(
        CAST(
            COALESCE(CAST(m.brand AS VARCHAR), '') || '-' ||
            COALESCE(CAST(m.parent_sku AS VARCHAR), '') || '-' ||
            COALESCE(CAST(m.component_sku AS VARCHAR), '') AS VARCHAR
        )
    ) AS pk,
    m.*
FROM parent_component_sku_mapping AS m
;
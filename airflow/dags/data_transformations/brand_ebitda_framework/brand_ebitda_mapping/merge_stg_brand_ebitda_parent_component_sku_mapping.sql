CREATE OR REPLACE TABLE $stage_db.merge_stg_brand_ebitda_parent_component_sku_mapping AS
SELECT
    *,
    '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
    '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
FROM $stage_db.create_stg_brand_ebitda_parent_component_sku_mapping
WHERE 1=0;

-- Merge the incremental data into the target table
MERGE INTO
    $stage_db.merge_stg_brand_ebitda_parent_component_sku_mapping AS tgt
USING
    (
        SELECT
            *
        FROM $stage_db.create_stg_brand_ebitda_parent_component_sku_mapping
        QUALIFY ROW_NUMBER() OVER (PARTITION BY pk ORDER BY pk) = 1 -- Dedupe based on pk
    ) AS src
        ON tgt.pk = src.pk

WHEN MATCHED THEN
    UPDATE SET
        tgt.brand = src.brand,
        tgt.parent_sku = src.parent_sku,
        tgt.parent_ns_item_number = src.parent_ns_item_number,
        tgt.product_type = src.product_type,
        tgt.component_sku = src.component_sku,
        tgt.component_ns_item_number = src.component_ns_item_number,
        tgt.component_item_quantity = src.component_item_quantity,
        tgt.record_updated_timestamp_utc = SYSDATE()

WHEN NOT MATCHED THEN
    INSERT (
        pk
        , brand
        , parent_sku
        , parent_ns_item_number
        , product_type
        , component_sku
        , component_ns_item_number
        , component_item_quantity
        , record_created_timestamp_utc
        , record_updated_timestamp_utc
    ) VALUES (
        src.pk
        , src.brand
        , src.parent_sku
        , src.parent_ns_item_number
        , src.product_type
        , src.component_sku
        , src.component_ns_item_number
        , src.component_item_quantity
        , SYSDATE()
        , SYSDATE()
    );

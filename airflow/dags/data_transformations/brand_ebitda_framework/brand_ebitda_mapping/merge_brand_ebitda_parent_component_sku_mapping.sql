CREATE OR REPLACE TABLE $curated_db.brand_ebitda_parent_component_sku_mapping (
    pk VARCHAR,
    brand VARCHAR,
    parent_sku VARCHAR,
    parent_ns_item_number VARCHAR,
    product_type VARCHAR,
    component_sku VARCHAR,
    component_ns_item_number VARCHAR,
    component_item_quantity INT,
    record_created_timestamp_utc TIMESTAMP DEFAULT SYSDATE(),
    record_updated_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);

BEGIN TRANSACTION;

MERGE INTO $curated_db.brand_ebitda_parent_component_sku_mapping AS t
USING $stage_db.merge_stg_brand_ebitda_parent_component_sku_mapping AS s
ON
    t.pk = s.pk

WHEN MATCHED THEN
    UPDATE SET
        t.brand = s.brand,
        t.parent_sku = s.parent_sku,
        t.parent_ns_item_number = s.parent_ns_item_number,
        t.product_type = s.product_type,
        t.component_sku = s.component_sku,
        t.component_ns_item_number = s.component_ns_item_number,
        t.component_item_quantity = s.component_item_quantity,
        t.record_updated_timestamp_utc = SYSDATE()

WHEN NOT MATCHED THEN
    INSERT (
        pk,
        brand,
        parent_sku,
        parent_ns_item_number,
        product_type,
        component_sku,
        component_ns_item_number,
        component_item_quantity,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        s.pk,
        s.brand,
        s.parent_sku,
        s.parent_ns_item_number,
        s.product_type,
        s.component_sku,
        s.component_ns_item_number,
        s.component_item_quantity,
        SYSDATE(),
        SYSDATE()
    );

-- Deduplicate records: keep only one primary key (the one with the latest updated timestamp)
DELETE FROM $curated_db.brand_ebitda_parent_component_sku_mapping t1
WHERE EXISTS (
    SELECT 1
    FROM $curated_db.brand_ebitda_parent_component_sku_mapping t2
    WHERE t1.pk = t2.pk
    AND t1.record_updated_timestamp_utc < t2.record_updated_timestamp_utc
);

COMMIT;

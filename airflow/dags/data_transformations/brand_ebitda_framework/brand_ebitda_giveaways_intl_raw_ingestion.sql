
copy into DWH_DEV.STAGING.brand_ebitda_giveaways_intl_raw_ingest
FROM (
SELECT
	$1:_airbyte_raw_id::VARCHAR,
	$1:_airbyte_extracted_at::VARCHAR,
	$1:_airbyte_generation_id::VARCHAR,
	$1:_airbyte_meta::VARCHAR,
  $1:"Market Place"::VARC<PERSON>R,
	$1:SKU::VARCHAR,
	$1:Link::VARCHAR,
	$1:"Type"::VARCHAR,
	$1:Week::VARCHAR,
	$1:Brand::VARCHAR,
	$1:"SOURCE"::VARCHAR,
	$1:Week_range::VARCHAR,
	$1:child_ASIN::VARCHAR,
	$1:parent_ASIN::VARCHAR,
	$1:Purchased_giveaways::VARCHAR,
	$1:Total_give_away____::VARCHAR
FROM @DWH_DEV.RAW.GOESSOR_DATALAKE_STAGE_DEV/raw/airbyte/Giveaways_Intl_Conso/
(FILE_FORMAT => 'dwh_dev.raw.PARQUET')
)

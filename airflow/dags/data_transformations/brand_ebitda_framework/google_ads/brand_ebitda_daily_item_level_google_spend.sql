CREATE OR REPLACE TABLE $stage_db.brand_ebitda_daily_item_level_google_ads_spend AS


WITH asin_traced_data AS (
SELECT *, 
 REGEXP_SUBSTR_ALL(campaign_name, 'B0[A-Z0-9]{8}') AS asin,
 REGEXP_SUBSTR(campaign_name, 'US|DE|IT|PL|AU|TR|AE|BE|MX|NL|CA|BR|UK|ES|FR|SE') AS country_code
from DWH.PROD.GOOGLE_ADS_CAMPAIGN_REPORT
) ,

flatten_data AS 
(
    SELECT *, asin1.value::varchar AS child_asin
    FROM asin_traced_data 
    ,LATERAL FLATTEN(input => asin,outer => TRUE) AS asin1
),

asin_level_spend AS (SELECT 
SEGMENT_DATE,
COALESCE(country_code,'US') AS country_code, 
BRAND_CODE, 
child_asin, 
SUM(cost_micros) AS google_ads_spend
FROM flatten_data
GROUP BY 
SEGMENT_DATE, 
country_code, 
BRAND_CODE, 
child_asin
)

SELECT 
asin_level.SEGMENT_DATE, 
asin_level.country_code, 
asin_level.BRAND_CODE, 
asin_level.child_asin, 

SUM(asin_level.google_ads_spend) AS asin_google_ads_spend
FROM asin_level_spend As asin_level
WHERE child_asin IS NOT NULL
GROUP BY 
SEGMENT_DATE, 
country_code,
BRAND_CODE,
child_asin
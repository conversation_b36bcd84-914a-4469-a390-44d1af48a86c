CREATE OR REPLACE TRANSIENT TABLE $stage_db.create_stg_brand_ebitda_daily_item_level_google_ads_spend AS

WITH google_ads_report AS (
    SELECT
        *,
        REGEXP_SUBSTR(campaign_name, 'B0[A-Z0-9]{8}') AS asin,
    FROM
        DWH.PROD.GOOGLE_ADS_CAMPAIGN_REPORT
),

asin_revenue_share AS (
    SELECT
        brand_code,
        asin,
        report_date,
        asin_revenue,
        brand_revenue,
        DIV0NULL(asin_revenue, brand_revenue) AS perc_share
    FROM
        $curated_db.brand_ebitda_daily_asin_revenue_share
    QUALIFY
        ROW_NUMBER() OVER (PARTITION BY brand_code, asin, report_date ORDER BY report_date DESC) = 1
),

ad_spend_without_asin AS (
    SELECT
        ga.segment_date,
        ga.brand_code,
        ar.asin,
        ar.perc_share,
        SUM(ga.clicks) * ar.perc_share AS total_clicks,
        SUM(ga.conversions) * ar.perc_share AS total_conversions,
        SUM(ga.impressions) * ar.perc_share AS total_impressions,
        SUM(ga.cost_micros) * ar.perc_share AS total_cost
    FROM
        google_ads_report AS ga
    LEFT JOIN
        asin_revenue_share AS ar
            ON ga.brand_code = ar.brand_code
            AND ga.segment_date = ar.report_date
    WHERE
        ga.asin IS NULL
    GROUP BY ALL
),

ad_spend_with_asin AS (
    SELECT
        segment_date,
        brand_code,
        asin,
        SUM(clicks) AS total_clicks,
        SUM(conversions) AS total_conversions,
        SUM(impressions) AS total_impressions,
        SUM(cost_micros) AS total_cost
    FROM
        google_ads_report
    WHERE
        asin IS NOT NULL
    GROUP BY ALL
),

combined_google_ads AS (
    SELECT
        brand_code,
        asin,
        segment_date,
        total_clicks,
        total_conversions,
        total_impressions,
        total_cost
    FROM
        ad_spend_without_asin

    UNION ALL

    SELECT
        brand_code,
        asin,
        segment_date,
        total_clicks,
        total_conversions,
        total_impressions,
        total_cost
    FROM
        ad_spend_with_asin
),

aggregated AS (
    SELECT
        ga.brand_code,
        ga.asin,
        ga.segment_date AS report_date,
        mktg_split.channel,
        mktg_split.perc_share,
        SUM(COALESCE(ga.total_clicks, 0)) AS total_clicks,
        SUM(COALESCE(ga.total_conversions, 0)) AS total_conversions,
        SUM(COALESCE(ga.total_impressions, 0)) AS total_impressions,
        SUM(COALESCE(ga.total_cost, 0)) * mktg_split.perc_share AS total_spend -- channel share of google ad spend
    FROM
        combined_google_ads AS ga
    LEFT JOIN
        $stage_db.merge_brand_ebitda_marketing_split AS mktg_split
            ON ga.brand_code = mktg_split.brand_code
            AND DATE_TRUNC("month", ga.segment_date) = mktg_split.month_start_date
    WHERE
        LOWER(REPLACE(mktg_split.marketing_activity, ' ', '')) = 'paidsearchgoogle'
        AND ga.segment_date >= '2025-01-01'
    GROUP BY ALL

    UNION ALL
        -- 2024 AMZ US
        SELECT
        ga.brand_code,
        ga.asin,
        ga.segment_date AS report_date,
        'AMZ-US' AS channel,
        CASE WHEN ga.brand_code != 'ZSK' THEN 0.8 ELSE 0.5 END AS perc_share,
        SUM(COALESCE(ga.total_clicks, 0)) AS total_clicks,
        SUM(COALESCE(ga.total_conversions, 0)) AS total_conversions,
        SUM(COALESCE(ga.total_impressions, 0)) AS total_impressions,
        SUM(COALESCE(ga.total_cost, 0)) * perc_share AS total_spend -- channel share of google ad spend
    FROM
        combined_google_ads AS ga
    WHERE
        ga.segment_date < '2025-01-01'
    GROUP BY ALL

    UNION ALL
        -- 2024 DTC
        SELECT
        ga.brand_code,
        ga.asin,
        ga.segment_date AS report_date,
        'DTC' AS channel,
        CASE WHEN ga.brand_code != 'ZSK' THEN 0.2 ELSE 0.5 END AS perc_share,
        SUM(COALESCE(ga.total_clicks, 0)) AS total_clicks,
        SUM(COALESCE(ga.total_conversions, 0)) AS total_conversions,
        SUM(COALESCE(ga.total_impressions, 0)) AS total_impressions,
        SUM(COALESCE(ga.total_cost, 0)) * perc_share AS total_spend -- channel share of google ad spend
    FROM
        combined_google_ads AS ga
    WHERE
        ga.segment_date < '2025-01-01'
    GROUP BY ALL
),

-- monthly misfit agency cost
misfit_agency_cost AS (
    SELECT
        misfit.month_start_date,
        asin_revenue.report_date AS report_date,
        misfit.geo AS country_code,
        misfit.brand,
        asin_revenue.asin,
        DIV0(misfit.amount_usd, EXTRACT(DAY FROM LAST_DAY(misfit.month_start_date, 'month'))) AS misfit_agency_cost_usd_brand,
        COALESCE(misfit_agency_cost_usd_brand * asin_revenue.asin_brand_revenue_share, 0) AS misfit_agency_cost_usd_asin
    FROM
        $stage_db.merge_brand_ebitda_misfit_agency_cost AS misfit
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON misfit.month_start_date = DATE_TRUNC("month", asin_revenue.report_date)
            AND misfit.brand = asin_revenue.brand_code
            AND misfit.geo = asin_revenue.country_code
    WHERE misfit.vendor = 'Google'
),

amz_us AS (
    SELECT
        a.brand_code,
        a.asin,
        'US' AS country_code,
        a.report_date,
        a.channel,
        a.perc_share,
        a.total_clicks,
        a.total_conversions,
        a.total_impressions,
        COALESCE(a.total_spend, 0) AS google_ad_spend,
        CASE WHEN m.country_code = 'US' THEN m.misfit_agency_cost_usd_asin ELSE 0 END AS misfit_agency_spend,
        google_ad_spend + misfit_agency_spend AS total_google_ads_spend
    FROM
        aggregated AS a
    LEFT JOIN
        misfit_agency_cost AS m
            ON a.report_date = m.report_date
            AND a.asin = m.asin
    WHERE
        LOWER(a.channel) = 'amz-us'
),

amz_int AS (
    SELECT
        a.brand_code,
        a.asin,
        'CA' AS country_code,
        a.report_date,
        a.channel,
        a.perc_share,
        a.total_clicks,
        a.total_conversions,
        a.total_impressions,
        COALESCE(a.total_spend, 0) AS google_ad_spend,
        CASE WHEN m.country_code = 'CA' THEN m.misfit_agency_cost_usd_asin ELSE 0 END AS misfit_agency_spend,
        google_ad_spend + misfit_agency_spend AS total_google_ads_spend
    FROM
        aggregated AS a
    LEFT JOIN
        misfit_agency_cost AS m
            ON a.report_date = m.report_date
            AND a.asin = m.asin
    WHERE
        LOWER(a.channel) = 'amz-int'
),

dtc AS (
    SELECT
        brand_code,
        asin,
        'N/A' AS country_code,
        report_date,
        channel,
        perc_share,
        total_clicks,
        total_conversions,
        total_impressions,
        COALESCE(total_spend, 0) AS google_ad_spend,
        0 AS misfit_agency_spend, -- misfit is currently allocated to Amazon only
        google_ad_spend + misfit_agency_spend AS total_google_ads_spend
    FROM
        aggregated
    WHERE
        LOWER(channel) = 'dtc'
),

all_channels_consolidated AS (
    SELECT
        *
    FROM
        amz_us

    UNION ALL

    SELECT
        *
    FROM
        amz_int

    UNION ALL

    SELECT
        *
    FROM
        dtc
)

SELECT
    MD5(
        CAST(
            COALESCE(CAST(channel AS VARCHAR), '') || '-' ||
            COALESCE(CAST(country_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(brand_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(asin AS VARCHAR), '') || '-' ||
            COALESCE(CAST(report_date AS VARCHAR), '') AS VARCHAR
        )
    ) AS pk
    , *
FROM all_channels_consolidated
;

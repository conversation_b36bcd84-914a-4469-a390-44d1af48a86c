-- Create prod table
CREATE OR REPLACE TABLE $curated_db.brand_ebitda_daily_item_level_google_ads_spend (
    pk VARCHAR,
    brand_code VARCHAR,
    asin VARCHAR,
    country_code VARCHAR,
    report_date DATE,
    channel VARCHAR,
    perc_share FLOAT,
    total_clicks FLOAT,
    total_conversions FLOAT,
    total_impressions FLOAT,
    google_ad_spend FLOAT,
    misfit_agency_spend FLOAT,
    total_google_ads_spend FLOAT,
    record_created_timestamp_utc TIMESTAMP DEFAULT SYSDATE(),
    record_updated_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);

-- Merge statement
BEGIN TRANSACTION;

MERGE INTO $curated_db.brand_ebitda_daily_item_level_google_ads_spend AS t
USING $stage_db.merge_stg_brand_ebitda_daily_item_level_google_ads_spend AS s
ON
    t.pk = s.pk

WHEN MATCHED THEN
    UPDATE SET
        t.perc_share = s.perc_share,
        t.total_clicks = s.total_clicks,
        t.total_conversions = s.total_conversions,
        t.total_impressions = s.total_impressions,
        t.google_ad_spend = s.google_ad_spend,
        t.misfit_agency_spend = s.misfit_agency_spend,
        t.total_google_ads_spend = s.total_google_ads_spend,
        t.record_updated_timestamp_utc = SYSDATE()

WHEN NOT MATCHED THEN
    INSERT (
        pk,
        brand_code,
        asin,
        country_code,
        report_date,
        channel,
        perc_share,
        total_clicks,
        total_conversions,
        total_impressions,
        google_ad_spend,
        misfit_agency_spend,
        total_google_ads_spend,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        s.pk,
        s.brand_code,
        s.asin,
        s.country_code,
        s.report_date,
        s.channel,
        s.perc_share,
        s.total_clicks,
        s.total_conversions,
        s.total_impressions,
        s.google_ad_spend,
        s.misfit_agency_spend,
        s.total_google_ads_spend,
        SYSDATE(),
        SYSDATE()
    );

DELETE FROM $curated_db.brand_ebitda_daily_item_level_google_ads_spend
WHERE (pk, record_updated_timestamp_utc) NOT IN (
    SELECT pk, MAX(record_updated_timestamp_utc)
    FROM $curated_db.brand_ebitda_daily_item_level_google_ads_spend
    GROUP BY pk
);

COMMIT;
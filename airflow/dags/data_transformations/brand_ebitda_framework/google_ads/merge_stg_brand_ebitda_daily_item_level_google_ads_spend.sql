-- Create staging table
CREATE OR REPLACE TABLE $stage_db.merge_stg_brand_ebitda_daily_item_level_google_ads_spend AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.create_stg_brand_ebitda_daily_item_level_google_ads_spend
    WHERE 1 = 0;

-- Merge statement
MERGE INTO
    $stage_db.merge_stg_brand_ebitda_daily_item_level_google_ads_spend AS tgt
USING
    (
        SELECT
            *
        FROM $stage_db.create_stg_brand_ebitda_daily_item_level_google_ads_spend
        QUALIFY ROW_NUMBER() OVER (PARTITION BY pk ORDER BY report_date DESC) = 1 -- dedupe
    ) AS src
        ON tgt.pk = src.pk

WHEN MATCHED THEN
    -- Update the record if it already exists
    UPDATE SET
        tgt.perc_share = src.perc_share,
        tgt.total_clicks = src.total_clicks,
        tgt.total_conversions = src.total_conversions,
        tgt.total_impressions = src.total_impressions,
        tgt.google_ad_spend = src.google_ad_spend,
        tgt.misfit_agency_spend = src.misfit_agency_spend,
        tgt.total_google_ads_spend = src.total_google_ads_spend,
        tgt.record_updated_timestamp_utc = SYSDATE()  -- Update the timestamp

WHEN NOT MATCHED THEN
    -- Insert a new record if it does not exist
    INSERT (
        pk,
        brand_code,
        asin,
        country_code,
        report_date,
        channel,
        perc_share,
        total_clicks,
        total_conversions,
        total_impressions,
        google_ad_spend,
        misfit_agency_spend,
        total_google_ads_spend,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        src.pk,
        src.brand_code,
        src.asin,
        src.country_code,
        src.report_date,
        src.channel,
        src.perc_share,
        src.total_clicks,
        src.total_conversions,
        src.total_impressions,
        src.google_ad_spend,
        src.misfit_agency_spend,
        src.total_google_ads_spend,
        SYSDATE(),
        SYSDATE()
    );

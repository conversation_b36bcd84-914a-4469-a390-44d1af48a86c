CREATE OR REPLACE TABLE $stage_db.brand_ebitda_daily_item_level_amz_ppc_spend AS

WITH asin_level_spends AS (
select 
report_date, 
country_code, 
asin, 
SUM(CASE WHEN campaign_type = 'Sponsored Products' THEN spend_usd ELSE NULL END) AS sp_ppc_spend, 
SUM(CASE WHEN campaign_type IN ('Sponsored Brands', 'Sponsored Brand') THEN spend_usd ELSE NULL END)AS sv_ppc_spend, 
SUM(CASE WHEN campaign_type IN ('Sponsored Brand Video', 'Sponsored Brands Video') THEN spend_usd ELSE NULL END) AS sbv_ppc_spend, 
SUM(CASE WHEN campaign_type = 'Sponsored Display' THEN spend_usd ELSE NULL END) AS sd_ppc_spend, 
SUM(spend_usd) AS total_ppc_spend, 

SUM(CASE WHEN campaign_type = 'Sponsored Products' THEN sales_14d_usd ELSE 0 END) AS sp_ppc_sales, 
SUM(CASE WHEN campaign_type ILIKE '%sponsored brand%' THEN sales_14d_usd ELSE 0 END) AS sb_ppc_sales, 
SUM(CASE WHEN campaign_type IN ('Sponsored Brand Video', 'Sponsored Brands Video') THEN sales_14d_usd ELSE 0 END) AS sbv_ppc_sales, 
SUM(CASE WHEN campaign_type = 'Sponsored Display' THEN sales_14d_usd ELSE 0 END) AS sd_ppc_sales, 
SUM(sales_14d_usd) AS total_ppc_sales,
from DWH.PROD.fact_amazon_ad_asins
GROUP BY 1,2,3
)

SELECT 
report_date, 
country_code, 
asin,
SUM(sp_ppc_spend) AS sp_ppc_spend, 
SUM(sv_ppc_spend) AS sv_ppc_spend, 
SUM(sbv_ppc_spend) AS sbv_ppc_spend, 
SUM(sd_ppc_spend) AS sd_ppc_spend, 
SUM(total_ppc_spend) AS total_ppc_spend, 

SUM(sp_ppc_sales) AS sp_ppc_sales, 
SUM(sb_ppc_sales) AS sb_ppc_sales,
SUM(sbv_ppc_sales) AS sbv_ppc_sales, 
SUM(sd_ppc_sales) AS sd_ppc_sales, 
SUM(total_ppc_sales) AS total_ppc_sales

FROM asin_level_spends AS asin_level 
GROUP BY 1,2,3


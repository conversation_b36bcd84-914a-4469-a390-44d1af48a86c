CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_brand_ebitda_marketing_split AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(brand AS VARCHAR), ''), '-',
            COALESCE(CAST(brand_code AS VARCHAR), ''), '-',
            COALESCE(CAST(line AS VARCHAR), ''), '-',
            COALESCE(CAST(marketing_activity AS VARCHAR), ''), '-',
            COALESCE(CAST(channel AS VARCHAR), ''), '-',
            COALESCE(CAST(month_start_date AS VARCHAR), '')
            )) AS pk,
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        line,
        brand,
        channel,
        brand_code,
        perc_share,
        marketing_budget,
        month_start_date,
        marketing_activity,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_brand_ebitda_marketing_split
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY _airbyte_extracted_at DESC NULLS LAST) = 1
);
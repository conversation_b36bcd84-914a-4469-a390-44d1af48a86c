CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_brand_ebitda_marketing_split AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_brand_ebitda_marketing_split
    WHERE 1 = 0;

INSERT INTO $raw_db.log_brand_ebitda_marketing_split (
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    line,
    brand,
    channel,
    brand_code,
    perc_share,
    marketing_budget,
    month_start_date,
    marketing_activity,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        line,
        brand,
        channel,
        brand_code,
        perc_share,
        marketing_budget,
        month_start_date,
        marketing_activity,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_brand_ebitda_marketing_split;
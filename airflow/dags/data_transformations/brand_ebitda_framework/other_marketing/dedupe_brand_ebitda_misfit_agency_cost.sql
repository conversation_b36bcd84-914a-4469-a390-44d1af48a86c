CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_brand_ebitda_misfit_agency_cost AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(month_start_date AS VARCHAR), ''), '-',
            COALESCE(CAST(brand AS VARCHAR), ''), '-',
            COALESCE(CAST(vendor AS VARCHAR), ''), '-',
            COALESCE(CAST(geo AS VARCHAR), '')
            )) AS pk,
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        geo,
        brand,
        vendor,
        amount_usd,
        month_start_date,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_brand_ebitda_misfit_agency_cost
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY _airbyte_extracted_at DESC NULLS LAST) = 1
);
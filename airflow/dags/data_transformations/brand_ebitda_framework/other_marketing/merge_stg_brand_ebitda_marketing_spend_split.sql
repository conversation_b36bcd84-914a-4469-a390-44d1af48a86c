CREATE TABLE IF NOT EXISTS $stage_db.merge_monthly_marketing_spend_split_by_channel AS
    SELECT
        *
        , '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc
        , '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_monthly_marketing_spend_split_by_channel
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_monthly_marketing_spend_split_by_channel AS tgt
USING
    $stage_db.dedupe_monthly_marketing_spend_split_by_channel AS src
    ON src.pk = tgt.pk

WHEN MATCHED THEN
    UPDATE SET
        tgt._airbyte_raw_id = src._airbyte_raw_id
        , tgt._airbyte_extracted_at = src._airbyte_extracted_at
        , tgt._airbyte_generation_id = src._airbyte_generation_id
        , tgt._airbyte_meta = src._airbyte_meta
        , tgt.perc_share = src.perc_share
        , tgt.marketing_budget = src.marketing_budget
        , tgt.file_name = src.file_name
        , tgt.etl_batch_run_time = src.etl_batch_run_time
        , tgt.record_updated_timestamp_utc = SYSDATE()

WHEN NOT MATCHED THEN
    INSERT (
        pk
        , _airbyte_raw_id
        , _airbyte_extracted_at
        , _airbyte_generation_id
        , _airbyte_meta
        , brand
        , brand_code
        , marketing_line
        , marketing_activity
        , channel
        , month_start_date
        , perc_share
        , marketing_budget
        , file_name
        , etl_batch_run_time
        , record_created_timestamp_utc
        , record_updated_timestamp_utc
    )
    VALUES
    (
        src.pk
        , src._airbyte_raw_id
        , src._airbyte_extracted_at
        , src._airbyte_generation_id
        , src._airbyte_meta
        , src.brand
        , src.brand_code
        , src.marketing_line
        , src.marketing_activity
        , src.channel
        , src.month_start_date
        , src.perc_share
        , src.marketing_budget
        , src.file_name
        , src.etl_batch_run_time
        , SYSDATE()
        , SYSDATE()
    );

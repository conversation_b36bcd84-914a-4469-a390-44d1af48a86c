CREATE TABLE IF NOT EXISTS $stage_db.merge_brand_ebitda_marketing_split AS
    SELECT

        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_brand_ebitda_marketing_split
    WHERE 1 = 0;

BEGIN TRANSACTION;

MERGE INTO
    $stage_db.merge_brand_ebitda_marketing_split AS tgt
USING
    $stage_db.dedupe_brand_ebitda_marketing_split AS src

        ON 1 = 1
       AND src.pk = tgt.pk

WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._airbyte_raw_id = src._airbyte_raw_id,
    tgt._airbyte_extracted_at = src._airbyte_extracted_at,
    tgt._airbyte_generation_id = src._airbyte_generation_id,
    tgt._airbyte_meta = src._airbyte_meta,
    tgt.line = src.line,
    tgt.brand = src.brand,
    tgt.channel = src.channel,
    tgt.brand_code = src.brand_code,
    tgt.perc_share = src.perc_share,
    tgt.marketing_budget = src.marketing_budget,
    tgt.month_start_date = src.month_start_date,
    tgt.marketing_activity = src.marketing_activity,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    line,
    brand,
    channel,
    brand_code,
    perc_share,
    marketing_budget,
    month_start_date,
    marketing_activity,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._airbyte_raw_id,
    src._airbyte_extracted_at,
    src._airbyte_generation_id,
    src._airbyte_meta,
    src.line,
    src.brand,
    src.channel,
    src.brand_code,
    src.perc_share,
    src.marketing_budget,
    src.month_start_date,
    src.marketing_activity,
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);

-- delete all entries that are not from the latest airbyte update
DELETE FROM $stage_db.merge_brand_ebitda_marketing_split
WHERE (record_updated_timestamp_utc) NOT IN (
    SELECT MAX(record_updated_timestamp_utc)
    FROM $stage_db.merge_brand_ebitda_marketing_split
);

COMMIT;


CREATE OR REPLACE TABLE
    $stage_db.stg_brandebitda_revenue_sku_level_wbr_totals AS

SELECT
    -- keys
    report_date,
    seller_id,
    country_code,
    sku,

    quantity,

    -- wbr final columns
      subtotal_revenue_sales
    + subtotal_revenue_other
    + subtotal_revenue_promo                         AS total_net_revenue,

      subtotal_fba_fees
    + subtotal_fba_reimbursement                     AS total_fulfillment_fees,

    subtotal_sellingfees                             AS total_selling_fees,

      subtotal_cogs_amzpartneredcarrier
    + subtotal_cogs_placement                        AS total_cogs,

    -- qa / alternate buckets
    subtotal_dealfees               AS alternate_deal_fees_qa,
    subtotal_storage                AS alternate_storage_fees_qa,
    subtotal_storage_longterm       AS alternate_long_term_storage_qa,
    subtotal_ppc                    AS alternate_ppc_cost_qa,
    subtotal_ppc_vine               AS alternate_ppc_vine_qa

FROM $stage_db.stg_brandebitda_revenue_sku_level_complete_aggregation_wbr_subtotals;

CREATE OR REPLACE TABLE $stage_db.stg_brandebitda_revenue_non_order_sku_specified_bi_mapping AS

WITH src AS (
    SELECT
        report_date,
        seller_id,
        country_code,
        revised_marketplace   AS marketplace,
        sku,
        bi_mapping,
        amount
    FROM $stage_db.stg_brandebitda_revenue_drr_non_order_level_aggregate_bi_map
    WHERE sku IS NOT NULL 
)
SELECT
    report_date,
    seller_id,
    country_code,
  --  marketplace,
    sku,

    SUM(CASE WHEN bi_mapping = 'storage_storage_other'            THEN amount ELSE 0 END) AS storage_storage_other,
    SUM(CASE WHEN bi_mapping = 'storage_other_service'            THEN amount ELSE 0 END) AS storage_other_service,
    SUM(CASE WHEN bi_mapping = 'storage_longterm_other'           THEN amount ELSE 0 END) AS storage_longterm_other,
    SUM(CASE WHEN bi_mapping = 'revenue_shippingcredits_refund'   THEN amount ELSE 0 END) AS revenue_shippingcredits_refund,
    SUM(CASE WHEN bi_mapping = 'revenue_sales_refund'             THEN amount ELSE 0 END) AS revenue_sales_refund,
    SUM(CASE WHEN bi_mapping = 'revenue_sales_liquidation'        THEN amount ELSE 0 END) AS revenue_sales_liquidation,
    SUM(CASE WHEN bi_mapping = 'revenue_sales_chargeback'         THEN amount ELSE 0 END) AS revenue_sales_chargeback,
    SUM(CASE WHEN bi_mapping = 'revenue_promo_refund'             THEN amount ELSE 0 END) AS revenue_promo_refund,
    SUM(CASE WHEN bi_mapping = 'revenue_other_refund'             THEN amount ELSE 0 END) AS revenue_other_refund,
    SUM(CASE WHEN bi_mapping = 'revenue_other_liquidation'        THEN amount ELSE 0 END) AS revenue_other_liquidation,
    SUM(CASE WHEN bi_mapping = 'revenue_giftwrapcredits_refund'   THEN amount ELSE 0 END) AS revenue_giftwrapcredits_refund,
    SUM(CASE WHEN bi_mapping = 'revenue_adjustment_liquidation'   THEN amount ELSE 0 END) AS revenue_adjustment_liquidation,
    SUM(CASE WHEN bi_mapping = 'not_reported_subscription'        THEN amount ELSE 0 END) AS not_reported_subscription, 
    SUM(CASE WHEN bi_mapping = 'not_reported_mbs'                 THEN amount ELSE 0 END) AS not_reported_mbs, 
    SUM(CASE WHEN bi_mapping = 'not_reported_immaterial'          THEN amount ELSE 0 END) AS not_reported_immaterial,
    SUM(CASE WHEN bi_mapping = 'fba_reimbursement_adjustment'     THEN amount ELSE 0 END) AS fba_reimbursement_adjustment,
    SUM(CASE WHEN bi_mapping = 'fba_other_service'                THEN amount ELSE 0 END) AS fba_other_service,
    SUM(CASE WHEN bi_mapping = 'fba_other_return'                 THEN amount ELSE 0 END) AS fba_other_return,
    SUM(CASE WHEN bi_mapping = 'fba_other_other'                  THEN amount ELSE 0 END) AS fba_other_other,
    SUM(CASE WHEN bi_mapping = 'fba_other_liquidation'            THEN amount ELSE 0 END) AS fba_other_liquidation,
    SUM(CASE WHEN bi_mapping = 'fba_other_adjustment'             THEN amount ELSE 0 END) AS fba_other_adjustment,
    SUM(CASE WHEN bi_mapping = 'fba_fees_refund'                  THEN amount ELSE 0 END) AS fba_fees_refund,
    SUM(CASE WHEN bi_mapping = 'commission_fees_refund'           THEN amount ELSE 0 END) AS commission_fees_refund,
    SUM(CASE WHEN bi_mapping = 'commission_fees_chargeback'       THEN amount ELSE 0 END) AS commission_fees_chargeback,
    SUM(CASE WHEN bi_mapping = 'cogs_placement_service'           THEN amount ELSE 0 END) AS cogs_placement_service,
    SUM(CASE WHEN bi_mapping = 'cogs_amzparteneredcarrier_other'  THEN amount ELSE 0 END) AS cogs_amzparteneredcarrier_other,
    SUM(CASE WHEN bi_mapping = 'advertising_vine_service'         THEN amount ELSE 0 END) AS advertising_vine_service,
    SUM(CASE WHEN bi_mapping = 'advertising_vine_other'           THEN amount ELSE 0 END) AS advertising_vine_other,
    SUM(CASE WHEN bi_mapping = 'advertising_ppc_service'          THEN amount ELSE 0 END) AS advertising_ppc_service,
    SUM(CASE WHEN bi_mapping = 'advertising_dealfees_service'     THEN amount ELSE 0 END) AS advertising_dealfees_service,
    SUM(CASE WHEN bi_mapping = 'advertising_dealfees_other'       THEN amount ELSE 0 END) AS advertising_dealfees_other

FROM src
GROUP BY
    report_date,
    seller_id,
    country_code,
   -- marketplace,
    sku

;

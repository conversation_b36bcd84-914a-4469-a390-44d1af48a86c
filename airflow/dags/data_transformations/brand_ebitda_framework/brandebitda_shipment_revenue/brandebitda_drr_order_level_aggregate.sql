CREATE OR <PERSON><PERSON><PERSON>CE TABLE $stage_db.stg_brandebitda_revenue_drr_order_level_aggregate AS

    SELECT
        SELLER_ID,
        COUNTRY_CODE,
        TYPE,
        ORDER_ID,
        SKU,
        MARKETPLACE,
        ACCOUNT_TYPE,
        SUM(QUANTITY) AS quantity,
        SUM(PRODUCT_SALES) AS product_sales,
        SUM(SHIPPING_CREDITS) AS shipping_credits,
        SUM(GIFT_WRAP_CREDITS) AS giftwrap_credits,
        SUM(PROMOTIONAL_REBATES) AS promo_rebates,
        SUM(SELLING_FEES) AS selling_fees,
        SUM(FBA_FEES) AS fba_fees,
        SUM(OTHER_TRANSACTION_FEES) AS other_transac,
        SUM(OTHER) AS other_fees,
        'drr' AS source,
        COUNT(*) AS cnt
    FROM dwh.PROD.FACT_AMAZON_SETTLEMENT_TRANSACTIONS_SCS
    WHERE type = 'Order'
    GROUP BY 
    SELLER_ID,
    COUNTRY_CODE, 
    TYP<PERSON>,
    ORDER_ID, 
    SKU, 
    MAR<PERSON><PERSON>LACE, 
    ACCOUNT_TYPE
CREATE OR REPLACE TABLE
    $stage_db.stg_brandebitda_revenue_sku_level_consolidated_daily AS

/* ───────── ① ORDER-side (DRR + Deferred, already prorated) ─────────── */
WITH order_src AS (
    SELECT
        shipment_date                    AS report_date,
        seller_id_shipment               AS seller_id,
        country_code,
        sku,

        /* order metrics */
        SUM(quantity)                                    AS quantity_order,
        SUM(pro_rated_source_product_sales)              AS product_sales_order,
        SUM(pro_rated_source_shipping_credits)           AS shipping_credits_order,
        SUM(pro_rated_source_giftwrap_credits)           AS giftwrap_credits_order,
        SUM(pro_rated_source_promo_rebates)              AS promo_rebates_order,
        SUM(pro_rated_source_selling_fees)               AS selling_fees_order,
        SUM(pro_rated_source_fba_fees)                   AS fba_fees_order,
        SUM(pro_rated_source_other_transac)              AS other_transaction_fees_order,
        SUM(pro_rated_source_other_fees)                 AS other_fees_order,
        SUM(pro_rated_total_sales)                       AS total_sales_order
    FROM   $stage_db.stg_brandebitda_revenue_drr_deffered_shipment_consolidated
    GROUP  BY
        shipment_date, seller_id_shipment,
        country_code, sku
)

/* ───────── ② NON-ORDER side, collapsed to the same buckets ──────────── */
, non_order_src AS (
    SELECT
        report_date,
        seller_id,
        country_code,
        sku,

        /* quantity doesn’t exist on non-order rows */
        0                                              AS quantity_non_order,

        /* revenue / cost buckets */
        /* PRODUCT-sales-like adjustments */
        (  revenue_sales_refund
         + revenue_sales_liquidation
         + revenue_sales_chargeback
         + revenue_other_refund
         + revenue_other_liquidation
        )                                             AS product_sales_non_order,

        revenue_shippingcredits_refund                AS shipping_credits_non_order,
        revenue_giftwrapcredits_refund                AS giftwrap_credits_non_order,
        revenue_promo_refund                          AS promo_rebates_non_order,

        ( commission_fees_refund
         + commission_fees_chargeback
        )                                             AS selling_fees_non_order,

        (  fba_fees_refund
         + fba_reimbursement_adjustment
         + fba_other_service
         + fba_other_return
         + fba_other_other
         + fba_other_liquidation
         + fba_other_adjustment
        )                                             AS fba_fees_non_order,

        revenue_adjustment_liquidation                AS other_transaction_fees_non_order,

        (  storage_storage_other
         + storage_other_service
         + storage_longterm_other
         + cogs_placement_service
         + cogs_amzparteneredcarrier_other
        -- + "below BE (software)"
        -- + "below BE (MBS)"
         + advertising_vine_service
         + advertising_vine_other
         + advertising_ppc_service
         + advertising_dealfees_service
         + advertising_dealfees_other
        -- + immaterial
        )                                             AS other_fees_non_order,

        /* total sales definition for non-order rows         */
        (  product_sales_non_order
         + shipping_credits_non_order
         + giftwrap_credits_non_order
        )                                             AS total_sales_non_order
    FROM $stage_db.stg_brandebitda_revenue_non_order_sku_consolidated_bi_mapping
)

/* ───────── ②‑bis  NON‑ORDER metrics aggregated at SKU level ───────── */
, non_order_sku_month_agg AS (
    SELECT
        report_date,    
        seller_id,
        country_code,
        sku,

        /* ─ Quantities & revenue buckets ─ */
        SUM(quantity_non_order)                   AS quantity_non_order,

        SUM(product_sales_non_order)              AS product_sales_non_order,
        SUM(shipping_credits_non_order)           AS shipping_credits_non_order,
        SUM(giftwrap_credits_non_order)           AS giftwrap_credits_non_order,
        SUM(promo_rebates_non_order)              AS promo_rebates_non_order,

        /* ─ Fee buckets ─ */
        SUM(selling_fees_non_order)               AS selling_fees_non_order,
        SUM(fba_fees_non_order)                   AS fba_fees_non_order,
        SUM(other_transaction_fees_non_order)     AS other_transaction_fees_non_order,
        SUM(other_fees_non_order)                 AS other_fees_non_order,

        /* ─ Sales total ─ */
        SUM(total_sales_non_order)                AS total_sales_non_order
    FROM  non_order_src
    GROUP BY
        report_date,
        seller_id, 
        country_code,
        sku
)


/* ───────── ③ outer-join & final projection ──────────────────────────── */
, joined AS (
    SELECT
        COALESCE(o.report_date,  n.report_date)        AS report_date,
        COALESCE(o.seller_id,    n.seller_id)          AS seller_id,
        COALESCE(o.country_code, n.country_code)       AS country_code,
        COALESCE(o.sku,          n.sku)                AS sku,

        /* bring through every order & non-order field (NULL→0) */
        COALESCE(o.quantity_order,0)                                  AS quantity_order,
        COALESCE(n.quantity_non_order,0)                              AS quantity_non_order,

        COALESCE(o.product_sales_order,0)                             AS product_sales_order,
        COALESCE(n.product_sales_non_order,0)                         AS product_sales_non_order,

        COALESCE(o.shipping_credits_order,0)                          AS shipping_credits_order,
        COALESCE(n.shipping_credits_non_order,0)                      AS shipping_credits_non_order,

        COALESCE(o.giftwrap_credits_order,0)                          AS giftwrap_credits_order,
        COALESCE(n.giftwrap_credits_non_order,0)                      AS giftwrap_credits_non_order,

        COALESCE(o.promo_rebates_order,0)                             AS promo_rebates_order,
        COALESCE(n.promo_rebates_non_order,0)                         AS promo_rebates_non_order,

        COALESCE(o.selling_fees_order,0)                              AS selling_fees_order,
        COALESCE(n.selling_fees_non_order,0)                          AS selling_fees_non_order,

        COALESCE(o.fba_fees_order,0)                                  AS fba_fees_order,
        COALESCE(n.fba_fees_non_order,0)                              AS fba_fees_non_order,

        COALESCE(o.other_transaction_fees_order,0)                    AS other_transaction_fees_order,
        COALESCE(n.other_transaction_fees_non_order,0)                AS other_transaction_fees_non_order,

        COALESCE(o.other_fees_order,0)                                AS other_fees_order,
        COALESCE(n.other_fees_non_order,0)                            AS other_fees_non_order,

        COALESCE(o.total_sales_order,0)                               AS total_sales_order,
        COALESCE(n.total_sales_non_order,0)                           AS total_sales_non_order
    FROM order_src o
    FULL OUTER JOIN non_order_sku_month_agg n
      ON  o.report_date   = n.report_date
      AND o.seller_id     = n.seller_id
      AND o.country_code  = n.country_code
      AND o.sku           = n.sku
)

/* ───────── ④ final SELECT with combined columns ─────────────────────── */
SELECT
    report_date,
    seller_id,
    country_code,
    sku,

    /* ————————— order vs non-order ————————— */
    quantity_order,
    quantity_non_order,

    product_sales_order,
    product_sales_non_order,

    shipping_credits_order,
    shipping_credits_non_order,

    giftwrap_credits_order,
    giftwrap_credits_non_order,

    promo_rebates_order,
    promo_rebates_non_order,

    selling_fees_order,
    selling_fees_non_order,

    fba_fees_order,
    fba_fees_non_order,

    other_transaction_fees_order,
    other_transaction_fees_non_order,

    other_fees_order,
    other_fees_non_order,

    total_sales_order,
    total_sales_non_order,

    /* ———————— consolidated (order + non-order) ———————— */
    quantity_order                                          AS quantity,          -- non-order has no quantity
    product_sales_order           + product_sales_non_order           AS product_sales_total,
    shipping_credits_order        + shipping_credits_non_order        AS shipping_credits_total,
    giftwrap_credits_order        + giftwrap_credits_non_order        AS giftwrap_credits_total,
    promo_rebates_order           + promo_rebates_non_order           AS promo_rebates_total,
    selling_fees_order            + selling_fees_non_order            AS selling_fees_total,
    fba_fees_order                + fba_fees_non_order                AS fba_fees_total,
    other_transaction_fees_order  + other_transaction_fees_non_order  AS other_transaction_fees_total,
    other_fees_order              + other_fees_non_order              AS other_fees_total,
    total_sales_order             + total_sales_non_order             AS total_sales_total

FROM joined;

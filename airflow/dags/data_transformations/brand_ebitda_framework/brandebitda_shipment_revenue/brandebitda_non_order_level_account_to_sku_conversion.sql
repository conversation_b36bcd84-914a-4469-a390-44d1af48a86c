

CREATE OR REPLACE TABLE
    $stage_db.stg_brandebitda_non_order_leve_account_to_sku_conversion AS

WITH account_lvl AS (            -- account totals (no SKU)
    SELECT
        report_date,
        seller_id,
        country_code,
        /* --- BI-mapping fields ------------------------------------------------ */
        SUM(storage_storage_other) AS storage_storage_other ,
        SUM(storage_other_service) AS storage_other_service,
        SUM(storage_longterm_other) AS storage_longterm_other,
        SUM(revenue_shippingcredits_refund) AS revenue_shippingcredits_refund ,
        SUM(revenue_sales_refund) AS revenue_sales_refund,
        SUM(revenue_sales_liquidation) AS revenue_sales_liquidation,
        SUM(revenue_sales_chargeback) AS revenue_sales_chargeback,
        SUM(revenue_promo_refund)             AS revenue_promo_refund,
        SUM(revenue_other_refund)             AS revenue_other_refund,
        SUM(revenue_other_liquidation)        AS revenue_other_liquidation,
        SUM(revenue_giftwrapcredits_refund)   AS revenue_giftwrapcredits_refund,
        SUM(revenue_adjustment_liquidation)   AS revenue_adjustment_liquidation,

        SUM(not_reported_subscription)        AS not_reported_subscription,
        SUM(not_reported_mbs)                 AS not_reported_mbs,
        SUM(not_reported_immaterial)          AS not_reported_immaterial,

        SUM(fba_reimbursement_adjustment)     AS fba_reimbursement_adjustment,
        SUM(fba_other_service)                AS fba_other_service,
        SUM(fba_other_return)                 AS fba_other_return,
        SUM(fba_other_other)                  AS fba_other_other,
        SUM(fba_other_liquidation)            AS fba_other_liquidation,
        SUM(fba_other_adjustment)             AS fba_other_adjustment,
        SUM(fba_fees_refund)                  AS fba_fees_refund,

        SUM(commission_fees_refund)           AS commission_fees_refund,
        SUM(commission_fees_chargeback)       AS commission_fees_chargeback,

        SUM(cogs_placement_service)           AS cogs_placement_service,
        SUM(cogs_amzparteneredcarrier_other)  AS cogs_amzparteneredcarrier_other,

        SUM(advertising_vine_service)         AS advertising_vine_service,
        SUM(advertising_vine_other)           AS advertising_vine_other,
        SUM(advertising_ppc_service)          AS advertising_ppc_service,
        SUM(advertising_dealfees_service)     AS advertising_dealfees_service,
        SUM(advertising_dealfees_other)       AS advertising_dealfees_other
    FROM $stage_db.stg_brandebitda_revenue_non_order_account_specified_bi_mapping
    GROUP BY report_date, seller_id, country_code
),

/* ---------- revenue shares per SKU (deduplicated & null-free) ------------- */
sku_contrib AS (
    SELECT
        shipment_date       AS report_date,
        seller_id,
        country_code,
        sku,
        COALESCE(sku_account_contrib,0) AS sku_contrib,

        
        ROW_NUMBER() OVER (
            PARTITION BY shipment_date, seller_id, country_code, sku
            ORDER BY   sku_account_sales DESC       -- keep the one with the biggest base
        ) AS rn
    FROM $stage_db.stg_brandebitda_shipment_sku_revenue_contribution
   
    WHERE sku IS NOT NULL           
      AND sku_account_contrib IS NOT NULL
      AND sku_account_contrib <> 0  
    QUALIFY rn = 1                  
), 

unique_sku_account_list AS (
    SELECT
        shipment_date       AS report_date,
        seller_id,
        country_code,
        sku
    FROM $stage_db.stg_brandebitda_shipment_sku_revenue_contribution
    WHERE sku IS NOT NULL           
      AND sku_account_contrib IS NOT NULL
      AND sku_account_contrib <> 0  
    QUALIFY  ROW_NUMBER() OVER (
            PARTITION BY shipment_date, seller_id, country_code, sku
            ORDER BY   sku_account_sales DESC       -- keep the one with the biggest base
        ) = 1
)

/* ----------- allocate: Cartesian join on matching keys ----------------------- */
SELECT
    u.report_date,
    u.seller_id,
    u.country_code,
    u.sku,

    /* ---------- multiply each metric by sku_contrib -------------------------- */
    a.storage_storage_other            * s.sku_contrib AS storage_storage_other,
    a.storage_other_service            * s.sku_contrib AS storage_other_service,
    a.storage_longterm_other           * s.sku_contrib AS storage_longterm_other,
    a.revenue_shippingcredits_refund   * s.sku_contrib AS revenue_shippingcredits_refund,
    a.revenue_sales_refund             * s.sku_contrib AS revenue_sales_refund,
    a.revenue_sales_liquidation        * s.sku_contrib AS revenue_sales_liquidation,
    a.revenue_sales_chargeback         * s.sku_contrib AS revenue_sales_chargeback,
    a.revenue_promo_refund             * s.sku_contrib AS revenue_promo_refund,
    a.revenue_other_refund             * s.sku_contrib AS revenue_other_refund,
    a.revenue_other_liquidation        * s.sku_contrib AS revenue_other_liquidation,
    a.revenue_giftwrapcredits_refund   * s.sku_contrib AS revenue_giftwrapcredits_refund,
    a.revenue_adjustment_liquidation   * s.sku_contrib AS revenue_adjustment_liquidation,
    a.not_reported_subscription        * s.sku_contrib AS not_reported_subscription,
    a.not_reported_mbs                 * s.sku_contrib AS not_reported_mbs,
    a.not_reported_immaterial          * s.sku_contrib AS not_reported_immaterial,
    a.fba_reimbursement_adjustment     * s.sku_contrib AS fba_reimbursement_adjustment,
    a.fba_other_service                * s.sku_contrib AS fba_other_service,
    a.fba_other_return                 * s.sku_contrib AS fba_other_return,
    a.fba_other_other                  * s.sku_contrib AS fba_other_other,
    a.fba_other_liquidation            * s.sku_contrib AS fba_other_liquidation,
    a.fba_other_adjustment             * s.sku_contrib AS fba_other_adjustment,
    a.fba_fees_refund                  * s.sku_contrib AS fba_fees_refund,
    a.commission_fees_refund           * s.sku_contrib AS commission_fees_refund,
    a.commission_fees_chargeback       * s.sku_contrib AS commission_fees_chargeback,
    a.cogs_placement_service           * s.sku_contrib AS cogs_placement_service,
    a.cogs_amzparteneredcarrier_other  * s.sku_contrib AS cogs_amzparteneredcarrier_other,
    a.advertising_vine_service         * s.sku_contrib AS advertising_vine_service,
    a.advertising_vine_other           * s.sku_contrib AS advertising_vine_other,
    a.advertising_ppc_service          * s.sku_contrib AS advertising_ppc_service,
    a.advertising_dealfees_service     * s.sku_contrib AS advertising_dealfees_service,
    a.advertising_dealfees_other       * s.sku_contrib AS advertising_dealfees_other

FROM unique_sku_account_list u 
LEFT JOIN account_lvl a
    ON u.report_date = a.report_date
    AND u.seller_id = a.seller_id
    AND u.country_code = a.country_code
LEFT JOIN   sku_contrib s
       ON  u.report_date  = s.report_date
       AND u.sku = s.sku
       AND u.seller_id    = s.seller_id
       AND u.country_code = s.country_code;

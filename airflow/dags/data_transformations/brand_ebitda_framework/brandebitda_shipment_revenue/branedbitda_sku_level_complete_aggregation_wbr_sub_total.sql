
CREATE OR REPLACE TABLE
    $stage_db.stg_brandebitda_revenue_sku_level_complete_aggregation_wbr_subtotals       AS

SELECT
    /* keys */
    report_date,
    seller_id,
    country_code,
    sku,

    /* ───────── grouped subtotals ───────── */

    SUM(quantity_order) AS quantity,

    /*  revenue sales  */
      SUM(revenue_sales_order)
    + SUM(revenue_sales_refund)
    + SUM(revenue_sales_liquidation)
    + SUM(revenue_sales_chargeback)                       AS subtotal_revenue_sales,

    /*  revenue other  */
      SUM(revenue_shippingcredits_order)
    + SUM(revenue_shippingcredits_refund)
    + SUM(revenue_giftwrapcredits_order)
    + SUM(revenue_giftwrapcredits_refund)
    + SUM(revenue_other_refund)
    + SUM(revenue_other_liquidation)
    + SUM(revenue_adjustment_liquidation)                 AS subtotal_revenue_other,

    /*  revenue promo  */
      SUM(revenue_promo_order)
    + SUM(revenue_promo_refund)                           AS subtotal_revenue_promo,

    /*  selling fees   */
      SUM(commission_fees_order)
    + SUM(commission_fees_refund)
    + SUM(commission_fees_chargeback)
    + SUM(commission_other_order)                         AS subtotal_sellingfees,

    /*  FBA fees       */
      SUM(fba_fees_order)
    + SUM(fba_fees_refund)
    + SUM(fba_other_service)
    + SUM(fba_other_return)
    + SUM(fba_other_other)
    + SUM(fba_other_liquidation)
    + SUM(fba_other_adjustment)                           AS subtotal_fba_fees,

    /*  FBA reimbursement */
      SUM(fba_reimbursement_adjustment)                   AS subtotal_fba_reimbursement,

    /*  Deal fees      */
      SUM(advertising_dealfees_service)
    + SUM(advertising_dealfees_other)                     AS subtotal_dealfees,

    /*  PPC & Vine     */
      SUM(advertising_ppc_service)                        AS subtotal_ppc,
      SUM(advertising_vine_service)
    + SUM(advertising_vine_other)                         AS subtotal_ppc_vine,

    /*  Storage        */
      SUM(storage_storage_other)
    + SUM(storage_other_service)                          AS subtotal_storage,
      SUM(storage_longterm_other)                         AS subtotal_storage_longterm,

    /*  COGS adjuncts  */
      SUM(cogs_amzparteneredcarrier_other)                AS subtotal_cogs_amzpartneredcarrier,
      SUM(cogs_placement_service)                         AS subtotal_cogs_placement,

  

FROM $stage_db.stg_brandebitda_revenue_sku_level_complete_aggregation_bi_mapping
GROUP BY
    report_date, seller_id, country_code, sku;

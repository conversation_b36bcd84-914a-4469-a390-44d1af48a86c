CREATE OR REPLACE TABLE $stage_db.stg_brandebitda_shipment_sku_revenue_contribution AS

WITH seller_id_account_level_sales AS (        -- ① daily account totals
    SELECT
        seller_id_shipment,
        country_code,                          -- ← added
        shipment_date,
        SUM(total_sales)        AS total_account_sales
    FROM   $stage_db.stg_brandebitda_revenue_shipment_level_aggregate
    GROUP  BY seller_id_shipment,
              country_code,
              shipment_date
),

sku_level_sales AS (                           -- ② daily SKU totals
    SELECT
        seller_id_shipment,
        country_code,
        shipment_date,
        sku,
        SUM(total_sales)        AS sku_account_sales
    FROM   $stage_db.stg_brandebitda_revenue_shipment_level_aggregate
    GROUP  BY seller_id_shipment,
              country_code,
              shipment_date,
              sku
)

SELECT
    s.shipment_date,
    s.country_code,
    s.seller_id_shipment      AS seller_id,
    s.sku,
    s.sku_account_sales,
    a.total_account_sales      AS account_sales,
    s.sku_account_sales
      / NULLIF(a.total_account_sales,0)  AS sku_account_contrib        -- fractional share
FROM   sku_level_sales              s
JOIN   seller_id_account_level_sales a
       ON  s.seller_id_shipment = a.seller_id_shipment
       AND s.country_code       = a.country_code
       AND s.shipment_date      = a.shipment_date
ORDER  BY s.shipment_date DESC,
          s.seller_id_shipment,
          s.country_code,
          s.sku;

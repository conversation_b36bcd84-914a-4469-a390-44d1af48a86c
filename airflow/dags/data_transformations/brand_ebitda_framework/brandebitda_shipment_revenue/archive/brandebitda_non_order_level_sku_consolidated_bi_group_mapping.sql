/* stg_brandebitda_revenue_non_order_sku_metric_groups
   One row per (date, seller, country, marketplace, SKU) with group-level totals
*/
CREATE OR REPLACE TABLE $stage_db.stg_brandebitda_revenue_non_order_sku_consolidated_bi_group_mapping AS

WITH src AS (
  SELECT *
  FROM $stage_db.stg_brandebitda_revenue_non_order_sku_consolidated_bi_mapping
)

SELECT
  -- dimensional keys
  report_date,
  seller_id,
  country_code,
  marketplace,
  sku,

  /* ---------- revenue subtotals ---------- */
  COALESCE(revenue_sales_refund,0) +
  COALESCE(revenue_sales_liquidation,0) +
  COALESCE(revenue_sales_chargeback,0)               AS subtotal_revenue_sales,

  COALESCE(revenue_shippingcredits_refund,0) +
  COALESCE(revenue_giftwrapcredits_refund,0) +
  COALESCE(revenue_other_refund,0) +
  COALESCE(revenue_other_liquidation,0) +
  COALESCE(revenue_adjustment_liquidation,0)         AS subtotal_revenue_other,

  COALESCE(revenue_promo_refund,0)                   AS subtotal_revenue_promo,

  /* ---------- fees & reimbursements ---------- */
  COALESCE(commission_fees_refund,0) +
  COALESCE(commission_fees_chargeback,0)             AS subtotal_sellingfees,

  COALESCE(fba_reimbursement_adjustment,0)           AS subtotal_fba_reimbursement,

  COALESCE(fba_fees_refund,0) +
  COALESCE(fba_other_adjustment,0) +
  COALESCE(fba_other_service,0) +
  COALESCE(fba_other_return,0) +
  COALESCE(fba_other_liquidation,0) +
  COALESCE(fba_other_other,0)                        AS subtotal_fba_fees,

  /* ---------- storage ---------- */
  COALESCE(storage_storage_other,0) +
  COALESCE(storage_other_service,0)                  AS subtotal_storage,

  COALESCE(storage_longterm_other,0)                 AS subtotal_storage_longterm,

  /* ---------- advertising ---------- */
  COALESCE(advertising_dealfees_service,0) +
  COALESCE(advertising_dealfees_other,0)             AS subtotal_dealfees,

  COALESCE(advertising_ppc_service,0)                AS subtotal_ppc,

  COALESCE(advertising_vine_service,0) +
  COALESCE(advertising_vine_other,0)                 AS subtotal_ppc_vine,

  /* ---------- COGS ---------- */
  COALESCE(cogs_amzparteneredcarrier_other,0)        AS subtotal_cogs_amzpartneredcarrier,
  COALESCE(cogs_placement_service,0)                 AS subtotal_cogs_placement,

  /* ---------- not-reported buckets ---------- */
  --COALESCE(immaterial,0)                             AS not_reported_immaterial,
  --COALESCE("below BE (software)",0)                  AS not_reported_subscription,
  --COALESCE("below BE (MBS)",0)                       AS not_reported_mbs,
  0                                                  AS not_reported_mcf      -- no MCF metrics here

FROM src;

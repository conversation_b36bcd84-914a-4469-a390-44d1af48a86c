CREATE OR REPLACE TABLE $stage_db.stg_brandebitda_revenue_drr_deffered_shipment_consolidated AS

WITH drr_deferred_data AS (
  SELECT
    COALESCE(a.SELLER_ID, b.SELLER_ID) AS SELLER_ID,
    COALESCE(a.COUNTRY_CODE, b.COUNTRY_CODE) AS COUNTRY_CODE,
    COALESCE(a.TYPE, b.TYPE) AS TYPE,
    COALESCE(a.ORDER_ID, b.ORDER_ID) AS ORDER_ID,
    COALESCE(a.SKU, b.SKU) AS SKU,
    COALESCE(a.MARKETPLACE, b.MARKETPLACE) AS MARKETPLACE,
    COALESCE(a.ACCOUNT_TYPE, b.ACCOUNT_TYPE) AS ACCOUNT_TYPE,

    a.cnt AS DRR_CNT,
    b.cnt AS DEFERRED_CNT,

    -- Precedence logic centralized
    IFF(a.cnt > 0, 'drr', IFF(b.cnt > 0, 'deferred', 'NA')) AS source,

    -- Reuse precedence flag using IFF or define with a CTE above for reuse
    IFF(a.cnt > 0, a.quantity, b.quantity) AS source_quantity,
    IFF(a.cnt > 0, a.product_sales, b.product_sales) AS source_product_sales,
    IFF(a.cnt > 0, a.shipping_credits, b.shipping_credits) AS source_shipping_credits,
    IFF(a.cnt > 0, a.giftwrap_credits, b.giftwrap_credits) AS source_giftwrap_credits,
    IFF(a.cnt > 0, a.promo_rebates, b.promo_rebates) AS source_promo_rebates,
    IFF(a.cnt > 0, a.selling_fees, b.selling_fees) AS source_selling_fees,
    IFF(a.cnt > 0, a.fba_fees, b.fba_fees) AS source_fba_fees,
    IFF(a.cnt > 0, a.other_transac, b.other_transac) AS source_other_transac,
    IFF(a.cnt > 0, a.other_fees, b.other_fees) AS source_other_fees,

    -- Total sales: product_sales + shipping + giftwrap
    (
      IFF(a.cnt > 0, a.product_sales, b.product_sales) +
      IFF(a.cnt > 0, a.shipping_credits, b.shipping_credits) +
      IFF(a.cnt > 0, a.giftwrap_credits, b.giftwrap_credits) + 
      IFF(a.cnt > 0, a.promo_rebates, b.promo_rebates) 
    ) AS source_total_sales

  FROM DWH_DEV.STAGING.stg_brandebitda_revenue_drr_order_level_aggregate a
  FULL OUTER JOIN DWH_DEV.STAGING.stg_brandebitda_revenue_deffered_order_level_aggregate b
    ON a.SELLER_ID = b.SELLER_ID
   AND a.COUNTRY_CODE = b.COUNTRY_CODE
   AND a.ORDER_ID = b.ORDER_ID
   AND a.SKU = b.SKU
   AND a.TYPE = b.TYPE
   AND a.MARKETPLACE = b.MARKETPLACE
   AND a.ACCOUNT_TYPE = b.ACCOUNT_TYPE

),


joined_shipments_to_drr AS (
  SELECT
    a.*,
    b.source_total_sales,
    b.source_product_sales,
    b.source_shipping_credits,
    b.source_giftwrap_credits,
    b.source_promo_rebates,
    b.source_selling_fees,
    b.source_fba_fees,
    b.source_other_transac,
    b.source_other_fees,
    (b.source_total_sales - a.total_sales) AS gap_total_sales,
    b.source_quantity
  FROM DWH_DEV.STAGING.stg_brandebitda_revenue_shipment_level_aggregate a
  LEFT JOIN drr_deferred_data b
    ON a.seller_id_shipment = b.SELLER_ID
    AND a.order_id = b.ORDER_ID
    AND a.sku = b.SKU
    AND a.country_code = b.COUNTRY_CODE
)


  SELECT
    *,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_product_sales / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_product_sales END AS pro_rated_source_product_sales,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_shipping_credits / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_shipping_credits END AS pro_rated_source_shipping_credits,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_giftwrap_credits / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_giftwrap_credits END AS pro_rated_source_giftwrap_credits,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_promo_rebates / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_promo_rebates END AS pro_rated_source_promo_rebates,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_selling_fees / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_selling_fees END AS pro_rated_source_selling_fees,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_fba_fees / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_fba_fees END AS pro_rated_source_fba_fees,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_other_transac / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_other_transac END AS pro_rated_source_other_transac,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN CAST(source_other_fees / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2))
         ELSE source_other_fees END AS pro_rated_source_other_fees,
    CASE WHEN ABS(gap_total_sales) >= 0.01 
         THEN (CAST(source_product_sales / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2)) +
               CAST(source_shipping_credits / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2)) +
               CAST(source_giftwrap_credits / NULLIF(source_quantity, 0) * quantity AS DECIMAL(10,2)))
         ELSE source_total_sales END AS pro_rated_total_sales
  FROM joined_shipments_to_drr

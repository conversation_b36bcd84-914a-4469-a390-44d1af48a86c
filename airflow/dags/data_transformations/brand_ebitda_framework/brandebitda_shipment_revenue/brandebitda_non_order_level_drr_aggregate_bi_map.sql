CREATE OR REPLACE TABLE $stage_db.stg_brandebitda_revenue_drr_non_order_level_aggregate_bi_map AS

WITH non_order_unpivoted_drr_with_simplified_description AS (
SELECT
DATE(REPORT_DATE) as report_date,
SELLER_ID,
COUNTRY_CODE,
TYPE,
SKU,
LOWER(MARKETPLACE) AS marketplace,
CASE
    WHEN SKU IS NOT NULL AND DESCRIPTION LIKE 'FBA Inventory Reimbursement%' THEN 'FBA Inventory Reimbursement'
    WHEN SKU IS NOT NULL AND DESCRIPTION LIKE 'FBA Customer Return Per Unit Fee%' THEN 'FBA Customer Return Per Unit Fee'
    WHEN SKU IS NOT NULL AND DESCRIPTION LIKE 'Inbound Defect Fee%' THEN 'Inbound Defect Fee - Shipments delivered to the wrong location'
    WHEN DESCRIPTION LIKE 'FBA Inventory Reimbursement - %' THEN 'FBA Inventory Reimbursement'
    WHEN DESCRIPTION LIKE 'FBA Prep Fee%' THEN 'FBA Prep Fee'
    WHEN DESCRIPTION LIKE 'FBA Removal Order%' THEN 'FBA Removal Order'
    WHEN DESCRIPTION LIKE 'AWD %' THEN 'AWD'
    WHEN DESCRIPTION LIKE 'Deals-%' OR DESCRIPTION LIKE 'Lightning Deal-%' OR DESCRIPTION LIKE 'Coupon %' THEN 'Deals / Coupons'
    WHEN DESCRIPTION LIKE 'FBA Customer Returns Fee (Non-Apparel %' THEN 'FBA Customer Returns Fee (Non-Apparel and Non-Shoes)'
    WHEN SKU IS NOT NULL AND DESCRIPTION NOT LIKE 'FBA Inventory Reimbursement%' THEN 'product_description'
    ELSE DESCRIPTION
END AS simplified_description,
CASE
    WHEN SKU IS NOT NULL THEN 'yes'
    ELSE 'no'
END AS sku_specified,
CASE
    WHEN marketplace IS NOT NULL THEN marketplace
    ELSE 'null_marketplace'
END AS revised_marketplace,
SUM(PRODUCT_SALES) AS product_sales,
SUM(SHIPPING_CREDITS) AS shipping_credits,
SUM(GIFT_WRAP_CREDITS) AS giftwrap_credits,
SUM(PROMOTIONAL_REBATES) AS promotional_rebates,
SUM(SELLING_FEES) AS selling_fees,
SUM(FBA_FEES) AS fba_fees,
SUM(OTHER_TRANSACTION_FEES) AS other_transaction_fees,
SUM(OTHER) AS other_fees
FROM DWH.PROD.FACT_AMAZON_SETTLEMENT_TRANSACTIONS_SCS
WHERE 1=1
AND type NOT IN ('Order','Transfer','Debt')
AND TOTAL!=0
GROUP BY ALL
)
, non_order_pivoted_drr_with_simplified_description AS (
SELECT * FROM non_order_unpivoted_drr_with_simplified_description
UNPIVOT
(AMOUNT FOR category IN (PRODUCT_SALES,SHIPPING_CREDITS,GIFTWRAP_CREDITS,PROMOTIONAL_REBATES,SELLING_FEES,FBA_FEES,OTHER_TRANSACTION_FEES,OTHER_FEES))
WHERE amount!=0
)
, adapted_bi_mapping AS (
SELECT *,
CASE
    WHEN marketplace IS NOT NULL THEN marketplace
    ELSE 'null_marketplace'
END AS revised_marketplace, 
CASE 
    WHEN CATEGORY = 'other' THEN 'OTHER_FEES'
    ELSE CATEGORY 
END AS category_revised, 
CASE 
WHEN sku_specified = TRUE THEN 'yes'
WHEN sku_specified =FALSE THEN 'no'
END AS sku_specified_revised
FROM DWH_DEV.STAGING.STG_BRANDEBITDA_MAPPING_CATEGORY_BI_MAPPING_MANUAL_LOAD
)

--, joined_non_order_pivoted_drr_with_simplified_description_to_bi_mapping AS (
SELECT
a.*,
b.bi_mapping
FROM non_order_pivoted_drr_with_simplified_description a
LEFT JOIN adapted_bi_mapping b
ON LOWER(a.TYPE) = LOWER(b.TYPE)
AND LOWER(a.simplified_description) = LOWER(b.simplified_description)
AND LOWER(a.category) = LOWER(b.category_revised)
AND LOWER(a.sku_specified) = LOWER(b.sku_specified_revised)
AND LOWER(a.revised_marketplace) = LOWER(b.revised_marketplace)
--where bi_mapping is null
--)
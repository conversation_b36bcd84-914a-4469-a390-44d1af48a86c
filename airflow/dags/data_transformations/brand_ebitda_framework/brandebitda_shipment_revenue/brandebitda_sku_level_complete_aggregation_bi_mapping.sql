CREATE OR REPLACE TABLE
    $stage_db.stg_brandebitda_revenue_sku_level_complete_aggregation_bi_mapping AS

/* ───────── ① ORDER-side (DRR + Deferred, already prorated) ─────────── */
WITH order_src AS (
    SELECT
        shipment_date                    AS report_date,
        seller_id_shipment               AS seller_id,
        country_code,
        sku,

        /* order metrics */
        SUM(quantity)                                    AS quantity_order,
        SUM(pro_rated_source_product_sales)              AS revenue_sales_order,
        SUM(pro_rated_source_shipping_credits)           AS revenue_shippingcredits_order,
        SUM(pro_rated_source_giftwrap_credits)           AS revenue_giftwrapcredits_order,
        SUM(pro_rated_source_promo_rebates)              AS revenue_promo_order,
        SUM(pro_rated_source_selling_fees)               AS commission_fees_order,
        SUM(pro_rated_source_fba_fees)                   AS fba_fees_order,
        SUM(pro_rated_source_other_transac)              AS commission_other_order,
        SUM(pro_rated_source_other_fees)                 AS not_reported_immaterial
    FROM   $stage_db.stg_brandebitda_revenue_drr_deffered_shipment_consolidated
    GROUP  BY
        shipment_date, seller_id_shipment,
        country_code, sku
)

    /* ───────── non_order_src (aggregated) ───────── */
, non_order_src AS (
    SELECT
        report_date,
        seller_id,
        country_code,
        sku,

        /* quantity doesn’t exist on non-order rows */
        0 AS quantity_non_order,

        /* ── revenue / cost buckets ── */
        SUM(revenue_sales_refund)            AS revenue_sales_refund,
        SUM(revenue_sales_liquidation)       AS revenue_sales_liquidation,
        SUM(revenue_sales_chargeback)        AS revenue_sales_chargeback,
        SUM(revenue_other_refund)            AS revenue_other_refund,
        SUM(revenue_other_liquidation)       AS revenue_other_liquidation,

        SUM(revenue_shippingcredits_refund)  AS revenue_shippingcredits_refund,
        SUM(revenue_giftwrapcredits_refund)  AS revenue_giftwrapcredits_refund,
        SUM(revenue_promo_refund)            AS revenue_promo_refund,

        SUM(commission_fees_refund)          AS commission_fees_refund,
        SUM(commission_fees_chargeback)      AS commission_fees_chargeback,

        SUM(fba_fees_refund)                 AS fba_fees_refund,
        SUM(fba_reimbursement_adjustment)    AS fba_reimbursement_adjustment,
        SUM(fba_other_service)               AS fba_other_service,
        SUM(fba_other_return)                AS fba_other_return,
        SUM(fba_other_other)                 AS fba_other_other,
        SUM(fba_other_liquidation)           AS fba_other_liquidation,
        SUM(fba_other_adjustment)            AS fba_other_adjustment,

        SUM(revenue_adjustment_liquidation)  AS revenue_adjustment_liquidation,

        SUM(storage_storage_other)           AS storage_storage_other,
        SUM(storage_other_service)           AS storage_other_service,
        SUM(storage_longterm_other)          AS storage_longterm_other,

        SUM(cogs_placement_service)          AS cogs_placement_service,
        SUM(cogs_amzparteneredcarrier_other) AS cogs_amzparteneredcarrier_other,

        SUM(advertising_vine_service)        AS advertising_vine_service,
        SUM(advertising_vine_other)          AS advertising_vine_other,
        SUM(advertising_ppc_service)         AS advertising_ppc_service,
        SUM(advertising_dealfees_service)    AS advertising_dealfees_service,
        SUM(advertising_dealfees_other)      AS advertising_dealfees_other

    FROM   $stage_db.stg_brandebitda_revenue_non_order_sku_consolidated_bi_mapping
    GROUP  BY report_date, seller_id, country_code, sku
),

/* ③ One-to-one/full join on the four key dimensions */
joined AS (
    SELECT
        COALESCE(o.report_date,  n.report_date)   AS report_date,
        COALESCE(o.seller_id,    n.seller_id)     AS seller_id,
        COALESCE(o.country_code, n.country_code)  AS country_code,
        COALESCE(o.sku,          n.sku)           AS sku,

        /* order metrics */
        o.quantity_order,
        o.revenue_sales_order,
        o.revenue_shippingcredits_order,
        o.revenue_giftwrapcredits_order,
        o.revenue_promo_order,
        o.commission_fees_order,
        o.fba_fees_order,
        o.commission_other_order,
        o.not_reported_immaterial,

        /* non-order metrics */
        n.quantity_non_order,
        n.revenue_sales_refund,
        n.revenue_sales_liquidation,
        n.revenue_sales_chargeback,
        n.revenue_other_refund,
        n.revenue_other_liquidation,

        n.revenue_shippingcredits_refund,
        n.revenue_giftwrapcredits_refund,
        n.revenue_promo_refund,

        n.commission_fees_refund,
        n.commission_fees_chargeback,

        n.fba_fees_refund,
        n.fba_reimbursement_adjustment,
        n.fba_other_service,
        n.fba_other_return,
        n.fba_other_other,
        n.fba_other_liquidation,
        n.fba_other_adjustment,

        n.revenue_adjustment_liquidation,

        n.storage_storage_other,
        n.storage_other_service,
        n.storage_longterm_other,
        n.cogs_placement_service,
        n.cogs_amzparteneredcarrier_other,

        n.advertising_vine_service,
        n.advertising_vine_other,
        n.advertising_ppc_service,
        n.advertising_dealfees_service,
        n.advertising_dealfees_other
    FROM order_src o
    FULL OUTER JOIN non_order_src n
      ON  o.report_date  = n.report_date
      AND o.seller_id    = n.seller_id
      AND o.country_code = n.country_code
      AND o.sku          = n.sku
)

/* ④ Persist the joined result */
SELECT * FROM joined;

CREATE OR REPLACE TABLE
    $stage_db.stg_brandebitda_revenue_sku_level_revenue_fba_all_components    AS

WITH bi_mapping_and_subtotal AS 
(
SELECT
    /* keys */
    report_date,
    seller_id,
    country_code,
    sku,

    /* ───────── grouped subtotals ───────── */

    SUM(quantity_order) AS quantity,

    /* A. Revenue – product-sales‐like */
    SUM(revenue_sales_order)                               AS revenue_sales_order,
    SUM(revenue_sales_refund)                              AS revenue_sales_refund,
    SUM(revenue_sales_liquidation)                         AS revenue_sales_liquidation,
    SUM(revenue_sales_chargeback)                          AS revenue_sales_chargeback,

    /* B. Revenue – other credits / debits */
    SUM(revenue_shippingcredits_order)                     AS revenue_shippingcredits_order,
    SUM(revenue_shippingcredits_refund)                    AS revenue_shippingcredits_refund,
    SUM(revenue_giftwrapcredits_order)                     AS revenue_giftwrapcredits_order,
    SUM(revenue_giftwrapcredits_refund)                    AS revenue_giftwrapcredits_refund,
    SUM(revenue_other_refund)                              AS revenue_other_refund,
    SUM(revenue_other_liquidation)                         AS revenue_other_liquidation,
    SUM(revenue_adjustment_liquidation)                    AS revenue_adjustment_liquidation,

    /* C. Revenue – promo */
    SUM(revenue_promo_order)                               AS revenue_promo_order,
    SUM(revenue_promo_refund)                              AS revenue_promo_refund,

    /* D. Selling fees */
    SUM(commission_fees_order)                             AS commission_fees_order,
    SUM(commission_fees_refund)                            AS commission_fees_refund,
    SUM(commission_fees_chargeback)                        AS commission_fees_chargeback,
    SUM(commission_other_order)                            AS commission_other_order,

    /* E. FBA fees */
    SUM(fba_fees_order)                                    AS fba_fees_order,
    SUM(fba_fees_refund)                                   AS fba_fees_refund,
    SUM(fba_other_service)                                 AS fba_other_service,
    SUM(fba_other_return)                                  AS fba_other_return,
    SUM(fba_other_other)                                   AS fba_other_other,
    SUM(fba_other_liquidation)                             AS fba_other_liquidation,
    SUM(fba_other_adjustment)                              AS fba_other_adjustment,

    /* F. FBA reimbursement */
    SUM(fba_reimbursement_adjustment)                      AS fba_reimbursement_adjustment,

    /* G. Deal / PPC / Vine */
    SUM(advertising_dealfees_service)                      AS advertising_dealfees_service,
    SUM(advertising_dealfees_other)                        AS advertising_dealfees_other,
    SUM(advertising_ppc_service)                           AS advertising_ppc_service,
    SUM(advertising_vine_service)                          AS advertising_vine_service,
    SUM(advertising_vine_other)                            AS advertising_vine_other,

    /* H. Storage & long-term storage */
    SUM(storage_storage_other)                             AS storage_storage_other,
    SUM(storage_other_service)                             AS storage_other_service,
    SUM(storage_longterm_other)                            AS storage_longterm_other,

    /* I. COGS adjuncts */
    SUM(cogs_amzparteneredcarrier_other)                   AS cogs_amzparteneredcarrier_other,
    SUM(cogs_placement_service)                            AS cogs_placement_service,



    /*  revenue sales  */
      SUM(revenue_sales_order)
    + SUM(revenue_sales_refund)
    + SUM(revenue_sales_liquidation)
    + SUM(revenue_sales_chargeback)                       AS subtotal_revenue_sales,

    /*  revenue other  */
      SUM(revenue_shippingcredits_order)
    + SUM(revenue_shippingcredits_refund)
    + SUM(revenue_giftwrapcredits_order)
    + SUM(revenue_giftwrapcredits_refund)
    + SUM(revenue_other_refund)
    + SUM(revenue_other_liquidation)
    + SUM(revenue_adjustment_liquidation)                 AS subtotal_revenue_other,

    /*  revenue promo  */
      SUM(revenue_promo_order)
    + SUM(revenue_promo_refund)                           AS subtotal_revenue_promo,

    /*  selling fees   */
      SUM(commission_fees_order)
    + SUM(commission_fees_refund)
    + SUM(commission_fees_chargeback)
    + SUM(commission_other_order)                         AS subtotal_selling_fees,

    /*  FBA fees       */
      SUM(fba_fees_order)
    + SUM(fba_fees_refund)
    + SUM(fba_other_service)
    + SUM(fba_other_return)
    + SUM(fba_other_other)
    + SUM(fba_other_liquidation)
    + SUM(fba_other_adjustment)                           AS subtotal_fba_fees,

    /*  FBA reimbursement */
      SUM(fba_reimbursement_adjustment)                   AS subtotal_fba_reimbursement,

    /*  Deal fees      */
      SUM(advertising_dealfees_service)
    + SUM(advertising_dealfees_other)                     AS subtotal_deal_fees,

    /*  PPC & Vine     */
      SUM(advertising_ppc_service)                        AS subtotal_ppc,
      SUM(advertising_vine_service)
    + SUM(advertising_vine_other)                         AS subtotal_ppc_vine,

    /*  Storage        */
      SUM(storage_storage_other)
    + SUM(storage_other_service)                          AS subtotal_storage,
      SUM(storage_longterm_other)                         AS subtotal_storage_longterm,

    /*  COGS adjuncts  */
      SUM(cogs_amzparteneredcarrier_other)                AS subtotal_cogs_amzpartneredcarrier,
      SUM(cogs_placement_service)                         AS subtotal_cogs_placement,

  
FROM $stage_db.stg_brandebitda_revenue_sku_level_complete_aggregation_bi_mapping
GROUP BY
    report_date, seller_id, country_code, sku
)

SELECT
    bi_sub.*,

    /* ───────────── WBR-style totals added here ───────────── */

      subtotal_revenue_sales
    + subtotal_revenue_other
    + subtotal_revenue_promo                         AS total_net_revenue,

      subtotal_fba_fees
    + subtotal_fba_reimbursement                     AS total_fulfillment_fees,

    subtotal_selling_fees                            AS total_selling_fees,

      subtotal_cogs_amzpartneredcarrier
    + subtotal_cogs_placement                        AS total_cogs,

    /* QA / alternate buckets */
    subtotal_deal_fees            AS alternate_deal_fees_qa,
    subtotal_storage              AS alternate_storage_fees_qa,
    subtotal_storage_longterm     AS alternate_long_term_storage_qa,
    subtotal_ppc                  AS alternate_ppc_cost_qa,
    subtotal_ppc_vine             AS alternate_ppc_vine_qa

FROM bi_mapping_and_subtotal AS bi_sub
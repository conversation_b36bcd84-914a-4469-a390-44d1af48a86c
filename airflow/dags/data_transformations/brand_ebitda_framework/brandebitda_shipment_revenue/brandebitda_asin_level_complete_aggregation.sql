
CREATE OR REPLACE TABLE
    $stage_db.stg_brandebitda_revenue_asin_level_consolidated_daily AS


/* ── ①  SKU → ASIN bridge (unchanged) ─────────────────────────────── */
WITH sku_asin_map_orders AS (
    SELECT DISTINCT
           o.sku,
           o.country_code,
           o.brand_code,
           o.asin,
           o.product_name,
           o.market_place_region
    FROM $stage_db.stg_brand_ebitda_sku_asin_map_orders_source_table  o
)

/* ── ②  bring SKU-level splits (order / non-order) ─────────────────── */
, sku_cf AS (
    SELECT
        report_date,
        seller_id,
        country_code,
        sku,
        quantity_order,
        quantity_non_order,
        product_sales_order,
        product_sales_non_order,
        shipping_credits_order,
        shipping_credits_non_order,
        giftwrap_credits_order,
        giftwrap_credits_non_order,
        promo_rebates_order,
        promo_rebates_non_order,
        selling_fees_order,
        selling_fees_non_order,
        fba_fees_order,
        fba_fees_non_order,
        other_transaction_fees_order,
        other_transaction_fees_non_order,
        other_fees_order,
        other_fees_non_order,
        total_sales_order,
        total_sales_non_order
    FROM $stage_db.stg_brandebitda_revenue_sku_level_consolidated_daily
)

/* ── ③  most-recent brand / category per ASIN × country ────────────── */
, map_latest AS (
    SELECT *
    FROM (
        SELECT
            child_asin,
            country_code,
            brand,
            category,
            niche,
            product_type,
            brand_grouping,
            brand_manager,
            account_title,
            ROW_NUMBER() OVER (
                PARTITION BY child_asin, country_code
                ORDER BY record_updated_timestamp_utc DESC,
                         record_created_timestamp_utc DESC
            ) AS rn
        FROM $stage_db.merge_brand_ebitda_asin_mapping
        WHERE child_asin IS NOT NULL
    )
    WHERE rn = 1
)

/* ── ④  aggregate SKU → ASIN ───────────────────────────────────────── */
, asin_daily AS (
    SELECT
        s.report_date,
        s.seller_id,
        m.market_place_region,
        s.country_code,
        m.brand_code,
        m.asin,
        m.product_name,

        /* -------- order / non-order buckets -------- */
        SUM(s.quantity_order)                       AS quantity_order,
        SUM(s.quantity_non_order)                   AS quantity_non_order,

        SUM(s.product_sales_order)                  AS product_sales_order,
        SUM(s.product_sales_non_order)              AS product_sales_non_order,

        SUM(s.shipping_credits_order)               AS shipping_credits_order,
        SUM(s.shipping_credits_non_order)           AS shipping_credits_non_order,

        SUM(s.giftwrap_credits_order)               AS giftwrap_credits_order,
        SUM(s.giftwrap_credits_non_order)           AS giftwrap_credits_non_order,

        SUM(s.promo_rebates_order)                  AS promo_rebates_order,
        SUM(s.promo_rebates_non_order)              AS promo_rebates_non_order,

        SUM(s.selling_fees_order)                   AS selling_fees_order,
        SUM(s.selling_fees_non_order)               AS selling_fees_non_order,

        SUM(s.fba_fees_order)                       AS fba_fees_order,
        SUM(s.fba_fees_non_order)                   AS fba_fees_non_order,

        SUM(s.other_transaction_fees_order)         AS other_transaction_fees_order,
        SUM(s.other_transaction_fees_non_order)     AS other_transaction_fees_non_order,

        SUM(s.other_fees_order)                     AS other_fees_order,
        SUM(s.other_fees_non_order)                 AS other_fees_non_order,

        SUM(s.total_sales_order)                    AS total_sales_order,
        SUM(s.total_sales_non_order)                AS total_sales_non_order
    FROM   sku_cf s
    JOIN   sku_asin_map_orders m
           ON  s.sku          = m.sku
           AND s.country_code = m.country_code
    GROUP  BY
        s.report_date,
        s.seller_id,
        m.market_place_region,
        s.country_code,
        m.brand_code,
        m.asin,
        m.product_name
)

/* ── ⑤  add *_total columns & brand enrichment ─────────────────────── */
SELECT
    a.*,

    /* ---------- *_total columns ---------- */
    /* Quantity: non-order always zero, so keep order value */
    a.quantity_order                                           AS quantity_total,

    a.product_sales_order          + a.product_sales_non_order           AS product_sales_total,
    a.shipping_credits_order       + a.shipping_credits_non_order        AS shipping_credits_total,
    a.giftwrap_credits_order       + a.giftwrap_credits_non_order        AS giftwrap_credits_total,
    a.promo_rebates_order          + a.promo_rebates_non_order           AS promo_rebates_total,
    a.selling_fees_order           + a.selling_fees_non_order            AS selling_fees_total,
    a.fba_fees_order               + a.fba_fees_non_order                AS fba_fees_total,
    a.other_transaction_fees_order + a.other_transaction_fees_non_order  AS other_transaction_fees_total,
    a.other_fees_order             + a.other_fees_non_order              AS other_fees_total,
    a.total_sales_order            + a.total_sales_non_order             AS total_sales_total,

    ABS( COALESCE(a.product_sales_order, 0)           + COALESCE(a.product_sales_non_order, 0) ) +
    ABS( COALESCE(a.shipping_credits_order, 0)        + COALESCE(a.shipping_credits_non_order, 0) ) +
    ABS( COALESCE(a.giftwrap_credits_order, 0)        + COALESCE(a.giftwrap_credits_non_order, 0) ) -
    ABS( COALESCE(a.promo_rebates_order, 0)           + COALESCE(a.promo_rebates_non_order, 0) )     AS total_net_revenue,


    /* ---------- brand master attributes ---------- */
    ml.brand,
    ml.category,
    ml.niche,
    ml.product_type,
    ml.brand_grouping,
    ml.brand_manager,
    ml.account_title

FROM   asin_daily a
LEFT JOIN map_latest ml
       ON  a.asin         = ml.child_asin
       AND a.country_code = ml.country_code;

CREATE OR REPLACE TABLE
    $stage_db.stg_brandebitda_revenue_sku_level_order_non_order_revenue_fba_all_components    AS

with shipments_data as (
    select 
        "amazon_order_id" as shipment_order_id,
        LOWER("sales_channel") as shipment_channel,
        'US' AS country_code,
        "seller_id" as shipment_seller_id, 
        "sku" as shipment_sku, 
        DATE_TRUNC('day',TRY_TO_DATE("shipment_date")) as shipment_date,
        SUM("quantity_shipped") AS quantity,
        SUM("item_price") AS product_sales,
        SUM("shipping_price") AS shipping_credits,
        SUM("gift_wrap_price") AS giftwrap_credits,
        SUM("item_promotion_discount" + "ship_promotion_discount") AS promo_rebates,
        product_sales + shipping_credits + giftwrap_credits + promo_rebates AS total_sales
    from dwh.staging.amazon_fba_fulfilled_shipments
    where TRY_TO_DATE("shipment_date") >= '2024-01-01'
    and LOWER("sales_channel") ='amazon.com'
    group by all
)
, drr_data as (
    select
        SELLER_ID,
        COUNTRY_CODE,
        TYP<PERSON>,
        ORDER_ID,
        SKU,
        LOWER(MARKETPLACE) as marketplace,
        ACCOUNT_TYPE,
        SUM(QUANTITY) AS quantity,
        SUM(PRODUCT_SALES) AS product_sales,
        SUM(SHIPPING_CREDITS) AS shipping_credits,
        SUM(GIFT_WRAP_CREDITS) AS giftwrap_credits,
        SUM(PROMOTIONAL_REBATES) AS promo_rebates,
        SUM(SELLING_FEES) AS selling_fees,
        SUM(FBA_FEES) AS fba_fees,
        SUM(OTHER_TRANSACTION_FEES) AS other_transac,
        SUM(OTHER) AS other_fees,
        'drr' as source,
        count(*) as cnt
    FROM DWH.PROD.FACT_AMAZON_SETTLEMENT_TRANSACTIONS_SCS
    WHERE type='Order' and LOWER(MARKETPLACE) ='amazon.com'
    AND report_date::date >= '2024-01-01' AND report_date::date <= CURRENT_DATE() -2
    GROUP BY ALL
)
, deferred_data as (
    select
        SELLER_ID,
        COUNTRY_CODE,
        TYPE,
        ORDER_ID,
        SKU,
        LOWER(MARKETPLACE) as marketplace,
        ACCOUNT_TYPE,
        SUM(QUANTITY) AS quantity,
        SUM(PRODUCT_SALES) AS product_sales,
        SUM(SHIPPING_CREDITS) AS shipping_credits,
        SUM(GIFT_WRAP_CREDITS) AS giftwrap_credits,
        SUM(PROMOTIONAL_REBATES) AS promo_rebates,
        SUM(SELLING_FEES) AS selling_fees,
        SUM(FBA_FEES) AS fba_fees,
        SUM(OTHER_TRANSACTION_FEES) AS other_transac,
        SUM(OTHER) AS other_fees,
        'deferred' as source,
        count(*) as cnt
    FROM dwh.prod.fact_amazon_deferred_transactions
    WHERE type='Order' and LOWER(MARKETPLACE) ='amazon.com'
    AND report_requested_time::date = CURRENT_DATE() -1
    GROUP BY ALL
    )
, drr_deferred_data as (
SELECT 
coalesce(a.SELLER_ID, b.SELLER_ID) as SELLER_ID,
coalesce(a.COUNTRY_CODE, b.COUNTRY_CODE) as COUNTRY_CODE,
coalesce(a.TYPE, b.TYPE) as TYPE,
coalesce(a.ORDER_ID, b.ORDER_ID) as ORDER_ID,
coalesce(a.SKU, b.SKU) as SKU,
coalesce(a.MARKETPLACE, b.MARKETPLACE) as MARKETPLACE,
coalesce(a.ACCOUNT_TYPE, b.ACCOUNT_TYPE) as ACCOUNT_TYPE,
a.cnt AS DRR_CNT, b.cnt AS DEFERRED_CNT,
CASE WHEN DRR_CNT>0 THEN 'drr' WHEN DEFERRED_CNT>0 THEN 'deferred' ELSE 'NA' END AS source,
CASE WHEN DRR_CNT>0 THEN a.quantity WHEN DEFERRED_CNT>0 THEN b.quantity ELSE 0 END AS source_quantity,
CASE WHEN DRR_CNT>0 THEN a.product_sales WHEN DEFERRED_CNT>0 THEN b.product_sales ELSE 0 END AS source_product_sales,
CASE WHEN DRR_CNT>0 THEN a.shipping_credits WHEN DEFERRED_CNT>0 THEN b.shipping_credits ELSE 0 END AS source_shipping_credits,
CASE WHEN DRR_CNT>0 THEN a.giftwrap_credits WHEN DEFERRED_CNT>0 THEN b.giftwrap_credits ELSE 0 END AS source_giftwrap_credits,
CASE WHEN DRR_CNT>0 THEN a.promo_rebates WHEN DEFERRED_CNT>0 THEN b.promo_rebates ELSE 0 END AS source_promo_rebates,
CASE WHEN DRR_CNT>0 THEN a.selling_fees WHEN DEFERRED_CNT>0 THEN b.selling_fees ELSE 0 END AS source_selling_fees,
CASE WHEN DRR_CNT>0 THEN a.fba_fees WHEN DEFERRED_CNT>0 THEN b.fba_fees ELSE 0 END AS source_fba_fees,
CASE WHEN DRR_CNT>0 THEN a.other_transac WHEN DEFERRED_CNT>0 THEN b.other_transac ELSE 0 END AS source_other_transac,
CASE WHEN DRR_CNT>0 THEN a.other_fees WHEN DEFERRED_CNT>0 THEN b.other_fees ELSE 0 END AS source_other_fees,
source_product_sales + source_shipping_credits + source_giftwrap_credits + source_promo_rebates AS source_total_sales
from drr_data a
full join deferred_data b
on 
a.SELLER_ID=b.SELLER_ID
and a.COUNTRY_CODE=b.COUNTRY_CODE
and a.ORDER_ID=b.ORDER_ID
and a.SKU=b.SKU
and a.TYPE=b.TYPE
and a.MARKETPLACE=b.MARKETPLACE
and a.ACCOUNT_TYPE=b.ACCOUNT_TYPE
)
, joined_shipments_to_drr_and_deferred_before_unit_fee_solution AS (
select
*,
b.source_total_sales - a.total_sales AS gap_total_sales
FROM shipments_data a
left join DRR_DEFERRED_DATA b
on a.shipment_seller_id=b.seller_id
and a.shipment_order_id=b.order_id
and a.shipment_sku=b.sku
and a.country_code=b.country_code
and a.shipment_channel=b.marketplace
)
,joined_shipments_to_drr_and_deferred_with_unit_fee_solution AS (
SELECT
*,
CASE WHEN ABS(gap_total_sales)>=0.01 THEN CAST(SOURCE_PRODUCT_SALES / SOURCE_QUANTITY * QUANTITY AS DECIMAL(10,2)) ELSE SOURCE_PRODUCT_SALES END AS pro_rated_source_product_sales,
CASE WHEN ABS(gap_total_sales)>=0.01 THEN CAST(SOURCE_SHIPPING_CREDITS / SOURCE_QUANTITY * QUANTITY AS DECIMAL(10,2)) ELSE SOURCE_SHIPPING_CREDITS END AS pro_rated_source_shipping_credits,
CASE WHEN ABS(gap_total_sales)>=0.01 THEN CAST(SOURCE_GIFTWRAP_CREDITS / SOURCE_QUANTITY * QUANTITY AS DECIMAL(10,2)) ELSE SOURCE_GIFTWRAP_CREDITS END AS pro_rated_source_giftwrap_credits,
CASE WHEN ABS(gap_total_sales)>=0.01 THEN CAST(SOURCE_PROMO_REBATES / SOURCE_QUANTITY * QUANTITY AS DECIMAL(10,2)) ELSE SOURCE_PROMO_REBATES END AS pro_rated_source_promo_rebates,
CASE WHEN ABS(gap_total_sales)>=0.01 THEN CAST(SOURCE_SELLING_FEES / SOURCE_QUANTITY * QUANTITY AS DECIMAL(10,2)) ELSE SOURCE_SELLING_FEES END AS pro_rated_source_selling_fees,
CASE WHEN ABS(gap_total_sales)>=0.01 THEN CAST(SOURCE_FBA_FEES / SOURCE_QUANTITY * QUANTITY AS DECIMAL(10,2)) ELSE SOURCE_FBA_FEES END AS pro_rated_source_fba_fees,
CASE WHEN ABS(gap_total_sales)>=0.01 THEN CAST(SOURCE_OTHER_TRANSAC / SOURCE_QUANTITY * QUANTITY AS DECIMAL(10,2)) ELSE SOURCE_OTHER_TRANSAC END AS pro_rated_source_other_transac,
CASE WHEN ABS(gap_total_sales)>=0.01 THEN CAST(SOURCE_OTHER_FEES / SOURCE_QUANTITY * QUANTITY AS DECIMAL(10,2)) ELSE SOURCE_OTHER_FEES END AS pro_rated_source_other_fees,
CASE WHEN ABS(gap_total_sales)>=0.01 THEN pro_rated_source_product_sales + pro_rated_source_shipping_credits + pro_rated_source_giftwrap_credits ELSE source_total_sales END AS pro_rated_total_sales,
pro_rated_total_sales - total_sales AS gap_pro_rated_vs_total_sales
FROM joined_shipments_to_drr_and_deferred_before_unit_fee_solution
)
, JOINED_SHIPMENTS_TO_DRR_DEFERRED_incl_source AS (
select
SHIPMENT_SELLER_ID,
shipment_sku,
shipment_channel,
source,
--CONCAT(YEAR(shipment_date),'_',WEEK(shipment_date)) AS week,
shipment_date,
SUM(quantity) AS shipments_quantity,
sum(product_sales) AS shipments_product_sales,
sum(shipping_credits) AS shipments_shipping_credits,
sum(GIFTWRAP_CREDITS) AS shipments_giftwrap_credits,
sum(promo_rebates) AS shipments_promo_rebates,
--
sum(source_quantity) AS joined_quantity,
SUM(pro_rated_source_product_sales) AS joined_product_sales,
sum(pro_rated_source_shipping_credits) AS joined_shipping_credits,
sum(pro_rated_source_giftwrap_credits) AS joined_giftwrap_credits,
sum(pro_rated_source_promo_rebates) AS joined_promo_rebates,
sum(pro_rated_source_selling_fees) AS joined_selling_fees,
sum(pro_rated_source_fba_fees) AS joined_fba_fees,
sum(pro_rated_source_other_transac) AS joined_other_transac_fees,
sum(pro_rated_source_other_fees) AS joined_other_fees,
--
sum(total_sales) AS shipments_total_sales,
sum(pro_rated_total_sales) AS joined_total_sales,
sum(gap_pro_rated_vs_total_sales) AS gap_total_sales
from joined_shipments_to_drr_and_deferred_with_unit_fee_solution
--where shipment_date BETWEEN '2024-05-26' AND '2024-06-02'
group by all
)
, JOINED_SHIPMENTS_TO_DRR_DEFERRED_excl_source AS (
select
SHIPMENT_SELLER_ID,
shipment_sku,
shipment_channel,
shipment_date,
SUM(quantity) AS shipments_quantity,
sum(product_sales) AS shipments_product_sales,
sum(shipping_credits) AS shipments_shipping_credits,
sum(GIFTWRAP_CREDITS) AS shipments_giftwrap_credits,
sum(promo_rebates) AS shipments_promo_rebates,
--
sum(source_quantity) AS joined_quantity,
SUM(pro_rated_source_product_sales) AS joined_product_sales,
sum(pro_rated_source_shipping_credits) AS joined_shipping_credits,
sum(pro_rated_source_giftwrap_credits) AS joined_giftwrap_credits,
sum(pro_rated_source_promo_rebates) AS joined_promo_rebates,
sum(pro_rated_source_selling_fees) AS joined_selling_fees,
sum(pro_rated_source_fba_fees) AS joined_fba_fees,
sum(pro_rated_source_other_transac) AS joined_other_transac_fees,
sum(pro_rated_source_other_fees) AS joined_other_fees,
--
sum(total_sales) AS shipments_total_sales,
sum(pro_rated_total_sales) AS joined_total_sales,
sum(gap_pro_rated_vs_total_sales) AS gap_total_sales
from joined_shipments_to_drr_and_deferred_with_unit_fee_solution
group by all
)

/* -------------------------------- NON-ORDER DATA ------------------------------------*/
, drr_non_order_data as (
    select
        DISTINCT TO_DATE(DATE_TRUNC('day',REPORT_DATE)) AS report_day,
        seller_id,
        TYPE,
        CASE   
            WHEN SKU IS NOT NULL AND DESCRIPTION LIKE 'FBA Inventory Reimbursement%' THEN 'FBA Inventory Reimbursement'
            WHEN SKU IS NOT NULL AND DESCRIPTION LIKE 'FBA Customer Return Per Unit Fee%' THEN 'FBA Customer Return Per Unit Fee' 
            WHEN SKU IS NOT NULL AND DESCRIPTION LIKE 'Inbound Defect Fee%' THEN 'Inbound Defect Fee - Shipments delivered to the wrong location'
            WHEN DESCRIPTION LIKE 'FBA Inventory Reimbursement - %' THEN 'FBA Inventory Reimbursement'
            WHEN DESCRIPTION LIKE 'FBA Prep Fee%' THEN 'FBA Prep Fee'
            WHEN DESCRIPTION LIKE 'FBA Removal Order%' THEN 'FBA Removal Order'
            WHEN DESCRIPTION LIKE 'AWD %' THEN 'AWD'
            WHEN DESCRIPTION LIKE 'Deals-%' OR DESCRIPTION LIKE 'Lightning Deal-%' OR DESCRIPTION LIKE 'Coupon %' THEN 'Deals / Coupons'
            WHEN DESCRIPTION LIKE 'FBA Customer Returns Fee (Non-Apparel %' THEN 'FBA Customer Returns Fee (Non-Apparel and Non-Shoes)'
            WHEN SKU IS NOT NULL AND DESCRIPTION NOT LIKE 'FBA Inventory Reimbursement%' THEN 'product_description'
            ELSE DESCRIPTION
        END AS simplified_description, 
        SKU,
        CASE
            WHEN SKU IS NOT NULL THEN 'yes'
            ELSE 'no'
        END AS sku_specified,
        CASE
            WHEN marketplace IS NOT NULL THEN LOWER(marketplace)
            ELSE 'null_marketplace'
        END AS revised_marketplace,
        CAST(SUM(QUANTITY) AS float) AS quantity,
        SUM(PRODUCT_SALES) AS product_sales,
        SUM(SHIPPING_CREDITS) AS shipping_credits,
        SUM(GIFT_WRAP_CREDITS) AS giftwrap_credits,
        SUM(PROMOTIONAL_REBATES) AS promotional_rebates,
        SUM(SELLING_FEES) AS selling_fees,
        SUM(FBA_FEES) AS fba_fees,
        SUM(OTHER_TRANSACTION_FEES) AS other_transaction_fees,
        SUM(OTHER) AS other
        --'drr' as source,
    FROM DWH.PROD.FACT_AMAZON_SETTLEMENT_TRANSACTIONS_SCS
    WHERE type NOT IN ('Order','Transfer','Debt') 
    AND COUNTRY_CODE='US' 
    AND TOTAL!=0
    AND report_date::date >= '2024-01-01' AND report_date::date <= CURRENT_DATE -2
    GROUP BY ALL
)
, deferred_non_order_data as (
    select
        DISTINCT TO_DATE(DATE_TRUNC('day',REPORT_DATE)) AS report_day,
        seller_id,
        TYPE,
        CASE   
            WHEN SKU IS NOT NULL AND DESCRIPTION LIKE 'FBA Inventory Reimbursement%' THEN 'FBA Inventory Reimbursement'
            WHEN SKU IS NOT NULL AND DESCRIPTION LIKE 'FBA Customer Return Per Unit Fee%' THEN 'FBA Customer Return Per Unit Fee' 
            WHEN SKU IS NOT NULL AND DESCRIPTION LIKE 'Inbound Defect Fee%' THEN 'Inbound Defect Fee - Shipments delivered to the wrong location'
            WHEN DESCRIPTION LIKE 'FBA Inventory Reimbursement - %' THEN 'FBA Inventory Reimbursement'
            WHEN DESCRIPTION LIKE 'FBA Prep Fee%' THEN 'FBA Prep Fee'
            WHEN DESCRIPTION LIKE 'FBA Removal Order%' THEN 'FBA Removal Order'
            WHEN DESCRIPTION LIKE 'AWD %' THEN 'AWD'
            WHEN DESCRIPTION LIKE 'Deals-%' OR DESCRIPTION LIKE 'Lightning Deal-%' OR DESCRIPTION LIKE 'Coupon %' THEN 'Deals / Coupons'
            WHEN DESCRIPTION LIKE 'FBA Customer Returns Fee (Non-Apparel %' THEN 'FBA Customer Returns Fee (Non-Apparel and Non-Shoes)'
            WHEN SKU IS NOT NULL AND DESCRIPTION NOT LIKE 'FBA Inventory Reimbursement%' THEN 'product_description'
            ELSE DESCRIPTION
        END AS simplified_description, 
        SKU,
        CASE
            WHEN SKU IS NOT NULL THEN 'yes'
            ELSE 'no'
        END AS sku_specified,
        CASE
            WHEN marketplace IS NOT NULL THEN LOWER(marketplace)
            ELSE 'null_marketplace'
        END AS revised_marketplace,
        CAST(SUM(QUANTITY) AS float) AS quantity,
        SUM(PRODUCT_SALES) AS product_sales,
        SUM(SHIPPING_CREDITS) AS shipping_credits,
        SUM(GIFT_WRAP_CREDITS) AS giftwrap_credits,
        SUM(PROMOTIONAL_REBATES) AS promotional_rebates,
        SUM(SELLING_FEES) AS selling_fees,
        SUM(FBA_FEES) AS fba_fees,
        SUM(OTHER_TRANSACTION_FEES) AS other_transaction_fees,
        SUM(OTHER) AS other
        --'deferred' as source,
    FROM dwh.prod.fact_amazon_deferred_transactions
    WHERE type NOT IN ('Order','Transfer','Debt') 
    AND COUNTRY_CODE='US' 
    AND TOTAL!=0
    AND report_requested_time::date = CURRENT_DATE -1
    GROUP BY ALL
)
, union_of_drr_deferred_non_order_data AS (
SELECT *
FROM (
SELECT * from drr_non_order_data
UNION ALL 
SELECT * from deferred_non_order_data
)
GROUP BY ALL
)
, pivoted_union_of_drr_deferred_non_order_data AS (
SELECT * FROM union_of_drr_deferred_non_order_data
UNPIVOT
(AMOUNT FOR category IN (QUANTITY,PRODUCT_SALES,SHIPPING_CREDITS,GIFTWRAP_CREDITS,promotional_rebates,SELLING_FEES,FBA_FEES,other_transaction_fees,OTHER))
WHERE amount!=0
)
, adapted_bi_mapping AS (
SELECT *,
CASE
    WHEN marketplace IS NOT NULL THEN marketplace
    ELSE 'null_marketplace'
END AS revised_marketplace,
sku_specified AS revised_sku_specified
FROM DWH_DEV.STAGING.MERGE_BRAND_EBITDA_FIELD_MAPPING
)
, joined_non_order_drr_deferred_data_to_bi_mapping_day_level AS (
SELECT
a.*,
CASE
    WHEN a.CATEGORY='QUANTITY' THEN 'quantity'
    ELSE b.bi_mapping
END AS BI_mapping,
CASE
    WHEN a.CATEGORY='QUANTITY' THEN 'quantity'
    ELSE b.wbr_total
END AS wbr_total,
CASE
    WHEN a.CATEGORY='QUANTITY' THEN 'quantity'
    ELSE b.IS_WBR_MAIN_SOURCE
END AS IS_WBR_MAIN_SOURCE,
FROM pivoted_union_of_drr_deferred_non_order_data a
LEFT JOIN adapted_bi_mapping b
ON LOWER(a.TYPE) = LOWER(b.TYPE)
AND LOWER(a.simplified_description) = LOWER(b.simplified_description)
AND LOWER(a.category) = LOWER(b.category)
AND LOWER(a.sku_specified) = LOWER(b.revised_sku_specified)
AND LOWER(a.revised_marketplace) = LOWER(b.revised_marketplace)
)

, non_order_refund_quantity AS (
SELECT
report_day,
seller_id,
SKU,
--revised_marketplace, WE DON'T CONSIDER MARKETPLACE HERE AS IT IS A MIX OF AMAZON.COM AND NULL WHICH WOULD CAUSE DUPLICATES IN THE JOINS BELOW
SUM(CASE WHEN CATEGORY ='QUANTITY' THEN AMOUNT ELSE 0 END) AS amount
FROM joined_non_order_drr_deferred_data_to_bi_mapping_day_level
WHERE type ='Refund'
GROUP BY ALL
)
, non_order_refund_net_rev AS (
SELECT
report_day,
seller_id,
SKU,
--revised_marketplace, WE DON'T CONSIDER MARKETPLACE HERE AS IT IS A MIX OF AMAZON.COM AND NULL WHICH WOULD CAUSE DUPLICATES IN THE JOINS BELOW
SUM(CASE WHEN CATEGORY !='QUANTITY' THEN AMOUNT ELSE 0 END) AS amount
FROM joined_non_order_drr_deferred_data_to_bi_mapping_day_level
WHERE bi_mapping LIKE 'revenue%refund'
AND IS_WBR_MAIN_SOURCE LIKE '%yes%'
GROUP BY ALL
)
, non_order_liquidation_net_rev AS (
SELECT
report_day,
seller_id,
SKU,
--revised_marketplace, WE DON'T CONSIDER MARKETPLACE HERE AS IT IS A MIX OF AMAZON.COM AND NULL WHICH WOULD CAUSE DUPLICATES IN THE JOINS BELOW
SUM(AMOUNT) AS amount
FROM joined_non_order_drr_deferred_data_to_bi_mapping_day_level
WHERE bi_mapping LIKE 'revenue%liquidation'
AND IS_WBR_MAIN_SOURCE LIKE '%yes%'
GROUP BY ALL
)
, non_order_fulfillment_fees_sku_allocated_non_returns AS (
SELECT
report_day,
seller_id,
SKU,
--revised_marketplace, WE DON'T CONSIDER MARKETPLACE HERE AS IT IS A MIX OF AMAZON.COM AND NULL WHICH WOULD CAUSE DUPLICATES IN THE JOINS BELOW
SUM(AMOUNT) AS amount
FROM joined_non_order_drr_deferred_data_to_bi_mapping_day_level
WHERE type!='Refund'
AND bi_mapping LIKE 'fba%'
AND IS_WBR_MAIN_SOURCE LIKE '%yes%'
AND SKU is not null
GROUP BY ALL
)
, non_order_fulfillment_fees_sku_allocated_returns AS (
SELECT
report_day,
seller_id,
SKU,
--revised_marketplace, WE DON'T CONSIDER MARKETPLACE HERE AS IT IS A MIX OF AMAZON.COM AND NULL WHICH WOULD CAUSE DUPLICATES IN THE JOINS BELOW
SUM(AMOUNT) AS amount
FROM joined_non_order_drr_deferred_data_to_bi_mapping_day_level
WHERE type = 'Refund'
AND bi_mapping LIKE 'fba%'
AND IS_WBR_MAIN_SOURCE LIKE '%yes%'
AND SKU is not null
GROUP BY ALL
)
, non_order_fulfillment_fees_non_sku_allocated AS (
SELECT
report_day,
seller_id,
--SKU,
--revised_marketplace, WE DON'T CONSIDER MARKETPLACE HERE AS IT IS A MIX OF AMAZON.COM AND NULL WHICH WOULD CAUSE DUPLICATES IN THE JOINS BELOW
SUM(AMOUNT) AS amount
FROM joined_non_order_drr_deferred_data_to_bi_mapping_day_level
WHERE bi_mapping LIKE 'fba%'
AND IS_WBR_MAIN_SOURCE LIKE '%yes%'
AND SKU is null
GROUP BY ALL
)
, non_order_selling_fees_sku_allocated_non_returns AS (
SELECT
report_day,
seller_id,
SKU,
--revised_marketplace, WE DON'T CONSIDER MARKETPLACE HERE AS IT IS A MIX OF AMAZON.COM AND NULL WHICH WOULD CAUSE DUPLICATES IN THE JOINS BELOW
SUM(AMOUNT) AS amount
FROM joined_non_order_drr_deferred_data_to_bi_mapping_day_level
WHERE type!='Refund'
AND bi_mapping LIKE 'commission%'
AND IS_WBR_MAIN_SOURCE LIKE '%yes%'
AND SKU is not null
GROUP BY ALL
)
, non_order_selling_fees_sku_allocated_returns AS (
SELECT
report_day,
seller_id,
SKU,
--revised_marketplace, WE DON'T CONSIDER MARKETPLACE HERE AS IT IS A MIX OF AMAZON.COM AND NULL WHICH WOULD CAUSE DUPLICATES IN THE JOINS BELOW
SUM(AMOUNT) AS amount
FROM joined_non_order_drr_deferred_data_to_bi_mapping_day_level
WHERE type = 'Refund'
AND bi_mapping LIKE 'commission%'
AND IS_WBR_MAIN_SOURCE LIKE '%yes%'
AND SKU is not null
GROUP BY ALL
)
, non_order_selling_fees_non_sku_allocated_excl_mbs AS (
SELECT
report_day,
seller_id,
--SKU,
--revised_marketplace, WE DON'T CONSIDER MARKETPLACE HERE AS IT IS A MIX OF AMAZON.COM AND NULL WHICH WOULD CAUSE DUPLICATES IN THE JOINS BELOW
SUM(AMOUNT) AS amount
FROM joined_non_order_drr_deferred_data_to_bi_mapping_day_level
WHERE bi_mapping LIKE 'commission%'
AND IS_WBR_MAIN_SOURCE LIKE '%yes%'
AND SKU is null
AND LOWER(simplified_description) NOT LIKE 'premium services fee'
GROUP BY ALL
)
, non_order_cogs_sku_allocated_non_returns AS (
SELECT
report_day,
seller_id,
SKU,
--revised_marketplace, WE DON'T CONSIDER MARKETPLACE HERE AS IT IS A MIX OF AMAZON.COM AND NULL WHICH WOULD CAUSE DUPLICATES IN THE JOINS BELOW
SUM(AMOUNT) AS amount
FROM joined_non_order_drr_deferred_data_to_bi_mapping_day_level
WHERE type!='Refund'
AND bi_mapping LIKE 'cogs%'
AND IS_WBR_MAIN_SOURCE LIKE '%yes%'
AND SKU is not null
GROUP BY ALL
)
, non_order_cogs_non_sku_allocated AS (
SELECT
report_day,
seller_id,
--SKU,
--revised_marketplace, WE DON'T CONSIDER MARKETPLACE HERE AS IT IS A MIX OF AMAZON.COM AND NULL WHICH WOULD CAUSE DUPLICATES IN THE JOINS BELOW
SUM(AMOUNT) AS amount
FROM joined_non_order_drr_deferred_data_to_bi_mapping_day_level
WHERE bi_mapping LIKE 'cogs%'
AND IS_WBR_MAIN_SOURCE LIKE '%yes%'
AND SKU is null
GROUP BY ALL
)
, sku_account_contrib AS (
SELECT
shipment_date,
seller_id,
sku,
sku_account_contrib
from DWH_DEV.STAGING.stg_brandebitda_shipment_sku_revenue_contribution
where country_code ='US' and shipment_date>= '2024-01-01'
group by all
)
, unique_combinations_of_day_sku_account_from_shipments_and_drr_deferred AS (
SELECT
DISTINCT SKU,
SELLER_ID,
report_day,
FROM (
SELECT
DISTINCT SKU,
SELLER_ID,
report_day
FROM joined_non_order_drr_deferred_data_to_bi_mapping_day_level
UNION
SELECT
DISTINCT shipment_sku,
SHIPMENT_SELLER_ID,
shipment_date
FROM JOINED_SHIPMENTS_TO_DRR_DEFERRED_incl_source
)
)
, amazon_data_day_sku_account_level AS (
SELECT
a.REPORT_DAY,
a.SELLER_ID,
a.SKU,
ZEROIFNULL(contrib.sku_account_contrib) AS sku_account_contrib,
ZEROIFNULL(b.shipments_quantity) AS quantity_from_orders,
ZEROIFNULL(c.amount) AS quantity_from_returns,
quantity_from_orders - quantity_from_returns AS net_quantity,
-- NET REVENUE
ZEROIFNULL(b.shipments_product_sales) AS product_sales_from_orders,
ZEROIFNULL(b.shipments_shipping_credits) AS shipping_credits_from_orders,
ZEROIFNULL(b.shipments_giftwrap_credits) AS giftwrap_credits_from_orders,
ZEROIFNULL(b.shipments_promo_rebates) AS promo_rebates_from_orders,
ZEROIFNULL(d.amount) AS net_rev_from_returns,
ZEROIFNULL(e.amount) AS net_rev_from_liquidations,
product_sales_from_orders + shipping_credits_from_orders + giftwrap_credits_from_orders + promo_rebates_from_orders + net_rev_from_returns + net_rev_from_liquidations AS net_revenue,
-- FULFILLMENT
-ZEROIFNULL(b.joined_fba_fees) AS fulfillment_fees_from_orders,
-ZEROIFNULL(f.amount) AS fulfillment_fees_from_non_order_sku_allocated_non_returns,
-ZEROIFNULL(g.amount) AS fulfillment_fees_from_non_order_sku_allocated_returns,
-ZEROIFNULL(h.amount) AS seller_account_fulfillment_fees_from_non_order_non_sku_allocated,
ZEROIFNULL(seller_account_fulfillment_fees_from_non_order_non_sku_allocated * sku_account_contrib) AS fulfillment_fees_from_non_order_non_sku_allocated,
fulfillment_fees_from_orders 
    + fulfillment_fees_from_non_order_sku_allocated_non_returns 
    + fulfillment_fees_from_non_order_sku_allocated_returns 
    + fulfillment_fees_from_non_order_non_sku_allocated AS total_fulfillment_fees,
-- SELLING FEES
-ZEROIFNULL(b.joined_selling_fees) AS selling_fees_from_orders,
-ZEROIFNULL(i.amount) AS selling_fees_from_non_order_sku_allocated_non_returns,
-ZEROIFNULL(j.amount) AS selling_fees_from_non_order_sku_allocated_returns,
-ZEROIFNULL(k.amount) AS seller_account_selling_fees_from_non_order_non_sku_allocated_excl_mbs,
ZEROIFNULL(seller_account_selling_fees_from_non_order_non_sku_allocated_excl_mbs * sku_account_contrib) AS selling_fees_from_non_order_non_sku_allocated_excl_mbs,
-- COGS
-ZEROIFNULL(l.amount) AS cogs_from_non_order_sku_allocated_non_returns,
-ZEROIFNULL(m.amount) AS seller_account_cogs_from_non_order_non_sku_allocated,
ZEROIFNULL(seller_account_cogs_from_non_order_non_sku_allocated * sku_account_contrib) AS cogs_from_non_order_non_sku_allocated
FROM unique_combinations_of_day_sku_account_from_shipments_and_drr_deferred a
LEFT JOIN sku_account_contrib contrib
ON a.report_day = contrib.shipment_date
AND a.seller_id = contrib.seller_id
AND a.sku = contrib.sku
LEFT JOIN JOINED_SHIPMENTS_TO_DRR_DEFERRED_excl_source b
ON a.report_day = b.shipment_date
AND a.seller_id = b.shipment_seller_id
AND a.sku = b.shipment_sku
LEFT JOIN non_order_refund_quantity c
ON a.report_day = c.report_day
AND a.seller_id = c.seller_id
AND a.sku = c.sku
LEFT JOIN non_order_refund_net_rev d
ON a.report_day = d.report_day
AND a.seller_id = d.seller_id
AND a.sku = d.sku
LEFT JOIN non_order_liquidation_net_rev e
ON a.report_day = e.report_day
AND a.seller_id = e.seller_id
AND a.sku = e.sku
LEFT JOIN non_order_fulfillment_fees_sku_allocated_non_returns f
ON a.report_day = f.report_day
AND a.seller_id = f.seller_id
AND a.sku = f.sku
LEFT JOIN non_order_fulfillment_fees_sku_allocated_returns g
ON a.report_day = g.report_day
AND a.seller_id = g.seller_id
AND a.sku = g.sku
LEFT JOIN non_order_fulfillment_fees_non_sku_allocated h
ON a.report_day = h.report_day
AND a.seller_id = h.seller_id
--AND a.sku = h.sku
LEFT JOIN non_order_selling_fees_sku_allocated_non_returns i
ON a.report_day = i.report_day
AND a.seller_id = i.seller_id
AND a.sku = i.sku
LEFT JOIN non_order_selling_fees_sku_allocated_returns j
ON a.report_day = j.report_day
AND a.seller_id = j.seller_id
AND a.sku = j.sku
LEFT JOIN non_order_selling_fees_non_sku_allocated_excl_mbs k
ON a.report_day = k.report_day
AND a.seller_id = k.seller_id
--AND a.sku = h.sku
LEFT JOIN non_order_cogs_sku_allocated_non_returns l
ON a.report_day = l.report_day
AND a.seller_id = l.seller_id
AND a.sku = l.sku
LEFT JOIN non_order_cogs_non_sku_allocated m
ON a.report_day = m.report_day
AND a.seller_id = m.seller_id



)
, refined_sku_asin_mapping_table AS (
SELECT
*,
CASE
    WHEN SKU IS NOT NULL AND BRAND_CODE IS NULL AND LOWER(PRODUCT_NAME) LIKE '%viking%' THEN 'VIK'
    WHEN SKU IS NOT NULL AND BRAND_CODE IS NULL AND LOWER(PRODUCT_NAME) LIKE 'rockin%' THEN 'RGR'
    WHEN SKU IS NOT NULL AND BRAND_CODE IS NULL AND LOWER(PRODUCT_NAME) LIKE '%puracy%' THEN 'PUR'
    WHEN SKU IS NOT NULL AND BRAND_CODE IS NULL AND LOWER(PRODUCT_NAME) LIKE '%ototo%' THEN 'OTO'
    WHEN SKU IS NOT NULL AND BRAND_CODE IS NULL AND LOWER(PRODUCT_NAME) LIKE '%key nutrients%' THEN 'KNT'
    WHEN SKU IS NOT NULL AND BRAND_CODE IS NULL AND LOWER(PRODUCT_NAME) LIKE '%fullstar%' THEN 'FUL'
    ELSE BRAND_CODE
END AS refined_brand_code
FROM DWH_DEV.STAGING.stg_brand_ebitda_sku_asin_map_orders_source_table
)
, distinct_sku_to_asin_mapping AS (
SELECT DISTINCT sku,
asin,
refined_brand_code,
FROM refined_sku_asin_mapping_table
where country_code='US'
-- !!! MAKE SURE NO MULTIPLE ASIN ARE MAPPED TO A SINGLE SKU AS IT WOULD CREATE DUPLICATE IN THE JOINS BELOW !!!
)
, distinct_asin_to_brand_mapping AS (
SELECT DISTINCT asin,
refined_brand_code,
FROM refined_sku_asin_mapping_table
where country_code='US'
)
, amazon_data_grouped_at_day_sku_level_and_mapped_to_asin_and_brand_step_one AS (
SELECT
a.REPORT_DAY,
IFNULL(a.SKU,'no_sku') AS SKU,
CASE
    WHEN a.SKU LIKE 'B0________' THEN a.SKU
    ELSE IFNULL(b.ASIN,'no_asin')
END AS mapped_asin,
IFNULL(b.refined_brand_code,'no_brand') as brand_one,
SUM(QUANTITY_FROM_ORDERS) as QUANTITY_FROM_ORDERS,
SUM(QUANTITY_FROM_RETURNS)as QUANTITY_FROM_RETURNS,
SUM(NET_QUANTITY) as NET_QUANTITY,
SUm(PRODUCT_SALES_FROM_ORDERS) as PRODUCT_SALES_FROM_ORDERS,
SUm(SHIPPING_CREDITS_FROM_ORDERS) as SHIPPING_CREDITS_FROM_ORDERS,
SUm(GIFTWRAP_CREDITS_FROM_ORDERS) as GIFTWRAP_CREDITS_FROM_ORDERS,
SUm(PROMO_REBATES_FROM_ORDERS) as PROMO_REBATES_FROM_ORDERS,
SUM(NET_REV_FROM_RETURNS) as NET_REV_FROM_RETURNS,
SUM(NET_REV_FROM_LIQUIDATIONS) as NET_REV_FROM_LIQUIDATIONS,
SUM(NET_REVENUE) as NET_REVENUE,
SUM(FULFILLMENT_FEES_FROM_ORDERS) as FULFILLMENT_FEES_FROM_ORDERS,
SUM(FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS) as FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
SUM(FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS) as FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS,
SUm(FULFILLMENT_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED) as FULFILLMENT_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED,
SUm(TOTAL_FULFILLMENT_FEES) as TOTAL_FULFILLMENT_FEES,
SUM(SELLING_FEES_FROM_ORDERS) as SELLING_FEES_FROM_ORDERS,
SUM(SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS) as SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
SUM(SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS) as SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS,
SUm(SELLING_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED_EXCL_MBS) as SELLING_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED_EXCL_MBS,
SUM(COGS_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS) as COGS_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
SUm(COGS_FROM_NON_ORDER_NON_SKU_ALLOCATED) as COGS_FROM_NON_ORDER_NON_SKU_ALLOCATED,
FROM amazon_data_day_sku_account_level a
LEFT JOIN distinct_sku_to_asin_mapping b
ON a.sku = b.sku
GROUP BY ALL
)
, amazon_data_grouped_at_day_sku_level_and_mapped_to_asin_and_brand_step_two AS (
SELECT
a.*,
IFNULL(b.refined_brand_code,'no_brand') AS brand_two
FROM amazon_data_grouped_at_day_sku_level_and_mapped_to_asin_and_brand_step_one a
LEFT JOIN distinct_asin_to_brand_mapping b
ON a.SKU = b.ASIN
)
, amazon_data_grouped_at_day_sku_level_and_mapped_to_asin_and_brand_final AS (
SELECT
REPORT_DAY,
SKU,
mapped_asin,
CASE
    WHEN BRAND_ONE !='no_brand' THEN BRAND_ONE
    WHEN BRAND_TWO !='no_brand' THEN BRAND_TWO
ELSE 'no_brand'
END AS mapped_brand_code,
QUANTITY_FROM_ORDERS,
QUANTITY_FROM_RETURNS,
NET_QUANTITY,
PRODUCT_SALES_FROM_ORDERS,
SHIPPING_CREDITS_FROM_ORDERS,
GIFTWRAP_CREDITS_FROM_ORDERS,
PROMO_REBATES_FROM_ORDERS,
NET_REV_FROM_RETURNS,
NET_REV_FROM_LIQUIDATIONS,
NET_REVENUE,
FULFILLMENT_FEES_FROM_ORDERS,
FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS,
FULFILLMENT_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED,
TOTAL_FULFILLMENT_FEES,
SELLING_FEES_FROM_ORDERS,
SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS,
SELLING_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED_EXCL_MBS,
COGS_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
COGS_FROM_NON_ORDER_NON_SKU_ALLOCATED,
FROM amazon_data_grouped_at_day_sku_level_and_mapped_to_asin_and_brand_step_two
)
-- , latest_unit_cogs_from_airbyte AS (
-- SELECT
-- ASIN,
-- MAX_BY(UNIT_RATE, MONTH) AS latest_cogs,
-- MAX_BY(MONTH, MONTH) AS latest_month
-- FROM DWH_DEV.STAGING.MERGE_BRAND_EBITDA_COGS
-- WHERE GEOGRAPHY ='US'
-- GROUP BY ALL
-- )
-- , amazon_data_at_day_sku_asin_brand_level_joined_to_ns_cogs AS (
-- SELECT
-- a.*,
-- CASE
--     WHEN QUANTITY_FROM_ORDERS=0 OR (QUANTITY_FROM_ORDERS!=0 AND (b.latest_cogs IS NULL OR b.latest_cogs=0)) THEN NULL
--     ELSE b.latest_cogs
-- END AS latest_cogs_from_ns,
-- CASE
--     WHEN QUANTITY_FROM_ORDERS=0 OR (QUANTITY_FROM_ORDERS!=0 AND (b.latest_cogs IS NULL OR b.latest_cogs=0)) THEN NULL
--     ELSE b.latest_month
-- END AS month_of_latest_cogs_from_ns,
-- AVG(LATEST_COGS_FROM_NS) OVER (PARTITION BY a.REPORT_DAY,MAPPED_BRAND_CODE) AS avg_brand_day_ns_cogs,
-- CASE
--     WHEN latest_cogs_from_ns IS NOT NULL THEN IFNULL(latest_cogs_from_ns * QUANTITY_FROM_ORDERS,0)
--     ELSE IFNULL(avg_brand_day_ns_cogs * QUANTITY_FROM_ORDERS,0)
-- END AS calculated_cogs,
-- CASE
--     WHEN QUANTITY_FROM_RETURNS=0 THEN 0
--     WHEN QUANTITY_FROM_RETURNS !=0 AND latest_cogs_from_ns IS NOT NULL THEN QUANTITY_FROM_RETURNS * latest_cogs_from_ns * 0.7
--     ELSE QUANTITY_FROM_RETURNS * avg_brand_day_ns_cogs * 0.7
-- END AS cogs_from_unsellable_returns,
-- CASE
--     WHEN QUANTITY_FROM_RETURNS=0 THEN 0
--     WHEN QUANTITY_FROM_RETURNS !=0 AND latest_cogs_from_ns IS NOT NULL THEN (QUANTITY_FROM_RETURNS * latest_cogs_from_ns * -0.3)
--     ELSE QUANTITY_FROM_RETURNS * avg_brand_day_ns_cogs * -0.3
-- END AS cogs_refund_from_sellable_returns,
-- FROM amazon_data_grouped_at_day_sku_level_and_mapped_to_asin_and_brand_final a
-- LEFT JOIN latest_unit_cogs_from_airbyte b
-- ON mapped_asin = b.ASIN
-- )


/* ─────────── 1. nearest-PAST COGS ─────────── */
, past_cogs AS (
    SELECT
        a.report_day,
        a.mapped_asin               AS asin,
        c.UNIT_COGS_EXC_SERVICE_FEE_USD AS unit_cogs_excl_service_fees,
        c.UNIT_SERVICE_FEE_USD AS unit_service_fees,
        c.TOTAL_UNIT_COGS_USD  AS total_unit_cogs,
        ROW_NUMBER() OVER (
            PARTITION BY a.report_day, a.mapped_asin
            ORDER BY c.report_date DESC         -- latest ≤ report_day
        ) AS rn
    FROM amazon_data_grouped_at_day_sku_level_and_mapped_to_asin_and_brand_final a
   -- LEFT JOIN DWH_DEV.STAGING.MERGE_BRAND_EBITDA_COGS c
    LEFT JOIN DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_COGS c
           ON c.asin      = a.mapped_asin
          AND c.country_code = 'US'
          AND c.report_date  <= a.report_day

QUALIFY rn = 1
)

/* ─────────── 2. nearest-FUTURE COGS (only if no past) ─────────── */
, future_cogs AS (
    SELECT
        a.report_day,
        a.mapped_asin               AS asin,
        c.UNIT_COGS_EXC_SERVICE_FEE_USD AS unit_cogs_excl_service_fees,
        c.UNIT_SERVICE_FEE_USD AS unit_service_fees,
        c.TOTAL_UNIT_COGS_USD  AS total_unit_cogs,
        ROW_NUMBER() OVER (
            PARTITION BY a.report_day, a.mapped_asin
            ORDER BY c.report_date ASC          -- earliest > report_day
        ) AS rn
    FROM amazon_data_grouped_at_day_sku_level_and_mapped_to_asin_and_brand_final a
    LEFT JOIN DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_COGS c
           ON c.asin      = a.mapped_asin
          AND c.country_code = 'US'
          AND c.report_date     >  a.report_day

QUALIFY rn = 1
)

/* ─────────── 3. final choice ─────────── */
, resolved_cogs AS (
    SELECT
        COALESCE(p.report_day , f.report_day ) AS report_day,
        COALESCE(p.asin       , f.asin       ) AS asin,
        COALESCE(p.total_unit_cogs , f.total_unit_cogs  ) AS chosen_cogs,
        COALESCE(p.unit_cogs_excl_service_fees, f.unit_cogs_excl_service_fees) AS unit_cogs_excl_service_fees,
        COALESCE(p.unit_service_fees, f.unit_service_fees) AS unit_service_fees
    FROM past_cogs  p
    FULL OUTER JOIN future_cogs f
           ON f.report_day = p.report_day
          AND f.asin       = p.asin
)

, amazon_data_at_day_sku_asin_brand_level_joined_to_ns_cogs AS (

SELECT
    a.*,

    /*COGS captured directly from NetSuite mapping */
    rc.chosen_cogs   AS latest_cogs_from_ns,
    rc.unit_cogs_excl_service_fees AS unit_cogs_excl_service_fees, 
    rc.unit_service_fees AS unit_service_fees,
   -- rc.chosen_month  AS month_of_latest_cogs_from_ns,

    /* Brand-day COGS-to-revenue ratio (%).                          *
       • Includes only rows that *do* have chosen_cogs.                   *
       • NULLIF avoids divide-by-zero.                                    */

    SUM(COALESCE(rc.chosen_cogs * a.quantity_from_orders, 0)) 
        OVER (PARTITION BY a.report_day, a.mapped_brand_code)      /
    NULLIF(
        SUM(a.net_revenue) 
            OVER (PARTITION BY a.report_day, a.mapped_brand_code)
    ,0)
        AS avg_brand_day_ns_cogs,            

    SUM(COALESCE(rc.unit_cogs_excl_service_fees * a.quantity_from_orders, 0)) 
        OVER (PARTITION BY a.report_day, a.mapped_brand_code)      /
    NULLIF(
        SUM(a.net_revenue) 
            OVER (PARTITION BY a.report_day, a.mapped_brand_code)
    ,0)
        AS avg_brand_day_cogs_excl_service_fees,
    
    SUM(COALESCE(rc.unit_service_fees * a.quantity_from_orders, 0)) 
        OVER (PARTITION BY a.report_day, a.mapped_brand_code)      /
    NULLIF(
        SUM(a.net_revenue) 
            OVER (PARTITION BY a.report_day, a.mapped_brand_code)
    ,0)
        AS avg_brand_day_cogs_service_fees,

     


    /* Final COGS for ordered units.                                  *
       • Use unit COGS when present, else brand % × row revenue.           */

    CASE
        WHEN rc.chosen_cogs IS NOT NULL
             THEN rc.chosen_cogs * a.quantity_from_orders
        ELSE avg_brand_day_ns_cogs * a.net_revenue
    END AS calculated_cogs,

    CASE
        WHEN rc.unit_cogs_excl_service_fees IS NOT NULL
             THEN rc.unit_cogs_excl_service_fees * a.quantity_from_orders
        ELSE avg_brand_day_cogs_excl_service_fees * a.net_revenue
    END AS cogs_excl_service_fees,

    CASE
        WHEN rc.unit_service_fees IS NOT NULL
             THEN rc.unit_service_fees * a.quantity_from_orders
        ELSE avg_brand_day_cogs_service_fees * a.net_revenue
    END AS cogs_service_fees,

    /* Unsellable returns @ 70 % write-off.                           */
    CASE
        WHEN a.quantity_from_returns = 0 THEN 0
        WHEN QUANTITY_FROM_RETURNS !=0 AND latest_cogs_from_ns IS NOT NULL THEN (QUANTITY_FROM_RETURNS * latest_cogs_from_ns * 0.7)
        ELSE avg_brand_day_ns_cogs * a.quantity_from_returns * 0.7
    END AS cogs_from_unsellable_returns,

    /* Sellable returns refund @ -30 %.                               */
    CASE
        WHEN a.quantity_from_returns = 0 THEN 0
        WHEN QUANTITY_FROM_RETURNS !=0 AND latest_cogs_from_ns IS NOT NULL THEN (QUANTITY_FROM_RETURNS * latest_cogs_from_ns * -0.3)
        ELSE avg_brand_day_ns_cogs * a.quantity_from_returns * -0.3

    END AS cogs_refund_from_sellable_returns

FROM amazon_data_grouped_at_day_sku_level_and_mapped_to_asin_and_brand_final a
LEFT JOIN resolved_cogs rc
       ON rc.report_day = a.report_day
      AND rc.asin       = a.mapped_asin
)


/*--------------------------- INSERT AND CALCULATION OF FEES BILLED ONCE A MONTH (MBS, LONG-TERM STORAGE, VINE) AND OF MONTHLY PLUGS -----------------*/
, monthly_billed_mbs_lts_vine AS (
SELECT
YEAR(REPORT_DAY) AS report_year,
MONTH(REPORT_DAY) AS report_month,
NULLIFZERO(SUM(CASE WHEN LOWER(simplified_description) LIKE '%premium services fee%' THEN selling_fees ELSE 0 END)) AS billed_mbs,
NULLIFZERO(SUM(CASE WHEN LOWER(simplified_description) LIKE '%long-term storage%' THEN other ELSE 0 END)) AS billed_lts,
NULLIFZERO(SUM(CASE WHEN LOWER(simplified_description) LIKE '%vine enrollment fee%' THEN selling_fees ELSE 0 END)
    + SUM(CASE WHEN LOWER(simplified_description) LIKE '%vine enrollment fee%' THEN other ELSE 0 END)) AS billed_vine,
LAST_VALUE(billed_mbs) IGNORE NULLS OVER ( ORDER BY report_year ASC, report_month ASC) AS latest_billed_mbs,
LAST_VALUE(billed_lts) IGNORE NULLS OVER ( ORDER BY report_year ASC, report_month ASC) AS latest_billed_lts,
LAST_VALUE(billed_vine) IGNORE NULLS OVER ( ORDER BY report_year ASC, report_month ASC) AS latest_billed_vine
FROM union_of_drr_deferred_non_order_data
GROUP BY ALL
)
, amazon_data_at_day_sku_asin_brand_level_joined_to_ns_cogs_adding_monthly_fees_and_plugs AS (
SELECT
a.*,
b.billed_mbs AS month_bill_mbs,
b.billed_lts AS month_bill_lts,
b.billed_vine AS month_bill_vine,
b.latest_billed_mbs,
b.latest_billed_lts,
b.latest_billed_vine,
40000 AS monthly_tooling_plug,
50000 AS monthly_selling_fees_plug,
CASE
    WHEN REPORT_DAY >='2025-02-01' AND mapped_brand_code ='BOK' THEN 100000 
    ELSE 0
END AS monthly_boka_airfreight_plug,

-- TIME AND SKU ALLOCATIOn OF MBS / LTS / VINE / PLUGS
ZEROIFNULL(DIV0(net_revenue, SUM(net_revenue) OVER (PARTITION BY REPORT_DAY))) AS sku_day_revenue_contrib,
ZEROIFNULL(DIV0(net_revenue, SUM(net_revenue) OVER (PARTITION BY REPORT_DAY,mapped_brand_code))) AS sku_day_brand_revenue_contrib,
DATEDIFF('day', DATE_FROM_PARTS(YEAR(REPORT_DAY),MONTH(REPORT_DAY),1), DATE_FROM_PARTS(YEAR(REPORT_DAY),MONTH(REPORT_DAY)+1,1)) AS number_days_in_month,

monthly_tooling_plug / number_days_in_month * sku_day_revenue_contrib AS allocated_monthly_tooling_plug,
monthly_selling_fees_plug / number_days_in_month * sku_day_revenue_contrib AS allocated_monthly_selling_fees_plug,
monthly_boka_airfreight_plug / number_days_in_month * sku_day_brand_revenue_contrib AS allocated_monthly_boka_airfreight_plug,
CASE
    WHEN month_bill_mbs IS NOT NULL THEN - month_bill_mbs / number_days_in_month * sku_day_revenue_contrib
    ELSE - b.latest_billed_mbs / number_days_in_month * sku_day_revenue_contrib
END AS allocated_mbs,
CASE
    WHEN month_bill_lts IS NOT NULL THEN - month_bill_lts / number_days_in_month * sku_day_revenue_contrib
    ELSE - b.latest_billed_lts / number_days_in_month * sku_day_revenue_contrib
END AS allocated_lts,
CASE
    WHEN month_bill_vine IS NOT NULL THEN - month_bill_vine / number_days_in_month * sku_day_revenue_contrib
    ELSE - b.latest_billed_vine / number_days_in_month * sku_day_revenue_contrib
END AS allocated_vine
FROM amazon_data_at_day_sku_asin_brand_level_joined_to_ns_cogs a
LEFT JOIN monthly_billed_mbs_lts_vine b
ON YEAR(REPORT_DAY) = b.report_year
AND MONTH(REPORT_DAY) = b.report_month
--WHERE report_day >='2025-04-01'
--ORDER BY 1 DESC
)

SELECT
REPORT_DAY,
--CONCAT(YEAR(DATE_TRUNC('day',REPORT_DAY)),'_',month(DATE_TRUNC('day',REPORT_DAY))) AS year_month,
SKU,
MAPPED_ASIN,
MAPPED_BRAND_CODE,
SUM(QUANTITY_FROM_ORDERS) AS QUANTITY_FROM_ORDERS,
SUM(QUANTITY_FROM_RETURNS) AS QUANTITY_FROM_RETURNS,
SUM(NET_QUANTITY) AS NET_QUANTITY,
SUM(PRODUCT_SALES_FROM_ORDERS) AS PRODUCT_SALES_FROM_ORDERS,
SUM(SHIPPING_CREDITS_FROM_ORDERS) AS SHIPPING_CREDITS_FROM_ORDERS,
SUM(GIFTWRAP_CREDITS_FROM_ORDERS) AS GIFTWRAP_CREDITS_FROM_ORDERS,
SUM(PROMO_REBATES_FROM_ORDERS) AS PROMO_REBATES_FROM_ORDERS,
SUM(NET_REV_FROM_RETURNS) AS NET_REV_FROM_RETURNS,
SUM(NET_REV_FROM_LIQUIDATIONS) AS NET_REV_FROM_LIQUIDATIONS,
SUM(NET_REVENUE) AS NET_REVENUE,
SUM(FULFILLMENT_FEES_FROM_ORDERS) AS FULFILLMENT_FEES_FROM_ORDERS,
SUM(FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS) AS FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
SUM(FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS) AS FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS,
SUM(FULFILLMENT_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED) AS FULFILLMENT_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED,
SUM(TOTAL_FULFILLMENT_FEES) AS TOTAL_FULFILLMENT_FEES,
SUM(SELLING_FEES_FROM_ORDERS) AS SELLING_FEES_FROM_ORDERS,
SUM(SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS) AS SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
SUM(SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS) AS SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS,
SUM(SELLING_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED_EXCL_MBS) AS SELLING_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED_EXCL_MBS,
SUM(allocated_mbs) AS ALLOCATED_MBS,
SUM(allocated_monthly_selling_fees_plug) AS ALLOCATED_MONTHLY_SELLING_FEES_PLUG,
SUM(SELLING_FEES_FROM_ORDERS)
    + SUM(SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS)
    + SUM(SELLING_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS)
    + SUM(SELLING_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED_EXCL_MBS)
    + SUM(ALLOCATED_MBS)
    + SUM(ALLOCATED_MONTHLY_SELLING_FEES_PLUG) AS TOTAL_SELLING_FEES,
    
MAX(latest_cogs_from_ns) AS MAX_LATEST_NS_COGS,
MAX(unit_cogs_excl_service_fees) AS max_unit_cogs_excl_service_fees, 
MAX(unit_service_fees) AS max_unit_service_fees,
--MAX(month_of_latest_cogs_from_ns) AS MAX_MONTH_LATEST_NS_COGS,
MAX(avg_brand_day_ns_cogs) AS MAX_AVG_BRAND_DAY_NS_COGS,
SUM(CALCULATED_COGS) AS CALCULATED_COGS_NS,
SUM(cogs_excl_service_fees) AS COGS_EXCL_SERVICE_FEES, 
SUM(cogs_service_fees) AS COGS_SERVICE_FEES,
SUM(allocated_monthly_tooling_plug) AS TOOLING_PLUG,
SUM(cogs_from_unsellable_returns) AS COGS_FROM_UNSELLABLE_RETURNS,
SUM(cogs_refund_from_sellable_returns) AS COGS_REFUNDS_FROM_SELLABLE_RETURNS,
SUM(allocated_monthly_boka_airfreight_plug) AS BOKA_MONTHLY_AIR_FREIGHT_PLUG,
SUM(COGS_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS) AS COGS_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS,
SUM(COGS_FROM_NON_ORDER_NON_SKU_ALLOCATED) AS COGS_FROM_NON_ORDER_NON_SKU_ALLOCATED,
(COALESCE(SUM(CALCULATED_COGS),0)
    + COALESCE(SUM(allocated_monthly_tooling_plug),0)
    + COALESCE(SUM(cogs_from_unsellable_returns),0)
    + COALESCE(SUM(cogs_refund_from_sellable_returns),0)
    + COALESCE(SUM(allocated_monthly_boka_airfreight_plug),0)
    + COALESCE(SUM(COGS_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS),0)
    + COALESCE(SUM(COGS_FROM_NON_ORDER_NON_SKU_ALLOCATED),0)) AS TOTAL_COGS_EXCL_THREE_PL_FREIGHT,
SUM(allocated_lts) AS allocated_long_term_storage_fee,
SUM(allocated_vine) AS allocated_vine
FROM amazon_data_at_day_sku_asin_brand_level_joined_to_ns_cogs_adding_monthly_fees_and_plugs 
--WHERE YEAR_WEEK='2025_22'
WHERE REPORT_DAY>='2024-01-01' --AND REPORT_DAY<='2025-05-31'
GROUP BY ALL
ORDER BY 1 DESC, 2;
CREATE OR REPLACE TABLE
    $stage_db.stg_brandebitda_revenue_non_order_sku_consolidated_bi_mapping AS

/* ── ① source CTEs ─────────────────────────────────────────────────────── */
WITH actual AS (        -- metrics that were already at SKU level
    SELECT *
    FROM $stage_db.stg_brandebitda_revenue_non_order_sku_specified_bi_mapping
)

, alloc  AS (        -- account→SKU allocation
    SELECT *
    FROM $stage_db.stg_brandebitda_non_order_leve_account_to_sku_conversion
)

/* ── ② full outer-join to align the two sources on one row ─────────────── */
, joined AS (
    SELECT
        COALESCE(a.report_date,  l.report_date)      AS report_date,
        COALESCE(a.seller_id,    l.seller_id)        AS seller_id,
        COALESCE(a.country_code, l.country_code)     AS country_code,
       -- COALESCE(a.marketplace,  l.marketplace)      AS marketplace,
        COALESCE(a.sku,          l.sku)              AS sku,

        /* ------------- 32 × 3 column sets ------------- */
        /* storage_storage_other */
        COALESCE(a.storage_storage_other,0)            AS storage_storage_other_actual,
        COALESCE(l.storage_storage_other,0)            AS storage_storage_other_allocated,

        /* storage_other_service */
        COALESCE(a.storage_other_service,0)            AS storage_other_service_actual,
        COALESCE(l.storage_other_service,0)            AS storage_other_service_allocated,

        /* storage_longterm_other */
        COALESCE(a.storage_longterm_other,0)           AS storage_longterm_other_actual,
        COALESCE(l.storage_longterm_other,0)           AS storage_longterm_other_allocated,

        /* revenue_shippingcredits_refund */
        COALESCE(a.revenue_shippingcredits_refund,0)   AS revenue_shippingcredits_refund_actual,
        COALESCE(l.revenue_shippingcredits_refund,0)   AS revenue_shippingcredits_refund_allocated,

        /* revenue_sales_refund */
        COALESCE(a.revenue_sales_refund,0)             AS revenue_sales_refund_actual,
        COALESCE(l.revenue_sales_refund,0)             AS revenue_sales_refund_allocated,

        /* revenue_sales_liquidation */
        COALESCE(a.revenue_sales_liquidation,0)        AS revenue_sales_liquidation_actual,
        COALESCE(l.revenue_sales_liquidation,0)        AS revenue_sales_liquidation_allocated,

        /* revenue_sales_chargeback */
        COALESCE(a.revenue_sales_chargeback,0)         AS revenue_sales_chargeback_actual,
        COALESCE(l.revenue_sales_chargeback,0)         AS revenue_sales_chargeback_allocated,

        /* revenue_promo_refund */
        COALESCE(a.revenue_promo_refund,0)             AS revenue_promo_refund_actual,
        COALESCE(l.revenue_promo_refund,0)             AS revenue_promo_refund_allocated,

        /* revenue_other_refund */
        COALESCE(a.revenue_other_refund,0)             AS revenue_other_refund_actual,
        COALESCE(l.revenue_other_refund,0)             AS revenue_other_refund_allocated,

        /* revenue_other_liquidation */
        COALESCE(a.revenue_other_liquidation,0)        AS revenue_other_liquidation_actual,
        COALESCE(l.revenue_other_liquidation,0)        AS revenue_other_liquidation_allocated,

        /* revenue_giftwrapcredits_refund */
        COALESCE(a.revenue_giftwrapcredits_refund,0)   AS revenue_giftwrapcredits_refund_actual,
        COALESCE(l.revenue_giftwrapcredits_refund,0)   AS revenue_giftwrapcredits_refund_allocated,

        /* revenue_adjustment_liquidation */
        COALESCE(a.revenue_adjustment_liquidation,0)   AS revenue_adjustment_liquidation_actual,
        COALESCE(l.revenue_adjustment_liquidation,0)   AS revenue_adjustment_liquidation_allocated,

        /* immaterial */
        
        -- COALESCE(a.immaterial,0)                       AS immaterial_actual,
        -- COALESCE(l.immaterial,0)                       AS immaterial_allocated,

        /* fba_reimbursement_adjustment */
        COALESCE(a.fba_reimbursement_adjustment,0)     AS fba_reimbursement_adjustment_actual,
        COALESCE(l.fba_reimbursement_adjustment,0)     AS fba_reimbursement_adjustment_allocated,

        /* fba_other_service */
        COALESCE(a.fba_other_service,0)                AS fba_other_service_actual,
        COALESCE(l.fba_other_service,0)                AS fba_other_service_allocated,

        /* fba_other_return */
        COALESCE(a.fba_other_return,0)                 AS fba_other_return_actual,
        COALESCE(l.fba_other_return,0)                 AS fba_other_return_allocated,

        /* fba_other_other */
        COALESCE(a.fba_other_other,0)                  AS fba_other_other_actual,
        COALESCE(l.fba_other_other,0)                  AS fba_other_other_allocated,

        /* fba_other_liquidation */
        COALESCE(a.fba_other_liquidation,0)            AS fba_other_liquidation_actual,
        COALESCE(l.fba_other_liquidation,0)            AS fba_other_liquidation_allocated,

        /* fba_other_adjustment */
        COALESCE(a.fba_other_adjustment,0)             AS fba_other_adjustment_actual,
        COALESCE(l.fba_other_adjustment,0)             AS fba_other_adjustment_allocated,

        /* fba_fees_refund */
        COALESCE(a.fba_fees_refund,0)                  AS fba_fees_refund_actual,
        COALESCE(l.fba_fees_refund,0)                  AS fba_fees_refund_allocated,

        /* commission_fees_refund */
        COALESCE(a.commission_fees_refund,0)           AS commission_fees_refund_actual,
        COALESCE(l.commission_fees_refund,0)           AS commission_fees_refund_allocated,

        /* commission_fees_chargeback */
        COALESCE(a.commission_fees_chargeback,0)       AS commission_fees_chargeback_actual,
        COALESCE(l.commission_fees_chargeback,0)       AS commission_fees_chargeback_allocated,

        /* cogs_placement_service */
        COALESCE(a.cogs_placement_service,0)           AS cogs_placement_service_actual,
        COALESCE(l.cogs_placement_service,0)           AS cogs_placement_service_allocated,

        /* cogs_amzparteneredcarrier_other */
        COALESCE(a.cogs_amzparteneredcarrier_other,0)  AS cogs_amzparteneredcarrier_other_actual,
        COALESCE(l.cogs_amzparteneredcarrier_other,0)  AS cogs_amzparteneredcarrier_other_allocated,

        /* below BE (software) */
        -- COALESCE(a."below BE (software)",0)            AS "below BE (software)_actual",
        -- COALESCE(l."below BE (software)",0)            AS "below BE (software)_allocated",

        -- /* below BE (MBS) */
        -- COALESCE(a."below BE (MBS)",0)                 AS "below BE (MBS)_actual",
        -- COALESCE(l."below BE (MBS)",0)                 AS "below BE (MBS)_allocated",

        /* advertising_vine_service */
        COALESCE(a.advertising_vine_service,0)         AS advertising_vine_service_actual,
        COALESCE(l.advertising_vine_service,0)         AS advertising_vine_service_allocated,

        /* advertising_vine_other */
        COALESCE(a.advertising_vine_other,0)           AS advertising_vine_other_actual,
        COALESCE(l.advertising_vine_other,0)           AS advertising_vine_other_allocated,

        /* advertising_ppc_service */
        COALESCE(a.advertising_ppc_service,0)          AS advertising_ppc_service_actual,
        COALESCE(l.advertising_ppc_service,0)          AS advertising_ppc_service_allocated,

        /* advertising_dealfees_service */
        COALESCE(a.advertising_dealfees_service,0)     AS advertising_dealfees_service_actual,
        COALESCE(l.advertising_dealfees_service,0)     AS advertising_dealfees_service_allocated,

        /* advertising_dealfees_other */
        COALESCE(a.advertising_dealfees_other,0)       AS advertising_dealfees_other_actual,
        COALESCE(l.advertising_dealfees_other,0)       AS advertising_dealfees_other_allocated
    FROM actual a
    FULL OUTER JOIN alloc l
      ON a.report_date   = l.report_date
     AND a.seller_id     = l.seller_id
     AND a.country_code  = l.country_code
     AND a.sku           = l.sku
)

/* ── ③ final projection: add combined = actual + allocated ─────────────── */
SELECT
    report_date,
    seller_id,
    country_code,
    --marketplace,
    sku,

    /* ─────────── repeat pattern for every metric ─────────── */
    storage_storage_other_actual,
    storage_storage_other_allocated,
    storage_storage_other_actual     + storage_storage_other_allocated      AS storage_storage_other,

    storage_other_service_actual,
    storage_other_service_allocated,
    storage_other_service_actual     + storage_other_service_allocated      AS storage_other_service,

    storage_longterm_other_actual,
    storage_longterm_other_allocated,
    storage_longterm_other_actual    + storage_longterm_other_allocated     AS storage_longterm_other,

    revenue_shippingcredits_refund_actual,
    revenue_shippingcredits_refund_allocated,
    revenue_shippingcredits_refund_actual + revenue_shippingcredits_refund_allocated AS revenue_shippingcredits_refund,

    revenue_sales_refund_actual,
    revenue_sales_refund_allocated,
    revenue_sales_refund_actual      + revenue_sales_refund_allocated       AS revenue_sales_refund,

    revenue_sales_liquidation_actual,
    revenue_sales_liquidation_allocated,
    revenue_sales_liquidation_actual + revenue_sales_liquidation_allocated  AS revenue_sales_liquidation,

    revenue_sales_chargeback_actual,
    revenue_sales_chargeback_allocated,
    revenue_sales_chargeback_actual  + revenue_sales_chargeback_allocated   AS revenue_sales_chargeback,

    revenue_promo_refund_actual,
    revenue_promo_refund_allocated,
    revenue_promo_refund_actual      + revenue_promo_refund_allocated       AS revenue_promo_refund,

    revenue_other_refund_actual,
    revenue_other_refund_allocated,
    revenue_other_refund_actual      + revenue_other_refund_allocated       AS revenue_other_refund,

    revenue_other_liquidation_actual,
    revenue_other_liquidation_allocated,
    revenue_other_liquidation_actual + revenue_other_liquidation_allocated  AS revenue_other_liquidation,

    revenue_giftwrapcredits_refund_actual,
    revenue_giftwrapcredits_refund_allocated,
    revenue_giftwrapcredits_refund_actual + revenue_giftwrapcredits_refund_allocated AS revenue_giftwrapcredits_refund,

    revenue_adjustment_liquidation_actual,
    revenue_adjustment_liquidation_allocated,
    revenue_adjustment_liquidation_actual + revenue_adjustment_liquidation_allocated AS revenue_adjustment_liquidation,

    -- immaterial_actual,
    -- immaterial_allocated,
    -- immaterial_actual                 + immaterial_allocated                AS immaterial,

    fba_reimbursement_adjustment_actual,
    fba_reimbursement_adjustment_allocated,
    fba_reimbursement_adjustment_actual + fba_reimbursement_adjustment_allocated    AS fba_reimbursement_adjustment,

    fba_other_service_actual,
    fba_other_service_allocated,
    fba_other_service_actual          + fba_other_service_allocated         AS fba_other_service,

    fba_other_return_actual,
    fba_other_return_allocated,
    fba_other_return_actual           + fba_other_return_allocated          AS fba_other_return,

    fba_other_other_actual,
    fba_other_other_allocated,
    fba_other_other_actual            + fba_other_other_allocated           AS fba_other_other,

    fba_other_liquidation_actual,
    fba_other_liquidation_allocated,
    fba_other_liquidation_actual      + fba_other_liquidation_allocated     AS fba_other_liquidation,

    fba_other_adjustment_actual,
    fba_other_adjustment_allocated,
    fba_other_adjustment_actual       + fba_other_adjustment_allocated      AS fba_other_adjustment,

    fba_fees_refund_actual,
    fba_fees_refund_allocated,
    fba_fees_refund_actual            + fba_fees_refund_allocated           AS fba_fees_refund,

    commission_fees_refund_actual,
    commission_fees_refund_allocated,
    commission_fees_refund_actual     + commission_fees_refund_allocated    AS commission_fees_refund,

    commission_fees_chargeback_actual,
    commission_fees_chargeback_allocated,
    commission_fees_chargeback_actual + commission_fees_chargeback_allocated AS commission_fees_chargeback,

    cogs_placement_service_actual,
    cogs_placement_service_allocated,
    cogs_placement_service_actual     + cogs_placement_service_allocated    AS cogs_placement_service,

    cogs_amzparteneredcarrier_other_actual,
    cogs_amzparteneredcarrier_other_allocated,
    cogs_amzparteneredcarrier_other_actual + cogs_amzparteneredcarrier_other_allocated AS cogs_amzparteneredcarrier_other,

    -- "below BE (software)_actual",
    -- "below BE (software)_allocated",
    -- "below BE (software)_actual"      + "below BE (software)_allocated"     AS "below BE (software)",

    -- "below BE (MBS)_actual",
    -- "below BE (MBS)_allocated",
    -- "below BE (MBS)_actual"           + "below BE (MBS)_allocated"          AS "below BE (MBS)",

    advertising_vine_service_actual,
    advertising_vine_service_allocated,
    advertising_vine_service_actual   + advertising_vine_service_allocated  AS advertising_vine_service,

    advertising_vine_other_actual,
    advertising_vine_other_allocated,
    advertising_vine_other_actual     + advertising_vine_other_allocated    AS advertising_vine_other,

    advertising_ppc_service_actual,
    advertising_ppc_service_allocated,
    advertising_ppc_service_actual    + advertising_ppc_service_allocated   AS advertising_ppc_service,

    advertising_dealfees_service_actual,
    advertising_dealfees_service_allocated,
    advertising_dealfees_service_actual + advertising_dealfees_service_allocated AS advertising_dealfees_service,

    advertising_dealfees_other_actual,
    advertising_dealfees_other_allocated,
    advertising_dealfees_other_actual + advertising_dealfees_other_allocated AS advertising_dealfees_other

FROM joined;

CREATE OR REPLACE TABLE $stage_db.stg_brandebitda_revenue_shipment_level_aggregate AS

    SELECT
        "amazon_order_id" AS order_id,
        "sales_channel" AS channel,
        CASE
          WHEN LOWER("sales_channel") LIKE 'amazon.com.tr%' THEN 'TR'
          WHEN LOWER("sales_channel") LIKE 'amazon.com.au%' THEN 'AU'
          WHEN LOWER("sales_channel") LIKE 'amazon.com.mx%' THEN 'MX'
          WHEN LOWER("sales_channel") LIKE 'amazon.com.be%' THEN 'BE'
          WHEN LOWER("sales_channel") LIKE 'amazon.com.br%' THEN 'BR'
          WHEN LOWER("sales_channel") LIKE 'amazon.ca%' THEN 'CA'
          WHEN LOWER("sales_channel") LIKE 'amazon.ae%' THEN 'AE'
          WHEN LOWER("sales_channel") LIKE 'amazon.co.uk%' THEN 'UK'
          WHEN LOWER("sales_channel") LIKE 'amazon.de%' THEN 'DE'
          WHEN LOWER("sales_channel") LIKE 'amazon.fr%' THEN 'FR'
          WHEN LOWER("sales_channel") LIKE 'amazon.it%' THEN 'IT'
          WHEN LOWER("sales_channel") LIKE 'amazon.pl%' THEN 'PL'
          WHEN LOWER("sales_channel") LIKE 'amazon.nl%' THEN 'NL'
          WHEN LOWER("sales_channel") LIKE 'amazon.ie%' THEN 'IE'
          WHEN LOWER("sales_channel") LIKE 'amazon.es%' THEN 'ES'
          WHEN LOWER("sales_channel") LIKE 'amazon.se%' THEN 'SE'
          WHEN LOWER("sales_channel") LIKE 'amazon.com%' THEN 'US'
          ELSE 'OTHER'
        END AS country_code,
        "seller_id" AS seller_id_shipment,
        "sku" AS sku,
        TRY_TO_DATE("shipment_date") AS shipment_date,
        SUM(COALESCE("quantity_shipped",0)) AS quantity,
        SUM(COALESCE("item_price",0)) AS product_sales,
        SUM(COALESCE("shipping_price",0)) AS shipping_credits,
        SUM(COALESCE("gift_wrap_price",0)) AS giftwrap_credits,
        SUM(COALESCE("item_promotion_discount",0) + COALESCE("ship_promotion_discount",0)) AS promo_rebates,
        SUM(COALESCE("item_price",0)) + SUM(COALESCE("shipping_price",0)) + SUM(COALESCE("gift_wrap_price",0))
          + SUM(COALESCE("item_promotion_discount",0) + COALESCE("ship_promotion_discount",0))  AS total_sales
    FROM dwh.staging.amazon_fba_fulfilled_shipments
    GROUP BY 
    "amazon_order_id", 
    "sales_channel", 
    "seller_id", 
    "sku", 
    TRY_TO_DATE("shipment_date")
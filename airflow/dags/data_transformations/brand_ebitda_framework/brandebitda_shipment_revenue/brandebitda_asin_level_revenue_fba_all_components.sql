/* ── ASIN-level: every component, subtotal and WBR total ─────────────── */
CREATE OR REPLACE TABLE
    $stage_db.stg_brandebitda_revenue_asin_level_revenue_fba_all_components AS

WITH sku_all AS (        -- ① every metric at SKU grain (built earlier)
    SELECT *
    FROM $stage_db.stg_brandebitda_revenue_sku_level_revenue_fba_all_components
),

sku_asin_map AS (        -- ② unique SKU→ASIN reference
    SELECT
        sku,
        country_code,
        brand_code,
        asin,
        product_name,
        market_place_region,
        ROW_NUMBER() OVER (            -- keep just one row per sku-country
            PARTITION BY sku, country_code
            ORDER BY 1                 -- arbitrary but deterministic
        ) AS rn
    FROM $stage_db.stg_brand_ebitda_sku_asin_map_orders_source_table
    QUALIFY rn = 1
)
SELECT
    /* ── keys ── */
    s.report_date,
    s.seller_id,
    s.country_code,
    m.brand_code,
    m.asin,
    m.product_name,

    /* ── quantities & every atomic component ─────────────────────────── */
    SUM(s.quantity)                                    AS quantity,

    /* A. Revenue – product sales like */
    SUM(s.revenue_sales_order)                         AS revenue_sales_order,
    SUM(s.revenue_sales_refund)                        AS revenue_sales_refund,
    SUM(s.revenue_sales_liquidation)                   AS revenue_sales_liquidation,
    SUM(s.revenue_sales_chargeback)                    AS revenue_sales_chargeback,

    /* B. Revenue – other credits / debits */
    SUM(s.revenue_shippingcredits_order)               AS revenue_shippingcredits_order,
    SUM(s.revenue_shippingcredits_refund)              AS revenue_shippingcredits_refund,
    SUM(s.revenue_giftwrapcredits_order)               AS revenue_giftwrapcredits_order,
    SUM(s.revenue_giftwrapcredits_refund)              AS revenue_giftwrapcredits_refund,
    SUM(s.revenue_other_refund)                        AS revenue_other_refund,
    SUM(s.revenue_other_liquidation)                   AS revenue_other_liquidation,
    SUM(s.revenue_adjustment_liquidation)              AS revenue_adjustment_liquidation,

    /* C. Revenue – promo */
    SUM(s.revenue_promo_order)                         AS revenue_promo_order,
    SUM(s.revenue_promo_refund)                        AS revenue_promo_refund,

    /* D. Selling fees */
    SUM(s.commission_fees_order)                       AS commission_fees_order,
    SUM(s.commission_fees_refund)                      AS commission_fees_refund,
    SUM(s.commission_fees_chargeback)                  AS commission_fees_chargeback,
    SUM(s.commission_other_order)                      AS commission_other_order,

    /* E. FBA fees */
    SUM(s.fba_fees_order)                              AS fba_fees_order,
    SUM(s.fba_fees_refund)                             AS fba_fees_refund,
    SUM(s.fba_other_service)                           AS fba_other_service,
    SUM(s.fba_other_return)                            AS fba_other_return,
    SUM(s.fba_other_other)                             AS fba_other_other,
    SUM(s.fba_other_liquidation)                       AS fba_other_liquidation,
    SUM(s.fba_other_adjustment)                        AS fba_other_adjustment,

    /* F. FBA reimbursement */
    SUM(s.fba_reimbursement_adjustment)                AS fba_reimbursement_adjustment,

    /* G. Deal / PPC / Vine */
    SUM(s.advertising_dealfees_service)                AS advertising_dealfees_service,
    SUM(s.advertising_dealfees_other)                  AS advertising_dealfees_other,
    SUM(s.advertising_ppc_service)                     AS advertising_ppc_service,
    SUM(s.advertising_vine_service)                    AS advertising_vine_service,
    SUM(s.advertising_vine_other)                      AS advertising_vine_other,

    /* H. Storage */
    SUM(s.storage_storage_other)                       AS storage_storage_other,
    SUM(s.storage_other_service)                       AS storage_other_service,
    SUM(s.storage_longterm_other)                      AS storage_longterm_other,

    /* I. COGS adjuncts */
    SUM(s.cogs_amzparteneredcarrier_other)             AS cogs_amzparteneredcarrier_other,
    SUM(s.cogs_placement_service)                      AS cogs_placement_service,

    /* ── subtotals (simple sums of the same components) ──────────────── */
    SUM(s.subtotal_revenue_sales)                      AS subtotal_revenue_sales,
    SUM(s.subtotal_revenue_other)                      AS subtotal_revenue_other,
    SUM(s.subtotal_revenue_promo)                      AS subtotal_revenue_promo,
    SUM(s.subtotal_selling_fees)                        AS subtotal_selling_fees,
    SUM(s.subtotal_fba_fees)                           AS subtotal_fba_fees,
    SUM(s.subtotal_fba_reimbursement)                  AS subtotal_fba_reimbursement,
    SUM(s.subtotal_deal_fees)                           AS subtotal_deal_fees,
    SUM(s.subtotal_ppc)                                AS subtotal_ppc,
    SUM(s.subtotal_ppc_vine)                           AS subtotal_ppc_vine,
    SUM(s.subtotal_storage)                            AS subtotal_storage,
    SUM(s.subtotal_storage_longterm)                   AS subtotal_storage_longterm,
    SUM(s.subtotal_cogs_amzpartneredcarrier)           AS subtotal_cogs_amzpartneredcarrier,
    SUM(s.subtotal_cogs_placement)                     AS subtotal_cogs_placement,

    /* ── WBR totals ───────────────────────────────────────────────────── */
      SUM(s.subtotal_revenue_sales)
    + SUM(s.subtotal_revenue_other)
    + SUM(s.subtotal_revenue_promo)                    AS total_net_revenue,

      SUM(s.subtotal_fba_fees)
    + SUM(s.subtotal_fba_reimbursement)                AS total_fulfillment_fees,

    SUM(s.subtotal_selling_fees)                        AS total_selling_fees,

      SUM(s.subtotal_cogs_amzpartneredcarrier)
    + SUM(s.subtotal_cogs_placement)                   AS total_cogs,

    /* QA buckets */
    SUM(s.subtotal_deal_fees)                          AS alternate_deal_fees_qa,
    SUM(s.subtotal_storage)                            AS alternate_storage_fees_qa,
    SUM(s.subtotal_storage_longterm)                   AS alternate_long_term_storage_qa,
    SUM(s.subtotal_ppc)                                AS alternate_ppc_cost_qa,
    SUM(s.subtotal_ppc_vine)                           AS alternate_ppc_vine_qa

FROM  sku_all s
LEFT  JOIN sku_asin_map m
  ON  s.sku          = m.sku
  AND s.country_code = m.country_code        
GROUP BY
    s.report_date,
    s.seller_id,
    s.country_code,
    m.brand_code,
    m.asin,
    m.product_name

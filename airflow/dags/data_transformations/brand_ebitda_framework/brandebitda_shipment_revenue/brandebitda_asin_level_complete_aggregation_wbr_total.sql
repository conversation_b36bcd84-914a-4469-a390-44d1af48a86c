
CREATE OR REPLACE TABLE
    $stage_db.stg_brandebitda_revenue_asin_level_wbr_totals AS

WITH sku_totals AS (  -- upstream sku-level metrics
    SELECT *
    FROM $stage_db.stg_brandebitda_revenue_sku_level_wbr_totals
),

sku_asin_map_orders AS (  -- sku→asin reference
    SELECT DISTINCT
           o.sku,
           o.country_code,
           o.brand_code,
           o.asin,
           o.product_name,
           o.market_place_region
    FROM $stage_db.stg_brand_ebitda_sku_asin_map_orders_source_table o
),

joined AS (
    SELECT
        s.report_date,
        s.seller_id,
        s.country_code,
        s.sku,                   
        m.brand_code,
        m.asin,
        m.product_name,
        m.market_place_region,

        s.quantity,
        s.total_net_revenue,
        s.total_fulfillment_fees,
        s.total_selling_fees,
        s.total_cogs,
        s.alternate_deal_fees_qa,
        s.alternate_storage_fees_qa,
        s.alternate_long_term_storage_qa,
        s.alternate_ppc_cost_qa,
        s.alternate_ppc_vine_qa
        --s.not_reported_immaterial,
        --s.not_reported_subscription,
        --s.not_reported_mcf,
        --s.not_reported_mbs
    FROM sku_totals s
    LEFT JOIN sku_asin_map_orders m
           ON s.sku          = m.sku
          AND s.country_code = m.country_code
)

SELECT
    report_date,
    seller_id,
    country_code,
    brand_code,
    asin,
    product_name,
    market_place_region,

    SUM(quantity)                        AS quantity,
    SUM(total_net_revenue)               AS total_net_revenue,
    SUM(total_fulfillment_fees)          AS total_fulfillment_fees,
    SUM(total_selling_fees)              AS total_selling_fees,
    SUM(total_cogs)                      AS total_cogs,
    SUM(alternate_deal_fees_qa)          AS alternate_deal_fees_qa,
    SUM(alternate_storage_fees_qa)       AS alternate_storage_fees_qa,
    SUM(alternate_long_term_storage_qa)  AS alternate_long_term_storage_qa,
    SUM(alternate_ppc_cost_qa)           AS alternate_ppc_cost_qa,
    SUM(alternate_ppc_vine_qa)           AS alternate_ppc_vine_qa
FROM joined
GROUP BY
    report_date,
    seller_id,
    country_code,
    brand_code,
    asin,
    product_name,
    market_place_region;

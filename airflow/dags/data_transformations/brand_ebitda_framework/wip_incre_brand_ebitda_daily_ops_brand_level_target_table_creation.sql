CREATE TABLE IF NOT EXISTS $stage_db.wip_incre_brand_ebitda_daily_ops_brand_level (
    marketplace VARCHAR,
    brand_code VARCHAR,
    brand_name VARCHAR, 
    brand_category VARCHAR,
    country_code VARCHAR,
    channel VARCHAR,
    purchase_date DATE,
    total_quantity FLOAT,
    item_price_lc FLOAT,
    item_price_usd FLOAT,
    shipping_price_lc FLOAT,
    shipping_price_usd FLOAT,
    giftwrap_price_lc FLOAT,
    giftwrap_price_usd FLOAT,
    item_promo_discount_lc FLOAT,
    item_promo_discount_usd FLOAT,
    ship_promo_discount_lc FLOAT,
    ship_promo_discount_usd FLOAT,
    ordered_product_sales_lc FLOAT,
    ordered_product_sales_usd FLOAT,
    cancelled_order_count FLOAT,
    total_order_count FLOAT,
    avg_7_days_sales_usd FLOAT,
    mtd_ordered_product_sales_usd FLOAT,
    qtd_ordered_product_sales_usd FLOAT,
    last_year_same_day_sales_usd FLOAT,
    avg_7_days_last_year_sales_usd FLOAT,
    mtd_last_year_sales_usd FLOAT,
    qtd_last_year_sales_usd FLOAT
   
);

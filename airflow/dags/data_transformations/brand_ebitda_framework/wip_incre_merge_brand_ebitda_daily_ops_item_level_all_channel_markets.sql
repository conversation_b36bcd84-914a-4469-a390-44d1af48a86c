MERGE INTO $stage_db.wip_incre_brand_ebitda_daily_ops_item_level_all_channel_markets AS tgt
USING (
  SELECT *
  FROM $stage_db.wip_incre_stg_brand_ebitda_daily_ops_item_level_stg
) AS src
ON tgt.marketplace = src.marketplace
   AND tgt.channel = src.channel
   AND COALESCE(tgt.brand_code, 'NO_BRAND') = COALESCE(src.brand_code, 'NO_BRAND')
   AND COALESCE(tgt.brand_name, 'NO_BRAND') = COALESCE(src.brand_name, 'NO_BRAND')
   AND tgt.sku = src.sku
   AND COALESCE(tgt.asin, 'NO_ASIN') = COALESCE(src.asin, 'NO_ASIN')
   AND tgt.country_code = src.country_code
   AND tgt.purchase_date = src.purchase_date
WHEN MATCHED THEN 
  UPDATE SET
    tgt.total_quantity = src.total_quantity,
    tgt.item_price_lc = src.item_price_lc,
    tgt.item_price_usd = src.item_price_usd,
    tgt.shipping_price_lc = src.shipping_price_lc,
    tgt.shipping_price_usd = src.shipping_price_usd,
    tgt.giftwrap_price_lc = src.giftwrap_price_lc,
    tgt.giftwrap_price_usd = src.giftwrap_price_usd,
    tgt.item_promo_discount_lc = src.item_promo_discount_lc,
    tgt.item_promo_discount_usd = src.item_promo_discount_usd,
    tgt.ship_promo_discount_lc = src.ship_promo_discount_lc,
    tgt.ship_promo_discount_usd = src.ship_promo_discount_usd,
    
    tgt.ordered_product_sales_lc = src.ordered_product_sales_lc,
    tgt.ordered_product_sales_usd = src.ordered_product_sales_usd,
    tgt.cancelled_order_count = src.cancelled_order_count,
    tgt.total_order_count = src.total_order_count,
    tgt.avg_7_days_sales_usd = src.avg_7_days_sales_usd,
    tgt.mtd_ordered_product_sales_usd = src.mtd_ordered_product_sales_usd,
    tgt.qtd_ordered_product_sales_usd = src.qtd_ordered_product_sales_usd,
    tgt.last_year_same_day_sales_usd = src.last_year_same_day_sales_usd,
    tgt.avg_7_days_last_year_sales_usd = src.avg_7_days_last_year_sales_usd,
    tgt.mtd_last_year_sales_usd = src.mtd_last_year_sales_usd,
    tgt.qtd_last_year_sales_usd = src.qtd_last_year_sales_usd

WHEN NOT MATCHED THEN 
  INSERT (
    marketplace,
    channel, 
    brand_category, 
    brand_code,
    brand_name, 
    sku,
    asin,
    product_name,
    country_code,
    purchase_date,
    total_quantity,
    item_price_lc,
    item_price_usd,
    shipping_price_lc,
    shipping_price_usd,
    giftwrap_price_lc,
    giftwrap_price_usd,
    item_promo_discount_lc,
    item_promo_discount_usd,
    ship_promo_discount_lc,
    ship_promo_discount_usd,
  
    ordered_product_sales_lc,
    ordered_product_sales_usd,
    cancelled_order_count,
    total_order_count,
    avg_7_days_sales_usd,
    mtd_ordered_product_sales_usd,
    qtd_ordered_product_sales_usd,
    last_year_same_day_sales_usd,
    avg_7_days_last_year_sales_usd,
    mtd_last_year_sales_usd,
    qtd_last_year_sales_usd

  )
  VALUES (
    src.marketplace,
    src.channel,
    src.brand_category, 
    src.brand_code,
    src.brand_name, 
    src.sku,
    src.asin,
    src.product_name,
    src.country_code,
    src.purchase_date,
    src.total_quantity,
    src.item_price_lc,
    src.item_price_usd,
    src.shipping_price_lc,
    src.shipping_price_usd,
    src.giftwrap_price_lc,
    src.giftwrap_price_usd,
    src.item_promo_discount_lc,
    src.item_promo_discount_usd,
    src.ship_promo_discount_lc,
    src.ship_promo_discount_usd,
   
    src.ordered_product_sales_lc,
    src.ordered_product_sales_usd,
    src.cancelled_order_count,
    src.total_order_count,
    src.avg_7_days_sales_usd,
    src.mtd_ordered_product_sales_usd,
    src.qtd_ordered_product_sales_usd,
    src.last_year_same_day_sales_usd,
    src.avg_7_days_last_year_sales_usd,
    src.mtd_last_year_sales_usd,
    src.qtd_last_year_sales_usd
  );

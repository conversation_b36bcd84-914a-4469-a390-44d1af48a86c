CREATE OR REPLACE TABLE $stage_db.brand_ebitda_all_channel_business_ops_with_cancel_daily AS

WITH sku_brand_map AS ( 
SELECT 
    distinct
    "sku", 
    "brand"
FROM SANDBOX.OPS.SKU_ITEM_MAPPING
QUALIFY  ROW_NUMBER() OVER( PARTITION BY "sku" ORDER BY "brand" DESC) =1
),

sku_country_brand_map AS (
SELECT 
    distinct
    "sku", 
    "country",
    "brand"
FROM SANDBOX.OPS.SKU_ITEM_MAPPING
QUALIFY  ROW_NUMBER() OVER( PARTITION BY "sku", "country" ORDER BY "brand" DESC) =1
),


unique_order_level_data AS (
SELECT *, 
CASE 
    WHEN "country_code" = 'US' THEN convert_timezone('UTC', 'America/Los_Angeles', "purchase_date_utc")
    WHEN "country_code" = 'DE' THEN convert_timezone('UTC', 'Europe/Berlin', "purchase_date_utc")
    WHEN "country_code" = 'AU' THEN convert_timezone('UTC', 'Australia/Sydney', "purchase_date_utc")
    WHEN "country_code" = 'IT' THEN convert_timezone('UTC', 'Europe/Rome', "purchase_date_utc")
    WHEN "country_code" = 'TR' THEN convert_timezone('UTC', 'Europe/Istanbul', "purchase_date_utc")
    WHEN "country_code" = 'PL' THEN convert_timezone('UTC', 'Europe/Warsaw', "purchase_date_utc")
    WHEN "country_code" = 'BE' THEN convert_timezone('UTC', 'Europe/Brussels', "purchase_date_utc")
    WHEN "country_code" = 'MX' THEN convert_timezone('UTC', 'America/Mexico_City', "purchase_date_utc")
    WHEN "country_code" = 'NL' THEN convert_timezone('UTC', 'Europe/Amsterdam', "purchase_date_utc")
    WHEN "country_code" = 'CA' THEN convert_timezone('UTC', 'America/Toronto', "purchase_date_utc")
    WHEN "country_code" = 'BR' THEN convert_timezone('UTC', 'America/Sao_Paulo', "purchase_date_utc")
    WHEN "country_code" = 'UK' THEN convert_timezone('UTC', 'Europe/London', "purchase_date_utc")
    WHEN "country_code" = 'FR' THEN convert_timezone('UTC', 'Europe/Paris', "purchase_date_utc")
    WHEN "country_code" = 'ES' THEN convert_timezone('UTC', 'Europe/Madrid', "purchase_date_utc")
    WHEN "country_code" = 'SE' THEN convert_timezone('UTC', 'Europe/Stockholm', "purchase_date_utc")
    ELSE "purchase_date_utc" -- Fallback: no conversion
END AS purchase_date_local_zone, 

CASE 
    WHEN "country_code" IN ('DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'PL', 'TR', 'BE') THEN 'EU'
    ELSE "country_code"
END AS market_place_region

FROM DWH.PROD.fact_all_orders AS orders    

WHERE "marketplace" = 'AMAZON'
    AND "sales_channel" not like '%non-amazon%'
    AND "purchase_date_utc" >= '2023-08-01'
    --AND "order_status" NOT IN ('cancelled', 'null') 
    AND "order_status" IS NOT NULL
    AND "country_code" IS NOT NULL

QUALIFY ROW_NUMBER() OVER(PARTITION BY "external_order_id", orders."sku" ORDER BY "record_updated_timestamp_utc" DESC) = 1 


),


unique_order_shopify_level_data AS (
SELECT orders.*,

convert_timezone('UTC', dim."snowflake_time_zone", orders."purchase_date_utc") AS purchase_date_local_zone,

CASE 
    WHEN orders."country_code" IN ('DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'PL', 'TR', 'BE') THEN 'EU'
    ELSE orders."country_code"
END AS market_place_region



FROM DWH.PROD.fact_all_orders AS orders  
LEFT JOIN dwh.prod.brand_timezones_dim AS dim
 ON  orders."brand" = dim."brand"
    AND orders."country_code" = dim."country_code"

WHERE orders."marketplace" = 'SHOPIFY'
    
    AND orders."purchase_date_utc" >= '2023-08-01'
    AND orders."country_code" IS NOT NULL

QUALIFY ROW_NUMBER() OVER(PARTITION BY orders."external_order_id", orders."sku" ORDER BY orders."record_updated_timestamp_utc" DESC) = 1 

),


union_all_orders_data AS (

SELECT *
FROM unique_order_level_data

UNION ALL 

SELECT * 
FROM unique_order_shopify_level_data

),


order_level_clean_data AS (
SELECT 
    orders.*,
    orders."brand" AS order_table_brand,
    map."brand" AS asin_map_brand, 
    brand_map_2."brand" AS other_country_asin_map_brand,

    COALESCE(map."brand", orders."brand", brand_map_2."brand") AS combine_brand
FROM union_all_orders_data AS orders
LEFT JOIN sku_country_brand_map as map 
    ON orders."sku" = map."sku" and orders.market_place_region = map."country"
 LEFT JOIN sku_brand_map AS brand_map_2 
    ON orders."sku" = brand_map_2."sku"
),

exchange_rate_prep AS (
SELECT 
base_currency,
CASE WHEN 
transactional_currency = 'EURO' THEN 'EUR'
ELSE transactional_currency END AS transactional_currency, 
exchange_rate,
effective_date
FROM DWH.NETSUITE.foreign_exchange
WHERE BASE_CURRENCY ='USD'
QUALIFY ROW_NUMBER() OVER(PARTITION BY transactional_currency, effective_date ORDER BY BASE_CURRENCY DESC) = 1
),

cancelled_order_count_aggre AS (
SELECT 
        "marketplace", 
        combine_brand,
        "country_code",
        "sku",
        date_trunc('day', purchase_date_local_zone) AS purchase_date, 
        COUNT(DISTINCT CASE WHEN COALESCE("order_status",'pending') = 'cancelled' 
                            THEN "external_order_id" END) AS cancelled_order_count,


        COUNT(distinct "external_order_id") AS total_order_count
     FROM order_level_clean_data AS orders
    LEFT JOIN exchange_rate_prep AS ns
        ON orders."currency" = ns.transactional_currency 
        AND date_trunc('day', orders.purchase_date_local_zone) = ns.effective_date
    GROUP BY 
        "marketplace", 
        combine_brand,
        "country_code",
        "sku",
        date_trunc('day', purchase_date_local_zone)


),


price_aggregates AS (
    SELECT 
        "marketplace", 
        combine_brand,
        "country_code",
        "sku",
        date_trunc('day', purchase_date_local_zone) AS purchase_date, 
        SUM("quantity") AS quantity,
        SUM("quantity" * "item_price_per_unit") AS item_price_lc, 
        SUM("quantity" * "shipping_price_per_unit") AS shipping_price_lc, 
        SUM("quantity" * "item_promotion_discount_per_unit") AS item_promo_discount, 
        SUM("quantity" * "ship_promotion_discount_per_unit") AS ship_promo_discount, 
        SUM("quantity" * "gift_wrap_price_per_unit") AS giftwrap_price_lc,

        -- Aggregating USD prices (using division by ns.exchange_rate)
        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "item_price_per_unit"
                ELSE ("quantity" * "item_price_per_unit") * ns.exchange_rate
            END
        ) AS item_price_usd,

        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "shipping_price_per_unit"
                ELSE ("quantity" * "shipping_price_per_unit") * ns.exchange_rate
            END
        ) AS shipping_price_usd,

        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "item_promotion_discount_per_unit"
                ELSE ("quantity" * "item_promotion_discount_per_unit") * ns.exchange_rate
            END
        ) AS item_promo_discount_usd,

        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "ship_promotion_discount_per_unit"
                ELSE ("quantity" * "ship_promotion_discount_per_unit") * ns.exchange_rate
            END
        ) AS ship_promo_discount_usd,

        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "gift_wrap_price_per_unit"
                ELSE ("quantity" * "gift_wrap_price_per_unit") * ns.exchange_rate
            END
        ) AS gift_wrap_price_usd
        
    FROM order_level_clean_data AS orders
    LEFT JOIN exchange_rate_prep AS ns
        ON orders."currency" = ns.transactional_currency 
        AND date_trunc('day', orders.purchase_date_local_zone) = ns.effective_date
   WHERE COALESCE(orders."order_status", 'pending') <> 'cancelled'
    GROUP BY 
        "marketplace", 
        combine_brand,
        "country_code",
        "sku",
        date_trunc('day', purchase_date_local_zone)
),


sku_level_ops AS (SELECT 
    price_aggre."marketplace", 
    price_aggre.combine_brand,
    price_aggre."country_code",
    price_aggre."sku",
    price_aggre.purchase_date,
    price_aggre.quantity,
    price_aggre.item_price_lc,
    price_aggre.item_price_usd,
    price_aggre.shipping_price_lc,
    price_aggre.shipping_price_usd,
    price_aggre.item_promo_discount,
    price_aggre.item_promo_discount_usd,
    price_aggre.ship_promo_discount,
    price_aggre.ship_promo_discount_usd,
    price_aggre.giftwrap_price_lc,
    price_aggre.gift_wrap_price_usd,

    cance_aggre.cancelled_order_count, 
    cance_aggre.total_order_count, 
    
    -- Ordered product sales in LC
    (COALESCE(price_aggre.item_price_lc,0) + COALESCE(price_aggre.shipping_price_lc,0) + COALESCE(price_aggre.giftwrap_price_lc,0) 
     - COALESCE(price_aggre.item_promo_discount,0) - COALESCE(price_aggre.ship_promo_discount,0)) AS ordered_product_sales_lc,

    -- Ordered product sales in USD
    (COALESCE(price_aggre.item_price_usd,0) + COALESCE(price_aggre.shipping_price_usd,0) + COALESCE(price_aggre.gift_wrap_price_usd,0)
     - COALESCE(price_aggre.item_promo_discount_usd,0) - COALESCE(price_aggre.ship_promo_discount_usd,0)) AS ordered_product_sales_usd

FROM price_aggregates AS price_aggre
LEFT JOIN cancelled_order_count_aggre AS cance_aggre
    ON price_aggre."marketplace" = cance_aggre."marketplace"
       AND price_aggre.combine_brand = cance_aggre.combine_brand
       AND price_aggre."country_code" = cance_aggre."country_code"
       AND price_aggre."sku" = cance_aggre."sku"
       AND date_trunc('day', price_aggre.purchase_date) = date_trunc('day', cance_aggre.purchase_date)
),


brand_level_aggregation AS (
    SELECT 
        sku_ops."marketplace", 
        sku_ops.combine_brand,
        sku_ops."country_code",
        sku_ops.purchase_date AS purchase_date_timestamp,
        DATE_TRUNC('day',sku_ops.purchase_date) AS purchase_date,
        SUM(sku_ops.quantity) AS total_quantity,
        SUM(sku_ops.item_price_lc) AS total_item_price_lc,
        SUM(sku_ops.item_price_usd) AS total_item_price_usd,
        SUM(sku_ops.shipping_price_lc) AS total_shipping_price_lc,
        SUM(sku_ops.shipping_price_usd) AS total_shipping_price_usd,
        SUM(sku_ops.item_promo_discount) AS total_item_promo_discount,
        SUM(sku_ops.item_promo_discount_usd) AS total_item_promo_discount_usd,
        SUM(sku_ops.ship_promo_discount) AS total_ship_promo_discount,
        SUM(sku_ops.ship_promo_discount_usd) AS total_ship_promo_discount_usd,
        SUM(sku_ops.giftwrap_price_lc) AS total_giftwrap_price_lc,
        SUM(sku_ops.gift_wrap_price_usd) AS total_giftwrap_price_usd,

        SUM(cancelled_order_count) AS cancelled_order_count, 
        SUM(total_order_count) AS total_order_count, 

        SUM(cancelled_order_count) / SUM(total_order_count) AS brand_cancellation_rate,
        

        
        -- Aggregated ordered product sales in LC
        
        SUM(sku_ops.ordered_product_sales_lc * (1 + COALESCE(refund.refund_rate,0))) AS total_ordered_product_sales_lc,
        
        -- Aggregated ordered product sales in USD
        SUM(sku_ops.ordered_product_sales_usd * (1 + COALESCE(refund.refund_rate,0))) AS total_ordered_product_sales_usd
        
    FROM sku_level_ops as sku_ops
    LEFT JOIN  DWH_DEV.STAGING.BRAND_EBITDA_BRAND_REFUND_RATE AS refund
    ON sku_ops.combine_brand = refund.combine_brand
    GROUP BY 
        sku_ops."marketplace", 
        sku_ops.combine_brand,
        sku_ops."country_code",
        sku_ops.purchase_date
),


brand_level_with_cancellation AS (

SELECT 


        brand_aggre."marketplace", 
        brand_aggre.combine_brand,
        brand_aggre."country_code",
        brand_aggre.purchase_date_timestamp,
        brand_aggre.purchase_date,
        brand_aggre.total_quantity,
        brand_aggre.total_item_price_lc,
        brand_aggre.total_item_price_usd,
        brand_aggre.total_shipping_price_lc,
        brand_aggre.total_shipping_price_usd,
        brand_aggre.total_item_promo_discount,
        brand_aggre.total_item_promo_discount_usd,
        brand_aggre.total_ship_promo_discount,
        brand_aggre.total_ship_promo_discount_usd,
        brand_aggre.total_giftwrap_price_lc,
        brand_aggre.total_giftwrap_price_usd,

        brand_aggre.cancelled_order_count, 
        brand_aggre.total_order_count, 

        brand_aggre.brand_cancellation_rate,
        

        
        -- Aggregated ordered product sales in LC
        
        brand_aggre.total_ordered_product_sales_lc AS refund_ordered_product_sales_lc ,
        
        -- Aggregated ordered product sales in USD
        brand_aggre.total_ordered_product_sales_usd AS refund_ordered_product_sales_usd, 

        CASE WHEN brand_aggre.purchase_date < DATEADD(day, -7, CURRENT_DATE()) AND 
        brand_aggre.brand_cancellation_rate > cancel_factor.CANCELLATION_RATE
        
        THEN total_ordered_product_sales_lc
        ELSE brand_aggre.total_ordered_product_sales_lc * (1- ABS(cancel_factor.CANCELLATION_RATE - brand_aggre.brand_cancellation_rate)) 
        END AS total_ordered_product_sales_lc, 

        CASE WHEN brand_aggre.purchase_date < DATEADD(day, -7, CURRENT_DATE()) AND
        brand_aggre.brand_cancellation_rate > cancel_factor.CANCELLATION_RATE
        
        THEN total_ordered_product_sales_usd 
        
        ELSE brand_aggre.total_ordered_product_sales_usd * (1- ABS(cancel_factor.CANCELLATION_RATE - brand_aggre.brand_cancellation_rate)) 
        END AS total_ordered_product_sales_usd
        


FROM brand_level_aggregation AS brand_aggre
LEFT JOIN DWH_DEV.STAGING.BRAND_EBITDA_BRAND_CANCELLATION_FACTOR AS cancel_factor
ON brand_aggre.combine_brand = cancel_factor.brand_code 

),




time_based_daily_aggregation AS (
    SELECT 
        *,

        -- 7 Days Average Total Ordered Product Sales (USD)
        COALESCE(AVG(total_ordered_product_sales_usd) 
                 OVER (PARTITION BY combine_brand, "marketplace", "country_code" 
                       ORDER BY purchase_date 
                       ROWS BETWEEN 6 PRECEDING AND CURRENT ROW), 0) AS avg_7_days_sales_usd,
     

 -- MTD (Month-to-Date) Ordered Product Sales (USD)
        COALESCE(SUM(total_ordered_product_sales_usd) 
                 OVER (PARTITION BY combine_brand, "marketplace", "country_code", 
                              DATE_TRUNC('month', purchase_date)
                       ORDER BY purchase_date), 0) AS mtd_ordered_product_sales_usd,

        -- QTD (Quarter-to-Date) Ordered Product Sales (USD)
        COALESCE(SUM(total_ordered_product_sales_usd) 
                 OVER (PARTITION BY combine_brand, "marketplace", "country_code", 
                              DATE_TRUNC('quarter', purchase_date)
                       ORDER BY purchase_date), 0) AS qtd_ordered_product_sales_usd

    FROM brand_level_with_cancellation
    
), 


last_year_aggregation AS (
SELECT 
b1.*,

-- Last Year Same Day Sales (USD)
        COALESCE(b2.total_ordered_product_sales_usd,0) AS last_year_same_day_sales_usd,
   -- 7 Days Average of Last Year Same Date Total Ordered Product Sales (USD)
        COALESCE(AVG(b2.total_ordered_product_sales_usd) 
                 OVER (PARTITION BY b2.combine_brand, b2. "marketplace", b2."country_code" 
                       ORDER BY b1.purchase_date 
                       ROWS BETWEEN 6 PRECEDING AND CURRENT ROW), 0) AS avg_7_days_last_year_sales_usd, 

 -- MTD (Month-to-Date) Last Year Same Date Ordered Product Sales (USD)
        COALESCE(SUM(b2.total_ordered_product_sales_usd) 
                 OVER (PARTITION BY b2.combine_brand, b2."marketplace", b2."country_code", 
                              DATE_TRUNC('month', b2.purchase_date)
                       ORDER BY b2.purchase_date), 0) AS mtd_last_year_sales_usd,

        -- QTD (Quarter-to-Date) Last Year Same Date Ordered Product Sales (USD)
        COALESCE(SUM(b2.total_ordered_product_sales_usd) 
                 OVER (PARTITION BY b2.combine_brand, b2."marketplace", b2."country_code", 
                              DATE_TRUNC('quarter', b2.purchase_date)
                       ORDER BY b2.purchase_date), 0) AS qtd_last_year_sales_usd

        
FROM time_based_daily_aggregation b1
        LEFT JOIN time_based_daily_aggregation b2
        ON b1.combine_brand = b2.combine_brand
       AND b1."marketplace" = b2."marketplace"
       AND b1."country_code" = b2."country_code"
       AND b1.purchase_date = DATEADD(year, 1, b2.purchase_date)
),

-- channel level aggregation 
channel_level_data_aggregation AS (

SELECT 
    "marketplace", 
    combine_brand,

    -- Channel logic
    CASE 
        WHEN "marketplace" = 'AMAZON' AND "country_code" = 'US' THEN 'AMAZON US'
        WHEN "marketplace" = 'AMAZON' AND "country_code" != 'US' THEN 'AMAZON INTL'
        WHEN "marketplace" = 'SHOPIFY' THEN 'DTC'
        ELSE 'OTHER' 
    END AS channel, 

    purchase_date,
    
    -- Aggregated metrics
    SUM(total_quantity) AS total_quantity,
    SUM(total_item_price_lc) AS total_item_price_lc,
    SUM(total_item_price_usd) AS total_item_price_usd,
    SUM(total_shipping_price_lc) AS total_shipping_price_lc,
    SUM(total_shipping_price_usd) AS total_shipping_price_usd,
    SUM(total_item_promo_discount) AS total_item_promo_discount,
    SUM(total_item_promo_discount_usd) AS total_item_promo_discount_usd,
    SUM(total_ship_promo_discount) AS total_ship_promo_discount,
    SUM(total_ship_promo_discount_usd) AS total_ship_promo_discount_usd,
    SUM(total_giftwrap_price_lc) AS total_giftwrap_price_lc,
    SUM(total_giftwrap_price_usd) AS total_giftwrap_price_usd,
    SUM(total_ordered_product_sales_lc) AS total_ordered_product_sales_lc,
    SUM(total_ordered_product_sales_usd) AS total_ordered_product_sales_usd,

    -- Aggregated calculated metrics
    SUM(avg_7_days_sales_usd) AS avg_7_days_sales_usd,
    SUM(mtd_ordered_product_sales_usd) AS mtd_ordered_product_sales_usd,
    SUM(qtd_ordered_product_sales_usd) AS qtd_ordered_product_sales_usd,
    
    SUM(last_year_same_day_sales_usd) AS last_year_same_day_sales_usd,
    SUM(avg_7_days_last_year_sales_usd) AS avg_7_days_last_year_sales_usd,
    SUM(mtd_last_year_sales_usd) AS mtd_last_year_sales_usd,
    SUM(qtd_last_year_sales_usd) AS qtd_last_year_sales_usd, 

    SUM(cancelled_order_count) AS cancelled_order_count, 
    SUM(total_order_count) AS total_order_count

FROM last_year_aggregation
GROUP BY 
    "marketplace", 
    combine_brand,
    channel, 
    purchase_date
),


-- JOIN with targets at daily level 




ops_with_tagrets_brand_category_map AS (

SELECT 
opst.*, 
cat_map.brand AS brand_name, 
cat_map.category AS brand_category

FROM channel_level_data_aggregation AS opst
LEFT JOIN DWH_DEV.STAGING.BRAND_EBITDA_BRAND_CODE_CATEGORY_MAP AS cat_map
ON opst.combine_brand = cat_map.brand_code
),


ops_by_brand_channel_with_targets AS (

SELECT 
aggre.*, 
targets.revenue_targets, 

 -- MTD (Month-to-Date) Ordered Product Sales (USD)
        COALESCE(SUM(targets.revenue_targets) 
                 OVER (PARTITION BY aggre.combine_brand, aggre."marketplace", aggre.channel, 
                              DATE_TRUNC('month', aggre.purchase_date)
                       ORDER BY aggre.purchase_date), 0) AS mtd_revenue_targets,

        -- QTD (Quarter-to-Date) Ordered Product Sales (USD)
        COALESCE(SUM(targets.revenue_targets) 
                 OVER (PARTITION BY aggre.combine_brand, aggre."marketplace", aggre.channel, 
                              DATE_TRUNC('quarter', aggre.purchase_date)
                       ORDER BY aggre.purchase_date), 0) AS qtd_revenue_targets,


        targets.brand AS brand_targets,
        targets.channel AS channel_targets,
        targets.full_date AS full_date_targets



FROM ops_with_tagrets_brand_category_map AS aggre
LEFT JOIN DWH_DEV.STAGING.BRAND_EBITDA_BRAND_TARGETS_BY_CHANNEL  AS targets 
ON aggre.brand_name = targets.brand
AND aggre.channel = targets.channel
AND DATE_TRUNC('day',aggre.purchase_date) = DATE_TRUNC('day',targets.full_date)

)

SELECT *

FROM ops_by_brand_channel_with_targets
WHERE purchase_date < current_date()
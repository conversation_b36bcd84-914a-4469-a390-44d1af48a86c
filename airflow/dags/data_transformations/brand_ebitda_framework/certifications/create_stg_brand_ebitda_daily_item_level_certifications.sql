CREATE OR REPLACE TRANSIENT TABLE $stage_db.create_stg_brand_ebitda_daily_item_level_certifications AS

WITH generate_date AS (
    SELECT
        '2024-01-01'::date + row_number() over(order by 0) AS report_date
        , CASE
            WHEN DAYOFWEEK(report_date) = 7 THEN report_date -- If the date is Sunday, return the date itself
            ELSE DATE_TRUNC('WEEK', REPORT_DATE) - INTERVAL '1 day' -- Else, return to the date of the preceding Sunday
        END AS week_start_date
    FROM table(generator(rowcount => 1824))
),

daily_certifications AS (
    SELECT
        d.report_date,
        c.month_start,
        c.asin,
        c.brand AS brand_code,
        c.geo AS country_code,
        CASE
            WHEN c.channel = 'AMZ' AND c.geo = 'US' THEN 'AMZ-US'
            WHEN c.channel = 'AMZ' AND c.geo != 'US' THEN 'AMZ-INT'
            WHEN c.channel = 'DTC' THEN 'DTC'
            WHEN c.channel = 'OFFLINE' THEN 'Offline'
        END AS channel,
        DIV0NULL(MAX(brand_certification_cost), EXTRACT(DAY FROM LAST_DAY(c.month_start, 'month'))) AS daily_brand_certification_cost
    FROM
        $stage_db.merge_brand_ebitda_certifications AS c
    LEFT JOIN
        generate_date AS d
        ON DATE_TRUNC("month", d.report_date) = c.month_start
    GROUP BY ALL
),

-- Calculates the daily certification cost per ASIN based on their asin revenue contribution to their respective brand
daily_asin_level_cost AS (
    SELECT
        certifications.report_date,
        certifications.asin,
        certifications.brand_code,
        certifications.country_code,
        certifications.channel,
        certifications.daily_brand_certification_cost AS daily_brand_certification_cost_raw,
        DIV0NULL(asin_revenue.asin_revenue_country, SUM(asin_revenue.asin_revenue_country) OVER (PARTITION BY asin_revenue.report_date, asin_revenue.country_code)) AS asin_revenue_share,
        COALESCE(daily_brand_certification_cost_raw * asin_revenue_share, 0) AS daily_certification_cost
    FROM daily_certifications AS certifications
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON certifications.report_date = asin_revenue.report_date
            AND certifications.asin = asin_revenue.asin
            AND certifications.brand_code = asin_revenue.brand_code
            AND certifications.country_code = asin_revenue.country_code
    WHERE
        asin_revenue.asin IN (
            SELECT DISTINCT asin
            FROM daily_certifications
            WHERE asin IS NOT NULL
        )
)

SELECT
    MD5(
        CAST(
            COALESCE(CAST(report_date AS VARCHAR), '') || '-' ||
            COALESCE(CAST(asin AS VARCHAR), '') || '-' ||
            COALESCE(CAST(brand_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(country_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(channel AS VARCHAR), '') AS VARCHAR
        )
    ) AS pk,
    report_date,
    asin,
    brand_code,
    country_code,
    channel,
    SUM(daily_certification_cost) AS daily_certification_cost
FROM daily_asin_level_cost
GROUP BY ALL
;

CREATE OR REPLACE TABLE $stage_db.merge_stg_brand_ebitda_daily_item_level_certifications AS
SELECT
    *,
    '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
    '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
FROM $stage_db.create_stg_brand_ebitda_daily_item_level_certifications
WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_stg_brand_ebitda_daily_item_level_certifications AS tgt
USING
    (
        SELECT *
        FROM $stage_db.create_stg_brand_ebitda_daily_item_level_certifications
        QUALIFY ROW_NUMBER() OVER (PARTITION BY pk ORDER BY report_date DESC) = 1 -- dedupe
    ) AS src
        ON tgt.pk = src.pk

WHEN MATCHED THEN
    -- Update the record if it already exists
    UPDATE SET
        tgt.report_date = src.report_date,
        tgt.asin = src.asin,
        tgt.brand_code = src.brand_code,
        tgt.country_code = src.country_code,
        tgt.channel = src.channel,
        tgt.daily_certification_cost = src.daily_certification_cost,
        tgt.record_updated_timestamp_utc = SYSDATE()

WHEN NOT MATCHED THEN
    -- Insert a new record if it does not exist
    INSERT (
        pk,
        report_date,
        asin,
        brand_code,
        country_code,
        channel,
        daily_certification_cost,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        src.pk,
        src.report_date,
        src.asin,
        src.brand_code,
        src.country_code,
        src.channel,
        src.daily_certification_cost,
        SYSDATE(),
        SYSDATE()
    );
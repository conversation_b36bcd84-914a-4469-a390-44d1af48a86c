CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_brand_ebitda_certifications AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(month_start AS VARCHAR), ''), '-',
            COALESCE(CAST(brand AS VARCHAR), ''), '-',
            COALESCE(CAST(channel AS VARCHAR), ''), '-',
            COALESCE(CAST(geo AS VARCHAR), ''), '-',
            COALESCE(CAST(channel AS VARCHAR), ''), '-',
            COALESCE(CAST(asin AS VARCHAR), '')
            )) AS pk,
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        geo,
        asin,
        brand,
        channel,
        month_start,
        brand_certification_cost,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_brand_ebitda_certifications
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY _airbyte_extracted_at DESC NULLS LAST) = 1
);
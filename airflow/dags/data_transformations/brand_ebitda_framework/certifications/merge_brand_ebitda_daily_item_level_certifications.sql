CREATE OR REPLACE TABLE $curated_db.brand_ebitda_daily_item_level_certifications (
    pk VARCHAR,
    report_date DATE,
    asin VARCHAR,
    brand_code VARCHAR,
    country_code VARCHAR,
    channel VARCHAR,
    daily_certification_cost FLOAT,
    record_created_timestamp_utc TIMESTAMP DEFAULT SYSDATE(),
    record_updated_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);

BEGIN TRANSACTION;

MERGE INTO $curated_db.brand_ebitda_daily_item_level_certifications AS t
USING $stage_db.merge_stg_brand_ebitda_daily_item_level_certifications AS s
ON
    t.pk = s.pk

WHEN MATCHED THEN
    UPDATE SET
        t.report_date = s.report_date,
        t.asin = s.asin,
        t.brand_code = s.brand_code,
        t.country_code = s.country_code,
        t.channel = s.channel,
        t.daily_certification_cost = s.daily_certification_cost,
        t.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
    INSERT (
        pk,
        report_date,
        asin,
        brand_code,
        country_code,
        channel,
        daily_certification_cost,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        s.pk,
        s.report_date,
        s.asin,
        s.brand_code,
        s.country_code,
        s.channel,
        s.daily_certification_cost,
        s.record_created_timestamp_utc,
        s.record_updated_timestamp_utc
    );

DELETE FROM $curated_db.brand_ebitda_daily_item_level_certifications
WHERE (pk, record_updated_timestamp_utc) NOT IN (
    SELECT pk, MAX(record_updated_timestamp_utc)
    FROM $curated_db.brand_ebitda_daily_item_level_certifications
    GROUP BY pk
);

COMMIT;

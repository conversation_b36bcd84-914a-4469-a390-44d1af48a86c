CREATE TABLE IF NOT EXISTS $stage_db.merge_brand_ebitda_wbr_international AS
    SELECT

        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_brand_ebitda_wbr_international
    WHERE 1 = 0;

BEGIN TRANSACTION;

MERGE INTO
    $stage_db.merge_brand_ebitda_wbr_international AS tgt
USING
    $stage_db.dedupe_brand_ebitda_wbr_international AS src

        ON 1 = 1
       AND src.pk = tgt.pk

WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._airbyte_raw_id = src._airbyte_raw_id,
    tgt._airbyte_extracted_at = src._airbyte_extracted_at,
    tgt._airbyte_generation_id = src._airbyte_generation_id,
    tgt._airbyte_meta = src._airbyte_meta,
    tgt.brand = src.brand,
    tgt.units = src.units,
    tgt.entity = src.entity,
    tgt.market_id = src.market_id,
    tgt.week_year = src.week_year,
    tgt.brand_code = src.brand_code,
    tgt.child_asin = src.child_asin,
    tgt.market_mkp = src.market_mkp,
    tgt.parent_asin = src.parent_asin,
    tgt.selling_fees = src.selling_fees,
    tgt.fba_fees_final = src.fba_fees_final,
    tgt.week_start_date = src.week_start_date,
    tgt.fulfillment_fees = src.fulfillment_fees,
    tgt.total_direct_cost = src.total_direct_cost,
    tgt.net_revenue_less_promo = src.net_revenue_less_promo,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    brand,
    units,
    entity,
    market_id,
    week_year,
    brand_code,
    child_asin,
    market_mkp,
    parent_asin,
    selling_fees,
    fba_fees_final,
    week_start_date,
    fulfillment_fees,
    total_direct_cost,
    net_revenue_less_promo,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._airbyte_raw_id,
    src._airbyte_extracted_at,
    src._airbyte_generation_id,
    src._airbyte_meta,
    src.brand,
    src.units,
    src.entity,
    src.market_id,
    src.week_year,
    src.brand_code,
    src.child_asin,
    src.market_mkp,
    src.parent_asin,
    src.selling_fees,
    src.fba_fees_final,
    src.week_start_date,
    src.fulfillment_fees,
    src.total_direct_cost,
    src.net_revenue_less_promo,
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);

-- delete all entries that are not from the latest airbyte update
DELETE FROM $stage_db.merge_brand_ebitda_wbr_international
WHERE (record_updated_timestamp_utc) NOT IN (
    SELECT MAX(record_updated_timestamp_utc)
    FROM $stage_db.merge_brand_ebitda_wbr_international
);

COMMIT;

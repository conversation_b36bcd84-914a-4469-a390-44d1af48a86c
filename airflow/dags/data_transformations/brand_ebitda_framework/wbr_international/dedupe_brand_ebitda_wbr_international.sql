CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_brand_ebitda_wbr_international AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(brand_code AS VARCHAR), ''), '-',
            COALESCE(CAST(week_year AS VARCHAR), ''), '-',
            COALESCE(CAST(week_start_date AS VARCHAR), ''), '-',
            COALESCE(CAST(entity AS VARCHAR), ''), '-',
            COALESCE(CAST(child_asin AS VARCHAR), ''), '-',
            COALESCE(CAST(parent_asin AS VARCHAR), ''), '-',
            COALESCE(CAST(market_id AS VARCHAR), ''), '-',
            COALESCE(CAST(market_mkp AS VARCHAR), ''), '-',
            COALESCE(CAST(brand AS VARCHAR), '')
            )) AS pk,
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        brand,
        units,
        entity,
        market_id,
        week_year,
        brand_code,
        child_asin,
        market_mkp,
        parent_asin,
        selling_fees,
        fba_fees_final,
        week_start_date,
        fulfillment_fees,
        total_direct_cost,
        net_revenue_less_promo,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_brand_ebitda_wbr_international
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY _airbyte_extracted_at DESC NULLS LAST) = 1
);
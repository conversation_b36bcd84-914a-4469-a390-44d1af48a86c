CREATE OR REPLACE TABLE $stage_db.merge_stg_brand_ebitda_daily_item_level_facebook_spend AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.create_stg_brand_ebitda_daily_item_level_facebook_spend
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_stg_brand_ebitda_daily_item_level_facebook_spend AS tgt
USING
    (
        SELECT
            *
        FROM $stage_db.create_stg_brand_ebitda_daily_item_level_facebook_spend
        QUALIFY ROW_NUMBER() OVER (PARTITION BY pk ORDER BY date_start DESC) = 1 -- dedupe
    ) AS src
        ON tgt.pk = src.pk

WHEN MATCHED THEN
    -- Update the record if it already exists
    UPDATE SET
        tgt.perc_share = src.perc_share,
        tgt.fb_ad_spend = src.fb_ad_spend,
        tgt.misfit_agency_cost = src.misfit_agency_cost,
        tgt.total_fb_ad_spend = src.total_fb_ad_spend,
        tgt.record_updated_timestamp_utc = SYSDATE()  -- Update the timestamp

WHEN NOT MATCHED THEN
    -- Insert a new record if it does not exist
    INSERT (
        pk,
        date_start,
        brand_code,
        account_id,
        account_name,
        asin,
        country_code,
        channel,
        perc_share,
        fb_ad_spend,
        misfit_agency_cost,
        total_fb_ad_spend,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        src.pk,
        src.date_start,
        src.brand_code,
        src.account_id,
        src.account_name,
        src.asin,
        src.country_code,
        src.channel,
        src.perc_share,
        src.fb_ad_spend,
        src.misfit_agency_cost,
        src.total_fb_ad_spend,
        SYSDATE(),
        SYSDATE()
    );

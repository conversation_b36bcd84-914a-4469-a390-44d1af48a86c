CREATE OR REPLACE TRANSIENT TABLE $stage_db.create_stg_brand_ebitda_daily_item_level_facebook_spend AS

-- all accounts have USD as currency so no fx conversion needed
WITH raw_data_with_marketing_spend_allocation AS (
    -- 2025 marketing split
    SELECT
        fb.date_start
        , fb.brand_code
        , fb.account_id
        , fb.account_name
        , fb.account_currency
        , mktg_split.channel
        , mktg_split.perc_share
        , SUM(COALESCE(fb.clicks, 0)) AS total_clicks_all_channel
        , SUM(COALESCE(fb.conversions, 0)) AS total_conversions_all_channel
        , SUM(COALESCE(fb.impressions, 0)) AS total_impressions_all_channel
        , SUM(COALESCE(fb.spend, 0)) AS total_spend_all_channel
        , SUM(COALESCE(fb.spend, 0)) * mktg_split.perc_share AS total_spend -- channel share of facebook spend
    FROM
        DWH.PROD.FACEBOOK_ADS_CAMPAIGN_REPORT AS fb
    LEFT JOIN
        $stage_db.merge_brand_ebitda_marketing_split AS mktg_split
            ON fb.brand_code = mktg_split.brand_code
            AND DATE_TRUNC("month", fb.date_start) = mktg_split.month_start_date
    WHERE
        LOWER(REPLACE(mktg_split.marketing_activity, ' ', '')) = 'paidsocialmediameta'
        AND fb.date_start >= '2025-01-01'
    GROUP BY ALL

    UNION ALL
        -- 2024 marketing split for AMZ US
        SELECT
        fb.date_start
        , fb.brand_code
        , fb.account_id
        , fb.account_name
        , fb.account_currency
        , 'AMZ-US' AS channel
        , CASE WHEN fb.brand_code != 'ZSK' THEN 0.8 ELSE 0.5 END AS perc_share
        , SUM(COALESCE(fb.clicks, 0)) AS total_clicks_all_channel
        , SUM(COALESCE(fb.conversions, 0)) AS total_conversions_all_channel
        , SUM(COALESCE(fb.impressions, 0)) AS total_impressions_all_channel
        , SUM(COALESCE(fb.spend, 0)) AS total_spend_all_channel
        , SUM(COALESCE(fb.spend, 0)) * perc_share AS total_spend -- channel share of facebook spend
    FROM
        DWH.PROD.FACEBOOK_ADS_CAMPAIGN_REPORT AS fb
    WHERE
        fb.date_start < '2025-01-01'
    GROUP BY ALL

    UNION ALL
        -- 2024 marketing split for DTC
        SELECT
        fb.date_start
        , fb.brand_code
        , fb.account_id
        , fb.account_name
        , fb.account_currency
        , 'DTC' AS channel
        , CASE WHEN fb.brand_code != 'ZSK' THEN 0.2 ELSE 0.5 END AS perc_share
        , SUM(COALESCE(fb.clicks, 0)) AS total_clicks_all_channel
        , SUM(COALESCE(fb.conversions, 0)) AS total_conversions_all_channel
        , SUM(COALESCE(fb.impressions, 0)) AS total_impressions_all_channel
        , SUM(COALESCE(fb.spend, 0)) AS total_spend_all_channel
        , SUM(COALESCE(fb.spend, 0)) * perc_share AS total_spend -- channel share of facebook spend
    FROM
        DWH.PROD.FACEBOOK_ADS_CAMPAIGN_REPORT AS fb
    WHERE
        fb.date_start < '2025-01-01'
    GROUP BY ALL
),

-- monthly misfit agency cost
misfit_agency_cost AS (
    SELECT
        misfit.month_start_date
        , asin_revenue.report_date
        , misfit.geo AS country_code
        , misfit.brand
        , asin_revenue.asin
        , DIV0(misfit.amount_usd, EXTRACT(DAY FROM LAST_DAY(misfit.month_start_date, 'month'))) AS misfit_agency_cost_usd_brand
        , COALESCE(misfit_agency_cost_usd_brand * asin_revenue.asin_brand_revenue_share, 0) AS misfit_agency_cost_usd_asin
    FROM
        $stage_db.merge_brand_ebitda_misfit_agency_cost AS misfit
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON misfit.month_start_date = DATE_TRUNC("month", asin_revenue.report_date)
            AND misfit.brand = asin_revenue.brand_code
            AND misfit.geo = asin_revenue.country_code
    WHERE misfit.vendor = 'Facebook'
),

-- amz us
facebook_spend_with_asin_attribution_amz_us AS (
    SELECT
        raw_data.date_start
        , raw_data.brand_code
        , raw_data.account_id
        , raw_data.account_name
        , asin_revenue.asin
        , asin_revenue.country_code
        , raw_data.channel
        , raw_data.perc_share
        , COALESCE(raw_data.total_spend * asin_revenue.asin_brand_revenue_share, 0) AS fb_ad_spend
        , COALESCE(misfit.misfit_agency_cost_usd_asin, 0) AS misfit_agency_cost
        , COALESCE(fb_ad_spend + misfit_agency_cost, 0) AS total_fb_ad_spend
    FROM
        raw_data_with_marketing_spend_allocation AS raw_data
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON raw_data.date_start = asin_revenue.report_date
            AND raw_data.brand_code = asin_revenue.brand_code
    LEFT JOIN
        misfit_agency_cost AS misfit
            ON asin_revenue.report_date = misfit.report_date
            AND asin_revenue.asin = misfit.asin
            AND asin_revenue.country_code = misfit.country_code
    WHERE
        LOWER(raw_data.channel) = 'amz-us'
        AND asin_revenue.country_code = 'US'
),

-- amazon int
-- add CTE similar to this for other channels
facebook_spend_with_asin_attribution_amz_int AS (
    SELECT
        raw_data.date_start
        , raw_data.brand_code
        , raw_data.account_id
        , raw_data.account_name
        , asin_revenue.asin
        , asin_revenue.country_code
        , raw_data.channel
        , raw_data.perc_share
        , COALESCE(raw_data.total_spend * asin_revenue.asin_brand_revenue_share, 0) AS fb_ad_spend
        , COALESCE(misfit.misfit_agency_cost_usd_asin, 0) AS misfit_agency_cost
        , COALESCE(fb_ad_spend + misfit_agency_cost, 0) AS total_fb_ad_spend
    FROM
        raw_data_with_marketing_spend_allocation AS raw_data
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON raw_data.date_start = asin_revenue.report_date
            AND raw_data.brand_code = asin_revenue.brand_code
    LEFT JOIN
        misfit_agency_cost AS misfit
            ON asin_revenue.report_date = misfit.report_date
            AND asin_revenue.asin = misfit.asin
            AND asin_revenue.country_code = misfit.country_code
    WHERE
        LOWER(raw_data.channel) = 'amz-int'
        AND asin_revenue.country_code = 'CA' -- amz int allocation exclusive to CA
),

all_channels_consolidated AS (
    SELECT *
    FROM facebook_spend_with_asin_attribution_amz_us

    UNION ALL

    SELECT *
    FROM facebook_spend_with_asin_attribution_amz_int
)

SELECT
    MD5(
        CAST(
            COALESCE(CAST(date_start AS VARCHAR), '') || '-' ||
            COALESCE(CAST(brand_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(account_id AS VARCHAR), '') || '-' ||
            COALESCE(CAST(asin AS VARCHAR), '') || '-' ||
            COALESCE(CAST(country_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(channel AS VARCHAR), '') AS VARCHAR
        )
    ) AS pk,
    *
FROM
    all_channels_consolidated
;
CREATE OR REPLACE TABLE $curated_db.brand_ebitda_daily_item_level_facebook_spend (
    pk VARCHAR,
    report_date DATE,
    brand_code VARCHAR,
    account_id VARCHAR,
    account_name VARCHAR,
    asin VARCHAR,
    country_code VARCHAR,
    channel VARCHAR,
    fb_ad_spend FLOAT,
    misfit_agency_cost FLOAT,
    total_fb_ad_spend FLOAT,
    record_created_timestamp_utc TIMESTAMP DEFAULT SYSDATE(),
    record_updated_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);

BEGIN TRANSACTION;

MERGE INTO $curated_db.brand_ebitda_daily_item_level_facebook_spend AS t
USING $stage_db.merge_stg_brand_ebitda_daily_item_level_facebook_spend AS s
ON
    t.pk = s.pk

WHEN MATCHED THEN
    UPDATE SET
        t.fb_ad_spend = s.fb_ad_spend
        , t.misfit_agency_cost = s.misfit_agency_cost
        , t.total_fb_ad_spend = s.total_fb_ad_spend
        , t.record_updated_timestamp_utc = SYSDATE()

WHEN NOT MATCHED THEN
    INSERT (
        pk
        , report_date
        , brand_code
        , account_id
        , account_name
        , asin
        , country_code
        , channel
        , fb_ad_spend
        , misfit_agency_cost
        , total_fb_ad_spend
        , record_created_timestamp_utc
        , record_updated_timestamp_utc
    ) VALUES (
        s.pk
        , s.date_start
        , s.brand_code
        , s.account_id
        , s.account_name
        , s.asin
        , s.country_code
        , s.channel
        , s.fb_ad_spend
        , s.misfit_agency_cost
        , s.total_fb_ad_spend
        , SYSDATE()
        , SYSDATE()
    );

DELETE FROM $curated_db.brand_ebitda_daily_item_level_facebook_spend
WHERE (pk, record_updated_timestamp_utc) NOT IN (
    SELECT pk, MAX(record_updated_timestamp_utc)
    FROM $curated_db.brand_ebitda_daily_item_level_facebook_spend
    GROUP BY pk
);

COMMIT;

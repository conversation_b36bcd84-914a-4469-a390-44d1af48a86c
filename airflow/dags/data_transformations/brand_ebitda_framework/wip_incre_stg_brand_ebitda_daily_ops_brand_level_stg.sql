CREATE OR REPLACE TABLE $stage_db.wip_incre_brand_ebitda_daily_ops_brand_level_stg AS

WITH current_max_daily AS (
    SELECT MAX(purchase_date) AS current_max_daily
    FROM $stage_db.wip_incre_brand_ebitda_daily_ops_brand_level
),
hourly_data_filtered AS (
    SELECT h.*
    FROM DWH_DEV.STAGING.wip_incre_merge_brand_ebitda_all_markets_item_level_ops_hourly_merge AS h
    CROSS JOIN current_max_daily c
    WHERE DATE_TRUNC('DAY', h.purchase_hour_datetime) >= COALESCE(DATEADD(day, -60, c.current_max_daily), '2022-01-01')
),
daily_channel_data_aggregation AS (
    SELECT 
        marketplace, 
        brand_code,
        brand_name, 
        brand_category, 
        country_code,
        channel,
        DATE_TRUNC('DAY', purchase_hour_datetime) AS purchase_date,
        SUM(total_quantity) AS total_quantity,
        SUM(item_price_lc) AS item_price_lc,
        SUM(item_price_usd) AS item_price_usd,
        SUM(shipping_price_lc) AS shipping_price_lc,
        SUM(shipping_price_usd) AS shipping_price_usd,
        SUM(giftwrap_price_lc) AS giftwrap_price_lc,
        SUM(giftwrap_price_usd) AS giftwrap_price_usd,
        SUM(item_promo_discount_lc) AS item_promo_discount_lc,
        SUM(item_promo_discount_usd) AS item_promo_discount_usd,
        SUM(ship_promo_discount_lc) AS ship_promo_discount_lc,
        SUM(ship_promo_discount_usd) AS ship_promo_discount_usd,
        
        SUM(ordered_product_sales_lc) AS ordered_product_sales_lc,
        SUM(ordered_product_sales_usd) AS ordered_product_sales_usd,
        SUM(cancelled_order_count) AS cancelled_order_count, 
        SUM(total_order_count) AS total_order_count
    FROM hourly_data_filtered
    GROUP BY 
        marketplace, 
        brand_code,
        brand_category, 
        brand_name, 
        country_code,
        channel,
        DATE_TRUNC('DAY', purchase_hour_datetime)
),
time_based_daily_aggregation AS (
    SELECT 
        *,
        COALESCE(AVG(ordered_product_sales_usd) 
                 OVER (PARTITION BY brand_code, channel, marketplace, country_code
                       ORDER BY purchase_date
                       ROWS BETWEEN 6 PRECEDING AND CURRENT ROW), 0) AS avg_7_days_sales_usd,
        COALESCE(SUM(ordered_product_sales_usd) 
                 OVER (PARTITION BY brand_code, channel, marketplace, country_code, DATE_TRUNC('month', purchase_date)
                       ORDER BY purchase_date), 0) AS mtd_ordered_product_sales_usd,
        COALESCE(SUM(ordered_product_sales_usd) 
                 OVER (PARTITION BY brand_code, channel, marketplace, country_code, DATE_TRUNC('quarter', purchase_date)
                       ORDER BY purchase_date), 0) AS qtd_ordered_product_sales_usd
    FROM daily_channel_data_aggregation
),
last_year_aggregation AS (
    SELECT 
        b1.*,
        COALESCE(b2.ordered_product_sales_usd, 0) AS last_year_same_day_sales_usd,
        COALESCE(AVG(b2.ordered_product_sales_usd) 
                 OVER (PARTITION BY b2.brand_code, b2.channel, b2.marketplace, b2.country_code
                       ORDER BY b1.purchase_date
                       ROWS BETWEEN 6 PRECEDING AND CURRENT ROW), 0) AS avg_7_days_last_year_sales_usd, 
        COALESCE(SUM(b2.ordered_product_sales_usd) 
                 OVER (PARTITION BY b2.brand_code, b2.channel, b2.marketplace, b2.country_code, DATE_TRUNC('month', b2.purchase_date)
                       ORDER BY b2.purchase_date), 0) AS mtd_last_year_sales_usd,
        COALESCE(SUM(b2.ordered_product_sales_usd) 
                 OVER (PARTITION BY b2.brand_code, b2.channel, b2.marketplace, b2.country_code, DATE_TRUNC('quarter', b2.purchase_date)
                       ORDER BY b2.purchase_date), 0) AS qtd_last_year_sales_usd
    FROM time_based_daily_aggregation b1
    LEFT JOIN time_based_daily_aggregation b2
      ON b1.brand_code = b2.brand_code
     AND b1.marketplace = b2.marketplace
     AND b1.country_code = b2.country_code
     AND b1.channel = b2.channel
     AND b1.purchase_date = DATEADD(year, 1, b2.purchase_date)
    QUALIFY ROW_NUMBER() OVER (
        PARTITION BY b1.brand_code, b1.marketplace, b1.country_code, b1.channel, b1.purchase_date
        ORDER BY b1.brand_code DESC
    ) = 1
),
channel_level_data_aggregation AS (
    SELECT 
        marketplace, 
        channel, 
        country_code,
        brand_category, 
        brand_code,
        brand_name,
        purchase_date,
        SUM(total_quantity) AS total_quantity,
        SUM(item_price_lc) AS item_price_lc,
        SUM(item_price_usd) AS item_price_usd,
        SUM(shipping_price_lc) AS shipping_price_lc,
        SUM(shipping_price_usd) AS shipping_price_usd,
        SUM(giftwrap_price_lc) AS giftwrap_price_lc,
        SUM(giftwrap_price_usd) AS giftwrap_price_usd,
        SUM(item_promo_discount_lc) AS item_promo_discount_lc,
        SUM(item_promo_discount_usd) AS item_promo_discount_usd,
        SUM(ship_promo_discount_lc) AS ship_promo_discount_lc,
        SUM(ship_promo_discount_usd) AS ship_promo_discount_usd,
        
        SUM(ordered_product_sales_lc) AS ordered_product_sales_lc,
        SUM(ordered_product_sales_usd) AS ordered_product_sales_usd,
        SUM(avg_7_days_sales_usd) AS avg_7_days_sales_usd,
        SUM(mtd_ordered_product_sales_usd) AS mtd_ordered_product_sales_usd,
        SUM(qtd_ordered_product_sales_usd) AS qtd_ordered_product_sales_usd,
        SUM(last_year_same_day_sales_usd) AS last_year_same_day_sales_usd,
        SUM(avg_7_days_last_year_sales_usd) AS avg_7_days_last_year_sales_usd,
        SUM(mtd_last_year_sales_usd) AS mtd_last_year_sales_usd,
        SUM(qtd_last_year_sales_usd) AS qtd_last_year_sales_usd,
        SUM(cancelled_order_count) AS cancelled_order_count, 
        SUM(total_order_count) AS total_order_count
    FROM last_year_aggregation
    GROUP BY 
        marketplace, 
        channel, 
        country_code,
        brand_code,
        brand_name, 
        brand_category, 
        purchase_date
)

SELECT *
FROM channel_level_data_aggregation;

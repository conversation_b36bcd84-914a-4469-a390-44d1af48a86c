CREATE OR REPLACE TABLE $stage_db.brand_ebitda_all_markets_item_level_ops_hourly AS

WITH sku_brand_map AS (
SELECT 
    distinct
    "sku", 
    "brand"
FROM SANDBOX.OPS.SKU_ITEM_MAPPING
QUALIFY  ROW_NUMBER() OVER( PARTITION BY "sku" ORDER BY "brand" DESC) =1
),

sku_item_number_map AS (
SELECT 
    distinct
    "sku", 
    "item_number",
    "item_name"
FROM SANDBOX.OPS.SKU_ITEM_MAPPING
QUALIFY  ROW_NUMBER() OVER( PARTITION BY "sku" ORDER BY "brand" DESC) =1
),

sku_country_brand_map AS (
SELECT 
    distinct
    "sku", 
    "country",
    "brand"
FROM SANDBOX.OPS.SKU_ITEM_MAPPING
QUALIFY  ROW_NUMBER() OVER( PARTITION BY "sku", "country" ORDER BY "brand" DESC) =1
),


unique_order_level_data AS (
SELECT *, 
CASE 
    WHEN "country_code" = 'US' THEN convert_timezone('UTC', 'America/Los_Angeles', "purchase_date_utc")
    WHEN "country_code" = 'DE' THEN convert_timezone('UTC', 'Europe/Berlin', "purchase_date_utc")
    WHEN "country_code" = 'AU' THEN convert_timezone('UTC', 'Australia/Sydney', "purchase_date_utc")
    WHEN "country_code" = 'IT' THEN convert_timezone('UTC', 'Europe/Rome', "purchase_date_utc")
    WHEN "country_code" = 'TR' THEN convert_timezone('UTC', 'Europe/Istanbul', "purchase_date_utc")
    WHEN "country_code" = 'PL' THEN convert_timezone('UTC', 'Europe/Warsaw', "purchase_date_utc")
    WHEN "country_code" = 'BE' THEN convert_timezone('UTC', 'Europe/Brussels', "purchase_date_utc")
    WHEN "country_code" = 'MX' THEN convert_timezone('UTC', 'America/Mexico_City', "purchase_date_utc")
    WHEN "country_code" = 'NL' THEN convert_timezone('UTC', 'Europe/Amsterdam', "purchase_date_utc")
    WHEN "country_code" = 'CA' THEN convert_timezone('UTC', 'America/Toronto', "purchase_date_utc")
    WHEN "country_code" = 'BR' THEN convert_timezone('UTC', 'America/Sao_Paulo', "purchase_date_utc")
    WHEN "country_code" = 'UK' THEN convert_timezone('UTC', 'Europe/London', "purchase_date_utc")
    WHEN "country_code" = 'FR' THEN convert_timezone('UTC', 'Europe/Paris', "purchase_date_utc")
    WHEN "country_code" = 'ES' THEN convert_timezone('UTC', 'Europe/Madrid', "purchase_date_utc")
    WHEN "country_code" = 'SE' THEN convert_timezone('UTC', 'Europe/Stockholm', "purchase_date_utc")
    ELSE "purchase_date_utc" -- Fallback: no conversion
END AS purchase_date_local_zone, 

CASE 
    WHEN "country_code" IN ('DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'PL', 'TR', 'BE') THEN 'EU'
    ELSE "country_code"
END AS market_place_region

FROM DWH.PROD.fact_all_orders AS orders    

WHERE "marketplace" = 'AMAZON'
    AND "sales_channel" not like '%non-amazon%'
    AND "purchase_date_utc" >= '2022-01-01'
    --AND "order_status" NOT IN ('cancelled', 'null') 
    AND "order_status" IS NOT NULL
    AND "country_code" IS NOT NULL

QUALIFY ROW_NUMBER() OVER(PARTITION BY "external_order_id", orders."sku" ORDER BY "record_updated_timestamp_utc" DESC) = 1 


),


unique_order_shopify_level_data AS (
SELECT orders.*,

convert_timezone('UTC', dim."snowflake_time_zone", orders."purchase_date_utc") AS purchase_date_local_zone,

CASE 
    WHEN orders."country_code" IN ('DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'PL', 'TR', 'BE') THEN 'EU'
    ELSE orders."country_code"
END AS market_place_region



FROM DWH.PROD.fact_all_orders AS orders  
LEFT JOIN dwh.prod.brand_timezones_dim AS dim
 ON  orders."brand" = dim."brand"
    AND orders."country_code" = dim."country_code"

WHERE orders."marketplace" = 'SHOPIFY'
    
    AND orders."purchase_date_utc" >= '2022-01-01'
    AND orders."country_code" IS NOT NULL

QUALIFY ROW_NUMBER() OVER(PARTITION BY orders."external_order_id", orders."sku" ORDER BY orders."record_updated_timestamp_utc" DESC) = 1 

),


union_all_orders_data AS (

SELECT *
FROM unique_order_level_data

UNION ALL 

SELECT * 
FROM unique_order_shopify_level_data

),


order_level_clean_data AS (
SELECT 
    orders.*,
    orders."brand" AS order_table_brand,
    map."brand" AS asin_map_brand, 
    brand_map_2."brand" AS other_country_asin_map_brand,

    COALESCE(map."brand", orders."brand", brand_map_2."brand") AS combine_brand
FROM union_all_orders_data AS orders
LEFT JOIN sku_country_brand_map as map 
    ON orders."sku" = map."sku" and orders.market_place_region = map."country"
 LEFT JOIN sku_brand_map AS brand_map_2 
    ON orders."sku" = brand_map_2."sku"
),

exchange_rate_prep AS (
SELECT 
base_currency,
CASE WHEN 
transactional_currency = 'EURO' THEN 'EUR'
ELSE transactional_currency END AS transactional_currency, 
exchange_rate,
effective_date
FROM DWH.NETSUITE.foreign_exchange
WHERE BASE_CURRENCY ='USD'
QUALIFY ROW_NUMBER() OVER(PARTITION BY transactional_currency, effective_date ORDER BY BASE_CURRENCY DESC) = 1
),

cancelled_order_count_aggre AS (
SELECT 
        "marketplace", 
        combine_brand,
        "country_code",
        "sku",
        date_trunc('hour', purchase_date_local_zone) AS purchase_hour_datetime, 
        COUNT(DISTINCT CASE WHEN COALESCE("order_status",'pending') = 'cancelled' 
                            THEN "external_order_id" END) AS cancelled_order_count,


        COUNT(distinct "external_order_id") AS total_order_count
     FROM order_level_clean_data AS orders
    GROUP BY 
        "marketplace", 
        combine_brand,
        "country_code",
        "sku",
        date_trunc('hour', purchase_date_local_zone)


),


price_aggregates AS (
    SELECT 
        "marketplace", 
        combine_brand,
        "country_code",
        "sku",
        date_trunc('hour', purchase_date_local_zone) AS purchase_hour_datetime, 
        SUM("quantity") AS quantity,
        SUM("quantity" * "item_price_per_unit") AS item_price_lc, 
        SUM("quantity" * "shipping_price_per_unit") AS shipping_price_lc, 
        SUM("quantity" * "item_promotion_discount_per_unit") AS item_promo_discount, 
        SUM("quantity" * "ship_promotion_discount_per_unit") AS ship_promo_discount, 
        SUM("quantity" * "gift_wrap_price_per_unit") AS giftwrap_price_lc,

        -- Aggregating USD prices (using division by ns.exchange_rate)
        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "item_price_per_unit"
                ELSE ("quantity" * "item_price_per_unit") * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate)
            END
        ) AS item_price_usd,

        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "shipping_price_per_unit"
                ELSE ("quantity" * "shipping_price_per_unit") * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate)
            END
        ) AS shipping_price_usd,

        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "item_promotion_discount_per_unit"
                ELSE ("quantity" * "item_promotion_discount_per_unit") * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate)
            END
        ) AS item_promo_discount_usd,

        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "ship_promotion_discount_per_unit"
                ELSE ("quantity" * "ship_promotion_discount_per_unit") * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate)
            END
        ) AS ship_promo_discount_usd,

        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "gift_wrap_price_per_unit"
                ELSE ("quantity" * "gift_wrap_price_per_unit") * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate)
            END
        ) AS gift_wrap_price_usd
        
    FROM order_level_clean_data AS orders
     -- First join: exchange rate for the purchase date
    LEFT JOIN exchange_rate_prep AS ns_current
        ON orders."currency" = ns_current.transactional_currency 
        AND date_trunc('day', orders.purchase_date_local_zone) = ns_current.effective_date

     -- Second join: exchange rate for the day before the purchase date
    LEFT JOIN exchange_rate_prep AS ns_d1
        ON orders."currency" = ns_d1.transactional_currency 
        AND date_trunc('day', DATEADD(day, -1, purchase_date_local_zone)) = ns_d1.effective_date

   WHERE COALESCE(orders."order_status", 'pending') <> 'cancelled'
    GROUP BY 
        "marketplace", 
        combine_brand,
        "country_code",
        "sku",
        date_trunc('hour', purchase_date_local_zone)
),


sku_level_ops AS (SELECT 
    price_aggre."marketplace", 
    price_aggre.combine_brand,
    price_aggre."country_code",
    price_aggre."sku",
    price_aggre.purchase_hour_datetime,
    price_aggre.quantity,
    price_aggre.item_price_lc,
    price_aggre.item_price_usd,
    price_aggre.shipping_price_lc,
    price_aggre.shipping_price_usd,
    price_aggre.item_promo_discount,
    price_aggre.item_promo_discount_usd,
    price_aggre.ship_promo_discount,
    price_aggre.ship_promo_discount_usd,
    price_aggre.giftwrap_price_lc,
    price_aggre.gift_wrap_price_usd,

    cance_aggre.cancelled_order_count, 
    cance_aggre.total_order_count, 
    
    -- Ordered product sales in LC
    (COALESCE(price_aggre.item_price_lc,0) + COALESCE(price_aggre.shipping_price_lc,0) + COALESCE(price_aggre.giftwrap_price_lc,0) 
     - COALESCE(price_aggre.item_promo_discount,0) - COALESCE(price_aggre.ship_promo_discount,0)) AS ordered_product_sales_lc,

    -- Ordered product sales in USD
    (COALESCE(price_aggre.item_price_usd,0) + COALESCE(price_aggre.shipping_price_usd,0) + COALESCE(price_aggre.gift_wrap_price_usd,0)
     - COALESCE(price_aggre.item_promo_discount_usd,0) - COALESCE(price_aggre.ship_promo_discount_usd,0)) AS ordered_product_sales_usd

FROM price_aggregates AS price_aggre
LEFT JOIN cancelled_order_count_aggre AS cance_aggre
    ON price_aggre."marketplace" = cance_aggre."marketplace"
       AND price_aggre.combine_brand = cance_aggre.combine_brand
       AND price_aggre."country_code" = cance_aggre."country_code"
       AND price_aggre."sku" = cance_aggre."sku"
       AND price_aggre.purchase_hour_datetime = cance_aggre.purchase_hour_datetime
),


brand_level_aggregation AS (
    SELECT 
        sku_ops."marketplace", 
        sku_ops.combine_brand,
        sku_ops."country_code",
        
        
        sku_item."item_number", 
        sku_item."item_name",
        
        sku_ops.purchase_hour_datetime AS purchase_hour_datetime,
        
        --DATE_TRUNC('day',sku_ops.purchase_date) AS purchase_date,
        SUM(sku_ops.quantity) AS total_quantity,
        SUM(sku_ops.item_price_lc) AS total_item_price_lc,
        SUM(sku_ops.item_price_usd) AS total_item_price_usd,
        SUM(sku_ops.shipping_price_lc) AS total_shipping_price_lc,
        SUM(sku_ops.shipping_price_usd) AS total_shipping_price_usd,
        SUM(sku_ops.item_promo_discount) AS total_item_promo_discount,
        SUM(sku_ops.item_promo_discount_usd) AS total_item_promo_discount_usd,
        SUM(sku_ops.ship_promo_discount) AS total_ship_promo_discount,
        SUM(sku_ops.ship_promo_discount_usd) AS total_ship_promo_discount_usd,
        SUM(sku_ops.giftwrap_price_lc) AS total_giftwrap_price_lc,
        SUM(sku_ops.gift_wrap_price_usd) AS total_giftwrap_price_usd,

        SUM(cancelled_order_count) AS cancelled_order_count, 
        SUM(total_order_count) AS total_order_count, 

        SUM(cancelled_order_count) /  NULLIF(SUM(total_order_count), 0) AS brand_cancellation_rate,
        

        
        -- Aggregated ordered product sales in LC
        
        SUM(sku_ops.ordered_product_sales_lc * (1 + COALESCE(refund.refund_rate,0))) AS total_ordered_product_sales_lc,
        
        -- Aggregated ordered product sales in USD
        SUM(sku_ops.ordered_product_sales_usd * (1 + COALESCE(refund.refund_rate,0))) AS total_ordered_product_sales_usd
        
    FROM sku_level_ops as sku_ops
    LEFT JOIN  DWH_DEV.STAGING.BRAND_EBITDA_BRAND_REFUND_RATE AS refund
    ON sku_ops.combine_brand = refund.combine_brand
    LEFT JOIN sku_item_number_map AS sku_item
    ON sku_ops."sku" = sku_item."sku"
    GROUP BY 
        sku_ops."marketplace", 
        sku_ops.combine_brand,
        sku_item."item_number", 
        sku_item."item_name",
        sku_ops."country_code",
        sku_ops.purchase_hour_datetime
),


brand_sku_level_with_cancellation AS (

SELECT 


        brand_aggre."marketplace", 
        brand_aggre.combine_brand,
        brand_aggre."country_code",
        brand_aggre."item_number",
        brand_aggre."item_name",
        brand_aggre.purchase_hour_datetime,
        --brand_aggre.purchase_date,
        brand_aggre.total_quantity,
        brand_aggre.total_item_price_lc,
        brand_aggre.total_item_price_usd,
        brand_aggre.total_shipping_price_lc,
        brand_aggre.total_shipping_price_usd,
        brand_aggre.total_item_promo_discount,
        brand_aggre.total_item_promo_discount_usd,
        brand_aggre.total_ship_promo_discount,
        brand_aggre.total_ship_promo_discount_usd,
        brand_aggre.total_giftwrap_price_lc,
        brand_aggre.total_giftwrap_price_usd,

        brand_aggre.cancelled_order_count, 
        brand_aggre.total_order_count, 

        brand_aggre.brand_cancellation_rate,
        

        
        -- Aggregated ordered product sales in LC
        
        brand_aggre.total_ordered_product_sales_lc AS refund_ordered_product_sales_lc ,
        
        -- Aggregated ordered product sales in USD
        brand_aggre.total_ordered_product_sales_usd AS refund_ordered_product_sales_usd, 

        CASE WHEN brand_aggre.purchase_hour_datetime < DATEADD(day, -7, CURRENT_DATE()) AND 
        brand_aggre.brand_cancellation_rate > cancel_factor.CANCELLATION_RATE
        
        THEN total_ordered_product_sales_lc
        ELSE brand_aggre.total_ordered_product_sales_lc * (1- ABS(cancel_factor.CANCELLATION_RATE - brand_aggre.brand_cancellation_rate)) 
        END AS total_ordered_product_sales_lc, 

        CASE WHEN brand_aggre.purchase_hour_datetime < DATEADD(day, -7, CURRENT_DATE()) AND
        brand_aggre.brand_cancellation_rate > cancel_factor.CANCELLATION_RATE
        
        THEN total_ordered_product_sales_usd 
        
        ELSE brand_aggre.total_ordered_product_sales_usd * (1- ABS(cancel_factor.CANCELLATION_RATE - brand_aggre.brand_cancellation_rate)) 
        END AS total_ordered_product_sales_usd
        


FROM brand_level_aggregation AS brand_aggre
LEFT JOIN DWH_DEV.STAGING.BRAND_EBITDA_BRAND_CANCELLATION_FACTOR AS cancel_factor
ON brand_aggre.combine_brand = cancel_factor.brand_code 

),


brand_sku_level_with_last_day AS (
    SELECT 
        curr.*,
        ld.total_ordered_product_sales_usd AS last_year_total_ordered_product_sales_usd
    FROM brand_sku_level_with_cancellation AS curr
    LEFT JOIN brand_sku_level_with_cancellation AS ld
        ON curr.combine_brand = ld.combine_brand
           AND curr."marketplace" = ld."marketplace"
           AND curr."item_number" = ld."item_number"
           AND curr."country_code" = ld."country_code"
           

           -- Same week of the year
           AND EXTRACT(YEAR FROM ld.purchase_hour_datetime) = EXTRACT(YEAR FROM curr.purchase_hour_datetime) - 1
           AND EXTRACT(WEEK FROM ld.purchase_hour_datetime) = EXTRACT(WEEK FROM curr.purchase_hour_datetime)

           -- Same weekday (e.g., Monday -> Monday)
           AND EXTRACT(DAYOFWEEK FROM ld.purchase_hour_datetime) = EXTRACT(DAYOFWEEK FROM curr.purchase_hour_datetime)

           -- Same hour of the day
           AND EXTRACT(HOUR FROM ld.purchase_hour_datetime) = EXTRACT(HOUR FROM curr.purchase_hour_datetime)

    --WHERE DATE_TRUNC('day',curr.purchase_hour_datetime) = '2025-02-10'
),



-- channel level aggregation 
channel_level_data_aggregation AS (

SELECT 
    "marketplace", 
    combine_brand,

    -- item number -- 
    "item_number", 
    "item_name",

    
    CASE WHEN "marketplace" = 'SHOPIFY' THEN 'SHOPIFY'
    ELSE "country_code" END AS "country_code",

    CASE WHEN "marketplace" = 'SHOPIFY' THEN 'SHOPIFY'
    WHEN "marketplace" = 'AMAZON' AND "country_code" = 'US' THEN 'AMAZON US'
    WHEN "marketplace" = 'AMAZON' AND "country_code" <> 'US' THEN 'AMAZON INTL'
    ELSE NULL END AS channel, 
    purchase_hour_datetime,
    
    -- Aggregated metrics
    SUM(total_quantity) AS total_quantity,
    SUM(total_item_price_lc) AS total_item_price_lc,
    SUM(total_item_price_usd) AS total_item_price_usd,
    SUM(total_shipping_price_lc) AS total_shipping_price_lc,
    SUM(total_shipping_price_usd) AS total_shipping_price_usd,
    SUM(total_item_promo_discount) AS total_item_promo_discount,
    SUM(total_item_promo_discount_usd) AS total_item_promo_discount_usd,
    SUM(total_ship_promo_discount) AS total_ship_promo_discount,
    SUM(total_ship_promo_discount_usd) AS total_ship_promo_discount_usd,
    SUM(total_giftwrap_price_lc) AS total_giftwrap_price_lc,
    SUM(total_giftwrap_price_usd) AS total_giftwrap_price_usd,
    SUM(total_ordered_product_sales_lc) AS total_ordered_product_sales_lc,
    SUM(total_ordered_product_sales_usd) AS total_ordered_product_sales_usd,

    SUM(last_year_total_ordered_product_sales_usd) AS last_year_total_ordered_product_sales_usd,
   

    SUM(cancelled_order_count) AS cancelled_order_count, 
    SUM(total_order_count) AS total_order_count

FROM brand_sku_level_with_last_day AS ly
GROUP BY 
    "marketplace", 
    channel,
    combine_brand,
    "item_number",
    "item_name",
    "country_code", 
    purchase_hour_datetime
),





ops_with_tagrets_brand_category_map AS (

SELECT 
opst.*, 
cat_map.brand AS brand_name, 
cat_map.category AS brand_category,

FROM channel_level_data_aggregation AS opst
LEFT JOIN DWH_DEV.STAGING.BRAND_EBITDA_BRAND_CODE_CATEGORY_MAP AS cat_map
ON opst.combine_brand = cat_map.brand_code

)


SELECT *

FROM ops_with_tagrets_brand_category_map

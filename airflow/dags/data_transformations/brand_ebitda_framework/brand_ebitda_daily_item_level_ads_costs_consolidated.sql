CREATE OR R<PERSON>LACE TABLE $stage_db.brand_ebitda_daily_item_level_all_ads_cost_consolidated AS

/* ── ① ASIN-level cash-flow source ───────────────────────────────────── */
WITH asin_revenue AS (
    SELECT
        report_date          AS purchase_date,
        country_code,
        brand_code                AS combine_brand,        -- brand_code
        brand                AS brand_name,
        category             AS brand_category,
        asin,
        product_name,
        
        ABS(product_sales_total)
        + ABS(shipping_credits_total)
        + ABS(giftwrap_credits_total) AS total_ordered_product_sales_usd
    FROM $stage_db.stg_brandebitda_revenue_asin_level_consolidated_daily
), 

google_ads_spend AS (

    SELECT
    report_date, 
    country_code, 
    asin, 
    sum(total_google_ads_spend) AS total_google_ads_spend
    FROM DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_GOOGLE_ADS_SPEND
    WHERE channel IN ('AMZ-US', 'AMZ-INT')
    GROUP BY report_date, country_code, asin
), 

fb_ads_spend AS (

    SELECT 
        report_date, 
        country_code, 
        asin, 
        sum(TOTAL_FB_AD_SPEND) AS total_fb_ads_spend
    FROM DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_FACEBOOK_SPEND  
    GROUP BY report_date, country_code, asin
)

/* --------------- final SELECT with media-spend joins ------------------ */
SELECT
    ar.purchase_date,
    ar.country_code,
    ar.combine_brand,
    ar.brand_name,
    ar.brand_category,
    ar.asin,
    ar.product_name,
    ar.total_ordered_product_sales_usd,

    /* Amazon PPC spend */
    ppc.sp_ppc_spend,
    ppc.sv_ppc_spend,
    ppc.sbv_ppc_spend,
    ppc.sd_ppc_spend,
    ppc.total_ppc_spend,

    /* Google Ads spend */
    asin_google.total_google_ads_spend AS total_asin_google_ads_spend,

    /* Facebook Ads spend */

    fb_ads.total_fb_ads_spend AS total_asin_fb_ads_spend

FROM   asin_revenue                                ar

/* ---------- media-spend joins (unchanged) ---------- */

LEFT JOIN google_ads_spend asin_google
       ON ar.asin         = asin_google.asin
      AND ar.country_code = asin_google.country_code
      AND ar.purchase_date= asin_google.report_date

LEFT JOIN fb_ads_spend fb_ads
       ON ar.asin = fb_ads.asin
      AND ar.country_code  = fb_ads.country_code
      AND ar.purchase_date = fb_ads.report_date

LEFT JOIN DWH_DEV.STAGING.brand_ebitda_daily_item_level_amz_ppc_spend      ppc
       ON ar.asin         = ppc.asin
      AND ar.country_code = ppc.country_code
      AND ar.purchase_date= ppc.report_date;

CREATE OR REPLACE TRANSIENT TABLE $stage_db.create_stg_brand_ebitda_daily_item_level_publications AS

-- publications have a start date and an end date, but the cost needs to be spread across the entire month.
-- this model calculates the daily cost by first determining the total monthly cost
-- and then dividing it equally by the number of days in that month.

WITH generate_date AS (
    SELECT
        '2024-01-01'::date + row_number() over(order by 0) AS report_date
        , CASE
            WHEN DAYOFWEEK(report_date) = 7 THEN report_date -- If the date is Sunday, return the date itself
            ELSE DATE_TRUNC('WEEK', REPORT_DATE) - INTERVAL '1 day' -- Else, return to the date of the preceding Sunday
        END AS week_start_date
    FROM table(generator(rowcount => 1824))
),

monthly_brand_publications AS (
    SELECT
        brand_code,
        asin,
        DATE_TRUNC("month", TO_DATE(start_date)) AS month_start_date,
        SUM(COALESCE(publication_cost, 0)) AS monthly_publications_cost,
    FROM
        $stage_db.merge_brand_ebitda_publications
    GROUP BY ALL
),

daily_pub_cost_split_actual AS (
    SELECT
        generate_date.report_date,
        publications.month_start_date,
        publications.brand_code,
        publications.asin,
        DIV0(publications.monthly_publications_cost, EXTRACT(DAY FROM LAST_DAY(publications.month_start_date, 'month'))) AS daily_publications_cost,
    FROM
        monthly_brand_publications AS publications
    LEFT JOIN
        generate_date
            ON publications.month_start_date = DATE_TRUNC("month", generate_date.report_date)
),


-- actual daily brand publications spend by channel based on marketing budget
daily_pub_cost_split_with_mktg_split AS (
    -- 2025 marketing split
    SELECT
        publications.report_date,
        marketing_split.month_start_date,
        publications.brand_code,
        publications.asin,
        marketing_split.channel,
        marketing_split.perc_share,
        daily_publications_cost * marketing_split.perc_share AS daily_publications_cost,
    FROM
        daily_pub_cost_split_actual AS publications
    LEFT JOIN
        $stage_db.merge_brand_ebitda_marketing_split AS marketing_split
            ON marketing_split.month_start_date = publications.month_start_date
            AND marketing_split.brand_code = publications.brand_code
    WHERE
        marketing_split.marketing_activity ILIKE '%publication%'
        AND publications.report_date >= '2025-01-01'

    UNION ALL
    -- 2024 marketing split for AMZ US
    SELECT
        publications.report_date,
        DATE_TRUNC("month", publications.report_date) AS month_start_date,
        publications.brand_code,
        publications.asin,
        'AMZ-US' AS channel,
        CASE WHEN publications.brand_code != 'ZSK' THEN 0.8 ELSE 0.5 END AS perc_share,
        daily_publications_cost * perc_share AS daily_publications_cost,
    FROM
        daily_pub_cost_split_actual AS publications
    WHERE
        publications.report_date < '2025-01-01'

    UNION ALL
    -- 2024 marketing split for DTC
    SELECT
        publications.report_date,
        DATE_TRUNC("month", publications.report_date) AS month_start_date,
        publications.brand_code,
        publications.asin,
        'DTC' AS channel,
        CASE WHEN publications.brand_code != 'ZSK' THEN 0.2 ELSE 0.5 END AS perc_share,
        daily_publications_cost * perc_share AS daily_publications_cost,
    FROM
        daily_pub_cost_split_actual AS publications
    WHERE
        publications.report_date < '2025-01-01'
),

-- actual daily asin publications cost for AMZ US
asin_daily_pub_cost_split_amzus AS (
    SELECT
        publications.report_date,
        publications.month_start_date,
        publications.brand_code,
        asin_revenue.asin,
        asin_revenue.country_code,
        publications.channel,
        COALESCE(publications.daily_publications_cost * asin_revenue.asin_brand_revenue_share, 0) AS daily_publications_cost
    FROM
        daily_pub_cost_split_with_mktg_split AS publications
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON publications.report_date = asin_revenue.report_date
            AND publications.brand_code = asin_revenue.brand_code
    WHERE
        LOWER(publications.channel) = 'amz-us'
        AND asin_revenue.country_code = 'US'
        AND publications.asin IS NULL

    UNION ALL

    SELECT
        report_date,
        month_start_date,
        brand_code,
        asin,
        'US' AS country_code,
        channel,
        COALESCE(daily_publications_cost, 0) AS daily_publications_cost
    FROM
        daily_pub_cost_split_with_mktg_split
    WHERE
        LOWER(channel) = 'amz-us'
        AND country_code = 'US'
        AND asin IS NOT NULL -- to get actual publications cost assigned to ASIN
),

-- actual daily asin publications cost for AMZ INT
-- for simple attribution, total publication spend is attributed to CA
asin_daily_pub_cost_split_amzint AS (
    SELECT
        publications.report_date,
        publications.month_start_date,
        publications.brand_code,
        asin_revenue.asin,
        asin_revenue.country_code,
        publications.channel,
        COALESCE(publications.daily_publications_cost * asin_revenue.asin_brand_revenue_share, 0) AS daily_publications_cost
    FROM
        daily_pub_cost_split_with_mktg_split AS publications
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON publications.report_date = asin_revenue.report_date
            AND publications.brand_code = asin_revenue.brand_code
    WHERE
        LOWER(publications.channel) = 'amz-int'
        AND asin_revenue.country_code = 'CA'
        AND publications.asin IS NULL

    UNION ALL

    SELECT
        report_date,
        month_start_date,
        brand_code,
        asin,
        'CA' AS country_code,
        channel,
        COALESCE(daily_publications_cost, 0) AS daily_publications_cost
    FROM
        daily_pub_cost_split_with_mktg_split
    WHERE
        LOWER(channel) = 'amz-int'
        AND country_code = 'CA'
        AND asin IS NOT NULL -- to get actual publications cost assigned to ASIN
),

actual_data_max_month AS (
    SELECT
        MAX(month_start_date) AS latest_month_with_actual
    FROM
        monthly_brand_publications
),

-- brand level daily publications spend estimate based on marketing budget
daily_pub_cost_split_estimate AS (
    SELECT
        generate_date.report_date,
        marketing_split.month_start_date,
        marketing_split.brand_code,
        marketing_split.channel,
        marketing_split.perc_share,
        DIV0(marketing_split.marketing_budget, EXTRACT(DAY FROM LAST_DAY(marketing_split.month_start_date, 'month'))) * marketing_split.perc_share AS daily_publications_budget
    FROM
        $stage_db.merge_brand_ebitda_marketing_split AS marketing_split
    LEFT JOIN
        generate_date
            ON marketing_split.month_start_date = DATE_TRUNC("month", generate_date.report_date)
    WHERE marketing_split.month_start_date > (SELECT latest_month_with_actual FROM actual_data_max_month)
),

-- amz us
asin_daily_pub_cost_split_estimate_amzus AS (
    SELECT
        estimate.report_date,
        estimate.month_start_date,
        estimate.brand_code,
        asin_revenue.asin,
        asin_revenue.country_code,
        estimate.channel,
        COALESCE(estimate.daily_publications_budget * asin_revenue.asin_brand_revenue_share, 0) AS daily_publications_cost_estimate
    FROM
        daily_pub_cost_split_estimate AS estimate
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON estimate.report_date = asin_revenue.report_date
            AND estimate.brand_code = asin_revenue.brand_code
    WHERE
        LOWER(estimate.channel) = 'amz-us'
        AND asin_revenue.country_code = 'US'
),

all_channels_consolidated AS (
    SELECT
        report_date,
        month_start_date,
        brand_code,
        asin,
        country_code,
        channel,
        'actual' AS cost_type,
        SUM(COALESCE(daily_publications_cost, 0)) AS daily_publications_cost
    FROM
        asin_daily_pub_cost_split_amzus
    GROUP BY ALL

    UNION ALL

     SELECT
        report_date,
        month_start_date,
        brand_code,
        asin,
        country_code,
        channel,
        'actual' AS cost_type,
        SUM(COALESCE(daily_publications_cost, 0)) AS daily_publications_cost
    FROM
        asin_daily_pub_cost_split_amzint
    GROUP BY ALL

    UNION ALL

    SELECT
        report_date,
        month_start_date,
        brand_code,
        asin,
        country_code,
        channel,
        'estimate' AS cost_type,
        SUM(COALESCE(daily_publications_cost_estimate, 0)) AS daily_publications_cost
    FROM
        asin_daily_pub_cost_split_estimate_amzus
    GROUP BY ALL
)

SELECT
    MD5(
        CAST(
            COALESCE(CAST(report_date AS VARCHAR), '') || '-' ||
            COALESCE(CAST(brand_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(asin AS VARCHAR), '') || '-' ||
            COALESCE(CAST(country_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(channel AS VARCHAR), '') AS VARCHAR
        )
    ) AS pk,
    *
FROM
    all_channels_consolidated
;

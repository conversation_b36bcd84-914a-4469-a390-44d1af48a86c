CREATE TABLE IF NOT EXISTS $stage_db.merge_brand_ebitda_publications AS
    SELECT

        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_brand_ebitda_publications
    WHERE 1 = 0;

BEGIN TRANSACTION;

MERGE INTO
    $stage_db.merge_brand_ebitda_publications AS tgt
USING
    $stage_db.dedupe_brand_ebitda_publications AS src

        ON 1 = 1
       AND src.pk = tgt.pk

WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._airbyte_raw_id = src._airbyte_raw_id,
    tgt._airbyte_extracted_at = src._airbyte_extracted_at,
    tgt._airbyte_generation_id = src._airbyte_generation_id,
    tgt._airbyte_meta = src._airbyte_meta,
    tgt.asin = src.asin,
    tgt.end_date = src.end_date,
    tgt.brand_code = src.brand_code,
    tgt.claim_code = src.claim_code,
    tgt.start_date = src.start_date,
    tgt.publication = src.publication,
    tgt.publication_cost = src.publication_cost,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    asin,
    end_date,
    brand_code,
    claim_code,
    start_date,
    publication,
    publication_cost,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._airbyte_raw_id,
    src._airbyte_extracted_at,
    src._airbyte_generation_id,
    src._airbyte_meta,
    src.asin,
    src.end_date,
    src.brand_code,
    src.claim_code,
    src.start_date,
    src.publication,
    src.publication_cost,
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);

-- delete all entries that are not from the latest airbyte update
DELETE FROM $stage_db.merge_brand_ebitda_publications
WHERE (record_updated_timestamp_utc) NOT IN (
    SELECT MAX(record_updated_timestamp_utc)
    FROM $stage_db.merge_brand_ebitda_publications
);

COMMIT;

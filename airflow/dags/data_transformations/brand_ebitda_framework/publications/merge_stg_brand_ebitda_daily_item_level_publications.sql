CREATE TABLE IF NOT EXISTS $stage_db.merge_stg_brand_ebitda_daily_item_level_publications AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.create_stg_brand_ebitda_daily_item_level_publications
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_stg_brand_ebitda_daily_item_level_publications AS tgt
USING
    (
        SELECT
            *
        FROM $stage_db.create_stg_brand_ebitda_daily_item_level_publications
        QUALIFY ROW_NUMBER() OVER (PARTITION BY pk ORDER BY report_date DESC) = 1 -- dedupe
    ) AS src
        ON tgt.pk = src.pk

WHEN MATCHED THEN
    -- Update the record if it already exists
    UPDATE SET
        tgt.daily_publications_cost = src.daily_publications_cost,
        tgt.record_updated_timestamp_utc = SYSDATE()  -- Update the timestamp

WHEN NOT MATCHED THEN
    -- Insert a new record if it does not exist
    INSERT (
        pk,
        report_date,
        month_start_date,
        brand_code,
        asin,
        country_code,
        channel,
        cost_type,
        daily_publications_cost,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        src.pk,
        src.report_date,
        src.month_start_date,
        src.brand_code,
        src.asin,
        src.country_code,
        src.channel,
        src.cost_type,
        src.daily_publications_cost,
        SYSDATE(),
        SYSDATE()
    );

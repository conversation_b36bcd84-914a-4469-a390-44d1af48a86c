CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_brand_ebitda_publications AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(brand_code AS VARCHAR), ''), '-',
            COALESCE(CAST(publication AS VARCHAR), ''), '-',
            COALESCE(CAST(start_date AS VARCHAR), ''), '-',
            COALESCE(CAST(end_date AS VARCHAR), ''), '-',
            COALESCE(CAST(asin AS VARCHAR), '')
            )) AS pk,
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        asin,
        end_date,
        brand_code,
        claim_code,
        start_date,
        publication,
        publication_cost,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_brand_ebitda_publications
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY _airbyte_extracted_at DESC NULLS LAST) = 1
);
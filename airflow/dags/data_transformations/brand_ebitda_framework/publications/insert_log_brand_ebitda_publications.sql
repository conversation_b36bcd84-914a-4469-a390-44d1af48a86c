CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_brand_ebitda_publications AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_brand_ebitda_publications
    WHERE 1 = 0;

INSERT INTO $raw_db.log_brand_ebitda_publications (
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    asin,
    end_date,
    brand_code,
    claim_code,
    start_date,
    publication,
    publication_cost,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        asin,
        end_date,
        brand_code,
        claim_code,
        start_date,
        publication,
        publication_cost,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_brand_ebitda_publications;
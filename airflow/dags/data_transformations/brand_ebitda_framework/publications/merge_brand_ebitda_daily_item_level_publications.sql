CREATE TABLE IF NOT EXISTS $curated_db.brand_ebitda_daily_item_level_publications (
    pk VARCHAR,
    report_date DATE,
    month_start_date DATE,
    brand_code VARCHAR,
    asin VARCHAR,
    country_code VARCHAR,
    channel VARCHAR,
    cost_type VARCHAR,
    daily_publications_cost FLOAT,
    record_created_timestamp_utc TIMESTAMP DEFAULT SYSDATE(),
    record_updated_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);

BEGIN TRANSACTION;

MERGE INTO $curated_db.brand_ebitda_daily_item_level_publications AS t
USING $stage_db.merge_stg_brand_ebitda_daily_item_level_publications AS s
ON
    t.pk = s.pk

WHEN MATCHED THEN
    UPDATE SET
        t.cost_type = s.cost_type,
        t.daily_publications_cost = s.daily_publications_cost,
        t.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
    INSERT (
        pk,
        report_date,
        month_start_date,
        brand_code,
        asin,
        country_code,
        channel,
        cost_type,
        daily_publications_cost,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        s.pk,
        s.report_date,
        s.month_start_date,
        s.brand_code,
        s.asin,
        s.country_code,
        s.channel,
        s.cost_type,
        s.daily_publications_cost,
        s.record_created_timestamp_utc,
        s.record_updated_timestamp_utc
    );

DELETE FROM $curated_db.brand_ebitda_daily_item_level_publications
WHERE (pk, record_updated_timestamp_utc) NOT IN (
    SELECT pk, MAX(record_updated_timestamp_utc)
    FROM $curated_db.brand_ebitda_daily_item_level_publications
    GROUP BY pk
);

COMMIT;

CREATE OR REPLACE TABLE DWH_DEV.STAGING.brandebitda_wbr_international_asin_level_margins_hybrid AS 
 
 WITH 

 /* ---------- 0. helper calendar ---------- */
generate_date AS (
    SELECT
        '2023-01-01'::DATE + ROW_NUMBER() OVER (ORDER BY 0) AS report_date ,
        DATE_TRUNC('month', report_date)                    AS month_start_date
    FROM TABLE(GENERATOR(ROWCOUNT => 1500))
),

/* ---------- 1. monthly fee per ASIN ---------- */
est_storage_fee_monthly AS (
    SELECT
        "snapshot_date"  AS snapshot_date ,
        "month_of_charge" AS month_of_charge ,
        "asin"            AS asin ,
        "country_code"    AS country_code ,
        "brand"           AS brand ,
        "currency"        AS local_currency ,
        SUM("estimated_monthly_storage_fee")                AS estimated_monthly_storage_fee_local
    FROM DWH.PROD.FACT_AMAZON_FBA_STORAGE_FEES
    GROUP BY ALL
),

/* ---------- 2. daily fee (USD) + plug allocation ---------- */
est_storage_fee_daily AS (
    SELECT DISTINCT
        snapshot_date ,
        month_of_charge ,
        TO_DATE(month_of_charge || '-01')                   AS adj_month_of_charge ,
        gd.report_date ,
        asin ,
        country_code ,
        brand ,
        local_currency ,

        /* base daily fee in local currency */
        SUM( DIV0NULL(estimated_monthly_storage_fee_local ,
                       EXTRACT(DAY FROM LAST_DAY(adj_month_of_charge))) )
            AS estimated_daily_storage_fee_local ,

        /* FX rate and base fee in USD */
        MAX(COALESCE(forex.exchange_rate , 1))              AS fx_rate ,
        estimated_daily_storage_fee_local * fx_rate         AS estimated_daily_storage_fee_usd ,

        /* country-day total fee in USD (window) */
        SUM(estimated_daily_storage_fee_local * fx_rate)
          OVER (PARTITION BY gd.report_date) AS total_daily_fee_usd_for_share ,

        /* ASIN share of that country-day total */
        DIV0NULL( estimated_daily_storage_fee_local * fx_rate ,
                  total_daily_fee_usd_for_share )              AS asin_fee_share ,

        /* per-day plug for the country : 6 000 / days_in_month */
        DIV0NULL(6000 , EXTRACT(DAY FROM LAST_DAY(gd.report_date)))
                                                         AS plug_per_day_usd ,

        /* plug slice for this ASIN */
        asin_fee_share * plug_per_day_usd                  AS plug_usd_for_asin ,

        /* final daily fee incl. plug */
        estimated_daily_storage_fee_usd + plug_usd_for_asin
                                                         AS estimated_daily_storage_fee_usd_with_plug
    FROM est_storage_fee_monthly              AS storage
    JOIN generate_date                        AS gd
      ON TO_DATE(storage.month_of_charge || '-01')
     = DATE_TRUNC('month', gd.report_date)
    LEFT JOIN DWH.NETSUITE.FOREIGN_EXCHANGE   AS forex
      ON gd.report_date           = forex.effective_date
     AND storage.local_currency   = forex.transactional_currency
     AND forex.base_currency      = 'USD'
    WHERE country_code <> 'US'        -- INTL only
    GROUP BY ALL
    ORDER BY report_date DESC
),

/* ---------- 3. monthly roll-up ---------- */
amazon_storage_raw_report AS (
SELECT
    DATE_TRUNC('day', report_date)                    AS report_date ,
    asin ,
    country_code ,
   SUM(estimated_daily_storage_fee_usd_with_plug) AS storage_fees_usd_with_plug
FROM est_storage_fee_daily
GROUP BY ALL
order by 1 desc
 ),
 
 avg_month_asin_level_fba_fees AS (
SELECT
    asin, 
    country_code, 
    (-1 * discount_avg_total_fba_fees_per_unit) AS avg_total_fba_fee_per_unit, 
    (-1 * discount_avg_fulfillment_fees_per_unit) AS avg_fulfillment_fee_per_unit ,
    (-1 * discount_avg_selling_fees_per_unit) AS avg_selling_fees_per_unit
FROM
   DWH_DEV.STAGING.BRAND_EBITDA_FBA_FEES_PER_UNIT_AVG_MONTHLY_MANUAL_LOAD
QUALIFY ROW_NUMBER() OVER(PARTITION BY asin, country_code ORDER BY discount_avg_total_fba_fees_per_unit desc) = 1
),
 
revenue_ops_fba_fees AS (
 
 SELECT 
        --ops_rev.marketplace, 
        brand_name,

        ops_rev.asin,

        ops_rev.country_code,
        ops_rev.market_place_region,
        --ops_rev.channel,
        
        ops_rev.purchase_date,
        
        ops_rev.total_quantity,
        ops_rev.refund_quantity, 
        ops_rev.net_quantity,
    
        ops_rev.total_item_price_lc,
        ops_rev.total_item_price_usd,
        ops_rev.total_shipping_price_lc,
        ops_rev.total_shipping_price_usd,
        ops_rev.total_item_promo_discount,
        ops_rev.total_item_promo_discount_usd,
        ops_rev.total_ship_promo_discount,
        ops_rev.total_ship_promo_discount_usd,
        ops_rev.total_giftwrap_price_lc,
        ops_rev.total_giftwrap_price_usd,
    
        ops_rev.total_order_count, 
    
        ops_rev.ordered_product_sales_lc, 
        
        ops_rev.ordered_product_sales_usd, 
    
        ops_rev.refund_item_price_lc, 
        ops_rev.refund_shipping_lc, 
    
        ops_rev.refund_item_price_usd, 
        ops_rev.refund_shipping_usd, 
    
        ops_rev.refund_ordered_product_sales_lc, 
    
        ops_rev.refund_ordered_product_sales_usd,
    
        ops_rev.net_ordered_product_sales_lc, 
    
        ops_rev.net_ordered_product_sales_usd, 

        COALESCE(avg_fba_fees.avg_total_fba_fee_per_unit, fba_fees.estimated_fee_total_usd) AS estimated_fee_total_per_unit_usd,
        COALESCE(avg_fba_fees.avg_selling_fees_per_unit, fba_fees.estimated_referral_fee_per_unit_usd) AS estimated_referral_fee_per_unit_usd, 
        COALESCE(avg_fba_fees.avg_fulfillment_fee_per_unit, fba_fees.expcted_fulfilment_fee_per_unit_usd) AS expcted_fulfilment_fee_per_unit_usd ,
        

  

        ops_rev.total_quantity * COALESCE(avg_fba_fees.avg_selling_fees_per_unit, fba_fees.estimated_referral_fee_per_unit_usd) AS gross_referral_fees_usd, 
        
        ops_rev.refund_quantity * COALESCE(avg_fba_fees.avg_selling_fees_per_unit, fba_fees.estimated_referral_fee_per_unit_usd) AS refund_referral_fees_usd, 
        
        
        ops_rev.net_quantity * COALESCE(avg_fba_fees.avg_selling_fees_per_unit, 
        fba_fees.estimated_referral_fee_per_unit_usd) AS net_referral_fees_usd,

        ops_rev.total_quantity * COALESCE(avg_fba_fees.avg_fulfillment_fee_per_unit, fba_fees.expcted_fulfilment_fee_per_unit_usd) AS gross_fulfilmentl_fees_usd, 

        ops_rev.refund_quantity * COALESCE(avg_fba_fees.avg_fulfillment_fee_per_unit, fba_fees.expcted_fulfilment_fee_per_unit_usd) AS refund_fulfilmentlfees_usd, 
        
       ops_rev.net_quantity * COALESCE(avg_fba_fees.avg_fulfillment_fee_per_unit, fba_fees.expcted_fulfilment_fee_per_unit_usd) AS net_fulfilment_fees_usd,

        COALESCE(gross_referral_fees_usd, 0) + COALESCE(gross_fulfilmentl_fees_usd, 0) AS total_gross_fba_fees_usd, 
        
        COALESCE(refund_referral_fees_usd, 0) + COALESCE(refund_fulfilmentlfees_usd, 0) AS total_refund_fba_fees_usd, 
        
        COALESCE(net_referral_fees_usd, 0) + COALESCE(net_fulfilment_fees_usd, 0) AS total_net_fba_fees_usd

        

   FROM DWH_DEV.STAGING.brandebitda_wbr_international_ops_revenue_asin_daily AS ops_rev
   
   LEFT JOIN DWH_DEV.STAGING.brandebitda_wbr_international_fba_fee_preview AS fba_fees
   ON ops_rev.asin = fba_fees.asin
   AND ops_rev.market_place_region = fba_fees.country_region
   AND ops_rev.purchase_date = fba_fees.report_start_date 

   LEFT JOIN avg_month_asin_level_fba_fees AS avg_fba_fees
   ON ops_rev.asin = avg_fba_fees.asin
   AND ops_rev.country_code = avg_fba_fees.country_code

), 


-- asin_brand_map AS (
-- SELECT 
-- DISTINCT
-- asin AS asin, 
-- brand_code
-- FROM 

-- ), 


 wbr_asin_map_latest AS (
    SELECT *
    FROM (
        SELECT
            child_asin,
            country_code,
            brand,
            category,
            niche,
            product_type,
            brand_grouping,
            brand_manager,
            account_title,
            ROW_NUMBER() OVER (
                PARTITION BY child_asin, country_code
                ORDER BY record_updated_timestamp_utc DESC,
                         record_created_timestamp_utc DESC
            ) AS rn
        FROM DWH_DEV.STAGING.merge_brand_ebitda_asin_mapping
        WHERE child_asin IS NOT NULL
    )
    WHERE rn = 1
)

----------------xx----------------- COGS --------------------------xx------------------


/* ─────────── 1. nearest-PAST COGS ─────────── */
, past_cogs AS (
    SELECT
        a.purchase_date,
        a.asin               AS asin,
        a.country_code, 
        COALESCE(c.unit_rate,fall_back_c.unit_rate)   AS unit_cogs,
        COALESCE(c.month , fall_back_c.month)          AS cogs_month,
        ROW_NUMBER() OVER (
            PARTITION BY a.purchase_date, a.asin, a.country_code
            ORDER BY COALESCE(c.month , fall_back_c.month)  DESC         -- latest ≤ report_day
        ) AS rn
    FROM  revenue_ops_fba_fees a
    LEFT JOIN DWH_DEV.STAGING.MERGE_BRAND_EBITDA_COGS c
           ON c.asin      = a.asin
          AND c.geography = a.country_code
          AND c.month     <= a.purchase_date
    LEFT JOIN DWH_DEV.STAGING.MERGE_BRAND_EBITDA_COGS fall_back_c
           ON fall_back_c.asin      = a.asin
          AND fall_back_c.geography = a.market_place_region
          AND fall_back_c.month     <= a.purchase_date

QUALIFY rn = 1
)

/* ─────────── 2. nearest-FUTURE COGS (only if no past) ─────────── */
, future_cogs AS (
    SELECT
        a.purchase_date,
        a.asin               AS asin,
        a.country_code, 
        COALESCE(c.unit_rate , fall_back_c.unit_rate)   AS unit_cogs,
        COALESCE(c.month , fall_back_c.month)   AS cogs_month,
        ROW_NUMBER() OVER (
            PARTITION BY a.purchase_date, a.asin, a.country_code
            ORDER BY COALESCE(c.month , fall_back_c.month)  ASC          -- earliest > report_day
        ) AS rn
    FROM revenue_ops_fba_fees a
    LEFT JOIN DWH_DEV.STAGING.MERGE_BRAND_EBITDA_COGS c
           ON c.asin      = a.asin
          AND c.geography = a.country_code
          AND c.month     >  a.purchase_date
    LEFT JOIN DWH_DEV.STAGING.MERGE_BRAND_EBITDA_COGS fall_back_c
           ON fall_back_c.asin      = a.asin
          AND fall_back_c.geography = a.market_place_region
          AND fall_back_c.month     > a.purchase_date

QUALIFY rn = 1
)
/* ─────────── 3. final choice ─────────── */
, resolved_cogs AS (
    SELECT
        COALESCE(p.purchase_date , f.purchase_date ) AS purchase_date,
        COALESCE(p.country_code, f.country_code) AS country_code,
        COALESCE(p.asin , f.asin) AS asin,
        p.unit_cogs AS past_unit_cogs, 
        f.unit_cogs AS future_unit_cogs,
        
        COALESCE(p.unit_cogs  , f.unit_cogs  ) AS chosen_cogs,
        COALESCE(p.cogs_month , f.cogs_month ) AS chosen_month
    FROM past_cogs  p
    FULL OUTER JOIN future_cogs f
           ON f.purchase_date = p.purchase_date
          AND f.asin       = p.asin
          AND f.country_code = p.country_code
    WHERE COALESCE(p.asin , f.asin) is not null
),




fallback_cogs AS (

    SELECT 
        purchase_date, 
        asin, 
        chosen_cogs AS fallback_cogs
    FROM resolved_cogs
    WHERE country_code = 'CA'

), 


fallback_cogs_uk AS (

    SELECT 
        purchase_date, 
        asin, 
        chosen_cogs AS fallback_cogs
    FROM resolved_cogs
    WHERE country_code = 'UK'

)

, amazon_revenue_fba_fees_cogs_ns_joined AS (

SELECT
    a.*,

    /*COGS captured directly from NetSuite mapping */
   COALESCE(rc.chosen_cogs, flc.fallback_cogs, flc_uk.fallback_cogs)  AS latest_cogs_from_ns,
   
    rc.chosen_month  AS month_of_latest_cogs_from_ns,

    /* Brand-day COGS-to-revenue ratio (%).                          *
       • Includes only rows that *do* have chosen_cogs.                   *
       • NULLIF avoids divide-by-zero.                                    */

    SUM(COALESCE(latest_cogs_from_ns * a.total_quantity, 0)) 
        OVER (PARTITION BY a.purchase_date, a.brand_name)      /
    NULLIF(
        SUM(a.net_ordered_product_sales_usd) 
            OVER (PARTITION BY a.purchase_date, a.brand_name)
    ,0)
        AS avg_brand_day_ns_cogs,   -- kept same column name

    -- SUM(a.net_ordered_product_sales_usd) OVER(PARTITION BY a.purchase_date, a.asin, a.country_code )
    -- /NULLIF(SUM(a.net_ordered_product_sales_usd) OVER(PARTITION BY a.purchase_date, a.country_code), 0) 
    -- AS asin_revenue_contribution,

    -- asin_revenue_contribution * 50000/30 AS tooling_freight_plug,


    MAX(DAY(LAST_DAY(a.purchase_date)))
        OVER (PARTITION BY DATE_TRUNC('month', a.purchase_date))  AS days_in_month,

    a.net_ordered_product_sales_usd 
    / NULLIF(SUM(a.net_ordered_product_sales_usd) 
            OVER (PARTITION BY a.purchase_date), 0) AS row_daily_rev_share,

    (row_daily_rev_share * 50000) / NULLIF(days_in_month, 0) AS tooling_freight_plug,

    

    /* Final COGS for ordered units.                                  *
       • Use unit COGS when present, else brand % × row revenue.           */

    CASE
        WHEN latest_cogs_from_ns IS NOT NULL
             THEN (latest_cogs_from_ns * a.total_quantity)
        ELSE (avg_brand_day_ns_cogs * a.net_ordered_product_sales_usd)
    END AS calculated_cogs,

    /* Unsellable returns @ 70 % write-off.                           */
    CASE
        WHEN a.refund_quantity = 0
             THEN 0
        ELSE latest_cogs_from_ns * a.refund_quantity * 0.7
    END AS cogs_from_unsellable_returns,

    /* Sellable returns refund @ -30 %.                               */
    CASE
        WHEN a.refund_quantity = 0
             THEN 0 
        ELSE latest_cogs_from_ns * a.refund_quantity * 0.3
    END AS cogs_refund_from_sellable_returns

FROM revenue_ops_fba_fees a
LEFT JOIN resolved_cogs rc
       ON rc.purchase_date = a.purchase_date
      AND rc.asin       = a.asin
      AND rc.country_code  = a.country_code
LEFT JOIN fallback_cogs AS flc 
      ON flc.purchase_date = a.purchase_date
      AND flc.asin       = a.asin
LEFT JOIN fallback_cogs_uk AS flc_uk
      ON flc_uk.purchase_date = a.purchase_date
      AND flc_uk.asin       = a.asin


),
-----------------xx-----------------COGS END -----------------xx------------------


amz_3pl AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(CASE WHEN activity = 'storage' THEN daily_amz_3pl_fee ELSE 0 END) AS daily_3pl_storage,
        SUM(CASE WHEN activity = 'inbound' THEN daily_amz_3pl_fee ELSE 0 END) AS daily_3pl_inbound,
        SUM(CASE WHEN activity = 'outbound' THEN daily_amz_3pl_fee ELSE 0 END) AS daily_3pl_outbound,
        SUM(CASE WHEN activity = 'amazon_freight' THEN daily_amz_3pl_fee ELSE 0 END) AS daily_3pl_freight,
        SUM(CASE WHEN activity = 'air_freight' THEN daily_amz_3pl_fee ELSE 0 END) AS daily_3pl_airfreight,
        COALESCE(daily_3pl_storage,0) + COALESCE(daily_3pl_inbound,0) + COALESCE(daily_3pl_outbound,0) AS daily_3pl_total
    FROM

      DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_3PL_FEES
    GROUP BY ALL
),

------------stroage fees fix ----------------------
base_storage_fees AS (
    SELECT
        report_date,
        asin,
        country_code,
        actual_storage_fee_usd,
        estimated_storage_fee_usd,
        total_daily_storage_fee_usd
    FROM DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_AMAZON_STORAGE_FEES
    WHERE total_onhand_inventory_country > 0
),

eu_allocation_factors AS (
    SELECT
        report_date,
        asin,
        COUNT(DISTINCT country_code) AS eu_country_count
    FROM base_storage_fees
    WHERE country_code IN ('DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'PL', 'TR', 'BE')
    AND actual_storage_fee_usd > 0
    GROUP BY report_date, asin
),

amz_storage AS (
    SELECT
        b.report_date,
        b.asin,
        b.country_code,
        SUM(b.actual_storage_fee_usd) AS daily_amz_storage_fee_actual,
        SUM(b.estimated_storage_fee_usd) AS daily_amz_storage_fee_estimate,
        SUM(b.total_daily_storage_fee_usd) AS raw_storage_fee,

        CASE 
            WHEN b.country_code IN ('DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'PL', 'TR', 'BE')
            THEN SUM(b.total_daily_storage_fee_usd) / NULLIF(e.eu_country_count, 0)
            ELSE SUM(b.total_daily_storage_fee_usd)
        END AS total_amz_storage_fee
    FROM base_storage_fees b
    LEFT JOIN eu_allocation_factors e
        ON b.report_date = e.report_date AND b.asin = e.asin
    GROUP BY b.report_date, b.asin, b.country_code, e.eu_country_count
),



-- amz_storage AS (
--     SELECT
--         report_date,
--         asin,
--         country_code,
--         SUM(actual_storage_fee_usd) AS daily_amz_storage_fee_actual,
--         SUM(estimated_storage_fee_usd) AS daily_amz_storage_fee_estimate,
--         SUM(total_daily_storage_fee_usd) AS daily_amz_storage_fee,
--         --CASE WHEN country_code IN ('DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'PL', 'TR', 'BE')
        
        
--         daily_amz_storage_fee AS total_amz_storage_fee
--     FROM
--         DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_AMAZON_STORAGE_FEES
--     GROUP BY ALL
-- ),

google_ads AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(google_ad_spend) AS google_ad_spend_api,
        SUM(misfit_agency_spend) AS misfit_agency_cost,
        google_ad_spend_api + misfit_agency_cost AS daily_google_ad_spend
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_GOOGLE_ADS_SPEND
    WHERE country_code != 'N/A'
    GROUP BY ALL
),

facebook_ads AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(fb_ad_spend) AS facebook_ad_spend_api,
        SUM(misfit_agency_cost) AS misfit_agency_cost,
        SUM(fb_ad_spend) + SUM(misfit_agency_cost) AS daily_fb_ad_spend
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_FACEBOOK_SPEND
    GROUP BY ALL
),

amz_dsp AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(daily_dsp_cost_usd) AS daily_dsp_cost, 
        SUM(daily_dsp_sales_usd) AS daily_dsp_sales
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_DSP
    GROUP BY ALL
),

affiliates AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(daily_affiliate_cost) AS daily_affiliate_cost
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_AFFILIATES_COST
    GROUP BY ALL
),

influencers AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(daily_influencer_cost) AS daily_influencer_cost
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_INFLUENCER
    GROUP BY ALL
),

deal_and_coupon AS (
    SELECT
        purchased_date,
        asin,
        country_code,
        SUM(coupon_fee) AS coupon,
        SUM(deal_fee) AS deal,
        coupon + deal AS daily_deal_and_coupon_fee
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_DEAL_COUPON
    GROUP BY ALL
),

publications AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(daily_publications_cost) AS daily_publications_cost
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_PUBLICATIONS
    GROUP BY ALL
),

royalties AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(daily_royalty_cost) AS daily_royalty_cost
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_ROYALTIES

    GROUP BY ALL
),

tiktok AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(daily_tiktok_ad_spend) AS daily_tiktok_ad_spend
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_TIKTOK_AD_SPEND
    GROUP BY ALL
),

ugc AS (
    SELECT
        report_date,
        asin,
        country_code,
        SUM(daily_ugc_cost) AS daily_ugc_cost
    FROM
        DWH_DEV.PROD.BRAND_EBITDA_DAILY_ITEM_LEVEL_UGC

    GROUP BY ALL
)


, ads_cte AS (
    SELECT
        DATE_TRUNC('day', report_date) AS report_date,
        country_code,
        asin,
        SUM(ABS(sp_ppc_spend))               AS sp_ppc_spend,
        SUM(ABS(sv_ppc_spend))              AS sv_ppc_spend,
        SUM(ABS(sbv_ppc_spend))             AS sbv_ppc_spend,
        SUM(ABS(sd_ppc_spend))               AS sd_ppc_spend,
        SUM(ABS(total_ppc_spend))            AS total_ppc_spend,

        SUM(sp_ppc_sales) AS sp_ppc_sales, 
        SUM(sb_ppc_sales) AS sb_ppc_sales,
        SUM(sbv_ppc_sales) AS sbv_ppc_sales, 
        SUM(sd_ppc_sales) AS sd_ppc_sales, 
        SUM(total_ppc_sales) AS total_ppc_sales


        -- SUM(ABS(total_asin_google_ads_spend)) AS google_ads_spend,
        -- SUM(ABS(total_asin_fb_ads_spend))     AS facebook_ads_spend
    --FROM DWH_DEV.STAGING.brand_ebitda_daily_item_level_all_ads_cost_consolidated
    FROM DWH_DEV.STAGING.brand_ebitda_daily_item_level_amz_ppc_spend
    GROUP BY report_date, country_code, asin
),

giveaways AS (

    SELECT 
        report_date,
        country_code,
        child_asin,
        SUM(total_giveaways_daily) AS total_giveaways
    FROM DWH_DEV.STAGING.STG_BRANDEBITDA_GIVEAWAYS_CONSOLIDATED_US_DAILY 
    GROUP BY 
    report_date,
    country_code,
    child_asin
),

consolidated_mapping AS (

    SELECT DISTINCT
    purchase_date AS report_date, 
    asin, 
    country_code
    FROM revenue_ops_fba_fees

    UNION

    SELECT DISTINCT 
        report_date,
        asin,
        country_code
    FROM ads_cte

    UNION
    
    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM amz_3pl
    
    UNION
    
    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM google_ads
    
    UNION
        
    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM facebook_ads
    
    UNION
    
    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM amz_dsp
    
    UNION
        
    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM affiliates
    
    UNION
    
    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM influencers
    
    UNION
    
    SELECT DISTINCT
        purchased_date,
        asin,
        country_code
    FROM deal_and_coupon
    
    UNION
    
    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM publications
    
    UNION
    
    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM royalties
    
    UNION
    
    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM tiktok
    
    UNION
        
    SELECT DISTINCT
        report_date,
        asin,
        country_code
    FROM ugc
)


SELECT
    
    cm.report_date,
    cm.country_code,
    CASE 
        WHEN cm.country_code IN ('DE', 'FR', 'ES', 'IT', 'BE', 'SE', 'TR', 'NL', 'PL')
        THEN 'EU'
    ELSE cm.country_code END AS MARKETPLACE, 
    cm.asin,
    brand_map.brand AS brand_name,


        amz_agg.total_quantity AS QUANTITY_FROM_ORDERS,
        amz_agg.refund_quantity AS QUANTITY_FROM_RETURNS, 
        amz_agg.net_quantity AS NET_QUANTITY,
    
        amz_agg.total_item_price_lc,
        amz_agg.total_item_price_usd AS PRODUCT_SALES_FROM_ORDERS,
        amz_agg.total_shipping_price_lc,
        amz_agg.total_shipping_price_usd AS SHIPPING_CREDITS_FROM_ORDERS,
        amz_agg.total_item_promo_discount,
        amz_agg.total_item_promo_discount_usd,
        amz_agg.total_ship_promo_discount,
        amz_agg.total_ship_promo_discount_usd,
        amz_agg.total_giftwrap_price_lc,
        amz_agg.total_giftwrap_price_usd AS GIFTWRAP_CREDITS_FROM_ORDERS,

        COALESCE(amz_agg.total_item_promo_discount_usd,0)+ COALESCE(amz_agg.total_ship_promo_discount_usd,0) AS PROMO_REBATES_FROM_ORDERS,
    
        amz_agg.total_order_count, 
    
        amz_agg.ordered_product_sales_lc, 
        
        amz_agg.ordered_product_sales_usd, 
    
        amz_agg.refund_item_price_lc, 
        amz_agg.refund_shipping_lc, 
    
        amz_agg.refund_item_price_usd, 
        amz_agg.refund_shipping_usd, 
    
        amz_agg.refund_ordered_product_sales_lc, 
    
        amz_agg.refund_ordered_product_sales_usd AS NET_REV_FROM_RETURNS,
    
        amz_agg.net_ordered_product_sales_lc, 
    
        amz_agg.net_ordered_product_sales_usd AS NET_REVENUE, 

        amz_agg.estimated_fee_total_per_unit_usd,
        amz_agg.estimated_referral_fee_per_unit_usd, 
        amz_agg.expcted_fulfilment_fee_per_unit_usd ,
        
        amz_agg.gross_fulfilmentl_fees_usd AS FULFILLMENT_FEES_FROM_ORDERS, 
        amz_agg.refund_fulfilmentlfees_usd AS FULFILLMENT_FEES_SKU_ALLOCATED_RETURNS, 
        amz_agg.net_fulfilment_fees_usd AS TOTAL_FULFILLMENT_FEES,

        amz_agg.gross_referral_fees_usd AS SELLING_FEES_FROM_ORDERS, 
        amz_agg.refund_referral_fees_usd AS SELLING_FEES_FROM_SKU_ALLOCATED_RETURNS, 
        amz_agg.net_referral_fees_usd AS TOTAL_SELLING_FEES,

        amz_agg.total_gross_fba_fees_usd AS FBA_FEES_FROM_ORDERS, 
        amz_agg.total_refund_fba_fees_usd AS FBA_FEES_FRO_RETURNS, 
        amz_agg.total_net_fba_fees_usd AS TOTAL_FBA_FEES,

        CASE 
        WHEN cm.country_code IN ('DE', 'FR', 'ES', 'IT', 'BE', 'SE', 'TR', 'NL', 'PL', 'UK')
            THEN amz_agg.total_net_fba_fees_usd * 0.02 
        ELSE 0 END
        AS digital_service_plug_intl,

    

        
        

        amz_agg.latest_cogs_from_ns AS MAX_LATEST_NS_COGS,
        amz_agg.month_of_latest_cogs_from_ns AS MAX_MONTH_LATEST_NS_COGS,

        amz_agg.avg_brand_day_ns_cogs AS MAX_AVG_BRAND_DAY_NS_COGS,  

        amz_agg.calculated_cogs AS CALCULATED_COGS_NS,

        amz_agg.tooling_freight_plug AS TOOLING_PLUG,

        amz_agg.cogs_from_unsellable_returns AS COGS_FROM_UNSELLABLE_RETURNS,

        amz_agg.cogs_refund_from_sellable_returns AS COGS_REFUNDS_FROM_SELLABLE_RETURNS,


     
        amz_3pl.daily_3pl_freight AS daily_3pl_freight,

        COALESCE(calculated_cogs, 0) 
            + COALESCE(tooling_freight_plug, 0)
            + COALESCE(cogs_from_unsellable_returns, 0) 
            - COALESCE(cogs_refund_from_sellable_returns, 0) AS TOTAL_COGS_EXCL_THREE_PL_FREIGHT,

        COALESCE(calculated_cogs, 0) 
            + COALESCE(tooling_freight_plug, 0)
            + COALESCE(cogs_from_unsellable_returns, 0) 
            - COALESCE(cogs_refund_from_sellable_returns, 0) 
            + COALESCE(daily_3pl_freight, 0) AS TOTAL_COGS,


     ads_cte.sp_ppc_spend,
     ads_cte.sv_ppc_spend,
     ads_cte.sbv_ppc_spend,
     ads_cte.sd_ppc_spend,

     ads_cte.sp_ppc_sales, 
     ads_cte.sb_ppc_sales,
     ads_cte.sbv_ppc_sales, 
     ads_cte.sd_ppc_sales, 
     ads_cte.total_ppc_sales,

     10000 * DIV0NULL(amz_agg.ordered_product_sales_usd, SUM(amz_agg.ordered_product_sales_usd) 
            OVER(PARTITION BY DATE_TRUNC('month',cm.report_date))) AS ppc_addition_calculation_plug,
     
     COALESCE(ads_cte.total_ppc_spend,0) + COALESCE(ppc_addition_calculation_plug, 0) AS TOTAL_PPC,

 
   
    
    amz_dsp.daily_dsp_cost AS TOTAL_DSP,
    amz_dsp.daily_dsp_sales AS total_dsp_sales,
    deal_and_coupon.coupon, 
    deal_and_coupon.deal,
    deal_and_coupon.daily_deal_and_coupon_fee AS TOTAL_DEAL_FEES,
    google_ads.google_ad_spend_api,
    google_ads.misfit_agency_cost AS google_ads_misfit_agency_cost,
    google_ads.daily_google_ad_spend AS TOTAL_GOOGLE,
    facebook_ads.facebook_ad_spend_api,
    facebook_ads.misfit_agency_cost AS fb_ads_misfit_agency_cost,
    ugc.daily_ugc_cost,
    facebook_ads.daily_fb_ad_spend + ugc.daily_ugc_cost AS TOTAL_FACEBOOK_UGC,
    tiktok.daily_tiktok_ad_spend,
    influencers.daily_influencer_cost,
    COALESCE(tiktok.daily_tiktok_ad_spend,0) + COALESCE(influencers.daily_influencer_cost,0) AS tiktok_and_influencer_cost,
    affiliates.daily_affiliate_cost AS TOTAL_AFFILIATES,
    amz_3pl.daily_3pl_storage,
    amz_3pl.daily_3pl_inbound,
    amz_3pl.daily_3pl_outbound,
    
    amz_3pl.daily_3pl_total AS TOTAL_3PL_EXCL_FREIGHT,
    amz_storage.daily_amz_storage_fee_actual AS storage_model_daily_amz_storage_fee_actual,
    amz_storage.daily_amz_storage_fee_estimate AS storage_model_daily_amz_storage_fee_estimate,
    storage_raw.storage_fees_usd_with_plug AS source_amazon_fba_storage_fees_usd_with_plug ,

    20000 * DIV0NULL(amz_agg.ordered_product_sales_usd, SUM(amz_agg.ordered_product_sales_usd) 
            OVER(PARTITION BY DATE_TRUNC('month',cm.report_date))) AS storage_cost_warehouse_expense_additional_plug,
    
    COALESCE(storage_raw.storage_fees_usd_with_plug, amz_storage.total_amz_storage_fee,0) +       
    COALESCE(storage_cost_warehouse_expense_additional_plug,0)
    AS total_amz_storage_fee,
    
    royalties.daily_royalty_cost,
    publications.daily_publications_cost AS total_publication_cost,
    giveaways.total_giveaways AS total_daily_giveaways_cost
FROM
    consolidated_mapping AS cm

LEFT JOIN amazon_revenue_fba_fees_cogs_ns_joined AS amz_agg
    ON cm.report_date = amz_agg.purchase_date
    AND cm.asin = amz_agg.asin
    AND cm.country_code = amz_agg.country_code

LEFT JOIN  wbr_asin_map_latest AS brand_map
    ON cm.asin = brand_map.child_asin
    AND cm.country_code = brand_map.country_code
    
LEFT JOIN
    amz_3pl
    ON cm.report_date = amz_3pl.report_date
    AND cm.asin = amz_3pl.asin
    AND cm.country_code = amz_3pl.country_code
LEFT JOIN
    amz_storage
    ON cm.report_date = amz_storage.report_date
    AND cm.asin = amz_storage.asin
    AND cm.country_code = amz_storage.country_code
LEFT JOIN
    google_ads
    ON cm.report_date = google_ads.report_date
    AND cm.asin = google_ads.asin
    AND cm.country_code = google_ads.country_code
LEFT JOIN
    facebook_ads
    ON cm.report_date = facebook_ads.report_date
    AND cm.asin = facebook_ads.asin
    AND cm.country_code = facebook_ads.country_code
LEFT JOIN
    amz_dsp
    ON cm.report_date = amz_dsp.report_date
    AND cm.asin = amz_dsp.asin
    AND cm.country_code = amz_dsp.country_code
LEFT JOIN
    affiliates
    ON cm.report_date = affiliates.report_date
    AND cm.asin = affiliates.asin
    AND cm.country_code = affiliates.country_code
LEFT JOIN
    influencers
    ON cm.report_date = influencers.report_date
    AND cm.asin = influencers.asin
    AND cm.country_code = influencers.country_code
LEFT JOIN
    deal_and_coupon
    ON cm.report_date = deal_and_coupon.purchased_date
    AND cm.asin = deal_and_coupon.asin
    AND cm.country_code = deal_and_coupon.country_code
LEFT JOIN
    publications
    ON cm.report_date = publications.report_date
    AND cm.asin = publications.asin
    AND cm.country_code = publications.country_code
LEFT JOIN
    royalties
    ON cm.report_date = royalties.report_date
    AND cm.asin = royalties.asin
    AND cm.country_code = royalties.country_code
LEFT JOIN
    tiktok
    ON cm.report_date = tiktok.report_date
    AND cm.asin = tiktok.asin
    AND cm.country_code = tiktok.country_code
LEFT JOIN
    ugc
    ON cm.report_date = ugc.report_date
    AND cm.asin = ugc.asin
    AND cm.country_code = ugc.country_code

LEFT JOIN ads_cte
    ON cm.report_date = ads_cte.report_date
    AND cm.asin = ads_cte.asin
    AND cm.country_code = ads_cte.country_code

LEFT JOIN giveaways 
    ON cm.report_date = giveaways.report_date
    AND cm.asin = giveaways.child_asin
    AND cm.country_code = giveaways.country_code

LEFT JOIN amazon_storage_raw_report storage_raw
    ON cm.report_date = storage_raw.report_date
    AND cm.asin = storage_raw.asin
    AND cm.country_code = storage_raw.country_code

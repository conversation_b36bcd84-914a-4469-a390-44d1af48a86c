CREATE OR REPLACE TABLE DWH_DEV.STAGING.brandebitda_wbr_international_ops_revenue_asin_daily AS 

WITH sku_brand_map AS (
SELECT 
    "sku", 
    "brand"
    
FROM SANDBOX.OPS.SKU_ITEM_MAPPING
QUALIFY  ROW_NUMBER() OVER( PARTITION BY "sku" ORDER BY "last_encountered_at" DESC) =1
),


sku_country_brand_map AS (
SELECT 
    "sku", 
    "country",
    "brand"
FROM SANDBOX.OPS.SKU_ITEM_MAPPING
WHERE "last_encountered_at" is not null
QUALIFY  ROW_NUMBER() OVER( PARTITION BY "sku", "country" ORDER BY "last_encountered_at" DESC) =1

),



unique_order_level_data AS (
SELECT *, 
CASE 
    WHEN "country_code" = 'US' THEN convert_timezone('UTC', 'America/Los_Angeles', "purchase_date_utc")
    WHEN "country_code" = 'DE' THEN convert_timezone('UTC', 'Europe/Berlin', "purchase_date_utc")
    WHEN "country_code" = 'AU' THEN convert_timezone('UTC', 'Australia/Sydney', "purchase_date_utc")
    WHEN "country_code" = 'IT' THEN convert_timezone('UTC', 'Europe/Rome', "purchase_date_utc")
    WHEN "country_code" = 'TR' THEN convert_timezone('UTC', 'Europe/Istanbul', "purchase_date_utc")
    WHEN "country_code" = 'PL' THEN convert_timezone('UTC', 'Europe/Warsaw', "purchase_date_utc")
    WHEN "country_code" = 'BE' THEN convert_timezone('UTC', 'Europe/Brussels', "purchase_date_utc")
    WHEN "country_code" = 'MX' THEN convert_timezone('UTC', 'America/Mexico_City', "purchase_date_utc")
    WHEN "country_code" = 'NL' THEN convert_timezone('UTC', 'Europe/Amsterdam', "purchase_date_utc")
    WHEN "country_code" = 'CA' THEN convert_timezone('UTC', 'America/Toronto', "purchase_date_utc")
    WHEN "country_code" = 'BR' THEN convert_timezone('UTC', 'America/Sao_Paulo', "purchase_date_utc")
    WHEN "country_code" = 'UK' THEN convert_timezone('UTC', 'Europe/London', "purchase_date_utc")
    WHEN "country_code" = 'FR' THEN convert_timezone('UTC', 'Europe/Paris', "purchase_date_utc")
    WHEN "country_code" = 'ES' THEN convert_timezone('UTC', 'Europe/Madrid', "purchase_date_utc")
    WHEN "country_code" = 'SE' THEN convert_timezone('UTC', 'Europe/Stockholm', "purchase_date_utc")
    ELSE "purchase_date_utc" -- Fallback: no conversion
END AS purchase_date_local_zone, 

CASE 
    WHEN "country_code" IN ('DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'PL', 'TR', 'BE') THEN 'EU'
    ELSE "country_code"
END AS market_place_region

FROM DWH.PROD.fact_all_orders AS orders    

WHERE "marketplace" = 'AMAZON'
    AND "sales_channel" not like '%non-amazon%'
    AND "purchase_date_utc" >= '2024-01-01'
    --AND "order_status" NOT IN ('cancelled', 'null') 
    AND "order_status" IS NOT NULL
    AND "country_code" IS NOT NULL
    AND "country_code" != 'US'

QUALIFY ROW_NUMBER() OVER(PARTITION BY "external_order_id", orders."sku" ORDER BY "record_updated_timestamp_utc" DESC) = 1 


),



exchange_rate_prep AS (
SELECT 
base_currency,
CASE WHEN 
transactional_currency = 'EURO' THEN 'EUR'
ELSE transactional_currency END AS transactional_currency, 
exchange_rate,
effective_date
FROM DWH.NETSUITE.foreign_exchange
WHERE BASE_CURRENCY ='USD'
QUALIFY ROW_NUMBER() OVER(PARTITION BY transactional_currency, effective_date ORDER BY BASE_CURRENCY DESC) = 1
),

cancelled_order_count_aggre AS (
SELECT 
        "marketplace", 
        --combine_brand,
        "country_code",
        "sku",
        date_trunc('hour', purchase_date_local_zone) AS purchase_hour_datetime, 
        COUNT(DISTINCT CASE WHEN COALESCE("order_status",'pending') = 'cancelled' 
                            THEN "external_order_id" END) AS cancelled_order_count,


        COUNT(distinct "external_order_id") AS total_order_count
     FROM unique_order_level_data AS orders
    GROUP BY 
        "marketplace", 
       -- combine_brand,
        "country_code",
        "sku",
        date_trunc('hour', purchase_date_local_zone)


),

refund_order_level_data AS (
SELECT *, 
CASE 
    WHEN "country_code" = 'US' THEN convert_timezone('UTC', 'America/Los_Angeles', "purchase_date_utc")
    WHEN "country_code" = 'DE' THEN convert_timezone('UTC', 'Europe/Berlin', "purchase_date_utc")
    WHEN "country_code" = 'AU' THEN convert_timezone('UTC', 'Australia/Sydney', "purchase_date_utc")
    WHEN "country_code" = 'IT' THEN convert_timezone('UTC', 'Europe/Rome', "purchase_date_utc")
    WHEN "country_code" = 'TR' THEN convert_timezone('UTC', 'Europe/Istanbul', "purchase_date_utc")
    WHEN "country_code" = 'PL' THEN convert_timezone('UTC', 'Europe/Warsaw', "purchase_date_utc")
    WHEN "country_code" = 'BE' THEN convert_timezone('UTC', 'Europe/Brussels', "purchase_date_utc")
    WHEN "country_code" = 'MX' THEN convert_timezone('UTC', 'America/Mexico_City', "purchase_date_utc")
    WHEN "country_code" = 'NL' THEN convert_timezone('UTC', 'Europe/Amsterdam', "purchase_date_utc")
    WHEN "country_code" = 'CA' THEN convert_timezone('UTC', 'America/Toronto', "purchase_date_utc")
    WHEN "country_code" = 'BR' THEN convert_timezone('UTC', 'America/Sao_Paulo', "purchase_date_utc")
    WHEN "country_code" = 'UK' THEN convert_timezone('UTC', 'Europe/London', "purchase_date_utc")
    WHEN "country_code" = 'FR' THEN convert_timezone('UTC', 'Europe/Paris', "purchase_date_utc")
    WHEN "country_code" = 'ES' THEN convert_timezone('UTC', 'Europe/Madrid', "purchase_date_utc")
    WHEN "country_code" = 'SE' THEN convert_timezone('UTC', 'Europe/Stockholm', "purchase_date_utc")
    ELSE "purchase_date_utc" -- Fallback: no conversion
END AS purchase_date_local_zone, 

CASE 
    WHEN "country_code" = 'US' THEN convert_timezone('UTC', 'America/Los_Angeles', "refund_date_utc")
    WHEN "country_code" = 'DE' THEN convert_timezone('UTC', 'Europe/Berlin', "refund_date_utc")
    WHEN "country_code" = 'AU' THEN convert_timezone('UTC', 'Australia/Sydney', "refund_date_utc")
    WHEN "country_code" = 'IT' THEN convert_timezone('UTC', 'Europe/Rome', "refund_date_utc")
    WHEN "country_code" = 'TR' THEN convert_timezone('UTC', 'Europe/Istanbul', "refund_date_utc")
    WHEN "country_code" = 'PL' THEN convert_timezone('UTC', 'Europe/Warsaw', "refund_date_utc")
    WHEN "country_code" = 'BE' THEN convert_timezone('UTC', 'Europe/Brussels', "refund_date_utc")
    WHEN "country_code" = 'MX' THEN convert_timezone('UTC', 'America/Mexico_City', "refund_date_utc")
    WHEN "country_code" = 'NL' THEN convert_timezone('UTC', 'Europe/Amsterdam', "refund_date_utc")
    WHEN "country_code" = 'CA' THEN convert_timezone('UTC', 'America/Toronto', "refund_date_utc")
    WHEN "country_code" = 'BR' THEN convert_timezone('UTC', 'America/Sao_Paulo', "refund_date_utc")
    WHEN "country_code" = 'UK' THEN convert_timezone('UTC', 'Europe/London', "refund_date_utc")
    WHEN "country_code" = 'FR' THEN convert_timezone('UTC', 'Europe/Paris', "refund_date_utc")
    WHEN "country_code" = 'ES' THEN convert_timezone('UTC', 'Europe/Madrid', "refund_date_utc")
    WHEN "country_code" = 'SE' THEN convert_timezone('UTC', 'Europe/Stockholm', "refund_date_utc")
    ELSE "purchase_date_utc" -- Fallback: no conversion
END AS refund_date_local_zone, 


CASE 
    WHEN "country_code" IN ('DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'PL', 'TR', 'BE') THEN 'EU'
    ELSE "country_code"
END AS market_place_region

FROM DWH.PROD.fact_all_refunds AS refunds  

WHERE "marketplace" = 'AMAZON'
    AND "sales_channel" not like '%non-amazon%'
    AND "purchase_date_utc" >= '2024-01-01'
    AND "country_code" IS NOT NULL

QUALIFY ROW_NUMBER() OVER(PARTITION BY "external_order_id", "sku" ORDER BY "record_updated_timestamp_utc" DESC) = 1 

),



price_aggregates AS (
    SELECT 
        "marketplace", 
        --combine_brand,
        "country_code",
        "sku",
        date_trunc('hour', purchase_date_local_zone) AS purchase_hour_datetime, 
        SUM("quantity") AS quantity,
        SUM("quantity" * "item_price_per_unit") AS item_price_lc, 
        SUM("quantity" * "shipping_price_per_unit") AS shipping_price_lc, 
        SUM("quantity" * "item_promotion_discount_per_unit") AS item_promo_discount, 
        SUM("quantity" * "ship_promotion_discount_per_unit") AS ship_promo_discount, 
        SUM("quantity" * "gift_wrap_price_per_unit") AS giftwrap_price_lc,

        -- Aggregating USD prices (using division by ns.exchange_rate)
        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "item_price_per_unit"
                ELSE ("quantity" * "item_price_per_unit") * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate)
            END
        ) AS item_price_usd,

        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "shipping_price_per_unit"
                ELSE ("quantity" * "shipping_price_per_unit") * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate)
            END
        ) AS shipping_price_usd,

        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "item_promotion_discount_per_unit"
                ELSE ("quantity" * "item_promotion_discount_per_unit") * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate)
            END
        ) AS item_promo_discount_usd,

        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "ship_promotion_discount_per_unit"
                ELSE ("quantity" * "ship_promotion_discount_per_unit") * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate)
            END
        ) AS ship_promo_discount_usd,

        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "quantity" * "gift_wrap_price_per_unit"
                ELSE ("quantity" * "gift_wrap_price_per_unit") * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate)
            END
        ) AS gift_wrap_price_usd
        
    FROM unique_order_level_data AS orders
     -- First join: exchange rate for the purchase date
    LEFT JOIN exchange_rate_prep AS ns_current
        ON orders."currency" = ns_current.transactional_currency 
        AND date_trunc('day', orders.purchase_date_local_zone) = ns_current.effective_date

     -- Second join: exchange rate for the day before the purchase date
    LEFT JOIN exchange_rate_prep AS ns_d1
        ON orders."currency" = ns_d1.transactional_currency 
        AND date_trunc('day', DATEADD(day, -1, purchase_date_local_zone)) = ns_d1.effective_date

   WHERE COALESCE(orders."order_status", 'pending') <> 'cancelled'
    GROUP BY 
        "marketplace", 
        --combine_brand,
        "country_code",
        "sku",
        date_trunc('hour', purchase_date_local_zone)
),


refund_price_aggregate AS (
    
    SELECT 
        "marketplace", 
        "country_code",
        "sku",
        date_trunc('hour', refund_date_local_zone) AS refund_hour_datetime, 
        SUM("quantity") AS refund_quantity,
        SUM("product_refund") AS item_price_refund_lc, 
        SUM("shipping_refund") AS shipping_refund_lc, 

         SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "product_refund"
                ELSE "product_refund" * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate)
            END
        ) AS item_price_refund_usd, 

        SUM(
            CASE 
                WHEN "currency" = 'USD' THEN "shipping_refund"
                ELSE "shipping_refund" * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate)
            END
        ) AS shipping_refund_usd
        
        
    FROM refund_order_level_data AS refunds
    LEFT JOIN exchange_rate_prep AS ns_current
        ON refunds."currency" = ns_current.transactional_currency 
        AND date_trunc('day', refunds.refund_date_local_zone) = ns_current.effective_date

     -- Second join: exchange rate for the day before the purchase date
    LEFT JOIN exchange_rate_prep AS ns_d1
        ON refunds."currency" = ns_d1.transactional_currency 
        AND date_trunc('day', DATEADD(day, -1, refund_date_local_zone)) = ns_d1.effective_date
    GROUP BY 
        "marketplace", 
        "country_code",
        "sku",
        date_trunc('hour', refund_date_local_zone)


), 

sku_level_refunds AS (

    SELECT 
    "marketplace", 
    "country_code",
    "sku",
    refund_hour_datetime,
    refund_quantity,

    item_price_refund_lc, 
    shipping_refund_lc, 

    item_price_refund_usd,
    shipping_refund_usd, 

    COALESCE(item_price_refund_lc, 0) + COALESCE(shipping_refund_lc, 0) AS refund_ordered_product_sales_lc, 

    COALESCE(item_price_refund_usd, 0) + COALESCE(shipping_refund_usd, 0) AS refund_ordered_product_sales_usd, 
    

    FROM refund_price_aggregate

),



sku_level_ops AS (
SELECT 
    price_aggre."marketplace", 
    --price_aggre.combine_brand,
    price_aggre."country_code",
    price_aggre."sku",
    price_aggre.purchase_hour_datetime,
    price_aggre.quantity,
    price_aggre.item_price_lc,
    price_aggre.item_price_usd,
    price_aggre.shipping_price_lc,
    price_aggre.shipping_price_usd,
    price_aggre.item_promo_discount,
    price_aggre.item_promo_discount_usd,
    price_aggre.ship_promo_discount,
    price_aggre.ship_promo_discount_usd,
    price_aggre.giftwrap_price_lc,
    price_aggre.gift_wrap_price_usd,

    cance_aggre.cancelled_order_count, 
    cance_aggre.total_order_count, 

    CASE 
    WHEN price_aggre."country_code" IN ('DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'PL', 'TR', 'BE') THEN 'EU'
    ELSE price_aggre."country_code"
END AS market_place_region,
    
    -- Ordered product sales in LC
    (COALESCE(price_aggre.item_price_lc,0) + COALESCE(price_aggre.shipping_price_lc,0) + COALESCE(price_aggre.giftwrap_price_lc,0) 
     - COALESCE(price_aggre.item_promo_discount,0) - COALESCE(price_aggre.ship_promo_discount,0)) AS ordered_product_sales_lc,

    -- Ordered product sales in USD
    (COALESCE(price_aggre.item_price_usd,0) + COALESCE(price_aggre.shipping_price_usd,0) + COALESCE(price_aggre.gift_wrap_price_usd,0)
     - COALESCE(price_aggre.item_promo_discount_usd,0) - COALESCE(price_aggre.ship_promo_discount_usd,0)) AS ordered_product_sales_usd

FROM price_aggregates AS price_aggre
LEFT JOIN cancelled_order_count_aggre AS cance_aggre
    ON price_aggre."marketplace" = cance_aggre."marketplace"
       --AND price_aggre.combine_brand = cance_aggre.combine_brand
       AND price_aggre."country_code" = cance_aggre."country_code"
       AND price_aggre."sku" = cance_aggre."sku"
       AND price_aggre.purchase_hour_datetime = cance_aggre.purchase_hour_datetime
),


item_brand_level_aggregation AS (
    SELECT 
        sku_ops."marketplace", 
        sku_ops."country_code",
        sku_ops."sku",
        
        sku_ops.purchase_hour_datetime AS purchase_hour_datetime,
        
        --DATE_TRUNC('day',sku_ops.purchase_date) AS purchase_date,
        SUM(sku_ops.quantity) AS total_quantity,
        SUM(sku_refund.refund_quantity) refund_quantity, 

        COALESCE(SUM(sku_ops.quantity),0) - COALESCE(SUM(sku_refund.refund_quantity),0) AS net_quantity, 
        
        SUM(sku_ops.item_price_lc) AS total_item_price_lc,
        SUM(sku_ops.item_price_usd) AS total_item_price_usd,
        SUM(sku_ops.shipping_price_lc) AS total_shipping_price_lc,
        SUM(sku_ops.shipping_price_usd) AS total_shipping_price_usd,
        SUM(sku_ops.item_promo_discount) AS total_item_promo_discount,
        SUM(sku_ops.item_promo_discount_usd) AS total_item_promo_discount_usd,
        SUM(sku_ops.ship_promo_discount) AS total_ship_promo_discount,
        SUM(sku_ops.ship_promo_discount_usd) AS total_ship_promo_discount_usd,
        SUM(sku_ops.giftwrap_price_lc) AS total_giftwrap_price_lc,
        SUM(sku_ops.gift_wrap_price_usd) AS total_giftwrap_price_usd,

        SUM(cancelled_order_count) AS cancelled_order_count, 
        SUM(total_order_count) AS total_order_count, 

        SUM(cancelled_order_count) /  NULLIF(SUM(total_order_count), 0) AS brand_cancellation_rate,

        SUM(ordered_product_sales_lc) AS ordered_product_sales_lc, 

        SUM(ordered_product_sales_usd) AS ordered_product_sales_usd, 

        SUM(sku_refund.item_price_refund_lc) AS refund_item_price_lc, 
        SUM(sku_refund.shipping_refund_lc) AS refund_shipping_lc, 

        SUM(sku_refund.item_price_refund_usd) AS refund_item_price_usd, 
        SUM(sku_refund.shipping_refund_usd) AS refund_shipping_usd, 

        SUM(sku_refund.refund_ordered_product_sales_lc) AS refund_ordered_product_sales_lc, 

        SUM(sku_refund.refund_ordered_product_sales_usd) AS refund_ordered_product_sales_usd,

        COALESCE(SUM(ordered_product_sales_lc),0)+ COALESCE(SUM(sku_refund.refund_ordered_product_sales_lc),0) AS net_ordered_product_sales_lc, 

        COALESCE(SUM(ordered_product_sales_usd),0)+ COALESCE(SUM(sku_refund.refund_ordered_product_sales_usd),0) AS net_ordered_product_sales_usd
        
        
    FROM sku_level_ops as sku_ops

    LEFT JOIN sku_level_refunds AS sku_refund
    ON sku_ops."sku" = sku_refund."sku"
    AND sku_ops."marketplace" = sku_refund."marketplace"
    AND sku_ops."country_code" = sku_refund."country_code"
    AND sku_ops.purchase_hour_datetime = sku_refund.refund_hour_datetime
    
    
    LEFT JOIN sku_country_brand_map as map 
    ON sku_ops."sku" = map."sku" 
    and sku_ops."country_code" = map."country"
    
    LEFT JOIN sku_brand_map AS brand_map_2 
    ON sku_ops."sku" = brand_map_2."sku"

    GROUP BY 
        sku_ops."marketplace", 
        sku_ops."sku",
        sku_ops."country_code",
        sku_ops.purchase_hour_datetime
),

-- channel level aggregation 
channel_level_data_aggregation AS (

SELECT 
    "marketplace", 
    --combine_brand,

    -- item number -- 
    "sku", 

    
    "country_code",

    purchase_hour_datetime,
    
    -- Aggregated metrics
    SUM(total_quantity) AS total_quantity,
    SUM(refund_quantity) AS refund_quantity, 
    SUM(net_quantity) AS net_quantity,
    
    SUM(total_item_price_lc) AS total_item_price_lc,
    SUM(total_item_price_usd) AS total_item_price_usd,
    SUM(total_shipping_price_lc) AS total_shipping_price_lc,
    SUM(total_shipping_price_usd) AS total_shipping_price_usd,
    SUM(total_item_promo_discount) AS total_item_promo_discount,
    SUM(total_item_promo_discount_usd) AS total_item_promo_discount_usd,
    SUM(total_ship_promo_discount) AS total_ship_promo_discount,
    SUM(total_ship_promo_discount_usd) AS total_ship_promo_discount_usd,
    SUM(total_giftwrap_price_lc) AS total_giftwrap_price_lc,
    SUM(total_giftwrap_price_usd) AS total_giftwrap_price_usd,

    SUM(total_order_count) AS total_order_count, 

    SUM(ordered_product_sales_lc) AS ordered_product_sales_lc, 
    
    SUM(ordered_product_sales_usd) AS ordered_product_sales_usd, 

    SUM(refund_item_price_lc) AS refund_item_price_lc, 
    SUM(refund_shipping_lc) AS refund_shipping_lc, 

    SUM(refund_item_price_usd) AS refund_item_price_usd, 
    SUM(refund_shipping_usd) AS refund_shipping_usd, 

    SUM(refund_ordered_product_sales_lc) AS refund_ordered_product_sales_lc, 

    SUM(refund_ordered_product_sales_usd) AS refund_ordered_product_sales_usd,

    SUM(net_ordered_product_sales_lc) AS net_ordered_product_sales_lc, 

    SUM(net_ordered_product_sales_usd) AS net_ordered_product_sales_usd

FROM item_brand_level_aggregation
GROUP BY 
    "marketplace", 
    "sku",
    "country_code", 
    purchase_hour_datetime
),





ops_with_tagrets_brand_category_map AS (

SELECT 
opst.*, 
COALESCE(asin_map.asin, asin_map_region.asin) AS asin, 


FROM channel_level_data_aggregation AS opst
LEFT JOIN  DWH_DEV.STAGING.stg_brand_ebitda_sku_asin_map_orders_source_table AS asin_map 
ON opst."sku" = asin_map.sku
AND opst."country_code" = asin_map.country_code

LEFT JOIN DWH_DEV.STAGING.stg_brand_ebitda_sku_asin_map_orders_source_table AS asin_map_region
ON opst."sku" = asin_map_region.sku
AND opst."country_code" = asin_map_region.market_place_region


),


 wbr_asin_map_latest AS (
    SELECT *
    FROM (
        SELECT
            child_asin,
            country_code,
            brand,
            category,
            niche,
            product_type,
            brand_grouping,
            brand_manager,
            account_title,
            ROW_NUMBER() OVER (
                PARTITION BY child_asin, country_code
                ORDER BY record_updated_timestamp_utc DESC,
                         record_created_timestamp_utc DESC
            ) AS rn
        FROM DWH_DEV.STAGING.merge_brand_ebitda_asin_mapping
        WHERE child_asin IS NOT NULL
    )
    WHERE rn = 1
)



SELECT 
        
        brand_map.brand AS brand_name, 
        asin AS asin,

        "country_code" AS country_code,
        CASE 
    WHEN "country_code" IN ('DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'PL', 'TR', 'BE') THEN 'EU'
    ELSE "country_code"
    END AS market_place_region,
        
        DATE_TRUNC('DAY', purchase_hour_datetime) AS purchase_date,
        
         SUM(total_quantity) AS total_quantity,
    SUM(refund_quantity) AS refund_quantity, 
    SUM(net_quantity) AS net_quantity,
    
    SUM(total_item_price_lc) AS total_item_price_lc,
    SUM(total_item_price_usd) AS total_item_price_usd,
    SUM(total_shipping_price_lc) AS total_shipping_price_lc,
    SUM(total_shipping_price_usd) AS total_shipping_price_usd,
    SUM(total_item_promo_discount) AS total_item_promo_discount,
    SUM(total_item_promo_discount_usd) AS total_item_promo_discount_usd,
    SUM(total_ship_promo_discount) AS total_ship_promo_discount,
    SUM(total_ship_promo_discount_usd) AS total_ship_promo_discount_usd,
    SUM(total_giftwrap_price_lc) AS total_giftwrap_price_lc,
    SUM(total_giftwrap_price_usd) AS total_giftwrap_price_usd,

    SUM(total_order_count) AS total_order_count, 

    SUM(ordered_product_sales_lc) AS ordered_product_sales_lc, 
    
    SUM(ordered_product_sales_usd) AS ordered_product_sales_usd, 

    SUM(refund_item_price_lc) AS refund_item_price_lc, 
    SUM(refund_shipping_lc) AS refund_shipping_lc, 

    SUM(refund_item_price_usd) AS refund_item_price_usd, 
    SUM(refund_shipping_usd) AS refund_shipping_usd, 

    SUM(refund_ordered_product_sales_lc) AS refund_ordered_product_sales_lc, 

    SUM(refund_ordered_product_sales_usd) AS refund_ordered_product_sales_usd,

    SUM(net_ordered_product_sales_lc) AS net_ordered_product_sales_lc, 

    SUM(net_ordered_product_sales_usd) AS net_ordered_product_sales_usd

   FROM ops_with_tagrets_brand_category_map ops
   LEFT JOIN  wbr_asin_map_latest AS brand_map
   ON ops.asin = brand_map.child_asin
  AND  ops."country_code" = brand_map.country_code
     GROUP BY 
        brand_map.brand,
        asin,
        "country_code",
        market_place_region,
        DATE_TRUNC('DAY', purchase_hour_datetime)


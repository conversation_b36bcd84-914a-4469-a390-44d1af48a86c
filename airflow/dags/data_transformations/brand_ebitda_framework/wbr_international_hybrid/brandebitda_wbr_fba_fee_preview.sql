CREATE OR REPLACE TABLE DWH_DEV.STAGING.brandebitda_wbr_international_fba_fee_preview AS 

WITH exchange_rate_prep AS (
SELECT 
base_currency,
CASE WHEN 
transactional_currency = 'EURO' THEN 'EUR'
ELSE transactional_currency END AS transactional_currency, 
exchange_rate,
effective_date
FROM DWH.NETSUITE.foreign_exchange
WHERE BASE_CURRENCY ='USD'
QUALIFY ROW_NUMBER() OVER(PARTITION BY transactional_currency, effective_date ORDER BY BASE_CURRENCY DESC) = 1
), 

currency_conversion AS (
select
date_trunc('day',"ReportstartDate") AS report_start_date,
date_trunc('day',"ReportendDate") AS report_end_date, 
"asin" AS asin, 
CASE 
WHEN "currency" ='MXN' THEN 'MX'
WHEN "currency" ='PLN' THEN 'PL'
WHEN "currency" ='EUR' THEN 'EU'
WHEN "currency" ='AED' THEN 'AE'
WHEN "currency" ='CAD' THEN 'CA'
WHEN "currency" ='TRY' THEN 'TR'
WHEN "currency" ='USD' THEN 'US'
WHEN "currency" ='SEK' THEN 'SE'
WHEN "currency" ='GBP' THEN 'UK'
END AS country_region, 

"estimated_fee_total" AS estimated_fee_total_lc, 
"estimated_referral_fee_per_unit" AS estimated_referral_fee_per_unit_lc, 
"expected_domestic_fulfilment_fee_per_unit" AS expected_domestic_fulfilment_fee_per_unit_lc,

CASE WHEN fba_fees."currency"= 'USD' THEN "estimated_fee_total"
ELSE "estimated_fee_total" * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate) 
END AS estimated_fee_total_usd, 

CASE WHEN fba_fees."currency"= 'USD' THEN "estimated_referral_fee_per_unit"
ELSE "estimated_referral_fee_per_unit" * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate) 
END AS estimated_referral_fee_per_unit_usd, 

CASE WHEN fba_fees."currency"= 'USD' THEN "expected_domestic_fulfilment_fee_per_unit"
ELSE "expected_domestic_fulfilment_fee_per_unit" * COALESCE(ns_current.exchange_rate, ns_d1.exchange_rate) 
END AS expected_fulfilment_fee_per_unit_usd

FROM DWH.PROD.amazon_fee_preview AS fba_fees
 LEFT JOIN exchange_rate_prep AS ns_current
        ON fba_fees."currency" = ns_current.transactional_currency 
        AND date_trunc('day', fba_fees."ReportstartDate") = ns_current.effective_date

     -- Second join: exchange rate for the day before the purchase date
    LEFT JOIN exchange_rate_prep AS ns_d1
        ON fba_fees."currency" = ns_d1.transactional_currency 
        AND date_trunc('day', DATEADD(day, -1, fba_fees."ReportstartDate")) = ns_d1.effective_date


WHERE "ReportstartDate" >= '2024-01-01'
)

select 
report_start_date, 
asin, 
country_region, 
MAX(estimated_fee_total_lc) AS estimated_fee_total_lc , 
MAX(estimated_referral_fee_per_unit_lc) AS estimated_referral_fee_per_unit_lc, 
MAX(expected_domestic_fulfilment_fee_per_unit_lc) AS expected_domestic_fulfilment_fee_per_unit_lc, 
MAX(estimated_fee_total_usd) AS estimated_fee_total_usd, 
MAX(estimated_referral_fee_per_unit_usd) AS estimated_referral_fee_per_unit_usd, 
CASE WHEN MAX(expected_fulfilment_fee_per_unit_usd) IS NULL 
THEN MAX(estimated_fee_total_usd) - MAX(estimated_referral_fee_per_unit_usd)
ELSE MAX(expected_fulfilment_fee_per_unit_usd)
END AS expcted_fulfilment_fee_per_unit_usd
FROM currency_conversion
GROUP BY 
report_start_date, 
asin, 
country_region

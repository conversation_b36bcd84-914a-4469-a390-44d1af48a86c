CREATE OR REPLACE TABLE $stage_db.brand_ebitda_all_giveaways_consolidated AS

WITH typed_inputs AS (
    SELECT
          LOWER(main_table.source) AS source
        , main_table.child_asin
        , 'US' AS market_id
        , main_table.week AS week_number
        , date_dimension.full_date
        

        , ABS(TRY_TO_DOUBLE(REPLACE(REPLACE(main_table.purchased_giveaways, '$', ''), ',', ''))) / 7 AS purchased_giveaways
        , ABS(TRY_TO_DOUBLE(REPLACE(REPLACE(main_table.total_give_away_dollars, '$', ''), ',', ''))) / 7 AS total_giveaways_dollars
    FROM DWH_DEV.STAGING.brand_ebitda_giveaways_us_raw_ingest AS main_table
    LEFT JOIN date_dimension AS date_dimension
        ON main_table.week = date_dimension.week_label
),

typed_inputs_intl AS (
    SELECT
          LOWER(main_table.source) AS source
        , main_table.child_asin
        , market_id
        , main_table.week AS week_number
        , date_dimension.full_date


        , ABS(TRY_TO_DOUBLE(REPLACE(REPLACE(main_table.purchased_giveaways, '$', ''), ',', ''))) / 7 AS purchased_giveaways
        , ABS(TRY_TO_DOUBLE(REPLACE(REPLACE(main_table.total_give_away_dollars, '$', ''), ',', ''))) / 7 AS total_giveaways_dollars
    FROM DWH_DEV.STAGING.brand_ebitda_giveaways_intl_raw_ingest AS main_table
    LEFT JOIN date_dimension AS date_dimension
        ON main_table.week = date_dimension.week_label
),

unioned_giveaway AS (
    SELECT *
    FROM typed_inputs
    UNION ALL
    SELECT *
    FROM typed_inputs_intl
)

SELECT
      source
    , child_asin
    , market_id
    , week_number
    , full_date
    , file_name
    , purchased_giveaways AS total_purchased_giveaways
    , total_giveaways_dollars
    -- Postcards
    , CASE WHEN REGEXP_LIKE(source, '^(thanks|postcard|leaflets)')
           THEN purchased_giveaways ELSE 0 END AS purchased_postcards
    , CASE WHEN REGEXP_LIKE(source, '^(thanks|postcard|leaflets)')
           THEN total_giveaways_dollars ELSE 0 END AS postcards_dollars
    -- Giveaways
    , CASE WHEN REGEXP_LIKE(source, '^(giveaway)')
           THEN purchased_giveaways ELSE 0 END AS purchased_giveaways
    , CASE WHEN REGEXP_LIKE(source, '^(giveaway)')
           THEN total_giveaways_dollars ELSE 0 END AS giveaways_dollars
    -- Influencer
    , CASE WHEN REGEXP_LIKE(source, '^(convomat)')
           THEN purchased_giveaways ELSE 0 END AS purchased_influencer
    , CASE WHEN REGEXP_LIKE(source, '^(convomat)')
           THEN total_giveaways_dollars ELSE 0 END AS influencer_dollars
FROM unioned_giveaway


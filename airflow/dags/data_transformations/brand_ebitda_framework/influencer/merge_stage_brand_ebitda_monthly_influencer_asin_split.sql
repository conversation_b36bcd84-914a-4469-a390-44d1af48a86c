CREATE TABLE IF NOT EXISTS $stage_db.merge_brand_ebitda_monthly_influencer_asin_split AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_brand_ebitda_monthly_influencer_asin_split
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_brand_ebitda_monthly_influencer_asin_split AS tgt
USING
    $stage_db.dedupe_brand_ebitda_monthly_influencer_asin_split AS src
    
        ON 1 = 1
       AND src.pk = tgt.pk
       
WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._airbyte_raw_id = src._airbyte_raw_id,
    tgt._airbyte_extracted_at = src._airbyte_extracted_at,
    tgt._airbyte_generation_id = src._airbyte_generation_id,
    tgt._airbyte_meta = src._airbyte_meta,
    tgt.brand = src.brand,
    tgt.month = src.month,
    tgt.brand_code = src.brand_code,
    tgt.child_asin = src.child_asin,
    tgt.plan_split = src.plan_split,
    tgt.parent_asin = src.parent_asin,
    tgt.actual_split = src.actual_split,
    tgt.product_title = src.product_title,
    tgt.budget_country = src.budget_country,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    brand,
    month,
    brand_code,
    child_asin,
    plan_split,
    parent_asin,
    actual_split,
    product_title,
    budget_country,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._airbyte_raw_id, 
    src._airbyte_extracted_at, 
    src._airbyte_generation_id, 
    src._airbyte_meta, 
    src.brand, 
    src.month, 
    src.brand_code, 
    src.child_asin, 
    src.plan_split, 
    src.parent_asin, 
    src.actual_split, 
    src.product_title, 
    src.budget_country, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
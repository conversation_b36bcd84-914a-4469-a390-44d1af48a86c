CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_brand_ebitda_monthly_influencer_asin_split AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_brand_ebitda_monthly_influencer_asin_split
    WHERE 1 = 0;

INSERT INTO $raw_db.log_brand_ebitda_monthly_influencer_asin_split (
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    brand,
    month,
    brand_code,
    child_asin,
    plan_split,
    parent_asin,
    actual_split,
    product_title,
    budget_country,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        brand,
        month,
        brand_code,
        child_asin,
        plan_split,
        parent_asin,
        actual_split,
        product_title,
        budget_country,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_brand_ebitda_monthly_influencer_asin_split;
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_brand_ebitda_monthly_influencer_spend AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(brand AS VARCHAR), ''), '-',
            COALESCE(CAST(brand_code AS VARCHAR), ''), '-',
            COALESCE(CAST(budget_country AS VARCHAR), ''), '-',
            COALESCE(CAST(influencer_name AS VARCHAR), ''), '-',
            COALESCE(CAST(date_influencer_added AS VARCHAR), ''), '-',
            COALESCE(CAST(source AS VARCHAR), '')
            )) AS pk,
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        brand,
        source,
        brand_code,
        budget_country,
        influencer_cost,
        influencer_name,
        date_influencer_added,
        file_name,
        etl_batch_run_time

    FROM $raw_db.raw_brand_ebitda_monthly_influencer_spend
    WHERE 1=1
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY _airbyte_extracted_at DESC NULLS LAST) = 1
);
CREATE OR REPLACE TABLE $stage_db.merge_stg_brand_ebitda_daily_item_level_influencer AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.create_stg_brand_ebitda_daily_item_level_influencer
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_stg_brand_ebitda_daily_item_level_influencer AS tgt
USING
    (
        SELECT
            *
        FROM $stage_db.create_stg_brand_ebitda_daily_item_level_influencer
        QUALIFY ROW_NUMBER() OVER (PARTITION BY pk ORDER BY report_date DESC) = 1 -- dedupe
    ) AS src
        ON tgt.pk = src.pk

WHEN MATCHED THEN
    -- Update the record if it already exists
    UPDATE SET
        tgt.channel = src.channel,
        tgt.attribution_type = src.attribution_type,
        tgt.daily_influencer_cost = src.daily_influencer_cost,
        tgt.record_updated_timestamp_utc = SYSDATE()  -- Update the timestamp

WHEN NOT MATCHED THEN
    -- Insert a new record if it does not exist
    INSERT (
        pk,
        brand_code,
        report_date,
        month_start_date,
        asin,
        country_code,
        channel,
        attribution_type,
        daily_influencer_cost,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        src.pk,
        src.brand_code,
        src.report_date,
        src.month_start_date,
        src.asin,
        src.country_code,
        src.channel,
        src.attribution_type,
        src.daily_influencer_cost,
        SYSDATE(),
        SYSDATE()
    );

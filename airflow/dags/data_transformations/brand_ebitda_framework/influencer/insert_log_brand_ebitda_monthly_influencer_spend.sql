CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_brand_ebitda_monthly_influencer_spend AS
    SELECT
         *
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_brand_ebitda_monthly_influencer_spend
    WHERE 1 = 0;

INSERT INTO $raw_db.log_brand_ebitda_monthly_influencer_spend (
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    brand,
    source,
    brand_code,
    budget_country,
    influencer_cost,
    influencer_name,
    date_influencer_added,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc
)
    SELECT
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        brand,
        source,
        brand_code,
        budget_country,
        influencer_cost,
        influencer_name,
        date_influencer_added,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM
    $raw_db.raw_brand_ebitda_monthly_influencer_spend;
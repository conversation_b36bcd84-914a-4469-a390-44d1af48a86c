CREATE OR REPLACE TRANSIENT TABLE $stage_db.create_stg_brand_ebitda_daily_item_level_influencer AS

WITH generate_date AS (
    SELECT
        '2024-01-01'::date + row_number() over(order by 0) AS report_date
        , CASE
            WHEN DAYOFWEEK(report_date) = 7 THEN report_date -- If the date is Sunday, return the date itself
            ELSE DATE_TRUNC('WEEK', REPORT_DATE) - INTERVAL '1 day' -- Else, return to the date of the preceding Sunday
        END AS week_start_date
    FROM table(generator(rowcount => 1824))
),

monthly_influencer_cost AS (
    SELECT
        brand_code
        , budget_country AS country_code
        , DATE_TRUNC("month", date_influencer_added) AS month_start_date
        , SUM(COALESCE(influencer_cost, 0)) AS monthly_influencer_cost
    FROM
        $stage_db.merge_brand_ebitda_monthly_influencer_spend
    GROUP BY ALL
),

-- Determines the latest month for which actual influencer cost data is available.
actual_data_max_month AS (
    SELECT
        MAX(month_start_date) AS latest_month_with_actual
    FROM
        monthly_influencer_cost
),

child_asin_mapping AS (
    SELECT
        brand_code
        , brand
        , budget_country
        , child_asin AS asin
        , product_title
        , month AS month_start_date
        , plan_split
        , actual_split
        , COALESCE(actual_split, plan_split) AS cost_split
    FROM
        $stage_db.merge_brand_ebitda_influencer_child_asin_split
),

daily_asin_level_influencer_cost AS (
    SELECT
        cost.brand_code
        , cost.country_code
        , cost.month_start_date
        , generate_date.report_date
        , map.asin
        , map.product_title
        , map.cost_split
        , cost.monthly_influencer_cost
        , DIV0NULL(cost.monthly_influencer_cost * map.cost_split, EXTRACT(DAY FROM LAST_DAY(cost.month_start_date, 'month'))) AS daily_influencer_cost
    FROM
        monthly_influencer_cost AS cost
    LEFT JOIN
        generate_date
            ON cost.month_start_date = DATE_TRUNC("month", report_date)
    LEFT JOIN
        child_asin_mapping AS map
            ON cost.brand_code = map.brand_code
            AND cost.month_start_date = map.month_start_date
            AND cost.country_code = map.budget_country
),

daily_asin_leve_with_channel_split_us AS (
    SELECT
        influencer.report_date
        , influencer.month_start_date
        , influencer.brand_code AS brand_code
        , influencer.asin
        , influencer.country_code
        , marketing_split.channel
        , marketing_split.perc_share
        , COALESCE(influencer.daily_influencer_cost * marketing_split.perc_share, 0) AS daily_influencer_cost
    FROM
        daily_asin_level_influencer_cost AS influencer
    LEFT JOIN
        $stage_db.merge_brand_ebitda_marketing_split AS marketing_split
            ON influencer.month_start_date = marketing_split.month_start_date
            AND influencer.brand_code = marketing_split.brand_code
    WHERE
        LOWER(marketing_split.channel) != 'amz-int'
        AND influencer.country_code = 'US'
        AND marketing_split.perc_share > 0
        AND marketing_split.marketing_activity ILIKE '%influencer%'
    QUALIFY
        ROW_NUMBER() OVER (PARTITION BY asin, country_code, report_date, channel ORDER BY report_date) = 1
),

daily_asin_leve_with_channel_split_int AS (
    SELECT
        influencer.report_date
        , influencer.month_start_date
        , influencer.brand_code AS brand_code
        , influencer.asin
        , influencer.country_code
        , marketing_split.channel
        , marketing_split.perc_share
        , COALESCE(influencer.daily_influencer_cost * marketing_split.perc_share, 0) AS daily_influencer_cost
    FROM
        daily_asin_level_influencer_cost AS influencer
    LEFT JOIN
        $stage_db.merge_brand_ebitda_marketing_split AS marketing_split
            ON influencer.month_start_date = marketing_split.month_start_date
            AND influencer.brand_code = marketing_split.brand_code
    WHERE
        LOWER(marketing_split.channel) = 'amz-int'
        AND influencer.country_code != 'US'
        AND marketing_split.perc_share > 0
        AND marketing_split.marketing_activity ILIKE '%influencer%'
    QUALIFY
        ROW_NUMBER() OVER (PARTITION BY asin, country_code, report_date, channel ORDER BY report_date) = 1
),

-- Calculates the daily influencer estimated cost from the marketing budget per brand and channel
daily_brand_influencer_cost_estimate AS (
    SELECT
        generate_date.report_date
        , marketing_split.month_start_date
        , marketing_split.brand_code
        , marketing_split.channel
        , marketing_split.perc_share
        , SUM(marketing_split.marketing_budget) AS budget
        , DIV0(SUM(marketing_split.marketing_budget), EXTRACT(DAY FROM LAST_DAY(marketing_split.month_start_date, 'month'))) AS daily_marketing_budget_all_channel
        , COALESCE(daily_marketing_budget_all_channel, 0) AS daily_marketing_budget
    FROM
        $stage_db.merge_brand_ebitda_marketing_split AS marketing_split
    LEFT JOIN generate_date
        ON marketing_split.month_start_date = DATE_TRUNC("month", generate_date.report_date)
    WHERE marketing_split.marketing_activity ILIKE '%influencer%'
    GROUP BY ALL
),

-- amazon us
-- Calculates daily ASIN-level influencer estimated cost
-- Cost is calculated per ASIN via brand revenue contribution method
daily_asin_influencer_amz_us_estimate AS (
    SELECT
        influencer.report_date
        , influencer.month_start_date
        , influencer.channel
        , influencer.brand_code
        , asin_revenue.asin
        , asin_revenue.country_code
        , influencer.daily_marketing_budget_all_channel
        , COALESCE(influencer.daily_marketing_budget * asin_revenue.asin_brand_revenue_share, 0) AS daily_influencer_marketing_budget
    FROM
        daily_brand_influencer_cost_estimate AS influencer
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON influencer.report_date = asin_revenue.report_date
            AND influencer.brand_code = asin_revenue.brand_code
    WHERE
        LOWER(influencer.channel) = 'amz-us'
        AND asin_revenue.country_code = 'US'
),

-- amazon int
daily_asin_influencer_amz_int_estimate AS (
    SELECT
        influencer.report_date
        , influencer.month_start_date
        , influencer.channel
        , influencer.brand_code
        , asin_revenue.asin
        , asin_revenue.country_code
        , daily_marketing_budget_all_channel
        , COALESCE(influencer.daily_marketing_budget * asin_revenue.asin_brand_revenue_share, 0) AS daily_influencer_marketing_budget
    FROM
        daily_brand_influencer_cost_estimate AS influencer
    LEFT JOIN
        $curated_db.brand_ebitda_daily_asin_revenue_share AS asin_revenue
            ON influencer.report_date = asin_revenue.report_date
            AND influencer.brand_code = asin_revenue.brand_code
    WHERE
        LOWER(influencer.channel) = 'amz-int'
        AND asin_revenue.country_code != 'US'
),

-- Consolidates daily ASIN-level influencer estimated cost
estimates AS (
    SELECT *
    FROM daily_asin_influencer_amz_us_estimate

    UNION ALL

    SELECT *
    FROM daily_asin_influencer_amz_int_estimate
),

-- Selects the estimated daily influencer cost from the marketing budgets ONLY for months beyond the latest actual data.
estimated_influencer_spend AS (
    SELECT
        report_date
        , month_start_date
        , channel
        , brand_code
        , asin
        , country_code
        , daily_influencer_marketing_budget
    FROM
        estimates
    WHERE
        month_start_date > (SELECT latest_month_with_actual FROM actual_data_max_month)
),

-- join actual and estimates
final_consolidated_influencer_cost AS (
    SELECT
        report_date
        , month_start_date
        , channel
        , brand_code
        , asin
        , country_code
        , 'actual' AS attribution_type
        , daily_influencer_cost
    FROM
        daily_asin_leve_with_channel_split_us
    WHERE month_start_date <= (SELECT latest_month_with_actual FROM actual_data_max_month)

    UNION ALL

    SELECT
        report_date
        , month_start_date
        , channel
        , brand_code
        , asin
        , country_code
        , 'actual' AS attribution_type
        , daily_influencer_cost
    FROM
        daily_asin_leve_with_channel_split_int
    WHERE month_start_date <= (SELECT latest_month_with_actual FROM actual_data_max_month)

    UNION ALL

    SELECT
        report_date
        , month_start_date
        , channel
        , brand_code
        , asin
        , country_code
        , 'estimate' AS attribution_type
        , daily_influencer_marketing_budget
    FROM
        daily_asin_influencer_amz_us_estimate
    WHERE month_start_date > (SELECT latest_month_with_actual FROM actual_data_max_month)

    UNION ALL

        SELECT
        report_date
        , month_start_date
        , channel
        , brand_code
        , asin
        , country_code
        , 'estimate' AS attribution_type
        , daily_influencer_marketing_budget
    FROM
        daily_asin_influencer_amz_int_estimate
    WHERE month_start_date > (SELECT latest_month_with_actual FROM actual_data_max_month)
)

SELECT
    MD5(
        CAST(
            COALESCE(CAST(report_date AS VARCHAR), '') || '-' ||
            COALESCE(CAST(channel AS VARCHAR), '') || '-' ||
            COALESCE(CAST(brand_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(asin AS VARCHAR), '') || '-' ||
            COALESCE(CAST(country_code AS VARCHAR), '') AS VARCHAR
        )
    ) AS pk,
    *
FROM
    final_consolidated_influencer_cost
WHERE
    ABS(daily_influencer_cost) > 0
;

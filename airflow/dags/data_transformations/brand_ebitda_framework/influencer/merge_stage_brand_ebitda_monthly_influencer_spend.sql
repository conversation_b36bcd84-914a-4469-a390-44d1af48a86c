CREATE TABLE IF NOT EXISTS $stage_db.merge_brand_ebitda_monthly_influencer_spend AS
    SELECT

        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_brand_ebitda_monthly_influencer_spend
    WHERE 1 = 0;

BEGIN TRANSACTION;

MERGE INTO
    $stage_db.merge_brand_ebitda_monthly_influencer_spend AS tgt
USING
    $stage_db.dedupe_brand_ebitda_monthly_influencer_spend AS src

        ON 1 = 1
       AND src.pk = tgt.pk

WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._airbyte_raw_id = src._airbyte_raw_id,
    tgt._airbyte_extracted_at = src._airbyte_extracted_at,
    tgt._airbyte_generation_id = src._airbyte_generation_id,
    tgt._airbyte_meta = src._airbyte_meta,
    tgt.brand = src.brand,
    tgt.source = src.source,
    tgt.brand_code = src.brand_code,
    tgt.budget_country = src.budget_country,
    tgt.influencer_cost = src.influencer_cost,
    tgt.influencer_name = src.influencer_name,
    tgt.date_influencer_added = src.date_influencer_added,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    brand,
    source,
    brand_code,
    budget_country,
    influencer_cost,
    influencer_name,
    date_influencer_added,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._airbyte_raw_id,
    src._airbyte_extracted_at,
    src._airbyte_generation_id,
    src._airbyte_meta,
    src.brand,
    src.source,
    src.brand_code,
    src.budget_country,
    src.influencer_cost,
    src.influencer_name,
    src.date_influencer_added,
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);

-- delete all entries that are not from the latest airbyte update
DELETE FROM $stage_db.merge_brand_ebitda_monthly_influencer_spend
WHERE (record_updated_timestamp_utc) NOT IN (
    SELECT MAX(record_updated_timestamp_utc)
    FROM $stage_db.merge_brand_ebitda_monthly_influencer_spend
);

COMMIT;

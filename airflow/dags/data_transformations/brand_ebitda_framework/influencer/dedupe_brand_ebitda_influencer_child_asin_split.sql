CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_brand_ebitda_influencer_child_asin_split AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(brand AS VARCHAR), ''), '-',
            COALESCE(CAST(brand_code AS VARCHAR), ''), '-',
            COALESCE(CAST(budget_country AS VARCHAR), ''), '-',
            COALESCE(CAST(child_asin AS VARCHAR), ''), '-',
            COALESCE(CAST(product_title AS VARCHAR), ''), '-',
            COALESCE(CAST(month AS VARCHAR), '')
            )) AS pk,
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        brand,
        month,
        brand_code,
        child_asin,
        plan_split,
        actual_split,
        product_title,
        budget_country,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_brand_ebitda_influencer_child_asin_split
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY _airbyte_extracted_at DESC NULLS LAST) = 1
);
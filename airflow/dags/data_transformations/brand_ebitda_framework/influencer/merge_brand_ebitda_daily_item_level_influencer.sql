CREATE OR REPLACE TABLE $curated_db.brand_ebitda_daily_item_level_influencer (
    pk VARCHAR,
    brand_code VARCHAR,
    report_date DATE,
    month_start_date DATE,
    asin VARCHAR,
    country_code VARCHAR,
    channel VARCHAR,
    attribution_type VARCHAR,
    daily_influencer_cost FLOAT,
    record_created_timestamp_utc TIMESTAMP DEFAULT SYSDATE(),
    record_updated_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);

BEGIN TRANSACTION;

MERGE INTO $curated_db.brand_ebitda_daily_item_level_influencer AS t
USING $stage_db.merge_stg_brand_ebitda_daily_item_level_influencer AS s
ON
    t.pk = s.pk

WHEN MATCHED THEN
    UPDATE SET
        t.channel = s.channel,
        t.attribution_type = s.attribution_type,
        t.daily_influencer_cost = s.daily_influencer_cost,
        t.record_updated_timestamp_utc = SYSDATE()

WHEN NOT MATCHED THEN
    INSERT (
        pk,
        brand_code,
        report_date,
        month_start_date,
        asin,
        country_code,
        channel,
        attribution_type,
        daily_influencer_cost,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        s.pk,
        s.brand_code,
        s.report_date,
        s.month_start_date,
        s.asin,
        s.country_code,
        s.channel,
        s.attribution_type,
        s.daily_influencer_cost,
        SYSDATE(),
        SYSDATE()
    );

DELETE FROM $curated_db.brand_ebitda_daily_item_level_influencer
WHERE (pk, record_updated_timestamp_utc) NOT IN (
    SELECT pk, MAX(record_updated_timestamp_utc)
    FROM $curated_db.brand_ebitda_daily_item_level_influencer
    GROUP BY pk
);

COMMIT;

CREATE TABLE IF NOT EXISTS $stage_db.merge_brand_ebitda_field_mapping AS
    SELECT

        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_brand_ebitda_field_mapping
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_brand_ebitda_field_mapping AS tgt
USING
    $stage_db.dedupe_brand_ebitda_field_mapping AS src

        ON 1 = 1
       AND src.pk = tgt.pk

WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._airbyte_raw_id = src._airbyte_raw_id,
    tgt._airbyte_extracted_at = src._airbyte_extracted_at,
    tgt._airbyte_generation_id = src._airbyte_generation_id,
    tgt._airbyte_meta = src._airbyte_meta,
    tgt.type = src.type,
    tgt.amount = src.amount,
    tgt.category = src.category,
    tgt.wbr_total = src.wbr_total,
    tgt.bi_mapping = src.bi_mapping,
    tgt.marketplace = src.marketplace,
    tgt.wbr_subtotal = src.wbr_subtotal,
    tgt.sku_specified = src.sku_specified,
    tgt.time_allocation = src.time_allocation,
    tgt.is_wbr_main_source = src.is_wbr_main_source,
    tgt.product_allocation = src.product_allocation,
    tgt.simplified_description = src.simplified_description,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    type,
    amount,
    category,
    wbr_total,
    bi_mapping,
    marketplace,
    wbr_subtotal,
    sku_specified,
    time_allocation,
    is_wbr_main_source,
    product_allocation,
    simplified_description,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._airbyte_raw_id,
    src._airbyte_extracted_at,
    src._airbyte_generation_id,
    src._airbyte_meta,
    src.type,
    src.amount,
    src.category,
    src.wbr_total,
    src.bi_mapping,
    src.marketplace,
    src.wbr_subtotal,
    src.sku_specified,
    src.time_allocation,
    src.is_wbr_main_source,
    src.product_allocation,
    src.simplified_description,
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);

-- delete all entries that are not from the latest airbyte update
DELETE FROM $stage_db.merge_brand_ebitda_field_mapping
WHERE (record_updated_timestamp_utc) NOT IN (
    SELECT MAX(record_updated_timestamp_utc)
    FROM $stage_db.merge_brand_ebitda_field_mapping
);

COMMIT;
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_brand_ebitda_field_mapping AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(type AS VARCHAR), ''), '-',
            COALESCE(CAST(marketplace AS VARCHAR), ''), '-',
            COALESCE(CAST(sku_specified AS VARCHAR), ''), '-',
            COALESCE(CAST(simplified_description AS VARCHAR), ''), '-',
            COALESCE(CAST(category AS VARCHAR), ''), '-',
            COALESCE(CAST(amount AS VARCHAR), ''), '-',
            COALESCE(CAST(bi_mapping AS VARCHAR), ''), '-',
            COALESCE(CAST(wbr_subtotal AS VARCHAR), ''), '-',
            COALESCE(CAST(wbr_total AS VARCHAR), ''), '-',
            COALESCE(CAST(is_wbr_main_source AS VARCHAR), ''), '-',
            COALESCE(CAST(product_allocation AS VARCHAR), ''), '-',
            COALESCE(CAST(time_allocation AS VARCHAR), '')
            )) AS pk,
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        type,
        amount,
        category,
        wbr_total,
        bi_mapping,
        marketplace,
        wbr_subtotal,
        sku_specified,
        time_allocation,
        is_wbr_main_source,
        product_allocation,
        simplified_description,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_brand_ebitda_field_mapping
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY _airbyte_extracted_at DESC NULLS LAST) = 1
);
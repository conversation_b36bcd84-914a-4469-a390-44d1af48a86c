CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_brand_ebitda_field_mapping AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_brand_ebitda_field_mapping
    WHERE 1 = 0;

INSERT INTO $raw_db.log_brand_ebitda_field_mapping (
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    type,
    amount,
    category,
    wbr_total,
    bi_mapping,
    marketplace,
    wbr_subtotal,
    sku_specified,
    time_allocation,
    is_wbr_main_source,
    product_allocation,
    simplified_description,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        type,
        amount,
        category,
        wbr_total,
        bi_mapping,
        marketplace,
        wbr_subtotal,
        sku_specified,
        time_allocation,
        is_wbr_main_source,
        product_allocation,
        simplified_description,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_brand_ebitda_field_mapping;
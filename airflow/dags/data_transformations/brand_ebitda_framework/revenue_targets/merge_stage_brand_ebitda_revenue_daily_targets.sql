CREATE TABLE IF NOT EXISTS $stage_db.merge_brand_ebitda_revenue_daily_targets AS
    SELECT

        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_brand_ebitda_revenue_daily_targets
    WHERE 1 = 0;

BEGIN TRANSACTION;

MERGE INTO
    $stage_db.merge_brand_ebitda_revenue_daily_targets AS tgt
USING
    $stage_db.dedupe_brand_ebitda_revenue_daily_targets AS src

        ON 1 = 1
       AND src.pk = tgt.pk

WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._airbyte_raw_id = src._airbyte_raw_id,
    tgt._airbyte_extracted_at = src._airbyte_extracted_at,
    tgt._airbyte_generation_id = src._airbyte_generation_id,
    tgt._airbyte_meta = src._airbyte_meta,
    tgt.date = src.date,
    tgt.Brand = src.Brand,
    tgt.Channel = src.Channel,
    tgt.Brand_Code = src.Brand_Code,
    tgt.daily_target = src.daily_target,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _airbyte_raw_id,
    _airbyte_extracted_at,
    _airbyte_generation_id,
    _airbyte_meta,
    date,
    Brand,
    Channel,
    Brand_Code,
    daily_target,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._airbyte_raw_id,
    src._airbyte_extracted_at,
    src._airbyte_generation_id,
    src._airbyte_meta,
    src.date,
    src.Brand,
    src.Channel,
    src.Brand_Code,
    src.daily_target,
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);

-- delete all entries that are not from the latest airbyte update
DELETE FROM $stage_db.merge_brand_ebitda_revenue_daily_targets
WHERE (record_updated_timestamp_utc) NOT IN (
    SELECT MAX(record_updated_timestamp_utc)
    FROM $stage_db.merge_brand_ebitda_revenue_daily_targets
);

COMMIT;

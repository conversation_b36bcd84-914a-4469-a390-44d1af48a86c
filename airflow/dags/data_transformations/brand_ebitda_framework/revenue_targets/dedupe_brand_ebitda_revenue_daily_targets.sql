CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_brand_ebitda_revenue_daily_targets AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(brand AS VARCHAR), ''), '-',
            COALESCE(CAST(brand_code AS VARCHAR), ''), '-',
            COALESCE(CAST(channel AS VARCHAR), ''), '-',
            COALESCE(CAST(date AS VARCHAR), '')
            )) AS pk,
        _airbyte_raw_id,
        _airbyte_extracted_at,
        _airbyte_generation_id,
        _airbyte_meta,
        date,
        Brand,
        Channel,
        Brand_Code,
        daily_target,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_brand_ebitda_revenue_daily_targets
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY _airbyte_extracted_at DESC NULLS LAST) = 1
);
CREATE OR REPLACE TABLE  $stage_db.stg_brand_ebitda_amazon_asin_level_margins_all_compo_wbr_aggregate AS 

With dsp_sales AS (
    SELECT
        report_date,
        asin,
        country_code AS market_id,
        SUM(total_sales) AS total_sales_usd
    FROM DWH.PROD.FACT_AMAZON_ADVERTISING_DSP dsp
    WHERE country_code = 'US'
      AND order_name NOT ILIKE '%avm%'
      AND order_name NOT ILIKE '%added value%'
    GROUP BY report_date, asin, country_code
),
sessions_orders AS (
    SELECT 
        snapshot_date,
        item_id AS asin,
        brand_code,
        country_code,
        marketplace,
        seller_id,
        SUM(sessions) AS sessions_count,
        SUM(TOTAL_ORDER_ITEMS) AS orders_count
    FROM dwh.prod.FACT_ALL_ITEM_SALES_TRAFFIC_REPORT
    where country_code = 'US'
    GROUP BY snapshot_date, item_id, brand_code, country_code, marketplace, seller_id
    QUALIFY ROW_NUMBER() OVER(PARTITION BY snapshot_date,item_id,country_code ORDER BY sessions_count DESC) = 1 
),


promo_rebates AS (
    SELECT
        report_date,
        country_code,
        asin,
        brand_code,
        SUM(PROMO_REBATES_TOTAL) AS promo_amount
    FROM DWH_DEV.STAGING.STG_BRANDEBITDA_REVENUE_ASIN_LEVEL_CONSOLIDATED_DAILY
    where country_code = 'US'
    GROUP BY report_date, country_code, asin, brand_code
),

ppc_revenue AS (
    SELECT
        report_date,
        asin,
        brand_code,
        country_code,
        SUM(CASE WHEN campaign_type = 'Sponsored Products'       THEN attributed_sales_14d ELSE 0 END) AS ppc_revenue_sp,
        SUM(CASE WHEN campaign_type = 'Sponsored Brands'         THEN attributed_sales_14d ELSE 0 END) AS ppc_revenue_sb,
        SUM(CASE WHEN campaign_type = 'Sponsored Brands Video'   THEN attributed_sales_14d ELSE 0 END) AS ppc_revenue_sbv,
        SUM(CASE WHEN campaign_type = 'Sponsored Display'        THEN attributed_sales_14d ELSE 0 END) AS ppc_revenue_sd
    FROM DWH.PROD.FACT_AMAZON_AD_ASINS 
    where country_code = 'US'
    GROUP BY report_date, asin, brand_code, country_code
    QUALIFY ROW_NUMBER() OVER(PARTITION BY report_date, asin, country_code ORDER BY ppc_revenue_sp DESC) = 1
)

SELECT

    c."year_actual" as year,
    s.report_date AS report_date,
    s.brand_code as brand,
    s.asin,
    SUM(COALESCE(sess.orders_count, 0))   AS orders_count,
    SUM(COALESCE(sess.sessions_count, 0)) AS sessions_count,
    SUM(s.quantity_from_orders) as ordered_units,
    SUM(s.QUANTITY_FROM_RETURNS) as returns__units,
    SUM(s.NET_REVENUE)   AS net_revenue,
    SUM(COALESCE(promos.promo_amount, 0)) AS promo_amount,
 
    -SUM(s.total_cogs)              AS total_cogs,
    -SUM(s.CALCULATED_COGS_NS)      AS cogs_from_netsuite,
    -(COALESCE(SUM(s.total_cogs),0) - COALESCE(SUM(s.CALCULATED_COGS_NS),0)) AS cogs_non_netsuite,

    -(COALESCE(SUM(s.TOTAL_FULFILLMENT_FEES), 0) + COALESCE(SUM(s.TOTAL_SELLING_FEES), 0)) AS total_fba_fees,
    -COALESCE(SUM(s.FULFILLMENT_FEES_FROM_ORDERS), 0) AS fulfilment_fees_from_orders,
    
    -COALESCE(SUM(s.FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_NON_RETURNS)
        + SUM(s.FULFILLMENT_FEES_FROM_NON_ORDER_SKU_ALLOCATED_RETURNS)
        + SUM(FULFILLMENT_FEES_FROM_NON_ORDER_NON_SKU_ALLOCATED), 0) AS fulfilment_fees_from_non_orders,
    
    -SUM(s.TOTAL_SELLING_FEES)            AS selling_fees,
    -SUM(s.TOTAL_AMZ_STORAGE_FEE)           AS storage_fees,

    -SUM(COALESCE(s.DAILY_3PL_INBOUND, 0))    AS DAILY_3PL_INBOUND,
    -SUM(COALESCE(s.DAILY_3PL_OUTBOUND, 0))   AS DAILY_3PL_OUTBOUND,
    -SUM(COALESCE(s.DAILY_3PL_STORAGE, 0))    AS DAILY_3PL_STORAGE,
    -1*(SUM(TOTAL_3PL_EXCL_FREIGHT)) AS total_3pl_excl_freight,
    
    -SUM(s.TOTAL_DAILY_GIVEAWAYS_COST)            AS giveaways,
    
    -SUM(s.TOTAL_DSP)            AS dsp_cost,

    -SUM(s.sv_ppc_spend)                  AS sb_spend,
    -SUM(s.sd_ppc_spend)                  AS sd_spend,
    -SUM(s.sbv_ppc_spend)                 AS sbv_spend,
    -SUM(s.sp_ppc_spend)                  AS sp_spend,
    -SUM(s.ALLOCATED_VINE)                AS allocated_vine,
    -SUM(s.TOTAL_PPC)               AS total_ppc_spend,

    -COALESCE(SUM(s.TOTAL_PPC),0) -COALESCE(SUM(s.TOTAL_DSP),0)  AS total_ppc_dsp_cost,

    SUM(COALESCE(dsp.total_sales_usd, 0)) AS dsp_total_sales_usd,
    
    

    SUM(COALESCE(ppc.ppc_revenue_sb, 0))  AS SB_revenue,
    SUM(COALESCE(ppc.ppc_revenue_sd, 0))  AS SD_revenue,
    SUM(COALESCE(ppc.ppc_revenue_sbv, 0)) AS SBV_revenue,
    SUM(COALESCE(ppc.ppc_revenue_sp, 0))  AS SP_revenue,
    
    SUM(COALESCE(ppc.ppc_revenue_sp, 0)) 
        + SUM(COALESCE(ppc.ppc_revenue_sb, 0)) 
        + SUM(COALESCE(ppc.ppc_revenue_sbv, 0)) 
        + SUM(COALESCE(ppc.ppc_revenue_sd, 0)) AS total_ppc_revenue,

   
    
    -SUM(s.TOTAL_DEAL_FEES)     AS total_deal_coupon_fees,
   
    -SUM(s.TOTAL_GOOGLE)              AS google_ads_spend,
   
    -SUM(s.TOTAL_AFFILIATES)          AS total_affiliates_spend,


    -SUM(facebook_ad_spend_api) AS fb_ads_spend,
    -SUM(fb_ads_misfit_agency_cost) AS fb_ads_misfit_agency_cost,
    -SUM(s.DAILY_UGC_COST) as ugc_spend,
   
    -SUM(s.TOTAL_FACEBOOK_UGC)            AS total_fb_ugc_spend,

   
    -SUM(s.DAILY_TIKTOK_AD_SPEND) as daily_tiktok_ads_spend,
    -SUM(daily_influencer_cost) as influencers_cost,
    -SUM(s.TIKTOK_AND_INFLUENCER_COST) AS tiktok_ads_influencer,

    -sum(s.total_daily_certification_cost) as certifications_cost,

    -SUM(s.total_publication_cost) AS publications_cost,

    -(COALESCE(SUM(s.total_publication_cost), 0) + COALESCE(SUM(s.total_daily_certification_cost), 0)) AS total_publication_cert_fees,
    
    -SUM(s.DAILY_ROYALTY_COST)    AS royalties,
    

    0    AS freight_,
   

    COALESCE(SUM(s.NET_REVENUE),0)
        -COALESCE(SUM(s.total_cogs), 0)
        -COALESCE(SUM(s.TOTAL_FULFILLMENT_FEES), 0)
        -COALESCE(SUM(s.TOTAL_SELLING_FEES), 0)
        -COALESCE(SUM(s.TOTAL_AMZ_STORAGE_FEE), 0)
        -COALESCE(SUM(TOTAL_3PL_EXCL_FREIGHT), 0)
        -COALESCE(SUM(s.TOTAL_DAILY_GIVEAWAYS_COST), 0)
        -COALESCE(SUM(s.TOTAL_DSP), 0)
        -COALESCE(SUM(s.TOTAL_PPC), 0)
        -COALESCE(SUM(s.TOTAL_DEAL_FEES), 0)
        -COALESCE(SUM(s.TOTAL_GOOGLE), 0) 
        -COALESCE(SUM(s.TOTAL_AFFILIATES), 0)
        -COALESCE(SUM(s.TOTAL_FACEBOOK_UGC),0)
        -COALESCE(SUM(s.TIKTOK_AND_INFLUENCER_COST), 0)  
        -COALESCE(SUM(s.total_daily_certification_cost), 0)
        -COALESCE(SUM(s.total_publication_cost), 0)
        -COALESCE(SUM(s.DAILY_ROYALTY_COST), 0)
    AS contribution_margin_usd, 


    DIV0NULL(contribution_margin_usd, SUM(net_revenue)) AS contribution_margin_percent  

FROM 
    DWH_DEV.STAGING.stg_brand_ebitda_amazon_asin_level_margins_all_components s
LEFT JOIN 
    dwh.prod.calendar_dimension c
    ON s.report_date = c."date_actual"
LEFT JOIN
    dsp_sales dsp
    ON s.report_date = dsp.report_date
    AND s.asin = dsp.asin
    AND s.country_code = dsp.market_id
LEFT JOIN
    sessions_orders sess
    ON s.report_date = sess.snapshot_date
    AND s.asin = sess.asin
    AND s.country_code = sess.country_code
LEFT JOIN
    promo_rebates promos
    ON s.report_date = promos.report_date
    AND s.asin = promos.asin
    AND s.country_code = promos.country_code
LEFT JOIN
    ppc_revenue ppc
    ON s.report_date = ppc.report_date
    AND s.asin = ppc.asin
       AND s.country_code = ppc.country_code

WHERE 
    s.country_code = 'US'
GROUP BY 
    s.report_date,
    s.asin,
    s.brand_code,
    c."year_actual",
    c."week_of_year"
ORDER BY 
    year DESC
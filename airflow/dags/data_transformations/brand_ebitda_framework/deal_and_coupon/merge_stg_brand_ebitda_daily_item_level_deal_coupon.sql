CREATE OR REPLACE TABLE $stage_db.merge_stg_brand_ebitda_daily_item_level_deal_coupon AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.create_stg_brand_ebitda_daily_item_level_deal_coupon
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_stg_brand_ebitda_daily_item_level_deal_coupon AS tgt
USING
    (
        SELECT
            *
        FROM $stage_db.create_stg_brand_ebitda_daily_item_level_deal_coupon
        QUALIFY ROW_NUMBER() OVER (PARTITION BY pk ORDER BY purchased_date DESC) = 1 -- dedupe
    ) AS src
        ON tgt.pk = src.pk

WHEN MATCHED THEN
    -- Update the record if it already exists
    UPDATE SET
        tgt.coupon_fee = src.coupon_fee,
        tgt.deal_fee = src.deal_fee,
        tgt.deal_and_coupon_fee = src.deal_and_coupon_fee,
        tgt.record_updated_timestamp_utc = SYSDATE()

WHEN NOT MATCHED THEN
    INSERT (
        pk,
        purchased_date,
        asin,
        brand_code,
        country_code,
        coupon_fee,
        deal_fee,
        deal_and_coupon_fee,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        src.pk,
        src.purchased_date,
        src.asin,
        src.brand_code,
        src.country_code,
        src.coupon_fee,
        src.deal_fee,
        src.deal_and_coupon_fee,
        SYSDATE(),
        SYSDATE()
    );

CREATE OR REPLACE TABLE $curated_db.brand_ebitda_daily_item_level_deal_coupon (
    pk VARCHAR,
    purchased_date DATE,
    asin VARCHAR,
    brand_code VARCHAR,
    country_code VARCHAR,
    coupon_fee FLOAT,
    deal_fee FLOAT,
    deal_and_coupon_fee FLOAT,
    record_created_timestamp_utc TIMESTAMP DEFAULT SYSDATE(),
    record_updated_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);

BEGIN TRANSACTION;

MERGE INTO $curated_db.brand_ebitda_daily_item_level_deal_coupon AS t
USING $stage_db.merge_stg_brand_ebitda_daily_item_level_deal_coupon AS s
ON
    t.pk = s.pk

WHEN MATCHED THEN
    UPDATE SET
        t.purchased_date = s.purchased_date,
        t.asin = s.asin,
        t.brand_code = s.brand_code,
        t.country_code = s.country_code,
        t.coupon_fee = s.coupon_fee,
        t.deal_fee = s.deal_fee,
        t.deal_and_coupon_fee = s.deal_and_coupon_fee,
        t.record_updated_timestamp_utc = SYSDATE()

WHEN NOT MATCHED THEN
    INSERT (
        pk,
        purchased_date,
        asin,
        brand_code,
        country_code,
        coupon_fee,
        deal_fee,
        deal_and_coupon_fee,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    ) VALUES (
        s.pk,
        s.purchased_date,
        s.asin,
        s.brand_code,
        s.country_code,
        s.coupon_fee,
        s.deal_fee,
        s.deal_and_coupon_fee,
        SYSDATE(),
        SYSDATE()
    );

DELETE FROM $curated_db.brand_ebitda_daily_item_level_deal_coupon
WHERE (pk, record_updated_timestamp_utc) NOT IN (
    SELECT pk, MAX(record_updated_timestamp_utc)
    FROM $curated_db.brand_ebitda_daily_item_level_deal_coupon
    GROUP BY pk
);

COMMIT;

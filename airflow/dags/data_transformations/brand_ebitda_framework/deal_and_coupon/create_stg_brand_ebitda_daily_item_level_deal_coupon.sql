CREATE OR REPLACE TRANSIENT TABLE $stage_db.create_stg_brand_ebitda_daily_item_level_deal_coupon AS

WITH deal_and_coupon_fee AS (
   -- old promo model
   SELECT
        purchased_date_local AS purchased_date,
        asin,
        brand_code,
        country_code,
        SUM(CASE WHEN campaign_type = 'COUPON' AND purchased_date_local < DATE_TRUNC("week", CURRENT_DATE) THEN total_promo_cost ELSE total_promo_cost_est_w END) AS coupon_fee,
        SUM(CASE WHEN campaign_type != 'COUPON' THEN total_promo_cost ELSE 0 END) AS deal_fee,
        COALESCE(coupon_fee, 0) + COALESCE(deal_fee, 0) AS deal_and_coupon_fee
    FROM dwh_dev.staging.promotion_fees_classification
    WHERE purchased_date_local < '2025-06-01'
    GROUP BY purchased_date_local, asin, brand_code, country_code
),

-- new deal model
base_deal_data_raw AS (
    SELECT
        country_code, brand_code, asin, deal_type, campaign_status, campaign_id, item_sales, included,
        to_date(start_time) AS start_date_only, to_date(END_time) AS END_date_only_raw, to_date(first_cancel_date) AS first_cancel_date_only_raw,
        event_name, seller_price
    FROM dwh.prod.fact_amazon_deals
    WHERE item_sales > 0 and deal_price > 0 and start_time > '2025-06-02'
    union
    SELECT
        country_code, brand_code, asin, deal_type, campaign_status, campaign_id, item_sales, included,
        to_date(start_time) AS start_date_only, to_date(END_time) AS END_date_only_raw, to_date(first_cancel_date) AS first_cancel_date_only_raw,
        event_name, seller_price
    FROM dwh_dev.prod.fact_amazon_deals
    WHERE item_sales > 0 and deal_price > 0 and start_time > '2025-06-02'
    QUALIFY row_number() over (
        PARTITION BY campaign_id, asin
        ORDER BY END_date_only_raw desc, first_cancel_date_only_raw desc, item_sales desc, campaign_status desc, deal_type desc, seller_price desc, start_date_only desc, brand_code desc
    ) = 1
),

base_deal_data AS (
    SELECT
        country_code,
        max(brand_code) AS brand_code,
        asin,
        campaign_id,
        min(start_date_only) AS start_date_only,
        max(deal_type) AS deal_type,
        max(campaign_status) AS campaign_status,
        sum(item_sales) AS item_sales,
        max(included) AS included,
        max(END_date_only_raw) AS END_date_only_raw,
        max(first_cancel_date_only_raw) AS first_cancel_date_only_raw,
        max(event_name) AS event_name,
        max (seller_price) AS seller_price,
        coalesce(max(first_cancel_date_only_raw), max(END_date_only_raw)) AS effective_END_date_only,
        (datediff('day', min(start_date_only), coalesce(max(first_cancel_date_only_raw), max(END_date_only_raw))) + 1) AS total_deal_duration_days
    FROM
        base_deal_data_raw
    GROUP BY
        country_code, asin, campaign_id
),
campaign_asin_counts AS (
    SELECT
    campaign_id,
    count(DISTINCT CASE WHEN included = true THEN asin ELSE NULL END) AS total_asins_in_campaign
    FROM base_deal_data
    GROUP BY campaign_id
),

deal_daily_expansion AS (
    SELECT bd.*, dateadd(day, seq.value::int, bd.start_date_only) AS current_deal_date
    FROM base_deal_data bd, lateral flatten(input => array_generate_range(0, datediff('day', bd.start_date_only, bd.effective_END_date_only) + 1)) AS seq
),

weekly_day_assignment AS (
    SELECT dde.*, DATEADD(day, - (CASE WHEN DAYOFWEEK(dde.current_deal_date) = 7 THEN 0 ELSE DAYOFWEEK(dde.current_deal_date) END), dde.current_deal_date) AS week_start_date
    FROM deal_daily_expansion dde
),

shared_weekly_fixed_fee AS (
    SELECT campaign_id, week_start_date,
        count(DISTINCT CASE WHEN included = true THEN asin ELSE NULL END) AS DISTINCT_asins_in_week,
        count(current_deal_date) AS days_in_week_for_campaign_week,
        (70 * count(current_deal_date)) AS fixed_fee_component_for_campaign_week
    FROM weekly_day_assignment
    WHERE included = true
    GROUP BY campaign_id, week_start_date
),

weekly_assigned_with_asin_counts AS (
    SELECT wda.*, cac.total_asins_in_campaign
    FROM weekly_day_assignment wda
    INNER JOIN campaign_asin_counts cac
        ON wda.campaign_id = cac.campaign_id
    WHERE wda.included = true
),

deal_fee_params AS (
    SELECT DISTINCT
        country_code,
        deal_type,
        -- Calculate fixed_rate_per_day_per_asin_standard
        CASE
            WHEN country_code = 'US' THEN 70.0
            WHEN country_code = 'UK' THEN 3.4
            WHEN country_code = 'DE' THEN 4.68
            ELSE 2.93
        END AS fixed_rate_per_day_per_asin_standard,
        -- Calculate ca_deal_total_fixed_fee_by_type
        CASE
            WHEN country_code = 'CA' THEN
            CASE
                WHEN deal_type = 'BEST_DEAL' THEN 29.21
                WHEN deal_type = 'LIGHTNING_DEAL' THEN 14.6
                ELSE NULL -- Explicitly state NULL if other deal_types for CA are not covered
            END
        ELSE NULL
        END AS ca_deal_total_fixed_fee_by_type,
        -- Calculate sales_percentage_rate
        CASE
            WHEN country_code = 'CA' THEN 0.0
            WHEN country_code = 'US' THEN 0.01
            WHEN country_code = 'UK' THEN 0.0075
            WHEN country_code = 'DE' THEN 0.0075
            ELSE 0.0075
        END AS sales_percentage_rate,
        -- Calculate campaign_cap_amount
        CASE
            WHEN country_code = 'US' THEN 2000.0
            WHEN country_code = 'UK' THEN 543.35
            WHEN country_code = 'DE' THEN 585.31
            ELSE 468.25
        END AS campaign_cap_amount
    FROM weekly_assigned_with_asin_counts
),

prime_day_base_fees AS (
    SELECT DISTINCT
        country_code,
        deal_type,
        -- Best Deal Base Fee
        CASE
            WHEN country_code = 'US' THEN 1000.0
            WHEN country_code = 'CA' THEN 58.47
            WHEN country_code = 'UK' THEN 136.0
            WHEN country_code = 'DE' THEN 164.0
            ELSE 93.62
        END AS BEST_DEAL_base_fee,
        -- Lightning Deal Base Fee
        CASE
            WHEN country_code = 'US' THEN 500.0
            WHEN country_code = 'CA' THEN 29.23
            WHEN country_code = 'UK' THEN 68.0
            WHEN country_code = 'DE' THEN 82.0
            ELSE 46.81
        END AS lightning_deal_base_fee
    FROM weekly_assigned_with_asin_counts
),

daily_agg_for_fees AS (
    SELECT
        wwac.country_code,
        wwac.brand_code,
        wwac.asin,
        wwac.deal_type,
        wwac.campaign_id,
        wwac.item_sales,
        wwac.start_date_only,
        wwac.effective_END_date_only, -- ya viene consolidada de base_deal_data
        wwac.current_deal_date,
        wwac.total_asins_in_campaign,
        wwac.total_deal_duration_days,
        wwac.event_name,
        wwac.included,
        DIV0(wwac.item_sales, wwac.total_deal_duration_days) AS proportional_item_sales_by_day,
        1 AS days_in_day,
        MAX(CASE
            WHEN wwac.current_deal_date = '2025-07-06' and (wwac.event_name is not NULL and wwac.event_name <> '') THEN 1 ELSE 0 END)
        OVER (PARTITION BY wwac.campaign_id, wwac.asin, wwac.start_date_only) AS is_prime_day_deal_flag
    FROM
        weekly_assigned_with_asin_counts wwac
    WHERE wwac.included = true
    GROUP BY
        wwac.country_code, wwac.brand_code, wwac.asin, wwac.deal_type,
        wwac.campaign_id, wwac.item_sales, wwac.start_date_only,
        wwac.effective_end_date_only, -- mantenemos en GROUP BY si la queremos en el output, pero ya está consolidada
        wwac.current_deal_date, wwac.total_asins_in_campaign, wwac.total_deal_duration_days,
        wwac.event_name, wwac.included
),

final_fee_calculation_base AS (
    SELECT daff.*,
        CASE WHEN daff.is_prime_day_deal_flag = 1 THEN 'EVENT' ELSE 'BAU' END AS fee_calculation_type,
        dfp.fixed_rate_per_day_per_asin_standard,
        dfp.ca_deal_total_fixed_fee_by_type,
        dfp.sales_percentage_rate,
        dfp.campaign_cap_amount
    FROM daily_agg_for_fees daff INNER JOIN deal_fee_params dfp

        ON daff.country_code = dfp.country_code and daff.deal_type = dfp.deal_type
),

uncapped_values_per_day AS (
    SELECT ffcb.* exclude (fixed_rate_per_day_per_asin_standard, ca_deal_total_fixed_fee_by_type, sales_percentage_rate, campaign_cap_amount),
        ffcb.fixed_rate_per_day_per_asin_standard, ffcb.ca_deal_total_fixed_fee_by_type,
        ffcb.sales_percentage_rate, ffcb.campaign_cap_amount,
        (CASE ffcb.country_code WHEN 'CA' THEN NULL ELSE DIV0(ffcb.fixed_rate_per_day_per_asin_standard, ffcb.total_asins_in_campaign) END) AS proportional_fixed_fee_per_day,
        (ffcb.sales_percentage_rate * ffcb.proportional_item_sales_by_day) AS proportional_sales_fee_component,
        CASE ffcb.fee_calculation_type
            WHEN 'EVENT' THEN
                CASE ffcb.deal_type WHEN 'BEST_DEAL' THEN (CASE ffcb.country_code WHEN 'US' THEN 1000.0 WHEN 'CA' THEN 58.47 WHEN 'UK' THEN 136.0 WHEN 'DE' THEN 164.0 ELSE 93.62 END) / ffcb.total_asins_in_campaign / ffcb.total_deal_duration_days
                    WHEN 'LIGHTNING_DEAL' THEN CASE WHEN ffcb.current_deal_date = ffcb.start_date_only THEN (CASE ffcb.country_code WHEN 'US' THEN 500.0 WHEN 'CA' THEN 29.23 WHEN 'UK' THEN 68.0 WHEN 'DE' THEN 82.0 ELSE 46.81 END) / ffcb.total_asins_in_campaign ELSE 0.0 END
                    ELSE 0.0 END
            WHEN 'BAU' THEN
                (CASE ffcb.country_code WHEN 'CA' THEN DIV0(ffcb.ca_deal_total_fixed_fee_by_type, ffcb.total_asins_in_campaign) / ffcb.total_deal_duration_days ELSE DIV0(ffcb.fixed_rate_per_day_per_asin_standard, ffcb.total_asins_in_campaign) END) + (ffcb.sales_percentage_rate * ffcb.proportional_item_sales_by_day)
            ELSE 0.0 END AS calculated_final_value_uncapped
    FROM final_fee_calculation_base AS ffcb
),

campaign_total_fees AS (
    SELECT
        campaign_id,
        max(campaign_cap_amount) AS campaign_cap,
        sum(calculated_final_value_uncapped) AS total_campaign_fee_uncapped
    FROM uncapped_values_per_day GROUP BY campaign_id
),
final_allocated_fees AS (
    SELECT ufpd.* exclude (calculated_final_value_uncapped),
        ctf.total_campaign_fee_uncapped,
        ctf.campaign_cap AS campaign_cap_amount,
        DIV0(least(ctf.campaign_cap, ctf.total_campaign_fee_uncapped), ctf.total_campaign_fee_uncapped) AS allocation_factor,
        (ufpd.calculated_final_value_uncapped * DIV0(least(ctf.campaign_cap, ctf.total_campaign_fee_uncapped), ctf.total_campaign_fee_uncapped)) AS calculated_final_value_capped
    FROM uncapped_values_per_day ufpd INNER JOIN campaign_total_fees ctf
        ON ufpd.campaign_id = ctf.campaign_id
),

-- new coupon model
exchange_rates AS (
    SELECT
        CASE
            WHEN transactional_currency = 'EURO' THEN 'EUR'
            ELSE transactional_currency
        END AS transactional_currency_adjusted,
        effective_date,
        exchange_rate
    FROM DWH.NETSUITE.foreign_exchange
    WHERE BASE_CURRENCY = 'USD'
    AND effective_date >= '2025-06-02'
),

raw_order_details_usd AS (
    SELECT
        DATE(CONVERT_TIMEZONE('UTC', 'America/Los_Angeles', fao."purchase_date_utc")) AS purchase_date_local,
        fao."asin" AS ASIN,
        fao."brand" AS BRAND,
        fao."country_code" AS COUNTRY_CODE,
        fao."currency" AS CURRENCY,
        fao."quantity" AS quantity,
        fao_amz."promotion_ids",
        (fao."gross_revenue_lc" *
            CASE
                WHEN fao."currency" = 'USD' THEN 1.0
                ELSE COALESCE(er.exchange_rate, 0.0)
            END
        ) AS gross_revenue_usd,
        (
            (
                CASE
                    WHEN fao."currency" = 'USD' THEN fao."gross_revenue_lc" * 1.0
                    ELSE fao."gross_revenue_lc" *  COALESCE(er.exchange_rate, 0.0)
                END
            ) - fao."item_promotion_discount_usd"
        ) AS revenue_after_coupon_line_item,
        TRIM(REGEXP_REPLACE(fao_amz."promotion_ids", '.*PLM-', '')) AS promotion_code_extracted_line_item,
        CASE
            WHEN fao_amz."promotion_ids" LIKE '%PLM%' THEN 1
            ELSE 0
        END AS is_plm_coupon_redeemed_flag,
        DATEADD(day, - (CASE WHEN DAYOFWEEK(DATE(CONVERT_TIMEZONE('UTC', 'America/Los_Angeles', fao."purchase_date_utc"))) = 7 THEN 0 ELSE DAYOFWEEK(DATE(CONVERT_TIMEZONE('UTC', 'America/Los_Angeles', fao."purchase_date_utc"))) END), DATE(CONVERT_TIMEZONE('UTC', 'America/Los_Angeles', fao."purchase_date_utc"))) AS week_start_date
    FROM
        DWH.PROD.FACT_ALL_ORDERS fao
    INNER JOIN
        DWH.PROD.FACT_AMAZON_ORDERS fao_amz ON fao."order_id" = fao_amz."amazon_order_id"
    LEFT JOIN
        exchange_rates er ON DATE(CONVERT_TIMEZONE('UTC', 'America/Los_Angeles', fao."purchase_date_utc")) = er.effective_date
                            AND fao."currency" = er.transactional_currency_adjusted
    WHERE
       purchase_date_local >= '2025-06-02'
),

daily_aggregated_metrics AS (
    SELECT
        purchase_date_local,
        ASIN,
        BRAND,
        COUNTRY_CODE,
        CURRENCY,
        week_start_date,
        SUM(quantity) AS total_quantity,
        SUM(CASE WHEN is_plm_coupon_redeemed_flag = 1 THEN quantity ELSE 0 END) AS coupon_redeemed_quantity,
        DIV0(
            SUM(CASE WHEN is_plm_coupon_redeemed_flag = 1 THEN quantity ELSE 0 END),
            SUM(quantity)
        ) AS coupon_redemption_percentage,
        SUM(CASE WHEN is_plm_coupon_redeemed_flag = 1 THEN revenue_after_coupon_line_item ELSE 0 END) AS ops_usd_for_coupon_orders,
        MAX(CASE WHEN is_plm_coupon_redeemed_flag = 1 THEN promotion_code_extracted_line_item ELSE NULL END) AS promotion_code_extracted,
        MAX(CASE COUNTRY_CODE
                WHEN 'US' THEN 5.00
                WHEN 'CA' THEN 0.44
                WHEN 'UK' THEN 2.72
                WHEN 'DE' THEN 4.68
                ELSE 2.34
            END) AS fixed_fee_amount_by_country,
        MAX(CASE COUNTRY_CODE
                WHEN 'CA' THEN 0.0
                WHEN 'US' THEN 0.025
                WHEN 'UK' THEN 0.015
                WHEN 'DE' THEN 0.015
                ELSE 0.01
            END) AS sales_percentage_rate
    FROM raw_order_details_usd
    GROUP BY
        purchase_date_local,
        ASIN,
        BRAND,
        COUNTRY_CODE,
        CURRENCY,
        week_start_date
),

redeeming_asins_per_group AS (
    SELECT
        purchase_date_local,
        promotion_code_extracted,
        COUNTRY_CODE,
        BRAND,
        week_start_date,
        COUNT(DISTINCT ASIN) AS distinct_redeeming_asins_in_group,
        MAX(fixed_fee_amount_by_country) AS fixed_fee_amount_for_this_group
    FROM daily_aggregated_metrics
    WHERE
        coupon_redemption_percentage > 0
        AND promotion_code_extracted IS NOT NULL
        AND promotion_code_extracted <> ''
    GROUP BY
        purchase_date_local,
        promotion_code_extracted,
        COUNTRY_CODE,
        BRAND,
        week_start_date
),

joined_coupon_data AS (
    SELECT
        dam.*,
        rag.distinct_redeeming_asins_in_group,
        rag.fixed_fee_amount_for_this_group
    FROM daily_aggregated_metrics dam
    INNER JOIN redeeming_asins_per_group rag
        ON dam.purchase_date_local = rag.purchase_date_local
        AND dam.promotion_code_extracted = rag.promotion_code_extracted
        AND dam.COUNTRY_CODE = rag.COUNTRY_CODE
        AND dam.BRAND = rag.BRAND
        AND dam.week_start_date = rag.week_start_date
    WHERE
        dam.coupon_redemption_percentage > 0
        AND dam.promotion_code_extracted IS NOT NULL
        AND dam.promotion_code_extracted <> ''
),

new_coupon_data AS (
    SELECT
        purchase_date_local,
        asin,
        brand,
        country_code,
        week_start_date,
        (WEEKOFYEAR(week_start_date) + 1) AS week_number_of_year,
        total_quantity,
        coupon_redeemed_quantity,
        coupon_redemption_percentage,
        ops_usd_for_coupon_orders,
        fixed_fee_amount_for_this_group,
        distinct_redeeming_asins_in_group,
        DIV0(fixed_fee_amount_for_this_group, distinct_redeeming_asins_in_group) AS prorated_fixed_fee_component,
        sales_percentage_rate,
        (DIV0(fixed_fee_amount_for_this_group, distinct_redeeming_asins_in_group)) + (sales_percentage_rate * ops_usd_for_coupon_orders) AS calculated_coupon_fee
    FROM joined_coupon_data
),

joined_deal_and_coupon AS (
    -- old deal and coupon
    SELECT
        purchased_date,
        asin,
        brand_code,
        country_code,
        SUM(deal_fee) AS deal_fee,
        SUM(coupon_fee) AS coupon_fee
    FROM deal_and_coupon_fee
    WHERE purchased_date < '2025-06-01' -- new promo model started on June 1, 2025
    GROUP BY ALL

    UNION ALL
    -- new deal
    SELECT
        current_deal_date,
        asin,
        brand_code,
        country_code,
        SUM(COALESCE(calculated_final_value_capped, 0)) AS deal_fee,
        0 AS coupon_fee
    FROM final_allocated_fees
    WHERE current_deal_date >= '2025-06-01' -- new promo model started on June 1, 2025
    GROUP BY ALL

    UNION ALL
    -- new coupon
    SELECT
        purchase_date_local,
        asin,
        brand AS brand_code,
        country_code,
        0 AS deal_fee,
        SUM(COALESCE(calculated_coupon_fee, 0)) AS coupon_fee
    FROM new_coupon_data
    WHERE purchase_date_local >= '2025-06-01' -- new promo model started on June 1, 2025
    GROUP BY ALL
),

final_deal_and_coupon_data AS (
    SELECT
        purchased_date,
        asin,
        brand_code,
        country_code,
        SUM(COALESCE(coupon_fee, 0)) AS coupon_fee,
        SUM(COALESCE(deal_fee, 0)) AS deal_fee,
        SUM(COALESCE(coupon_fee, 0)) + SUM(COALESCE(deal_fee, 0)) AS deal_and_coupon_fee
    FROM joined_deal_and_coupon
    GROUP BY ALL
)

SELECT
    MD5(
        CAST(
            COALESCE(CAST(purchased_date AS VARCHAR), '') || '-' ||
            COALESCE(CAST(asin AS VARCHAR), '') || '-' ||
            COALESCE(CAST(brand_code AS VARCHAR), '') || '-' ||
            COALESCE(CAST(country_code AS VARCHAR), '') AS VARCHAR
        )
    ) AS pk,
    *
    FROM final_deal_and_coupon_data
WHERE
    deal_and_coupon_fee > 0
;
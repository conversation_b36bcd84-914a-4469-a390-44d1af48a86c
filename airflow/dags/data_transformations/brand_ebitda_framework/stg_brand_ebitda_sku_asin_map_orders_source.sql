CREATE OR REPLACE TABLE  $stage_db.stg_brand_ebitda_sku_asin_map_orders_source_table  as

    SELECT DISTINCT
"sku" AS sku, 
"country_code" AS country_code,
"brand" AS brand_code,  
"asin" AS asin,  
"product_name" AS product_name,
CASE 
    WHEN "country_code" IN ('DE', 'FR', 'IT', 'ES', 'NL', 'SE', 'PL', 'TR', 'BE') THEN 'EU'
    ELSE "country_code"
END AS market_place_region
FROM DWH.PROD.fact_all_orders AS orders 
WHERE "marketplace" = 'AMAZON'
    AND "sales_channel" not like '%non-amazon%'
    AND "purchase_date_utc" >= '2022-01-01'
    --AND "order_status" NOT IN ('cancelled', 'null') 
    AND "order_status" IS NOT NULL
    AND "country_code" IS NOT NULL

QUALIFY ROW_NUMBER() OVER(PARTITION BY "sku", orders."country_code" ORDER BY "purchase_date_utc" DESC) = 1 ;
CREATE TABLE IF NOT EXISTS $curated_db.fact_amazon_ads_attribution_performance_report AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_performance_report
    WHERE 1 = 0;


MERGE INTO
    $curated_db.fact_amazon_ads_attribution_performance_report AS tgt
USING
    $stage_db.dedupe_performance_report AS src
        ON 1 = 1
       AND src.campaignid = tgt.campaignid
       AND src.reportdate = tgt.reportdate
WHEN MATCHED AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.accountId = src.accountId,
    tgt.accountName = src.accountName,
    tgt.adGroupId = src.adGroupId,
    tgt.advertiserName = src.advertiserName,
    tgt.attributedAddToCartClicks14d = src.attributedAddToCartClicks14d,
    tgt.attributedDetailPageViewsClicks14d = src.attributedDetailPageViewsClicks14d,
    tgt.attributedPurchases14d = src.attributedPurchases14d,
    tgt.attributedSales14d = src.attributedSales14d,
    tgt.attributedTotalAddToCartClicks14d = src.attributedTotalAddToCartClicks14d,
    tgt.attributedTotalDetailPageViewsClicks14d = src.attributedTotalDetailPageViewsClicks14d,
    tgt.attributedTotalPurchases14d = src.attributedTotalPurchases14d,
    tgt.campaignId = src.campaignId,
    tgt.click_throughs = src.click_throughs,
    tgt.countryName = src.countryName,
    tgt.creativeId = src.creativeId,
    tgt.date = src.date,
    tgt.profileId = src.profileId,
    tgt.publisher = src.publisher,
    tgt.reportDate = src.reportDate,
    tgt.totalAttributedSales14d = src.totalAttributedSales14d,
    tgt.totalUnitsSold14d = src.totalUnitsSold14d,
    tgt.unitsSold14d = src.unitsSold14d,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    accountId,
    accountName,
    adGroupId,
    advertiserName,
    attributedAddToCartClicks14d,
    attributedDetailPageViewsClicks14d,
    attributedPurchases14d,
    attributedSales14d,
    attributedTotalAddToCartClicks14d,
    attributedTotalDetailPageViewsClicks14d,
    attributedTotalPurchases14d,
    campaignId,
    click_throughs,
    countryName,
    creativeId,
    date,
    profileId,
    publisher,
    reportDate,
    totalAttributedSales14d,
    totalUnitsSold14d,
    unitsSold14d,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.accountId, 
    src.accountName, 
    src.adGroupId, 
    src.advertiserName, 
    src.attributedAddToCartClicks14d, 
    src.attributedDetailPageViewsClicks14d, 
    src.attributedPurchases14d, 
    src.attributedSales14d, 
    src.attributedTotalAddToCartClicks14d, 
    src.attributedTotalDetailPageViewsClicks14d, 
    src.attributedTotalPurchases14d, 
    src.campaignId, 
    src.click_throughs, 
    src.countryName, 
    src.creativeId, 
    src.date, 
    src.profileId, 
    src.publisher, 
    src.reportDate, 
    src.totalAttributedSales14d, 
    src.totalUnitsSold14d, 
    src.unitsSold14d, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
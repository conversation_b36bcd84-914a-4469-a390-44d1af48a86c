CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_performance_report AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(reportdate AS VARCHAR), ''), '-',
            COALESCE(CAST(accountid AS VARCHAR), ''), '-',
            COALESCE(CAST(campaignid AS VARCHAR), ''), '-',
            COALESCE(CAST(countryname AS VARCHAR), '')
            )) AS pk,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountId,
        accountName,
        adGroupId,
        advertiserName,
        attributedAddToCartClicks14d,
        attributedDetailPageViewsClicks14d,
        attributedPurchases14d,
        attributedSales14d,
        attributedTotalAddToCartClicks14d,
        attributedTotalDetailPageViewsClicks14d,
        attributedTotalPurchases14d,
        campaignId,
        click_throughs,
        countryName,
        creativeId,
        date,
        profileId,
        publisher,
        reportDate,
        totalAttributedSales14d,
        totalUnitsSold14d,
        unitsSold14d,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_performance_report
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY campaignid
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);
MERGE INTO $stage_db.STG_AMAZON_MARKET_BASKET_ANALYTICS T
USING 
(
  SELECT 
        REPORTREQUESTTIME AS REPORT_REQUEST_TIME
      , R<PERSON><PERSON><PERSON><PERSON>DATE AS REPORT_END_DATE
      , <PERSON><PERSON><PERSON><PERSON><PERSON>RTDATE AS REPORT_START_DATE
      , _DATON_BATCH_ID AS DATON_BATCH_ID
      , _DATON_BATCH_RUNTIME AS DATON_BATCH_RUNTIME
      , _DATON_USER_ID AS DATON_USER_ID
      , ASIN
      , COMBINATIONPCT AS COMBINATION_PCT
      , ENDDATE AS END_DATE
      , MARKETPLACEID AS MARKETPLACE_ID
      , MAR<PERSON><PERSON>LACE<PERSON><PERSON> AS MARKETPLACE_NAME
      , M<PERSON>UNTRY_CODE
      , PURCHASEDWITHASIN AS PURCHASED_WITH_ASIN
      , PURCHASEDWITHRANK AS PURCHASED_WITH_RANK
      , SELLINGPARTNERID AS SELLER_ID
      , STARTDATE AS START_DATE
      , FILE_NAME
      , ETL_BATCH_RUN_TIME
      , '$raw_db.RAW_AMAZON_MARKET_BASKET_ANALYTICS' AS DATA_SOURCE
      , SYSDATE() AS RECORD_CREATED_TIMESTAMP_UTC 
      , SYSDATE() AS RECORD_UPDATED_TIMESTAMP_UTC 
  FROM $raw_db.RAW_AMAZON_MARKET_BASKET_ANALYTICS MBA
  LEFT JOIN dwh.prod.AMAZON_MARKETPLACES M 
    ON MBA.MARKETPLACEID = M.MARKETPLACE_ID
  QUALIFY ROW_NUMBER() OVER (PARTITION BY ASIN, PURCHASEDWITHASIN, COUNTRY_CODE, STARTDATE, ENDDATE ORDER BY _DATON_BATCH_RUNTIME DESC) = 1
) S 
ON    T.ASIN = S.ASIN 
  AND T.PURCHASED_WITH_ASIN = S.PURCHASED_WITH_ASIN
  AND T.COUNTRY_CODE = S.COUNTRY_CODE
  AND T.START_DATE = S.START_DATE
  AND T.END_DATE = S.END_DATE
WHEN MATCHED THEN
UPDATE SET 
          T.REPORT_REQUEST_TIME = S.REPORT_REQUEST_TIME
        , T.REPORT_END_DATE = S.REPORT_END_DATE
        , T.REPORT_START_DATE = S.REPORT_START_DATE
        , T.DATON_BATCH_ID = S.DATON_BATCH_ID
        , T.DATON_BATCH_RUNTIME = S.DATON_BATCH_RUNTIME
        , T.DATON_USER_ID = S.DATON_USER_ID
        , T.COMBINATION_PCT = S.COMBINATION_PCT
        , T.MARKETPLACE_ID = S.MARKETPLACE_ID
        , T.MARKETPLACE_NAME = S.MARKETPLACE_NAME
        , T.PURCHASED_WITH_ASIN = S.PURCHASED_WITH_ASIN
        , T.PURCHASED_WITH_RANK = S.PURCHASED_WITH_RANK
        , T.SELLER_ID = S.SELLER_ID
        , T.FILE_NAME = S.FILE_NAME
        , T.ETL_BATCH_RUN_TIME = S.ETL_BATCH_RUN_TIME
        , T.UPDATED_BY = 'amazon_market_basket_analytics dag'
        , T.RECORD_UPDATED_TIMESTAMP_UTC = SYSDATE()
WHEN NOT MATCHED THEN
INSERT
(
    REPORT_REQUEST_TIME
  , REPORT_END_DATE
  , REPORT_START_DATE
  , DATON_BATCH_ID
  , DATON_BATCH_RUNTIME
  , DATON_USER_ID
  , ASIN
  , COMBINATION_PCT
  , START_DATE
  , END_DATE
  , MARKETPLACE_ID
  , MARKETPLACE_NAME
  , COUNTRY_CODE
  , PURCHASED_WITH_ASIN
  , PURCHASED_WITH_RANK
  , SELLER_ID
  , FILE_NAME
  , ETL_BATCH_RUN_TIME
  , CREATED_BY
  , DATA_SOURCE
  , RECORD_CREATED_TIMESTAMP_UTC 
  , RECORD_UPDATED_TIMESTAMP_UTC 
)
VALUES
(
    REPORT_REQUEST_TIME
  , REPORT_END_DATE
  , REPORT_START_DATE
  , DATON_BATCH_ID
  , DATON_BATCH_RUNTIME
  , DATON_USER_ID
  , ASIN
  , COMBINATION_PCT
  , START_DATE
  , END_DATE
  , MARKETPLACE_ID
  , MARKETPLACE_NAME
  , COUNTRY_CODE
  , PURCHASED_WITH_ASIN
  , PURCHASED_WITH_RANK
  , SELLER_ID
  , FILE_NAME
  , ETL_BATCH_RUN_TIME
  , 'amazon_market_basket_analytics dag'
  , DATA_SOURCE
  , SYSDATE()
  , SYSDATE()
);

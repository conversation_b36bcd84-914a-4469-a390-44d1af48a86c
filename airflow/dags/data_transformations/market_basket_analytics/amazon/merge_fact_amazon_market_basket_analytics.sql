MERGE INTO $curated_db.FACT_AMAZON_MARKET_BASKET_ANALYTICS T
USING $stage_db.STG_FACT_AMAZON_MARKET_BASKET_ANALYTICS S
    ON  T.ASIN = S.ASIN 
    AND T.PURCHASED_WITH_ASIN = S.PURCHASED_WITH_ASIN
    AND T.COUNTRY_CODE = S.COUNTRY_CODE
    AND T.START_DATE = S.START_DATE
    AND T.END_DATE = S.END_DATE
WHEN MATCHED THEN
UPDATE SET 
      T.SELLER_ID= S.SELLER_ID
    , T.PURCHASED_WITH_RANK= S.PURCHASED_WITH_RANK
    , T.COMBINATION_PCT = S.COMBINATION_PCT
    , T.DATA_SOURCE = S.DATA_SOURCE
    , T.FILE_NAME= S.FILE_NAME
    , T.UPDATED_BY = S.UPDATED_BY 
    , T.RECORD_UPDATED_TIMESTAMP_UTC = S.RECORD_UPDATED_TIMESTAMP_UTC
WHEN NOT MATCHED THEN
INSERT
(
      SELLER_ID
	, ASIN 
	, COUNTRY_CODE
	, START_DATE
	, END_DATE
	, PURCHASED_WITH_ASIN
	, PURCHASED_WITH_RANK
    , COMBINATION_PCT
	, FILE_NAME
    , DATA_SOURCE
	, CREATED_BY 
	, UPDATED_BY
    , RECORD_CREATED_TIMESTAMP_UTC 
    , RECORD_UPDATED_TIMESTAMP_UTC 
)   
VALUES
(
      SELLER_ID
	, ASIN 
	, COUNTRY_CODE
	, START_DATE
	, END_DATE
	, PURCHASED_WITH_ASIN
	, PURCHASED_WITH_RANK	
    , COMBINATION_PCT
	, FILE_NAME
    , DATA_SOURCE
	, CREATED_BY 
	, UPDATED_BY
    , RECORD_CREATED_TIMESTAMP_UTC 
    , RECORD_UPDATED_TIMESTAMP_UTC 
);

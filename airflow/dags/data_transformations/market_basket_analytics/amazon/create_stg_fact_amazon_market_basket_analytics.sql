CREATE OR <PERSON><PERSON><PERSON>CE TRANSIENT TABLE $stage_db.STG_FACT_AMAZON_MARKET_BASKET_ANALYTICS AS 
SELECT
	  SELLER_ID
	, ASIN 
	, COUNTRY_CODE
	, START_DATE
	, END_DATE
	, PURCHASED_WITH_ASIN
	, PURCHASED_WITH_RANK	
	, COMBINATION_PCT
	, '$stage_db.STG_AMAZON_MARKET_BASKET_ANALYTICS' AS DATA_SOURCE
	, FILE_NAME
	, CREATED_BY 
	, UPDATED_BY
	, SYSDATE() AS RECORD_CREATED_TIMESTAMP_UTC 
    , SYSDATE() AS RECORD_UPDATED_TIMESTAMP_UTC 
FROM $stage_db.STG_AMAZON_MARKET_BASKET_ANALYTICS 
WHERE RECORD_UPDATED_TIMESTAMP_UTC > TO_TIMESTAMP_NTZ('$start_ts')
	AND ASIN IS NOT NULL;

INSERT INTO $raw_db.RAW_AMAZON_MARKET_BASKET_ANALYTICS_LOG
(
      REPORTREQUESTTIME
    , REPORTENDDATE
    , R<PERSON><PERSON><PERSON>TARTDA<PERSON>
    , _DATON_BATCH_ID
    , _DATON_BATCH_RUNTIME
    , _<PERSON><PERSON>ON_USER_ID
    , <PERSON>IN
    , CO<PERSON><PERSON>AT<PERSON><PERSON><PERSON>
    , ENDDATE
    , MARKETPLACEID
    , MARKETPLACENAME
    , <PERSON><PERSON><PERSON><PERSON>ED<PERSON>THASIN
    , PURCH<PERSON>EDWITHRANK
    , SELLING<PERSON>RTNERID
    , STARTDATE
    , FILE_NAME
    , ETL_BATCH_RUN_TIME
    , LOG_TIMESTAMP_UTC 
)
SELECT 
      REPORTREQUESTTIME
    , REPORTENDDATE
    , REPORTSTARTDA<PERSON>
    , _<PERSON><PERSON>ON_BATCH_ID
    , _DATON_BATCH_RUNTIME
    , _DATON_USER_ID
    , ASIN
    , COMBINATIONPCT
    , ENDDATE
    , MARKETPLACEID
    , MARKETPLACENAME
    , <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>THASIN
    , PURCHASEDWITHRANK
    , SELLINGPARTNERID
    , STARTDATE
    , FILE_NAME
    , ETL_BATCH_RUN_TIME
    , SYSDATE() AS LOG_TIMESTAMP_UTC 
FROM $raw_db.RAW_AMAZON_MARKET_BASKET_ANALYTICS;

DELETE FROM $raw_db.RAW_AMAZON_MARKET_BASKET_ANALYTICS_LOG WHERE DATE(LOG_TIMESTAMP_UTC) < CURRENT_DATE - 30;

ALTER SESSION SET WEEK_START = 7;
SET DATE_RANGE = 90;
CREATE TABLE IF NOT EXISTS $curated_db.ORDERS_PROMOTION_CLASSIFICATION (
	BRAND_CODE VARCHAR(50),
	SELLER_NAME VARCHAR(16777216),
	SELLER_ID VARCHAR(16777216),
	ASIN VARCHAR(16777216),
	COUNTRY_CODE VARCHAR(16777216),
	PURCHASED_DATE_UTC DATE,
	PURCHASED_AT_UTC TIMESTAMP_NTZ(9),
	AMAZON_ORDER_ID VARCHAR(16777216),
	SKU VARCHAR(16777216),
	CAMPAIGN_TYPE VARCHAR(16777216),
	CAMPAIGN_NAME VARCHAR(16777216),
	CAMPAIGN_ID VARCHAR(16777216),
	PROMO_TYPE VARCHAR(16777216),
	AMAZON_EVENT VARCHAR(16777216),
	PROMO_VALUE FLOAT,
	QUANTITY FLOAT,
	ITEM_PRICE FLOAT,
	ITEM_PROMOTION_DISCOUNT FLOAT,
	UNIQUE_KEY VARCHAR(32),
	PURCHASED_DATE_LOCAL DATE
);

MERGE INTO
    $curated_db.orders_promotion_classification AS tgt
USING (
    WITH max AS (
        SELECT MAX(PURCHASED_DATE_UTC) AS last_date FROM $curated_db.orders_promotion_classification
    )
    , stg AS (
        SELECT DISTINCT
            *,
            ROUND(SUM(IFF(campaign_type NOT IN ('7DD', 'PED', 'DOTD', 'OD'), promo_value, NULL)) OVER (PARTITION BY amazon_order_id, sku), 2) AS calculated_promo,
        FROM $stage_db.stg_orders_promotion_classification
        WHERE purchased_date_utc >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
    ),

    clean_unaccounted_coupons AS (
        SELECT * EXCLUDE(calculated_promo),
        FROM (
            SELECT *, FROM stg
            WHERE
                campaign_type NOT IN ('7DD', 'PED', 'DOTD', 'OD')
            QUALIFY
                ROW_NUMBER() OVER (
                    PARTITION BY amazon_order_id, sku, CAST(promo_value AS STRING), quantity, CAST(item_price AS STRING), CAST(item_promotion_discount AS STRING)
                    ORDER BY campaign_type DESC
                ) = 1
        )
    ),

    marketplaces AS (
        SELECT DISTINCT 
            marketplace AS marketplace_name,
            country_code,
        FROM DWH.PROD.AMAZON_MARKETPLACES
    )

    , brands AS (
        SELECT DISTINCT 
            MIN("brand_code") OVER (PARTITION BY "asin") AS brand_code, 
            "asin" AS asin
        FROM dwh.prod.fact_amazon_orders
    )

    , final_union AS (
        SELECT * EXCLUDE(calculated_promo), FROM stg
        WHERE
            campaign_type IN ('7DD', 'PED', 'DOTD', 'OD')
        UNION ALL
        SELECT *, FROM clean_unaccounted_coupons
    )
    , sellers AS (
        SELECT DISTINCT 
            "seller_id" AS seller_id, 
            MAX("seller_name") OVER (PARTITION BY "seller_id") AS seller_name,
        FROM DWH.PROD.FACT_AMAZON_INVENTORY
    )

    SELECT DISTINCT
        COALESCE(final_union.brand_code, brands.brand_code) AS brand_code,
        sellers.seller_name,
        final_union.* EXCLUDE(brand_code),
        MD5(CAST(
            COALESCE(CAST(brand_code as STRING), '') || '-' || 
            COALESCE(CAST(amazon_order_id as STRING), '') || '-' || 
            COALESCE(CAST(sku as STRING), '') || '-' || 
            COALESCE(CAST(final_union.asin as STRING), '') || '-' || 
            COALESCE(CAST(campaign_type as STRING), '') || '-' || 
            COALESCE(CAST(campaign_name as STRING), '') || '-' || 
            COALESCE(CAST(promo_value as STRING), '') || '-' || 
            COALESCE(CAST(campaign_id as STRING), '') as STRING)) AS unique_key,
    CASE
        WHEN final_union.country_code = 'US'
            THEN DATE(CONVERT_TIMEZONE('UTC', 'America/Los_Angeles', final_union.purchased_at_utc))
        WHEN final_union.country_code = 'CA'
            THEN DATE(CONVERT_TIMEZONE('UTC',  'Canada/Pacific', final_union.purchased_at_utc))
        WHEN final_union.country_code = 'FR'
            THEN DATE(CONVERT_TIMEZONE('UTC', 'Europe/Paris', final_union.purchased_at_utc))
        WHEN final_union.country_code = 'IT'
            THEN DATE(CONVERT_TIMEZONE('UTC', 'Europe/Rome', final_union.purchased_at_utc))
        WHEN final_union.country_code = 'ES'
            THEN DATE(CONVERT_TIMEZONE('UTC', 'Europe/Madrid', final_union.purchased_at_utc))
        WHEN final_union.country_code = 'UK'
            THEN DATE(CONVERT_TIMEZONE('UTC', 'Europe/London', final_union.purchased_at_utc))
        WHEN final_union.country_code = 'DE'
            THEN DATE(CONVERT_TIMEZONE('UTC', 'Europe/Copenhagen', final_union.purchased_at_utc))
        WHEN final_union.country_code = 'NL'
            THEN DATE(CONVERT_TIMEZONE('UTC', 'Europe/Amsterdam', final_union.purchased_at_utc))
        WHEN final_union.country_code = 'PL'
            THEN DATE(CONVERT_TIMEZONE('UTC', 'Europe/Warsaw', final_union.purchased_at_utc))
        WHEN final_union.country_code = 'SE'
            THEN DATE(CONVERT_TIMEZONE('UTC', 'Europe/Stockholm', final_union.purchased_at_utc))
        WHEN final_union.country_code = 'MX'
            THEN DATE(CONVERT_TIMEZONE('UTC', 'Mexico/BajaNorte', final_union.purchased_at_utc))
        ELSE DATE(final_union.purchased_at_utc)
    END
    AS purchased_date_local,
    FROM final_union
    LEFT JOIN brands USING (asin)
    LEFT JOIN sellers USING (seller_id)
    WHERE NOT REGEXP_LIKE(campaign_name, '.*(shipping|subscribe|paws|same|vine).*', 'i')
    QUALIFY
        ROW_NUMBER() OVER (
            PARTITION BY unique_key
            ORDER BY purchased_date_local DESC
        ) = 1
) AS src    
        ON src.unique_key = tgt.unique_key
WHEN MATCHED THEN
UPDATE SET
    tgt.brand_code = src.brand_code,
    tgt.seller_name = src.seller_name,
    tgt.seller_id = src.seller_id,
    tgt.asin = src.asin,
    tgt.country_code = src.country_code,
    tgt.purchased_date_utc = src.purchased_date_utc,
    tgt.purchased_at_utc = src.purchased_at_utc,
    tgt.amazon_order_id = src.amazon_order_id,
    tgt.sku = src.sku,
    tgt.campaign_type = src.campaign_type,
    tgt.campaign_name = src.campaign_name,
    tgt.campaign_id = src.campaign_id,
    tgt.promo_type = src.promo_type,
    tgt.amazon_event = src.amazon_event,
    tgt.promo_value = src.promo_value,
    tgt.quantity = src.quantity,
    tgt.item_price = src.item_price,
    tgt.item_promotion_discount = src.item_promotion_discount,
    tgt.unique_key = src.unique_key,
    tgt.purchased_date_local = src.purchased_date_local
WHEN NOT MATCHED THEN
INSERT (
    brand_code,
    seller_name,
    seller_id,
    asin,
    country_code,
    purchased_date_utc,
    purchased_at_utc,
    amazon_order_id,
    sku,
    campaign_type,
    campaign_name,
    campaign_id,
    promo_type,
    amazon_event,
    promo_value,
    quantity,
    item_price,
    item_promotion_discount,
    unique_key,
    purchased_date_local
)
VALUES
(
    src.brand_code,
    src.seller_name,
    src.seller_id,
    src.asin,
    src.country_code,
    src.purchased_date_utc,
    src.purchased_at_utc,
    src.amazon_order_id,
    src.sku,
    src.campaign_type,
    src.campaign_name,
    src.campaign_id,
    src.promo_type,
    src.amazon_event,
    src.promo_value,
    src.quantity,
    src.item_price,
    src.item_promotion_discount,
    src.unique_key,
    src.purchased_date_local
);

ALTER SESSION SET WEEK_START = 1;
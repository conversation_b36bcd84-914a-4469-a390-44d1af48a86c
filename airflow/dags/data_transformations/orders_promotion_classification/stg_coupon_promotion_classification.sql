ALTER SESSION SET WEEK_START = 7;
SET DATE_RANGE = 90;

CREATE TABLE IF NOT EXISTS $stage_db.stg_coupon_promotion_classification (
	AMAZON_ORDER_ID VARCHAR(16777216),
	SKU VARCHAR(16777216),
	COUNTRY_CODE VARCHAR(16777216),
	BRAND_CODE VARCHAR(50),
	SELLER_ID VARCHAR(16777216),
	PURCHASED_AT_UTC TIMESTAMP_NTZ(9),
	PURCHASED_DATE_UTC DATE,
	PURCHASED_DATE_PST DATE,
	ASIN VARCHAR(16777216),
	QUANTITY FLOAT,
	DEAL_PRICE FLOAT,
	ITEM_PROMOTION_DISCOUNT FLOAT,
	ITEM_PRICE FLOAT,
	EXTERNAL_ID VARCHAR(16777216),
	PROMO_VALUE FLOAT,
	CAMPAIGN_NAME VARCHAR(16777216),
	PROMO_TYPE VARCHAR(16777216),
	AMAZON_EVENT VARCHAR(1),
	<PERSON><PERSON><PERSON><PERSON><PERSON>_ID VARCHAR(16777216),
	CAMPAIG<PERSON>_TYPE VARCHAR(6)
);

MERGE INTO
    $stage_db.stg_coupon_promotion_classification AS tgt
USING (

    WITH country_codes AS (
        SELECT DISTINCT LOWER(sales_channel) AS sales_channel, country_code
        FROM DWH.PROD.AMAZON_MARKETPLACES
    )
    , max AS (
        SELECT MAX(PURCHASED_DATE_UTC) AS last_date FROM $stage_db.stg_coupon_promotion_classification
    )
    ,raw_settle AS (
        SELECT
            DATE(posted_date_time) AS posted_date,
            s.brand_code,
            s.sales_channel,
            order_id,
            country_code,
            sku,
            promotion_id,
            REGEXP_SUBSTR(promotion_id, '([a-z0-9]{8})-[a-z0-9]{4}',1,1,'e',1) AS external_id,
            quantity_purchased AS quantity,
            amount,
            COUNT(
                DISTINCT
                CASE LEFT(promotion_id, 4)
                    WHEN 'VPC-' THEN 1
                    WHEN 'Coup' THEN 1
                    WHEN 'DCMS' THEN 2
                    WHEN 'PLM-' THEN 3
                    ELSE 4
                END
            ) OVER (PARTITION BY order_id, country_code, sku) AS total_promo_type,
            ROUND(SUM(amount) OVER (PARTITION BY order_id, country_code, sku), 2) AS total_promo_amount,
            -- As promotion value is negative amount, min(amount) is max discount.
            DENSE_RANK() OVER (
                PARTITION BY order_id, country_code, sku, s.quantity_purchased, promotion_id
                ORDER BY amount
            ) AS ranking_max,
            DENSE_RANK() OVER (
                PARTITION BY order_id, country_code, sku, quantity_purchased, promotion_id
                ORDER BY amount DESC
            ) AS ranking_min,        
        FROM DWH.STAGING.merge_stage_amazon_settlements AS s
        WHERE true
            AND POSTED_DATE_TIME >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - (30 + $DATE_RANGE))
            AND amount != 0
            AND LOWER(transaction_type) = 'order'
            AND LOWER(amount_description) != 'shipping'
            AND NOT REGEXP_LIKE(promotion_id, '.*(Subscribe|PAWS|MPC|Shipping|Duplicated|vine).*', 'i')
    )
    , granular_settle_max_promo AS (
        SELECT
            * EXCLUDE(quantity, amount),
            SUM(quantity) AS quantity,
            SUM(amount) AS amount,
        FROM (
            SELECT
                order_id,
                country_code,
                sku,
                promotion_id,
                quantity AS actual_quantity,
                amount AS actual_amount,
                total_promo_amount,
                ROUND(SUM(amount) OVER (PARTITION BY order_id, country_code, sku, quantity), 2) AS total_unique_amount_per_qty,
                ROUND(SUM(amount) OVER (PARTITION BY order_id, country_code, sku), 2) AS total_unique_promo_amount,
                quantity,
                amount,
            FROM raw_settle
            WHERE ranking_max = 1
        )
        GROUP BY ALL
    )
    , granular_settle_min_promo AS (
        SELECT
            * EXCLUDE(quantity, amount),
            SUM(quantity) AS quantity,
            SUM(amount) AS amount,
        FROM (
            SELECT
                order_id,
                country_code,
                sku,
                promotion_id,
                quantity AS actual_quantity,
                amount AS actual_amount,
                total_promo_amount,
                ROUND(SUM(amount) OVER (PARTITION BY order_id, country_code, sku, quantity), 2) AS total_unique_amount_per_qty,
                ROUND(SUM(amount) OVER (PARTITION BY order_id, country_code, sku), 2) AS total_unique_promo_amount,
                quantity,
                amount,
            FROM raw_settle
            WHERE ranking_min = 1
        )
        GROUP BY ALL
    )
    , settle_sum AS (
        SELECT
            order_id,
            country_code,
            sku,
            promotion_id,
            total_promo_type,
            total_promo_amount,
            SUM(quantity) AS quantity,
            ROUND(SUM(amount), 2) AS amount,
        FROM raw_settle
        GROUP BY ALL
    )
    ,orders AS (
        SELECT DISTINCT
            country_codes.country_code,
            "brand_code" AS brand_code,
            "derived_seller_id" AS seller_id,
            "purchase_date" AS purchased_at_utc,
            DATE("purchase_date") AS purchased_date_utc,
            DATE(CONVERT_TIMEZONE('UTC', 'America/Los_Angeles',"purchase_date")) AS purchased_date_pst,
            o."amazon_order_id" AS amazon_order_id,
            "asin" AS asin,
            "sku" AS sku,
            "quantity" AS quantity,
            DIV0("item_price", "quantity") AS deal_price,
            "item_promotion_discount" AS item_promotion_discount,
            "item_price" AS item_price,
            promotions.value::TEXT AS promotion_id
        FROM dwh.prod.fact_amazon_orders AS o
        LEFT JOIN country_codes
            ON o."sales_channel" = country_codes.sales_channel
        ,LATERAL FLATTEN(INPUT => SPLIT("promotion_ids", ',')) AS promotions
        WHERE "purchase_date" >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
            AND item_promotion_discount > 0
            AND promotion_id IS NOT NULL
    )
    , promos_raw AS (
        SELECT DISTINCT
            AMAZON_ORDER_ID,
            shipment_item_id,
            shipment_id,
            promotion_rule_value,
            item_promotion_discount,
            item_promotion_id,
            description,
        FROM DWH.STAGING.MERGE_PROMOTIONS_REPORT AS l
    )
    , promos AS (
        SELECT DISTINCT
            "shipment_id",
            COALESCE(s."order_id", orders.amazon_order_id) AS amazon_order_id,
            COALESCE(s."asin", orders.asin) AS asin,
            COALESCE(s."sku", orders.sku) AS sku,
            COALESCE(
                REGEXP_SUBSTR(description, '-([0-9a-z]{8}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{12})',1,1,'e',1), 
                REGEXP_SUBSTR(promotion_id, '-([0-9a-z]{8}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{12})',1,1,'e',1)
            ) AS coupon_campaign_id,
            description,
            promotion_id,
            promotion_rule_value,
            orders.item_promotion_discount,
            item_promotion_id,
        FROM DWH.PROD.FACT_ALL_SHIPMENTS AS s
        INNER JOIN promos_raw
            ON s."order_id" = promos_raw.amazon_order_id
            AND s."shipment_id" = promos_raw.shipment_id
            AND s."shipment_item_id" = promos_raw.shipment_item_id
        FULL OUTER JOIN orders
            ON s."order_id" = orders.amazon_order_id
            AND s."asin" = orders.asin
        WHERE
            s."purchase_date_utc" >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
            AND NOT REGEXP_LIKE(promos_raw.description, '.*(Shipping|S & S|Subscribe and Save|SameDay).*', 'i')
    )
    , promos_sum AS (
        SELECT 
            description,
            p.amazon_order_id,
            coupon_campaign_id,
            promotion_rule_value,
            p.sku,
            p.asin,
            p.item_promotion_discount,
        FROM (
            SELECT
                description,
                coupon_campaign_id,
                amazon_order_id,
                promotion_rule_value,
                sku,
                asin,
                SUM(item_promotion_discount) AS item_promotion_discount,
            FROM promos
            GROUP BY ALL
        ) AS p
        LEFT OUTER JOIN orders
            ON
                p.amazon_order_id = orders.amazon_order_id
                AND p.item_promotion_discount = orders.item_promotion_discount
    )
    , tailored_promo AS (
        SELECT DISTINCT 
            tracking_id, 
            id, 
            TRIM(description) AS title
        FROM DWH.RAW.log_spes_tailored_promotions
        QUALIFY ROW_NUMBER() OVER (PARTITION BY tracking_id ORDER BY etl_batch_run_time) = 1
    )
    , coupons_settlement AS (
        SELECT DISTINCT
            COALESCE(orders.amazon_order_id, settle.order_id, granular_settle.order_id) AS amazon_order_id, 
            COALESCE(orders.sku, settle.sku, granular_settle.sku) AS sku, 
            orders.* EXCLUDE(promotion_id, amazon_order_id, sku),
            COALESCE(settle.promotion_id, granular_settle.promotion_id, settle_max_promo.promotion_id, settle_min_promo.promotion_id, orders.promotion_id) AS promotion_id,
            granular_settle.external_id,
            COALESCE(
                settle.amount, granular_settle.amount,
                -- case: duplicate data.
                IFF(
                    -orders.item_promotion_discount IN (settle_max_promo.total_unique_promo_amount, settle_max_promo.total_unique_amount_per_qty),
                    settle_max_promo.actual_amount,
                    settle_max_promo.amount
                ),
                IFF(
                    -orders.item_promotion_discount IN (settle_min_promo.total_unique_amount_per_qty, settle_min_promo.total_unique_promo_amount),
                    settle_min_promo.actual_amount,
                    settle_min_promo.amount
                )
            ) AS promo_value,
        FROM orders
        -- Case 2: Map by each individial line in settle or sum all amount by sku.
        FULL OUTER JOIN raw_settle AS granular_settle
            ON
                orders.sku = granular_settle.sku
                AND orders.amazon_order_id = granular_settle.order_id
        -- Case 1: Map by total of amount by sku and promotion.
        LEFT OUTER JOIN settle_sum AS settle
            ON
                granular_settle.sku = settle.sku
                AND granular_settle.order_id = settle.order_id
        -- Case 3: settle has several lines with duplicating order id, SKU, promotion id and different/the same amount.
        -- Pick the maximum ABS(amount) per sku, promotion and then sum them by sku for mapping.
        LEFT OUTER JOIN granular_settle_max_promo AS settle_max_promo
            ON
                granular_settle.sku = settle_max_promo.sku
                AND granular_settle.order_id = settle_max_promo.order_id
        -- Case 4: The same case 3, but pick the minium ABS(amount) per sku, promotion and then sum them by sku for mapping.
        LEFT OUTER JOIN granular_settle_min_promo AS settle_min_promo
            ON
                granular_settle.sku = settle_min_promo.sku
                AND granular_settle.order_id = settle_min_promo.order_id
        WHERE 
            orders.item_promotion_discount != 0
    )
    ,coupons AS (
        SELECT
            cp.* REPLACE (
                ABS(COALESCE(cp.promo_value, cp.item_promotion_discount)) AS promo_value
                ,IFF(cp.external_id IS NULL, REGEXP_SUBSTR(cp.promotion_id, 'DCMS-([a-z0-9]{8})-', 1, 1, 'e', 1), cp.external_id) AS external_id
            ),
            CASE
                WHEN CONTAINS(cp.promotion_id, 'PLM') THEN CONCAT('Coupons-SPC', REPLACE(cp.promotion_id, 'PLM', ''))
                WHEN promos.coupon_campaign_id = REGEXP_SUBSTR(cp.promotion_id, '(VPC-[\S]+)',1,1,'e',1)
                    THEN COALESCE(promos.description, cp.promotion_id)
                ELSE COALESCE(cp.promotion_id, promos.description)
            END AS campaign_name,
            CASE
                WHEN CONTAINS(cp.promotion_id, 'PLM') OR CONTAINS(cp.promotion_id, 'Coupons-') THEN 'STANDARD'
                WHEN promos.promotion_rule_value = '59' AND NOT (CONTAINS(cp.promotion_id, 'DCMS')) THEN 'BUNDLE'
            END AS promo_type,
            '' AS amazon_event,
            ROUND(SUM(ABS(COALESCE(cp.promo_value, promos.item_promotion_discount, cp.item_promotion_discount))) OVER (
                PARTITION BY cp.amazon_order_id, cp.sku
            ), 2) AS total_promo_amount,
        FROM coupons_settlement AS cp
        LEFT OUTER JOIN promos_sum AS promos
            ON
                cp.amazon_order_id = promos.amazon_order_id
                AND cp.sku = promos.sku
        -- For case, there is no any matching rows from settlement, and then getting default from orders, we need to exclude some promotion types
    )
    , coupons_scraped AS (
        SELECT DISTINCT
            coupon_id,
            country_code,
            coupon_title AS title,
            DATE(start_date) AS start_at,
            DATE(end_date) AS end_at,
        FROM DWH.PROD.FACT_AMAZON_COUPONS AS c
        QUALIFY ROW_NUMBER() OVER (PARTITION BY country_code, coupon_id ORDER BY record_updated_timestamp_utc) = 1
    )
    ,coupon_partial_campaign_names AS (
        SELECT DISTINCT
            description AS campaign_name,
            TRIM(REGEXP_SUBSTR(TRIM(description, 'Coupons-'), '(^[\s\S]+) [\w-]+?-[0-9a-z]{8}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{12}',1,1,'e',1), '...') AS campaign_prefix,
            REGEXP_SUBSTR(description, '-([0-9a-z]{8}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{12})',1,1,'e',1) AS campaign_id,
        FROM promos
        WHERE REGEXP_LIKE(description, '.*-([0-9a-z]{8}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{12}).*')
            OR CONTAINS(description, '...')
    )
    -- Enrich coupon campaign name
    , coupons_enriching_name AS (
        SELECT DISTINCT
            coupons.* REPLACE (
                IFF(
                    tailored.tracking_id IS NOT NULL,
                    tailored.title,
                    COALESCE(scraped.title, scraped2.title, campaigns.campaign_name, coupons.campaign_name)
                ) AS campaign_name,
                IFF(tailored.tracking_id IS NOT NULL, NULL, promo_type) AS promo_type
            ),
            CASE
                WHEN tailored.tracking_id IS NOT NULL THEN tailored.tracking_id
                WHEN REGEXP_LIKE(coupons.promotion_id, 'DCMS.*') THEN coupons.external_id
                WHEN REGEXP_LIKE(coupons.promotion_id, 'Coupons.*|PLM.*') THEN coupons.promotion_id
                WHEN scraped2.title IS NOT NULL THEN scraped2.coupon_id
                ELSE COALESCE(REGEXP_SUBSTR(coupons.campaign_name, '([0-9a-z]{8}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{12})',1,1,'e',1)
                    ,REGEXP_SUBSTR(coupons.promotion_id, '([0-9a-z]{8}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{12})',1,1,'e',1))
            END AS campaign_id,
            CASE
                WHEN tailored.tracking_id IS NOT NULL THEN 'TP'
                WHEN CONTAINS(coupons.promotion_id, 'DCMS') THEN 'LD'
                ELSE 'COUPON'
            END AS campaign_type,
        FROM coupons
        LEFT OUTER JOIN coupon_partial_campaign_names AS campaigns
            ON ENDSWITH(coupons.campaign_name, campaigns.campaign_id)
        LEFT OUTER JOIN coupons_scraped AS scraped
            ON
                coupons.country_code = scraped.country_code
                AND REGEXP_SUBSTR(coupons.promotion_id, '([0-9a-z]{8}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{4}-[0-9a-z]{12})',1,1,'e',1) = scraped.coupon_id
        LEFT OUTER JOIN coupons_scraped AS scraped2
            ON
                coupons.country_code = scraped2.country_code
                AND coupons.campaign_name = scraped2.title
        -- tailored promo
        LEFT OUTER JOIN tailored_promo AS tailored
            ON coupons.promotion_id = tailored.tracking_id
                OR coupons.promotion_id = tailored.id
        -- remove duplicate because a partial campaign name might have multiple coupons_scraped.titles
        QUALIFY ROW_NUMBER() OVER (
            PARTITION BY coupons.amazon_order_id, coupons.asin, coupons.sku, coupons.quantity, CAST(promo_value AS STRING), campaign_id, LEFT(coupons.campaign_name, 14)
        ORDER BY coupons.asin) = 1
    )
    , coupons_proxy AS (
        -- use complete matched orders to fill the blanks on missing ones
        SELECT DISTINCT
            a.* REPLACE (
                COALESCE(a.external_id, b.external_id) AS external_id,
                b.campaign_name AS campaign_name,
                b.campaign_id AS campaign_id,
                b.campaign_type AS campaign_type,
                b.promo_type AS promo_type
            ),
            ABS(DATEDIFF('DAY', a.purchased_date_utc, b.purchased_date_utc)) AS near,
        FROM coupons_enriching_name AS a
        LEFT OUTER JOIN (
            SELECT DISTINCT
                sku,
                deal_price,
                item_promotion_discount,
                purchased_date_utc,
                campaign_name,
                campaign_id,
                campaign_type,
                promo_type,
                external_id,
            FROM coupons_enriching_name
            WHERE COALESCE(campaign_name, '') != ''
        ) AS b
            ON
                a.sku = b.sku
                AND a.deal_price = b.deal_price
                AND a.item_promotion_discount = b.item_promotion_discount
                AND a.purchased_date_utc BETWEEN b.purchased_date_utc - 2 AND b.purchased_date_utc + 2
        WHERE COALESCE(a.campaign_name, '') = ''
        QUALIFY ROW_NUMBER() OVER (PARTITION BY a.amazon_order_id, a.sku ORDER BY near NULLS LAST) = 1
    )
    ,union_coupon AS (
        SELECT *,
        FROM coupons_enriching_name
        WHERE
            COALESCE(campaign_name, '') != ''
        UNION ALL
        SELECT * EXCLUDE(near), FROM coupons_proxy
    )
    ,final AS (
        SELECT
            * EXCLUDE(total_promo_amount, promotion_id) REPLACE (
                IFF(
                    CONTAINS(campaign_name, ' VPC-'),
                    TRIM(REGEXP_SUBSTR(campaign_name, '(^[\s\S]+) VPC-',1,1,'e',1), '...'),
                    campaign_name
                ) AS campaign_name,
                CASE
                    WHEN CONTAINS(campaign_name, '%') OR CONTAINS(campaign_name, 'percent') THEN 'PERCENT_OFF'
                    WHEN REGEXP_LIKE(campaign_name, '.*(\$|£|€).*') THEN 'VALUE_OFF'
                    WHEN REGEXP_LIKE(campaign_name, '*.(buy.*get)*.', 'i') THEN 'BUNDLE'
                    ELSE promo_type
                END AS promo_type
            ),
        FROM union_coupon
        WHERE
            external_id IS NOT NULL
            OR COALESCE(campaign_name, '') != ''
        QUALIFY ROW_NUMBER() OVER (PARTITION BY amazon_order_id, sku, campaign_name, CAST(promo_value AS STRING) ORDER BY sku) = 1
    )
    SELECT * FROM final

) AS src    
    ON src.amazon_order_id = tgt.amazon_order_id
        AND src.asin = tgt.asin
        AND src.sku = tgt.sku
        AND src.campaign_id = tgt.campaign_id
        AND src.campaign_name = tgt.campaign_name
WHEN MATCHED THEN
UPDATE SET
    tgt.amazon_order_id = src.amazon_order_id,
    tgt.sku = src.sku,
    tgt.country_code = src.country_code,
    tgt.brand_code = src.brand_code,
    tgt.seller_id = src.seller_id,
    tgt.purchased_at_utc = src.purchased_at_utc,
    tgt.purchased_date_utc = src.purchased_date_utc,
    tgt.purchased_date_pst = src.purchased_date_pst,
    tgt.asin = src.asin,
    tgt.quantity = src.quantity,
    tgt.deal_price = src.deal_price,
    tgt.item_promotion_discount = src.item_promotion_discount,
    tgt.item_price = src.item_price,
    tgt.external_id = src.external_id,
    tgt.promo_value = src.promo_value,
    tgt.campaign_name = src.campaign_name,
    tgt.promo_type = src.promo_type,
    tgt.amazon_event = src.amazon_event,
    tgt.campaign_id = src.campaign_id,
    tgt.campaign_type = src.campaign_type
WHEN NOT MATCHED THEN
INSERT (
    amazon_order_id,
    sku,
    country_code,
    brand_code,
    seller_id,
    purchased_at_utc,
    purchased_date_utc,
    purchased_date_pst,
    asin,
    quantity,
    deal_price,
    item_promotion_discount,
    item_price,
    external_id,
    promo_value,
    campaign_name,
    promo_type,
    amazon_event,
    campaign_id,
    campaign_type
)
VALUES
(
    src.amazon_order_id,
    src.sku,
    src.country_code,
    src.brand_code,
    src.seller_id,
    src.purchased_at_utc,
    src.purchased_date_utc,
    src.purchased_date_pst,
    src.asin,
    src.quantity,
    src.deal_price,
    src.item_promotion_discount,
    src.item_price,
    src.external_id,
    src.promo_value,
    src.campaign_name,
    src.promo_type,
    src.amazon_event,
    src.campaign_id,
    src.campaign_type
);

ALTER SESSION SET WEEK_START = 1;

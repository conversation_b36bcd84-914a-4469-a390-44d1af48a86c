ALTER SESSION SET WEEK_START = 7;
SET DATE_RANGE = 90;
CREATE TABLE IF NOT EXISTS $stage_db.STG_ORDERS_PROMOTION_CLASSIFICATION (
	COUNTRY_CODE VARCHAR(16777216),
	<PERSON><PERSON><PERSON>_CODE VARCHAR(50),
	SELLER_ID VARCHAR(16777216),
	PURCHASED_DATE_UTC DATE,
	PURCHASED_AT_UTC TIMESTAMP_NTZ(9),
	AMAZON_ORDER_ID VARCHAR(16777216),
	ASIN VARCHAR(16777216),
	SKU VARCHAR(16777216),
	CAMPAIGN_TYPE VARCHAR(16777216),
	CAMPAIGN_NAME VARCHAR(16777216),
	CAMPAIGN_ID VARCHAR(16777216),
	PROMO_TYPE VARCHAR(16777216),
	AMAZON_EVENT VARCHAR(16777216),
	PROMO_VALUE FLOAT,
	QUANTITY FLOAT,
	ITEM_PRICE FLOAT,
	ITEM_PROMOTION_DISCOUNT FLOAT
);
MERGE INTO
    $stage_db.stg_orders_promotion_classification AS tgt
USING (

    WITH country_codes AS (
        SELECT DISTINCT LOWER(sales_channel) AS sales_channel, country_code
        FROM DWH.PROD.AMAZON_MARKETPLACES
    )
    , max AS (
        SELECT MAX(PURCHASED_DATE_UTC) AS last_date FROM $stage_db.stg_orders_promotion_classification
    )
    , dcms_settle AS (
        SELECT DISTINCT
            order_id,
            country_code,
            sku,
            promotion_id,
            REGEXP_SUBSTR(promotion_id, 'DCMS-([a-z0-9]{8})-',1,1,'e',1) AS external_id,
            SUM(quantity_purchased) AS quantity,
            ROUND(SUM(amount), 2) AS amount,
        FROM DWH.STAGING.merge_stage_amazon_settlements AS s
        WHERE POSTED_DATE_TIME >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - (30 + $DATE_RANGE))
            AND amount != 0
            AND LOWER(transaction_type) = 'order'
            AND LOWER(amount_description) != 'shipping'
            AND REGEXP_LIKE(promotion_id, '.*(DCMS).*', 'i')
        GROUP BY ALL
    )
    , orders AS (
        SELECT
            country_codes.country_code,
            "brand_code" AS brand_code,
            "derived_seller_id" AS seller_id,
            DATE("purchase_date") AS purchased_date_utc,
            DATE(CONVERT_TIMEZONE('UTC', 'America/Los_Angeles',"purchase_date")) AS purchased_date_pst,
            CONVERT_TIMEZONE('UTC', 'America/Los_Angeles',"purchase_date") AS purchased_at_pst,
            "purchase_date" AS purchased_at_utc,
            "amazon_order_id" AS amazon_order_id,
            "asin" AS asin,
            "sku" AS sku,
            "quantity" AS quantity,
            DIV0("item_price", "quantity") AS deal_price,
            "item_promotion_discount" AS item_promotion_discount,
            "item_price" AS item_price,
            promotion_id.value::TEXT AS promotion_id,
        FROM dwh.prod.fact_amazon_orders AS o
        LEFT JOIN country_codes
            ON o."sales_channel" = country_codes.sales_channel
        ,LATERAL FLATTEN(INPUT => SPLIT("promotion_ids", ',')) AS promotion_id
        WHERE "purchase_date" >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
--        AND COALESCE("promotion_ids", '') != ''
--        AND "item_price" > 0
--        AND NOT (REGEXP_LIKE(promotion_id, '.*(Subscribe|PAWS|MPC|Shipping|Duplicated).*', 'i'))
    )
    , tailored_promo AS (
        SELECT DISTINCT
            tracking_id,
            start_timestamp,
            end_timestamp,
            description
        -- FROM DWH.STAGING.merge_spes_tailored_promotions
        FROM DWH.RAW.log_spes_tailored_promotions
        WHERE (
            DATE(start_timestamp) >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
            OR DATE(end_timestamp) >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
        )
        QUALIFY ROW_NUMBER() OVER (
            PARTITION BY start_timestamp, end_timestamp, brand_id, country, percent_off, tracking_id
            ORDER BY etl_batch_run_time DESC
            -- ORDER BY record_updated_timestamp_utc DESC
        ) = 1
    )
    , deals AS (
        SELECT DISTINCT
            start_time,
            end_time,
            country_code,
            d.asin,
            d.sku,
            internal_description,
            campaign_id,
            COALESCE(d.external_id, bq.external_id) AS external_id,
            event_name,
            deal_type,
            deal_title,
            deal_price,
        FROM DWH.PROD.FACT_AMAZON_DEALS AS d
        LEFT JOIN SANDBOX.TEST_POC.BQ_DEALS_EXTERNAL_IDS AS bq -- complete with BQ legacy data
        ON d.campaign_id = bq.id
        WHERE d.start_time >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
            OR d.end_time >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
    )
    , coupons AS (
        SELECT *,
        FROM $stage_db.stg_coupon_promotion_classification
        WHERE purchased_date_utc >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
    )
    , tp AS (
        SELECT DISTINCT
            orders.*,
            tailored.description AS campaign_name,
            tailored.tracking_id AS campaign_id,
            'PERCENT_OFF' AS promo_type,
            tp.promo_value,
        FROM tailored_promo AS tailored
        INNER JOIN coupons AS tp
            ON
                tp.campaign_type = 'TP'
                AND tailored.tracking_id = tp.campaign_name
        INNER JOIN orders
            ON
                tp.amazon_order_id = orders.amazon_order_id
                AND tp.sku = orders.sku
                AND orders.purchased_date_utc BETWEEN tailored.start_timestamp AND tailored.end_timestamp
    )
    , orders_settle_ld AS (
        SELECT
            orders.* EXCLUDE(promotion_id),
            ABS(COALESCE(ld.promo_value, DIV0(settle.amount, settle.quantity))) AS promo_value,
            COALESCE(ld.external_id, settle.external_id) AS external_id,
            IFF(ld.sku IS NOT NULL, TRUE, FALSE) AS match_promo_value,
        FROM orders
        LEFT OUTER JOIN coupons AS ld
            ON
                ld.campaign_type = 'LD'
                AND orders.amazon_order_id = ld.amazon_order_id
                AND orders.sku = ld.sku
        LEFT OUTER JOIN dcms_settle AS settle
            ON
                orders.amazon_order_id = settle.order_id
                AND orders.sku = settle.sku
        WHERE LENGTH(COALESCE(ld.external_id, settle.external_id)) > 2
    )
    , ld AS (
        SELECT DISTINCT
            orders.* EXCLUDE(external_id, promo_value),
            deals.internal_description AS campaign_name,
            deals.deal_type AS promo_type,
            deals.event_name AS amazon_event,
            orders.promo_value,
            deals.external_id AS campaign_id,
        FROM orders_settle_ld AS orders
        LEFT OUTER JOIN deals
            ON
                orders.external_id = deals.external_id
                AND orders.country_code = deals.country_code
                AND (
                    orders.purchased_at_pst BETWEEN deals.start_time AND deals.end_time
                    OR orders.match_promo_value
                )
        WHERE
            deals.external_id IS NOT NULL
    )
    , bd_dotd AS (
        SELECT DISTINCT
            orders.* EXCLUDE(promotion_id),
            deals.internal_description AS campaign_name,
            deals.external_id AS campaign_id,
            deals.deal_type AS promo_type,
            deals.event_name AS amazon_event,
            deals.deal_price AS promo_value,
        FROM orders
        INNER JOIN deals
            ON
                orders.deal_price = deals.deal_price
                AND orders.country_code = deals.country_code
                AND orders.sku = deals.sku
                AND orders.purchased_at_pst BETWEEN deals.start_time AND deals.end_time
    )
    , prime AS (
        SELECT
            * EXCLUDE(start_at, end_at),
            MIN(start_at) AS start_at,
            MAX(end_at) AS end_at,
        FROM (
            SELECT DISTINCT
                country_code,
                promo_name,
                discount_type,
                ped_campaign AS campaign,
                sku,
                asin,
                id,
                ROUND(discounted_price, 2) AS promo_value,
                start_date AS start_at,
                end_date AS end_at,
            FROM DWH.PROD.FACT_AMAZON_PRIME_EXCLUSIVE_DISCOUNTS AS ped
            WHERE
                start_date >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
                OR end_date >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
            UNION
            SELECT DISTINCT
                ped2.country_code,
                promo_title,
                discount_type,
                promo_event_name_string_id AS campaign,
                sku,
                asin,
                id,
                ROUND(price, 2),
                start_date,
                end_date
            FROM DWH.PROD.FACT_AMAZON_PRICE_DISCOUNTS AS ped2
            WHERE
                start_date >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
                OR end_date >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
        )
        GROUP BY ALL        
    )
    , ped AS (
        SELECT DISTINCT
            orders.* EXCLUDE(promotion_id),
            prime.promo_name AS campaign_name,
            prime.id AS campaign_id,
            prime.discount_type AS promo_type,
            prime.campaign AS amazon_event,
            prime.promo_value,
        FROM orders
        INNER JOIN prime
            ON
                orders.country_code = prime.country_code
                AND orders.deal_price = prime.promo_value
                AND orders.sku = prime.sku
                AND orders.purchased_date_utc BETWEEN prime.start_at AND prime.end_at
        QUALIFY ROW_NUMBER() OVER (PARTITION BY orders.amazon_order_id, orders.sku ORDER BY promo_type DESC) = 1
    )
    , promos_union AS (
        SELECT
            country_code,
            brand_code,
            seller_id,
            purchased_date_utc,
            purchased_at_utc,
            amazon_order_id,
            asin,
            sku,
            campaign_type,
            campaign_name,
            campaign_id,
            promo_type,
            amazon_event,
            promo_value,
            quantity,
            item_price,
            item_promotion_discount,
        FROM coupons
        WHERE
            campaign_type = 'COUPON'
            AND COALESCE(campaign_name, '') != ''
        UNION ALL
        SELECT
            country_code,
            brand_code,
            seller_id,
            purchased_date_utc,
            purchased_at_utc,
            amazon_order_id,
            asin,
            sku,
            'LD' AS campaign_type,
            campaign_name,
            campaign_id,
            CASE
                WHEN promo_type = 'LIGHTNING_DEAL' THEN NULL
                ELSE promo_type
            END AS promo_type,
            amazon_event,
            promo_value,
            quantity,
            item_price,
            item_promotion_discount,
        FROM ld
        UNION ALL
        SELECT
            country_code,
            brand_code,
            seller_id,
            purchased_date_utc,
            purchased_at_utc,
            amazon_order_id,
            asin,
            sku,
            'TP' AS campaign_type,
            campaign_name,
            campaign_id,
            promo_type,
            NULL AS amazon_event,
            promo_value,
            quantity,
            item_price,
            item_promotion_discount,
        FROM tp
        UNION ALL
        SELECT
            country_code,
            brand_code,
            seller_id,
            purchased_date_utc,
            purchased_at_utc,
            amazon_order_id,
            asin,
            sku,
            CASE
                WHEN promo_type = 'LIGHTNING_DEAL' THEN 'LD'
                WHEN promo_type = 'BEST_DEAL' THEN '7DD'
                WHEN promo_type = 'DEAL_OF_THE_DAY' THEN 'DOTD'
            END AS campaign_type,
            campaign_name,
            campaign_id,
            CASE
                WHEN promo_type IN ('LIGHTNING_DEAL', 'BEST_DEAL', 'DEAL_OF_THE_DAY') THEN NULL
                ELSE promo_type
            END AS promo_type,
            amazon_event,
            promo_value,
            quantity,
            item_price,
            item_promotion_discount,
        FROM bd_dotd
        UNION ALL
        SELECT
            country_code,
            brand_code,
            seller_id,
            purchased_date_utc,
            purchased_at_utc,
            amazon_order_id,
            asin,
            sku,
            'PED' AS campaign_type,
            campaign_name,
            campaign_id,
            promo_type,
            amazon_event,
            promo_value,
            quantity,
            item_price,
            item_promotion_discount,
        FROM ped
    )

    SELECT DISTINCT *, FROM promos_union

) AS src    
    ON src.amazon_order_id = tgt.amazon_order_id
        AND src.asin = tgt.asin
        AND src.sku = tgt.sku
        AND src.campaign_id = tgt.campaign_id
        AND src.campaign_name = tgt.campaign_name
WHEN MATCHED THEN
UPDATE SET
    tgt.country_code = src.country_code,
    tgt.brand_code = src.brand_code,
    tgt.seller_id = src.seller_id,
    tgt.purchased_date_utc = src.purchased_date_utc,
    tgt.purchased_at_utc = src.purchased_at_utc,
    tgt.amazon_order_id = src.amazon_order_id,
    tgt.asin = src.asin,
    tgt.sku = src.sku,
    tgt.campaign_type = src.campaign_type,
    tgt.campaign_name = src.campaign_name,
    tgt.campaign_id = src.campaign_id,
    tgt.promo_type = src.promo_type,
    tgt.amazon_event = src.amazon_event,
    tgt.promo_value = src.promo_value,
    tgt.quantity = src.quantity,
    tgt.item_price = src.item_price,
    tgt.item_promotion_discount = src.item_promotion_discount
WHEN NOT MATCHED THEN
INSERT (
    country_code,
    brand_code,
    seller_id,
    purchased_date_utc,
    purchased_at_utc,
    amazon_order_id,
    asin,
    sku,
    campaign_type,
    campaign_name,
    campaign_id,
    promo_type,
    amazon_event,
    promo_value,
    quantity,
    item_price,
    item_promotion_discount
)
VALUES
(
    src.country_code,
    src.brand_code,
    src.seller_id,
    src.purchased_date_utc,
    src.purchased_at_utc,
    src.amazon_order_id,
    src.asin,
    src.sku,
    src.campaign_type,
    src.campaign_name,
    src.campaign_id,
    src.promo_type,
    src.amazon_event,
    src.promo_value,
    src.quantity,
    src.item_price,
    src.item_promotion_discount
);

ALTER SESSION SET WEEK_START = 1;
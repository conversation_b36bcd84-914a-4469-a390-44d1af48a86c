ALTER SESSION SET WEEK_START = 7;
SET DATE_RANGE = 90;
CREATE TABLE IF NOT EXISTS $curated_db.promotion_fees_classification (
	PURCHASED_DATE_LOCAL DATE,
	WEEK INTEGER,
	COUNTRY_CODE VARCHAR(16777216),
	BRAND_CODE VARCHAR(50),
	SELLER_ID VARCHAR(16777216),
	SELLER_NAME VARCHAR(16777216),
	ASIN VARCHAR(16777216),
	CAMPAIGN_NAME VARCHAR(16777216),
	CAMPAIGN_ID VARCHAR(16777216),
	CAMPAIGN_TYPE VARCHAR(16777216),
	AMAZON_EVENT VARCHAR(16777216),
	TOTAL_PROMOTION FLOAT,
	TOTAL_UNITS FLOAT,
	CAMPAIGN_ASIN_COUNT NUMBER(18,0),
	PROMO_TYPE VARCHAR(16777216),
	CAMPAIGN_START DATE,
	TOTAL_PROMO_COST FLOAT,
	TOTAL_PROMO_COST_EST_W FLOAT,
	TOTAL_PROMO_COST_EST_7D FLOAT
);
MERGE INTO
    $curated_db.promotion_fees_classification AS tgt
USING (
    WITH max AS (
        SELECT MAX(purchased_date_local) AS last_date FROM $curated_db.promotion_fees_classification
    )
    ,orders_promo_data AS (
        SELECT
            purchased_date_local,
            WEEKOFYEAR(date(purchased_date_local)) as WEEK,
            country_code,
            brand_code,
            seller_id,
            seller_name,
            asin,
            campaign_name,
            COALESCE(campaign_id, '') AS campaign_id,
            campaign_type,
            COALESCE(amazon_event, '') AS amazon_event,
            ROUND(SUM(promo_value), 2) AS total_promotion,
            SUM(quantity) AS total_units,
        FROM
            $curated_db.orders_promotion_classification
        WHERE
            campaign_type IS NOT NULL
            AND campaign_type NOT IN ('ORGANIC', 'PED', 'VB', 'TP')
            AND purchased_date_local >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
        GROUP BY ALL
    )
    , asins_campaign AS (
        SELECT
            brand_code,
            campaign_name,
            campaign_id,
            COUNT(DISTINCT asin) AS asins,
            COUNT(DISTINCT CONCAT(asin, purchased_date_local)) AS asin_date,
        FROM orders_promo_data
        GROUP BY ALL
    )
    , campaign_days AS (
        SELECT
            MIN(purchased_date_local) AS campaign_start,
            COUNT(DISTINCT purchased_date_local) AS campaign_days,
            SUM(total_promotion) AS campaign_total,
            campaign_name,
            campaign_id,
        FROM orders_promo_data
        GROUP BY ALL
    )
    , deal_fees AS (
        SELECT DISTINCT
            country as country_code,
            CASE UPPER(promo_type)
                WHEN 'BEST DEAL (7DD)' THEN '7DD'
                WHEN 'COUPONS' THEN 'COUPON'
                ELSE UPPER(promo_type)
            END AS promo_type,
            TRY_TO_DOUBLE(TRIM(fee_bau, '$')) AS fee_bau,
            lenght_bau,
            TRY_TO_DOUBLE(TRIM(fee_events, '$')) AS fee_events,
            lenght_event,
        FROM
            -- June 2025 backward
            -- https://docs.google.com/spreadsheets/d/1mxptKVpU-Tgb5PlTC0UforlWnKcPXE37WMpToxMeHH4/
            SANDBOX.TEST_POC.DEAL_FEES_SPREADSHEET
        WHERE
            country IS NOT NULL
    )
    , rates AS (
        SELECT effective_date, transactional_currency AS currency, exchange_rate AS rate 
        FROM DWH.NETSUITE.FOREIGN_EXCHANGE WHERE base_currency = 'USD'
    )
    , deal_fees_with_date AS (
        SELECT DISTINCT
            country as country_code,
            currency,
            CASE UPPER(promo_type)
                WHEN 'BEST DEAL (7DD)' THEN '7DD'
                WHEN 'COUPONS' THEN 'COUPON'
                ELSE UPPER(promo_type)
            END AS promo_type,
            TRY_TO_DOUBLE(TRIM(daily_fee, '$')) AS fee_daily,
            TRY_TO_DOUBLE(TRIM(sales_percent, '%'))/100 AS sales_percent,
            TRY_TO_DOUBLE(TRIM(cap, '$')) AS cap,
            lenght_bau,
            TRY_TO_DOUBLE(TRIM(fee_events, '$')) AS fee_events,
            lenght_event,
            EFFECTIVE_DATE
        FROM
            -- This needs to be updated with the current fees from amazon. Current sponsors are Gilles and Valeria
            -- https://docs.google.com/spreadsheets/d/1mxptKVpU-Tgb5PlTC0UforlWnKcPXE37WMpToxMeHH4/
            SANDBOX.TEST_POC.DEAL_FEES_2025_SPREADSHEET
        FULL OUTER JOIN (SELECT effective_date FROM rates)  
        WHERE
            country IS NOT NULL
    ),
    deal_fees_25 as (
        SELECT 
            COUNTRY_CODE,
            CURRENCY, 
            PROMO_TYPE, 
            FEE_DAILY, 
            SALES_PERCENT, 
            CASE 
                WHEN (MONTH(EFFECTIVE_DATE) = 7 AND DAY(EFFECTIVE_DATE) BETWEEN 8 AND 11)
                  OR (MONTH(EFFECTIVE_DATE) = 11 AND DAY(EFFECTIVE_DATE) BETWEEN 20 AND 30)
                  OR (MONTH(EFFECTIVE_DATE) = 12 AND DAY(EFFECTIVE_DATE) = 1)
                THEN 1000
                ELSE CAP
            END AS CAP,
            LENGHT_BAU, 
            FEE_EVENTS, 
            LENGHT_EVENT, 
            EFFECTIVE_DATE AS FEE_EFFECTIVE_DATE,
        FROM deal_fees_with_date
    )
    
    , ld_bd_dotd_deals AS (
        SELECT * FROM
        (
            SELECT DISTINCT
                start_time AS start_at_pst,
                CONVERT_TIMEZONE('UTC', 'America/Los_Angeles',start_time) AS deal_start_at_utc,
                CONVERT_TIMEZONE('UTC', 'America/Los_Angeles',end_time) AS deal_end_at_utc,
                country_code AS deal_market,
                COALESCE(d.external_id, bq.external_id) AS external_id,
                d.scheduled_fee AS fees,
            FROM DWH.PROD.FACT_AMAZON_DEALS AS d
            LEFT JOIN SANDBOX.TEST_POC.BQ_DEALS_EXTERNAL_IDS AS bq
            ON d.campaign_id = bq.id
            WHERE (d.start_time >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE)
                OR d.end_time >= IFF((SELECT last_date FROM max) IS NULL, DATE('2024-01-01'), CURRENT_DATE - $DATE_RANGE))
        )
        QUALIFY ROW_NUMBER() OVER (PARTITION BY external_id ORDER BY start_at_pst DESC) = 1
    )
    , joined AS (
        SELECT
            orders.*,
            asins_campaign.asins AS campaign_asin_count,
            fees.promo_type,
            COALESCE(ROUND(
                CASE
                    WHEN orders.campaign_type = 'COUPON' 
                        THEN orders.total_units * fees.fee_bau
                    WHEN orders.amazon_event != '' 
                        THEN DIV0(COALESCE(deals.fees, fees.fee_events), asins_campaign.asin_date)
                    WHEN orders.campaign_type = '7DD' AND DATE(deals.start_at_pst) = orders.purchased_date_local 
                        THEN DIV0(COALESCE(deals.fees, fees.fee_events), asins_campaign.asins)
                    WHEN orders.campaign_type != '7DD' 
                        THEN DIV0(COALESCE(deals.fees, fees.fee_bau), asins_campaign.asin_date)
                END, 
            2), 0) AS total_promo_cost,
            COALESCE(ROUND(
                CASE
                    WHEN orders.campaign_type = 'COUPON' 
                        THEN (campaign_days.campaign_total * fees25.sales_percent) + (campaign_days.campaign_days * (fees25.fee_daily / coalesce(rates.rate, 1.0)))
                    WHEN orders.amazon_event != '' 
                        THEN DIV0(IFF((campaign_days.campaign_total * fees25.sales_percent + (campaign_days.campaign_days * (fees25.fee_daily / coalesce(rates.rate, 1.0))) + fees25.fee_events) >= fees25.cap, fees25.cap, (campaign_days.campaign_days * (fees25.fee_daily / coalesce(rates.rate, 1.0))) + fees25.fee_events + campaign_days.campaign_total * fees25.sales_percent), asins_campaign.asins)
                    ELSE DIV0(IFF(campaign_days.campaign_total * fees25.sales_percent + (campaign_days.campaign_days * (fees25.fee_daily / coalesce(rates.rate, 1.0))) > fees25.cap, fees25.cap, + (campaign_days.campaign_days * (fees25.fee_daily / coalesce(rates.rate, 1.0))) + campaign_days.campaign_total * fees25.sales_percent), asins_campaign.asins)
                END, 
            2), 0) AS total_promo_cost_25,
            campaign_start,
        FROM
            orders_promo_data AS orders
        LEFT OUTER JOIN
            asins_campaign
            ON
                orders.brand_code = asins_campaign.brand_code
                AND orders.campaign_name = asins_campaign.campaign_name
                AND orders.campaign_id = asins_campaign.campaign_id
        LEFT OUTER JOIN
            campaign_days
            ON
                orders.campaign_name = campaign_days.campaign_name
                AND orders.campaign_id = campaign_days.campaign_id
        LEFT OUTER JOIN
            deal_fees AS fees
            ON
                UPPER(orders.campaign_type) = fees.promo_type
                AND orders.country_code = fees.country_code
        LEFT OUTER JOIN
            deal_fees_25 AS fees25
            ON
                UPPER(orders.campaign_type) = fees25.promo_type
                AND orders.country_code = fees25.country_code
                AND orders.purchased_date_local = fees25.fee_effective_date
        LEFT OUTER JOIN
            rates
            ON
                fees25.currency = rates.currency
                AND orders.purchased_date_local = rates.effective_date
        LEFT OUTER JOIN
            ld_bd_dotd_deals AS deals
            ON
                orders.country_code = deals.deal_market
                AND orders.campaign_id = deals.external_id
    )

    SELECT 
        * EXCLUDE (total_promo_cost, total_promo_cost_25),
        IFF(campaign_start >= '2025-06-01', total_promo_cost_25, total_promo_cost) AS total_promo_cost,
        -- estimate costs for recent week
        ROUND(IFF(purchased_date_local >= DATE_TRUNC(week, CURRENT_DATE - 7) AND campaign_type = 'COUPON', IFF(campaign_start >= '2025-06-01', total_promo_cost_25, total_promo_cost) * 1.35, 0)) AS total_promo_cost_est_w,
        ROUND(IFF(purchased_date_local >= CURRENT_DATE - 7 AND campaign_type = 'COUPON', IFF(campaign_start >= '2025-06-01', total_promo_cost_25, total_promo_cost) * 1.35, 0)) AS total_promo_cost_est_7d,
    FROM joined
    QUALIFY
        ROW_NUMBER() OVER (
            PARTITION BY asin, campaign_name, campaign_id
            ORDER BY purchased_date_local DESC
        ) = 1
) AS src    
    ON src.purchased_date_local = tgt.purchased_date_local
        AND src.asin = tgt.asin
        AND src.campaign_name = tgt.campaign_name
        AND src.campaign_id = tgt.campaign_id
WHEN MATCHED THEN
UPDATE SET
    tgt.purchased_date_local = src.purchased_date_local,
    tgt.week = src.week,
    tgt.country_code = src.country_code,
    tgt.brand_code = src.brand_code,
    tgt.seller_id = src.seller_id,
    tgt.seller_name = src.seller_name,
    tgt.asin = src.asin,
    tgt.campaign_name = src.campaign_name,
    tgt.campaign_id = src.campaign_id,
    tgt.campaign_type = src.campaign_type,
    tgt.amazon_event = src.amazon_event,
    tgt.total_promotion = src.total_promotion,
    tgt.total_units = src.total_units,
    tgt.campaign_asin_count = src.campaign_asin_count,
    tgt.promo_type = src.promo_type,
    tgt.campaign_start = src.campaign_start,
    tgt.total_promo_cost = src.total_promo_cost,
    tgt.total_promo_cost_est_w = src.total_promo_cost_est_w,
    tgt.total_promo_cost_est_7d = src.total_promo_cost_est_7d
WHEN NOT MATCHED THEN
INSERT (
    purchased_date_local,
    week,
    country_code,
    brand_code,
    seller_id,
    seller_name,
    asin,
    campaign_name,
    campaign_id,
    campaign_type,
    amazon_event,
    total_promotion,
    total_units,
    campaign_asin_count,
    promo_type,
    campaign_start,
    total_promo_cost,
    total_promo_cost_est_w,
    total_promo_cost_est_7d
)
VALUES
(
    src.purchased_date_local,
    src.week,
    src.country_code,
    src.brand_code,
    src.seller_id,
    src.seller_name,
    src.asin,
    src.campaign_name,
    src.campaign_id,
    src.campaign_type,
    src.amazon_event,
    src.total_promotion,
    src.total_units,
    src.campaign_asin_count,
    src.promo_type,
    src.campaign_start,
    src.total_promo_cost,
    src.total_promo_cost_est_w,
    src.total_promo_cost_est_7d
);

ALTER SESSION SET WEEK_START = 1;
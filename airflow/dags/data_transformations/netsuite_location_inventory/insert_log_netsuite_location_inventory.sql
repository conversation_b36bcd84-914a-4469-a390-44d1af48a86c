CREATE TRANSIENT TABLE IF NOT EXISTS $stage_db.log_fact_netsuite_location_inventory AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $stage_db.FACT_NETSUITE_LOCATION_INVENTORY
    WHERE 1 = 0;

INSERT INTO $stage_db.log_fact_netsuite_location_inventory (
    item_internal_id,
    item_type,
    item_name,
    item_display_name,
    item_asin,
    item_class,
    item_product_type,
    location_internal_id,
    location_name,
    location_main_address_country,
    location_name_corrected,
    location_type,
    location_category,
    location_subsidiary_name,
    location_subsidiary_internal_id,
    item_value_on_hand,
    item_last_purchase_price,
    item_average_cost,
    item_pref_stock_level,
    item_quantity_available,
    item_quantity_back_ordered,
    item_quantity_committed,
    item_quantity_in_transit,
    item_quantity_on_hand,
    item_quantity_on_order,
    last_quant_avail_change_timestamp,
    sync_timestamp,
    log_timestamp_utc
)
SELECT
    item_internal_id,
    item_type,
    item_name,
    item_display_name,
    item_asin,
    item_class,
    item_product_type,
    location_internal_id,
    location_name,
    location_main_address_country,
    location_name_corrected,
    location_type,
    location_category,
    location_subsidiary_name,
    location_subsidiary_internal_id,
    item_value_on_hand,
    item_last_purchase_price,
    item_average_cost,
    item_pref_stock_level,
    item_quantity_available,
    item_quantity_back_ordered,
    item_quantity_committed,
    item_quantity_in_transit,
    item_quantity_on_hand,
    item_quantity_on_order,
    last_quant_avail_change_timestamp,
    sync_timestamp,
    SYSDATE() AS log_timestamp_utc
FROM 
    $stage_db.FACT_NETSUITE_LOCATION_INVENTORY;
CREATE OR <PERSON><PERSON>LACE TABLE $stage_db.FACT_NETSUITE_LOCATION_INVENTORY AS (
    WITH NS_LOCATIONS AS (
  SELECT 
    DISTINCT LOCATION_INT_ID, 
    LOCATION_MAIN_ADDRESS_COUNTRY 
  FROM 
    NETSUITE.NETSUITE.NETSUITE_LOCATIONS
) 
SELECT 
  ITEM_INTERNAL_ID, 
  ITEM_TYPE, 
  ITEM_NAME, 
  ITEM_DISPLAY_NAME, 
  ITEM_ASIN, 
  ITEM_CLASS, 
  ITEM_PRODUCT_TYPE, 
  LOCATION_INTERNAL_ID, 
  LOCATION_NAME, 
  LOCATION_MAIN_ADDRESS_COUNTRY, 
  UPPER(
    TRIM(
      NVL(
        REGEXP_SUBSTR(
          LOCATION_NAME, '^(.*)-[A-Z]{2,3}-.*$', 
          1, 1, 'e'
        ), 
        LOCATION_NAME
      )
    )
  ) AS LOCATION_NAME_CORRECTED, 
  LOCATION_TYPE, 
  LOCATION_CATEGORY, 
  LOCATION_SUBSIDIARY_NAME, 
  LOCATION_SUBSIDIARY_INTERNAL_ID, 
  ITEM_VALUE_ON_HAND, 
  ITEM_LAST_PURCHASE_PRICE, 
  ITEM_AVERAGE_COST, 
  ITEM_PREF_STOCK_LEVEL, 
  ITEM_QUANTITY_AVAILABLE, 
  ITEM_QUANTITY_BACK_ORDERED, 
  ITEM_QUANTITY_COMMITTED, 
  ITEM_QUANTITY_IN_TRANSIT, 
  ITEM_QUANTITY_ON_HAND, 
  ITEM_QUANTITY_ON_ORDER, 
  LAST_QUANT_AVAIL_CHANGE_TIMESTAMP, 
  SYNC_TIMESTAMP 
FROM 
  NETSUITE.NETSUITE.NETSUITE_INVENTORY_LOCATIONS 
  LEFT JOIN NS_LOCATIONS ON LOCATION_INT_ID = LOCATION_INTERNAL_ID 
WHERE 
  NOT (
    (
      ITEM_QUANTITY_AVAILABLE = 0 
      OR ITEM_QUANTITY_AVAILABLE IS NULL
    ) 
    AND (
      ITEM_QUANTITY_BACK_ORDERED = 0 
      OR ITEM_QUANTITY_BACK_ORDERED IS NULL
    ) 
    AND (
      ITEM_QUANTITY_COMMITTED = 0 
      OR ITEM_QUANTITY_COMMITTED IS NULL
    ) 
    AND (
      ITEM_QUANTITY_IN_TRANSIT = 0 
      OR ITEM_QUANTITY_IN_TRANSIT IS NULL
    ) 
    AND (
      ITEM_QUANTITY_ON_HAND = 0 
      OR ITEM_QUANTITY_ON_HAND IS NULL
    ) 
    AND (
      ITEM_QUANTITY_ON_ORDER = 0 
      OR ITEM_QUANTITY_ON_ORDER IS NULL
    )
  )
);
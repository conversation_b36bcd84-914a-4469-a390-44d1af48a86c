CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amazon_sns_performance_report AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(snapshot_date AS VARCHAR), ''), '-',
            COALESCE(CAST(week_1_start_date AS VARCHAR), ''), '-',
            COALESCE(CAST(marketplaceid AS VARCHAR), ''), '-',
            COALESCE(CAST(sellingpartnerid AS VARCHAR), ''), '-',
            COALESCE(CAST(country AS VARCHAR), ''), '-',
            COALESCE(CAST(asin AS VARCHAR), ''), '-',
            COALESCE(CAST(fnsku AS VARCHAR), ''), '-',
            COALESCE(CAST(offer_state AS VARCHAR), ''), '-',
            COALESCE(CAST(sku AS VARCHAR), '')
            )) AS pk,
        ReportRequestTime,
        ReportstartDate,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        asin,
        country,
        fnsku,
        marketplaceId,
        marketplaceName,
        offer_state,
        oos_rate_week_1,
        oos_rate_week_2,
        oos_rate_week_3,
        oos_rate_week_4,
        product_name,
        sellingPartnerId,
        sku,
        snapshot_date,
        sns_discount_week_1,
        sns_discount_week_2,
        sns_discount_week_3,
        sns_discount_week_4,
        sns_sale_price_week_1,
        sns_sale_price_week_2,
        sns_sale_price_week_3,
        sns_sale_price_week_4,
        sns_units_shipped_week_1,
        sns_units_shipped_week_2,
        sns_units_shipped_week_3,
        sns_units_shipped_week_4,
        week_1_start_date,
        file_name,
        etl_batch_run_time
    FROM $raw_db.raw_amazon_sns_performance_report
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);
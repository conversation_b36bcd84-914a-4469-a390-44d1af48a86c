CREATE TABLE IF NOT EXISTS $stage_db.merge_amazon_sns_performance_report AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_amazon_sns_performance_report
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_amazon_sns_performance_report AS tgt
USING
    $stage_db.dedupe_amazon_sns_performance_report AS src
        ON 1 = 1
       AND src.pk = tgt.pk
WHEN MATCHED AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    tgt.ReportRequestTime = src.ReportRequestTime,
    tgt.ReportstartDate = src.ReportstartDate,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.asin = src.asin,
    tgt.country = src.country,
    tgt.fnsku = src.fnsku,
    tgt.marketplaceId = src.marketplaceId,
    tgt.marketplaceName = src.marketplaceName,
    tgt.offer_state = src.offer_state,
    tgt.oos_rate_week_1 = src.oos_rate_week_1,
    tgt.oos_rate_week_2 = src.oos_rate_week_2,
    tgt.oos_rate_week_3 = src.oos_rate_week_3,
    tgt.oos_rate_week_4 = src.oos_rate_week_4,
    tgt.product_name = src.product_name,
    tgt.sellingPartnerId = src.sellingPartnerId,
    tgt.sku = src.sku,
    tgt.snapshot_date = src.snapshot_date,
    tgt.sns_discount_week_1 = src.sns_discount_week_1,
    tgt.sns_discount_week_2 = src.sns_discount_week_2,
    tgt.sns_discount_week_3 = src.sns_discount_week_3,
    tgt.sns_discount_week_4 = src.sns_discount_week_4,
    tgt.sns_sale_price_week_1 = src.sns_sale_price_week_1,
    tgt.sns_sale_price_week_2 = src.sns_sale_price_week_2,
    tgt.sns_sale_price_week_3 = src.sns_sale_price_week_3,
    tgt.sns_sale_price_week_4 = src.sns_sale_price_week_4,
    tgt.sns_units_shipped_week_1 = src.sns_units_shipped_week_1,
    tgt.sns_units_shipped_week_2 = src.sns_units_shipped_week_2,
    tgt.sns_units_shipped_week_3 = src.sns_units_shipped_week_3,
    tgt.sns_units_shipped_week_4 = src.sns_units_shipped_week_4,
    tgt.week_1_start_date = src.week_1_start_date,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    ReportRequestTime,
    ReportstartDate,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    asin,
    country,
    fnsku,
    marketplaceId,
    marketplaceName,
    offer_state,
    oos_rate_week_1,
    oos_rate_week_2,
    oos_rate_week_3,
    oos_rate_week_4,
    product_name,
    sellingPartnerId,
    sku,
    snapshot_date,
    sns_discount_week_1,
    sns_discount_week_2,
    sns_discount_week_3,
    sns_discount_week_4,
    sns_sale_price_week_1,
    sns_sale_price_week_2,
    sns_sale_price_week_3,
    sns_sale_price_week_4,
    sns_units_shipped_week_1,
    sns_units_shipped_week_2,
    sns_units_shipped_week_3,
    sns_units_shipped_week_4,
    week_1_start_date,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src.ReportRequestTime, 
    src.ReportstartDate, 
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.asin, 
    src.country, 
    src.fnsku, 
    src.marketplaceId, 
    src.marketplaceName, 
    src.offer_state, 
    src.oos_rate_week_1, 
    src.oos_rate_week_2, 
    src.oos_rate_week_3, 
    src.oos_rate_week_4, 
    src.product_name, 
    src.sellingPartnerId, 
    src.sku, 
    src.snapshot_date, 
    src.sns_discount_week_1, 
    src.sns_discount_week_2, 
    src.sns_discount_week_3, 
    src.sns_discount_week_4, 
    src.sns_sale_price_week_1, 
    src.sns_sale_price_week_2, 
    src.sns_sale_price_week_3, 
    src.sns_sale_price_week_4, 
    src.sns_units_shipped_week_1, 
    src.sns_units_shipped_week_2, 
    src.sns_units_shipped_week_3, 
    src.sns_units_shipped_week_4, 
    src.week_1_start_date, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
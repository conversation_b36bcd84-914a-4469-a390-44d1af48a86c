CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_amazon_sns_performance_report AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_amazon_sns_performance_report
    WHERE 1 = 0;

INSERT INTO $raw_db.log_amazon_sns_performance_report (
    ReportRequestTime,
    ReportstartDate,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    asin,
    country,
    fnsku,
    marketplaceId,
    marketplaceName,
    offer_state,
    oos_rate_week_1,
    oos_rate_week_2,
    oos_rate_week_3,
    oos_rate_week_4,
    product_name,
    sellingPartnerId,
    sku,
    snapshot_date,
    sns_discount_week_1,
    sns_discount_week_2,
    sns_discount_week_3,
    sns_discount_week_4,
    sns_sale_price_week_1,
    sns_sale_price_week_2,
    sns_sale_price_week_3,
    sns_sale_price_week_4,
    sns_units_shipped_week_1,
    sns_units_shipped_week_2,
    sns_units_shipped_week_3,
    sns_units_shipped_week_4,
    week_1_start_date,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
SELECT
    ReportRequestTime,
    ReportstartDate,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    asin,
    country,
    fnsku,
    marketplaceId,
    marketplaceName,
    offer_state,
    oos_rate_week_1,
    oos_rate_week_2,
    oos_rate_week_3,
    oos_rate_week_4,
    product_name,
    sellingPartnerId,
    sku,
    snapshot_date,
    sns_discount_week_1,
    sns_discount_week_2,
    sns_discount_week_3,
    sns_discount_week_4,
    sns_sale_price_week_1,
    sns_sale_price_week_2,
    sns_sale_price_week_3,
    sns_sale_price_week_4,
    sns_units_shipped_week_1,
    sns_units_shipped_week_2,
    sns_units_shipped_week_3,
    sns_units_shipped_week_4,
    week_1_start_date,
    file_name,
    etl_batch_run_time,
    SYSDATE() AS log_timestamp_utc
FROM $raw_db.raw_amazon_sns_performance_report;
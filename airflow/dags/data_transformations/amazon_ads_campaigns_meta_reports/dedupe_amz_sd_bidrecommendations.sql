CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amz_sd_bidrecommendations AS
SELECT
    --  primary key  --
    MD5(
        CONCAT(
            COALESCE(CAST(fetchdate AS VARCHAR), ''), '-',
            COALESCE(CAST(accountid AS VARCHAR), ''), '-',
            COALESCE(CAST(targetingclauses AS VARCHAR), ''), '-',
            COALESCE(CAST(profileid AS VARCHAR), '')
        )
    ) AS pk
    , requesttime AS request_time
    , accountid AS account_id
    , accountname AS account_name
    , bidoptimization AS bid_optimization
    , bidrecommendations AS bid_recommendations
    , category AS category
    , costtype AS cost_type
    , countryname AS country_name
    , fetchdate AS fetch_date
    , name AS name
    , profileid AS profile_id
    , targetingclauses AS targeting_clauses
    , _daton_batch_id
    , _daton_batch_runtime
    , _daton_user_id
    , file_name
    , etl_batch_run_time
FROM $raw_db.raw_amz_sd_bid_recommendations
WHERE 1=1
QUALIFY ROW_NUMBER() OVER(
    PARTITION BY fetchdate, accountid, profileid, targetingclauses
    ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1;

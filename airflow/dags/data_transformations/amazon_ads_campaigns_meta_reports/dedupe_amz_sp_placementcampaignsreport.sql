
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amz_sp_placementcampaignsreport AS (
    SELECT
        
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(reportdate AS VARCHAR), ''), '-',
        COALESCE(CAST(accountid AS VARCHAR), ''), '-',
        COALESCE(CAST(campaignid AS VARCHAR), ''), '-',
        COALESCE(CAST(placement AS VARCHAR), '')
        )) AS pk,
        
        requesttime,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountid,
        accountname,
        attributedconversions14d,
        attributedconversions14dsamesku,
        attributedconversions1d,
        attributedconversions1dsamesku,
        attributedconversions30d,
        attributedconversions30dsamesku,
        attributedconversions7d,
        attributedconversions7dsamesku,
        attributedsales14d,
        attributedsales14dsamesku,
        attributedsales1d,
        attributedsales1dsamesku,
        attributedsales30d,
        attributedsales30dsamesku,
        attributedsales7d,
        attributedsales7dsamesku,
        attributedunitsordered14d,
        attributedunitsordered14dsamesku,
        attributedunitsordered1d,
        attributedunitsordered1dsamesku,
        attributedunitsordered30d,
        attributedunitsordered30dsamesku,
        attributedunitsordered7d,
        attributedunitsordered7dsamesku,
        bidplus,
        biddingstrategy,
        campaignbudget,
        campaignid,
        campaignstatus,
        clicks,
        cost,
        countryname,
        impressions,
        placement,
        profileid,
        reportdate,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_amz_sp_placementcampaignsreport
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY reportdate, accountid, campaignid, placement
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);

CREATE TABLE IF NOT EXISTS $stage_db.merge_amz_sb_campaign AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    
    FROM $stage_db.dedupe_amz_sb_campaign
    
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_amz_sb_campaign AS tgt
USING
    
    $stage_db.dedupe_amz_sb_campaign AS src
    
    ON 1 = 1
    AND src.pk = tgt.pk 
WHEN MATCHED
    AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    
    tgt.pk = src.pk,
    tgt.requesttime = src.requesttime,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.accountid = src.accountid,
    tgt.accountname = src.accountname,
    tgt.bidmultiplier = src.bidmultiplier,
    tgt.bidoptimization = src.bidoptimization,
    tgt.budget = src.budget,
    tgt.budgettype = src.budgettype,
    tgt.campaignid = src.campaignid,
    tgt.countryname = src.countryname,
    tgt.bidding = src.bidding,
    tgt.brandEntityId = src.brandEntityId,
    tgt.costType = src.costType,
    tgt.extendedData = src.extendedData,
    tgt.goal = src.goal,
    tgt.isMultiAdGroupsEnabled = src.isMultiAdGroupsEnabled,
    tgt.smartDefault = src.smartDefault,
    tgt.creative = src.creative,
    tgt.enddate = src.enddate,
    tgt.fetchdate = src.fetchdate,
    tgt.landingpage = src.landingpage,
    tgt.name = src.name,
    tgt.portfolioid = src.portfolioid,
    tgt.profileid = src.profileid,
    tgt.servingstatus = src.servingstatus,
    tgt.startdate = src.startdate,
    tgt.state = src.state,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    
    pk,
    requesttime,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    accountid,
    accountname,
    bidmultiplier,
    bidoptimization,
    budget,
    budgettype,
    campaignid,
    countryname,
    bidding,
    brandEntityId,
    costType,
    extendedData,
    goal,
    isMultiAdGroupsEnabled,
    smartDefault,
    creative,
    enddate,
    fetchdate,
    landingpage,
    name,
    portfolioid,
    profileid,
    servingstatus,
    startdate,
    state,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
     
    src.pk,
    src.requesttime, 
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.accountid, 
    src.accountname, 
    src.bidmultiplier, 
    src.bidoptimization, 
    src.budget, 
    src.budgettype, 
    src.campaignid, 
    src.countryname,
    src.bidding,
    src.brandEntityId,
    src.costType,
    src.extendedData,
    src.goal,
    src.isMultiAdGroupsEnabled,
    src.smartDefault,
    src.creative, 
    src.enddate, 
    src.fetchdate, 
    src.landingpage, 
    src.name, 
    src.portfolioid, 
    src.profileid, 
    src.servingstatus, 
    src.startdate, 
    src.state, 
    src.file_name,
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
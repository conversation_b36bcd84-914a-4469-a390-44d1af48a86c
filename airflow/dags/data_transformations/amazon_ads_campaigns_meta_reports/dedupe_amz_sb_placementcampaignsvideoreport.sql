
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amz_sb_placementcampaignsvideoreport AS (
    SELECT
        
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(reportdate AS VARCHAR), ''), '-',
        COALESCE(CAST(accountid AS VARCHAR), ''), '-',
        COALESCE(CAST(campaignid AS VARCHAR), ''), '-',
        COALESCE(CAST(placement AS VARCHAR), '')
        )) AS pk,

        requesttime,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountid,
        accountname,
        attributedconversions14d,
        attributedconversions14dsamesku,
        attributedDetailPageViewsClicks14d,
        attributedOrderRateNewToBrand14d,
        attributedOrdersNewToBrand14d,
        attributedOrdersNewToBrandPercentage14d,
        attributedsales14d,
        attributedsales14dsamesku,
        attributedSalesNewToBrand14d,
        attributedSalesNewToBrandPercentage14d,
        attributedUnitsOrderedNewToBrand14d,
        attributedUnitsOrderedNewToBrandPercentage14d,
        campaignbudget,
        campaignbudgettype,
        campaignid,
        campaignname,
        campaignstatus,
        clicks,
        cost,
        countryname,
        dpv14d,
        impressions,
        placement,
        profileid,
        reportdate,
        vctr,
        video5SecondViewRate,
        video5SecondViews,
        videoCompleteViews,
        videoFirstQuartileViews,
        videoMidpointViews,
        videoThirdQuartileViews,
        videoUnmutes,
        viewableImpressions,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_amz_sb_placementcampaignsvideoreport
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY reportdate, accountid, campaignid, placement
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);

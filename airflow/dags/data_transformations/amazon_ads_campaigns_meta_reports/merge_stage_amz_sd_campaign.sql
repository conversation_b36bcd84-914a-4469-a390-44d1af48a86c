CREATE TABLE IF NOT EXISTS $stage_db.merge_amz_sd_campaign AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    
    FROM $stage_db.dedupe_amz_sd_campaign
    
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_amz_sd_campaign AS tgt
USING
    
    $stage_db.dedupe_amz_sd_campaign AS src
    
    ON 1 = 1
    AND src.pk = tgt.pk 
WHEN MATCHED
    AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    
    tgt.pk = src.pk,
    tgt.requesttime = src.requesttime,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.accountid = src.accountid,
    tgt.accountname = src.accountname,
    tgt.budget = src.budget,
    tgt.budgettype = src.budgettype,
    tgt.campaignid = src.campaignid,
    tgt.countryname = src.countryname,
    tgt.enddate = src.enddate,
    tgt.fetchdate = src.fetchdate,
    tgt.name = src.name,
    tgt.portfolioid = src.portfolioid,
    tgt.profileid = src.profileid,
    tgt.startdate = src.startdate,
    tgt.state = src.state,
    tgt.tactic = src.tactic,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    
    pk,
    requesttime,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    accountid,
    accountname,
    budget,
    budgettype,
    campaignid,
    countryname,
    enddate,
    fetchdate,
    name,
    portfolioid,
    profileid,
    startdate,
    state,
    tactic,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
     
    src.pk,
    src.requesttime, 
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.accountid, 
    src.accountname, 
    src.budget, 
    src.budgettype, 
    src.campaignid, 
    src.countryname, 
    src.enddate, 
    src.fetchdate, 
    src.name, 
    src.portfolioid, 
    src.profileid, 
    src.startdate, 
    src.state, 
    src.tactic, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
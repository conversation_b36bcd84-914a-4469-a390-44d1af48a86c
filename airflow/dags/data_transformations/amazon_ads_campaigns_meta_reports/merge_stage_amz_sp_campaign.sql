CREATE TABLE IF NOT EXISTS $stage_db.merge_amz_sp_campaign AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_amz_sp_campaign
    WHERE 1 = 0;


MERGE INTO
    $stage_db.merge_amz_sp_campaign AS tgt
USING
    $stage_db.dedupe_amz_sp_campaign AS src
    ON 1 = 1
    AND src.pk = tgt.pk 
WHEN MATCHED
    AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    tgt.requesttime = src.requesttime,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.accountid = src.accountid,
    tgt.accountname = src.accountname,
    tgt.bidding = src.bidding,
    tgt.budget = src.budget,
    tgt.budget_type = src.budget_type,
    tgt.budget_value = src.budget_value,
    tgt.effective_budget = src.effective_budget,
    tgt.campaignid = src.campaignid,
    tgt.campaigntype = src.campaigntype,
    tgt.countryname = src.countryname,
    tgt.dailybudget = src.dailybudget,
    tgt.dynamicbidding = src.dynamicbidding,
    tgt.enddate = src.enddate,
    tgt.fetchdate = src.fetchdate,
    tgt.name = src.name,
    tgt.portfolioid = src.portfolioid,
    tgt.premiumbidadjustment = src.premiumbidadjustment,
    tgt.profileid = src.profileid,
    tgt.startdate = src.startdate,
    tgt.state = src.state,
    tgt.tags = src.tags,
    tgt.targetingtype = src.targetingtype,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    requesttime,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    accountid,
    accountname,
    bidding,
    budget,
    budget_type,
    budget_value,
    effective_budget,
    campaignid,
    campaigntype,
    countryname,
    dailybudget,
    dynamicbidding,
    enddate,
    fetchdate,
    name,
    portfolioid,
    premiumbidadjustment,
    profileid,
    startdate,
    state,
    tags,
    targetingtype,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src.requesttime,
    src._daton_batch_id,
    src._daton_batch_runtime,
    src._daton_user_id,
    src.accountid,
    src.accountname, 
    src.bidding,
    src.budget,
    src.budget_type,
    src.budget_value,
    src.effective_budget,
    src.campaignid,
    src.campaigntype,
    src.countryname,
    src.dailybudget,
    src.dynamicbidding,
    src.enddate,
    src.fetchdate,
    src.name,
    src.portfolioid,
    src.premiumbidadjustment,
    src.profileid,
    src.startdate,
    src.state,
    src.tags,
    src.targetingtype, 
    src.file_name,
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
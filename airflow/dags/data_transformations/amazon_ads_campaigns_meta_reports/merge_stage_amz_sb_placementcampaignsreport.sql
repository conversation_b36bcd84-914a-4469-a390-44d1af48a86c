CREATE TABLE IF NOT EXISTS $stage_db.merge_amz_sb_placementcampaignsreport AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    
    FROM $stage_db.dedupe_amz_sb_placementcampaignsreport
    
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_amz_sb_placementcampaignsreport AS tgt
USING
    
    $stage_db.dedupe_amz_sb_placementcampaignsreport AS src
    
    ON 1 = 1
    AND src.pk = tgt.pk 
WHEN MATCHED
    AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    
    tgt.pk = src.pk,
    tgt.requesttime = src.requesttime,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.accountid = src.accountid,
    tgt.accountname = src.accountname,
    tgt.attributedconversions14d = src.attributedconversions14d,
    tgt.attributedconversions14dsamesku = src.attributedconversions14dsamesku,
    tgt.attributeddetailpageviewsclicks14d = src.attributeddetailpageviewsclicks14d,
    tgt.attributedorderratenewtobrand14d = src.attributedorderratenewtobrand14d,
    tgt.attributedordersnewtobrand14d = src.attributedordersnewtobrand14d,
    tgt.attributedordersnewtobrandpercentage14d = src.attributedordersnewtobrandpercentage14d,
    tgt.attributedsales14d = src.attributedsales14d,
    tgt.attributedsales14dsamesku = src.attributedsales14dsamesku,
    tgt.attributedsalesnewtobrand14d = src.attributedsalesnewtobrand14d,
    tgt.attributedsalesnewtobrandpercentage14d = src.attributedsalesnewtobrandpercentage14d,
    tgt.attributedunitsorderednewtobrand14d = src.attributedunitsorderednewtobrand14d,
    tgt.attributedunitsorderednewtobrandpercentage14d = src.attributedunitsorderednewtobrandpercentage14d,
    tgt.campaignbudgettype = src.campaignbudgettype,
    tgt.campaignid = src.campaignid,
    tgt.campaignname = src.campaignname,
    tgt.campaignstatus = src.campaignstatus,
    tgt.clicks = src.clicks,
    tgt.cost = src.cost,
    tgt.countryname = src.countryname,
    tgt.dpv14d = src.dpv14d,
    tgt.impressions = src.impressions,
    tgt.placement = src.placement,
    tgt.profileid = src.profileid,
    tgt.reportdate = src.reportdate,
    tgt.unitssold14d = src.unitssold14d,
    tgt.applicableBudgetRuleId = src.applicableBudgetRuleId,
    tgt.applicableBudgetRuleName = src.applicableBudgetRuleName,
    tgt.campaignRuleBasedBudget = src.campaignRuleBasedBudget,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    
    pk,
    requesttime,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    accountid,
    accountname,
    attributedconversions14d,
    attributedconversions14dsamesku,
    attributeddetailpageviewsclicks14d,
    attributedorderratenewtobrand14d,
    attributedordersnewtobrand14d,
    attributedordersnewtobrandpercentage14d,
    attributedsales14d,
    attributedsales14dsamesku,
    attributedsalesnewtobrand14d,
    attributedsalesnewtobrandpercentage14d,
    attributedunitsorderednewtobrand14d,
    attributedunitsorderednewtobrandpercentage14d,
    campaignbudgettype,
    campaignid,
    campaignname,
    campaignstatus,
    clicks,
    cost,
    countryname,
    dpv14d,
    impressions,
    placement,
    profileid,
    reportdate,
    unitssold14d,
    applicableBudgetRuleId,
    applicableBudgetRuleName,
    campaignRuleBasedBudget,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
     
    src.pk,
    src.requesttime, 
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.accountid, 
    src.accountname, 
    src.attributedconversions14d, 
    src.attributedconversions14dsamesku, 
    src.attributeddetailpageviewsclicks14d, 
    src.attributedorderratenewtobrand14d, 
    src.attributedordersnewtobrand14d, 
    src.attributedordersnewtobrandpercentage14d, 
    src.attributedsales14d, 
    src.attributedsales14dsamesku, 
    src.attributedsalesnewtobrand14d, 
    src.attributedsalesnewtobrandpercentage14d, 
    src.attributedunitsorderednewtobrand14d, 
    src.attributedunitsorderednewtobrandpercentage14d, 
    src.campaignbudgettype, 
    src.campaignid, 
    src.campaignname, 
    src.campaignstatus, 
    src.clicks, 
    src.cost, 
    src.countryname, 
    src.dpv14d, 
    src.impressions, 
    src.placement, 
    src.profileid, 
    src.reportdate, 
    src.unitssold14d,
    src.applicableBudgetRuleId,
    src.applicableBudgetRuleName,
    src.campaignRuleBasedBudget,
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amz_sp_campaign AS (
WITH flat_campaign AS (
    SELECT
        budget_data.value:budgetType::VARCHAR AS budget_type
        , budget_data.value:budget::VARCHAR AS budget_value
        , budget_data.value:effectiveBudget::VARCHAR AS effective_budget
        , *
    FROM $raw_db.raw_amz_sp_campaign,
    LATERAL FLATTEN(INPUT => budget, outer => TRUE) AS budget_data
)
    SELECT
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(fetchdate AS VARCHAR), ''), '-',
        COALESCE(CAST(accountid AS VARCHAR), ''), '-',
        COALESCE(CAST(campaignid AS VARCHAR), '')
        )) AS pk,
        requesttime,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountid,
        accountname,
        bidding,
        budget,
        budget_type,
        budget_value,
        effective_budget,
        campaignid,
        campaigntype,
        countryname,
        dailybudget,
        dynamicbidding,
        enddate,
        fetchdate,
        name,
        portfolioid,
        premiumbidadjustment,
        profileid,
        startdate,
        state,
        tags,
        targetingtype,
        file_name,
        etl_batch_run_time
    FROM flat_campaign
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY fetchdate, accountid, campaignid
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);

CREATE TABLE IF NOT EXISTS $stage_db.merge_amz_sd_bidrecommendations AS
SELECT
     *
   , '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc
   , '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
FROM $stage_db.dedupe_amz_sd_bidrecommendations
WHERE 1 = 0;


MERGE INTO
    $stage_db.merge_amz_sd_bidrecommendations AS tgt
USING
    $stage_db.dedupe_amz_sd_bidrecommendations AS src
    ON src.pk = tgt.pk
WHEN MATCHED THEN
UPDATE SET
    tgt.request_time = src.request_time
    , tgt.account_id = src.account_id
    , tgt.account_name = src.account_name
    , tgt.bid_optimization = src.bid_optimization
    , tgt.bid_recommendations = src.bid_recommendations
    , tgt.category = src.category
    , tgt.cost_type = src.cost_type
    , tgt.country_name = src.country_name
    , tgt.fetch_date = src.fetch_date
    , tgt.name = src.name
    , tgt.profile_id = src.profile_id
    , tgt.targeting_clauses = src.targeting_clauses
    , tgt._daton_batch_id = src._daton_batch_id
    , tgt._daton_batch_runtime = src._daton_batch_runtime
    , tgt._daton_user_id = src._daton_user_id
    , tgt.file_name = src.file_name
    , tgt.etl_batch_run_time = src.etl_batch_run_time
    , tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk
    , request_time
    , account_id
    , account_name
    , bid_optimization
    , bid_recommendations
    , category
    , cost_type
    , country_name
    , fetch_date
    , name
    , profile_id
    , targeting_clauses
    , _daton_batch_id
    , _daton_batch_runtime
    , _daton_user_id
    , file_name
    , etl_batch_run_time
    , record_created_timestamp_utc
    , record_updated_timestamp_utc
)
VALUES (
    src.pk
    , src.request_time
    , src.account_id
    , src.account_name
    , src.bid_optimization
    , src.bid_recommendations
    , src.category
    , src.cost_type
    , src.country_name
    , src.fetch_date
    , src.name
    , src.profile_id
    , src.targeting_clauses
    , src._daton_batch_id
    , src._daton_batch_runtime
    , src._daton_user_id
    , src.file_name
    , src.etl_batch_run_time
    , SYSDATE()
    , SYSDATE()
);
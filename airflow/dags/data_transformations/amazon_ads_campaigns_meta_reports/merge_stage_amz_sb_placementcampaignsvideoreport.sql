CREATE TABLE IF NOT EXISTS $stage_db.merge_amz_sb_placementcampaignsvideoreport AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    
    FROM $stage_db.dedupe_amz_sb_placementcampaignsvideoreport
    
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_amz_sb_placementcampaignsvideoreport AS tgt
USING
    
    $stage_db.dedupe_amz_sb_placementcampaignsvideoreport AS src
    
    ON 1 = 1
    AND src.pk = tgt.pk 
WHEN MATCHED
    AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    
    tgt.pk = src.pk,
    tgt.requesttime = src.requesttime,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.accountid = src.accountid,
    tgt.accountname = src.accountname,
    tgt.attributedconversions14d = src.attributedconversions14d,
    tgt.attributedconversions14dsamesku = src.attributedconversions14dsamesku,
    tgt.attributedDetailPageViewsClicks14d = src.attributedDetailPageViewsClicks14d,
    tgt.attributedOrderRateNewToBrand14d = src.attributedOrderRateNewToBrand14d,
    tgt.attributedOrdersNewToBrand14d = src.attributedOrdersNewToBrand14d,
    tgt.attributedOrdersNewToBrandPercentage14d = src.attributedOrdersNewToBrandPercentage14d,
    tgt.attributedsales14d = src.attributedsales14d,
    tgt.attributedsales14dsamesku = src.attributedsales14dsamesku,
    tgt.attributedSalesNewToBrand14d = src.attributedSalesNewToBrand14d,
    tgt.attributedSalesNewToBrandPercentage14d = src.attributedSalesNewToBrandPercentage14d,
    tgt.attributedUnitsOrderedNewToBrand14d = src.attributedUnitsOrderedNewToBrand14d,
    tgt.attributedUnitsOrderedNewToBrandPercentage14d = src.attributedUnitsOrderedNewToBrandPercentage14d,
    tgt.campaignbudget = src.campaignbudget,
    tgt.campaignbudgettype = src.campaignbudgettype,
    tgt.campaignid = src.campaignid,
    tgt.campaignname = src.campaignname,
    tgt.campaignstatus = src.campaignstatus,
    tgt.clicks = src.clicks,
    tgt.cost = src.cost,
    tgt.countryname = src.countryname,
    tgt.dpv14d = src.dpv14d,
    tgt.impressions = src.impressions,
    tgt.placement = src.placement,
    tgt.profileid = src.profileid,
    tgt.reportdate = src.reportdate,
    tgt.vctr = src.vctr,
    tgt.video5SecondViewRate = src.video5SecondViewRate,
    tgt.video5SecondViews = src.video5SecondViews,
    tgt.videoCompleteViews = src.videoCompleteViews,
    tgt.videoFirstQuartileViews = src.videoFirstQuartileViews,
    tgt.videoMidpointViews = src.videoMidpointViews,
    tgt.videoThirdQuartileViews = src.videoThirdQuartileViews,
    tgt.videoUnmutes = src.videoUnmutes,
    tgt.viewableImpressions = src.viewableImpressions,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    requesttime,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    accountid,
    accountname,
    attributedconversions14d,
    attributedconversions14dsamesku,
    attributedDetailPageViewsClicks14d,
    attributedOrderRateNewToBrand14d,
    attributedOrdersNewToBrand14d,
    attributedOrdersNewToBrandPercentage14d,
    attributedsales14d,
    attributedsales14dsamesku,
    attributedSalesNewToBrand14d,
    attributedSalesNewToBrandPercentage14d,
    attributedUnitsOrderedNewToBrand14d,
    attributedUnitsOrderedNewToBrandPercentage14d,
    campaignbudget,
    campaignbudgettype,
    campaignid,
    campaignname,
    campaignstatus,
    clicks,
    cost,
    countryname,
    dpv14d,
    impressions,
    placement,
    profileid,
    reportdate,
    vctr,
    video5SecondViewRate,
    video5SecondViews,
    videoCompleteViews,
    videoFirstQuartileViews,
    videoMidpointViews,
    videoThirdQuartileViews,
    videoUnmutes,
    viewableImpressions,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src.requesttime,
    src._daton_batch_id,
    src._daton_batch_runtime,
    src._daton_user_id,
    src.accountid,
    src.accountname,
    src.attributedconversions14d,
    src.attributedconversions14dsamesku,
    src.attributedDetailPageViewsClicks14d,
    src.attributedOrderRateNewToBrand14d,
    src.attributedOrdersNewToBrand14d,
    src.attributedOrdersNewToBrandPercentage14d,
    src.attributedsales14d,
    src.attributedsales14dsamesku,
    src.attributedSalesNewToBrand14d,
    src.attributedSalesNewToBrandPercentage14d,
    src.attributedUnitsOrderedNewToBrand14d,
    src.attributedUnitsOrderedNewToBrandPercentage14d,
    src.campaignbudget,
    src.campaignbudgettype,
    src.campaignid,
    src.campaignname,
    src.campaignstatus,
    src.clicks,
    src.cost,
    src.countryname,
    src.dpv14d,
    src.impressions,
    src.placement,
    src.profileid,
    src.reportdate,
    src.vctr,
    src.video5SecondViewRate,
    src.video5SecondViews,
    src.videoCompleteViews,
    src.videoFirstQuartileViews,
    src.videoMidpointViews,
    src.videoThirdQuartileViews,
    src.videoUnmutes,
    src.viewableImpressions,
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);
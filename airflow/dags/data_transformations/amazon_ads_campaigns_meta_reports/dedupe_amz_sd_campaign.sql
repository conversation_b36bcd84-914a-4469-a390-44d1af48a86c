
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amz_sd_campaign AS (
    SELECT
        
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(fetchdate AS VARCHAR), ''), '-',
        COALESCE(CAST(accountid AS VARCHAR), ''), '-',
        COALESCE(CAST(campaignid AS VARCHAR), '')
        )) AS pk,
        
        requesttime,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountid,
        accountname,
        budget,
        budgettype,
        campaignid,
        countryname,
        enddate,
        fetchdate,
        name,
        portfolioid,
        profileid,
        startdate,
        state,
        tactic,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_amz_sd_campaign
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY fetchdate, accountid, campaignid
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);

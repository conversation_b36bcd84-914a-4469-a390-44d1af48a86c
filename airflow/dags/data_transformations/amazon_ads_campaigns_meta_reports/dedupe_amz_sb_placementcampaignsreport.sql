
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amz_sb_placementcampaignsreport AS (
    SELECT
        
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(reportdate AS VARCHAR), ''), '-',
        COALESCE(CAST(accountid AS VARCHAR), ''), '-',
        COALESCE(CAST(campaignid AS VARCHAR), ''), '-',
        COALESCE(CAST(placement AS VARCHAR), '')
        )) AS pk,
        
        requesttime,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountid,
        accountname,
        attributedconversions14d,
        attributedconversions14dsamesku,
        attributeddetailpageviewsclicks14d,
        attributedorderratenewtobrand14d,
        attributedordersnewtobrand14d,
        attributedordersnewtobrandpercentage14d,
        attributedsales14d,
        attributedsales14dsamesku,
        attributedsalesnewtobrand14d,
        attributedsalesnewtobrandpercentage14d,
        attributedunitsorderednewtobrand14d,
        attributedunitsorderednewtobrandpercentage14d,
        campaignbudgettype,
        campaignid,
        campaignname,
        campaignstatus,
        clicks,
        cost,
        countryname,
        dpv14d,
        impressions,
        placement,
        profileid,
        reportdate,
        unitssold14d,
        applicableBudgetRuleId,
        applicableBudgetRuleName,
        campaignRuleBasedBudget,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_amz_sb_placementcampaignsreport
    WHERE 1=1 AND placement IS NOT NULL 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY reportdate, accountid, campaignid, placement
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);

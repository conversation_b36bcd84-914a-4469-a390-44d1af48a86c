
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amz_portfolio AS (
    SELECT
        
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(fetchdate AS VARCHAR), ''), '-',
        COALESCE(CAST(accountid AS VARCHAR), ''), '-',
        COALESCE(CAST(portfolioid AS VARCHAR), '')
        )) AS pk,
        
        requesttime,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountid,
        accountname,
        budget,
        countryname,
        fetchdate,
        inbudget,
        name,
        portfolioid,
        profileid,
        state,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_amz_portfolio
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY fetchdate, accountid, portfolioid
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);

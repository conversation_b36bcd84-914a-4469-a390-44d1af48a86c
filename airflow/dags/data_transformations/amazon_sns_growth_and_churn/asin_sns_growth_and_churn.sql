CREATE OR REPLACE TABLE $curated_db.FACT_AMAZON_ASIN_SNS_GROWTH_AND_CHURN AS (
WITH Q1 AS (SELECT A."buyer_email", A."order_id", A."sku", CONVERT_TIMEZONE('UTC', 'America/Los_Angeles', A."purchase_date_utc" )::DATE AS "purchase_date", B."asin", B."brand_code",
			CASE WHEN (B."promotion_ids" ILIKE '%subscribe%' OR B."promotion_ids" ILIKE '%sns%') THEN 'SNS' ELSE 'REGULAR' END AS "order_type", SUM(B."quantity") AS "quantity_shipped",
			SUM(B."item_price") AS "item_price"
			FROM DWH.PROD.FACT_ALL_SHIPMENTS AS A
			INNER JOIN DWH.PROD.FACT_AMAZON_ORDERS AS B
			ON A."order_id" = B."amazon_order_id" and A."sku"=B."sku"
            WHERE A."country_code" = 'US'
            AND B."order_status" != 'Cancelled' AND A."buyer_email" IS NOT NULL AND A."buyer_email" != '--'
            AND TRIM(A."buyer_email") != '' AND LTRIM(A."buyer_email") != '' AND RTRIM(A."buyer_email") != ''
            AND CONVERT_TIMEZONE('UTC', 'America/Los_Angeles', A."purchase_date_utc")::DATE BETWEEN DATEADD(year, -2, CURRENT_DATE()) AND CURRENT_DATE()
            GROUP BY 1,2,3,4,5,6,7),
Q2 AS (
-- Calculate the first_product_purchase_date for each buyer_email and asin
			SELECT
            "buyer_email",
            "asin", "sku",
            MIN("purchase_date") AS "first_product_purchase_date"
            FROM Q1
            WHERE "order_type" != 'SNS'
            GROUP BY 1,2,3
                    ),
Q3 AS (
-- Calculate the first_sns_product_purchase_date for each buyer_email and asin
            SELECT
            "buyer_email",
            "asin",
            "sku",
            MIN("purchase_date") AS "first_sns_product_purchase_date",
            MAX("purchase_date") AS "last_sns_product_purchase_date"
            FROM Q1
            WHERE "order_type" = 'SNS'
            GROUP BY 1,2,3
                    ),
FINAL AS (
            SELECT
                        Q1.*,
                        Q2."first_product_purchase_date",
                        Q3."first_sns_product_purchase_date",
                        Q3."last_sns_product_purchase_date"
                    FROM Q1
                    LEFT JOIN Q2 ON Q1."buyer_email" = Q2."buyer_email" AND Q1."asin" = Q2."asin" AND Q1."sku" = Q2."sku"
                    LEFT JOIN Q3 ON Q1."buyer_email" = Q3."buyer_email" AND Q1."asin" = Q3."asin" AND Q1."sku"= Q3."sku"
                    ),
customer_last_asin_purchase AS (
		    SELECT
		        "buyer_email", "brand_code", "asin", "sku", "order_type", MAX("purchase_date") AS "last_product_purchase_date"
		    FROM
		        FINAL
		    WHERE "order_type" != 'SNS'
		    GROUP BY
		        1,2,3,4,5
),
customer_last_brand_purchase AS (
    SELECT
        "buyer_email", "brand_code", "order_type",
        MAX("purchase_date") AS "last_brand_purchase_date"
    FROM
        FINAL
    WHERE "order_type" != 'SNS'
    GROUP BY
        1,2,3
),
customer_last_asin_purchase_sns AS (
    SELECT
        "buyer_email", "brand_code", "asin", "sku", "order_type",
        MAX("purchase_date") AS "last_product_purchase_date_sns"
    FROM
        FINAL
    WHERE "order_type" = 'SNS'
    GROUP BY 1,2,3,4,5),
FINAL_ADDING_LATEST_PURCHASE AS (
SELECT FINAL.*, asin_lp."last_product_purchase_date", brand_lp."last_brand_purchase_date", asin_sns_lp."last_product_purchase_date_sns"
FROM FINAL
LEFT JOIN customer_last_asin_purchase as asin_lp
ON FINAL."buyer_email"=asin_lp."buyer_email" AND FINAL."asin" = asin_lp."asin" AND FINAL."sku" = asin_lp."sku" AND FINAL."brand_code" = asin_lp."brand_code" AND FINAL."order_type" = asin_lp."order_type"
LEFT JOIN customer_last_brand_purchase as brand_lp
ON FINAL."buyer_email"=brand_lp."buyer_email" AND FINAL."brand_code" = brand_lp."brand_code" AND FINAL."order_type" = brand_lp."order_type"
LEFT JOIN customer_last_asin_purchase_sns as asin_sns_lp
ON FINAL."buyer_email"=asin_sns_lp."buyer_email" AND FINAL."asin" = asin_sns_lp."asin" AND FINAL."sku" = asin_sns_lp."sku" AND FINAL."brand_code" = asin_sns_lp."brand_code" AND FINAL."order_type" = asin_sns_lp."order_type"
)
SELECT A.snapshot_date, B."brand_code", A."ASIN", A."SKU", A.ACTIVE_SUBSCRIPTIONS, B.new_sns_subscriber
FROM
(
SELECT "ASIN", "SKU", snapshot_date, ACTIVE_SUBSCRIPTIONS
FROM  dwh.prod.sns_forecast_report
WHERE snapshot_date::DATE BETWEEN DATEADD(year, -2, CURRENT_DATE()) AND CURRENT_DATE() AND country = 'US' AND  MARKETPLACE_NAME = 'US'
) AS A
INNER JOIN
(
SELECT "brand_code", "asin", "sku", "purchase_date", COUNT(DISTINCT "buyer_email") AS new_sns_subscriber
FROM FINAL_ADDING_LATEST_PURCHASE
WHERE ("order_type" = 'SNS' AND "purchase_date" = "first_sns_product_purchase_date")
GROUP BY 1,2,3,4
) AS B
ON A.snapshot_date = B."purchase_date" AND A."ASIN" = B."asin" AND A."SKU" = B."sku");
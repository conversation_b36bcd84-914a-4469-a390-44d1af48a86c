CREATE OR REPLACE TRANSIENT TABLE $stage_db.stg_shopify_sales_traffic_report AS
    SELECT
    start_date,
    end_date,
    brand_code,
    marketplace,
    product_id,
    max(product_title) as product_title,
    max(product_type) as product_type,
    sum(cart_sessions) as cart_sessions,
    sum(view_sessions) as view_sessions,
    sum(purchase_sessions) as purchase_sessions,
    max(checkout_purchase_sessions) as checkout_purchase_sessions,
    sum(checkout_sessions) as checkout_sessions,
    sum(discounts) as discounts,
    max(product_price) as product_price,
    sum(gross_sales) as gross_sales,
    sum(net_sales) as net_sales,
    sum(net_product_quantity) as net_product_quantity,
    sum(ordered_product_quantity) as ordered_product_quantity,
    max(_daton_batch_runtime) as _daton_batch_runtime
    from $stage_db.dedupe_shopify_product_session_data
    GROUP BY 1,2,3,4,5;
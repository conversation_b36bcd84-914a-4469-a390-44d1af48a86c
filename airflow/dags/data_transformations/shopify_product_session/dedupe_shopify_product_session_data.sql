CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_shopify_product_session_data AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(startdate AS VARCHAR), ''), '-',
            COALESCE(CAST(product_id AS VARCHAR), ''), '-',
            COALESCE(CAST(enddate AS VARCHAR), '')
            )) AS pk,
        split_part(file_name,'/',6) as brand_code,
        'SHOPIFY' AS marketplace,
        cart_sessions,
        checkout_purchase_sessions,
        checkout_sessions,
        discounts,
        endDate::date as end_date,
        gross_sales,
        net_product_quantity,
        net_sales,
        ordered_product_quantity,
        product_id,
        product_price,
        product_price_after_discounts,
        product_title,
        product_type,
        purchase_sessions,
        quantity_added_to_cart,
        quantity_purchased,
        returned_product_quantity,
        returns,
        startDate::date as start_date,
        taxes,
        view_cart_checkout_purchase_sessions,
        view_cart_checkout_sessions,
        view_cart_sessions,
        view_purchase_sessions,
        view_sessions,
        file_name,
        etl_batch_run_time,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        
    FROM $raw_db.raw_shopify_product_session_data
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);
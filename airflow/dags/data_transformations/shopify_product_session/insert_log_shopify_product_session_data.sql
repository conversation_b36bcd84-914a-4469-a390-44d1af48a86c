CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_shopify_product_session_data AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_shopify_product_session_data
    WHERE 1 = 0;

INSERT INTO $raw_db.log_shopify_product_session_data (
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    cart_sessions,
    checkout_purchase_sessions,
    checkout_sessions,
    discounts,
    endDate,
    gross_sales,
    net_product_quantity,
    net_sales,
    ordered_product_quantity,
    product_id,
    product_price,
    product_price_after_discounts,
    product_title,
    product_type,
    purchase_sessions,
    quantity_added_to_cart,
    quantity_purchased,
    returned_product_quantity,
    returns,
    startDate,
    taxes,
    view_cart_checkout_purchase_sessions,
    view_cart_checkout_sessions,
    view_cart_sessions,
    view_purchase_sessions,
    view_sessions,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        cart_sessions,
        checkout_purchase_sessions,
        checkout_sessions,
        discounts,
        endDate,
        gross_sales,
        net_product_quantity,
        net_sales,
        ordered_product_quantity,
        product_id,
        product_price,
        product_price_after_discounts,
        product_title,
        product_type,
        purchase_sessions,
        quantity_added_to_cart,
        quantity_purchased,
        returned_product_quantity,
        returns,
        startDate,
        taxes,
        view_cart_checkout_purchase_sessions,
        view_cart_checkout_sessions,
        view_cart_sessions,
        view_purchase_sessions,
        view_sessions,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_shopify_product_session_data;
CREATE TABLE IF NOT EXISTS $curated_db.fact_shopify_product_session_data AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.stg_shopify_sales_traffic_report
    WHERE 1 = 0;

MERGE INTO
    $curated_db.fact_shopify_product_session_data AS tgt
USING
    $stage_db.stg_shopify_sales_traffic_report AS src
    
        ON 1 = 1
       AND src.start_date = tgt.start_date
       and src.end_date = tgt.end_date
       AND src.product_id = tgt.product_id
       
WHEN MATCHED AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    tgt.start_date = src.start_date,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt.end_date = src.end_date,
    tgt.product_id = src.product_id,
    tgt.brand_code = src.brand_code,
    tgt.marketplace = src.marketplace,
    tgt.product_title = src.product_title,
    tgt.cart_sessions = src.cart_sessions,
    tgt.view_sessions = src.view_sessions,
    tgt.purchase_sessions = src.purchase_sessions,
    tgt.checkout_purchase_sessions = src.checkout_purchase_sessions,
    tgt.checkout_sessions = src.checkout_sessions,
    tgt.discounts = src.discounts,
    tgt.product_price = src.product_price,
    tgt.gross_sales = src.gross_sales,
    tgt.net_sales = src.net_sales,
    tgt.net_product_quantity = src.net_product_quantity,
    tgt.ordered_product_quantity = src.ordered_product_quantity,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    start_date,
    _daton_batch_runtime,
    end_date,
    product_id,
    brand_code,
    marketplace,
    product_title,
    product_type,
    cart_sessions,
    view_sessions,
    purchase_sessions,
    checkout_purchase_sessions,
    checkout_sessions,
    discounts,
    product_price,
    gross_sales,
    net_sales,
    net_product_quantity,
    ordered_product_quantity,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.start_date,
    src._daton_batch_runtime,
    src.end_date,
    src.product_id,
    src.brand_code,
    src.marketplace,
    src.product_title,
    src.product_type,
    src.cart_sessions,
    src.view_sessions,
    src.purchase_sessions,
    src.checkout_purchase_sessions,
    src.checkout_sessions,
    src.discounts,
    src.product_price,
    src.gross_sales,
    src.net_sales,
    src.net_product_quantity,
    src.ordered_product_quantity,
    SYSDATE(),
    SYSDATE()
);
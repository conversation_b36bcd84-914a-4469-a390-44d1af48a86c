CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_amazon_ads_attribution_product AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_amazon_ads_attribution_product
    WHERE 1 = 0;

INSERT INTO $raw_db.log_amazon_ads_attribution_product (
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    accountId,
    accountName,
    adGroupId,
    advertiserName,
    attributedAddToCartClicks14d,
    attributedDetailPageViewsClicks14d,
    attributedNewToBrandPurchases14d,
    attributedNewToBrandSales14d,
    attributedNewToBrandUnitsSold14d,
    attributedPurchases14d,
    attributedSales14d,
    brandHaloAttributedAddToCartClicks14d,
    brandHaloAttributedPurchases14d,
    brandHaloAttributedSales14d,
    brandHaloDetailPageViewsClicks14d,
    brandHaloNewToBrandPurchases14d,
    brandHaloNewToBrandSales14d,
    brandHaloNewToBrandUnitsSold14d,
    brandHaloUnitsSold14d,
    brandName,
    campaignId,
    countryName,
    date,
    marketplace,
    productAsin,
    productCategory,
    productConversionType,
    productGroup,
    productName,
    productSubcategory,
    profileId,
    publisher,
    reportDate,
    unitsSold14d,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountId,
        accountName,
        adGroupId,
        advertiserName,
        attributedAddToCartClicks14d,
        attributedDetailPageViewsClicks14d,
        attributedNewToBrandPurchases14d,
        attributedNewToBrandSales14d,
        attributedNewToBrandUnitsSold14d,
        attributedPurchases14d,
        attributedSales14d,
        brandHaloAttributedAddToCartClicks14d,
        brandHaloAttributedPurchases14d,
        brandHaloAttributedSales14d,
        brandHaloDetailPageViewsClicks14d,
        brandHaloNewToBrandPurchases14d,
        brandHaloNewToBrandSales14d,
        brandHaloNewToBrandUnitsSold14d,
        brandHaloUnitsSold14d,
        brandName,
        campaignId,
        countryName,
        date,
        marketplace,
        productAsin,
        productCategory,
        productConversionType,
        productGroup,
        productName,
        productSubcategory,
        profileId,
        publisher,
        reportDate,
        unitsSold14d,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_amazon_ads_attribution_product;
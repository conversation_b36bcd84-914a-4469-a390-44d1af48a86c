CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amazon_ads_attribution_product AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(reportdate AS VARCHAR), ''), '-',
            COALESCE(CAST(accountid AS VARCHAR), ''), '-',
            COALESCE(CAST(campaignid AS VARCHAR), ''), '-',
            COALESCE(CAST(countryname AS VARCHAR), '')
            )) AS pk,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountId,
        accountName,
        adGroupId,
        advertiserName,
        attributedAddToCartClicks14d,
        attributedDetailPageViewsClicks14d,
        attributedNewToBrandPurchases14d,
        attributedNewToBrandSales14d,
        attributedNewToBrandUnitsSold14d,
        attributedPurchases14d,
        attributedSales14d,
        brandHaloAttributedAddToCartClicks14d,
        brandHaloAttributedPurchases14d,
        brandHaloAttributedSales14d,
        brandHaloDetailPageViewsClicks14d,
        brandHaloNewToBrandPurchases14d,
        brandHaloNewToBrandSales14d,
        brandHaloNewToBrandUnitsSold14d,
        brandHaloUnitsSold14d,
        brandName,
        campaignId,
        countryName,
        date,
        marketplace,
        productAsin,
        productCategory,
        productConversionType,
        productGroup,
        productName,
        productSubcategory,
        profileId,
        publisher,
        reportDate,
        unitsSold14d,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_amazon_ads_attribution_product
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY campaignid
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);
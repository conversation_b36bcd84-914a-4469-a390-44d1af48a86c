CREATE TABLE IF NOT EXISTS $curated_db.fact_amazon_ads_attribution_product AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_amazon_ads_attribution_product
    WHERE 1 = 0;


MERGE INTO
    $curated_db.fact_amazon_ads_attribution_product AS tgt
USING
    $stage_db.dedupe_amazon_ads_attribution_product AS src
        ON 1 = 1
       AND src.campaignid = tgt.campaignid
       AND src.reportdate = tgt.reportdate
WHEN MATCHED AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.accountId = src.accountId,
    tgt.accountName = src.accountName,
    tgt.adGroupId = src.adGroupId,
    tgt.advertiserName = src.advertiserName,
    tgt.attributedAddToCartClicks14d = src.attributedAddToCartClicks14d,
    tgt.attributedDetailPageViewsClicks14d = src.attributedDetailPageViewsClicks14d,
    tgt.attributedNewToBrandPurchases14d = src.attributedNewToBrandPurchases14d,
    tgt.attributedNewToBrandSales14d = src.attributedNewToBrandSales14d,
    tgt.attributedNewToBrandUnitsSold14d = src.attributedNewToBrandUnitsSold14d,
    tgt.attributedPurchases14d = src.attributedPurchases14d,
    tgt.attributedSales14d = src.attributedSales14d,
    tgt.brandHaloAttributedAddToCartClicks14d = src.brandHaloAttributedAddToCartClicks14d,
    tgt.brandHaloAttributedPurchases14d = src.brandHaloAttributedPurchases14d,
    tgt.brandHaloAttributedSales14d = src.brandHaloAttributedSales14d,
    tgt.brandHaloDetailPageViewsClicks14d = src.brandHaloDetailPageViewsClicks14d,
    tgt.brandHaloNewToBrandPurchases14d = src.brandHaloNewToBrandPurchases14d,
    tgt.brandHaloNewToBrandSales14d = src.brandHaloNewToBrandSales14d,
    tgt.brandHaloNewToBrandUnitsSold14d = src.brandHaloNewToBrandUnitsSold14d,
    tgt.brandHaloUnitsSold14d = src.brandHaloUnitsSold14d,
    tgt.brandName = src.brandName,
    tgt.campaignId = src.campaignId,
    tgt.countryName = src.countryName,
    tgt.date = src.date,
    tgt.marketplace = src.marketplace,
    tgt.productAsin = src.productAsin,
    tgt.productCategory = src.productCategory,
    tgt.productConversionType = src.productConversionType,
    tgt.productGroup = src.productGroup,
    tgt.productName = src.productName,
    tgt.productSubcategory = src.productSubcategory,
    tgt.profileId = src.profileId,
    tgt.publisher = src.publisher,
    tgt.reportDate = src.reportDate,
    tgt.unitsSold14d = src.unitsSold14d,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    accountId,
    accountName,
    adGroupId,
    advertiserName,
    attributedAddToCartClicks14d,
    attributedDetailPageViewsClicks14d,
    attributedNewToBrandPurchases14d,
    attributedNewToBrandSales14d,
    attributedNewToBrandUnitsSold14d,
    attributedPurchases14d,
    attributedSales14d,
    brandHaloAttributedAddToCartClicks14d,
    brandHaloAttributedPurchases14d,
    brandHaloAttributedSales14d,
    brandHaloDetailPageViewsClicks14d,
    brandHaloNewToBrandPurchases14d,
    brandHaloNewToBrandSales14d,
    brandHaloNewToBrandUnitsSold14d,
    brandHaloUnitsSold14d,
    brandName,
    campaignId,
    countryName,
    date,
    marketplace,
    productAsin,
    productCategory,
    productConversionType,
    productGroup,
    productName,
    productSubcategory,
    profileId,
    publisher,
    reportDate,
    unitsSold14d,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.accountId, 
    src.accountName, 
    src.adGroupId, 
    src.advertiserName, 
    src.attributedAddToCartClicks14d, 
    src.attributedDetailPageViewsClicks14d, 
    src.attributedNewToBrandPurchases14d, 
    src.attributedNewToBrandSales14d, 
    src.attributedNewToBrandUnitsSold14d, 
    src.attributedPurchases14d, 
    src.attributedSales14d, 
    src.brandHaloAttributedAddToCartClicks14d, 
    src.brandHaloAttributedPurchases14d, 
    src.brandHaloAttributedSales14d, 
    src.brandHaloDetailPageViewsClicks14d, 
    src.brandHaloNewToBrandPurchases14d, 
    src.brandHaloNewToBrandSales14d, 
    src.brandHaloNewToBrandUnitsSold14d, 
    src.brandHaloUnitsSold14d, 
    src.brandName, 
    src.campaignId, 
    src.countryName, 
    src.date, 
    src.marketplace, 
    src.productAsin, 
    src.productCategory, 
    src.productConversionType, 
    src.productGroup, 
    src.productName, 
    src.productSubcategory, 
    src.profileId, 
    src.publisher, 
    src.reportDate, 
    src.unitsSold14d, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
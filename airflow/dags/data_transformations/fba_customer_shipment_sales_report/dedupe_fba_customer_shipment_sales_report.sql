CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_fba_customer_shipment_sales_report AS
WITH orders AS (
    SELECT DISTINCT
        "external_order_id" AS external_order_id
        , "country_code" AS country_code
    FROM DWH.PROD.FACT_ALL_ORDERS
    WHERE "marketplace" = 'AMAZON'
)
SELECT
    --  hash key  --
    MD5(
        CONCAT(
            COALESCE(CAST(sellingPartnerId AS VARCHAR), ''), '-',
            COALESCE(CAST(marketplaceId AS VARCHAR), ''), '-',
            COALESCE(CAST(shipment_date AS VARCHAR), ''), '-',
            COALESCE(CAST(sku AS VARCHAR), ''), '-',
            COALESCE(CAST(fnsku AS VARCHAR), ''), '-',
            COALESCE(CAST(asin AS VARCHAR), ''), '-',
            COALESCE(CAST(amazon_order_id AS VARCHAR), ''), '-',
            COALESCE(CAST(currency AS VARCHAR), '')
        )
    ) AS hash_key
    , reportRequestTime
    , reportStartDate
    , reportEndDate
    , amazon_order_id
    , asin
    , currency
    , fnsku
    , fulfillment_center_id
    , gift_wrap_price
    , item_price_per_unit
    , marketplaceId
    , marketplaceName
    , o.country_code
    , quantity
    , sellingPartnerId
    , ship_city
    , ship_postal_code
    , ship_state
    , shipment_date
    , shipping_price
    , sku
    , _daton_batch_id
    , _daton_batch_runtime
    , _daton_user_id
    , file_name
    , etl_batch_run_time
FROM $raw_db.raw_fba_customer_shipment_sales_report r
LEFT JOIN orders o
    ON o.external_order_id = r.amazon_order_id
QUALIFY RANK() OVER(
    PARTITION BY sellingpartnerid, marketplaceid, shipment_date, sku, fnsku, asin, amazon_order_id, currency
    ORDER BY ReportRequestTime DESC, etl_batch_run_time DESC NULLS LAST) = 1;
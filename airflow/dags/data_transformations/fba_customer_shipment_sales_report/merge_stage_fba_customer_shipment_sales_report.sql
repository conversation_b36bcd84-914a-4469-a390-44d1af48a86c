CREATE TABLE IF NOT EXISTS $stage_db.merge_fba_customer_shipment_sales_report AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_fba_customer_shipment_sales_report
    WHERE 1 = 0;


BEGIN TRANSACTION;

DELETE FROM $stage_db.merge_fba_customer_shipment_sales_report m
USING $stage_db.dedupe_fba_customer_shipment_sales_report d
WHERE m.hash_key = d.hash_key;


INSERT INTO $stage_db.merge_fba_customer_shipment_sales_report (
    hash_key
    , reportRequestTime
    , reportStartDate
    , reportEndDate
    , amazon_order_id
    , asin
    , currency
    , fnsku
    , fulfillment_center_id
    , gift_wrap_price
    , item_price_per_unit
    , marketplaceId
    , marketplaceName
    , country_code
    , quantity
    , sellingPartnerId
    , ship_city
    , ship_postal_code
    , ship_state
    , shipment_date
    , shipping_price
    , sku
    , _daton_batch_id
    , _daton_batch_runtime
    , _daton_user_id
    , file_name
    , etl_batch_run_time
    , record_created_timestamp_utc
    , record_updated_timestamp_utc
)
SELECT
    hash_key
    , ReportRequestTime
    , ReportStartDate
    , ReportEndDate
    , amazon_order_id
    , asin
    , currency
    , fnsku
    , fulfillment_center_id
    , gift_wrap_price
    , item_price_per_unit
    , marketplaceId
    , marketplaceName
    , country_code
    , quantity
    , sellingPartnerId
    , ship_city
    , ship_postal_code
    , ship_state
    , shipment_date
    , shipping_price
    , sku
    , _daton_batch_id
    , _daton_batch_runtime
    , _daton_user_id
    , file_name
    , etl_batch_run_time
    , SYSDATE()
    , SYSDATE()
FROM $stage_db.dedupe_fba_customer_shipment_sales_report;

COMMIT;
CREATE TABLE IF NOT EXISTS $curated_db.fact_fba_customer_shipment_sales_report (
    customer_shipment_date VARCHAR,
    merchant_sku VARCHAR,
    fnsku VARCHAR,
    asin VARCHAR,
    fc VARCHAR,
    marketplace VARCHAR,
    country_code VARCHAR,
    quantity NUMBER,
    amazon_order_id VARCHAR,
    currency VARCHAR,
    item_price_per_unit FLOAT,
    product_amount FLOAT,
    shipping_amount FLOAT,
    gift_amount FLOAT,
    shipment_to_city VARCHAR,
    shipment_to_state VARCHAR,
    shipment_to_postal_code NUMBER,
    seller_id VARCHAR,
	created_by VARCHAR DEFAULT 'DAG: fba_customer_shipment_sales_report',
	updated_by VARCHAR DEFAULT 'DAG: fba_customer_shipment_sales_report',
	record_created_timestamp_utc TIMESTAMP DEFAULT SYSDATE(),
	record_updated_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);


BEGIN TRANSACTION;

TRUNCATE $curated_db.fact_fba_customer_shipment_sales_report;

INSERT INTO $curated_db.fact_fba_customer_shipment_sales_report (
    customer_shipment_date
    , merchant_sku
    , fnsku
    , asin
    , fc
    , marketplace
    , country_code
    , quantity
    , amazon_order_id
    , currency
    , item_price_per_unit
    , product_amount
    , shipping_amount
    , gift_amount
    , shipment_to_city
    , shipment_to_state
    , shipment_to_postal_code
    , seller_id
)
SELECT
    shipment_date
    , sku
    , fnsku
    , asin
    , fulfillment_center_id
    , marketplaceName
    , country_code
    , quantity
    , amazon_order_id
    , currency
    , item_price_per_unit
    , item_price_per_unit * quantity
    , shipping_price
    , gift_wrap_price
    , ship_city
    , ship_state
    , ship_postal_code
    , sellingPartnerId
FROM $stage_db.merge_fba_customer_shipment_sales_report;

COMMIT;
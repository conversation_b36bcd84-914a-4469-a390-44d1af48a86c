---
test_id: "compare_customer_shipment_sales_vs_fact_all_shipment"
enabled: true
query: |
  WITH cte1 AS (
    SELECT
      amazon_order_id
      , asin
      , sku
      , currency
      , sum(quantity) AS qty
      , max(item_price_per_unit) AS price
    FROM dwh_dev.staging.merge_fba_customer_shipment_sales_report
    GROUP BY ALL
  ),
  cte2 AS (
    SELECT
      "external_order_id" AS amazon_order_id
      , "asin" AS asin
      , "sku" AS sku
      , "currency" AS currency
      , sum("quantity_shipped") AS qty
      , max("item_price_per_unit") AS price
    FROM dwh.prod.fact_all_shipments
    GROUP BY ALL
  )
  SELECT CASE WHEN COUNT(*)=1 THEN 1 ELSE 0 END AS "result" 
  FROM cte1
  JOIN cte2
    ON cte1.amazon_order_id = cte2.amazon_order_id
    AND cte1.asin = cte2.asin
    AND cte1.sku = cte2.sku
    AND cte1.currency = cte2.currency
  WHERE cte1.qty != cte2.qty
  LIMIT 1;
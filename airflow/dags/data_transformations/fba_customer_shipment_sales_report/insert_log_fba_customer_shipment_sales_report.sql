CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_fba_customer_shipment_sales_report AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_fba_customer_shipment_sales_report
    WHERE 1 = 0;

INSERT INTO $raw_db.log_fba_customer_shipment_sales_report (
    ReportRequestTime,
    ReportendDate,
    ReportstartDate,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    amazon_order_id,
    asin,
    currency,
    fnsku,
    fulfillment_center_id,
    gift_wrap_price,
    item_price_per_unit,
    marketplaceId,
    marketplaceName,
    quantity,
    sellingPartnerId,
    ship_city,
    ship_postal_code,
    ship_state,
    shipment_date,
    shipping_price,
    sku,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
SELECT
    ReportRequestTime,
    ReportendDate,
    ReportstartDate,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    amazon_order_id,
    asin,
    currency,
    fnsku,
    fulfillment_center_id,
    gift_wrap_price,
    item_price_per_unit,
    marketplaceId,
    marketplaceName,
    quantity,
    sellingPartnerId,
    ship_city,
    ship_postal_code,
    ship_state,
    shipment_date,
    shipping_price,
    sku,
    file_name,
    etl_batch_run_time,
    SYSDATE() AS log_timestamp_utc
FROM $raw_db.raw_fba_customer_shipment_sales_report;
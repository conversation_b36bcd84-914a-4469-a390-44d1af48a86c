/*
De-dupe on settlement_id, seller_id within a batch and pick data for the
latest daton batch time.
*/
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amazon_settlements AS (
    SELECT
        adjustment_id
      , amount
      , amount_description
      , amount_type
      , currency
      , _daton_batch_id AS daton_batch_id
      , _daton_batch_runtime AS daton_batch_runtime
      , _daton_user_id AS daton_user_id
      , deposit_date AS deposit_date_time
      , marketplaceid AS encrypted_marketplace_id
      , etl_batch_run_time
      , file_name
      , fulfillment_id
      , merchant_adjustment_item_id
      , merchant_order_id
      , merchant_order_item_id
      , order_id
      , order_item_code
      , posted_date
      , posted_date_time
      , promotion_id
      , quantity_purchased
      , reportenddate AS report_end_date
      , reportstartdate AS report_start_date
      , marketplace_name
      , UPPER(sellingpartnerid) AS seller_id
      , settlement_end_date
      , COALESCE(TRY_CAST(settlement_id AS INT), settlement_id) AS settlement_id
      , settlement_start_date
      , shipment_id
      , sku
      , total_amount
      , transaction_type
    FROM $raw_db.raw_amazon_settlements
    WHERE settlement_id IS NOT NULL 
      AND amount IS NOT NULL
      AND amount != ''
    -- Using rank instead of row_number, as we have multiple rows (with different amount description) for 
    -- the same settlement_id, seller_id
    QUALIFY RANK() OVER (PARTITION BY settlement_id, seller_id ORDER BY daton_batch_runtime DESC) = 1);

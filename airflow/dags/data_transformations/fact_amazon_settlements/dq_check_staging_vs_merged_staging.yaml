test_id: "check_staging_vs_merge_staging_diff"
enabled: true
query: |
  -- Checks if there is any diff between staging and merged staging (which holds the history of records inserted) for records with
  --      a common settlement_id & seller_id between the two datasets.
  -- If this returns a non-zero dataset, fail the workflow. The difference indicates records are different (for the same
  --      settlement_id, seller_id) between the two datasets.
  -- Also adds a count column (partitioned by settlement_id, seller_id) as the "minus" command does not account for duplicates
  --      For e.g. if dataset1 has two rows and dataset2 has one row, where all three rows are identical, "minus" would return an empty result
  --      indicating no difference in the two datasets, when there is an actual difference in the two.
  WITH unique_staging AS (
      SELECT DISTINCT
          settlement_id
        , seller_id
      FROM $stage_db.stage_amazon_settlements
  )
  , unique_merge_staging AS (
      SELECT DISTINCT
          settlement_id
        , seller_id
      FROM $stage_db.merge_stage_amazon_settlements
      WHERE data_source = 'LIVE'
  )
  , common_ids AS (
      SELECT s.*
      FROM unique_staging AS s
      JOIN unique_merge_staging AS m
          ON s.settlement_id = m.settlement_id
        AND s.seller_id = m.seller_id
  )
  , filtered_staging AS (
      SELECT 
          s.adjustment_id
        , s.amount
        , s.amount_description
        , s.amount_type
        --, s.brand_code --Removing check on column, might cause issues as we join with external orders table to get brand
        --, s.category   --Removing check on column, might cause issues as we join with external gl_mapping table to get brand
        , s.country_code
        , s.currency
        , s.data_source
        --, s.daton_batch_id
        --, s.daton_batch_runtime
        --, s.daton_user_id
        , s.deposit_date_time
        , s.encrypted_marketplace_id
        --, s.file_name
        , s.fulfillment_id
        , s.merchant_adjustment_item_id
        , s.merchant_order_id
        , s.merchant_order_item_id
        --, s.netsuite_gl --Removing check on column, might cause issues as we join with external gl_mapping table to get brand
        --, s.netsuite_id --Removing check on column, might cause issues as we join with external gl_mapping table to get brand
        --, s.netsuite_item_mapping --Removing check on column, might cause issues as we join with external gl_mapping table to get brand
        --, s.netsuite_item_number --Removing check on column, might cause issues as we join with external gl_mapping table to get brand
        , s.order_id
        , s.order_item_code
        , s.posted_date
        , s.posted_date_time
        , s.promotion_id
        , s.quantity_purchased
        --, s.report_end_date
        --, s.report_start_date
        , s.sales_channel
        , s.seller_id
        , s.settlement_end_date
        , s.settlement_id
        , s.settlement_start_date
        , s.shipment_id
        , s.sku
        , s.total_amount
        , s.transaction_type
        , COUNT(1) OVER (PARTITION BY s.settlement_id, s.seller_id) AS cnt
      FROM $stage_db.stage_amazon_settlements AS s
      JOIN common_ids AS i
          ON s.settlement_id = i.settlement_id
        AND s.seller_id = i.seller_id
  )
  , filtered_merge_staging AS (
      SELECT 
          s.adjustment_id
        , s.amount
        , s.amount_description
        , s.amount_type
        --, s.brand_code --Removing check on column, might cause issues as we join with external orders table to get brand
        --, s.category   --Removing check on column, might cause issues as we join with external gl_mapping table to get brand
        , s.country_code
        , s.currency
        , s.data_source
        --, s.daton_batch_id
        --, s.daton_batch_runtime
        --, s.daton_user_id
        , s.deposit_date_time
        , s.encrypted_marketplace_id
        --, s.file_name
        , s.fulfillment_id
        , s.merchant_adjustment_item_id
        , s.merchant_order_id
        , s.merchant_order_item_id
        --, s.netsuite_gl --Removing check on column, might cause issues as we join with external gl_mapping table to get brand
        --, s.netsuite_id --Removing check on column, might cause issues as we join with external gl_mapping table to get brand
        --, s.netsuite_item_mapping --Removing check on column, might cause issues as we join with external gl_mapping table to get brand
        --, s.netsuite_item_number --Removing check on column, might cause issues as we join with external gl_mapping table to get brand
        , s.order_id
        , s.order_item_code
        , s.posted_date
        , s.posted_date_time
        , s.promotion_id
        , s.quantity_purchased
        --, s.report_end_date
        --, s.report_start_date
        , s.sales_channel
        , s.seller_id
        , s.settlement_end_date
        , s.settlement_id
        , s.settlement_start_date
        , s.shipment_id
        , s.sku
        , s.total_amount
        , s.transaction_type
        , COUNT(1) OVER (PARTITION BY s.settlement_id, s.seller_id) AS cnt
      FROM $stage_db.merge_stage_amazon_settlements AS s
      JOIN common_ids AS i
          ON s.settlement_id = i.settlement_id
        AND s.seller_id = i.seller_id
      WHERE s.etl_batch_run_time IS NOT NULL
  )
  , diff AS (
      SELECT *
      FROM filtered_merge_staging
      MINUS
      SELECT *   
      FROM filtered_staging

      UNION

      SELECT *
      FROM filtered_staging
      MINUS
      SELECT *
      FROM filtered_merge_staging
  )
  SELECT CASE WHEN COUNT(1) > 0 THEN 2 ELSE 0 END AS "result" FROM diff;

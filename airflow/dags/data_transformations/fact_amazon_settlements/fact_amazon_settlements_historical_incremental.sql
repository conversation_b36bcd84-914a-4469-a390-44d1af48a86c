/*
Insert the historical incremental records from "merged staging" into the fact table.
Here we use the latest timestamp from fact table to determine which records need to be inserted,
as there is no de-dupe we do for historical.
*/
INSERT INTO $curated_db.fact_amazon_settlements (
    settlement_id
  , seller_id
  , amount_description
  , amount_type
  , brand_code
  , category
  , country_code
  , currency
  , data_source
  , deposit_date_time
  , fulfillment_id
  , posted_date_time
  , netsuite_gl
  , netsuite_id
  , netsuite_item_mapping
  , order_id
  , transaction_type
  , sales_channel
  , sku
  , amount
  , quantity_purchased
  , record_created_timestamp_utc
  , record_updated_timestamp_utc
)
    WITH max_hist_timestamp AS (
        SELECT 
            MAX(record_updated_timestamp_utc) AS max_record_updated_timestamp_utc 
        FROM $curated_db.fact_amazon_settlements
        WHERE data_source = 'HISTORICAL'
    )

    SELECT 
        settlement_id
      , seller_id
      , amount_description
      , amount_type
      , brand_code
      , category
      , country_code
      , currency
      , data_source
      , deposit_date_time
      , fulfillment_id
      , posted_date_time
      , netsuite_gl
      , netsuite_id
      , netsuite_item_mapping
      , order_id
      , transaction_type
      , sales_channel
      , sku
      , SUM(amount) AS amount
      , SUM(quantity_purchased) AS quantity_purchased
      , MAX(record_created_timestamp_utc) AS record_created_timestamp_utc
      , MAX(record_updated_timestamp_utc) AS record_updated_timestamp_utc
    FROM $stage_db.merge_stage_amazon_settlements, max_hist_timestamp
    WHERE data_source = 'HISTORICAL'
      AND record_updated_timestamp_utc > max_record_updated_timestamp_utc
    GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19;

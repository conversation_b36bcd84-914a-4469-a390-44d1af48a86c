/*
Create a merged staging table with legacy & historical data (one-time insert).
Future workflow runs will add to this table (both historical and "live" incremental loads)
before loading to final fact table.
*/

SET sysdt=SYSDATE();

CREATE TABLE IF NOT EXISTS $stage_db.merge_stage_amazon_settlements (
    adjustment_id VARCHAR
  , amount FLOAT
  , amount_description VARCHAR
  , amount_type VARCHAR
  , brand_code VA<PERSON>HAR
  , category VA<PERSON>HAR
  , country_code VARCHAR
  , currency VARCHAR
  , data_source VARCHAR
  , daton_batch_id BIGINT 
  , daton_batch_runtime BIGINT
  , daton_user_id BIGINT
  , deposit_date_time TIMESTAMP
  , encrypted_marketplace_id VARCHAR
  , etl_batch_run_time TIMESTAMP
  , file_name VARCHAR
  , fulfillment_id VARCHAR
  , merchant_adjustment_item_id VARCHAR
  , merchant_order_id VARCHAR
  , merchant_order_item_id VARCHAR
  , netsuite_gl VARCHAR
  , netsuite_id VARCHAR
  , netsuite_item_mapping VARCHAR
  , netsuite_item_number VARCHAR
  , order_id VARCHAR
  , order_item_code BIGINT
  , posted_date DATE
  , posted_date_time TIMESTAMP
  , promotion_id VARCHAR
  , quantity_purchased INT
  , report_end_date TIMES<PERSON>MP
  , report_start_date TIMESTAMP
  , sales_channel VARCHAR
  , seller_id VARCHAR
  , settlement_end_date VARCHAR
  , settlement_id BIGINT
  , settlement_start_date VARCHAR
  , shipment_id VARCHAR
  , sku VARCHAR 
  , total_amount FLOAT
  , transaction_type VARCHAR
  , record_created_timestamp_utc TIMESTAMP
  , record_updated_timestamp_utc TIMESTAMP
) AS

    WITH legacy_deduped AS (
        SELECT
            s."seller_id" AS seller_id
          , s."ReportendDate" AS report_end_date
          , s."ReportstartDate" AS report_start_date
          , s."_daton_batch_id" AS daton_batch_id
          , s."_daton_batch_runtime" AS daton_batch_runtime
          , s."_daton_user_id" AS daton_user_id
          , s."adjustment_id" AS adjustment_id
          , s."amount" AS amount
          , s."amount_description" AS amount_description
          , s."amount_type" AS amount_type
          , s."currency" AS currency
          , (CASE WHEN CHARINDEX ('-', s."deposit_date") > 0
                  THEN TO_TIMESTAMP (REPLACE(s."deposit_date", ' UTC', '.000Z'))
                  ELSE TO_TIMESTAMP (REPLACE(s."deposit_date", 'UTC', ''), 'DD.MM.YYYY HH24:MI:SS')
            END) AS deposit_date_time
          , s."fulfillment_id" AS fulfillment_id
          , s."marketplace_name" AS sales_channel
          , s."merchant_adjustment_item_id" AS merchant_adjustment_item_id
          , s."merchant_order_id" AS merchant_order_id
          , s."merchant_order_item_id" AS merchant_order_item_id
          , s."order_id" AS order_id
          , s."order_item_code" AS order_item_code
          , s."posted_date" AS posted_date
          , (CASE WHEN CHARINDEX('-', s."posted_date_time") > 0 
                  THEN TO_TIMESTAMP (REPLACE(s."posted_date_time", ' UTC', '.000Z'))
                  ELSE TO_TIMESTAMP (REPLACE(s."posted_date_time", 'UTC', ''), 'DD.MM.YYYY HH24:MI:SS')
            END) AS posted_date_time
          , s."promotion_id" AS promotion_id
          , s."quantity_purchased" AS quantity_purchased
          , s."settlement_end_date" AS settlement_end_date
          , s."settlement_id"::INT AS settlement_id
          , s."settlement_start_date" AS settlement_start_date
          , s."shipment_id" AS shipment_id
          , s."sku" AS sku
          , s."total_amount" AS total_amount
          , s."transaction_type" AS transaction_type
          , NULL AS brand
          , NULL AS category
          , NULL AS netsuite_gl
          , 'LIVE' AS data_source
        FROM staging_db.amazon_mws_legacy.flatfilev2settlementreport AS s
        -- Using rank instead of row_number, as we have multiple rows (with different amount_description) for 
        -- the same settlement_id, seller_id
        QUALIFY RANK() OVER (PARTITION BY settlement_id, UPPER(seller_id) ORDER BY s."_daton_batch_runtime" DESC) = 1
    )

    , historical AS (
        SELECT 
            l."seller_id" AS seller_id
          , NULL AS report_end_date
          , NULL AS report_start_date
          , NULL AS daton_batch_id
          , NULL AS daton_batch_runtime
          , NULL AS daton_user_id
          , NULL AS adjustment_id
          , l."amount" AS amount
          , l."amount_description" AS amount_description
          , l."amount_type" AS amount_type
          , l."currency" AS currency
          , NULL AS deposit_date_time
          , l."fulfillment_id" AS fulfillment_id
          , l."marketplace_name" AS sales_channel
          , NULL AS merchant_adjustment_item_id
          , NULL AS merchant_order_id
          , NULL AS merchant_order_item_id
          , l."order_id" AS order_id
          , NULL AS order_item_code
          , TO_DATE(l."posted_date_time") AS posted_date
          , l."posted_date_time" AS posted_date_time
          , NULL AS promotion_id
          , l."quantity_purchased" AS quantity_purchased
          , NULL AS settlement_end_date
          , l."settlement_id"::INT AS settlement_id
          , NULL AS settlement_start_date
          , NULL AS shipment_id
          , l."sku" AS sku
          , NULL AS total_amount
          , l."transaction_type" AS transaction_type
          , l."brand" AS brand
          , l."category" AS category
          , l."netsuite_gl" AS netsuite_gl
          , UPPER(l."data_source") AS data_source
        FROM staging_db.amazon_settlements_v2.settlement_historical_v2 AS l
    )

    , settlements_combined AS (
        SELECT *
        FROM legacy_deduped
        WHERE settlement_id IS NOT NULL
          AND TO_DATE(posted_date_time) < '2022-04-13'
        UNION ALL
        SELECT *
        FROM historical
        WHERE settlement_id IS NOT NULL
    )

    , marketplace_lookup AS (
        SELECT DISTINCT
            LOWER(sales_channel) AS sales_channel
          , UPPER(country_code) AS country_code
        FROM $curated_db.amazon_marketplaces
    )

    , unique_gl_mapping AS (
        SELECT *
        FROM analytics.common.amazon_settlement_gl_mapping_live 
        QUALIFY ROW_NUMBER() OVER (
            PARTITION BY UPPER(transaction_type), UPPER(amount_type), UPPER(amount_description) 
            ORDER BY netsuite_id NULLS LAST, netsuite_gl NULLS LAST, netsuite_item_mapping NULLS LAST) = 1
    )

    , unique_sellers AS (
        SELECT 
            seller_id
          , seller_code AS brand
        FROM $stage_db.stg_seller_info
        QUALIFY ROW_NUMBER() OVER (PARTITION BY UPPER(seller_id) ORDER BY brand NULLS LAST) = 1
    )

    SELECT
        (CASE WHEN s.adjustment_id = '' THEN NULL ELSE s.adjustment_id END) AS adjustment_id
      , CAST(REPLACE(s.amount, ',', '.') AS FLOAT) AS amount
      , s.amount_description
      , s.amount_type
      , (CASE WHEN s.data_source = 'HISTORICAL' THEN s.brand
              WHEN UPPER(us.brand) IN ('DBLY', 'FCTY', 'SSCB') THEN UPPER(b.brand_code)
              ELSE UPPER(us.brand)
        END) AS brand_code
      , (CASE WHEN s.data_source = 'LIVE' THEN ug.category
              ELSE s.category
        END) AS category
      , ml.country_code
      , UPPER(s.currency) AS currency
      , s.data_source
      , s.daton_batch_id
      , s.daton_batch_runtime
      , s.daton_user_id
      , s.deposit_date_time
      , NULL AS encrypted_marketplace_id
      , NULL AS etl_batch_run_time
      , NULL AS file_name
      , (CASE WHEN s.fulfillment_id = '' THEN NULL ELSE s.fulfillment_id END) AS fulfillment_id
      , (CASE WHEN s.merchant_adjustment_item_id = '' THEN NULL ELSE s.merchant_adjustment_item_id END) AS merchant_adjustment_item_id
      , (CASE WHEN s.merchant_order_id = '' THEN NULL ELSE s.merchant_order_id END) AS merchant_order_id
      , (CASE WHEN s.merchant_order_item_id = '' THEN NULL ELSE s.merchant_order_item_id END) AS merchant_order_item_id
      , (CASE WHEN s.data_source = 'LIVE' THEN ug.netsuite_gl ELSE s.netsuite_gl END) AS netsuite_gl
      , ug.netsuite_id
      , ug.netsuite_item_mapping
      , im.netsuite_item_number
      , (CASE WHEN s.order_id = '' THEN NULL ELSE s.order_id END) AS order_id
      , TRY_CAST(s.order_item_code AS INT) AS order_item_code
      , s.posted_date
      , s.posted_date_time
      , (CASE WHEN s.promotion_id = '' THEN NULL ELSE s.promotion_id END) AS promotion_id
      , s.quantity_purchased
      , s.report_end_date
      , s.report_start_date
      , (CASE WHEN s.sales_channel = '' THEN NULL
              WHEN LOWER(s.sales_channel) = 'si ca prod marketplace' THEN 'amazon.ca'
              WHEN LOWER(s.sales_channel) = 'si prod es marketplace' THEN 'amazon.es'
              WHEN LOWER(s.sales_channel) = 'si prod it marketplace' THEN 'amazon.it'
              WHEN LOWER(s.sales_channel) = 'si uk prod marketplace' THEN 'amazon.co.uk'
              ELSE LOWER(s.sales_channel)
        END) AS sales_channel
      , UPPER(s.seller_id) AS seller_id
      , s.settlement_end_date
      , COALESCE(TRY_CAST(s.settlement_id AS INT), s.settlement_id) AS settlement_id
      , s.settlement_start_date
      , (CASE WHEN s.shipment_id = '' THEN NULL ELSE s.shipment_id END) AS shipment_id
      , s.sku
      , TRY_CAST(s.total_amount AS FLOAT) AS total_amount
      , s.transaction_type
      , $sysdt AS record_created_timestamp_utc
      , $sysdt AS record_updated_timestamp_utc
    FROM settlements_combined AS s
    LEFT JOIN unique_gl_mapping AS ug
        ON UPPER(s.transaction_type) = UPPER(ug.transaction_type)
        AND UPPER(s.amount_type) = UPPER(ug.amount_type)
        AND UPPER(s.amount_description) = UPPER(ug.amount_description)
    LEFT JOIN unique_sellers AS us
        ON UPPER(s.seller_id) = UPPER(us.seller_id)
    LEFT JOIN marketplace_lookup AS ml
        ON UPPER(s.sales_channel) = UPPER(ml.sales_channel)
    LEFT JOIN dwh.prod.seller_sku_item_mapping b 
      ON b.seller_id = s.seller_id
      AND b.sku = s.sku 
      AND b.country_code = ml.country_code
    LEFT JOIN dwh.prod.sku_item_mapping AS im
        ON s.sku = im.sku
       AND UPPER(ml.country_code) = UPPER(im.country_code)
       AND UPPER(b.brand_code) = UPPER(im.brand_code);

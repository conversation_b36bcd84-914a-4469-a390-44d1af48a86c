/*
One time insert of historical and legacy data into the fact table from the "merged staging" table.
This should be the in the first run of the workflow, where the "merged staging" table only has 
historical and legacy data loaded. Every future run will add the incremental data into 
the "merged staging" table.
*/
CREATE TABLE IF NOT EXISTS $curated_db.fact_amazon_settlements (
    settlement_id
  , seller_id
  , amount_description
  , amount_type
  , brand_code
  , category
  , country_code
  , currency
  , data_source
  , daton_batch_id
  , daton_batch_runtime
  , daton_user_id
  , deposit_date_time
  , fulfillment_id
  , marketplace
  , netsuite_gl
  , netsuite_id
  , netsuite_item_mapping
  , netsuite_item_number
  , order_id
  , posted_date_time
  , sales_channel
  , sku
  , transaction_type
  , amount
  , quantity_purchased
  , record_created_timestamp_utc
  , record_updated_timestamp_utc
  , created_by
  , updated_by
) AS
    SELECT  
        settlement_id
      , seller_id
      , amount_description
      , amount_type
      , brand_code
      , category
      , country_code
      , currency
      , data_source
      , NULL AS daton_batch_id
      , NULL AS daton_batch_runtime
      , NULL AS daton_user_id
      , deposit_date_time
      , fulfillment_id
      , 'AMAZON' AS marketplace
      , netsuite_gl
      , netsuite_id
      , netsuite_item_mapping
      , netsuite_item_number
      , order_id
      , posted_date_time
      , sales_channel
      , sku
      , transaction_type
      , SUM(amount) AS amount
      , SUM(quantity_purchased) AS quantity_purchased
      , MAX(record_created_timestamp_utc) AS record_created_timestamp_utc -- Merged staging table will have the same timestamp for all records
      , MAX(record_updated_timestamp_utc) AS record_updated_timestamp_utc -- Merged staging table will have the same timestamp for all records
      , 'FACT_AMAZON_SETTLEMENTS' AS created_by
      , 'ORI-1894' AS updated_by
    FROM $stage_db.merge_stage_amazon_settlements
    GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24;

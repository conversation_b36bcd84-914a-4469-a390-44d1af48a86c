/*
Insert the staging data into the merged staging table. We insert
ALL records as this is the backfill. For the incremental runs, we 
only insert records where the settlement_id,seller_id were not seen
before.
*/
SET sysdt=SYSDATE();

INSERT INTO $stage_db.merge_stage_amazon_settlements (
    adjustment_id
  , amount
  , amount_description
  , amount_type
  , brand_code
  , category
  , country_code
  , currency
  , data_source
  , daton_batch_id
  , daton_batch_runtime
  , daton_user_id
  , deposit_date_time
  , encrypted_marketplace_id
  , etl_batch_run_time
  , file_name
  , fulfillment_id
  , merchant_adjustment_item_id
  , merchant_order_id
  , merchant_order_item_id
  , netsuite_gl
  , netsuite_id
  , netsuite_item_mapping
  , netsuite_item_number
  , order_id
  , order_item_code
  , posted_date
  , posted_date_time
  , promotion_id
  , quantity_purchased
  , report_end_date
  , report_start_date
  , sales_channel
  , seller_id
  , settlement_end_date
  , settlement_id
  , settlement_start_date
  , shipment_id
  , sku
  , total_amount
  , transaction_type
  , record_created_timestamp_utc
  , record_updated_timestamp_utc
)   
    SELECT 
        adjustment_id
      , amount
      , amount_description
      , amount_type
      , brand_code
      , category
      , country_code
      , currency
      , data_source
      , daton_batch_id
      , daton_batch_runtime
      , daton_user_id
      , deposit_date_time
      , encrypted_marketplace_id
      , etl_batch_run_time
      , file_name
      , fulfillment_id
      , merchant_adjustment_item_id
      , merchant_order_id
      , merchant_order_item_id
      , netsuite_gl
      , netsuite_id
      , netsuite_item_mapping
      , netsuite_item_number
      , order_id
      , order_item_code
      , posted_date
      , posted_date_time
      , promotion_id
      , quantity_purchased
      , report_end_date
      , report_start_date
      , sales_channel
      , seller_id
      , settlement_end_date
      , settlement_id
      , settlement_start_date
      , shipment_id
      , sku
      , total_amount
      , transaction_type
      , $sysdt
      , $sysdt
    FROM $stage_db.stage_amazon_settlements;

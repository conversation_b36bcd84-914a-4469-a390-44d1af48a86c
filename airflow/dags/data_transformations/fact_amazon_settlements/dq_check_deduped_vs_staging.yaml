test_id: "check_deduped_vs_staging"
enabled: true
query: |
  -- Checks if we have any duplicates introduced in staging.
  WITH deduped_temp AS (
    SELECT 
      (CASE WHEN adjustment_id = '' THEN NULL ELSE adjustment_id END) AS adjustment_id
    , CAST(REPLACE(amount, ',', '.') AS FLOAT) AS amount
    , amount_description
    , amount_type
    , UPPER(currency) AS currency
    , daton_batch_id
    , daton_batch_runtime
    , daton_user_id
    , (CASE WHEN CHARINDEX('-', deposit_date_time) > 0 THEN TO_TIMESTAMP(REPLACE(deposit_date_time, ' UTC', '.000Z'))
            ELSE TO_TIMESTAMP(REPLACE(deposit_date_time, 'UTC', ''), 'DD.MM.YYYY HH24:MI:SS')
      END) AS deposit_date_time
    , encrypted_marketplace_id
    , etl_batch_run_time
    , file_name
    , (CASE WHEN fulfillment_id = '' THEN NULL ELSE fulfillment_id END) AS fulfillment_id
    , (CASE WHEN merchant_adjustment_item_id = '' THEN NULL ELSE merchant_adjustment_item_id END) AS merchant_adjustment_item_id
    , (CASE WHEN merchant_order_id = '' THEN NULL ELSE merchant_order_id END) AS merchant_order_id
    , (CASE WHEN merchant_order_item_id = '' THEN NULL ELSE merchant_order_item_id END) AS merchant_order_item_id
    , (CASE WHEN order_id = '' THEN NULL ELSE order_id END) AS order_id
    , TRY_CAST(order_item_code AS INT) AS order_item_code
    , posted_date
    , (CASE WHEN CHARINDEX('-', posted_date_time) > 0 THEN TO_TIMESTAMP(REPLACE(posted_date_time, ' UTC', '.000Z'))
            ELSE TO_TIMESTAMP(REPLACE(posted_date_time, 'UTC', ''), 'DD.MM.YYYY HH24:MI:SS')
      END) AS posted_date_time
    , (CASE WHEN promotion_id = '' THEN NULL ELSE promotion_id END) AS promotion_id
    , quantity_purchased
    , report_end_date
    , report_start_date
    , (CASE WHEN marketplace_name = '' THEN NULL
            WHEN LOWER(marketplace_name) = 'si ca prod marketplace' THEN 'amazon.ca'
            WHEN LOWER(marketplace_name) = 'si prod es marketplace' THEN 'amazon.es'
            WHEN LOWER(marketplace_name) = 'si prod it marketplace' THEN 'amazon.it'
            WHEN LOWER(marketplace_name) = 'si uk prod marketplace' THEN 'amazon.co.uk'
            ELSE LOWER(marketplace_name)
      END) AS sales_channel
    , seller_id
    , settlement_end_date
    , settlement_id
    , settlement_start_date
    , (CASE WHEN shipment_id = '' THEN NULL ELSE shipment_id   END) AS shipment_id
    , sku
    , TRY_CAST(total_amount AS FLOAT) AS total_amount
    , transaction_type
    FROM $stage_db.dedupe_amazon_settlements
  )
  , deduped AS (
      SELECT
        *
      , MD5(CONCAT(
          COALESCE(adjustment_id::TEXT, 'xx'), '-'
        , COALESCE(amount::TEXT, 'xx'), '-'
        , COALESCE(amount_description::TEXT, 'xx'), '-'
        , COALESCE(amount_type::TEXT, 'xx'), '-'
        , COALESCE(currency::TEXT, 'xx'), '-'
        , COALESCE(daton_batch_id::TEXT, 'xx'), '-'
        , COALESCE(daton_batch_runtime::TEXT, 'xx'), '-'
        , COALESCE(daton_user_id::TEXT, 'xx'), '-'
        , COALESCE(deposit_date_time::TEXT, 'xx'), '-'
        , COALESCE(encrypted_marketplace_id::TEXT, 'xx'), '-'
        , COALESCE(etl_batch_run_time::TEXT, 'xx'), '-'
        , COALESCE(file_name::TEXT, 'xx'), '-'
        , COALESCE(fulfillment_id::TEXT, 'xx'), '-'
        , COALESCE(merchant_adjustment_item_id::TEXT, 'xx'), '-'
        , COALESCE(merchant_order_id::TEXT, 'xx'), '-'
        , COALESCE(merchant_order_item_id::TEXT, 'xx'), '-'
        , COALESCE(order_id::TEXT, 'xx'), '-'
        , COALESCE(order_item_code::TEXT, 'xx'), '-'
        , COALESCE(posted_date_time::TEXT, 'xx'), '-'
        , COALESCE(promotion_id::TEXT, 'xx'), '-'
        , COALESCE(quantity_purchased::TEXT, 'xx'), '-'
        , COALESCE(report_end_date::TEXT, 'xx'), '-'
        , COALESCE(report_start_date::TEXT, 'xx'), '-'
        , COALESCE(sales_channel::TEXT, 'xx'), '-'
        , COALESCE(seller_id::TEXT, 'xx'), '-'
        , COALESCE(settlement_end_date::TEXT, 'xx'), '-'
        , COALESCE(settlement_id::INT, 0), '-'
        , COALESCE(settlement_start_date::TEXT, 'xx'), '-'
        , COALESCE(shipment_id::TEXT, 'xx'), '-'
        , COALESCE(sku::TEXT, 'xx'), '-'
        , COALESCE(total_amount::TEXT, 'xx'), '-'
        , COALESCE(transaction_type::TEXT, 'xx'), '-')) AS pk
      FROM deduped_temp
  )
  , staged AS (
      SELECT
        adjustment_id
      , amount
      , amount_description
      , amount_type
      , currency
      , daton_batch_id
      , daton_batch_runtime
      , daton_user_id
      , deposit_date_time
      , encrypted_marketplace_id
      , etl_batch_run_time
      , file_name
      , fulfillment_id
      , merchant_adjustment_item_id
      , merchant_order_id
      , merchant_order_item_id
      , order_id
      , order_item_code
      , posted_date
      , posted_date_time
      , promotion_id
      , quantity_purchased
      , report_end_date
      , report_start_date
      , sales_channel
      , seller_id
      , settlement_end_date
      , settlement_id
      , settlement_start_date
      , shipment_id
      , sku
      , total_amount
      , transaction_type
      , MD5(CONCAT(
          COALESCE(adjustment_id::TEXT, 'xx'), '-'
        , COALESCE(amount::TEXT, 'xx'), '-'
        , COALESCE(amount_description::TEXT, 'xx'), '-'
        , COALESCE(amount_type::TEXT, 'xx'), '-'
        , COALESCE(currency::TEXT, 'xx'), '-'
        , COALESCE(daton_batch_id::TEXT, 'xx'), '-'
        , COALESCE(daton_batch_runtime::TEXT, 'xx'), '-'
        , COALESCE(daton_user_id::TEXT, 'xx'), '-'
        , COALESCE(deposit_date_time::TEXT, 'xx'), '-'
        , COALESCE(encrypted_marketplace_id::TEXT, 'xx'), '-'
        , COALESCE(etl_batch_run_time::TEXT, 'xx'), '-'
        , COALESCE(file_name::TEXT, 'xx'), '-'
        , COALESCE(fulfillment_id::TEXT, 'xx'), '-'
        , COALESCE(merchant_adjustment_item_id::TEXT, 'xx'), '-'
        , COALESCE(merchant_order_id::TEXT, 'xx'), '-'
        , COALESCE(merchant_order_item_id::TEXT, 'xx'), '-'
        , COALESCE(order_id::TEXT, 'xx'), '-'
        , COALESCE(order_item_code::TEXT, 'xx'), '-'
        , COALESCE(posted_date_time::TEXT, 'xx'), '-'
        , COALESCE(promotion_id::TEXT, 'xx'), '-'
        , COALESCE(quantity_purchased::TEXT, 'xx'), '-'
        , COALESCE(report_end_date::TEXT, 'xx'), '-'
        , COALESCE(report_start_date::TEXT, 'xx'), '-'
        , COALESCE(sales_channel::TEXT, 'xx'), '-'
        , COALESCE(seller_id::TEXT, 'xx'), '-'
        , COALESCE(settlement_end_date::TEXT, 'xx'), '-'
        , COALESCE(settlement_id::TEXT, 'xx'), '-'
        , COALESCE(settlement_start_date::TEXT, 'xx'), '-'
        , COALESCE(shipment_id::TEXT, 'xx'), '-'
        , COALESCE(sku::TEXT, 'xx'), '-'
        , COALESCE(total_amount::TEXT, 'xx'), '-'
        , COALESCE(transaction_type::TEXT, 'xx'), '-')) AS pk
      FROM $stage_db.stage_amazon_settlements
  )
  , deduped_grouped AS (
      SELECT 
        pk
      , COUNT(1) AS cnt_deduped
      FROM deduped
      GROUP BY 1
  )
  , staged_grouped AS (
      SELECT 
        pk
      , COUNT(1) AS cnt_staged
      FROM staged
      GROUP BY 1
  )
  , merged AS (
    SELECT * 
    FROM staged_grouped AS s
    LEFT JOIN deduped_grouped AS d
      ON d.pk = s.pk
    WHERE cnt_staged > cnt_deduped OR cnt_deduped IS NULL
  )
  SELECT CASE WHEN COUNT(1) > 0 THEN 2 ELSE 0 END AS "result" FROM merged;

/*
Insert the merged staged data into the fact table.
We will only insert new records where the settlement_id and seller_id were not seen before.
*/
MERGE INTO $curated_db.fact_amazon_settlements AS tgt
USING (
    SELECT
        settlement_id
      , seller_id
      , amount_description
      , amount_type
      , brand_code
      , category
      , country_code
      , currency
      , data_source
      , daton_batch_id
      , daton_batch_runtime
      , daton_user_id
      , deposit_date_time
      , fulfillment_id
      , netsuite_gl
      , netsuite_id
      , netsuite_item_mapping
      , netsuite_item_number
      , order_id
      , posted_date_time
      , sales_channel
      , sku
      , transaction_type
      , SUM(amount) AS amount
      , SUM(quantity_purchased) AS quantity_purchased
      , MAX(record_created_timestamp_utc) AS record_created_timestamp_utc
      , MAX(record_updated_timestamp_utc) AS record_updated_timestamp_utc
    FROM $stage_db.merge_stage_amazon_settlements
    GROUP BY 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23
) AS src
    ON tgt.settlement_id = src.settlement_id
   AND tgt.seller_id = src.seller_id

WHEN NOT MATCHED THEN 
    INSERT (
        settlement_id
      , seller_id
      , amount_description
      , amount_type
      , brand_code
      , category
      , country_code
      , currency
      , data_source
      , daton_batch_id
      , daton_batch_runtime
      , daton_user_id
      , deposit_date_time
      , fulfillment_id
      , marketplace
      , netsuite_gl
      , netsuite_id
      , netsuite_item_mapping
      , netsuite_item_number
      , order_id
      , posted_date_time
      , sales_channel
      , sku
      , transaction_type
      , amount
      , quantity_purchased
      , record_created_timestamp_utc
      , record_updated_timestamp_utc
      , created_by
      , updated_by
    )
    VALUES (
        src.settlement_id
      , src.seller_id
      , src.amount_description
      , src.amount_type
      , src.brand_code
      , src.category
      , src.country_code
      , src.currency
      , src.data_source
      , src.daton_batch_id
      , src.daton_batch_runtime
      , src.daton_user_id
      , src.deposit_date_time
      , src.fulfillment_id
      , 'AMAZON'
      , src.netsuite_gl
      , src.netsuite_id
      , src.netsuite_item_mapping
      , src.netsuite_item_number
      , src.order_id
      , src.posted_date_time
      , src.sales_channel
      , src.sku
      , src.transaction_type
      , src.amount
      , src.quantity_purchased
      , src.record_created_timestamp_utc
      , src.record_updated_timestamp_utc
      , 'FACT_AMAZON_SETTLEMENTS'
      , 'ORI-1894'
    );

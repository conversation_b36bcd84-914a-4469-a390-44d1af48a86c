/*
Insert the staging data into the merged staging table. We only insert
records where the settlement_id, seller_id were not seen before.
*/
SET sysdt=SYSDATE();

MERGE INTO $stage_db.merge_stage_amazon_settlements AS tgt
USING (
    SELECT 
        adjustment_id
      , amount
      , amount_description
      , amount_type
      , brand_code
      , category
      , country_code
      , currency
      , data_source
      , daton_batch_id
      , daton_batch_runtime
      , daton_user_id
      , deposit_date_time
      , encrypted_marketplace_id
      , etl_batch_run_time
      , file_name
      , fulfillment_id
      , merchant_adjustment_item_id
      , merchant_order_id
      , merchant_order_item_id
      , netsuite_gl
      , netsuite_id
      , netsuite_item_mapping
      , netsuite_item_number
      , order_id
      , order_item_code
      , posted_date
      , posted_date_time
      , promotion_id
      , quantity_purchased
      , report_end_date
      , report_start_date
      , sales_channel
      , seller_id
      , settlement_end_date
      , settlement_id
      , settlement_start_date
      , shipment_id
      , sku
      , total_amount
      , transaction_type
    FROM $stage_db.stage_amazon_settlements
) AS src
    ON tgt.settlement_id = src.settlement_id
   AND tgt.seller_id = src.seller_id

WHEN NOT MATCHED THEN 
    INSERT (
        tgt.adjustment_id
      , tgt.amount
      , tgt.amount_description
      , tgt.amount_type
      , tgt.brand_code
      , tgt.category
      , tgt.country_code
      , tgt.currency
      , tgt.data_source
      , tgt.daton_batch_id
      , tgt.daton_batch_runtime
      , tgt.daton_user_id
      , tgt.deposit_date_time
      , tgt.encrypted_marketplace_id
      , tgt.etl_batch_run_time
      , tgt.file_name
      , tgt.fulfillment_id
      , tgt.merchant_adjustment_item_id
      , tgt.merchant_order_id
      , tgt.merchant_order_item_id
      , tgt.netsuite_gl
      , tgt.netsuite_id
      , tgt.netsuite_item_mapping
      , tgt.netsuite_item_number
      , tgt.order_id
      , tgt.order_item_code
      , tgt.posted_date
      , tgt.posted_date_time
      , tgt.promotion_id
      , tgt.quantity_purchased
      , tgt.report_end_date
      , tgt.report_start_date
      , tgt.sales_channel
      , tgt.seller_id
      , tgt.settlement_end_date
      , tgt.settlement_id
      , tgt.settlement_start_date
      , tgt.shipment_id
      , tgt.sku
      , tgt.total_amount
      , tgt.transaction_type
      , tgt.record_created_timestamp_utc
      , tgt.record_updated_timestamp_utc
    )
    VALUES (
        src.adjustment_id
      , src.amount
      , src.amount_description
      , src.amount_type
      , src.brand_code
      , src.category
      , src.country_code
      , src.currency
      , src.data_source
      , src.daton_batch_id
      , src.daton_batch_runtime
      , src.daton_user_id
      , src.deposit_date_time
      , src.encrypted_marketplace_id
      , src.etl_batch_run_time
      , src.file_name
      , src.fulfillment_id
      , src.merchant_adjustment_item_id
      , src.merchant_order_id
      , src.merchant_order_item_id
      , src.netsuite_gl
      , src.netsuite_id
      , src.netsuite_item_mapping
      , src.netsuite_item_number
      , src.order_id
      , src.order_item_code
      , src.posted_date
      , src.posted_date_time
      , src.promotion_id
      , src.quantity_purchased
      , src.report_end_date
      , src.report_start_date
      , src.sales_channel
      , src.seller_id
      , src.settlement_end_date
      , src.settlement_id
      , src.settlement_start_date
      , src.shipment_id
      , src.sku
      , src.total_amount
      , src.transaction_type
      , $sysdt
      , $sysdt
    );

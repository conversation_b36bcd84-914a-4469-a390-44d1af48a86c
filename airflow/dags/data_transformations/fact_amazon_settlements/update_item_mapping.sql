UPDATE $curated_db.fact_amazon_settlements f  
SET brand_code = s.brand_code
FROM dwh.prod.seller_sku_item_mapping s 
WHERE f.seller_id = s.seller_id
AND f.country_code = s.country_code
AND f.sku = s.sku
AND f.brand_code IS NULL 
AND s.brand_code IS NOT NULL;

UPDATE $curated_db.fact_amazon_settlements f  
SET netsuite_item_number = s.netsuite_item_number
FROM dwh.prod.sku_item_mapping s 
WHERE f.brand_code = s.brand_code
AND f.country_code = s.country_code
AND f.sku = s.sku
AND f.netsuite_item_number IS NULL 
AND s.netsuite_item_number IS NOT NULL;

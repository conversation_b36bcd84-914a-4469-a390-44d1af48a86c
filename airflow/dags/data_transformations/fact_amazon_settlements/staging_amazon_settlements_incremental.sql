/*
Reads the de-dupe data and adds additional columns (country_code, brand_code etc).
This data will then be merged into the "merged staging" table based on new entries.
*/
CREATE OR REPLACE TRANSIENT TABLE $stage_db.stage_amazon_settlements (
    adjustment_id VARCHAR
  , amount FLOAT
  , amount_description VARCHAR
  , amount_type VARCHAR
  , brand_code VARCHAR
  , category VARCHAR
  , country_code VA<PERSON>HAR
  , currency VARCHAR
  , data_source VARCHAR
  , daton_batch_id BIGINT 
  , daton_batch_runtime BIGINT
  , daton_user_id BIGINT
  , deposit_date_time TIMESTAMP
  , encrypted_marketplace_id VARCHAR
  , etl_batch_run_time TIMESTAMP
  , file_name VARCHAR
  , fulfillment_id VARCHAR
  , merchant_adjustment_item_id VARCHAR
  , merchant_order_id VARCHAR
  , merchant_order_item_id VARCHAR
  , netsuite_gl VARCHAR
  , netsuite_id VARCHAR
  , netsuite_item_mapping VARCHAR
  , netsuite_item_number VARCHAR
  , order_id VARCHAR
  , order_item_code BIGINT
  , posted_date DATE
  , posted_date_time TIMESTAMP
  , promotion_id VARCHAR
  , quantity_purchased INT
  , report_end_date TIMESTAMP
  , report_start_date TIMESTAMP
  , sales_channel VARCHAR
  , seller_id VARCHAR
  , settlement_end_date VARCHAR
  , settlement_id BIGINT
  , settlement_start_date VARCHAR
  , shipment_id VARCHAR
  , sku VARCHAR 
  , total_amount FLOAT
  , transaction_type VARCHAR
) AS
    WITH marketplace_lookup AS (
        SELECT DISTINCT
            sales_channel
          , country_code
        FROM $curated_db.amazon_marketplaces
    )
    
    , unique_orders AS (
        SELECT
            o."amazon_order_id" AS amazon_order_id
          , o."sku" AS sku
          , o."derived_seller_id" AS derived_seller_id
          , o."brand_code" AS brand
        FROM $curated_db.fact_amazon_orders AS o
        QUALIFY ROW_NUMBER() OVER (PARTITION BY amazon_order_id, sku, derived_seller_id ORDER BY brand) = 1
    )

    , unique_gl_mapping AS (
        SELECT *
        FROM analytics.common.amazon_settlement_gl_mapping_live 
        QUALIFY ROW_NUMBER() OVER (PARTITION BY UPPER(transaction_type), UPPER(amount_type), UPPER(amount_description) 
                                   ORDER BY netsuite_id NULLS LAST
                                          , netsuite_gl NULLS LAST
                                          , netsuite_item_mapping NULLS LAST) = 1
    )

    , filtered_deduped_settlements AS (
        SELECT 
            (CASE WHEN l.adjustment_id = '' THEN NULL ELSE l.adjustment_id END) AS adjustment_id
          -- Handle cases with ,. sequence like 1,234.56
          , CAST(REVERSE(REGEXP_REPLACE(REVERSE(REPLACE(l.amount, ',', '.')), '\\.','', 1,2)) AS FLOAT) AS amount
          , l.amount_description
          , l.amount_type
          , UPPER(l.currency) AS currency
          , l.daton_batch_id
          , l.daton_batch_runtime
          , l.daton_user_id
          , (CASE WHEN CHARINDEX('-', l.deposit_date_time) > 0 THEN TO_TIMESTAMP(REPLACE(l.deposit_date_time, ' UTC', '.000Z'))
                  ELSE TO_TIMESTAMP(REPLACE(l.deposit_date_time, 'UTC', ''), 'DD.MM.YYYY HH24:MI:SS')
            END) AS deposit_date_time_updated
          , l.encrypted_marketplace_id
          , l.etl_batch_run_time
          , l.file_name
          , (CASE WHEN l.fulfillment_id = '' THEN NULL ELSE l.fulfillment_id END) AS fulfillment_id
          , (CASE WHEN l.merchant_adjustment_item_id = '' THEN NULL ELSE l.merchant_adjustment_item_id
            END) AS merchant_adjustment_item_id
          , (CASE WHEN l.merchant_order_id = '' THEN NULL ELSE l.merchant_order_id END) AS merchant_order_id
          , (CASE WHEN l.merchant_order_item_id = '' THEN NULL
                  ELSE l.merchant_order_item_id
            END) AS merchant_order_item_id
          , (CASE WHEN l.order_id = '' THEN NULL ELSE l.order_id END) AS order_id
          , TRY_CAST(l.order_item_code AS INT) AS order_item_code
          , l.posted_date
          , (CASE WHEN CHARINDEX('-', l.posted_date_time) > 0 THEN TO_TIMESTAMP(REPLACE(l.posted_date_time, ' UTC', '.000Z'))
                  ELSE TO_TIMESTAMP(REPLACE(l.posted_date_time, 'UTC', ''), 'DD.MM.YYYY HH24:MI:SS')
            END) AS posted_date_time_updated
          , (CASE WHEN l.promotion_id = '' THEN NULL ELSE l.promotion_id END) AS promotion_id
          , l.quantity_purchased
          , l.report_end_date
          , l.report_start_date
          , (CASE WHEN l.marketplace_name = '' THEN NULL
                  WHEN LOWER(l.marketplace_name) = 'si ca prod marketplace' THEN 'amazon.ca'
                  WHEN LOWER(l.marketplace_name) = 'si prod es marketplace' THEN 'amazon.es'
                  WHEN LOWER(l.marketplace_name) = 'si prod it marketplace' THEN 'amazon.it'
                  WHEN LOWER(l.marketplace_name) = 'si uk prod marketplace' THEN 'amazon.co.uk'
                  ELSE LOWER(l.marketplace_name)
            END) AS sales_channel
          , l.seller_id
          , l.settlement_end_date
          , l.settlement_id
          , l.settlement_start_date
          , (CASE WHEN l.shipment_id = '' THEN NULL ELSE l.shipment_id   END) AS shipment_id
          , l.sku
          , TRY_CAST(l.total_amount AS FLOAT) AS total_amount
          , l.transaction_type
        FROM $stage_db.dedupe_amazon_settlements AS l
        WHERE TO_DATE(posted_date_time_updated) >= '2022-04-13'
    )
    
    SELECT 
        s.adjustment_id
      , s.amount
      , s.amount_description
      , s.amount_type VARCHAR
      , o.brand AS brand_code
      , ug.category
      , ml.country_code
      , s.currency
      , 'LIVE' AS data_source
      , s.daton_batch_id
      , s.daton_batch_runtime
      , s.daton_user_id
      , s.deposit_date_time_updated AS deposit_date_time
      , s.encrypted_marketplace_id
      , s.etl_batch_run_time
      , s.file_name
      , s.fulfillment_id
      , s.merchant_adjustment_item_id
      , s.merchant_order_id
      , s.merchant_order_item_id
      , ug.netsuite_gl
      , ug.netsuite_id
      , ug.netsuite_item_mapping
      , im.netsuite_item_number
      , s.order_id
      , s.order_item_code
      , s.posted_date
      , s.posted_date_time_updated AS posted_date_time
      , s.promotion_id
      , s.quantity_purchased
      , s.report_end_date
      , s.report_start_date
      , s.sales_channel
      , s.seller_id
      , s.settlement_end_date
      , s.settlement_id
      , s.settlement_start_date
      , s.shipment_id
      , s.sku
      , s.total_amount
      , s.transaction_type
    FROM filtered_deduped_settlements AS s
    LEFT OUTER JOIN unique_gl_mapping AS ug
        ON s.transaction_type = ug.transaction_type 
       AND s.amount_type = ug.amount_type
       AND s.amount_description = ug.amount_description
    LEFT JOIN marketplace_lookup AS ml
        ON UPPER(s.sales_channel) = UPPER(ml.sales_channel)
    LEFT OUTER JOIN unique_orders AS o
        ON s.order_id = o.amazon_order_id
       AND s.sku = o.sku
       AND UPPER(s.seller_id) = UPPER(o.derived_seller_id)
    LEFT OUTER JOIN dwh.prod.sku_item_mapping AS im
        ON s.sku = im.sku
       AND UPPER(o.brand) = UPPER(im.brand_code)
       AND UPPER(ml.country_code) = UPPER(im.country_code);

/*
We do not have a timestamp in historical data to indicate what records were added since we last
look at the historical data. To get around this, every time the DAG runs, we create a snapshot
of the current historical data and compare it with the previous snapshot of historical data.
The delta (current - previous) will be added to the settlements table. We do not delete any
historical data from the settlements table. For adding the delta of historical data, we do the
following
1) Create a primary key (pk) with the key columns for both previous and current historical
2) Find all records where 
    a) pk not seen in previous OR
    b) pk seen in previous but current has more rows than previous. For this, we only add the
        additional number of rows seen. e.g. if previous pk had 10 rows and current pk has 15
        rows, we will add 5 additional rows to settlements table.
TODO: Figure out if we need to dedupe historical data and how to do it.
*/
CREATE OR REPLACE TRANSIENT TABLE $stage_db.stage_amazon_settlements_historical_incremental (
    amount
  , amount_description
  , amount_type
  , brand_code
  , category
  , currency
  , data_source
  , fulfillment_id
  , netsuite_gl
  , order_id
  , posted_date_time
  , quantity_purchased
  , sales_channel
  , seller_id
  , settlement_id
  , sku
  , transaction_type
) AS
    -- This is the latest historical data that is available
    WITH historical_current AS (
        SELECT 
            *
          , MD5(CONCAT(
                COALESCE(h."posted_date_time"::TEXT , ''),  '-'
              , COALESCE(h."seller_id" , ''),  '-'
              , COALESCE(h."brand", ''), '-'
              , COALESCE(h."settlement_id", ''), '-'
              , COALESCE(h."transaction_type", ''), '-'
              , COALESCE(h."order_id", ''), '-'
              , COALESCE(h."category", ''), '-'
              , COALESCE(h."netsuite_gl", ''), '-'
              , COALESCE(h."amount"::TEXT, ''), '-'
              , COALESCE(h."currency", ''), '-'
              , COALESCE(h."marketplace_name", ''), '-'
              , COALESCE(h."fulfillment_id", ''), '-'
              , COALESCE(h."amount_type", ''), '-'
              , COALESCE(h."amount_description", ''), '-'
              , COALESCE(h."sku", ''), '-'
              , COALESCE(h."quantity_purchased"::TEXT, '.'), '-'
              , COALESCE(h."data_source", '.'), '-'
        )) AS pk
        , ROW_NUMBER () OVER (PARTITION BY pk ORDER BY pk) AS row_number
        FROM staging_db.amazon_settlements_v2.settlement_historical_v2 AS h
    )

    , historical_previous AS (
        SELECT 
            *
          , MD5(CONCAT(
                COALESCE(h."posted_date_time"::TEXT , ''),  '-'
              , COALESCE(h."seller_id" , ''),  '-'
              , COALESCE(h."brand", ''), '-'
              , COALESCE(h."settlement_id", ''), '-'
              , COALESCE(h."transaction_type", ''), '-'
              , COALESCE(h."order_id", ''), '-'
              , COALESCE(h."category", ''), '-'
              , COALESCE(h."netsuite_gl", ''), '-'
              , COALESCE(h."amount"::TEXT, ''), '-'
              , COALESCE(h."currency", ''), '-'
              , COALESCE(h."marketplace_name", ''), '-'
              , COALESCE(h."fulfillment_id", ''), '-'
              , COALESCE(h."amount_type", ''), '-'
              , COALESCE(h."amount_description", ''), '-'
              , COALESCE(h."sku", ''), '-'
              , COALESCE(h."quantity_purchased"::TEXT, '.'), '-'
              , COALESCE(h."data_source", '.'), '-'
        )) AS pk
        FROM $stage_db.settlement_historical_v2_snapshot AS h
    )

    -- Get number of records per pk from current
    , unique_historical_current AS (
        SELECT 
            pk
          , COUNT(1) AS cnt_current
        FROM historical_current
        GROUP BY 1
    )

    -- Get number of records per pk from previous
    , unique_historical_previous AS (
        SELECT
            pk
          , COUNT(1) AS cnt_previous
        FROM historical_previous
        GROUP BY 1
    )

    -- Find the records that need to be added. This is either 
    -- a) Records where pk was not seen previously OR
    -- b) Records where pk was seen previously, but we see additional exact rows that were added to historical
    -- Not sure if there is a way to dedupe historical currently, so we treat historical as ground truth.
    , historical_delta AS (
        SELECT
            c.pk
          , (CASE WHEN p.pk IS NULL THEN c.cnt_current   -- Add all records when pk not seen previously
                  ELSE (c.cnt_current - p.cnt_previous)  -- Only add additional required number of records when pk seen previously
            END) AS num_records_needed
        FROM unique_historical_current AS c
        LEFT JOIN unique_historical_previous AS p
            ON c.pk = p.pk
    )

    SELECT
        c."amount" AS amount
      , c."amount_description" AS amount_description
      , c."amount_type" AS amount_type
      , UPPER(c."brand") AS brand_code
      , c."category" AS category
      , UPPER(c."currency") AS currency
      , UPPER(c."data_source") AS data_source
      , c."fulfillment_id" AS fulfillment_id
      , c."netsuite_gl" AS netsuite_gl
      , c."order_id" AS order_id
      , c."posted_date_time" AS posted_date_time
      , c."quantity_purchased" AS quantity_purchased
      , LOWER(c."marketplace_name") AS sales_channel
      , c."seller_id" AS seller_id
      , c."settlement_id"::INT AS settlement_id
      , c."sku" AS sku
      , c."transaction_type" AS transaction_type
    FROM historical_current AS c
    JOIN historical_delta AS d
        ON c.pk = d.pk
    WHERE c.row_number <= d.num_records_needed;

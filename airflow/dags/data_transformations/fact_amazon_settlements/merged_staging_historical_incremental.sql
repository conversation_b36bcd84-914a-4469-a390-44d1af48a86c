/*
Insert the historical diff into the merged staging table.
*/
SET sysdt=SYSDATE();

INSERT INTO $stage_db.merge_stage_amazon_settlements (
    adjustment_id
  , amount
  , amount_description
  , amount_type
  , brand_code
  , category
  , country_code
  , currency
  , data_source
  , daton_batch_id
  , daton_batch_runtime
  , daton_user_id
  , deposit_date_time
  , encrypted_marketplace_id
  , etl_batch_run_time
  , file_name
  , fulfillment_id
  , merchant_adjustment_item_id
  , merchant_order_id
  , merchant_order_item_id
  , netsuite_gl
  , netsuite_id
  , netsuite_item_mapping
  , order_id
  , order_item_code
  , posted_date
  , posted_date_time
  , promotion_id
  , quantity_purchased
  , report_end_date
  , report_start_date
  , sales_channel
  , seller_id
  , settlement_end_date
  , settlement_id
  , settlement_start_date
  , shipment_id
  , sku
  , total_amount
  , transaction_type
  , record_created_timestamp_utc
  , record_updated_timestamp_utc
)
    WITH marketplace_lookup AS (
        SELECT DISTINCT
            LOWER(sales_channel) AS sales_channel
          , UPPER(country_code) AS country_code
        FROM $curated_db.amazon_marketplaces
    )

    , unique_gl_mapping AS (
        SELECT *
        FROM analytics.common.amazon_settlement_gl_mapping_live 
        QUALIFY ROW_NUMBER() OVER (PARTITION BY UPPER(transaction_type), UPPER(amount_type), UPPER(amount_description) 
                                   ORDER BY netsuite_id NULLS LAST
                                          , netsuite_gl NULLS LAST
                                          , netsuite_item_mapping NULLS LAST) = 1
    )

    SELECT 
        NULL AS adjustment_id
      , h.amount
      , h.amount_description
      , h.amount_type
      , h.brand_code
      , h.category
      , ml.country_code
      , h.currency
      , h.data_source
      , NULL AS daton_batch_id
      , NULL AS daton_batch_runtime
      , NULL AS daton_user_id
      , NULL AS deposit_date_time
      , NULL AS encrypted_marketplace_id
      , NULL AS etl_batch_run_time
      , NULL AS file_name
      , (CASE WHEN h.fulfillment_id = '' THEN NULL
              ELSE h.fulfillment_id
        END) AS fulfillment_id
      , NULL AS merchant_adjustment_item_id
      , NULL AS merchant_order_id
      , NULL AS merchant_order_item_id
      , h.netsuite_gl
      , gl.netsuite_id
      , (CASE WHEN gl.netsuite_item_mapping LIKE '%Cypher%' THEN us.netsuite_item_number
              ELSE gl.netsuite_item_mapping 
        END) AS netsuite_item_mapping
      , (CASE WHEN h.order_id = '' THEN NULL
              ELSE h.order_id
        END) AS order_id
      , NULL AS order_item_code
      , TO_DATE(h.posted_date_time) AS posted_date
      , h.posted_date_time
      , NULL AS promotion_id
      , h.quantity_purchased
      , NULL AS report_end_date
      , NULL AS report_start_date
      , (CASE WHEN LOWER(h.sales_channel) = 'si ca prod marketplace' THEN 'amazon.ca'
              WHEN LOWER(h.sales_channel) = 'si prod es marketplace' THEN 'amazon.es'
              WHEN LOWER(h.sales_channel) = 'si prod it marketplace' THEN 'amazon.it'
              WHEN LOWER(h.sales_channel) = 'si uk prod marketplace' THEN 'amazon.co.uk'
              WHEN h.sales_channel = '' THEN NULL
              ELSE LOWER(h.sales_channel)
        END) AS sales_channel
      , h.seller_id
      , NULL AS settlement_end_date
      , h.settlement_id
      , NULL AS settlement_start_date
      , NULL AS shipment_id
      , h.sku
      , NULL AS total_amount
      , h.transaction_type
      , $sysdt AS record_created_timestamp_utc
      , $sysdt AS record_updated_timestamp_utc
    FROM $stage_db.stage_amazon_settlements_historical_incremental AS h
    LEFT JOIN unique_gl_mapping AS gl
        ON UPPER(h.transaction_type) = UPPER(gl.transaction_type)
        AND UPPER(h.amount_type) = UPPER(gl.amount_type)
        AND UPPER(h.amount_description) = UPPER(gl.amount_description)
    LEFT JOIN dwh.prod.sku_item_mapping AS us 
        ON h.sku = us.sku
        AND h.brand_code = us.brand_code
        AND h.country_code = us.country_code
    LEFT JOIN marketplace_lookup AS ml
        ON UPPER(h.sales_channel) = UPPER(ml.sales_channel);

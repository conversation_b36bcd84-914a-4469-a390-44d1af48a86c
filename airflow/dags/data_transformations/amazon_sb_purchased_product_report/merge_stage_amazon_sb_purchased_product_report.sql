CREATE TABLE IF NOT EXISTS $stage_db.merge_amazon_sb_purchased_product_report AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    
    FROM $stage_db.dedupe_amazon_sb_purchased_product_report
    
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_amazon_sb_purchased_product_report AS tgt
USING
    
    $stage_db.dedupe_amazon_sb_purchased_product_report AS src
    
    ON 1 = 1
    AND src.pk = tgt.pk 
WHEN MATCHED
    AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    
    tgt.pk = src.pk,
    tgt.requesttime = src.requesttime,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.accountid = src.accountid,
    tgt.accountname = src.accountname,
    tgt.adgroupname = src.adgroupname,
    tgt.attributiontype = src.attributiontype,
    tgt.budgetcurrency = src.budgetcurrency,
    tgt.campaignbudgetcurrencycode = src.campaignbudgetcurrencycode,
    tgt.campaignname = src.campaignname,
    tgt.countryname = src.countryname,
    tgt.date = src.date,
    tgt.newtobrandorders14d = src.newtobrandorders14d,
    tgt.newtobrandorderspercentage14d = src.newtobrandorderspercentage14d,
    tgt.newtobrandpurchases14d = src.newtobrandpurchases14d,
    tgt.newtobrandpurchasespercentage14d = src.newtobrandpurchasespercentage14d,
    tgt.newtobrandsales14d = src.newtobrandsales14d,
    tgt.newtobrandsalespercentage14d = src.newtobrandsalespercentage14d,
    tgt.newtobrandunitssold14d = src.newtobrandunitssold14d,
    tgt.newtobrandunitssoldpercentage14d = src.newtobrandunitssoldpercentage14d,
    tgt.orders14d = src.orders14d,
    tgt.productcategory = src.productcategory,
    tgt.productname = src.productname,
    tgt.profileid = src.profileid,
    tgt.purchasedasin = src.purchasedasin,
    tgt.reportdate = src.reportdate,
    tgt.sales14d = src.sales14d,
    tgt.unitssold14d = src.unitssold14d,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    
    pk,
    requesttime,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    accountid,
    accountname,
    adgroupname,
    attributiontype,
    budgetcurrency,
    campaignbudgetcurrencycode,
    campaignname,
    countryname,
    date,
    newtobrandorders14d,
    newtobrandorderspercentage14d,
    newtobrandpurchases14d,
    newtobrandpurchasespercentage14d,
    newtobrandsales14d,
    newtobrandsalespercentage14d,
    newtobrandunitssold14d,
    newtobrandunitssoldpercentage14d,
    orders14d,
    productcategory,
    productname,
    profileid,
    purchasedasin,
    reportdate,
    sales14d,
    unitssold14d,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
     
    src.pk,
    src.requesttime, 
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.accountid, 
    src.accountname, 
    src.adgroupname, 
    src.attributiontype, 
    src.budgetcurrency, 
    src.campaignbudgetcurrencycode, 
    src.campaignname, 
    src.countryname, 
    src.date, 
    src.newtobrandorders14d, 
    src.newtobrandorderspercentage14d, 
    src.newtobrandpurchases14d, 
    src.newtobrandpurchasespercentage14d, 
    src.newtobrandsales14d, 
    src.newtobrandsalespercentage14d, 
    src.newtobrandunitssold14d, 
    src.newtobrandunitssoldpercentage14d, 
    src.orders14d, 
    src.productcategory, 
    src.productname, 
    src.profileid, 
    src.purchasedasin, 
    src.reportdate, 
    src.sales14d, 
    src.unitssold14d, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
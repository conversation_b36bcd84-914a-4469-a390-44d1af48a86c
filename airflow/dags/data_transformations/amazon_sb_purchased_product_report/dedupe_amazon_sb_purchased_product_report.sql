
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amazon_sb_purchased_product_report AS (
    SELECT
        
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(date AS VARCHAR), ''), '-',
        COALESCE(CAST(campaignname AS VARCHAR), ''), '-',
        COALESCE(CAST(adgroupname AS VARCHAR), ''), '-',
        COALESCE(CAST(purchasedasin AS VARCHAR), '')
        )) AS pk,
        
        requesttime,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountid,
        accountname,
        adgroupname,
        attributiontype,
        budgetcurrency,
        campaignbudgetcurrencycode,
        campaignname,
        countryname,
        date,
        newtobrandorders14d,
        newtobrandorderspercentage14d,
        newtobrandpurchases14d,
        newtobrandpurchasespercentage14d,
        newtobrandsales14d,
        newtobrandsalespercentage14d,
        newtobrandunitssold14d,
        newtobrandunitssoldpercentage14d,
        orders14d,
        productcategory,
        productname,
        profileid,
        purchasedasin,
        reportdate,
        sales14d,
        unitssold14d,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_amazon_sb_purchased_product_report
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY date, campaignname, adgroupname, purchasedasin
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);


CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amazon_sb_ad_group AS (
    SELECT
        
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(accountid AS VARCHAR), ''), '-',
        COALESCE(CAST(campaignid AS VARCHAR), ''), '-',
        COALESCE(CAST(adgroupid AS VARCHAR), '')
        )) AS pk,
        
        requesttime,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountid,
        accountname,
        adgroupid,
        campaignid,
        countryname,
        name,
        profileid,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_amazon_sb_ad_group
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY accountid, campaignid, adgroupid
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);

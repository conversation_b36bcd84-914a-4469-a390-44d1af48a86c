
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amazon_sponsored_displays_product_report AS (
    SELECT
        
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(reportdate AS VARCHAR), ''), '-',
        COALESCE(CAST(adgroupid AS VARCHAR), ''), '-',
        COALESCE(CAST(adid AS VARCHAR), ''), '-',
        COALESCE(CAST(asin AS VARCHAR), ''), '-',
        COALESCE(CAST(campaignid AS VARCHAR), '')
        )) AS pk,
        
        requesttime,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        accountid,
        accountname,
        adgroupid,
        adgroupname,
        adid,
        asin,
        attributedbrandedsearches14d,
        attributedconversions14d,
        attributedconversions14dsamesku,
        attributedconversions1d,
        attributedconversions1dsamesku,
        attributedconversions30d,
        attributedconversions30dsamesku,
        attributedconversions7d,
        attributedconversions7dsamesku,
        attributeddetailpageview14d,
        attributedordersnewtobrand14d,
        attributedsales14d,
        attributedsales14dsamesku,
        attributedsales1d,
        attributedsales1dsamesku,
        attributedsales30d,
        attributedsales30dsamesku,
        attributedsales7d,
        attributedsales7dsamesku,
        attributedsalesnewtobrand14d,
        attributedunitsordered14d,
        attributedunitsordered1d,
        attributedunitsordered30d,
        attributedunitsordered7d,
        attributedunitsorderednewtobrand14d,
        campaignid,
        campaignname,
        clicks,
        cost,
        countryname,
        currency,
        impressions,
        profileid,
        reportdate,
        sku,
        tactic,
        viewattributedbrandedsearches14d,
        viewattributedconversions14d,
        viewattributeddetailpageview14d,
        viewattributedordersnewtobrand14d,
        viewattributedsales14d,
        viewattributedsalesnewtobrand14d,
        viewattributedunitsordered14d,
        viewattributedunitsorderednewtobrand14d,
        viewimpressions,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_amazon_sponsored_displays_product_report
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY reportdate, adgroupid, adid, asin, campaignid
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);

CREATE TABLE IF NOT EXISTS $stage_db.merge_amazon_sponsored_displays_product_report AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    
    FROM $stage_db.dedupe_amazon_sponsored_displays_product_report
    
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_amazon_sponsored_displays_product_report AS tgt
USING
    
    $stage_db.dedupe_amazon_sponsored_displays_product_report AS src
    
    ON 1 = 1
    AND src.pk = tgt.pk 
WHEN MATCHED
    AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    
    tgt.pk = src.pk,
    tgt.requesttime = src.requesttime,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.accountid = src.accountid,
    tgt.accountname = src.accountname,
    tgt.adgroupid = src.adgroupid,
    tgt.adgroupname = src.adgroupname,
    tgt.adid = src.adid,
    tgt.asin = src.asin,
    tgt.attributedbrandedsearches14d = src.attributedbrandedsearches14d,
    tgt.attributedconversions14d = src.attributedconversions14d,
    tgt.attributedconversions14dsamesku = src.attributedconversions14dsamesku,
    tgt.attributedconversions1d = src.attributedconversions1d,
    tgt.attributedconversions1dsamesku = src.attributedconversions1dsamesku,
    tgt.attributedconversions30d = src.attributedconversions30d,
    tgt.attributedconversions30dsamesku = src.attributedconversions30dsamesku,
    tgt.attributedconversions7d = src.attributedconversions7d,
    tgt.attributedconversions7dsamesku = src.attributedconversions7dsamesku,
    tgt.attributeddetailpageview14d = src.attributeddetailpageview14d,
    tgt.attributedordersnewtobrand14d = src.attributedordersnewtobrand14d,
    tgt.attributedsales14d = src.attributedsales14d,
    tgt.attributedsales14dsamesku = src.attributedsales14dsamesku,
    tgt.attributedsales1d = src.attributedsales1d,
    tgt.attributedsales1dsamesku = src.attributedsales1dsamesku,
    tgt.attributedsales30d = src.attributedsales30d,
    tgt.attributedsales30dsamesku = src.attributedsales30dsamesku,
    tgt.attributedsales7d = src.attributedsales7d,
    tgt.attributedsales7dsamesku = src.attributedsales7dsamesku,
    tgt.attributedsalesnewtobrand14d = src.attributedsalesnewtobrand14d,
    tgt.attributedunitsordered14d = src.attributedunitsordered14d,
    tgt.attributedunitsordered1d = src.attributedunitsordered1d,
    tgt.attributedunitsordered30d = src.attributedunitsordered30d,
    tgt.attributedunitsordered7d = src.attributedunitsordered7d,
    tgt.attributedunitsorderednewtobrand14d = src.attributedunitsorderednewtobrand14d,
    tgt.campaignid = src.campaignid,
    tgt.campaignname = src.campaignname,
    tgt.clicks = src.clicks,
    tgt.cost = src.cost,
    tgt.countryname = src.countryname,
    tgt.currency = src.currency,
    tgt.impressions = src.impressions,
    tgt.profileid = src.profileid,
    tgt.reportdate = src.reportdate,
    tgt.sku = src.sku,
    tgt.tactic = src.tactic,
    tgt.viewattributedbrandedsearches14d = src.viewattributedbrandedsearches14d,
    tgt.viewattributedconversions14d = src.viewattributedconversions14d,
    tgt.viewattributeddetailpageview14d = src.viewattributeddetailpageview14d,
    tgt.viewattributedordersnewtobrand14d = src.viewattributedordersnewtobrand14d,
    tgt.viewattributedsales14d = src.viewattributedsales14d,
    tgt.viewattributedsalesnewtobrand14d = src.viewattributedsalesnewtobrand14d,
    tgt.viewattributedunitsordered14d = src.viewattributedunitsordered14d,
    tgt.viewattributedunitsorderednewtobrand14d = src.viewattributedunitsorderednewtobrand14d,
    tgt.viewimpressions = src.viewimpressions,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    
    pk,
    requesttime,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    accountid,
    accountname,
    adgroupid,
    adgroupname,
    adid,
    asin,
    attributedbrandedsearches14d,
    attributedconversions14d,
    attributedconversions14dsamesku,
    attributedconversions1d,
    attributedconversions1dsamesku,
    attributedconversions30d,
    attributedconversions30dsamesku,
    attributedconversions7d,
    attributedconversions7dsamesku,
    attributeddetailpageview14d,
    attributedordersnewtobrand14d,
    attributedsales14d,
    attributedsales14dsamesku,
    attributedsales1d,
    attributedsales1dsamesku,
    attributedsales30d,
    attributedsales30dsamesku,
    attributedsales7d,
    attributedsales7dsamesku,
    attributedsalesnewtobrand14d,
    attributedunitsordered14d,
    attributedunitsordered1d,
    attributedunitsordered30d,
    attributedunitsordered7d,
    attributedunitsorderednewtobrand14d,
    campaignid,
    campaignname,
    clicks,
    cost,
    countryname,
    currency,
    impressions,
    profileid,
    reportdate,
    sku,
    tactic,
    viewattributedbrandedsearches14d,
    viewattributedconversions14d,
    viewattributeddetailpageview14d,
    viewattributedordersnewtobrand14d,
    viewattributedsales14d,
    viewattributedsalesnewtobrand14d,
    viewattributedunitsordered14d,
    viewattributedunitsorderednewtobrand14d,
    viewimpressions,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
     
    src.pk,
    src.requesttime, 
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.accountid, 
    src.accountname, 
    src.adgroupid, 
    src.adgroupname, 
    src.adid, 
    src.asin, 
    src.attributedbrandedsearches14d, 
    src.attributedconversions14d, 
    src.attributedconversions14dsamesku, 
    src.attributedconversions1d, 
    src.attributedconversions1dsamesku, 
    src.attributedconversions30d, 
    src.attributedconversions30dsamesku, 
    src.attributedconversions7d, 
    src.attributedconversions7dsamesku, 
    src.attributeddetailpageview14d, 
    src.attributedordersnewtobrand14d, 
    src.attributedsales14d, 
    src.attributedsales14dsamesku, 
    src.attributedsales1d, 
    src.attributedsales1dsamesku, 
    src.attributedsales30d, 
    src.attributedsales30dsamesku, 
    src.attributedsales7d, 
    src.attributedsales7dsamesku, 
    src.attributedsalesnewtobrand14d, 
    src.attributedunitsordered14d, 
    src.attributedunitsordered1d, 
    src.attributedunitsordered30d, 
    src.attributedunitsordered7d, 
    src.attributedunitsorderednewtobrand14d, 
    src.campaignid, 
    src.campaignname, 
    src.clicks, 
    src.cost, 
    src.countryname, 
    src.currency, 
    src.impressions, 
    src.profileid, 
    src.reportdate, 
    src.sku, 
    src.tactic, 
    src.viewattributedbrandedsearches14d, 
    src.viewattributedconversions14d, 
    src.viewattributeddetailpageview14d, 
    src.viewattributedordersnewtobrand14d, 
    src.viewattributedsales14d, 
    src.viewattributedsalesnewtobrand14d, 
    src.viewattributedunitsordered14d, 
    src.viewattributedunitsorderednewtobrand14d, 
    src.viewimpressions, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
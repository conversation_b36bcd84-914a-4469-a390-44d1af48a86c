CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_amazon_sponsored_displays_product_report AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_amazon_sponsored_displays_product_report
    WHERE 1 = 0;

INSERT INTO $raw_db.log_amazon_sponsored_displays_product_report
SELECT 
    *
  , SYSDATE() AS log_timestamp_utc 
FROM 
  $raw_db.raw_amazon_sponsored_displays_product_report;
CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_recharge_customers_report AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_recharge_customers_report
    WHERE 1 = 0;

INSERT INTO $raw_db.log_recharge_customers_report (
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    analytics_data,
    apply_credit_to_next_checkout_charge,
    apply_credit_to_next_recurring_charge,
    created_at,
    email,
    external_customer_id,
    first_charge_processed_at,
    first_name,
    has_payment_method_in_dunning,
    has_valid_payment_method,
    hash,
    id,
    last_name,
    phone,
    subscriptions_active_count,
    subscriptions_total_count,
    tax_exempt,
    updated_at,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        analytics_data,
        apply_credit_to_next_checkout_charge,
        apply_credit_to_next_recurring_charge,
        created_at,
        email,
        external_customer_id,
        first_charge_processed_at,
        first_name,
        has_payment_method_in_dunning,
        has_valid_payment_method,
        hash,
        id,
        last_name,
        phone,
        subscriptions_active_count,
        subscriptions_total_count,
        tax_exempt,
        updated_at,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_recharge_customers_report;
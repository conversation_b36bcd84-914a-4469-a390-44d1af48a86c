CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_recharge_orders_report AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(id AS VARCHAR), '')
            )) AS pk,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        address_id,
        billing_address,
        charge,
        client_details,
        created_at,
        currency,
        customer,
        discounts,
        external_cart_token,
        external_order_id,
        external_order_name,
        external_order_number,
        id,
        is_prepaid,
        line_items,
        note,
        order_attributes,
        processed_at,
        scheduled_at,
        shipping_address,
        shipping_lines,
        status,
        subtotal_price,
        tags,
        tax_lines,
        taxable,
        total_discounts,
        total_duties,
        total_line_items_price,
        total_price,
        total_refunds,
        total_tax,
        total_weight_grams,
        type,
        updated_at,
        split_part(split_part(file_name,'/',7),'_',1) as brand_code,
        split_part(split_part(FILE_NAME, '/',  7), '_', 3) as country_code,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_recharge_orders_report
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY id
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);
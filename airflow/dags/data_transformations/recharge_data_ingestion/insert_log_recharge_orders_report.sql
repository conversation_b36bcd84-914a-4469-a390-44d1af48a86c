CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_recharge_orders_report AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_recharge_orders_report
    WHERE 1 = 0;

INSERT INTO $raw_db.log_recharge_orders_report (
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    address_id,
    billing_address,
    charge,
    client_details,
    created_at,
    currency,
    customer,
    discounts,
    external_cart_token,
    external_order_id,
    external_order_name,
    external_order_number,
    id,
    is_prepaid,
    line_items,
    note,
    order_attributes,
    processed_at,
    scheduled_at,
    shipping_address,
    shipping_lines,
    status,
    subtotal_price,
    tags,
    tax_lines,
    taxable,
    total_discounts,
    total_duties,
    total_line_items_price,
    total_price,
    total_refunds,
    total_tax,
    total_weight_grams,
    type,
    updated_at,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        address_id,
        billing_address,
        charge,
        client_details,
        created_at,
        currency,
        customer,
        discounts,
        external_cart_token,
        external_order_id,
        external_order_name,
        external_order_number,
        id,
        is_prepaid,
        line_items,
        note,
        order_attributes,
        processed_at,
        scheduled_at,
        shipping_address,
        shipping_lines,
        status,
        subtotal_price,
        tags,
        tax_lines,
        taxable,
        total_discounts,
        total_duties,
        total_line_items_price,
        total_price,
        total_refunds,
        total_tax,
        total_weight_grams,
        type,
        updated_at,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_recharge_orders_report;
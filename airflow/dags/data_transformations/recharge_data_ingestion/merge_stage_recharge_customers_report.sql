CREATE TABLE IF NOT EXISTS $stage_db.merge_recharge_customers_report AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_recharge_customers_report
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_recharge_customers_report AS tgt
USING
    $stage_db.dedupe_recharge_customers_report AS src
    
        ON 1 = 1
       AND src.pk = tgt.pk
       
WHEN MATCHED AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt.brand_code = src.brand_code,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.analytics_data = src.analytics_data,
    tgt.apply_credit_to_next_checkout_charge = src.apply_credit_to_next_checkout_charge,
    tgt.apply_credit_to_next_recurring_charge = src.apply_credit_to_next_recurring_charge,
    tgt.created_at = src.created_at,
    tgt.email = src.email,
    tgt.external_customer_id = src.external_customer_id,
    tgt.first_charge_processed_at = src.first_charge_processed_at,
    tgt.first_name = src.first_name,
    tgt.has_payment_method_in_dunning = src.has_payment_method_in_dunning,
    tgt.has_valid_payment_method = src.has_valid_payment_method,
    tgt.hash = src.hash,
    tgt.id = src.id,
    tgt.last_name = src.last_name,
    tgt.phone = src.phone,
    tgt.subscriptions_active_count = src.subscriptions_active_count,
    tgt.subscriptions_total_count = src.subscriptions_total_count,
    tgt.tax_exempt = src.tax_exempt,
    tgt.updated_at = src.updated_at,
    tgt.country_code = src.country_code,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    analytics_data,
    apply_credit_to_next_checkout_charge,
    apply_credit_to_next_recurring_charge,
    created_at,
    email,
    external_customer_id,
    first_charge_processed_at,
    first_name,
    has_payment_method_in_dunning,
    has_valid_payment_method,
    hash,
    id,
    brand_code,
    last_name,
    phone,
    subscriptions_active_count,
    subscriptions_total_count,
    tax_exempt,
    updated_at,
    country_code,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.analytics_data, 
    src.apply_credit_to_next_checkout_charge, 
    src.apply_credit_to_next_recurring_charge, 
    src.created_at, 
    src.email, 
    src.external_customer_id, 
    src.first_charge_processed_at, 
    src.first_name, 
    src.has_payment_method_in_dunning, 
    src.has_valid_payment_method, 
    src.hash, 
    src.id,
    src.brand_code,
    src.last_name, 
    src.phone, 
    src.subscriptions_active_count, 
    src.subscriptions_total_count, 
    src.tax_exempt, 
    src.updated_at,
    src.country_code,
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
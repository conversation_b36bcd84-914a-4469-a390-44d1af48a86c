CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_recharge_customers_report AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(id AS VARCHAR), '')
            )) AS pk,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        analytics_data,
        apply_credit_to_next_checkout_charge,
        apply_credit_to_next_recurring_charge,
        created_at,
        email,
        external_customer_id,
        first_charge_processed_at,
        first_name,
        has_payment_method_in_dunning,
        has_valid_payment_method,
        hash,
        id,
        last_name,
        phone,
        subscriptions_active_count,
        subscriptions_total_count,
        tax_exempt,
        updated_at,
        split_part(split_part(file_name,'/',7),'_',1) as brand_code,
        split_part(split_part(FILE_NAME, '/',  7), '_', 3) as country_code,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_recharge_customers_report
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY id
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);
CREATE TABLE IF NOT EXISTS $stage_db.merge_recharge_subscriptions_report AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_recharge_subscriptions_report
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_recharge_subscriptions_report AS tgt
USING
    $stage_db.dedupe_recharge_subscriptions_report AS src
    
        ON 1 = 1
       AND src.pk = tgt.pk
       
WHEN MATCHED AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.address_id = src.address_id,
    tgt.analytics_data = src.analytics_data,
    tgt.cancellation_reason = src.cancellation_reason,
    tgt.cancellation_reason_comments = src.cancellation_reason_comments,
    tgt.cancelled_at = src.cancelled_at,
    tgt.charge_interval_frequency = src.charge_interval_frequency,
    tgt.created_at = src.created_at,
    tgt.customer_id = src.customer_id,
    tgt.external_product_id = src.external_product_id,
    tgt.external_variant_id = src.external_variant_id,
    tgt.has_queued_charges = src.has_queued_charges,
    tgt.id = src.id,
    tgt.brand_code = src.brand_code,
    tgt.is_prepaid = src.is_prepaid,
    tgt.is_skippable = src.is_skippable,
    tgt.is_swappable = src.is_swappable,
    tgt.max_retries_reached = src.max_retries_reached,
    tgt.next_charge_scheduled_at = src.next_charge_scheduled_at,
    tgt.order_day_of_month = src.order_day_of_month,
    tgt.order_interval_frequency = src.order_interval_frequency,
    tgt.order_interval_unit = src.order_interval_unit,
    tgt.presentment_currency = src.presentment_currency,
    tgt.price = src.price,
    tgt.product_title = src.product_title,
    tgt.properties = src.properties,
    tgt.quantity = src.quantity,
    tgt.sku = src.sku,
    tgt.sku_override = src.sku_override,
    tgt.status = src.status,
    tgt.updated_at = src.updated_at,
    tgt.country_code = src.country_code,
    tgt.variant_title = src.variant_title,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    address_id,
    analytics_data,
    cancellation_reason,
    cancellation_reason_comments,
    cancelled_at,
    charge_interval_frequency,
    created_at,
    customer_id,
    external_product_id,
    external_variant_id,
    has_queued_charges,
    id,
    brand_code,
    is_prepaid,
    is_skippable,
    is_swappable,
    max_retries_reached,
    next_charge_scheduled_at,
    order_day_of_month,
    order_interval_frequency,
    order_interval_unit,
    presentment_currency,
    price,
    product_title,
    properties,
    quantity,
    sku,
    sku_override,
    status,
    updated_at,
    country_code,
    variant_title,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.address_id, 
    src.analytics_data, 
    src.cancellation_reason, 
    src.cancellation_reason_comments, 
    src.cancelled_at, 
    src.charge_interval_frequency, 
    src.created_at, 
    src.customer_id, 
    src.external_product_id, 
    src.external_variant_id, 
    src.has_queued_charges, 
    src.id,
    src.brand_code,
    src.is_prepaid, 
    src.is_skippable, 
    src.is_swappable, 
    src.max_retries_reached, 
    src.next_charge_scheduled_at, 
    src.order_day_of_month, 
    src.order_interval_frequency, 
    src.order_interval_unit, 
    src.presentment_currency, 
    src.price, 
    src.product_title, 
    src.properties, 
    src.quantity, 
    src.sku, 
    src.sku_override, 
    src.status,
    src.updated_at,
    src.country_code,
    src.variant_title, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
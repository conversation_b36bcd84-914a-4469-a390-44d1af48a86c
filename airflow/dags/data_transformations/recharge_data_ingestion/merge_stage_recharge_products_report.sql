CREATE TABLE IF NOT EXISTS $stage_db.merge_recharge_products_report AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_recharge_products_report
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_recharge_products_report AS tgt
USING
    $stage_db.dedupe_recharge_products_report AS src
    
        ON 1 = 1
       AND src.pk = tgt.pk
       
WHEN MATCHED AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.created_at = src.created_at,
    tgt.discount_amount = src.discount_amount,
    tgt.discount_type = src.discount_type,
    tgt.handle = src.handle,
    tgt.id = src.id,
    tgt.brand_code = src.brand_code,
    tgt.images = src.images,
    tgt.product_id = src.product_id,
    tgt.shopify_product_id = src.shopify_product_id,
    tgt.subscription_defaults = src.subscription_defaults,
    tgt.title = src.title,
    tgt.updated_at = src.updated_at,
    tgt.country_code = src.country_code,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    created_at,
    discount_amount,
    discount_type,
    handle,
    id,
    brand_code,
    images,
    product_id,
    shopify_product_id,
    subscription_defaults,
    title,
    updated_at,
    country_code,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.created_at, 
    src.discount_amount, 
    src.discount_type, 
    src.handle, 
    src.id,
    src.brand_code,
    src.images, 
    src.product_id, 
    src.shopify_product_id, 
    src.subscription_defaults, 
    src.title,
    src.updated_at,
    src.country_code,
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
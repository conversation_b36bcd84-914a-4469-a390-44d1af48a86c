CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_recharge_products_report AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(product_id AS VARCHAR), '')
            )) AS pk,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        created_at,
        discount_amount,
        discount_type,
        handle,
        id,
        images,
        product_id,
        shopify_product_id,
        subscription_defaults,
        title,
        updated_at,
        split_part(split_part(file_name,'/',7),'_',1) as brand_code,
        split_part(split_part(FILE_NAME, '/',  7), '_', 3) as country_code,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_recharge_products_report
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY product_id
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_recharge_subscriptions_report AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(id AS VARCHAR), '')

            )) AS pk,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        address_id,
        analytics_data,
        cancellation_reason,
        cancellation_reason_comments,
        cancelled_at,
        charge_interval_frequency,
        created_at,
        customer_id,
        external_product_id,
        external_variant_id,
        has_queued_charges,
        id,
        is_prepaid,
        is_skippable,
        is_swappable,
        max_retries_reached,
        next_charge_scheduled_at,
        order_day_of_month,
        order_interval_frequency,
        order_interval_unit,
        presentment_currency,
        price,
        product_title,
        properties,
        quantity,
        sku,
        sku_override,
        status,
        updated_at,
        variant_title,
        file_name,
        split_part(split_part(file_name,'/',7),'_',1) as brand_code,
        split_part(split_part(FILE_NAME, '/',  7), '_', 3) as country_code,
        etl_batch_run_time
        
    FROM $raw_db.raw_recharge_subscriptions_report
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY id
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);
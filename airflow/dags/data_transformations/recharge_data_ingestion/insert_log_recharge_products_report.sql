CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_recharge_products_report AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_recharge_products_report
    WHERE 1 = 0;

INSERT INTO $raw_db.log_recharge_products_report (
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    created_at,
    discount_amount,
    discount_type,
    handle,
    id,
    images,
    product_id,
    shopify_product_id,
    subscription_defaults,
    title,
    updated_at,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        created_at,
        discount_amount,
        discount_type,
        handle,
        id,
        images,
        product_id,
        shopify_product_id,
        subscription_defaults,
        title,
        updated_at,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_recharge_products_report;
CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_recharge_subscriptions_report AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_recharge_subscriptions_report
    WHERE 1 = 0;

INSERT INTO $raw_db.log_recharge_subscriptions_report (
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    address_id,
    analytics_data,
    cancellation_reason,
    cancellation_reason_comments,
    cancelled_at,
    charge_interval_frequency,
    created_at,
    customer_id,
    external_product_id,
    external_variant_id,
    has_queued_charges,
    id,
    is_prepaid,
    is_skippable,
    is_swappable,
    max_retries_reached,
    next_charge_scheduled_at,
    order_day_of_month,
    order_interval_frequency,
    order_interval_unit,
    presentment_currency,
    price,
    product_title,
    properties,
    quantity,
    sku,
    sku_override,
    status,
    updated_at,
    variant_title,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        address_id,
        analytics_data,
        cancellation_reason,
        cancellation_reason_comments,
        cancelled_at,
        charge_interval_frequency,
        created_at,
        customer_id,
        external_product_id,
        external_variant_id,
        has_queued_charges,
        id,
        is_prepaid,
        is_skippable,
        is_swappable,
        max_retries_reached,
        next_charge_scheduled_at,
        order_day_of_month,
        order_interval_frequency,
        order_interval_unit,
        presentment_currency,
        price,
        product_title,
        properties,
        quantity,
        sku,
        sku_override,
        status,
        updated_at,
        variant_title,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_recharge_subscriptions_report;
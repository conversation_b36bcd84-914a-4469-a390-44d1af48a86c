CREATE TABLE IF NOT EXISTS $stage_db.merge_recharge_orders_report AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_recharge_orders_report
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_recharge_orders_report AS tgt
USING
    $stage_db.dedupe_recharge_orders_report AS src
    
        ON 1 = 1
       AND src.pk = tgt.pk
       
WHEN MATCHED AND src._daton_batch_runtime >= tgt._daton_batch_runtime THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.address_id = src.address_id,
    tgt.billing_address = src.billing_address,
    tgt.charge = src.charge,
    tgt.client_details = src.client_details,
    tgt.created_at = src.created_at,
    tgt.currency = src.currency,
    tgt.customer = src.customer,
    tgt.discounts = src.discounts,
    tgt.external_cart_token = src.external_cart_token,
    tgt.external_order_id = src.external_order_id,
    tgt.external_order_name = src.external_order_name,
    tgt.external_order_number = src.external_order_number,
    tgt.id = src.id,
    tgt.brand_code = src.brand_code,
    tgt.is_prepaid = src.is_prepaid,
    tgt.line_items = src.line_items,
    tgt.note = src.note,
    tgt.order_attributes = src.order_attributes,
    tgt.processed_at = src.processed_at,
    tgt.scheduled_at = src.scheduled_at,
    tgt.shipping_address = src.shipping_address,
    tgt.shipping_lines = src.shipping_lines,
    tgt.status = src.status,
    tgt.subtotal_price = src.subtotal_price,
    tgt.tags = src.tags,
    tgt.tax_lines = src.tax_lines,
    tgt.taxable = src.taxable,
    tgt.total_discounts = src.total_discounts,
    tgt.total_duties = src.total_duties,
    tgt.total_line_items_price = src.total_line_items_price,
    tgt.total_price = src.total_price,
    tgt.total_refunds = src.total_refunds,
    tgt.total_tax = src.total_tax,
    tgt.total_weight_grams = src.total_weight_grams,
    tgt.type = src.type,
    tgt.updated_at = src.updated_at,
    tgt.country_code = src.country_code,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    address_id,
    billing_address,
    charge,
    client_details,
    created_at,
    currency,
    customer,
    discounts,
    external_cart_token,
    external_order_id,
    external_order_name,
    external_order_number,
    id,
    brand_code,
    is_prepaid,
    line_items,
    note,
    order_attributes,
    processed_at,
    scheduled_at,
    shipping_address,
    shipping_lines,
    status,
    subtotal_price,
    tags,
    tax_lines,
    taxable,
    total_discounts,
    total_duties,
    total_line_items_price,
    total_price,
    total_refunds,
    total_tax,
    total_weight_grams,
    type,
    updated_at,
    country_code,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.address_id, 
    src.billing_address, 
    src.charge, 
    src.client_details, 
    src.created_at, 
    src.currency, 
    src.customer, 
    src.discounts, 
    src.external_cart_token, 
    src.external_order_id, 
    src.external_order_name, 
    src.external_order_number, 
    src.id,
    src.brand_code,
    src.is_prepaid, 
    src.line_items, 
    src.note, 
    src.order_attributes, 
    src.processed_at, 
    src.scheduled_at, 
    src.shipping_address, 
    src.shipping_lines, 
    src.status, 
    src.subtotal_price, 
    src.tags, 
    src.tax_lines, 
    src.taxable, 
    src.total_discounts, 
    src.total_duties, 
    src.total_line_items_price, 
    src.total_price, 
    src.total_refunds, 
    src.total_tax, 
    src.total_weight_grams, 
    src.type, 
    src.updated_at,
    src.country_code,
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
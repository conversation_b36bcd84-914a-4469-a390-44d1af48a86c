
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_amazon_ledger_detailed_inventory AS (
    SELECT
        
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(date AS VARCHAR), ''), '-',
        COALESCE(CAST(msku AS VARCHAR), ''), '-',
        COALESCE(CAST(fnsku AS VARCHAR), ''), '-',
        COALESCE(CAST(asin AS VARCHAR), ''), '-',
        COALESCE(CAST(disposition AS VARCHAR), ''), '-',
        COALESCE(CAST(country AS VARCHAR), ''), '-',
        COALESCE(CAST(fulfillment_center AS VARCHAR), ''), '-',
        COALESCE(CAST(event_type AS VARCHAR), ''), '-',
        COALESCE(CAST(reference_id AS VARCHAR), '')
        )) AS id_key,
        
        asin,
        country,
        date,
        disposition,
        event_type,
        fnsku,
        fulfillment_center,
        case when msku  like 'amzn.gr.%'
        then split_part(split_part(split_part(msku,'amzn.gr.',2),'-',1),'_',1)
        else msku end as sku,
        quantity,
        reason,
        reference_id,
        reportrequesttime,
        reportenddate,
        reportstartdate,
        title,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        marketplaceid,
        marketplacename,
        sellingpartnerid as seller_id,
        file_name,
        etl_batch_run_time,
        split_part(split_part(file_name, '/',  7), '_', 4) as connector_region
        
    FROM $raw_db.raw_amazon_ledger_detailed_inventory
    QUALIFY RANK() OVER(
        PARTITION BY date, msku, fnsku, asin, disposition,country,fulfillment_center,event_type,reference_id, sellingpartnerid
        ORDER BY _daton_batch_runtime DESC NULLS LAST) = 1
);

CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_AMAZON_LEDGER_DETAILED_INVENTORY AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.RAW_AMAZON_LEDGER_DETAILED_INVENTORY
    WHERE 1 = 0;

INSERT INTO $raw_db.log_AMAZON_LEDGER_DETAILED_INVENTORY (
ASIN, COUNTRY, "DATE", DISPOSITION, EVENT_TYPE, FNSKU, FULFILLMENT_CENTER, MSKU, QUANTITY, REASON, REFERENCE_ID, R<PERSON><PERSON><PERSON><PERSON><PERSON>UESTTI<PERSON>, R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, T<PERSON><PERSON>, "_DATON_BATCH_ID", "_DATON_BATCH_RUNTIME", "_DATON_USER_ID", MARKETPLACEID, MARKETPLACENAME, SELLINGPARTNERID, FILE_NAME, ETL_BATCH_RUN_TIME
,   log_timestamp_utc
)
SELECT ASIN, COUNTRY, "<PERSON><PERSON><PERSON>", <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>VENT_TYPE, FNSKU, FULFILLMENT_CENTER, MSKU, QUANTITY, REASON, REFERENCE_ID, REPORTREQUESTTIME, REPORTENDDATE, REPORTSTARTDATE, TITLE, "_DATON_BATCH_ID", "_DATON_BATCH_RUNTIME", "_DATON_USER_ID", MARKETPLACEID, MARKETPLACENAME, SELLINGPARTNERID, FILE_NAME, ETL_BATCH_RUN_TIME
,      SYSDATE() AS log_timestamp_utc
    FROM $raw_db.RAW_AMAZON_LEDGER_DETAILED_INVENTORY  ;

delete from $stage_db.amazon_ledger_detailed_inventory_v2 AS tgt
using (
        select
        distinct
        md5(cast(coalesce(cast(connector_region as
            varchar
        ), '') || '-' || coalesce(cast(date as
            varchar
        ), '') || '-' || coalesce(cast(sku as
            varchar
        ), '') || '-' || coalesce(cast(fnsku as
            varchar
        ), '') || '-' || coalesce(cast(asin as
            varchar
        ), '') || '-' || coalesce(cast(disposition as
            varchar
        ), '') || '-' || coalesce(cast(country as
            varchar
        ), '') || '-' || coalesce(cast(fulfillment_center as
            varchar
        ), '') || '-' || coalesce(cast(event_type as
            varchar
        ), '') || '-' || coalesce(cast(reference_id as
            varchar
        ), '') as
            varchar
        )) as etl_key
        from $stage_db.dedupe_amazon_ledger_detailed_inventory) src
where 1=1
and tgt.etl_key = src.etl_key;

INSERT INTO $stage_db.amazon_ledger_detailed_inventory_v2
(
    etl_key,
    asin,
    country,
    date,
    disposition,
    event_type,
    fnsku,
    fulfillment_center,
    sku,
    quantity,
    reason,
    reference_id,
    reportrequesttime,
    reportenddate,
    reportstartdate,
    title,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    marketplaceid,
    marketplacename,
    seller_id,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc,
    connector_region,
    created_by,
    updated_by
)
with src as(
select
        md5(cast(coalesce(cast(connector_region as
            varchar
        ), '') || '-' || coalesce(cast(date as
            varchar
        ), '') || '-' || coalesce(cast(sku as
            varchar
        ), '') || '-' || coalesce(cast(fnsku as
            varchar
        ), '') || '-' || coalesce(cast(asin as
            varchar
        ), '') || '-' || coalesce(cast(disposition as
            varchar
        ), '') || '-' || coalesce(cast(country as
            varchar
        ), '') || '-' || coalesce(cast(fulfillment_center as
            varchar
        ), '') || '-' || coalesce(cast(event_type as
            varchar
        ), '') || '-' || coalesce(cast(reference_id as
            varchar
        ), '') as
            varchar
        )) as etl_key,
        *
        from $stage_db.dedupe_amazon_ledger_detailed_inventory
        )
SELECT
    src.etl_key,
    src.asin,
    src.country,
    src.date,
    src.disposition,
    src.event_type,
    src.fnsku,
    src.fulfillment_center,
    src.sku,
    src.quantity,
    src.reason,
    src.reference_id,
    src.reportrequesttime,
    src.reportenddate,
    src.reportstartdate,
    src.title,
    src._daton_batch_id,
    src._daton_batch_runtime,
    src._daton_user_id,
    src.marketplaceid,
    src.marketplacename,
    src.seller_id,
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE(),
    split_part(split_part(src.FILE_NAME, '/',  7), '_', 4),
    'amazon_ledger_detailed_inventory',
    'ORI-3186'
FROM src;

-- update netsuite_item_number and brand_code
UPDATE $stage_db.amazon_ledger_detailed_inventory_v2 AS tgt
SET
netsuite_item_number = src.netsuite_item_number,
brand = src.BRAND_CODE,
record_updated_timestamp_utc = SYSDATE()
FROM
    (
    with ledger_detailed_report as (
    select
        distinct etl_key, sku, seller_id, asin,country
        from $stage_db.amazon_ledger_detailed_inventory_v2
        WHERE NETSUITE_ITEM_NUMBER IS NULL
        OR BRAND IS NULL
    ),
    ledger_detailed_report_with_brand as (
    select ledger_detailed_report.etl_key
    ,ledger_detailed_report.sku
    ,ledger_detailed_report.country
    ,CASE WHEN LOWER(TRIM(SELLER.IS_MULTI_BRAND))='no' THEN SELLER.SELLER_CODE
        ELSE brand_mapping.brand_code END AS BRAND_CODE
    FROM
    ledger_detailed_report
    LEFT JOIN
        $stage_db.STG_SELLER_INFO seller
        ON ledger_detailed_report.seller_id = seller.SELLER_ID
	LEFT JOIN
	    $curated_db.marketplace_asin_brand_mapping brand_mapping
        ON ledger_detailed_report.asin = brand_mapping.asin
        and ledger_detailed_report.country = brand_mapping.country_code
    )
    select l.etl_key
    ,l.sku
    ,l.brand_code
    ,sku_dimension.NETSUITE_ITEM_NUMBER as netsuite_item_number
    FROM
    ledger_detailed_report_with_brand l
    LEFT JOIN
        dwh.prod.sku_item_mapping sku_dimension
        ON l.sku = sku_dimension.sku
        and l.brand_code = sku_dimension.brand_code
        and (CASE WHEN l.country = 'GB' THEN 'UK' ELSE l.country END) = sku_dimension.country_code
    ) src
WHERE
tgt.etl_key = src.etl_key;

CREATE OR R<PERSON>LACE TRANSIENT TABLE $stage_db.dedupe_catalogue_performance_report AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(seller_id AS VARCHAR), ''), '-'
          , COALESCE(CAST(asin AS VARCHAR), ''), '-'
          , COALESCE(CAST(market_place_id AS VARCHAR), ''), '-'
          , COALESCE(CAST(weekly_week_end_date AS VARCHAR), '')
        )) AS pk
      , asin
      , country_code
      , market_place_id AS marketplace_id
      , seller_id
      , weekly_week_start_date
      , weekly_week_end_date
      , weekly_week_name
      , scraper_id
      , currency_code
      , asin_title
      , category
      , TRY_TO_NUMBER(two_day_shipping_impressions) AS two_day_shipping_impressions
      , TRY_TO_NUMBER(purchases_count) AS purchases_count
      , TRY_TO_DECIMAL(conversion_rate, 10, 2) AS conversion_rate
      , TRY_TO_NUMBER(same_day_shipping_clicks) AS same_day_shipping_clicks
      , TRY_TO_DECIMAL(rating_impressions, 10, 2) AS rating_impressions
      , TRY_TO_NUMBER(cart_adds_count) AS cart_adds_count
      , TRY_TO_NUMBER(two_day_shipping_cart_adds) AS two_day_shipping_cart_adds
      , TRY_TO_NUMBER(same_day_shipping_impressions) AS same_day_shipping_impressions
      , TRY_TO_NUMBER(one_day_shipping_purchases) AS one_day_shipping_purchases
      , TRY_TO_DECIMAL(purchase_price, 10, 2) AS purchase_price
      , TRY_TO_DECIMAL(total_sales_purchases, 10, 2) AS total_sales_purchases
      , TRY_TO_NUMBER(same_day_shipping_cart_adds) AS same_day_shipping_cart_adds
      , TRY_TO_NUMBER(same_day_shipping_purchases) AS same_day_shipping_purchases
      , TRY_TO_NUMBER(impressions_count) AS impressions_count
      , TRY_TO_DECIMAL(rating_purchases, 10, 2) AS rating_purchases
      , TRY_TO_DECIMAL(ctr_clicks, 10, 2) AS ctr_clicks
      , TRY_TO_NUMBER(one_day_shipping_impressions) AS one_day_shipping_impressions
      , TRY_TO_NUMBER(one_day_shipping_clicks) AS one_day_shipping_clicks
      , TRY_TO_DECIMAL(cart_adds_price, 10, 2) AS cart_adds_price
      , TRY_TO_DECIMAL(click_price, 10, 2) AS click_price
      , TRY_TO_NUMBER(two_day_shipping_purchases) AS two_day_shipping_purchases
      , TRY_TO_DECIMAL(impression_price, 10, 2) AS impression_price
      , TRY_TO_NUMBER(clicks) AS clicks
      , TRY_TO_NUMBER(one_day_shipping_cart_adds) AS one_day_shipping_cart_adds
      , TRY_TO_NUMBER(two_day_shipping_clicks) AS two_day_shipping_clicks
      , report_fetched_and_loaded_at
      , file_name
      , etl_batch_run_time
    FROM $raw_db.raw_catalogue_performance_report
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY seller_id, asin, market_place_id, weekly_week_end_date
        ORDER BY report_fetched_and_loaded_at DESC NULLS LAST) = 1
);


CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_brand_view_report AS (
    SELECT
        
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(seller_id AS VARCHAR), ''), '-',
        COALESCE(CAST(sub_brand_name AS VARCHAR), ''), '-',
        COALESCE(CAST(qp_brand_query AS VARCHAR), ''), '-',
        COALESCE(CAST(market_place_id AS VARCHAR), ''), '-',
        COALESCE(CAST(weekly_week_end_date AS VARCHAR), '')
        )) AS pk,
        
        TRY_TO_DECIMAL(qp_click_rate, 10, 2) AS qp_click_rate,
        TRY_TO_NUMBER(qp_query_rank) AS qp_query_rank,
        TRY_TO_NUMBER(qp_impressions) AS qp_impressions,
        TRY_TO_NUMBER(qp_brand_count_impressions) AS qp_brand_count_impressions,
        TRY_TO_DECIMAL(qp_purchase_rate, 10, 2) AS qp_purchase_rate,
        TRY_TO_DECIMAL(qp_cart_adds_brand_share, 10, 2) AS qp_cart_adds_brand_share,
        TRY_TO_NUMBER(qp_same_day_shipping_cart_adds) AS qp_same_day_shipping_cart_adds,
        TRY_TO_NUMBER(qp_same_day_shipping_purchases) AS qp_same_day_shipping_purchases,
        TRY_TO_NUMBER(qp_two_day_shipping_clicks) AS qp_two_day_shipping_clicks,
        TRY_TO_NUMBER(qp_one_day_shipping_clicks) AS qp_one_day_shipping_clicks,
        TRY_TO_DECIMAL(qp_brand_share_clicks, 10, 2) AS qp_brand_share_clicks,
        TRY_TO_DECIMAL(qp_purchases_brand_price, 10, 2) AS qp_purchases_brand_price,
        TRY_TO_DECIMAL(qp_purchase_price, 10, 2) AS qp_purchase_price,
        TRY_TO_DECIMAL(qp_clicks_brand_price, 10, 2) AS qp_clicks_brand_price,
        TRY_TO_NUMBER(qp_clicks) AS qp_clicks,
        TRY_TO_NUMBER(qp_two_day_shipping_purchases) AS qp_two_day_shipping_purchases,
        TRY_TO_NUMBER(qp_cart_adds) AS qp_cart_adds,
        TRY_TO_NUMBER(qp_one_day_shipping_cart_adds) AS qp_one_day_shipping_cart_adds,
        TRY_TO_DECIMAL(qp_purchases_brand_share, 10, 2) AS qp_purchases_brand_share,
        qp_brand_query,
        TRY_TO_DECIMAL(qp_cart_adds_price, 10, 2) AS qp_cart_adds_price,
        TRY_TO_NUMBER(qp_purchases_brand_count) AS qp_purchases_brand_count,
        TRY_TO_NUMBER(qp_same_day_shipping_clicks) AS qp_same_day_shipping_clicks,
        TRY_TO_NUMBER(qp_one_day_shipping_purchases) AS qp_one_day_shipping_purchases,
        TRY_TO_NUMBER(qp_query_volume) AS qp_query_volume,
        TRY_TO_NUMBER(qp_cart_adds_brand_count) AS qp_cart_adds_brand_count,
        TRY_TO_DECIMAL(qp_clicks_price, 10, 2) AS qp_clicks_price,
        TRY_TO_DECIMAL(qp_cart_adds_brand_price, 10, 2) AS qp_cart_adds_brand_price,
        TRY_TO_NUMBER(qp_two_day_shipping_cart_adds) AS qp_two_day_shipping_cart_adds,
        TRY_TO_NUMBER(qp_purchases) AS qp_purchases,
        TRY_TO_DECIMAL(qp_brand_share_impressions, 10, 2) AS qp_brand_share_impressions,
        TRY_TO_NUMBER(qp_brand_count_clicks) AS qp_brand_count_clicks,
        TRY_TO_DECIMAL(qp_cart_add_rate, 10, 2) AS qp_cart_add_rate,
        TRY_TO_NUMBER(sub_brand_value) AS sub_brand_value,
        sub_brand_name,
        weekly_week_start_date,
        weekly_week_end_date,
        weekly_week_name,
        scraper_id,
        seller_id,
        market_place_id,
        country_code,
        currency_code,
        report_fetched_and_loaded_at,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_brand_view_report
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY seller_id, sub_brand_name, qp_brand_query, market_place_id, weekly_week_end_date
        ORDER BY report_fetched_and_loaded_at DESC NULLS LAST) = 1
);

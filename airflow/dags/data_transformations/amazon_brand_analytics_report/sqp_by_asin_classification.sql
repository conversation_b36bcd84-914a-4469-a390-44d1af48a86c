ALTER SESSION SET WEEK_START = 7;
CREATE OR REPLACE TABLE $stage_db.sqp_by_asin_classification AS

WITH sqp_union AS (
	SELECT * FROM (
        SELECT DISTINCT
    		WEEKLY_WEEK_START_DATE,
    		WEEKLY_WEEK_END_DATE,
    
    		SELLER_ID,
    		COUNTRY_CODE,
    
    		ASIN,
    
    		QP_ASIN_QUERY,
    		QP_ASIN_QUERY_VOLUME,
    
    		QP_ASIN_IMPRESSIONS,
    		QP_ASIN_COUNT_IMPRESSIONS,
    		QP_ASIN_SHARE_IMPRESSIONS,
    
    		QP_ASIN_CLICKS,
    		QP_ASIN_COUNT_CLICKS,
    		QP_ASIN_SHARE_CLICKS,
    
    		QP_ASIN_CART_ADDS,
    		QP_ASIN_COUNT_CART_ADDS,
    		QP_ASIN_SHARE_CART_ADDS,
    
    		QP_ASIN_PURCHASES,
    		QP_ASIN_COUNT_PURCHASES,
    		QP_ASIN_SHARE_PURCHASES,
    	FROM DWH.PROD.FACT_ASIN_VIEW_REPORT
    	WHERE WEEKLY_WEEK_START_DATE >= DATE_TRUNC('week', CURRENT_DATE - 365)
    )

	UNION

	SELECT * FROM (
    SELECT DISTINCT
		WEEK_START_DATE,
		WEEK_END_DATE,

		SELLER_ID,
		MARKETPLACE_COUNTRY_CODE,

		ASIN,

		SEARCH_TERM,
		QUERY_VOLUME,

		IMPRESSIONS,
		COUNT_IMPRESSIONS,
		SHARE_IMPRESSIONS,

		CLICKS,
		COUNT_CLICKS,
		SHARE_CLICKS,

		CART_ADDS,
		COUNT_CART_ADDS,
		SHARE_CART_ADDS,

		PURCHASES,
		COUNT_PURCHASES,
		SHARE_PURCHASES,

	FROM DWH.RAW.BQ_SEARCH_QUERY_PERFORMANCE
	WHERE WEEK_START_DATE >= DATE_TRUNC('week', CURRENT_DATE - 365)
    )
)

-- placeholder for a comprehensive list from gsheets
, branded_terms AS (
    SELECT DISTINCT search_term FROM DWH.RAW.BQ_BRANDED_TERMS
)

, first_order AS (
    SELECT 
        "asin",
        "country_code",
        DATEDIFF('week', MIN("purchase_date_utc"), CURRENT_DATE) AS weeks_live
    FROM DWH.PROD.FACT_ALL_ORDERS
    WHERE "marketplace" = 'AMAZON'
        AND "country_code" IS NOT NULL
    GROUP BY ALL
)

, parents AS (
    SELECT
        COUNTRY_CODE,
        parent_asin,
        LISTAGG(DISTINCT child_asin, ',') WITHIN GROUP (ORDER BY child_asin) AS asins,
        COUNT(DISTINCT child_asin) AS asin_count,
    FROM DWH.PROD.PARENT_ASIN_CHILD_ASIN_MAPPING
    WHERE parent_asin != '' AND country_code IS NULL
    GROUP BY 1,2
)

, final AS (
    SELECT
        WEEKLY_WEEK_START_DATE,
        asin,
        LOWER(QP_ASIN_QUERY) AS search_term,
        COUNTRY_CODE,
        QP_ASIN_COUNT_PURCHASES,
        QP_ASIN_COUNT_CART_ADDS,
        QP_ASIN_COUNT_CLICKS,
        QP_ASIN_COUNT_IMPRESSIONS,
        QP_ASIN_QUERY_VOLUME,
        first_order.weeks_live,
    FROM sqp_union
    LEFT JOIN first_order
        ON sqp_union.ASIN = first_order."asin"
            AND sqp_union.country_code = first_order."country_code"
    LEFT JOIN branded_terms
        ON LOWER(QP_ASIN_QUERY) = LOWER(branded_terms.search_term)
    WHERE QP_ASIN_COUNT_PURCHASES > 0
        AND ARRAY_SIZE(SPLIT(QP_ASIN_QUERY,' ')) <= 10
        AND ARRAY_SIZE(SPLIT(QP_ASIN_QUERY,' ')) > 1
        AND REGEXP_LIKE(QP_ASIN_QUERY, '[a-z0-9\\s]*', 'i')
        AND NOT (REGEXP_LIKE(QP_ASIN_QUERY, '.* b[a-z0-9]{9} .*', 'i'))
        AND NOT (REGEXP_LIKE(QP_ASIN_QUERY, '.* 202[0-9]{1} .*', 'i'))
        AND branded_terms.search_term IS NULL
    QUALIFY COUNT(WEEKLY_WEEK_START_DATE) OVER (PARTITION BY ASIN, COUNTRY_CODE) >= CASE WHEN first_order.weeks_live > 25 THEN 13 ELSE ROUND(first_order.weeks_live / 2) END
)

, brands AS (
    SELECT DISTINCT
        LOWER(REGEXP_REPLACE(sub_brand_name, '[^a-zA-Z]', '')) AS brand_string,
    FROM DWH.PROD.fact_brand_view_report
)

SELECT 
    *,
    ROW_NUMBER() OVER (PARTITION BY asin, country_code ORDER BY sales DESC, carts DESC, clicks DESC, impressions DESC, sv DESC) AS top_x_sales_clean
    -- DISTINCT 
    -- search_term, 
    -- country_code 
    -- COUNTRY_CODE,
    -- asin,
    -- search_term,
    -- sales,
FROM (
    SELECT DISTINCT
        final.COUNTRY_CODE,
        final.asin,
        search_term,
        parent_asin,
        asin_count,
        SUM(QP_ASIN_COUNT_PURCHASES) OVER (PARTITION BY parents.parent_asin, final.country_code, search_term) AS sales,
        SUM(QP_ASIN_COUNT_CART_ADDS) OVER (PARTITION BY parents.parent_asin, final.country_code, search_term) AS carts,
        SUM(QP_ASIN_COUNT_CLICKS) OVER (PARTITION BY parents.parent_asin, final.country_code, search_term) AS clicks,
        SUM(QP_ASIN_COUNT_IMPRESSIONS) OVER (PARTITION BY parents.parent_asin, final.country_code, search_term) AS impressions,
        -- size the yearlong search volume of the KW for selection proxy when sales, carts, etc are a tie
        SUM(QP_ASIN_QUERY_VOLUME) OVER (PARTITION BY parents.parent_asin, final.country_code, search_term) AS sv,
    FROM final
    LEFT JOIN parents
        ON CONTAINS(parents.asins, final.asin)
        AND parents.COUNTRY_CODE = final.COUNTRY_CODE
    LEFT JOIN brands
        ON CONTAINS(LOWER(REGEXP_REPLACE(final.search_term, '[^a-zA-Z]', '')), brands.brand_string)
    WHERE brands.brand_string IS NULL
)
-- deduplicate parent groupings taking the largest family
QUALIFY ROW_NUMBER() OVER (PARTITION BY asin, country_code, search_term ORDER BY asin_count DESC, parent_asin DESC) = 1
-- limit to top 20 KWs
    -- AND ROW_NUMBER() OVER (PARTITION BY asin, country_code ORDER BY sales DESC, carts DESC, clicks DESC, impressions DESC, sv DESC) <= 20
-- ORDER BY 1,2

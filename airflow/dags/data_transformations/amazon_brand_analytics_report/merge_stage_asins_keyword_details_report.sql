MERGE INTO $stage_db.merge_asins_keyword_details_report AS tgt
USING (
    SELECT
        d.*
      , bm.brand_code
      , im.netsuite_item_number
    FROM $stage_db.dedupe_asins_keyword_details_report AS d
    LEFT JOIN dwh.prod.marketplace_asin_brand_mapping AS bm
        ON d.asin = bm.asin
       AND d.country_code = bm.country_code
    LEFT JOIN dwh.prod.asin_item_mapping AS im
        ON d.asin = im.asin
       AND d.country_code = im.country_code
       AND bm.brand_code = im.brand_code
) AS src
    ON src.pk = tgt.pk 
WHEN MATCHED
    AND src.report_fetched_and_loaded_at >= tgt.report_fetched_and_loaded_at THEN
UPDATE SET
    tgt.asin = src.asin
  , tgt.brand_code = src.brand_code
  , tgt.country_code = src.country_code
  , tgt.marketplace_id = src.market_place_id
  , tgt.netsuite_item_number = src.netsuite_item_number
  , tgt.seller_id = src.seller_id
  , tgt.weekly_week_start_date = src.weekly_week_start_date
  , tgt.weekly_week_end_date = src.weekly_week_end_date
  , tgt.weekly_week_name = src.weekly_week_name
  , tgt.scraper_id = src.scraper_id
  , tgt.asin_name = src.asin_name
  , tgt.currency_code = src.currency_code
  , tgt.qd_asin = src.qd_asin
  , tgt.qd_asin_metrics_click_share = src.qd_asin_metrics_click_share
  , tgt.qd_asin_metrics_clicks = src.qd_asin_metrics_clicks
  , tgt.qd_asin_metrics_impression_share = src.qd_asin_metrics_impression_share
  , tgt.qd_asin_metrics_impressions = src.qd_asin_metrics_impressions
  , tgt.qd_brand = src.qd_brand
  , tgt.qd_click_count = src.qd_click_count
  , tgt.qd_click_share = src.qd_click_share
  , tgt.qd_impression_count = src.qd_impression_count
  , tgt.qd_impression_share = src.qd_impression_share
  , tgt.qd_price = src.qd_price
  , tgt.qd_product_title = src.qd_product_title
  , tgt.qd_query_metrics_click_rate = src.qd_query_metrics_click_rate
  , tgt.qd_query_metrics_query_volume = src.qd_query_metrics_query_volume
  , tgt.qd_query_metrics_search_query = src.qd_query_metrics_search_query
  , tgt.qd_query_metrics_total_clicks = src.qd_query_metrics_total_clicks
  , tgt.qd_query_metrics_total_impressions = src.qd_query_metrics_total_impressions
  , tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at
  , tgt.etl_batch_run_time = src.etl_batch_run_time
  , tgt.file_name = src.file_name
  , tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk
  , asin
  , brand_code
  , country_code
  , marketplace_id
  , netsuite_item_number
  , seller_id
  , weekly_week_start_date
  , weekly_week_end_date
  , weekly_week_name
  , scraper_id
  , asin_name
  , currency_code
  , qd_asin
  , qd_asin_metrics_click_share
  , qd_asin_metrics_clicks
  , qd_asin_metrics_impression_share
  , qd_asin_metrics_impressions
  , qd_brand
  , qd_click_count
  , qd_click_share
  , qd_impression_count
  , qd_impression_share
  , qd_price
  , qd_product_title
  , qd_query_metrics_click_rate
  , qd_query_metrics_query_volume
  , qd_query_metrics_search_query
  , qd_query_metrics_total_clicks
  , qd_query_metrics_total_impressions
  , report_fetched_and_loaded_at
  , etl_batch_run_time
  , file_name
  , record_created_timestamp_utc
  , record_updated_timestamp_utc
)
VALUES
(
    src.pk
  , src.asin
  , src.brand_code
  , src.country_code
  , src.market_place_id
  , src.netsuite_item_number
  , src.seller_id
  , src.weekly_week_start_date
  , src.weekly_week_end_date
  , src.weekly_week_name
  , src.scraper_id
  , src.asin_name
  , src.currency_code
  , src.qd_asin
  , src.qd_asin_metrics_click_share
  , src.qd_asin_metrics_clicks
  , src.qd_asin_metrics_impression_share
  , src.qd_asin_metrics_impressions
  , src.qd_brand
  , src.qd_click_count
  , src.qd_click_share
  , src.qd_impression_count
  , src.qd_impression_share
  , src.qd_price
  , src.qd_product_title
  , src.qd_query_metrics_click_rate
  , src.qd_query_metrics_query_volume
  , src.qd_query_metrics_search_query
  , src.qd_query_metrics_total_clicks
  , src.qd_query_metrics_total_impressions
  , src.report_fetched_and_loaded_at
  , src.etl_batch_run_time
  , src.file_name
  , SYSDATE()
  , SYSDATE()
);

-- Backfill brand_code
UPDATE $stage_db.merge_asins_keyword_details_report AS tgt
SET tgt.brand_code = src.brand_code
  , tgt.record_updated_timestamp_utc = SYSDATE()
FROM dwh.prod.marketplace_asin_brand_mapping AS src
WHERE src.asin = tgt.asin
  AND src.country_code = tgt.country_code
  AND tgt.brand_code IS NULL
  AND src.brand_code IS NOT NULL;

-- Backfill netsuite_item_number
UPDATE $stage_db.merge_asins_keyword_details_report AS tgt
SET tgt.netsuite_item_number = src.netsuite_item_number
  , tgt.record_updated_timestamp_utc = SYSDATE()
FROM dwh.prod.asin_item_mapping AS src
WHERE src.asin = tgt.asin
  AND src.brand_code = tgt.brand_code
  AND src.country_code = tgt.country_code
  AND tgt.netsuite_item_number IS NULL
  AND src.netsuite_item_number IS NOT NULL;

CREATE TABLE IF NOT EXISTS $stage_db.merge_asin_view_report AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc
       , '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    
    FROM $stage_db.dedupe_asin_view_report
    
    WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_asin_view_report AS tgt
USING
    
    $stage_db.dedupe_asin_view_report AS src
    
    ON 1 = 1
    AND src.pk = tgt.pk 
WHEN MATCHED
    AND src.report_fetched_and_loaded_at >= tgt.report_fetched_and_loaded_at THEN
UPDATE SET
    
    tgt.pk = src.pk,
    tgt.qp_asin_one_day_shipping_cart_adds = src.qp_asin_one_day_shipping_cart_adds,
    tgt.qp_asin_cart_adds = src.qp_asin_cart_adds,
    tgt.qp_click_rate = src.qp_click_rate,
    tgt.qp_asin_query_rank = src.qp_asin_query_rank,
    tgt.qp_asin_clicks = src.qp_asin_clicks,
    tgt.qp_asin_query_volume = src.qp_asin_query_volume,
    tgt.qp_asin_median_price_clicks = src.qp_asin_median_price_clicks,
    tgt.qp_asin_share_impressions = src.qp_asin_share_impressions,
    tgt.qp_asin_count_clicks = src.qp_asin_count_clicks,
    tgt.qp_asin_purchases = src.qp_asin_purchases,
    tgt.qp_asin_count_impressions = src.qp_asin_count_impressions,
    tgt.qp_asin_two_day_shipping_clicks = src.qp_asin_two_day_shipping_clicks,
    tgt.qp_asin_query = src.qp_asin_query,
    tgt.qp_asin_count_purchases = src.qp_asin_count_purchases,
    tgt.qp_asin_impressions = src.qp_asin_impressions,
    tgt.qp_asin_median_query_price_cart_adds = src.qp_asin_median_query_price_cart_adds,
    tgt.qp_asin_share_clicks = src.qp_asin_share_clicks,
    tgt.qp_asin_two_day_shipping_cart_adds = src.qp_asin_two_day_shipping_cart_adds,
    tgt.qp_asin_share_cart_adds = src.qp_asin_share_cart_adds,
    tgt.qp_asin_one_day_shipping_purchases = src.qp_asin_one_day_shipping_purchases,
    tgt.qp_asin_median_price_purchases = src.qp_asin_median_price_purchases,
    tgt.qp_asin_same_day_shipping_purchases = src.qp_asin_same_day_shipping_purchases,
    tgt.qp_asin_same_day_shipping_cart_adds = src.qp_asin_same_day_shipping_cart_adds,
    tgt.qp_asin_purchase_rate = src.qp_asin_purchase_rate,
    tgt.qp_asin_median_query_price_clicks = src.qp_asin_median_query_price_clicks,
    tgt.qp_asin_count_cart_adds = src.qp_asin_count_cart_adds,
    tgt.qp_asin_one_day_shipping_clicks = src.qp_asin_one_day_shipping_clicks,
    tgt.qp_asin_cart_add_rate = src.qp_asin_cart_add_rate,
    tgt.qp_asin_same_day_shipping_clicks = src.qp_asin_same_day_shipping_clicks,
    tgt.qp_asin_median_query_price_purchases = src.qp_asin_median_query_price_purchases,
    tgt.qp_asin_two_day_shipping_purchases = src.qp_asin_two_day_shipping_purchases,
    tgt.qp_asin_median_price_cart_adds = src.qp_asin_median_price_cart_adds,
    tgt.qp_asin_share_purchases = src.qp_asin_share_purchases,
    tgt.asin = src.asin,
    tgt.asin_name = src.asin_name,
    tgt.weekly_week_start_date = src.weekly_week_start_date,
    tgt.weekly_week_end_date = src.weekly_week_end_date,
    tgt.weekly_week_name = src.weekly_week_name,
    tgt.scraper_id = src.scraper_id,
    tgt.seller_id = src.seller_id,
    tgt.market_place_id = src.market_place_id,
    tgt.country_code = src.country_code,
    tgt.currency_code = src.currency_code,
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    
    pk,
    qp_asin_one_day_shipping_cart_adds,
    qp_asin_cart_adds,
    qp_click_rate,
    qp_asin_query_rank,
    qp_asin_clicks,
    qp_asin_query_volume,
    qp_asin_median_price_clicks,
    qp_asin_share_impressions,
    qp_asin_count_clicks,
    qp_asin_purchases,
    qp_asin_count_impressions,
    qp_asin_two_day_shipping_clicks,
    qp_asin_query,
    qp_asin_count_purchases,
    qp_asin_impressions,
    qp_asin_median_query_price_cart_adds,
    qp_asin_share_clicks,
    qp_asin_two_day_shipping_cart_adds,
    qp_asin_share_cart_adds,
    qp_asin_one_day_shipping_purchases,
    qp_asin_median_price_purchases,
    qp_asin_same_day_shipping_purchases,
    qp_asin_same_day_shipping_cart_adds,
    qp_asin_purchase_rate,
    qp_asin_median_query_price_clicks,
    qp_asin_count_cart_adds,
    qp_asin_one_day_shipping_clicks,
    qp_asin_cart_add_rate,
    qp_asin_same_day_shipping_clicks,
    qp_asin_median_query_price_purchases,
    qp_asin_two_day_shipping_purchases,
    qp_asin_median_price_cart_adds,
    qp_asin_share_purchases,
    asin,
    asin_name,
    weekly_week_start_date,
    weekly_week_end_date,
    weekly_week_name,
    scraper_id,
    seller_id,
    market_place_id,
    country_code,
    currency_code,
    report_fetched_and_loaded_at,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
     
    src.pk,
    src.qp_asin_one_day_shipping_cart_adds, 
    src.qp_asin_cart_adds, 
    src.qp_click_rate, 
    src.qp_asin_query_rank, 
    src.qp_asin_clicks, 
    src.qp_asin_query_volume, 
    src.qp_asin_median_price_clicks, 
    src.qp_asin_share_impressions, 
    src.qp_asin_count_clicks, 
    src.qp_asin_purchases, 
    src.qp_asin_count_impressions, 
    src.qp_asin_two_day_shipping_clicks, 
    src.qp_asin_query, 
    src.qp_asin_count_purchases, 
    src.qp_asin_impressions, 
    src.qp_asin_median_query_price_cart_adds, 
    src.qp_asin_share_clicks, 
    src.qp_asin_two_day_shipping_cart_adds, 
    src.qp_asin_share_cart_adds, 
    src.qp_asin_one_day_shipping_purchases, 
    src.qp_asin_median_price_purchases, 
    src.qp_asin_same_day_shipping_purchases, 
    src.qp_asin_same_day_shipping_cart_adds, 
    src.qp_asin_purchase_rate, 
    src.qp_asin_median_query_price_clicks, 
    src.qp_asin_count_cart_adds, 
    src.qp_asin_one_day_shipping_clicks, 
    src.qp_asin_cart_add_rate, 
    src.qp_asin_same_day_shipping_clicks, 
    src.qp_asin_median_query_price_purchases, 
    src.qp_asin_two_day_shipping_purchases, 
    src.qp_asin_median_price_cart_adds, 
    src.qp_asin_share_purchases, 
    src.asin, 
    src.asin_name, 
    src.weekly_week_start_date, 
    src.weekly_week_end_date, 
    src.weekly_week_name, 
    src.scraper_id, 
    src.seller_id, 
    src.market_place_id, 
    src.country_code, 
    src.currency_code, 
    src.report_fetched_and_loaded_at, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
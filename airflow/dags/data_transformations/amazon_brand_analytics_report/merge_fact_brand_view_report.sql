MERGE INTO
    $curated_db.fact_brand_view_report AS tgt
USING (
    SELECT 
           f.pk
         , f.qp_click_rate
         , f.qp_query_rank
         , f.qp_impressions
         , f.qp_brand_count_impressions
         , f.qp_purchase_rate
         , f.qp_cart_adds_brand_share
         , f.qp_same_day_shipping_cart_adds
         , f.qp_same_day_shipping_purchases
         , f.qp_two_day_shipping_clicks
         , f.qp_one_day_shipping_clicks
         , f.qp_brand_share_clicks
         , f.qp_purchases_brand_price
         , f.qp_purchase_price
         , f.qp_clicks_brand_price
         , f.qp_clicks
         , f.qp_two_day_shipping_purchases
         , f.qp_cart_adds
         , f.qp_one_day_shipping_cart_adds
         , f.qp_purchases_brand_share
         , f.qp_brand_query
         , f.qp_cart_adds_price
         , f.qp_purchases_brand_count
         , f.qp_same_day_shipping_clicks
         , f.qp_one_day_shipping_purchases
         , f.qp_query_volume
         , f.qp_cart_adds_brand_count
         , f.qp_clicks_price
         , f.qp_cart_adds_brand_price
         , f.qp_two_day_shipping_cart_adds
         , f.qp_purchases
         , f.qp_brand_share_impressions
         , f.qp_brand_count_clicks
         , f.qp_cart_add_rate
         , f.sub_brand_value
         , f.sub_brand_name
         , b.brand_code
         , f.weekly_week_start_date
         , f.weekly_week_end_date
         , f.weekly_week_name
         , f.seller_id
         , f.country_code
         , f.currency_code
         , f.report_fetched_and_loaded_at
         , f.file_name
         , 'dwh.staging.merge_brand_view_report' AS data_source
         , 'amazon_brand_analytics_report dag' AS created_by
         , 'amazon_brand_analytics_report dag' AS updated_by   
FROM $stage_db.merge_brand_view_report f 
LEFT JOIN dwh.prod.amazon_brand_mapping  b 
    ON  f.sub_brand_name = b.brand_name 
    AND f.seller_id = b.seller_id
WHERE f.record_updated_timestamp_utc > TO_TIMESTAMP_NTZ('$start_ts')
) src
  ON src.pk = tgt.pk 
  WHEN MATCHED THEN 
  UPDATE SET
      tgt.qp_click_rate = src.qp_click_rate
    , tgt.qp_query_rank = src.qp_query_rank
    , tgt.qp_impressions = src.qp_impressions
    , tgt.qp_brand_count_impressions = src.qp_brand_count_impressions
    , tgt.qp_purchase_rate = src.qp_purchase_rate
    , tgt.qp_cart_adds_brand_share = src.qp_cart_adds_brand_share
    , tgt.qp_same_day_shipping_cart_adds = src.qp_same_day_shipping_cart_adds
    , tgt.qp_same_day_shipping_purchases = src.qp_same_day_shipping_purchases
    , tgt.qp_two_day_shipping_clicks = src.qp_two_day_shipping_clicks
    , tgt.qp_one_day_shipping_clicks = src.qp_one_day_shipping_clicks
    , tgt.qp_brand_share_clicks = src.qp_brand_share_clicks
    , tgt.qp_purchases_brand_price = src.qp_purchases_brand_price
    , tgt.qp_purchase_price = src.qp_purchase_price
    , tgt.qp_clicks_brand_price = src.qp_clicks_brand_price
    , tgt.qp_clicks = src.qp_clicks
    , tgt.qp_two_day_shipping_purchases = src.qp_two_day_shipping_purchases
    , tgt.qp_cart_adds = src.qp_cart_adds
    , tgt.qp_one_day_shipping_cart_adds = src.qp_one_day_shipping_cart_adds
    , tgt.qp_purchases_brand_share = src.qp_purchases_brand_share
    , tgt.qp_brand_query = src.qp_brand_query
    , tgt.qp_cart_adds_price = src.qp_cart_adds_price
    , tgt.qp_purchases_brand_count = src.qp_purchases_brand_count
    , tgt.qp_same_day_shipping_clicks = src.qp_same_day_shipping_clicks
    , tgt.qp_one_day_shipping_purchases = src.qp_one_day_shipping_purchases
    , tgt.qp_query_volume = src.qp_query_volume
    , tgt.qp_cart_adds_brand_count = src.qp_cart_adds_brand_count
    , tgt.qp_clicks_price = src.qp_clicks_price
    , tgt.qp_cart_adds_brand_price = src.qp_cart_adds_brand_price
    , tgt.qp_two_day_shipping_cart_adds = src.qp_two_day_shipping_cart_adds
    , tgt.qp_purchases = src.qp_purchases
    , tgt.qp_brand_share_impressions = src.qp_brand_share_impressions
    , tgt.qp_brand_count_clicks = src.qp_brand_count_clicks
    , tgt.qp_cart_add_rate = src.qp_cart_add_rate
    , tgt.sub_brand_value = src.sub_brand_value
    , tgt.sub_brand_name = src.sub_brand_name
    , tgt.brand_code = src.brand_code
    , tgt.weekly_week_start_date = src.weekly_week_start_date
    , tgt.weekly_week_end_date = src.weekly_week_end_date
    , tgt.weekly_week_name = src.weekly_week_name
    , tgt.seller_id = src.seller_id
    , tgt.country_code = src.country_code
    , tgt.currency_code = src.currency_code
    , tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at
    , tgt.file_name = src.file_name
    , tgt.data_source = src.data_source
    , tgt.updated_by = src.updated_by
    , tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT 
(
       pk
     , qp_click_rate
     , qp_query_rank
     , qp_impressions
     , qp_brand_count_impressions
     , qp_purchase_rate
     , qp_cart_adds_brand_share
     , qp_same_day_shipping_cart_adds
     , qp_same_day_shipping_purchases
     , qp_two_day_shipping_clicks
     , qp_one_day_shipping_clicks
     , qp_brand_share_clicks
     , qp_purchases_brand_price
     , qp_purchase_price
     , qp_clicks_brand_price
     , qp_clicks
     , qp_two_day_shipping_purchases
     , qp_cart_adds
     , qp_one_day_shipping_cart_adds
     , qp_purchases_brand_share
     , qp_brand_query
     , qp_cart_adds_price
     , qp_purchases_brand_count
     , qp_same_day_shipping_clicks
     , qp_one_day_shipping_purchases
     , qp_query_volume
     , qp_cart_adds_brand_count
     , qp_clicks_price
     , qp_cart_adds_brand_price
     , qp_two_day_shipping_cart_adds
     , qp_purchases
     , qp_brand_share_impressions
     , qp_brand_count_clicks
     , qp_cart_add_rate
     , sub_brand_value
     , sub_brand_name
     , brand_code
     , weekly_week_start_date
     , weekly_week_end_date
     , weekly_week_name
     , seller_id
     , country_code
     , currency_code
     , report_fetched_and_loaded_at
     , file_name
     , data_source
     , created_by
     , updated_by
     , record_created_timestamp_utc
     , record_updated_timestamp_utc  
)
VALUES
(
       src.pk
     , src.qp_click_rate
     , src.qp_query_rank
     , src.qp_impressions
     , src.qp_brand_count_impressions
     , src.qp_purchase_rate
     , src.qp_cart_adds_brand_share
     , src.qp_same_day_shipping_cart_adds
     , src.qp_same_day_shipping_purchases
     , src.qp_two_day_shipping_clicks
     , src.qp_one_day_shipping_clicks
     , src.qp_brand_share_clicks
     , src.qp_purchases_brand_price
     , src.qp_purchase_price
     , src.qp_clicks_brand_price
     , src.qp_clicks
     , src.qp_two_day_shipping_purchases
     , src.qp_cart_adds
     , src.qp_one_day_shipping_cart_adds
     , src.qp_purchases_brand_share
     , src.qp_brand_query
     , src.qp_cart_adds_price
     , src.qp_purchases_brand_count
     , src.qp_same_day_shipping_clicks
     , src.qp_one_day_shipping_purchases
     , src.qp_query_volume
     , src.qp_cart_adds_brand_count
     , src.qp_clicks_price
     , src.qp_cart_adds_brand_price
     , src.qp_two_day_shipping_cart_adds
     , src.qp_purchases
     , src.qp_brand_share_impressions
     , src.qp_brand_count_clicks
     , src.qp_cart_add_rate
     , src.sub_brand_value
     , src.sub_brand_name
     , src.brand_code
     , src.weekly_week_start_date
     , src.weekly_week_end_date
     , src.weekly_week_name
     , src.seller_id
     , src.country_code
     , src.currency_code
     , src.report_fetched_and_loaded_at
     , src.file_name
     , src.data_source
     , src.created_by
     , src.updated_by
     , SYSDATE()
     , SYSDATE() 
);

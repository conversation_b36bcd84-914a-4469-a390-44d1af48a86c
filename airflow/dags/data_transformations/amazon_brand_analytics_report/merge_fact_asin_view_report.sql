MERGE INTO
    $curated_db.fact_asin_view_report AS tgt
USING (
    SELECT
          f.pk
        , f.qp_asin_one_day_shipping_cart_adds
        , f.qp_asin_cart_adds
        , f.qp_click_rate
        , f.qp_asin_query_rank
        , f.qp_asin_clicks
        , f.qp_asin_query_volume
        , f.qp_asin_median_price_clicks
        , f.qp_asin_share_impressions
        , f.qp_asin_count_clicks
        , f.qp_asin_purchases
        , f.qp_asin_count_impressions
        , f.qp_asin_two_day_shipping_clicks
        , f.qp_asin_query
        , f.qp_asin_count_purchases
        , f.qp_asin_impressions
        , f.qp_asin_median_query_price_cart_adds
        , f.qp_asin_share_clicks
        , f.qp_asin_two_day_shipping_cart_adds
        , f.qp_asin_share_cart_adds
        , f.qp_asin_one_day_shipping_purchases
        , f.qp_asin_median_price_purchases
        , f.qp_asin_same_day_shipping_purchases
        , f.qp_asin_same_day_shipping_cart_adds
        , f.qp_asin_purchase_rate
        , f.qp_asin_median_query_price_clicks
        , f.qp_asin_count_cart_adds
        , f.qp_asin_one_day_shipping_clicks
        , f.qp_asin_cart_add_rate
        , f.qp_asin_same_day_shipping_clicks
        , f.qp_asin_median_query_price_purchases
        , f.qp_asin_two_day_shipping_purchases
        , f.qp_asin_median_price_cart_adds
        , f.qp_asin_share_purchases
        , f.asin
        , f.asin_name
        , b.brand_code
        , f.weekly_week_start_date
        , f.weekly_week_end_date
        , f.weekly_week_name
        , f.seller_id
        , f.country_code
        , f.currency_code
        , f.report_fetched_and_loaded_at
        , f.file_name
        , 'dwh.staging.merge_asin_view_report' AS data_source
        , 'amazon_brand_analytics_report dag' AS created_by
        , 'amazon_brand_analytics_report dag' AS updated_by   
    FROM dwh.staging.merge_asin_view_report f 
    LEFT JOIN dwh.prod.marketplace_asin_brand_mapping b 
        ON  f.asin = b.asin 
        AND f.country_code = b.country_code
    WHERE f.record_updated_timestamp_utc > TO_TIMESTAMP_NTZ('$start_ts')
) src
  ON src.pk = tgt.pk 
WHEN MATCHED THEN 
UPDATE SET
      tgt.qp_asin_one_day_shipping_cart_adds = src.qp_asin_one_day_shipping_cart_adds
    , tgt.qp_asin_cart_adds = src.qp_asin_cart_adds
    , tgt.qp_click_rate = src.qp_click_rate
    , tgt.qp_asin_query_rank = src.qp_asin_query_rank
    , tgt.qp_asin_clicks = src.qp_asin_clicks
    , tgt.qp_asin_query_volume = src.qp_asin_query_volume
    , tgt.qp_asin_median_price_clicks = src.qp_asin_median_price_clicks
    , tgt.qp_asin_share_impressions = src.qp_asin_share_impressions
    , tgt.qp_asin_count_clicks = src.qp_asin_count_clicks
    , tgt.qp_asin_purchases = src.qp_asin_purchases
    , tgt.qp_asin_count_impressions = src.qp_asin_count_impressions
    , tgt.qp_asin_two_day_shipping_clicks = src.qp_asin_two_day_shipping_clicks
    , tgt.qp_asin_query = src.qp_asin_query
    , tgt.qp_asin_count_purchases = src.qp_asin_count_purchases
    , tgt.qp_asin_impressions = src.qp_asin_impressions
    , tgt.qp_asin_median_query_price_cart_adds = src.qp_asin_median_query_price_cart_adds
    , tgt.qp_asin_share_clicks = src.qp_asin_share_clicks
    , tgt.qp_asin_two_day_shipping_cart_adds = src.qp_asin_two_day_shipping_cart_adds
    , tgt.qp_asin_share_cart_adds = src.qp_asin_share_cart_adds
    , tgt.qp_asin_one_day_shipping_purchases = src.qp_asin_one_day_shipping_purchases
    , tgt.qp_asin_median_price_purchases = src.qp_asin_median_price_purchases
    , tgt.qp_asin_same_day_shipping_purchases = src.qp_asin_same_day_shipping_purchases
    , tgt.qp_asin_same_day_shipping_cart_adds = src.qp_asin_same_day_shipping_cart_adds
    , tgt.qp_asin_purchase_rate = src.qp_asin_purchase_rate
    , tgt.qp_asin_median_query_price_clicks = src.qp_asin_median_query_price_clicks
    , tgt.qp_asin_count_cart_adds = src.qp_asin_count_cart_adds
    , tgt.qp_asin_one_day_shipping_clicks = src.qp_asin_one_day_shipping_clicks
    , tgt.qp_asin_cart_add_rate = src.qp_asin_cart_add_rate
    , tgt.qp_asin_same_day_shipping_clicks = src.qp_asin_same_day_shipping_clicks
    , tgt.qp_asin_median_query_price_purchases = src.qp_asin_median_query_price_purchases
    , tgt.qp_asin_two_day_shipping_purchases = src.qp_asin_two_day_shipping_purchases
    , tgt.qp_asin_median_price_cart_adds = src.qp_asin_median_price_cart_adds
    , tgt.qp_asin_share_purchases = src.qp_asin_share_purchases
    , tgt.asin = src.asin
    , tgt.asin_name = src.asin_name
    , tgt.brand_code= src.brand_code
    , tgt.weekly_week_start_date = src.weekly_week_start_date
    , tgt.weekly_week_end_date = src.weekly_week_end_date
    , tgt.weekly_week_name = src.weekly_week_name
    , tgt.seller_id = src.seller_id
    , tgt.country_code = src.country_code
    , tgt.currency_code = src.currency_code
    , tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at
    , tgt.file_name = src.file_name
    , tgt.data_source = src.data_source
    , tgt.updated_by = src.updated_by
    , tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT 
(
      pk
    , qp_asin_one_day_shipping_cart_adds
    , qp_asin_cart_adds
    , qp_click_rate
    , qp_asin_query_rank
    , qp_asin_clicks
    , qp_asin_query_volume
    , qp_asin_median_price_clicks
    , qp_asin_share_impressions
    , qp_asin_count_clicks
    , qp_asin_purchases
    , qp_asin_count_impressions
    , qp_asin_two_day_shipping_clicks
    , qp_asin_query
    , qp_asin_count_purchases
    , qp_asin_impressions
    , qp_asin_median_query_price_cart_adds
    , qp_asin_share_clicks
    , qp_asin_two_day_shipping_cart_adds
    , qp_asin_share_cart_adds
    , qp_asin_one_day_shipping_purchases
    , qp_asin_median_price_purchases
    , qp_asin_same_day_shipping_purchases
    , qp_asin_same_day_shipping_cart_adds
    , qp_asin_purchase_rate
    , qp_asin_median_query_price_clicks
    , qp_asin_count_cart_adds
    , qp_asin_one_day_shipping_clicks
    , qp_asin_cart_add_rate
    , qp_asin_same_day_shipping_clicks
    , qp_asin_median_query_price_purchases
    , qp_asin_two_day_shipping_purchases
    , qp_asin_median_price_cart_adds
    , qp_asin_share_purchases
    , asin
    , asin_name
    , brand_code
    , weekly_week_start_date
    , weekly_week_end_date
    , weekly_week_name
    , seller_id
    , country_code
    , currency_code
    , report_fetched_and_loaded_at
    , file_name
    , data_source
    , created_by
    , updated_by
    , record_created_timestamp_utc
    , record_updated_timestamp_utc  
)
VALUES
(
      pk
    , qp_asin_one_day_shipping_cart_adds
    , qp_asin_cart_adds
    , qp_click_rate
    , qp_asin_query_rank
    , qp_asin_clicks
    , qp_asin_query_volume
    , qp_asin_median_price_clicks
    , qp_asin_share_impressions
    , qp_asin_count_clicks
    , qp_asin_purchases
    , qp_asin_count_impressions
    , qp_asin_two_day_shipping_clicks
    , qp_asin_query
    , qp_asin_count_purchases
    , qp_asin_impressions
    , qp_asin_median_query_price_cart_adds
    , qp_asin_share_clicks
    , qp_asin_two_day_shipping_cart_adds
    , qp_asin_share_cart_adds
    , qp_asin_one_day_shipping_purchases
    , qp_asin_median_price_purchases
    , qp_asin_same_day_shipping_purchases
    , qp_asin_same_day_shipping_cart_adds
    , qp_asin_purchase_rate
    , qp_asin_median_query_price_clicks
    , qp_asin_count_cart_adds
    , qp_asin_one_day_shipping_clicks
    , qp_asin_cart_add_rate
    , qp_asin_same_day_shipping_clicks
    , qp_asin_median_query_price_purchases
    , qp_asin_two_day_shipping_purchases
    , qp_asin_median_price_cart_adds
    , qp_asin_share_purchases
    , asin
    , asin_name
    , brand_code
    , weekly_week_start_date
    , weekly_week_end_date
    , weekly_week_name
    , seller_id
    , country_code
    , currency_code
    , report_fetched_and_loaded_at
    , file_name
    , data_source
    , created_by
    , updated_by
    , SYSDATE()
    , SYSDATE() 
);

UPDATE $curated_db.fact_asin_view_report f
SET brand_code = b.brand_code
FROM dwh.prod.marketplace_asin_brand_mapping b 
WHERE   f.asin = b.asin 
	AND f.country_code = b.country_code
	AND f.brand_code IS NULL 
	AND b.brand_code IS NOT NULL;

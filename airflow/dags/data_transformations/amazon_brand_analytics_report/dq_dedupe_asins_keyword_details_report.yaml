---
test_id: "is_null_dedupe_pk"
enabled: true
query: |
  SELECT CASE WHEN count(*) = count(pk) THEN 0 ELSE 2 END AS "result"
  FROM $stage_db.dedupe_asins_keyword_details_report;
---
test_id: "is_unique_dedupe_pk"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1) > 0 THEN 2 ELSE 0 END as "result"
  FROM (SELECT 1
        FROM $stage_db.dedupe_asins_keyword_details_report
        GROUP BY pk
        HAVING COUNT(1) > 1
        LIMIT 1
  ) T;

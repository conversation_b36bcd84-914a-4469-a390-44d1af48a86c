
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_asins_keyword_details_report AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(seller_id AS VARCHAR), ''), '-',
            COALESCE(CAST(asin AS VARCHAR), ''), '-',
            COALESCE(CAST(qd_query_metrics_search_query AS VARCHAR), ''), '-',
            COALESCE(CAST(qd_asin AS VARCHAR), ''), '-',
            COALESCE(CAST(market_place_id AS VARCHAR), ''), '-',
            COALESCE(CAST(weekly_week_end_date AS VARCHAR), '')
        )) AS pk
      , asin
      , asin_name
      , country_code
      , market_place_id
      , scraper_id
      , seller_id
      , weekly_week_start_date
      , weekly_week_end_date
      , weekly_week_name
      , currency_code
      , qd_asin
      , qd_brand
      , qd_product_title
      , qd_query_metrics_search_query
      , TRY_TO_DECIMAL(qd_asin_metrics_impression_share, 10, 2) AS qd_asin_metrics_impression_share
      , TRY_TO_DECIMAL(qd_query_metrics_click_rate, 10, 2) AS qd_query_metrics_click_rate
      , TRY_TO_NUMBER(qd_asin_metrics_impressions) AS qd_asin_metrics_impressions
      , TRY_TO_DECIMAL(qd_asin_metrics_click_share, 10, 2) AS qd_asin_metrics_click_share
      , TRY_TO_NUMBER(qd_query_metrics_total_clicks) AS qd_query_metrics_total_clicks
      , TRY_TO_NUMBER(qd_query_metrics_query_volume) AS qd_query_metrics_query_volume
      , TRY_TO_NUMBER(qd_query_metrics_total_impressions) AS qd_query_metrics_total_impressions
      , TRY_TO_NUMBER(qd_asin_metrics_clicks) AS qd_asin_metrics_clicks
      , TRY_TO_DECIMAL(qd_price, 10, 2) AS qd_price
      , TRY_TO_DECIMAL(qd_impression_share, 10, 2) AS qd_impression_share
      , TRY_TO_NUMBER(qd_impression_count) AS qd_impression_count
      , TRY_TO_DECIMAL(qd_click_share, 10, 2) AS qd_click_share
      , TRY_TO_NUMBER(qd_click_count) AS qd_click_count
      , report_fetched_and_loaded_at
      , file_name
      , etl_batch_run_time
    FROM $raw_db.raw_asins_keyword_details_report
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY seller_id, asin, qd_query_metrics_search_query, qd_asin, market_place_id, weekly_week_end_date
        ORDER BY report_fetched_and_loaded_at DESC NULLS LAST) = 1
);

---
test_id: "is_null_raw_pk"
enabled: true
query: |
  SELECT CASE WHEN COUNT(*) = COUNT(seller_id)
               AND COUNT(*) = COUNT(asin)
               AND COUNT(*) = COUNT(market_place_id)
               AND COUNT(*) = COUNT(weekly_week_end_date)
              THEN 0 ELSE 2
         END AS "result"
  FROM $raw_db.raw_catalogue_performance_report;
---
test_id: "is_unique_raw_pk"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1) > 0 THEN 2 ELSE 0 END as "result"
  FROM (SELECT 1
        FROM $raw_db.raw_catalogue_performance_report
        GROUP BY seller_id, asin, market_place_id, weekly_week_end_date, report_fetched_and_loaded_at
        HAVING COUNT(1) > 1
        LIMIT 1
  ) T;

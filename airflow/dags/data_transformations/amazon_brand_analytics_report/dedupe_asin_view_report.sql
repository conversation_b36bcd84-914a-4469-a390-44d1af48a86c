
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_asin_view_report AS (
    SELECT
        
        --  primary key  --
        MD5(CONCAT(COALESCE(CAST(seller_id AS VARCHAR), ''), '-',
        COALESCE(CAST(asin AS VARCHAR), ''), '-',
        COALESCE(CAST(qp_asin_query AS VARCHAR), ''), '-',
        COALESCE(CAST(market_place_id AS VARCHAR), ''), '-',
        COALESCE(CAST(weekly_week_end_date AS VARCHAR), '')
        )) AS pk,
        
        TRY_TO_NUMBER(qp_asin_one_day_shipping_cart_adds) AS qp_asin_one_day_shipping_cart_adds,
        TRY_TO_NUMBER(qp_asin_cart_adds) AS qp_asin_cart_adds,
        TRY_TO_DECIMAL(qp_click_rate, 10, 2) AS qp_click_rate,
        TRY_TO_NUMBER(qp_asin_query_rank) AS qp_asin_query_rank,
        TRY_TO_NUMBER(qp_asin_clicks) AS qp_asin_clicks,
        TRY_TO_NUMBER(qp_asin_query_volume) AS qp_asin_query_volume,
        TRY_TO_DECIMAL(qp_asin_median_price_clicks, 10, 2) AS qp_asin_median_price_clicks,
        TRY_TO_DECIMAL(qp_asin_share_impressions, 10, 2) AS qp_asin_share_impressions,
        TRY_TO_NUMBER(qp_asin_count_clicks) AS qp_asin_count_clicks,
        TRY_TO_NUMBER(qp_asin_purchases) AS qp_asin_purchases,
        TRY_TO_NUMBER(qp_asin_count_impressions) AS qp_asin_count_impressions,
        TRY_TO_NUMBER(qp_asin_two_day_shipping_clicks) AS qp_asin_two_day_shipping_clicks,
        qp_asin_query,
        TRY_TO_NUMBER(qp_asin_count_purchases) AS qp_asin_count_purchases,
        TRY_TO_NUMBER(qp_asin_impressions) AS qp_asin_impressions,
        TRY_TO_DECIMAL(qp_asin_median_query_price_cart_adds, 10, 2) AS qp_asin_median_query_price_cart_adds,
        TRY_TO_DECIMAL(qp_asin_share_clicks, 10, 2) AS qp_asin_share_clicks,
        TRY_TO_NUMBER(qp_asin_two_day_shipping_cart_adds) AS qp_asin_two_day_shipping_cart_adds,
        TRY_TO_DECIMAL(qp_asin_share_cart_adds, 10, 2) AS qp_asin_share_cart_adds,
        TRY_TO_NUMBER(qp_asin_one_day_shipping_purchases) AS qp_asin_one_day_shipping_purchases,
        TRY_TO_DECIMAL(qp_asin_median_price_purchases, 10, 2) AS qp_asin_median_price_purchases,
        TRY_TO_NUMBER(qp_asin_same_day_shipping_purchases) AS qp_asin_same_day_shipping_purchases,
        TRY_TO_NUMBER(qp_asin_same_day_shipping_cart_adds) AS qp_asin_same_day_shipping_cart_adds,
        TRY_TO_DECIMAL(qp_asin_purchase_rate, 10, 2) AS qp_asin_purchase_rate,
        TRY_TO_DECIMAL(qp_asin_median_query_price_clicks, 10, 2) AS qp_asin_median_query_price_clicks,
        TRY_TO_NUMBER(qp_asin_count_cart_adds) AS qp_asin_count_cart_adds,
        TRY_TO_NUMBER(qp_asin_one_day_shipping_clicks) AS qp_asin_one_day_shipping_clicks,
        TRY_TO_DECIMAL(qp_asin_cart_add_rate, 10, 2) AS qp_asin_cart_add_rate,
        TRY_TO_NUMBER(qp_asin_same_day_shipping_clicks) AS qp_asin_same_day_shipping_clicks,
        TRY_TO_DECIMAL(qp_asin_median_query_price_purchases, 10, 2) AS qp_asin_median_query_price_purchases,
        TRY_TO_NUMBER(qp_asin_two_day_shipping_purchases) AS qp_asin_two_day_shipping_purchases,
        TRY_TO_DECIMAL(qp_asin_median_price_cart_adds, 10, 2) AS qp_asin_median_price_cart_adds,
        TRY_TO_DECIMAL(qp_asin_share_purchases, 10, 2) AS qp_asin_share_purchases,
        asin,
        asin_name,
        weekly_week_start_date,
        weekly_week_end_date,
        weekly_week_name,
        scraper_id,
        seller_id,
        market_place_id,
        country_code,
        currency_code,
        report_fetched_and_loaded_at,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_asin_view_report
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY seller_id, asin, qp_asin_query, market_place_id, weekly_week_end_date
        ORDER BY report_fetched_and_loaded_at DESC NULLS LAST) = 1
);

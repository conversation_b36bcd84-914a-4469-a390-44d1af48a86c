MERGE INTO $stage_db.merge_catalogue_performance_report AS tgt
USING (
    SELECT
        d.*
      , bm.brand_code
      , im.netsuite_item_number
    FROM $stage_db.dedupe_catalogue_performance_report AS d
    LEFT JOIN dwh.prod.marketplace_asin_brand_mapping AS bm
        ON d.asin = bm.asin
       AND d.country_code = bm.country_code
    LEFT JOIN dwh.prod.asin_item_mapping AS im
        ON d.asin = im.asin
       AND d.country_code = im.country_code
       AND bm.brand_code = im.brand_code
) AS src
    ON src.pk = tgt.pk 
WHEN MATCHED
    AND src.report_fetched_and_loaded_at >= tgt.report_fetched_and_loaded_at THEN
UPDATE SET
    tgt.asin = src.asin
  , tgt.brand_code = src.brand_code
  , tgt.country_code = src.country_code
  , tgt.marketplace_id = src.marketplace_id
  , tgt.netsuite_item_number = src.netsuite_item_number
  , tgt.seller_id = src.seller_id
  , tgt.weekly_week_start_date = src.weekly_week_start_date
  , tgt.weekly_week_end_date = src.weekly_week_end_date
  , tgt.weekly_week_name = src.weekly_week_name
  , tgt.scraper_id = src.scraper_id
  , tgt.currency_code = src.currency_code
  , tgt.asin_title = src.asin_title
  , tgt.category = src.category
  , tgt.cart_adds_count = src.cart_adds_count
  , tgt.cart_adds_price = src.cart_adds_price
  , tgt.click_price = src.click_price
  , tgt.clicks = src.clicks
  , tgt.conversion_rate = src.conversion_rate
  , tgt.ctr_clicks = src.ctr_clicks
  , tgt.impression_price = src.impression_price
  , tgt.impressions_count = src.impressions_count
  , tgt.one_day_shipping_cart_adds = src.one_day_shipping_cart_adds
  , tgt.one_day_shipping_clicks = src.one_day_shipping_clicks
  , tgt.one_day_shipping_impressions = src.one_day_shipping_impressions
  , tgt.one_day_shipping_purchases = src.one_day_shipping_purchases
  , tgt.purchase_price = src.purchase_price
  , tgt.purchases_count = src.purchases_count
  , tgt.rating_impressions = src.rating_impressions
  , tgt.rating_purchases = src.rating_purchases
  , tgt.same_day_shipping_cart_adds = src.same_day_shipping_cart_adds
  , tgt.same_day_shipping_clicks = src.same_day_shipping_clicks
  , tgt.same_day_shipping_impressions = src.same_day_shipping_impressions
  , tgt.same_day_shipping_purchases = src.same_day_shipping_purchases
  , tgt.total_sales_purchases = src.total_sales_purchases
  , tgt.two_day_shipping_cart_adds = src.two_day_shipping_cart_adds
  , tgt.two_day_shipping_clicks = src.two_day_shipping_clicks
  , tgt.two_day_shipping_impressions = src.two_day_shipping_impressions
  , tgt.two_day_shipping_purchases = src.two_day_shipping_purchases
  , tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at
  , tgt.file_name = src.file_name
  , tgt.etl_batch_run_time = src.etl_batch_run_time
  , tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk
  , asin
  , brand_code
  , country_code
  , marketplace_id
  , netsuite_item_number
  , seller_id
  , weekly_week_start_date
  , weekly_week_end_date
  , weekly_week_name
  , scraper_id
  , currency_code
  , asin_title
  , category
  , cart_adds_count
  , cart_adds_price
  , click_price
  , clicks
  , conversion_rate
  , ctr_clicks
  , impression_price
  , impressions_count
  , one_day_shipping_cart_adds
  , one_day_shipping_clicks
  , one_day_shipping_impressions
  , one_day_shipping_purchases
  , purchase_price
  , purchases_count
  , rating_impressions
  , rating_purchases
  , same_day_shipping_cart_adds
  , same_day_shipping_clicks
  , same_day_shipping_impressions
  , same_day_shipping_purchases
  , total_sales_purchases
  , two_day_shipping_cart_adds
  , two_day_shipping_clicks
  , two_day_shipping_impressions
  , two_day_shipping_purchases
  , report_fetched_and_loaded_at
  , file_name
  , etl_batch_run_time
  , record_created_timestamp_utc
  , record_updated_timestamp_utc
)
VALUES
(   
    src.pk
  , src.asin
  , src.brand_code
  , src.country_code
  , src.marketplace_id
  , src.netsuite_item_number
  , src.seller_id
  , src.weekly_week_start_date
  , src.weekly_week_end_date
  , src.weekly_week_name
  , src.scraper_id
  , src.currency_code
  , src.asin_title
  , src.category
  , src.cart_adds_count
  , src.cart_adds_price
  , src.click_price
  , src.clicks
  , src.conversion_rate
  , src.ctr_clicks
  , src.impression_price
  , src.impressions_count
  , src.one_day_shipping_cart_adds
  , src.one_day_shipping_clicks
  , src.one_day_shipping_impressions
  , src.one_day_shipping_purchases
  , src.purchase_price
  , src.purchases_count
  , src.rating_impressions
  , src.rating_purchases
  , src.same_day_shipping_cart_adds
  , src.same_day_shipping_clicks
  , src.same_day_shipping_impressions
  , src.same_day_shipping_purchases
  , src.total_sales_purchases
  , src.two_day_shipping_cart_adds
  , src.two_day_shipping_clicks
  , src.two_day_shipping_impressions
  , src.two_day_shipping_purchases
  , src.report_fetched_and_loaded_at
  , src.file_name
  , src.etl_batch_run_time
  , SYSDATE()
  , SYSDATE()
);

-- Backfill brand_code
UPDATE $stage_db.merge_catalogue_performance_report AS tgt
SET tgt.brand_code = src.brand_code
  , tgt.record_updated_timestamp_utc = SYSDATE()
FROM dwh.prod.marketplace_asin_brand_mapping AS src
WHERE src.asin = tgt.asin
  AND src.country_code = tgt.country_code
  AND tgt.brand_code IS NULL
  AND src.brand_code IS NOT NULL;

-- Backfill netsuite_item_number
UPDATE $stage_db.merge_catalogue_performance_report AS tgt
SET tgt.netsuite_item_number = src.netsuite_item_number
  , tgt.record_updated_timestamp_utc = SYSDATE()
FROM dwh.prod.asin_item_mapping AS src
WHERE src.asin = tgt.asin
  AND src.brand_code = tgt.brand_code
  AND src.country_code = tgt.country_code
  AND tgt.netsuite_item_number IS NULL
  AND src.netsuite_item_number IS NOT NULL;

---
test_id: "is_null_fact_pk"
enabled: true
query: |
  SELECT CASE WHEN count(*) = count(pk) THEN 0 ELSE 2 END AS "result"
  FROM $curated_db.fact_asins_keyword_details_report;
---
test_id: "is_unique_fact_pk"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1) > 0 THEN 2 ELSE 0 END as "result"
  FROM (SELECT 1
        FROM $curated_db.fact_asins_keyword_details_report
        GROUP BY pk
        HAVING COUNT(1) > 1
        LIMIT 1
  ) T;

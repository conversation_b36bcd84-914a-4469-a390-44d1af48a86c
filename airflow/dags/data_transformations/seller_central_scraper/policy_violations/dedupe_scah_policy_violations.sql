
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_scah_policy_violations AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(seller_id AS VARCHAR), ''), '-',
            COALESCE(CAST(asin AS VARCHAR), ''), '-',
            COALESCE(CAST(start_date_requested AS VARCHAR), ''), '-',
            COALESCE(CAST(end_date_requested AS VARCHAR), '')
            )) AS pk,
        id,
        title,
        asin,
        TRY_TO_TIMESTAMP(federation_format_date) AS federation_format_date,
        date_at,
        TRY_TO_TIMESTAMP(due_date) AS due_date,
        reason_message,
        gated,
        enforcement_type,
        appeal_id,
        appeal_status,
        appeal_token,
        appeal_enforcement_type,
        appeal_redirect_url,
        reinstatement_path,
        case_id,
        reason,
        spa_reason_code,
        appeals_email,
        removal_type_string_id,
        brand_name,
        next_steps,
        document_class,
        rule_name,
        sku,
        enforced,
        appeal_enforcement_id,
        appeal_enforcement_namespace,
        metric_name,
        issue_id,
        issue_type,
        issue_hash,
        issue_source_target,
        issue_source_artifact_id,
        issue_target_target,
        issue_target_artifact_id,
        issue_param_policy_version,
        issue_param_ahr_version,
        issue_param_policy_id,
        issue_param_ahr_weight,
        issue_param_program_name,
        issue_param_enforcer_metadata,
        issue_param_policy_name,
        issue_param_message_id,
        issue_param_base_ahr_weight,
        issue_param_client_id,
        issue_impact_date,
        issue_last_asserted_date,
        issue_marketplace_id,
        issue_exemption,
        issue_enforcements,
        instance_type,
        severity,
        TRY_TO_TIMESTAMP(report_fetched_and_loaded_at) AS report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        TRY_TO_TIMESTAMP(start_date_requested) AS start_date_requested,
        TRY_TO_TIMESTAMP(end_date_requested) AS end_date_requested,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_scah_policy_violations
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY seller_id, asin, start_date_requested, end_date_requested
        ORDER BY federation_format_date DESC NULLS LAST) = 1
);
CREATE TABLE IF NOT EXISTS $curated_db.fact_scah_policy_violations (
	PK VARCHAR(32),
	ID VARCHAR,
	TITLE VARCHAR,
	ASIN VARCHAR,
	FEDERATION_FORMAT_<PERSON><PERSON><PERSON> TIMESTAMP,
	DATE_AT VARCHAR,
	<PERSON><PERSON>_<PERSON><PERSON>E TIMESTAMP,
	<PERSON><PERSON>SO<PERSON>_MESSAGE VARCHAR,
	GATED VARCHAR,
	<PERSON>NFORCEMENT_TYPE VARCHAR,
	APPEAL_ID VARCHAR,
	APPEAL_STATUS VARCHAR,
	APPEAL_TOKEN VARCHAR,
	APPEAL_ENFORCEMENT_TYPE VARCHAR,
	APPEAL_REDIRECT_URL VARCHAR,
	REI<PERSON>TATEMENT_PATH VARCHAR,
	CASE_ID VARCHAR,
	<PERSON><PERSON><PERSON><PERSON> VARCHAR,
	SPA_REASON_CODE VARCHAR,
	APPEALS_EMAIL VARCHAR,
	REMOVAL_TYPE_STRING_ID VARCHAR,
	BRAND_NAME VARCHAR,
	NEXT_STEPS VARCHAR,
	DOCUMENT_CLASS VARCHAR,
	RU<PERSON>_<PERSON>AM<PERSON> VARCHAR,
	<PERSON><PERSON> VARCHAR,
	<PERSON><PERSON>ORCE<PERSON> VARCHAR,
	APPEAL_ENFORCEMENT_ID VARCHAR,
	APPEAL_ENFORCEMENT_NAMESPACE VARCHAR,
	METRIC_NAME VARCHAR,
	ISSUE_ID VARCHAR,
	ISSUE_TYPE VARCHAR,
	ISSUE_HASH VARCHAR,
	ISSUE_SOURCE_TARGET VARCHAR,
	ISSUE_SOURCE_ARTIFACT_ID VARCHAR,
	ISSUE_TARGET_TARGET VARCHAR,
	ISSUE_TARGET_ARTIFACT_ID VARCHAR,
	ISSUE_PARAM_POLICY_VERSION VARCHAR,
	ISSUE_PARAM_AHR_VERSION VARCHAR,
	ISSUE_PARAM_POLICY_ID VARCHAR,
	ISSUE_PARAM_AHR_WEIGHT VARCHAR,
	ISSUE_PARAM_PROGRAM_NAME VARCHAR,
	ISSUE_PARAM_ENFORCER_METADATA VARCHAR,
	ISSUE_PARAM_POLICY_NAME VARCHAR,
	ISSUE_PARAM_MESSAGE_ID VARCHAR,
	ISSUE_PARAM_BASE_AHR_WEIGHT VARCHAR,
	ISSUE_PARAM_CLIENT_ID VARCHAR,
	ISSUE_IMPACT_DATE VARCHAR,
	ISSUE_LAST_ASSERTED_DATE VARCHAR,
	ISSUE_MARKETPLACE_ID VARCHAR,
	ISSUE_EXEMPTION VARCHAR,
	ISSUE_ENFORCEMENTS VARCHAR,
	INSTANCE_TYPE VARCHAR,
	SEVERITY VARCHAR,
	REPORT_FETCHED_AND_LOADED_AT TIMESTAMP,
	SCRAPER_ID VARCHAR,
	SELLER_ID VARCHAR,
    SELLER_NAME VARCHAR,
	START_DATE_REQUESTED TIMESTAMP,
	END_DATE_REQUESTED TIMESTAMP,
    FILE_NAME VARCHAR,
	DATA_SOURCE VARCHAR,
	CREATED_BY VARCHAR DEFAULT 'DAG: fact_scah_policy_violations',
	UPDATED_BY VARCHAR DEFAULT 'DAG: fact_scah_policy_violations',
	RECORD_CREATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE(),
	RECORD_UPDATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE()
);


MERGE INTO $curated_db.fact_scah_policy_violations AS tgt
USING (
    SELECT 
        pv.PK,
        pv.ID,
        pv.TITLE,
        pv.ASIN,
        pv.FEDERATION_FORMAT_DATE,
        pv.DATE_AT,
        pv.DUE_DATE,
        pv.REASON_MESSAGE,
        pv.GATED,
        pv.ENFORCEMENT_TYPE,
        pv.APPEAL_ID,
        pv.APPEAL_STATUS,
        pv.APPEAL_TOKEN,
        pv.APPEAL_ENFORCEMENT_TYPE,
        pv.APPEAL_REDIRECT_URL,
        pv.REINSTATEMENT_PATH,
        pv.CASE_ID,
        pv.REASON,
        pv.SPA_REASON_CODE,
        pv.APPEALS_EMAIL,
        pv.REMOVAL_TYPE_STRING_ID,
        pv.BRAND_NAME,
        pv.NEXT_STEPS,
        pv.DOCUMENT_CLASS,
        pv.RULE_NAME,
        pv.SKU,
        pv.ENFORCED,
        pv.APPEAL_ENFORCEMENT_ID,
        pv.APPEAL_ENFORCEMENT_NAMESPACE,
        pv.METRIC_NAME,
        pv.ISSUE_ID,
        pv.ISSUE_TYPE,
        pv.ISSUE_HASH,
        pv.ISSUE_SOURCE_TARGET,
        pv.ISSUE_SOURCE_ARTIFACT_ID,
        pv.ISSUE_TARGET_TARGET,
        pv.ISSUE_TARGET_ARTIFACT_ID,
        pv.ISSUE_PARAM_POLICY_VERSION,
        pv.ISSUE_PARAM_AHR_VERSION,
        pv.ISSUE_PARAM_POLICY_ID,
        pv.ISSUE_PARAM_AHR_WEIGHT,
        pv.ISSUE_PARAM_PROGRAM_NAME,
        pv.ISSUE_PARAM_ENFORCER_METADATA,
        pv.ISSUE_PARAM_POLICY_NAME,
        pv.ISSUE_PARAM_MESSAGE_ID,
        pv.ISSUE_PARAM_BASE_AHR_WEIGHT,
        pv.ISSUE_PARAM_CLIENT_ID,
        pv.ISSUE_IMPACT_DATE,
        pv.ISSUE_LAST_ASSERTED_DATE,
        pv.ISSUE_MARKETPLACE_ID,
        pv.ISSUE_EXEMPTION,
        pv.ISSUE_ENFORCEMENTS,
        pv.INSTANCE_TYPE,
        pv.SEVERITY,
        pv.REPORT_FETCHED_AND_LOADED_AT,
        pv.SCRAPER_ID,
        pv.SELLER_ID,
        si.SELLER_NAME,
        pv.START_DATE_REQUESTED,
        pv.END_DATE_REQUESTED,
        pv.FILE_NAME,
        'dwh.staging.merge_scah_policy_violations' AS DATA_SOURCE,
        'DAG: fact_scah_policy_violations' AS CREATED_BY,
        'DAG: fact_scah_policy_violations' AS UPDATED_BY,
        pv.RECORD_CREATED_TIMESTAMP_UTC,
        pv.RECORD_UPDATED_TIMESTAMP_UTC
    FROM $stage_db.merge_scah_policy_violations pv
    LEFT JOIN $stage_db.stg_seller_info si ON pv.SELLER_ID = si.SELLER_ID
) AS src
ON tgt.pk = src.pk
WHEN MATCHED 
    AND src.RECORD_UPDATED_TIMESTAMP_UTC > tgt.RECORD_UPDATED_TIMESTAMP_UTC
THEN UPDATE SET
    tgt.ID = src.ID,
    tgt.TITLE = src.TITLE,
    tgt.ASIN = src.ASIN,
    tgt.FEDERATION_FORMAT_DATE = src.FEDERATION_FORMAT_DATE,
    tgt.DATE_AT = src.DATE_AT,
    tgt.DUE_DATE = src.DUE_DATE,
    tgt.REASON_MESSAGE = src.REASON_MESSAGE,
    tgt.GATED = src.GATED,
    tgt.ENFORCEMENT_TYPE = src.ENFORCEMENT_TYPE,
    tgt.APPEAL_ID = src.APPEAL_ID,
    tgt.APPEAL_STATUS = src.APPEAL_STATUS,
    tgt.APPEAL_TOKEN = src.APPEAL_TOKEN,
    tgt.APPEAL_ENFORCEMENT_TYPE = src.APPEAL_ENFORCEMENT_TYPE,
    tgt.APPEAL_REDIRECT_URL = src.APPEAL_REDIRECT_URL,
    tgt.REINSTATEMENT_PATH = src.REINSTATEMENT_PATH,
    tgt.CASE_ID = src.CASE_ID,
    tgt.REASON = src.REASON,
    tgt.SPA_REASON_CODE = src.SPA_REASON_CODE,
    tgt.APPEALS_EMAIL = src.APPEALS_EMAIL,
    tgt.REMOVAL_TYPE_STRING_ID = src.REMOVAL_TYPE_STRING_ID,
    tgt.BRAND_NAME = src.BRAND_NAME,
    tgt.NEXT_STEPS = src.NEXT_STEPS,
    tgt.DOCUMENT_CLASS = src.DOCUMENT_CLASS,
    tgt.RULE_NAME = src.RULE_NAME,
    tgt.SKU = src.SKU,
    tgt.ENFORCED = src.ENFORCED,
    tgt.APPEAL_ENFORCEMENT_ID = src.APPEAL_ENFORCEMENT_ID,
    tgt.APPEAL_ENFORCEMENT_NAMESPACE = src.APPEAL_ENFORCEMENT_NAMESPACE,
    tgt.METRIC_NAME = src.METRIC_NAME,
    tgt.ISSUE_ID = src.ISSUE_ID,
    tgt.ISSUE_TYPE = src.ISSUE_TYPE,
    tgt.ISSUE_HASH = src.ISSUE_HASH,
    tgt.ISSUE_SOURCE_TARGET = src.ISSUE_SOURCE_TARGET,
    tgt.ISSUE_SOURCE_ARTIFACT_ID = src.ISSUE_SOURCE_ARTIFACT_ID,
    tgt.ISSUE_TARGET_TARGET = src.ISSUE_TARGET_TARGET,
    tgt.ISSUE_TARGET_ARTIFACT_ID = src.ISSUE_TARGET_ARTIFACT_ID,
    tgt.ISSUE_PARAM_POLICY_VERSION = src.ISSUE_PARAM_POLICY_VERSION,
    tgt.ISSUE_PARAM_AHR_VERSION = src.ISSUE_PARAM_AHR_VERSION,
    tgt.ISSUE_PARAM_POLICY_ID = src.ISSUE_PARAM_POLICY_ID,
    tgt.ISSUE_PARAM_AHR_WEIGHT = src.ISSUE_PARAM_AHR_WEIGHT,
    tgt.ISSUE_PARAM_PROGRAM_NAME = src.ISSUE_PARAM_PROGRAM_NAME,
    tgt.ISSUE_PARAM_ENFORCER_METADATA = src.ISSUE_PARAM_ENFORCER_METADATA,
    tgt.ISSUE_PARAM_POLICY_NAME = src.ISSUE_PARAM_POLICY_NAME,
    tgt.ISSUE_PARAM_MESSAGE_ID = src.ISSUE_PARAM_MESSAGE_ID,
    tgt.ISSUE_PARAM_BASE_AHR_WEIGHT = src.ISSUE_PARAM_BASE_AHR_WEIGHT,
    tgt.ISSUE_PARAM_CLIENT_ID = src.ISSUE_PARAM_CLIENT_ID,
    tgt.ISSUE_IMPACT_DATE = src.ISSUE_IMPACT_DATE,
    tgt.ISSUE_LAST_ASSERTED_DATE = src.ISSUE_LAST_ASSERTED_DATE,
    tgt.ISSUE_MARKETPLACE_ID = src.ISSUE_MARKETPLACE_ID,
    tgt.ISSUE_EXEMPTION = src.ISSUE_EXEMPTION,
    tgt.ISSUE_ENFORCEMENTS = src.ISSUE_ENFORCEMENTS,
    tgt.INSTANCE_TYPE = src.INSTANCE_TYPE,
    tgt.SEVERITY = src.SEVERITY,
    tgt.REPORT_FETCHED_AND_LOADED_AT = src.REPORT_FETCHED_AND_LOADED_AT,
    tgt.SCRAPER_ID = src.SCRAPER_ID,
    tgt.SELLER_ID = src.SELLER_ID,
    tgt.SELLER_NAME = src.SELLER_NAME,
    tgt.START_DATE_REQUESTED = src.START_DATE_REQUESTED,
    tgt.END_DATE_REQUESTED = src.END_DATE_REQUESTED,
    tgt.FILE_NAME = src.FILE_NAME,
    tgt.DATA_SOURCE = src.DATA_SOURCE,
    tgt.UPDATED_BY = src.UPDATED_BY,
    tgt.RECORD_UPDATED_TIMESTAMP_UTC = SYSDATE()

WHEN NOT MATCHED THEN INSERT (
    PK,
    ID,
    TITLE,
    ASIN,
    FEDERATION_FORMAT_DATE,
    DATE_AT,
    DUE_DATE,
    REASON_MESSAGE,
    GATED,
    ENFORCEMENT_TYPE,
    APPEAL_ID,
    APPEAL_STATUS,
    APPEAL_TOKEN,
    APPEAL_ENFORCEMENT_TYPE,
    APPEAL_REDIRECT_URL,
    REINSTATEMENT_PATH,
    CASE_ID,
    REASON,
    SPA_REASON_CODE,
    APPEALS_EMAIL,
    REMOVAL_TYPE_STRING_ID,
    BRAND_NAME,
    NEXT_STEPS,
    DOCUMENT_CLASS,
    RULE_NAME,
    SKU,
    ENFORCED,
    APPEAL_ENFORCEMENT_ID,
    APPEAL_ENFORCEMENT_NAMESPACE,
    METRIC_NAME,
    ISSUE_ID,
    ISSUE_TYPE,
    ISSUE_HASH,
    ISSUE_SOURCE_TARGET,
    ISSUE_SOURCE_ARTIFACT_ID,
    ISSUE_TARGET_TARGET,
    ISSUE_TARGET_ARTIFACT_ID,
    ISSUE_PARAM_POLICY_VERSION,
    ISSUE_PARAM_AHR_VERSION,
    ISSUE_PARAM_POLICY_ID,
    ISSUE_PARAM_AHR_WEIGHT,
    ISSUE_PARAM_PROGRAM_NAME,
    ISSUE_PARAM_ENFORCER_METADATA,
    ISSUE_PARAM_POLICY_NAME,
    ISSUE_PARAM_MESSAGE_ID,
    ISSUE_PARAM_BASE_AHR_WEIGHT,
    ISSUE_PARAM_CLIENT_ID,
    ISSUE_IMPACT_DATE,
    ISSUE_LAST_ASSERTED_DATE,
    ISSUE_MARKETPLACE_ID,
    ISSUE_EXEMPTION,
    ISSUE_ENFORCEMENTS,
    INSTANCE_TYPE,
    SEVERITY,
    REPORT_FETCHED_AND_LOADED_AT,
    SCRAPER_ID,
    SELLER_ID,
    SELLER_NAME,
    START_DATE_REQUESTED,
    END_DATE_REQUESTED,
    FILE_NAME,
    DATA_SOURCE,
    CREATED_BY,
    UPDATED_BY,
    RECORD_CREATED_TIMESTAMP_UTC,
    RECORD_UPDATED_TIMESTAMP_UTC
)
VALUES (
    src.PK,
    src.ID,
    src.TITLE,
    src.ASIN,
    src.FEDERATION_FORMAT_DATE,
    src.DATE_AT,
    src.DUE_DATE,
    src.REASON_MESSAGE,
    src.GATED,
    src.ENFORCEMENT_TYPE,
    src.APPEAL_ID,
    src.APPEAL_STATUS,
    src.APPEAL_TOKEN,
    src.APPEAL_ENFORCEMENT_TYPE,
    src.APPEAL_REDIRECT_URL,
    src.REINSTATEMENT_PATH,
    src.CASE_ID,
    src.REASON,
    src.SPA_REASON_CODE,
    src.APPEALS_EMAIL,
    src.REMOVAL_TYPE_STRING_ID,
    src.BRAND_NAME,
    src.NEXT_STEPS,
    src.DOCUMENT_CLASS,
    src.RULE_NAME,
    src.SKU,
    src.ENFORCED,
    src.APPEAL_ENFORCEMENT_ID,
    src.APPEAL_ENFORCEMENT_NAMESPACE,
    src.METRIC_NAME,
    src.ISSUE_ID,
    src.ISSUE_TYPE,
    src.ISSUE_HASH,
    src.ISSUE_SOURCE_TARGET,
    src.ISSUE_SOURCE_ARTIFACT_ID,
    src.ISSUE_TARGET_TARGET,
    src.ISSUE_TARGET_ARTIFACT_ID,
    src.ISSUE_PARAM_POLICY_VERSION,
    src.ISSUE_PARAM_AHR_VERSION,
    src.ISSUE_PARAM_POLICY_ID,
    src.ISSUE_PARAM_AHR_WEIGHT,
    src.ISSUE_PARAM_PROGRAM_NAME,
    src.ISSUE_PARAM_ENFORCER_METADATA,
    src.ISSUE_PARAM_POLICY_NAME,
    src.ISSUE_PARAM_MESSAGE_ID,
    src.ISSUE_PARAM_BASE_AHR_WEIGHT,
    src.ISSUE_PARAM_CLIENT_ID,
    src.ISSUE_IMPACT_DATE,
    src.ISSUE_LAST_ASSERTED_DATE,
    src.ISSUE_MARKETPLACE_ID,
    src.ISSUE_EXEMPTION,
    src.ISSUE_ENFORCEMENTS,
    src.INSTANCE_TYPE,
    src.SEVERITY,
    src.REPORT_FETCHED_AND_LOADED_AT,
    src.SCRAPER_ID,
    src.SELLER_ID,
    src.SELLER_NAME,
    src.START_DATE_REQUESTED,
    src.END_DATE_REQUESTED,
    src.FILE_NAME,
    src.DATA_SOURCE,
    src.CREATED_BY,
    src.UPDATED_BY,
    SYSDATE(),
    SYSDATE()
);
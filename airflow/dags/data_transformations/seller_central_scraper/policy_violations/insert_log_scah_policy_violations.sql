CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_scah_policy_violations AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_scah_policy_violations
    WHERE 1 = 0;

INSERT INTO $raw_db.log_scah_policy_violations (
    id,
    title,
    asin,
    federation_format_date,
    date_at,
    due_date,
    reason_message,
    gated,
    enforcement_type,
    appeal_id,
    appeal_status,
    appeal_token,
    appeal_enforcement_type,
    appeal_redirect_url,
    reinstatement_path,
    case_id,
    reason,
    spa_reason_code,
    appeals_email,
    removal_type_string_id,
    brand_name,
    next_steps,
    document_class,
    rule_name,
    sku,
    enforced,
    appeal_enforcement_id,
    appeal_enforcement_namespace,
    metric_name,
    issue_id,
    issue_type,
    issue_hash,
    issue_source_target,
    issue_source_artifact_id,
    issue_target_target,
    issue_target_artifact_id,
    issue_param_policy_version,
    issue_param_ahr_version,
    issue_param_policy_id,
    issue_param_ahr_weight,
    issue_param_program_name,
    issue_param_enforcer_metadata,
    issue_param_policy_name,
    issue_param_message_id,
    issue_param_base_ahr_weight,
    issue_param_client_id,
    issue_impact_date,
    issue_last_asserted_date,
    issue_marketplace_id,
    issue_exemption,
    issue_enforcements,
    instance_type,
    severity,
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    start_date_requested,
    end_date_requested,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        id,
        title,
        asin,
        federation_format_date,
        date_at,
        due_date,
        reason_message,
        gated,
        enforcement_type,
        appeal_id,
        appeal_status,
        appeal_token,
        appeal_enforcement_type,
        appeal_redirect_url,
        reinstatement_path,
        case_id,
        reason,
        spa_reason_code,
        appeals_email,
        removal_type_string_id,
        brand_name,
        next_steps,
        document_class,
        rule_name,
        sku,
        enforced,
        appeal_enforcement_id,
        appeal_enforcement_namespace,
        metric_name,
        issue_id,
        issue_type,
        issue_hash,
        issue_source_target,
        issue_source_artifact_id,
        issue_target_target,
        issue_target_artifact_id,
        issue_param_policy_version,
        issue_param_ahr_version,
        issue_param_policy_id,
        issue_param_ahr_weight,
        issue_param_program_name,
        issue_param_enforcer_metadata,
        issue_param_policy_name,
        issue_param_message_id,
        issue_param_base_ahr_weight,
        issue_param_client_id,
        issue_impact_date,
        issue_last_asserted_date,
        issue_marketplace_id,
        issue_exemption,
        issue_enforcements,
        instance_type,
        severity,
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        start_date_requested,
        end_date_requested,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_scah_policy_violations;

CREATE TABLE IF NOT EXISTS $stage_db.merge_scah_policy_violations AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_scah_policy_violations
    WHERE 1 = 0;


MERGE INTO
    $stage_db.merge_scah_policy_violations AS tgt
USING
    $stage_db.dedupe_scah_policy_violations AS src
    
        ON 1 = 1
       AND src.pk = tgt.pk
       
WHEN MATCHED THEN 
UPDATE SET
    tgt.pk = src.pk,
    tgt.id = src.id,
    tgt.title = src.title,
    tgt.asin = src.asin,
    tgt.federation_format_date = src.federation_format_date,
    tgt.date_at = src.date_at,
    tgt.due_date = src.due_date,
    tgt.reason_message = src.reason_message,
    tgt.gated = src.gated,
    tgt.enforcement_type = src.enforcement_type,
    tgt.appeal_id = src.appeal_id,
    tgt.appeal_status = src.appeal_status,
    tgt.appeal_token = src.appeal_token,
    tgt.appeal_enforcement_type = src.appeal_enforcement_type,
    tgt.appeal_redirect_url = src.appeal_redirect_url,
    tgt.reinstatement_path = src.reinstatement_path,
    tgt.case_id = src.case_id,
    tgt.reason = src.reason,
    tgt.spa_reason_code = src.spa_reason_code,
    tgt.appeals_email = src.appeals_email,
    tgt.removal_type_string_id = src.removal_type_string_id,
    tgt.brand_name = src.brand_name,
    tgt.next_steps = src.next_steps,
    tgt.document_class = src.document_class,
    tgt.rule_name = src.rule_name,
    tgt.sku = src.sku,
    tgt.enforced = src.enforced,
    tgt.appeal_enforcement_id = src.appeal_enforcement_id,
    tgt.appeal_enforcement_namespace = src.appeal_enforcement_namespace,
    tgt.metric_name = src.metric_name,
    tgt.issue_id = src.issue_id,
    tgt.issue_type = src.issue_type,
    tgt.issue_hash = src.issue_hash,
    tgt.issue_source_target = src.issue_source_target,
    tgt.issue_source_artifact_id = src.issue_source_artifact_id,
    tgt.issue_target_target = src.issue_target_target,
    tgt.issue_target_artifact_id = src.issue_target_artifact_id,
    tgt.issue_param_policy_version = src.issue_param_policy_version,
    tgt.issue_param_ahr_version = src.issue_param_ahr_version,
    tgt.issue_param_policy_id = src.issue_param_policy_id,
    tgt.issue_param_ahr_weight = src.issue_param_ahr_weight,
    tgt.issue_param_program_name = src.issue_param_program_name,
    tgt.issue_param_enforcer_metadata = src.issue_param_enforcer_metadata,
    tgt.issue_param_policy_name = src.issue_param_policy_name,
    tgt.issue_param_message_id = src.issue_param_message_id,
    tgt.issue_param_base_ahr_weight = src.issue_param_base_ahr_weight,
    tgt.issue_param_client_id = src.issue_param_client_id,
    tgt.issue_impact_date = src.issue_impact_date,
    tgt.issue_last_asserted_date = src.issue_last_asserted_date,
    tgt.issue_marketplace_id = src.issue_marketplace_id,
    tgt.issue_exemption = src.issue_exemption,
    tgt.issue_enforcements = src.issue_enforcements,
    tgt.instance_type = src.instance_type,
    tgt.severity = src.severity,
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.scraper_id = src.scraper_id,
    tgt.seller_id = src.seller_id,
    tgt.start_date_requested = src.start_date_requested,
    tgt.end_date_requested = src.end_date_requested,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    id,
    title,
    asin,
    federation_format_date,
    date_at,
    due_date,
    reason_message,
    gated,
    enforcement_type,
    appeal_id,
    appeal_status,
    appeal_token,
    appeal_enforcement_type,
    appeal_redirect_url,
    reinstatement_path,
    case_id,
    reason,
    spa_reason_code,
    appeals_email,
    removal_type_string_id,
    brand_name,
    next_steps,
    document_class,
    rule_name,
    sku,
    enforced,
    appeal_enforcement_id,
    appeal_enforcement_namespace,
    metric_name,
    issue_id,
    issue_type,
    issue_hash,
    issue_source_target,
    issue_source_artifact_id,
    issue_target_target,
    issue_target_artifact_id,
    issue_param_policy_version,
    issue_param_ahr_version,
    issue_param_policy_id,
    issue_param_ahr_weight,
    issue_param_program_name,
    issue_param_enforcer_metadata,
    issue_param_policy_name,
    issue_param_message_id,
    issue_param_base_ahr_weight,
    issue_param_client_id,
    issue_impact_date,
    issue_last_asserted_date,
    issue_marketplace_id,
    issue_exemption,
    issue_enforcements,
    instance_type,
    severity,
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    start_date_requested,
    end_date_requested,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src.id, 
    src.title, 
    src.asin, 
    src.federation_format_date, 
    src.date_at, 
    src.due_date, 
    src.reason_message, 
    src.gated, 
    src.enforcement_type, 
    src.appeal_id, 
    src.appeal_status, 
    src.appeal_token, 
    src.appeal_enforcement_type, 
    src.appeal_redirect_url, 
    src.reinstatement_path, 
    src.case_id, 
    src.reason, 
    src.spa_reason_code, 
    src.appeals_email, 
    src.removal_type_string_id, 
    src.brand_name, 
    src.next_steps, 
    src.document_class, 
    src.rule_name, 
    src.sku, 
    src.enforced, 
    src.appeal_enforcement_id, 
    src.appeal_enforcement_namespace, 
    src.metric_name, 
    src.issue_id, 
    src.issue_type, 
    src.issue_hash, 
    src.issue_source_target, 
    src.issue_source_artifact_id, 
    src.issue_target_target, 
    src.issue_target_artifact_id, 
    src.issue_param_policy_version, 
    src.issue_param_ahr_version, 
    src.issue_param_policy_id, 
    src.issue_param_ahr_weight, 
    src.issue_param_program_name, 
    src.issue_param_enforcer_metadata, 
    src.issue_param_policy_name, 
    src.issue_param_message_id, 
    src.issue_param_base_ahr_weight, 
    src.issue_param_client_id, 
    src.issue_impact_date, 
    src.issue_last_asserted_date, 
    src.issue_marketplace_id, 
    src.issue_exemption, 
    src.issue_enforcements, 
    src.instance_type, 
    src.severity, 
    src.report_fetched_and_loaded_at, 
    src.scraper_id, 
    src.seller_id, 
    src.start_date_requested, 
    src.end_date_requested, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
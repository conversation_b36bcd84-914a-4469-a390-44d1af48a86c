CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_scah_customer_reviews AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(seller_id AS VARCHAR), ''), '-',
            COALESCE(CAST(country AS VARCHAR), ''), '-',
            COALESCE(CAST(review_id AS VARCHAR), ''), '-',
            COALESCE(CAST(report_fetched_and_loaded_at::date AS VARCHAR), '')
            )) AS pk,
        --  metadata fields  --
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        country,
        --  product fields  --
        brand_name,
        asin,
        child_asin,
        asin_title,
        asin_thumbnail,
        asin_weighted_star_rating,
        asin_total_review_count,
        --  review fields  --
        review_id,
        review_title,
        review_text,
        review_rating,
        review_author_public_name,
        review_created_timestamp,
        review_has_images,
        review_is_video,
        review_is_verified_purchase,
        review_is_vine_review,
        review_marked_as_done,
        review_is_replied_to,
        --  order fields  --
        order_id,
        order_timestamp,
        is_seller_of_record,
        is_digital_order,
        is_buyer_opted_out,
        --  etl fields  --
        file_name,
        etl_batch_run_time

    FROM $raw_db.raw_scah_customer_reviews
    WHERE 1=1
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY report_fetched_and_loaded_at DESC NULLS LAST) = 1
);
CREATE TABLE IF NOT EXISTS $curated_db.fact_scah_customer_reviews (
    PK VARCHAR(32),
    -- metadata fields
    REPORT_FETCHED_AND_LOADED_AT VARCHAR,
    <PERSON>RA<PERSON>ER_ID VARCHAR,
    SELLER_ID VARCHAR,
    SE<PERSON>ER_NAME VARCHAR,
    COUNTRY_CODE VARCHAR,
    -- product fields
    BRAND_NAME VARCHAR,
    ASIN VARCHAR,
    CHILD_ASIN VARCHAR,
    ASIN_T<PERSON>LE VARCHAR,
    ASIN_THUMBNAIL VARCHAR,
    ASIN_WEIGHTED_STAR_RATING VARCHAR,
    ASIN_TOTAL_REVIEW_COUNT VARCHAR,
    -- review fields
    REVIEW_ID VARCHAR,
    RE<PERSON>E<PERSON>_TITLE VARCHAR,
    REVIEW_TEXT VARCHAR,
    REVIEW_RATING VARCHAR,
    REVIEW_AUTHOR_PUBLIC_NAME VARCHAR,
    REVIEW_CREATED_TIMESTAMP VARCHAR,
    REVIE<PERSON>_HAS_IMAGES VARCHAR,
    REVIEW_IS_VIDEO VARCHAR,
    RE<PERSON>EW_IS_VERIFIED_PURCHASE VARCHAR,
    REVIEW_IS_VINE_REVIEW VARCHAR,
    REVIEW_MARKED_AS_DONE VARCHAR,
    REVIEW_IS_REPLIED_TO VARCHAR,
    -- order fields
    ORDER_ID VARCHAR,
    ORDER_TIMESTAMP VARCHAR,
    IS_SELLER_OF_RECORD VARCHAR,
    IS_DIGITAL_ORDER VARCHAR,
    IS_BUYER_OPTED_OUT VARCHAR,
    -- etl fields
    FILE_NAME VARCHAR,
    ETL_BATCH_RUN_TIME TIMESTAMP_NTZ,
    DATA_SOURCE VARCHAR,
    CREATED_BY VARCHAR DEFAULT 'DAG: fact_scah_customer_reviews',
    UPDATED_BY VARCHAR DEFAULT 'DAG: fact_scah_customer_reviews',
    RECORD_CREATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE(),
    RECORD_UPDATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE()
);

MERGE INTO $curated_db.fact_scah_customer_reviews target
USING (
    SELECT
        cr.PK,
        cr.REPORT_FETCHED_AND_LOADED_AT,
        cr.SCRAPER_ID,
        cr.SELLER_ID,
        si.SELLER_NAME,
        cr.COUNTRY AS COUNTRY_CODE,
        cr.BRAND_NAME,
        cr.ASIN,
        cr.CHILD_ASIN,
        cr.ASIN_TITLE,
        cr.ASIN_THUMBNAIL,
        cr.ASIN_WEIGHTED_STAR_RATING,
        cr.ASIN_TOTAL_REVIEW_COUNT,
        cr.REVIEW_ID,
        cr.REVIEW_TITLE,
        cr.REVIEW_TEXT,
        cr.REVIEW_RATING,
        cr.REVIEW_AUTHOR_PUBLIC_NAME,
        cr.REVIEW_CREATED_TIMESTAMP,
        cr.REVIEW_HAS_IMAGES,
        cr.REVIEW_IS_VIDEO,
        cr.REVIEW_IS_VERIFIED_PURCHASE,
        cr.REVIEW_IS_VINE_REVIEW,
        cr.REVIEW_MARKED_AS_DONE,
        cr.REVIEW_IS_REPLIED_TO,
        cr.ORDER_ID,
        cr.ORDER_TIMESTAMP,
        cr.IS_SELLER_OF_RECORD,
        cr.IS_DIGITAL_ORDER,
        cr.IS_BUYER_OPTED_OUT,
        cr.FILE_NAME,
        cr.ETL_BATCH_RUN_TIME,
        'dwh.staging.merge_scah_customer_reviews' AS DATA_SOURCE,
        'DAG: fact_scah_customer_reviews' AS CREATED_BY,
        'DAG: fact_scah_customer_reviews' AS UPDATED_BY,
        cr.RECORD_CREATED_TIMESTAMP_UTC,
        cr.RECORD_UPDATED_TIMESTAMP_UTC
    FROM $stage_db.merge_scah_customer_reviews cr
    LEFT JOIN $stage_db.stg_seller_info si ON cr.SELLER_ID = si.SELLER_ID
) source
ON target.PK = source.PK
WHEN MATCHED THEN
    UPDATE SET
        -- metadata fields
        target.REPORT_FETCHED_AND_LOADED_AT = source.REPORT_FETCHED_AND_LOADED_AT,
        target.SCRAPER_ID = source.SCRAPER_ID,
        target.SELLER_ID = source.SELLER_ID,
        target.SELLER_NAME = source.SELLER_NAME,
        target.COUNTRY_CODE = source.COUNTRY_CODE,
        -- product fields
        target.BRAND_NAME = source.BRAND_NAME,
        target.ASIN = source.ASIN,
        target.CHILD_ASIN = source.CHILD_ASIN,
        target.ASIN_TITLE = source.ASIN_TITLE,
        target.ASIN_THUMBNAIL = source.ASIN_THUMBNAIL,
        target.ASIN_WEIGHTED_STAR_RATING = source.ASIN_WEIGHTED_STAR_RATING,
        target.ASIN_TOTAL_REVIEW_COUNT = source.ASIN_TOTAL_REVIEW_COUNT,
        -- review fields
        target.REVIEW_ID = source.REVIEW_ID,
        target.REVIEW_TITLE = source.REVIEW_TITLE,
        target.REVIEW_TEXT = source.REVIEW_TEXT,
        target.REVIEW_RATING = source.REVIEW_RATING,
        target.REVIEW_AUTHOR_PUBLIC_NAME = source.REVIEW_AUTHOR_PUBLIC_NAME,
        target.REVIEW_CREATED_TIMESTAMP = source.REVIEW_CREATED_TIMESTAMP,
        target.REVIEW_HAS_IMAGES = source.REVIEW_HAS_IMAGES,
        target.REVIEW_IS_VIDEO = source.REVIEW_IS_VIDEO,
        target.REVIEW_IS_VERIFIED_PURCHASE = source.REVIEW_IS_VERIFIED_PURCHASE,
        target.REVIEW_IS_VINE_REVIEW = source.REVIEW_IS_VINE_REVIEW,
        target.REVIEW_MARKED_AS_DONE = source.REVIEW_MARKED_AS_DONE,
        target.REVIEW_IS_REPLIED_TO = source.REVIEW_IS_REPLIED_TO,
        -- order fields
        target.ORDER_ID = source.ORDER_ID,
        target.ORDER_TIMESTAMP = source.ORDER_TIMESTAMP,
        target.IS_SELLER_OF_RECORD = source.IS_SELLER_OF_RECORD,
        target.IS_DIGITAL_ORDER = source.IS_DIGITAL_ORDER,
        target.IS_BUYER_OPTED_OUT = source.IS_BUYER_OPTED_OUT,
        -- etl fields
        target.FILE_NAME = source.FILE_NAME,
        target.ETL_BATCH_RUN_TIME = source.ETL_BATCH_RUN_TIME,
        target.DATA_SOURCE = source.DATA_SOURCE,
        target.UPDATED_BY = source.UPDATED_BY,
        target.RECORD_UPDATED_TIMESTAMP_UTC = SYSDATE()
WHEN NOT MATCHED THEN
    INSERT (
        PK,
        -- metadata fields
        REPORT_FETCHED_AND_LOADED_AT,
        SCRAPER_ID,
        SELLER_ID,
        SELLER_NAME,
        COUNTRY_CODE,
        -- product fields
        BRAND_NAME,
        ASIN,
        CHILD_ASIN,
        ASIN_TITLE,
        ASIN_THUMBNAIL,
        ASIN_WEIGHTED_STAR_RATING,
        ASIN_TOTAL_REVIEW_COUNT,
        -- review fields
        REVIEW_ID,
        REVIEW_TITLE,
        REVIEW_TEXT,
        REVIEW_RATING,
        REVIEW_AUTHOR_PUBLIC_NAME,
        REVIEW_CREATED_TIMESTAMP,
        REVIEW_HAS_IMAGES,
        REVIEW_IS_VIDEO,
        REVIEW_IS_VERIFIED_PURCHASE,
        REVIEW_IS_VINE_REVIEW,
        REVIEW_MARKED_AS_DONE,
        REVIEW_IS_REPLIED_TO,
        -- order fields
        ORDER_ID,
        ORDER_TIMESTAMP,
        IS_SELLER_OF_RECORD,
        IS_DIGITAL_ORDER,
        IS_BUYER_OPTED_OUT,
        -- etl fields
        FILE_NAME,
        ETL_BATCH_RUN_TIME,
        DATA_SOURCE,
        CREATED_BY,
        UPDATED_BY,
        RECORD_CREATED_TIMESTAMP_UTC,
        RECORD_UPDATED_TIMESTAMP_UTC
    )
    VALUES (
        source.PK,
        -- metadata fields
        source.REPORT_FETCHED_AND_LOADED_AT,
        source.SCRAPER_ID,
        source.SELLER_ID,
        source.SELLER_NAME,
        source.COUNTRY_CODE,
        -- product fields
        source.BRAND_NAME,
        source.ASIN,
        source.CHILD_ASIN,
        source.ASIN_TITLE,
        source.ASIN_THUMBNAIL,
        source.ASIN_WEIGHTED_STAR_RATING,
        source.ASIN_TOTAL_REVIEW_COUNT,
        -- review fields
        source.REVIEW_ID,
        source.REVIEW_TITLE,
        source.REVIEW_TEXT,
        source.REVIEW_RATING,
        source.REVIEW_AUTHOR_PUBLIC_NAME,
        source.REVIEW_CREATED_TIMESTAMP,
        source.REVIEW_HAS_IMAGES,
        source.REVIEW_IS_VIDEO,
        source.REVIEW_IS_VERIFIED_PURCHASE,
        source.REVIEW_IS_VINE_REVIEW,
        source.REVIEW_MARKED_AS_DONE,
        source.REVIEW_IS_REPLIED_TO,
        -- order fields
        source.ORDER_ID,
        source.ORDER_TIMESTAMP,
        source.IS_SELLER_OF_RECORD,
        source.IS_DIGITAL_ORDER,
        source.IS_BUYER_OPTED_OUT,
        -- etl fields
        source.FILE_NAME,
        source.ETL_BATCH_RUN_TIME,
        source.DATA_SOURCE,
        source.CREATED_BY,
        source.UPDATED_BY,
        SYSDATE(),
        SYSDATE()
    );
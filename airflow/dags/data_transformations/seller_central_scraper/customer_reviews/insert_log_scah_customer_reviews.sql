CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_scah_customer_reviews AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_scah_customer_reviews
    WHERE 1 = 0;

INSERT INTO $raw_db.log_scah_customer_reviews (
    -- metadata fields
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    -- product fields
    brand_name,
    asin,
    child_asin,
    asin_title,
    asin_thumbnail,
    asin_weighted_star_rating,
    asin_total_review_count,
    -- review fields
    review_id,
    review_title,
    review_text,
    review_rating,
    review_author_public_name,
    review_created_timestamp,
    review_has_images,
    review_is_video,
    review_is_verified_purchase,
    review_is_vine_review,
    review_marked_as_done,
    review_is_replied_to,
    -- order fields
    order_id,
    order_timestamp,
    is_seller_of_record,
    is_digital_order,
    is_buyer_opted_out,
    -- etl fields
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        -- metadata fields
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        country,
        -- product fields
        brand_name,
        asin,
        child_asin,
        asin_title,
        asin_thumbnail,
        asin_weighted_star_rating,
        asin_total_review_count,
        -- review fields
        review_id,
        review_title,
        review_text,
        review_rating,
        review_author_public_name,
        review_created_timestamp,
        review_has_images,
        review_is_video,
        review_is_verified_purchase,
        review_is_vine_review,
        review_marked_as_done,
        review_is_replied_to,
        -- order fields
        order_id,
        order_timestamp,
        is_seller_of_record,
        is_digital_order,
        is_buyer_opted_out,
        -- etl fields
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_scah_customer_reviews; 
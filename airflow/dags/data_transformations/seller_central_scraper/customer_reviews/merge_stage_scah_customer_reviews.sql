CREATE TABLE IF NOT EXISTS $stage_db.merge_scah_customer_reviews AS
SELECT
    *,
    '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
    '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
FROM $stage_db.dedupe_scah_customer_reviews
WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_scah_customer_reviews AS tgt
USING
    $stage_db.dedupe_scah_customer_reviews AS src
    ON 1 = 1
    AND src.pk = tgt.pk
WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    -- metadata fields
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.scraper_id = src.scraper_id,
    tgt.seller_id = src.seller_id,
    tgt.country = src.country,
    -- product fields
    tgt.brand_name = src.brand_name,
    tgt.asin = src.asin,
    tgt.child_asin = src.child_asin,
    tgt.asin_title = src.asin_title,
    tgt.asin_thumbnail = src.asin_thumbnail,
    tgt.asin_weighted_star_rating = src.asin_weighted_star_rating,
    tgt.asin_total_review_count = src.asin_total_review_count,
    -- review fields
    tgt.review_id = src.review_id,
    tgt.review_title = src.review_title,
    tgt.review_text = src.review_text,
    tgt.review_rating = src.review_rating,
    tgt.review_author_public_name = src.review_author_public_name,
    tgt.review_created_timestamp = src.review_created_timestamp,
    tgt.review_has_images = src.review_has_images,
    tgt.review_is_video = src.review_is_video,
    tgt.review_is_verified_purchase = src.review_is_verified_purchase,
    tgt.review_is_vine_review = src.review_is_vine_review,
    tgt.review_marked_as_done = src.review_marked_as_done,
    tgt.review_is_replied_to = src.review_is_replied_to,
    -- order fields
    tgt.order_id = src.order_id,
    tgt.order_timestamp = src.order_timestamp,
    tgt.is_seller_of_record = src.is_seller_of_record,
    tgt.is_digital_order = src.is_digital_order,
    tgt.is_buyer_opted_out = src.is_buyer_opted_out,
    -- etl fields
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    -- metadata fields
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    -- product fields
    brand_name,
    asin,
    child_asin,
    asin_title,
    asin_thumbnail,
    asin_weighted_star_rating,
    asin_total_review_count,
    -- review fields
    review_id,
    review_title,
    review_text,
    review_rating,
    review_author_public_name,
    review_created_timestamp,
    review_has_images,
    review_is_video,
    review_is_verified_purchase,
    review_is_vine_review,
    review_marked_as_done,
    review_is_replied_to,
    -- order fields
    order_id,
    order_timestamp,
    is_seller_of_record,
    is_digital_order,
    is_buyer_opted_out,
    -- etl fields
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES (
    src.pk,
    -- metadata fields
    src.report_fetched_and_loaded_at,
    src.scraper_id,
    src.seller_id,
    src.country,
    -- product fields
    src.brand_name,
    src.asin,
    src.child_asin,
    src.asin_title,
    src.asin_thumbnail,
    src.asin_weighted_star_rating,
    src.asin_total_review_count,
    -- review fields
    src.review_id,
    src.review_title,
    src.review_text,
    src.review_rating,
    src.review_author_public_name,
    src.review_created_timestamp,
    src.review_has_images,
    src.review_is_video,
    src.review_is_verified_purchase,
    src.review_is_vine_review,
    src.review_marked_as_done,
    src.review_is_replied_to,
    -- order fields
    src.order_id,
    src.order_timestamp,
    src.is_seller_of_record,
    src.is_digital_order,
    src.is_buyer_opted_out,
    -- etl fields
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);
CREATE TABLE IF NOT EXISTS $stage_db.merge_scah_voice_of_the_customers AS
SELECT
    *,
    '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
    '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
FROM $stage_db.dedupe_scah_voice_of_the_customers
WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_scah_voice_of_the_customers AS tgt
USING
    $stage_db.dedupe_scah_voice_of_the_customers AS src
    ON 1 = 1
    AND src.pk = tgt.pk
WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    -- metadata fields
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.scraper_id = src.scraper_id,
    tgt.seller_id = src.seller_id,
    tgt.country = src.country,
    -- product fields
    tgt.mskus = src.mskus,
    tgt.asin = src.asin,
    tgt.item_name = src.item_name,
    tgt.condition = src.condition,
    tgt.fnsku = src.fnsku,
    tgt.image_url = src.image_url,
    tgt.retail_url = src.retail_url,
    tgt.order_count = src.order_count,
    tgt.ncx_count = src.ncx_count,
    tgt.ncx_rate = src.ncx_rate,
    tgt.ncx_review_rate = src.ncx_review_rate,
    tgt.ncx_return_rate = src.ncx_return_rate,
    tgt.concession_rate = src.concession_rate,
    tgt.display_rating = src.display_rating,
    tgt.last_update = src.last_update,
    tgt.last_offer_closed_date = src.last_offer_closed_date,
    tgt.most_common_return_reason_bucket = src.most_common_return_reason_bucket,
    tgt.most_common_return_reason_bucket_percent = src.most_common_return_reason_bucket_percent,
    tgt.most_common_return_reason_bucket_count = src.most_common_return_reason_bucket_count,
    tgt.addqr_health = src.addqr_health,
    tgt.pcx_health = src.pcx_health,
    tgt.fulfillment_channel = src.fulfillment_channel,
    tgt.insufficient_feedback = src.insufficient_feedback,
    tgt.listing_exists = src.listing_exists,
    tgt.is_at_risk_of_hrra = src.is_at_risk_of_hrra,
    tgt.vendor_code = src.vendor_code,
    tgt.is_listing_graded = src.is_listing_graded,
    tgt.is_hrra = src.is_hrra,
    tgt.threshold_metadata_key = src.threshold_metadata_key,
    tgt.threshold_metadata_mean = src.threshold_metadata_mean,
    tgt.threshold_metadata_standard_deviation = src.threshold_metadata_standard_deviation,
    tgt.threshold_metadata_ncx_review_mean = src.threshold_metadata_ncx_review_mean,
    tgt.threshold_metadata_ncx_review_standard_deviation = src.threshold_metadata_ncx_review_standard_deviation,
    tgt.threshold_metadata_ncx_return_mean = src.threshold_metadata_ncx_return_mean,
    tgt.threshold_metadata_ncx_return_standard_deviation = src.threshold_metadata_ncx_return_standard_deviation,
    -- etl fields
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    -- metadata fields
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    -- product fields
    mskus,
    asin,
    item_name,
    condition,
    fnsku,
    image_url,
    retail_url,
    order_count,
    ncx_count,
    ncx_rate,
    ncx_review_rate,
    ncx_return_rate,
    concession_rate,
    display_rating,
    last_update,
    last_offer_closed_date,
    most_common_return_reason_bucket,
    most_common_return_reason_bucket_percent,
    most_common_return_reason_bucket_count,
    addqr_health,
    pcx_health,
    fulfillment_channel,
    insufficient_feedback,
    listing_exists,
    is_at_risk_of_hrra,
    vendor_code,
    is_listing_graded,
    is_hrra,
    threshold_metadata_key,
    threshold_metadata_mean,
    threshold_metadata_standard_deviation,
    threshold_metadata_ncx_review_mean,
    threshold_metadata_ncx_review_standard_deviation,
    threshold_metadata_ncx_return_mean,
    threshold_metadata_ncx_return_standard_deviation,
    -- etl fields
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES (
    src.pk,
    -- metadata fields
    src.report_fetched_and_loaded_at,
    src.scraper_id,
    src.seller_id,
    src.country,
    -- product fields
    src.mskus,
    src.asin,
    src.item_name,
    src.condition,
    src.fnsku,
    src.image_url,
    src.retail_url,
    src.order_count,
    src.ncx_count,
    src.ncx_rate,
    src.ncx_review_rate,
    src.ncx_return_rate,
    src.concession_rate,
    src.display_rating,
    src.last_update,
    src.last_offer_closed_date,
    src.most_common_return_reason_bucket,
    src.most_common_return_reason_bucket_percent,
    src.most_common_return_reason_bucket_count,
    src.addqr_health,
    src.pcx_health,
    src.fulfillment_channel,
    src.insufficient_feedback,
    src.listing_exists,
    src.is_at_risk_of_hrra,
    src.vendor_code,
    src.is_listing_graded,
    src.is_hrra,
    src.threshold_metadata_key,
    src.threshold_metadata_mean,
    src.threshold_metadata_standard_deviation,
    src.threshold_metadata_ncx_review_mean,
    src.threshold_metadata_ncx_review_standard_deviation,
    src.threshold_metadata_ncx_return_mean,
    src.threshold_metadata_ncx_return_standard_deviation,
    -- etl fields
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
); 
CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_scah_voice_of_the_customers AS
    SELECT
         *
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_scah_voice_of_the_customers
    WHERE 1 = 0;

INSERT INTO $raw_db.log_scah_voice_of_the_customers (
    -- metadata fields
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    -- product fields
    mskus,
    asin,
    item_name,
    condition,
    fnsku,
    image_url,
    retail_url,
    order_count,
    ncx_count,
    ncx_rate,
    ncx_review_rate,
    ncx_return_rate,
    concession_rate,
    display_rating,
    last_update,
    last_offer_closed_date,
    most_common_return_reason_bucket,
    most_common_return_reason_bucket_percent,
    most_common_return_reason_bucket_count,
    addqr_health,
    pcx_health,
    fulfillment_channel,
    insufficient_feedback,
    listing_exists,
    is_at_risk_of_hrra,
    vendor_code,
    is_listing_graded,
    is_hrra,
    threshold_metadata_key,
    threshold_metadata_mean,
    threshold_metadata_standard_deviation,
    threshold_metadata_ncx_review_mean,
    threshold_metadata_ncx_review_standard_deviation,
    threshold_metadata_ncx_return_mean,
    threshold_metadata_ncx_return_standard_deviation,
    -- etl fields
    file_name,
    etl_batch_run_time,
    log_timestamp_utc
)
    SELECT
        -- metadata fields
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        country,
        -- product fields
        mskus,
        asin,
        item_name,
        condition,
        fnsku,
        image_url,
        retail_url,
        order_count,
        ncx_count,
        ncx_rate,
        ncx_review_rate,
        ncx_return_rate,
        concession_rate,
        display_rating,
        last_update,
        last_offer_closed_date,
        most_common_return_reason_bucket,
        most_common_return_reason_bucket_percent,
        most_common_return_reason_bucket_count,
        addqr_health,
        pcx_health,
        fulfillment_channel,
        insufficient_feedback,
        listing_exists,
        is_at_risk_of_hrra,
        vendor_code,
        is_listing_graded,
        is_hrra,
        threshold_metadata_key,
        threshold_metadata_mean,
        threshold_metadata_standard_deviation,
        threshold_metadata_ncx_review_mean,
        threshold_metadata_ncx_review_standard_deviation,
        threshold_metadata_ncx_return_mean,
        threshold_metadata_ncx_return_standard_deviation,
        -- etl fields
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM
    $raw_db.raw_scah_voice_of_the_customers;
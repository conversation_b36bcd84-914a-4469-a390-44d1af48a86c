CREATE TABLE IF NOT EXISTS $curated_db.fact_scah_voice_of_the_customers (
    PK VARCHAR(32),
    -- metadata fields
    REPORT_FETCHED_AND_LOADED_AT VARCHAR,
    <PERSON>RA<PERSON>ER_ID VARCHAR,
    SELLER_ID VARCHAR,
    <PERSON><PERSON>ER_NAME VARCHAR,
    COUN<PERSON>Y_CODE VARCHAR,
    -- product fields
    MSKUS VARCHAR,
    ASIN VARCHAR,
    ITEM_NAME VARCHAR,
    CONDITION VARCHAR,
    FNSKU VARCHAR,
    IMAGE_URL VARCHAR,
    RETA<PERSON>_URL VARCHAR,
    ORDER_COUNT VARCHAR,
    NCX_COUNT VARCHAR,
    NCX_RATE VARCHAR,
    NCX_REVIEW_RATE VARCHAR,
    NCX_RETURN_RATE VARCHAR,
    CONCESSION_RATE VARCHAR,
    DISPLAY_RATING VARCHAR,
    LAST_UPDATE VARCHAR,
    LAST_OFFER_CLOSED_DATE VARCHAR,
    MOST_COMMON_RETURN_REASON_BUCKET VARCHAR,
    MOST_COMMON_RETURN_REASON_BUCKET_PERCENT VARCHAR,
    MOST_COMMON_RETURN_REASON_BUCKET_COUNT VARCHAR,
    ADDQR_HEALTH VARCHAR,
    PCX_HEALTH VARCHAR,
    FULFILLMENT_CHANNEL VARCHAR,
    INSUFFICIENT_FEEDBACK VARCHAR,
    LISTING_EXISTS VARCHAR,
    IS_AT_RISK_OF_HRRA VARCHAR,
    VENDOR_CODE VARCHAR,
    IS_LISTING_GRADED VARCHAR,
    IS_HRRA VARCHAR,
    THRESHOLD_METADATA_KEY VARCHAR,
    THRESHOLD_METADATA_MEAN VARCHAR,
    THRESHOLD_METADATA_STANDARD_DEVIATION VARCHAR,
    THRESHOLD_METADATA_NCX_REVIEW_MEAN VARCHAR,
    THRESHOLD_METADATA_NCX_REVIEW_STANDARD_DEVIATION VARCHAR,
    THRESHOLD_METADATA_NCX_RETURN_MEAN VARCHAR,
    THRESHOLD_METADATA_NCX_RETURN_STANDARD_DEVIATION VARCHAR,
    -- etl fields
    FILE_NAME VARCHAR,
    ETL_BATCH_RUN_TIME TIMESTAMP_NTZ,
    DATA_SOURCE VARCHAR,
    CREATED_BY VARCHAR DEFAULT 'DAG: fact_scah_voice_of_the_customers',
    UPDATED_BY VARCHAR DEFAULT 'DAG: fact_scah_voice_of_the_customers',
    RECORD_CREATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE(),
    RECORD_UPDATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE()
);

MERGE INTO $curated_db.fact_scah_voice_of_the_customers target
USING (
    SELECT
        cr.PK,
        cr.REPORT_FETCHED_AND_LOADED_AT,
        cr.SCRAPER_ID,
        cr.SELLER_ID,
        si.SELLER_NAME,
        cr.COUNTRY AS COUNTRY_CODE,
        cr.MSKUS,
        cr.ASIN,
        cr.ITEM_NAME,
        cr.CONDITION,
        cr.FNSKU,
        cr.IMAGE_URL,
        cr.RETAIL_URL,
        cr.ORDER_COUNT,
        cr.NCX_COUNT,
        cr.NCX_RATE,
        cr.NCX_REVIEW_RATE,
        cr.NCX_RETURN_RATE,
        cr.CONCESSION_RATE,
        cr.DISPLAY_RATING,
        cr.LAST_UPDATE,
        cr.LAST_OFFER_CLOSED_DATE,
        cr.MOST_COMMON_RETURN_REASON_BUCKET,
        cr.MOST_COMMON_RETURN_REASON_BUCKET_PERCENT,
        cr.MOST_COMMON_RETURN_REASON_BUCKET_COUNT,
        cr.ADDQR_HEALTH,
        cr.PCX_HEALTH,
        cr.FULFILLMENT_CHANNEL,
        cr.INSUFFICIENT_FEEDBACK,
        cr.LISTING_EXISTS,
        cr.IS_AT_RISK_OF_HRRA,
        cr.VENDOR_CODE,
        cr.IS_LISTING_GRADED,
        cr.IS_HRRA,
        cr.THRESHOLD_METADATA_KEY,
        cr.THRESHOLD_METADATA_MEAN,
        cr.THRESHOLD_METADATA_STANDARD_DEVIATION,
        cr.THRESHOLD_METADATA_NCX_REVIEW_MEAN,
        cr.THRESHOLD_METADATA_NCX_REVIEW_STANDARD_DEVIATION,
        cr.THRESHOLD_METADATA_NCX_RETURN_MEAN,
        cr.THRESHOLD_METADATA_NCX_RETURN_STANDARD_DEVIATION,
        cr.FILE_NAME,
        cr.ETL_BATCH_RUN_TIME,
        'dwh.staging.merge_scah_voice_of_the_customers' AS DATA_SOURCE,
        'DAG: fact_scah_voice_of_the_customers' AS CREATED_BY,
        'DAG: fact_scah_voice_of_the_customers' AS UPDATED_BY,
        cr.RECORD_CREATED_TIMESTAMP_UTC,
        cr.RECORD_UPDATED_TIMESTAMP_UTC
    FROM $stage_db.merge_scah_voice_of_the_customers cr
    LEFT JOIN $stage_db.stg_seller_info si ON cr.SELLER_ID = si.SELLER_ID
) source
ON target.PK = source.PK
WHEN MATCHED THEN
    UPDATE SET
        -- metadata fields
        target.REPORT_FETCHED_AND_LOADED_AT = source.REPORT_FETCHED_AND_LOADED_AT,
        target.SCRAPER_ID = source.SCRAPER_ID,
        target.SELLER_ID = source.SELLER_ID,
        target.SELLER_NAME = source.SELLER_NAME,
        target.COUNTRY_CODE = source.COUNTRY_CODE,
        -- product fields
        target.MSKUS = source.MSKUS,
        target.ASIN = source.ASIN,
        target.ITEM_NAME = source.ITEM_NAME,
        target.CONDITION = source.CONDITION,
        target.FNSKU = source.FNSKU,
        target.IMAGE_URL = source.IMAGE_URL,
        target.RETAIL_URL = source.RETAIL_URL,
        target.ORDER_COUNT = source.ORDER_COUNT,
        target.NCX_COUNT = source.NCX_COUNT,
        target.NCX_RATE = source.NCX_RATE,
        target.NCX_REVIEW_RATE = source.NCX_REVIEW_RATE,
        target.NCX_RETURN_RATE = source.NCX_RETURN_RATE,
        target.CONCESSION_RATE = source.CONCESSION_RATE,
        target.DISPLAY_RATING = source.DISPLAY_RATING,
        target.LAST_UPDATE = source.LAST_UPDATE,
        target.LAST_OFFER_CLOSED_DATE = source.LAST_OFFER_CLOSED_DATE,
        target.MOST_COMMON_RETURN_REASON_BUCKET = source.MOST_COMMON_RETURN_REASON_BUCKET,
        target.MOST_COMMON_RETURN_REASON_BUCKET_PERCENT = source.MOST_COMMON_RETURN_REASON_BUCKET_PERCENT,
        target.MOST_COMMON_RETURN_REASON_BUCKET_COUNT = source.MOST_COMMON_RETURN_REASON_BUCKET_COUNT,
        target.ADDQR_HEALTH = source.ADDQR_HEALTH,
        target.PCX_HEALTH = source.PCX_HEALTH,
        target.FULFILLMENT_CHANNEL = source.FULFILLMENT_CHANNEL,
        target.INSUFFICIENT_FEEDBACK = source.INSUFFICIENT_FEEDBACK,
        target.LISTING_EXISTS = source.LISTING_EXISTS,
        target.IS_AT_RISK_OF_HRRA = source.IS_AT_RISK_OF_HRRA,
        target.VENDOR_CODE = source.VENDOR_CODE,
        target.IS_LISTING_GRADED = source.IS_LISTING_GRADED,
        target.IS_HRRA = source.IS_HRRA,
        target.THRESHOLD_METADATA_KEY = source.THRESHOLD_METADATA_KEY,
        target.THRESHOLD_METADATA_MEAN = source.THRESHOLD_METADATA_MEAN,
        target.THRESHOLD_METADATA_STANDARD_DEVIATION = source.THRESHOLD_METADATA_STANDARD_DEVIATION,
        target.THRESHOLD_METADATA_NCX_REVIEW_MEAN = source.THRESHOLD_METADATA_NCX_REVIEW_MEAN,
        target.THRESHOLD_METADATA_NCX_REVIEW_STANDARD_DEVIATION = source.THRESHOLD_METADATA_NCX_REVIEW_STANDARD_DEVIATION,
        target.THRESHOLD_METADATA_NCX_RETURN_MEAN = source.THRESHOLD_METADATA_NCX_RETURN_MEAN,
        target.THRESHOLD_METADATA_NCX_RETURN_STANDARD_DEVIATION = source.THRESHOLD_METADATA_NCX_RETURN_STANDARD_DEVIATION,
        -- etl fields
        target.FILE_NAME = source.FILE_NAME,
        target.ETL_BATCH_RUN_TIME = source.ETL_BATCH_RUN_TIME,
        target.DATA_SOURCE = source.DATA_SOURCE,
        target.UPDATED_BY = source.UPDATED_BY,
        target.RECORD_UPDATED_TIMESTAMP_UTC = SYSDATE()
WHEN NOT MATCHED THEN
    INSERT (
        PK,
        -- metadata fields
        REPORT_FETCHED_AND_LOADED_AT,
        SCRAPER_ID,
        SELLER_ID,
        SELLER_NAME,
        COUNTRY_CODE,
        -- product fields
        MSKUS,
        ASIN,
        ITEM_NAME,
        CONDITION,
        FNSKU,
        IMAGE_URL,
        RETAIL_URL,
        ORDER_COUNT,
        NCX_COUNT,
        NCX_RATE,
        NCX_REVIEW_RATE,
        NCX_RETURN_RATE,
        CONCESSION_RATE,
        DISPLAY_RATING,
        LAST_UPDATE,
        LAST_OFFER_CLOSED_DATE,
        MOST_COMMON_RETURN_REASON_BUCKET,
        MOST_COMMON_RETURN_REASON_BUCKET_PERCENT,
        MOST_COMMON_RETURN_REASON_BUCKET_COUNT,
        ADDQR_HEALTH,
        PCX_HEALTH,
        FULFILLMENT_CHANNEL,
        INSUFFICIENT_FEEDBACK,
        LISTING_EXISTS,
        IS_AT_RISK_OF_HRRA,
        VENDOR_CODE,
        IS_LISTING_GRADED,
        IS_HRRA,
        THRESHOLD_METADATA_KEY,
        THRESHOLD_METADATA_MEAN,
        THRESHOLD_METADATA_STANDARD_DEVIATION,
        THRESHOLD_METADATA_NCX_REVIEW_MEAN,
        THRESHOLD_METADATA_NCX_REVIEW_STANDARD_DEVIATION,
        THRESHOLD_METADATA_NCX_RETURN_MEAN,
        THRESHOLD_METADATA_NCX_RETURN_STANDARD_DEVIATION,
        -- etl fields
        FILE_NAME,
        ETL_BATCH_RUN_TIME,
        DATA_SOURCE,
        CREATED_BY,
        UPDATED_BY,
        RECORD_CREATED_TIMESTAMP_UTC,
        RECORD_UPDATED_TIMESTAMP_UTC
    )
    VALUES (
        source.PK,
        -- metadata fields
        source.REPORT_FETCHED_AND_LOADED_AT,
        source.SCRAPER_ID,
        source.SELLER_ID,
        source.SELLER_NAME,
        source.COUNTRY_CODE,
        -- product fields
        source.MSKUS,
        source.ASIN,
        source.ITEM_NAME,
        source.CONDITION,
        source.FNSKU,
        source.IMAGE_URL,
        source.RETAIL_URL,
        source.ORDER_COUNT,
        source.NCX_COUNT,
        source.NCX_RATE,
        source.NCX_REVIEW_RATE,
        source.NCX_RETURN_RATE,
        source.CONCESSION_RATE,
        source.DISPLAY_RATING,
        source.LAST_UPDATE,
        source.LAST_OFFER_CLOSED_DATE,
        source.MOST_COMMON_RETURN_REASON_BUCKET,
        source.MOST_COMMON_RETURN_REASON_BUCKET_PERCENT,
        source.MOST_COMMON_RETURN_REASON_BUCKET_COUNT,
        source.ADDQR_HEALTH,
        source.PCX_HEALTH,
        source.FULFILLMENT_CHANNEL,
        source.INSUFFICIENT_FEEDBACK,
        source.LISTING_EXISTS,
        source.IS_AT_RISK_OF_HRRA,
        source.VENDOR_CODE,
        source.IS_LISTING_GRADED,
        source.IS_HRRA,
        source.THRESHOLD_METADATA_KEY,
        source.THRESHOLD_METADATA_MEAN,
        source.THRESHOLD_METADATA_STANDARD_DEVIATION,
        source.THRESHOLD_METADATA_NCX_REVIEW_MEAN,
        source.THRESHOLD_METADATA_NCX_REVIEW_STANDARD_DEVIATION,
        source.THRESHOLD_METADATA_NCX_RETURN_MEAN,
        source.THRESHOLD_METADATA_NCX_RETURN_STANDARD_DEVIATION,
        -- etl fields
        source.FILE_NAME,
        source.ETL_BATCH_RUN_TIME,
        source.DATA_SOURCE,
        source.CREATED_BY,
        source.UPDATED_BY,
        SYSDATE(),
        SYSDATE()
    ); 
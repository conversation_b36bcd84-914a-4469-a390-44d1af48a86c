CREATE TABLE IF NOT EXISTS $stage_db.merge_spes_tailored_promotions AS
    SELECT
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_spes_tailored_promotions
    WHERE 1 = 0;


MERGE INTO
    $stage_db.merge_spes_tailored_promotions AS tgt
USING
    $stage_db.dedupe_spes_tailored_promotions AS src
        ON 1 = 1
       AND src.pk = tgt.pk

WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.seller_id = src.seller_id,
    tgt.scraper_id = src.scraper_id,
    tgt.country = src.country,
    tgt.brand_id = src.brand_id,
    tgt.brand_name = src.brand_name,
    tgt.partner_account_id = src.partner_account_id,
    tgt.id = src.id,
    tgt.canceled = src.canceled,
    tgt.dry_run = src.dry_run,
    tgt.creation_timestamp = src.creation_timestamp,
    tgt.last_updated = src.last_updated,
    tgt.budget_amount = src.budget_amount,
    tgt.budget_code = src.budget_code,
    tgt.claim_code = src.claim_code,
    tgt.combinability = src.combinability,
    tgt.description = src.description,
    tgt.enabled = src.enabled,
    tgt.percent_off = src.percent_off,
    tgt.product_selection_id = src.product_selection_id,
    tgt.promotion_id = src.promotion_id,
    tgt.redemptions_per_customer = src.redemptions_per_customer,
    tgt.end_timestamp = src.end_timestamp,
    tgt.start_timestamp = src.start_timestamp,
    tgt.show_promotion_on_detail_page = src.show_promotion_on_detail_page,
    tgt.tracking_id = src.tracking_id,
    tgt.bta_segment_id = src.bta_segment_id,
    tgt.bta_target_id = src.bta_target_id,
    tgt.bullseye_segment_id = src.bullseye_segment_id,
    tgt.segment_size = src.segment_size,
    tgt.segment_size_at_creation = src.segment_size_at_creation,
    tgt.status = src.status,
    tgt.redemptions = src.redemptions,
    tgt.total_discount_given_amount = src.total_discount_given_amount,
    tgt.total_discount_given_code = src.total_discount_given_code,
    tgt.total_sales_amount = src.total_sales_amount,
    tgt.total_sales_code = src.total_sales_code,
    tgt.audience_name = src.audience_name,
    tgt.audience_description = src.audience_description,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    report_fetched_and_loaded_at,
    seller_id,
    scraper_id,
    country,
    brand_id,
    brand_name,
    partner_account_id,
    id,
    canceled,
    dry_run,
    creation_timestamp,
    last_updated,
    budget_amount,
    budget_code,
    claim_code,
    combinability,
    description,
    enabled,
    percent_off,
    product_selection_id,
    promotion_id,
    redemptions_per_customer,
    end_timestamp,
    start_timestamp,
    show_promotion_on_detail_page,
    tracking_id,
    bta_segment_id,
    bta_target_id,
    bullseye_segment_id,
    segment_size,
    segment_size_at_creation,
    status,
    redemptions,
    total_discount_given_amount,
    total_discount_given_code,
    total_sales_amount,
    total_sales_code,
    audience_name,
    audience_description,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src.report_fetched_and_loaded_at,
    src.seller_id,
    src.scraper_id,
    src.country,
    src.brand_id,
    src.brand_name,
    src.partner_account_id,
    src.id,
    src.canceled,
    src.dry_run,
    src.creation_timestamp,
    src.last_updated,
    src.budget_amount,
    src.budget_code,
    src.claim_code,
    src.combinability,
    src.description,
    src.enabled,
    src.percent_off,
    src.product_selection_id,
    src.promotion_id,
    src.redemptions_per_customer,
    src.end_timestamp,
    src.start_timestamp,
    src.show_promotion_on_detail_page,
    src.tracking_id,
    src.bta_segment_id,
    src.bta_target_id,
    src.bullseye_segment_id,
    src.segment_size,
    src.segment_size_at_creation,
    src.status,
    src.redemptions,
    src.total_discount_given_amount,
    src.total_discount_given_code,
    src.total_sales_amount,
    src.total_sales_code,
    src.audience_name,
    src.audience_description,
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);
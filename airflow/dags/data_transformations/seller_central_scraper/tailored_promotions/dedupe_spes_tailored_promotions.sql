CREATE OR REPLACE TABLE $stage_db.dedupe_spes_tailored_promotions AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(seller_id AS VARCHAR), ''), '-',
            COALESCE(CAST(country AS VARCHAR), ''), '-',
            COALESCE(CAST(report_fetched_and_loaded_at AS VARCHAR), ''), '-',
            COALESCE(CAST(id AS VARCHAR), '')
        )) as pk,
        report_fetched_and_loaded_at,
        seller_id,
        scraper_id,
        country,
        brand_id,
        brand_name,
        partner_account_id,
        id,
        canceled,
        dry_run,
        creation_timestamp,
        last_updated,
        TRY_TO_DECIMAL(budget_amount, 10, 2) as budget_amount,
        budget_code,
        claim_code,
        combinability,
        description,
        enabled,
        TRY_TO_DECIMAL(percent_off, 10, 2) as percent_off,
        product_selection_id,
        promotion_id,
        TRY_TO_DECIMAL(redemptions_per_customer, 10, 2) as redemptions_per_customer,
        end_timestamp,
        start_timestamp,
        show_promotion_on_detail_page,
        tracking_id,
        bta_segment_id,
        bta_target_id,
        bullseye_segment_id,
        segment_size,
        segment_size_at_creation,
        status,
        TRY_TO_DECIMAL(redemptions, 10, 2) as redemptions,
        TRY_TO_DECIMAL(total_discount_given_amount, 10, 2) as total_discount_given_amount,
        total_discount_given_code,
        TRY_TO_DECIMAL(total_sales_amount, 10, 2) as total_sales_amount,
        total_sales_code,
        audience_name,
        audience_description,
        file_name,
        etl_batch_run_time
    FROM $raw_db.raw_spes_tailored_promotions
    QUALIFY ROW_NUMBER() OVER (
        PARTITION BY seller_id, country, report_fetched_and_loaded_at, id
        ORDER BY last_updated DESC
    ) = 1
);
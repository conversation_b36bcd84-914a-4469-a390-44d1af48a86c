CREATE TABLE IF NOT EXISTS $curated_db.fact_spes_tailored_promotions (
    PK VARCHAR(32),
    REPORT_FETCHED_AND_LOADED_AT VARCHAR,
    SELLER_ID VARCHAR,
    <PERSON>RA<PERSON>ER_ID VARCHAR,
    COUNTRY VARCHAR,
    <PERSON><PERSON>D_ID VARCHAR,
    <PERSON>AND_NAME VARCHAR,
    PARTNER_ACCOUNT_ID VARCHAR,
    ID VARCHAR,
    CA<PERSON>ELED VARCHAR,
    DRY_RUN VARCHAR,
    CREATION_TIMESTAMP VARCHAR,
    LAST_UPDATED VARCHAR,
    BUDGET_AMOUNT NUMBER(38,2),
    BUDGET_CODE VARCHAR,
    C<PERSON>IM_CODE VARCHAR,
    COMB<PERSON><PERSON>ILITY VARCHAR,
    <PERSON><PERSON><PERSON>PTI<PERSON> VARCHAR,
    ENABLED VARCHAR,
    PERCENT_OFF NUMBER(38,2),
    PRODUCT_SELECTION_ID VARCHAR,
    PROMOTION_ID VARCHAR,
    REDEMP<PERSON>ONS_PER_CUSTOMER NUMBER(38,2),
    <PERSON><PERSON>_TIMESTAMP VARCHAR,
    START_<PERSON>IMESTAMP VARCHAR,
    SHOW_PROMOTION_ON_DETAIL_PAGE VARCHAR,
    TRACKING_ID VARCHAR,
    BTA_SEGMENT_ID VARCHAR,
    BTA_TARGET_ID VARCHAR,
    BULLSEYE_SEGMENT_ID VARCHAR,
    SEGMENT_SIZE VARCHAR,
    SEGMENT_SIZE_AT_CREATION VARCHAR,
    STATUS VARCHAR,
    REDEMPTIONS NUMBER(38,2),
    TOTAL_DISCOUNT_GIVEN_AMOUNT NUMBER(38,2),
    TOTAL_DISCOUNT_GIVEN_CODE VARCHAR,
    TOTAL_SALES_AMOUNT NUMBER(38,2),
    TOTAL_SALES_CODE VARCHAR,
    AUDIENCE_NAME VARCHAR,
    AUDIENCE_DESCRIPTION VARCHAR,
    DATA_SOURCE VARCHAR,
    CREATED_BY VARCHAR DEFAULT 'DAG: fact_spes_tailored_promotions',
    UPDATED_BY VARCHAR DEFAULT 'DAG: fact_spes_tailored_promotions',
    RECORD_CREATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE(),
    RECORD_UPDATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE()
);

MERGE INTO $curated_db.fact_spes_tailored_promotions AS tgt
USING (
    SELECT
        ps.PK,
        ps.REPORT_FETCHED_AND_LOADED_AT,
        ps.SELLER_ID,
        ps.SCRAPER_ID,
        ps.COUNTRY,
        ps.BRAND_ID,
        ps.BRAND_NAME,
        ps.PARTNER_ACCOUNT_ID,
        ps.ID,
        ps.CANCELED,
        ps.DRY_RUN,
        ps.CREATION_TIMESTAMP,
        ps.LAST_UPDATED,
        ps.BUDGET_AMOUNT,
        ps.BUDGET_CODE,
        ps.CLAIM_CODE,
        ps.COMBINABILITY,
        ps.DESCRIPTION,
        ps.ENABLED,
        ps.PERCENT_OFF,
        ps.PRODUCT_SELECTION_ID,
        ps.PROMOTION_ID,
        ps.REDEMPTIONS_PER_CUSTOMER,
        ps.END_TIMESTAMP,
        ps.START_TIMESTAMP,
        ps.SHOW_PROMOTION_ON_DETAIL_PAGE,
        ps.TRACKING_ID,
        ps.BTA_SEGMENT_ID,
        ps.BTA_TARGET_ID,
        ps.BULLSEYE_SEGMENT_ID,
        ps.SEGMENT_SIZE,
        ps.SEGMENT_SIZE_AT_CREATION,
        ps.STATUS,
        ps.REDEMPTIONS,
        ps.TOTAL_DISCOUNT_GIVEN_AMOUNT,
        ps.TOTAL_DISCOUNT_GIVEN_CODE,
        ps.TOTAL_SALES_AMOUNT,
        ps.TOTAL_SALES_CODE,
        ps.AUDIENCE_NAME,
        ps.AUDIENCE_DESCRIPTION,
        'dwh.staging.merge_spes_tailored_promotions' AS DATA_SOURCE,
        'DAG: fact_spes_tailored_promotions' AS CREATED_BY,
        'DAG: fact_spes_tailored_promotions' AS UPDATED_BY,
        ps.RECORD_CREATED_TIMESTAMP_UTC,
        ps.RECORD_UPDATED_TIMESTAMP_UTC
    FROM $stage_db.merge_spes_tailored_promotions ps
) AS src
ON tgt.pk = src.pk
WHEN MATCHED
    AND src.RECORD_UPDATED_TIMESTAMP_UTC > tgt.RECORD_UPDATED_TIMESTAMP_UTC
THEN UPDATE SET
    tgt.REPORT_FETCHED_AND_LOADED_AT = src.REPORT_FETCHED_AND_LOADED_AT,
    tgt.SELLER_ID = src.SELLER_ID,
    tgt.SCRAPER_ID = src.SCRAPER_ID,
    tgt.COUNTRY = src.COUNTRY,
    tgt.BRAND_ID = src.BRAND_ID,
    tgt.BRAND_NAME = src.BRAND_NAME,
    tgt.PARTNER_ACCOUNT_ID = src.PARTNER_ACCOUNT_ID,
    tgt.ID = src.ID,
    tgt.CANCELED = src.CANCELED,
    tgt.DRY_RUN = src.DRY_RUN,
    tgt.CREATION_TIMESTAMP = src.CREATION_TIMESTAMP,
    tgt.LAST_UPDATED = src.LAST_UPDATED,
    tgt.BUDGET_AMOUNT = src.BUDGET_AMOUNT,
    tgt.BUDGET_CODE = src.BUDGET_CODE,
    tgt.CLAIM_CODE = src.CLAIM_CODE,
    tgt.COMBINABILITY = src.COMBINABILITY,
    tgt.DESCRIPTION = src.DESCRIPTION,
    tgt.ENABLED = src.ENABLED,
    tgt.PERCENT_OFF = src.PERCENT_OFF,
    tgt.PRODUCT_SELECTION_ID = src.PRODUCT_SELECTION_ID,
    tgt.PROMOTION_ID = src.PROMOTION_ID,
    tgt.REDEMPTIONS_PER_CUSTOMER = src.REDEMPTIONS_PER_CUSTOMER,
    tgt.END_TIMESTAMP = src.END_TIMESTAMP,
    tgt.START_TIMESTAMP = src.START_TIMESTAMP,
    tgt.SHOW_PROMOTION_ON_DETAIL_PAGE = src.SHOW_PROMOTION_ON_DETAIL_PAGE,
    tgt.TRACKING_ID = src.TRACKING_ID,
    tgt.BTA_SEGMENT_ID = src.BTA_SEGMENT_ID,
    tgt.BTA_TARGET_ID = src.BTA_TARGET_ID,
    tgt.BULLSEYE_SEGMENT_ID = src.BULLSEYE_SEGMENT_ID,
    tgt.SEGMENT_SIZE = src.SEGMENT_SIZE,
    tgt.SEGMENT_SIZE_AT_CREATION = src.SEGMENT_SIZE_AT_CREATION,
    tgt.STATUS = src.STATUS,
    tgt.REDEMPTIONS = src.REDEMPTIONS,
    tgt.TOTAL_DISCOUNT_GIVEN_AMOUNT = src.TOTAL_DISCOUNT_GIVEN_AMOUNT,
    tgt.TOTAL_DISCOUNT_GIVEN_CODE = src.TOTAL_DISCOUNT_GIVEN_CODE,
    tgt.TOTAL_SALES_AMOUNT = src.TOTAL_SALES_AMOUNT,
    tgt.TOTAL_SALES_CODE = src.TOTAL_SALES_CODE,
    tgt.AUDIENCE_NAME = src.AUDIENCE_NAME,
    tgt.AUDIENCE_DESCRIPTION = src.AUDIENCE_DESCRIPTION,
    tgt.DATA_SOURCE = src.DATA_SOURCE,
    tgt.UPDATED_BY = src.UPDATED_BY,
    tgt.RECORD_UPDATED_TIMESTAMP_UTC = SYSDATE()
WHEN NOT MATCHED THEN INSERT (
    PK,
    REPORT_FETCHED_AND_LOADED_AT,
    SELLER_ID,
    SCRAPER_ID,
    COUNTRY,
    BRAND_ID,
    BRAND_NAME,
    PARTNER_ACCOUNT_ID,
    ID,
    CANCELED,
    DRY_RUN,
    CREATION_TIMESTAMP,
    LAST_UPDATED,
    BUDGET_AMOUNT,
    BUDGET_CODE,
    CLAIM_CODE,
    COMBINABILITY,
    DESCRIPTION,
    ENABLED,
    PERCENT_OFF,
    PRODUCT_SELECTION_ID,
    PROMOTION_ID,
    REDEMPTIONS_PER_CUSTOMER,
    END_TIMESTAMP,
    START_TIMESTAMP,
    SHOW_PROMOTION_ON_DETAIL_PAGE,
    TRACKING_ID,
    BTA_SEGMENT_ID,
    BTA_TARGET_ID,
    BULLSEYE_SEGMENT_ID,
    SEGMENT_SIZE,
    SEGMENT_SIZE_AT_CREATION,
    STATUS,
    REDEMPTIONS,
    TOTAL_DISCOUNT_GIVEN_AMOUNT,
    TOTAL_DISCOUNT_GIVEN_CODE,
    TOTAL_SALES_AMOUNT,
    TOTAL_SALES_CODE,
    AUDIENCE_NAME,
    AUDIENCE_DESCRIPTION,
    DATA_SOURCE,
    CREATED_BY,
    UPDATED_BY,
    RECORD_CREATED_TIMESTAMP_UTC,
    RECORD_UPDATED_TIMESTAMP_UTC
)
VALUES (
    src.PK,
    src.REPORT_FETCHED_AND_LOADED_AT,
    src.SELLER_ID,
    src.SCRAPER_ID,
    src.COUNTRY,
    src.BRAND_ID,
    src.BRAND_NAME,
    src.PARTNER_ACCOUNT_ID,
    src.ID,
    src.CANCELED,
    src.DRY_RUN,
    src.CREATION_TIMESTAMP,
    src.LAST_UPDATED,
    src.BUDGET_AMOUNT,
    src.BUDGET_CODE,
    src.CLAIM_CODE,
    src.COMBINABILITY,
    src.DESCRIPTION,
    src.ENABLED,
    src.PERCENT_OFF,
    src.PRODUCT_SELECTION_ID,
    src.PROMOTION_ID,
    src.REDEMPTIONS_PER_CUSTOMER,
    src.END_TIMESTAMP,
    src.START_TIMESTAMP,
    src.SHOW_PROMOTION_ON_DETAIL_PAGE,
    src.TRACKING_ID,
    src.BTA_SEGMENT_ID,
    src.BTA_TARGET_ID,
    src.BULLSEYE_SEGMENT_ID,
    src.SEGMENT_SIZE,
    src.SEGMENT_SIZE_AT_CREATION,
    src.STATUS,
    src.REDEMPTIONS,
    src.TOTAL_DISCOUNT_GIVEN_AMOUNT,
    src.TOTAL_DISCOUNT_GIVEN_CODE,
    src.TOTAL_SALES_AMOUNT,
    src.TOTAL_SALES_CODE,
    src.AUDIENCE_NAME,
    src.AUDIENCE_DESCRIPTION,
    src.DATA_SOURCE,
    src.CREATED_BY,
    src.UPDATED_BY,
    SYSDATE(),
    SYSDATE()
);
CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_spes_tailored_promotions AS
    SELECT
         *
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_spes_tailored_promotions
    WHERE 1 = 0;

INSERT INTO $raw_db.log_spes_tailored_promotions (
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    brand_id,
    brand_name,
    partner_account_id,
    id,
    canceled,
    dry_run,
    creation_timestamp,
    last_updated,
    budget_amount,
    budget_code,
    claim_code,
    combinability,
    description,
    enabled,
    percent_off,
    product_selection_id,
    promotion_id,
    redemptions_per_customer,
    end_timestamp,
    start_timestamp,
    show_promotion_on_detail_page,
    tracking_id,
    bta_segment_id,
    bta_target_id,
    bullseye_segment_id,
    segment_size,
    segment_size_at_creation,
    status,
    redemptions,
    total_discount_given_amount,
    total_discount_given_code,
    total_sales_amount,
    total_sales_code,
    audience_name,
    audience_description,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc
)
    SELECT
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        country,
        brand_id,
        brand_name,
        partner_account_id,
        id,
        canceled,
        dry_run,
        creation_timestamp,
        last_updated,
        budget_amount,
        budget_code,
        claim_code,
        combinability,
        description,
        enabled,
        percent_off,
        product_selection_id,
        promotion_id,
        redemptions_per_customer,
        end_timestamp,
        start_timestamp,
        show_promotion_on_detail_page,
        tracking_id,
        bta_segment_id,
        bta_target_id,
        bullseye_segment_id,
        segment_size,
        segment_size_at_creation,
        status,
        redemptions,
        total_discount_given_amount,
        total_discount_given_code,
        total_sales_amount,
        total_sales_code,
        audience_name,
        audience_description,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM
        $raw_db.raw_spes_tailored_promotions;
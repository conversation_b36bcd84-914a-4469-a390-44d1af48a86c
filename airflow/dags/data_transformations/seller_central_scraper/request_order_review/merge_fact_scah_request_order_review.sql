CREATE TABLE IF NOT EXISTS $curated_db.fact_scah_request_order_review (
    PK VARCHAR(32),
    -- metadata fields
    REPORT_FETCHED_AND_LOADED_AT VARCHAR,
    <PERSON>RA<PERSON>ER_ID VARCHAR,
    SELLER_ID VARCHAR,
    SE<PERSON>ER_NAME VARCHAR,
    COUNTRY_CODE VARCHAR,
    MARKETPLACE_ID VARCHAR,
    -- review fields
    ORDER_ID VARCHAR,
    INELIGIBLE_REASON VARCHAR,
    STATUS VARCHAR,
    -- etl fields
    FILE_NAME VARCHAR,
    ETL_BATCH_RUN_TIME TIMESTAMP_NTZ,
    DATA_SOURCE VARCHAR,
    CREATED_BY VARCHAR DEFAULT 'DAG: fact_scah_request_order_review',
    UPDATED_BY VARCHAR DEFAULT 'DAG: fact_scah_request_order_review',
    RECORD_CREATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE(),
    RECORD_UPDATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE()
);

MERGE INTO $curated_db.fact_scah_request_order_review target
USING (
    SELECT
        cr.PK,
        cr.REPORT_FETCHED_AND_LOADED_AT,
        cr.SCRAPER_ID,
        cr.SELLER_ID,
        cr.MARKETPLACE_ID,
        si.SELLER_NAME,
        cr.COUNTRY AS COUNTRY_CODE,
        cr.ORDER_ID,
        cr.INELIGIBLE_REASON,
        cr.STATUS,
        cr.FILE_NAME,
        cr.ETL_BATCH_RUN_TIME,
        'dwh.staging.merge_scah_request_order_review' AS DATA_SOURCE,
        'DAG: fact_scah_request_order_review' AS CREATED_BY,
        'DAG: fact_scah_request_order_review' AS UPDATED_BY,
        cr.RECORD_CREATED_TIMESTAMP_UTC,
        cr.RECORD_UPDATED_TIMESTAMP_UTC
    FROM $stage_db.merge_scah_request_order_review cr
    LEFT JOIN $stage_db.stg_seller_info si ON cr.SELLER_ID = si.SELLER_ID
) source
ON target.PK = source.PK
WHEN MATCHED THEN
    UPDATE SET
        -- metadata fields
        target.REPORT_FETCHED_AND_LOADED_AT = source.REPORT_FETCHED_AND_LOADED_AT,
        target.SCRAPER_ID = source.SCRAPER_ID,
        target.SELLER_ID = source.SELLER_ID,
        target.SELLER_NAME = source.SELLER_NAME,
        target.COUNTRY_CODE = source.COUNTRY_CODE,
        target.MARKETPLACE_ID = source.MARKETPLACE_ID,
        -- review fields
        target.ORDER_ID = source.ORDER_ID,
        target.INELIGIBLE_REASON = source.INELIGIBLE_REASON,
        target.STATUS = source.STATUS,
        -- etl fields
        target.FILE_NAME = source.FILE_NAME,
        target.ETL_BATCH_RUN_TIME = source.ETL_BATCH_RUN_TIME,
        target.DATA_SOURCE = source.DATA_SOURCE,
        target.UPDATED_BY = source.UPDATED_BY,
        target.RECORD_UPDATED_TIMESTAMP_UTC = SYSDATE()
WHEN NOT MATCHED THEN
    INSERT (
        PK,
        -- metadata fields
        REPORT_FETCHED_AND_LOADED_AT,
        SCRAPER_ID,
        SELLER_ID,
        SELLER_NAME,
        COUNTRY_CODE,
        MARKETPLACE_ID,
        -- review fields
        ORDER_ID,
        INELIGIBLE_REASON,
        STATUS,
        -- etl fields
        FILE_NAME,
        ETL_BATCH_RUN_TIME,
        DATA_SOURCE,
        CREATED_BY,
        UPDATED_BY,
        RECORD_CREATED_TIMESTAMP_UTC,
        RECORD_UPDATED_TIMESTAMP_UTC
    )
    VALUES (
        source.PK,
        -- metadata fields
        source.REPORT_FETCHED_AND_LOADED_AT,
        source.SCRAPER_ID,
        source.SELLER_ID,
        source.SELLER_NAME,
        source.COUNTRY_CODE,
        source.MARKETPLACE_ID,
        -- review fields
        source.ORDER_ID,
        source.INELIGIBLE_REASON,
        source.STATUS,
        -- etl fields
        source.FILE_NAME,
        source.ETL_BATCH_RUN_TIME,
        source.DATA_SOURCE,
        source.CREATED_BY,
        source.UPDATED_BY,
        SYSDATE(),
        SYSDATE()
    );
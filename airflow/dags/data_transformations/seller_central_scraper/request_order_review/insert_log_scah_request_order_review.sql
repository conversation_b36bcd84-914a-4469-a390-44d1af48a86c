CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_scah_request_order_review AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_scah_request_order_review
    WHERE 1 = 0;

INSERT INTO $raw_db.log_scah_request_order_review (
    -- metadata fields
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    marketplace_id,
    country,
    -- review fields
    order_id,
    ineligible_reason,
    status,
    -- etl fields
    file_name,
    etl_batch_run_time,
    log_timestamp_utc
)
    SELECT
        -- metadata fields
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        marketplace_id,
        country,
        -- review fields
        order_id,
        ineligible_reason,
        status,
        -- etl fields
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM
    $raw_db.raw_scah_request_order_review;
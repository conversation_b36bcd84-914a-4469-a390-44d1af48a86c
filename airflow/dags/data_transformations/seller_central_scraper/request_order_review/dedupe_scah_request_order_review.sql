CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_scah_request_order_review AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(order_id AS VARCHAR), ''), '-',
            COALESCE(CAST(country AS VARCHAR), ''), '-',
            COALESCE(CAST(seller_id AS VARCHAR), ''), '-',
            COALESCE(CAST(marketplace_id AS VARCHAR), ''), '-',
            COALESCE(CAST(report_fetched_and_loaded_at::date AS VARCHAR), '')
            )) AS pk,
        --  metadata fields  --
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        marketplace_id,
        country,
        --  review fields  --
        order_id,
        ineligible_reason,
        status,
        --  etl fields  --
        file_name,
        etl_batch_run_time

    FROM $raw_db.raw_scah_request_order_review
    WHERE 1=1
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY report_fetched_and_loaded_at DESC NULLS LAST) = 1
);
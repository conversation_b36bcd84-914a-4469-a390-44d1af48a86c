CREATE TABLE IF NOT EXISTS $stage_db.merge_scah_request_order_review AS
SELECT
    *,
    '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
    '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
FROM $stage_db.dedupe_scah_request_order_review
WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_scah_request_order_review AS tgt
USING
    $stage_db.dedupe_scah_request_order_review AS src
    ON 1 = 1
    AND src.pk = tgt.pk
WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    -- metadata fields
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.scraper_id = src.scraper_id,
    tgt.seller_id = src.seller_id,
    tgt.country = src.country,
    tgt.marketplace_id = src.marketplace_id,
    -- review fields
    tgt.order_id = src.order_id,
    tgt.ineligible_reason = src.ineligible_reason,
    tgt.status = src.status,
    -- etl fields
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    -- metadata fields
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    marketplace_id,
    -- review fields
    order_id,
    ineligible_reason,
    status,
    -- etl fields
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES (
    src.pk,
    -- metadata fields
    src.report_fetched_and_loaded_at,
    src.scraper_id,
    src.seller_id,
    src.country,
    src.marketplace_id,
    -- review fields
    src.order_id,
    src.ineligible_reason,
    src.status,
    -- etl fields
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);
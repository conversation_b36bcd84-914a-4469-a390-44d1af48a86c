
CREATE TABLE IF NOT EXISTS $stage_db.merge_scas_content_creator AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_scas_content_creator
    WHERE 1 = 0;

MERGE INTO $stage_db.merge_scas_content_creator AS tgt USING $stage_db.dedupe_scas_content_creator AS src ON 1 = 1
AND src.pk = tgt.pk WHEN MATCHED THEN
UPDATE
SET
    tgt.pk = src.pk,
    tgt.brand_entity_id = src.brand_entity_id,
    tgt.brand_customer_id = src.brand_customer_id,
    tgt.brand_name = src.brand_name,
    tgt.brand_lbr_node_id = src.brand_lbr_node_id,
    tgt.brand_id = src.brand_id,
    tgt.brand_campaign_stats = src.brand_campaign_stats,
    tgt.campaign_id = src.campaign_id,
    tgt.campaign_rank = src.campaign_rank,
    tgt.campaign_qualifier = src.campaign_qualifier,
    tgt.campaign_name = src.campaign_name,
    tgt.campaign_status = src.campaign_status,
    tgt.campaign_type = src.campaign_type,
    tgt.campaign_asins = src.campaign_asins,
    tgt.campaign_marketplace_id = src.campaign_marketplace_id,
    tgt.campaign_interest_tags = src.campaign_interest_tags,
    tgt.campaign_providing_sample = src.campaign_providing_sample,
    tgt.campaign_fully_claimed = src.campaign_fully_claimed,
    tgt.campaign_edited = src.campaign_edited,
    tgt.campaign_content_submission_required = src.campaign_content_submission_required,
    tgt.campaign_start_date_time = src.campaign_start_date_time,
    tgt.campaign_scheduled_end_date_time = src.campaign_scheduled_end_date_time,
    tgt.campaign_actual_end_date_time = src.campaign_actual_end_date_time,
    tgt.campaign_create_date_time = src.campaign_create_date_time,
    tgt.campaign_last_updated_date_time = src.campaign_last_updated_date_time,
    tgt.campaign_number_of_creators_required = src.campaign_number_of_creators_required,
    tgt.campaign_number_of_creators_accepted = src.campaign_number_of_creators_accepted,
    tgt.campaign_number_of_creators_with_content_submitted = src.campaign_number_of_creators_with_content_submitted,
    tgt.campaign_commission_percentage = src.campaign_commission_percentage,
    tgt.campaign_promotional_payment_percentage = src.campaign_promotional_payment_percentage,
    tgt.campaign_total_budget = src.campaign_total_budget,
    tgt.campaign_remaining_budget = src.campaign_remaining_budget,
    tgt.campaign_currency_code = src.campaign_currency_code,
    tgt.campaign_clicks = src.campaign_clicks,
    tgt.campaign_orders = src.campaign_orders,
    tgt.campaign_sales = src.campaign_sales,
    tgt.campaign_spend = src.campaign_spend,
    tgt.ad_id = src.ad_id,
    tgt.advertiser_id = src.advertiser_id,
    tgt.store_id = src.store_id,
    tgt.advertiser_type = src.advertiser_type,
    tgt.creator_profile_name = src.creator_profile_name,
    tgt.creator_avatar = src.creator_avatar,
    tgt.ad_status = src.ad_status,
    tgt.ad_create_date_time = src.ad_create_date_time,
    tgt.ad_start_date_time = src.ad_start_date_time,
    tgt.ad_scheduled_end_date_time = src.ad_scheduled_end_date_time,
    tgt.ad_actual_end_date_time = src.ad_actual_end_date_time,
    tgt.ad_accepted_date_time = src.ad_accepted_date_time,
    tgt.ad_last_updated_date_time = src.ad_last_updated_date_time,
    tgt.ad_commission_percentage = src.ad_commission_percentage,
    tgt.ad_promotional_payment_percentage = src.ad_promotional_payment_percentage,
    tgt.ad_total_budget = src.ad_total_budget,
    tgt.ad_remaining_budget = src.ad_remaining_budget,
    tgt.ad_currency_code = src.ad_currency_code,
    tgt.ad_clicks = src.ad_clicks,
    tgt.ad_orders = src.ad_orders,
    tgt.ad_sales = src.ad_sales,
    tgt.ad_spend = src.ad_spend,
    tgt.creator_content_details = src.creator_content_details,
    tgt.campaign_stats = src.campaign_stats,
    tgt.asin_stats = src.asin_stats,
    tgt.seller_id = src.seller_id,
    tgt.country = src.country,
    tgt.marketplace_id = src.marketplace_id,
    tgt.scraper_id = src.scraper_id,
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE() WHEN NOT MATCHED THEN
INSERT (
        pk,
        brand_entity_id,
        brand_customer_id,
        brand_name,
        brand_lbr_node_id,
        brand_id,
        brand_campaign_stats,
        campaign_id,
        campaign_rank,
        campaign_qualifier,
        campaign_name,
        campaign_status,
        campaign_type,
        campaign_asins,
        campaign_marketplace_id,
        campaign_interest_tags,
        campaign_providing_sample,
        campaign_fully_claimed,
        campaign_edited,
        campaign_content_submission_required,
        campaign_start_date_time,
        campaign_scheduled_end_date_time,
        campaign_actual_end_date_time,
        campaign_create_date_time,
        campaign_last_updated_date_time,
        campaign_number_of_creators_required,
        campaign_number_of_creators_accepted,
        campaign_number_of_creators_with_content_submitted,
        campaign_commission_percentage,
        campaign_promotional_payment_percentage,
        campaign_total_budget,
        campaign_remaining_budget,
        campaign_currency_code,
        campaign_clicks,
        campaign_orders,
        campaign_sales,
        campaign_spend,
        ad_id,
        advertiser_id,
        store_id,
        advertiser_type,
        creator_profile_name,
        creator_avatar,
        ad_status,
        ad_create_date_time,
        ad_start_date_time,
        ad_scheduled_end_date_time,
        ad_actual_end_date_time,
        ad_accepted_date_time,
        ad_last_updated_date_time,
        ad_commission_percentage,
        ad_promotional_payment_percentage,
        ad_total_budget,
        ad_remaining_budget,
        ad_currency_code,
        ad_clicks,
        ad_orders,
        ad_sales,
        ad_spend,
        creator_content_details,
        campaign_stats,
        asin_stats,
        seller_id,
        country,
        marketplace_id,
        scraper_id,
        report_fetched_and_loaded_at,
        file_name,
        etl_batch_run_time,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    )
VALUES (
        src.pk,
        src.brand_entity_id,
        src.brand_customer_id,
        src.brand_name,
        src.brand_lbr_node_id,
        src.brand_id,
        src.brand_campaign_stats,
        src.campaign_id,
        src.campaign_rank,
        src.campaign_qualifier,
        src.campaign_name,
        src.campaign_status,
        src.campaign_type,
        src.campaign_asins,
        src.campaign_marketplace_id,
        src.campaign_interest_tags,
        src.campaign_providing_sample,
        src.campaign_fully_claimed,
        src.campaign_edited,
        src.campaign_content_submission_required,
        src.campaign_start_date_time,
        src.campaign_scheduled_end_date_time,
        src.campaign_actual_end_date_time,
        src.campaign_create_date_time,
        src.campaign_last_updated_date_time,
        src.campaign_number_of_creators_required,
        src.campaign_number_of_creators_accepted,
        src.campaign_number_of_creators_with_content_submitted,
        src.campaign_commission_percentage,
        src.campaign_promotional_payment_percentage,
        src.campaign_total_budget,
        src.campaign_remaining_budget,
        src.campaign_currency_code,
        src.campaign_clicks,
        src.campaign_orders,
        src.campaign_sales,
        src.campaign_spend,
        src.ad_id,
        src.advertiser_id,
        src.store_id,
        src.advertiser_type,
        src.creator_profile_name,
        src.creator_avatar,
        src.ad_status,
        src.ad_create_date_time,
        src.ad_start_date_time,
        src.ad_scheduled_end_date_time,
        src.ad_actual_end_date_time,
        src.ad_accepted_date_time,
        src.ad_last_updated_date_time,
        src.ad_commission_percentage,
        src.ad_promotional_payment_percentage,
        src.ad_total_budget,
        src.ad_remaining_budget,
        src.ad_currency_code,
        src.ad_clicks,
        src.ad_orders,
        src.ad_sales,
        src.ad_spend,
        src.creator_content_details,
        src.campaign_stats,
        src.asin_stats,
        src.seller_id,
        src.country,
        src.marketplace_id,
        src.scraper_id,
        src.report_fetched_and_loaded_at,
        src.file_name,
        src.etl_batch_run_time,
        SYSDATE(),
        SYSDATE()
    );
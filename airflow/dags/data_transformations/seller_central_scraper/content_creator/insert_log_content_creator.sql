CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_scas_content_creator AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_scas_content_creator
    WHERE 1 = 0;

INSERT INTO
    $raw_db.log_scas_content_creator (
        brand_entity_id,
        brand_customer_id,
        brand_name,
        brand_lbr_node_id,
        brand_id,
        brand_campaign_stats,
        campaign_id,
        campaign_rank,
        campaign_qualifier,
        campaign_name,
        campaign_status,
        campaign_type,
        campaign_asins,
        campaign_marketplace_id,
        campaign_interest_tags,
        campaign_providing_sample,
        campaign_fully_claimed,
        campaign_edited,
        campaign_content_submission_required,
        campaign_start_date_time,
        campaign_scheduled_end_date_time,
        campaign_actual_end_date_time,
        campaign_create_date_time,
        campaign_last_updated_date_time,
        campaign_number_of_creators_required,
        campaign_number_of_creators_accepted,
        campaign_number_of_creators_with_content_submitted,
        campaign_commission_percentage,
        campaign_promotional_payment_percentage,
        campaign_total_budget,
        campaign_remaining_budget,
        campaign_currency_code,
        campaign_clicks,
        campaign_orders,
        campaign_sales,
        campaign_spend,
        ad_id,
        advertiser_id,
        store_id,
        advertiser_type,
        creator_profile_name,
        creator_avatar,
        ad_status,
        ad_create_date_time,
        ad_start_date_time,
        ad_scheduled_end_date_time,
        ad_actual_end_date_time,
        ad_accepted_date_time,
        ad_last_updated_date_time,
        ad_commission_percentage,
        ad_promotional_payment_percentage,
        ad_total_budget,
        ad_remaining_budget,
        ad_currency_code,
        ad_clicks,
        ad_orders,
        ad_sales,
        ad_spend,
        creator_content_details,
        campaign_stats,
        asin_stats,
        seller_id,
        country,
        marketplace_id,
        scraper_id,
        report_fetched_and_loaded_at,
        file_name,
        etl_batch_run_time,
        log_timestamp_utc
    )
SELECT
    brand_entity_id,
    brand_customer_id,
    brand_name,
    brand_lbr_node_id,
    brand_id,
    brand_campaign_stats,
    campaign_id,
    campaign_rank,
    campaign_qualifier,
    campaign_name,
    campaign_status,
    campaign_type,
    campaign_asins,
    campaign_marketplace_id,
    campaign_interest_tags,
    campaign_providing_sample,
    campaign_fully_claimed,
    campaign_edited,
    campaign_content_submission_required,
    campaign_start_date_time,
    campaign_scheduled_end_date_time,
    campaign_actual_end_date_time,
    campaign_create_date_time,
    campaign_last_updated_date_time,
    campaign_number_of_creators_required,
    campaign_number_of_creators_accepted,
    campaign_number_of_creators_with_content_submitted,
    campaign_commission_percentage,
    campaign_promotional_payment_percentage,
    campaign_total_budget,
    campaign_remaining_budget,
    campaign_currency_code,
    campaign_clicks,
    campaign_orders,
    campaign_sales,
    campaign_spend,
    ad_id,
    advertiser_id,
    store_id,
    advertiser_type,
    creator_profile_name,
    creator_avatar,
    ad_status,
    ad_create_date_time,
    ad_start_date_time,
    ad_scheduled_end_date_time,
    ad_actual_end_date_time,
    ad_accepted_date_time,
    ad_last_updated_date_time,
    ad_commission_percentage,
    ad_promotional_payment_percentage,
    ad_total_budget,
    ad_remaining_budget,
    ad_currency_code,
    ad_clicks,
    ad_orders,
    ad_sales,
    ad_spend,
    creator_content_details,
    campaign_stats,
    asin_stats,
    seller_id,
    country,
    marketplace_id,
    scraper_id,
    report_fetched_and_loaded_at,
    file_name,
    etl_batch_run_time,
    SYSDATE() AS log_timestamp_utc
FROM $raw_db.raw_scas_content_creator;
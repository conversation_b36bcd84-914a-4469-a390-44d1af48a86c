
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_scas_content_creator AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(ad_id AS VARCHAR), ''), '-',
            COALESCE(CAST(report_fetched_and_loaded_at::date AS VARCHAR), '')
            )) AS pk,
        brand_entity_id,
        brand_customer_id,
        brand_name,
        brand_lbr_node_id,
        brand_id,
        brand_campaign_stats,
        campaign_id,
        TRY_TO_NUMBER(campaign_rank) AS campaign_rank,
        campaign_qualifier,
        campaign_name,
        campaign_status,
        campaign_type,
        campaign_asins,
        campaign_marketplace_id,
        campaign_interest_tags,
        TRY_TO_BOOLEAN(campaign_providing_sample) AS campaign_providing_sample,
        TRY_TO_BOOLEAN(campaign_fully_claimed) AS campaign_fully_claimed,
        TRY_TO_BOOLEAN(campaign_edited) AS campaign_edited,
        TRY_TO_BOOLEAN(campaign_content_submission_required) AS campaign_content_submission_required,
        TRY_TO_TIMESTAMP_NTZ(campaign_start_date_time) AS campaign_start_date_time,
        TRY_TO_TIMESTAMP_NTZ(campaign_scheduled_end_date_time) AS campaign_scheduled_end_date_time,
        TRY_TO_TIMESTAMP_NTZ(campaign_actual_end_date_time) AS campaign_actual_end_date_time,
        TRY_TO_TIMESTAMP_NTZ(campaign_create_date_time) AS campaign_create_date_time,
        TRY_TO_TIMESTAMP_NTZ(campaign_last_updated_date_time) AS campaign_last_updated_date_time,
        TRY_TO_NUMBER(campaign_number_of_creators_required) AS campaign_number_of_creators_required,
        TRY_TO_NUMBER(campaign_number_of_creators_accepted) AS campaign_number_of_creators_accepted,
        TRY_TO_NUMBER(campaign_number_of_creators_with_content_submitted) AS campaign_number_of_creators_with_content_submitted,
        TRY_TO_DOUBLE(campaign_commission_percentage) AS campaign_commission_percentage,
        TRY_TO_DOUBLE(campaign_promotional_payment_percentage) AS campaign_promotional_payment_percentage,
        TRY_TO_DOUBLE(campaign_total_budget) AS campaign_total_budget,
        TRY_TO_DOUBLE(campaign_remaining_budget) AS campaign_remaining_budget,
        campaign_currency_code,
        TRY_TO_NUMBER(campaign_clicks) AS campaign_clicks,
        TRY_TO_NUMBER(campaign_orders) AS campaign_orders,
        TRY_TO_DOUBLE(campaign_sales) AS campaign_sales,
        TRY_TO_DOUBLE(campaign_spend) AS campaign_spend,
        ad_id,
        advertiser_id,
        store_id,
        advertiser_type,
        creator_profile_name,
        creator_avatar,
        ad_status,
        TRY_TO_TIMESTAMP_NTZ(ad_create_date_time) AS ad_create_date_time,
        TRY_TO_TIMESTAMP_NTZ(ad_start_date_time) AS ad_start_date_time,
        TRY_TO_TIMESTAMP_NTZ(ad_scheduled_end_date_time) AS ad_scheduled_end_date_time,
        TRY_TO_TIMESTAMP_NTZ(ad_actual_end_date_time) AS ad_actual_end_date_time,
        TRY_TO_TIMESTAMP_NTZ(ad_accepted_date_time) AS ad_accepted_date_time,
        TRY_TO_TIMESTAMP_NTZ(ad_last_updated_date_time) AS ad_last_updated_date_time,
        TRY_TO_DOUBLE(ad_commission_percentage) AS ad_commission_percentage,
        TRY_TO_DOUBLE(ad_promotional_payment_percentage) AS ad_promotional_payment_percentage,
        TRY_TO_DOUBLE(ad_total_budget) AS ad_total_budget,
        TRY_TO_DOUBLE(ad_remaining_budget) AS ad_remaining_budget,
        ad_currency_code,
        TRY_TO_NUMBER(ad_clicks) AS ad_clicks,
        TRY_TO_NUMBER(ad_orders) AS ad_orders,
        TRY_TO_DOUBLE(ad_sales) AS ad_sales,
        TRY_TO_DOUBLE(ad_spend) AS ad_spend,
        creator_content_details,
        campaign_stats,
        asin_stats,
        seller_id,
        country,
        marketplace_id,
        scraper_id,
        report_fetched_and_loaded_at,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_scas_content_creator
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY ad_id, report_fetched_and_loaded_at::date
        ORDER BY report_fetched_and_loaded_at DESC NULLS LAST) = 1
);
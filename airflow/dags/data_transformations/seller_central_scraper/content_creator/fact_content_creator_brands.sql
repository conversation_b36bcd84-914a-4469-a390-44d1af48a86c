CREATE TABLE IF NOT EXISTS $curated_db.fact_content_creator_brands (
    ingested_at TIMESTAMP,
    ingest_date DATE,
    advertiser_id VARCHAR,
    seller_id VARCHAR,
    marketplace_id VARCHAR,
    country VARCHAR,
    entity_id VARCHAR,
    customer_id VARCHAR,
    brand_id VARCHAR,
    lbr_node_id VARCHAR,
    brand VARCHAR,
    pending VARCHAR,
    active VARCHAR,
    completed VARCHAR,
    declined VARCHAR,
    no_of_campaigns VARCHAR,
    spend VARCHAR,
    sales VARCHAR,
    clicks VARCHAR,
    orders VARCHAR,
    currency VARCHAR,
    record_created_timestamp_utc TIMESTAMP,
    record_updated_timestamp_utc TIMESTAMP
);
MERGE INTO $curated_db.fact_content_creator_brands AS target
USING (
SELECT
        report_fetched_and_loaded_at as  ingested_at,
        date(report_fetched_and_loaded_at) as ingest_date,
        advertiser_id,
        seller_id,
        marketplace_id,
        country,
        brand_entity_id as entity_id,
        brand_customer_id as customer_id,
        COALESCE(brand_id, brand_lbr_node_id) AS brand_id,
        brand_lbr_node_id as lbr_node_id,
        CASE
        WHEN LOWER(brand_name) LIKE '%fullstar%'
            THEN 'FullStar'
        WHEN LOWER(brand_name) LIKE '%aquafit%'
            THEN 'Aquafit'
        WHEN LOWER(brand_name) LIKE '%key nutrients%'
            THEN 'Key Nutrients'
        WHEN LOWER(brand_name) LIKE '%ototo%'
            THEN 'OTOTO'
        WHEN LOWER(brand_name) LIKE '%tree of life%'
            THEN 'Tree of Life'
        ELSE brand_name
        END AS brand,
        ccd.value:pending::VARCHAR as pending,
        ccd.value:active::VARCHAR as active,
        ccd.value:completed::VARCHAR as completed,
        ccd.value:declined::VARCHAR as declined,
        ccd.value:aggregatedcampaigndata:numberofcampaigns::VARCHAR as no_of_campaigns,
        ccd.value:aggregatedcampaigndata:spend::VARCHAR as spend,
        ccd.value:aggregatedcampaigndata:sales::VARCHAR as sales,
        ccd.value:aggregatedcampaigndata:clicks::VARCHAR as clicks,
        ccd.value:aggregatedcampaigndata:orders::VARCHAR as orders,
        COALESCE(ccd.value:aggregatedcampaigndata:currencycode::VARCHAR, 'USD') AS currency,
        etl_batch_run_time
FROM
        $stage_db.dedupe_scas_content_creator,
        LATERAL FLATTEN(input => TRY_PARSE_JSON(campaign_stats)) AS ccd
QUALIFY
    ROW_NUMBER() OVER (PARTITION BY brand_id ORDER BY ingested_at DESC) = 1
) AS source
ON target.brand_id = source.brand_id
WHEN MATCHED THEN
    UPDATE SET
        ingested_at = source.ingested_at,
        ingest_date = source.ingest_date,
        advertiser_id = source.advertiser_id,
        seller_id = source.seller_id,
        marketplace_id = source.marketplace_id,
        country = source.country,
        entity_id = source.entity_id,
        customer_id = source.customer_id,
        lbr_node_id = source.lbr_node_id,
        brand = source.brand,
        pending = source.pending,
        active = source.active,
        completed = source.completed,
        declined = source.declined,
        no_of_campaigns = source.no_of_campaigns,
        spend = source.spend,
        sales = source.sales,
        clicks = source.clicks,
        orders = source.orders,
        currency = source.currency,
        target.record_created_timestamp_utc = source.etl_batch_run_time,
        target.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
    INSERT (
        ingested_at,
        ingest_date,
        advertiser_id,
        seller_id,
        marketplace_id,
        country,
        entity_id,
        customer_id,
        brand_id,
        lbr_node_id,
        brand,
        pending,
        active,
        completed,
        declined,
        no_of_campaigns,
        spend,
        sales,
        clicks,
        orders,
        currency,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    )
    VALUES (
        source.ingested_at,
        source.ingest_date,
        source.advertiser_id,
        source.seller_id,
        source.marketplace_id,
        source.country,
        source.entity_id,
        source.customer_id,
        source.brand_id,
        source.lbr_node_id,
        source.brand,
        source.pending,
        source.active,
        source.completed,
        source.declined,
        source.no_of_campaigns,
        source.spend,
        source.sales,
        source.clicks,
        source.orders,
        source.currency,
        SYSDATE(),
        SYSDATE()
    );
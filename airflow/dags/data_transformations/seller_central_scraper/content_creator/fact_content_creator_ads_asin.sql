CREATE TABLE IF NOT EXISTS $curated_db.fact_content_creator_ads_asin (
    ingested_at TIMESTAMP,
    ingest_date DATE,
    advertiser_id VARCHAR,
    seller_id VARCHAR,
    marketplace_id VARCHAR,
    country VARCHAR,
    brand_id VARCHAR,
    brand VARCHAR,
    advertiser_type VARCHAR,
    ad_id VARCHAR,
    campaign_id VARCHAR,
    campaign_name VA<PERSON><PERSON><PERSON>,
    campaign_status VARCHAR,
    campaign_type VA<PERSON><PERSON><PERSON>,
    creator VA<PERSON><PERSON><PERSON>,
    creation_date DATE,
    start_date DATE,
    end_date DATE,
    last_updated_date DATE,
    asin VARCHAR,
    clicks INTEGER,
    orders INTEGER,
    spend VARCHAR,
    sales VARCHAR,
    record_created_timestamp_utc TIMESTAMP,
    record_updated_timestamp_utc TIMESTAMP
);
MERGE INTO $curated_db.fact_content_creator_ads_asin AS target
USING (
SELECT
        report_fetched_and_loaded_at as  ingested_at,
        date(report_fetched_and_loaded_at) as ingest_date,
        advertiser_id,
        seller_id,
        marketplace_id,
        country,
        brand_id,
        brand_name as brand,
        advertiser_type,
        ad_id,
        campaign_id,
        campaign_name,
        campaign_status,
        campaign_type,
        creator_profile_name as creator,
        date(ad_create_date_time) as creation_date,
        date(campaign_start_date_time) as start_date,
        date(campaign_scheduled_end_date_time) as end_date,
        date(campaign_last_updated_date_time) as last_updated_date,
        ast.value:asin::VARCHAR as asin,
        ast.value:adMetrics:clicks::INTEGER as clicks,
        ast.value:adMetrics:orders::INTEGER as orders,
        ast.value:adMetrics:earnings:amount::VARCHAR as spend,
        ast.value:adMetrics:revenue:amount::VARCHAR as sales,
        etl_batch_run_time
from $stage_db.dedupe_scas_content_creator,
LATERAL FLATTEN(input => TRY_PARSE_JSON(asin_stats)) AS ast
QUALIFY ROW_NUMBER() OVER (PARTITION BY ad_id, campaign_id, asin ORDER BY ingested_at DESC) = 1
) AS source
ON target.ad_id = source.ad_id
   AND target.campaign_id = source.campaign_id
   AND target.asin = source.asin
WHEN MATCHED THEN
    UPDATE SET
        target.ingested_at = source.ingested_at,
        target.ingest_date = source.ingest_date,
        target.advertiser_id = source.advertiser_id,
        target.seller_id = source.seller_id,
        target.marketplace_id = source.marketplace_id,
        target.country = source.country,
        target.brand_id = source.brand_id,
        target.brand = source.brand,
        target.advertiser_type = source.advertiser_type,
        target.campaign_name = source.campaign_name,
        target.campaign_status = source.campaign_status,
        target.campaign_type = source.campaign_type,
        target.creator = source.creator,
        target.creation_date = source.creation_date,
        target.start_date = source.start_date,
        target.end_date = source.end_date,
        target.last_updated_date = source.last_updated_date,
        target.clicks = source.clicks,
        target.orders = source.orders,
        target.spend = source.spend,
        target.sales = source.sales,
        target.RECORD_CREATED_TIMESTAMP_UTC = source.etl_batch_run_time,
        target.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
    INSERT (
        ingested_at,
        ingest_date,
        advertiser_id,
        seller_id,
        marketplace_id,
        country,
        brand_id,
        brand,
        advertiser_type,
        ad_id,
        campaign_id,
        campaign_name,
        campaign_status,
        campaign_type,
        creator,
        creation_date,
        start_date,
        end_date,
        last_updated_date,
        asin,
        clicks,
        orders,
        spend,
        sales,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    )
    VALUES (
        source.ingested_at,
        source.ingest_date,
        source.advertiser_id,
        source.seller_id,
        source.marketplace_id,
        source.country,
        source.brand_id,
        source.brand,
        source.advertiser_type,
        source.ad_id,
        source.campaign_id,
        source.campaign_name,
        source.campaign_status,
        source.campaign_type,
        source.creator,
        source.creation_date,
        source.start_date,
        source.end_date,
        source.last_updated_date,
        source.asin,
        source.clicks,
        source.orders,
        source.spend,
        source.sales,
        SYSDATE(),
        SYSDATE()
    );
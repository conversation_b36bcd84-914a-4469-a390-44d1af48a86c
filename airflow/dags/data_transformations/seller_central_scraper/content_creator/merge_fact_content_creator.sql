CREATE TABLE IF NOT EXISTS $curated_db.fact_scas_content_creator (
    pk VARCHAR(32),
    report_fetched_and_loaded_at VARCHAR,
    seller_id VARCHAR,
    country VARCHAR,
    marketplace_id VARCHAR,
    scraper_id VARCHAR,
    brand_entity_id VARCHAR,
    brand_customer_id VARCHAR,
    brand_name VA<PERSON><PERSON><PERSON>,
    brand_lbr_node_id VARCHAR,
    brand_id VARCHAR,
    brand_campaign_stats VARCHAR,
    campaign_id VARCHAR,
    campaign_rank NUMBER,
    campaign_qualifier VARCHAR,
    campaign_name VARCHAR,
    campaign_status VARCHAR,
    campaign_type VARCHAR,
    campaign_asins VARCHAR,
    campaign_marketplace_id VARCHAR,
    campaign_interest_tags VARCHAR,
    campaign_providing_sample BOOLEAN,
    campaign_fully_claimed BOOLEAN,
    campaign_edited BOOLEAN,
    campaign_content_submission_required BOOLEAN,
    campaign_start_date_time TIMESTAMP,
    campaign_scheduled_end_date_time TIMESTAMP,
    campaign_actual_end_date_time TIMESTAMP,
    campaign_create_date_time TIMESTAMP,
    campaign_last_updated_date_time TIMESTAMP,
    campaign_number_of_creators_required NUMBER,
    campaign_number_of_creators_accepted NUMBER,
    campaign_number_of_creators_with_content_submitted NUMBER,
    campaign_commission_percentage FLOAT,
    campaign_promotional_payment_percentage FLOAT,
    campaign_total_budget FLOAT,
    campaign_remaining_budget FLOAT,
    campaign_currency_code VARCHAR,
    campaign_clicks NUMBER,
    campaign_orders NUMBER,
    campaign_sales FLOAT,
    campaign_spend FLOAT,
    ad_id VARCHAR,
    advertiser_id VARCHAR,
    store_id VARCHAR,
    advertiser_type VARCHAR,
    creator_profile_name VARCHAR,
    creator_avatar VARCHAR,
    ad_status VARCHAR,
    ad_create_date_time TIMESTAMP,
    ad_start_date_time TIMESTAMP,
    ad_scheduled_end_date_time TIMESTAMP,
    ad_actual_end_date_time TIMESTAMP,
    ad_accepted_date_time TIMESTAMP,
    ad_last_updated_date_time TIMESTAMP,
    ad_commission_percentage FLOAT,
    ad_promotional_payment_percentage FLOAT,
    ad_total_budget FLOAT,
    ad_remaining_budget FLOAT,
    ad_currency_code VARCHAR,
    ad_clicks NUMBER,
    ad_orders NUMBER,
    ad_sales FLOAT,
    ad_spend FLOAT,
    creator_content_details VARCHAR,
    campaign_stats VARCHAR,
    asin_stats VARCHAR,
    file_name VARCHAR,
    data_source VARCHAR,
    created_by VARCHAR DEFAULT 'DAG: fact_scas_content_creator',
    updated_by VARCHAR DEFAULT 'DAG: fact_scas_content_creator',
    record_created_timestamp_utc TIMESTAMP DEFAULT SYSDATE(),
    record_updated_timestamp_utc TIMESTAMP DEFAULT SYSDATE()
);

MERGE INTO $curated_db.fact_scas_content_creator AS tgt USING (
    SELECT
        cc.pk,
        cc.report_fetched_and_loaded_at,
        cc.scraper_id,
        cc.seller_id,
        cc.country,
        cc.marketplace_id,
        cc.brand_entity_id,
        cc.brand_customer_id,
        cc.brand_name,
        cc.brand_lbr_node_id,
        cc.brand_id,
        cc.brand_campaign_stats,
        cc.campaign_id,
        cc.campaign_rank,
        cc.campaign_qualifier,
        cc.campaign_name,
        cc.campaign_status,
        cc.campaign_type,
        cc.campaign_asins,
        cc.campaign_marketplace_id,
        cc.campaign_interest_tags,
        cc.campaign_providing_sample,
        cc.campaign_fully_claimed,
        cc.campaign_edited,
        cc.campaign_content_submission_required,
        cc.campaign_start_date_time,
        cc.campaign_scheduled_end_date_time,
        cc.campaign_actual_end_date_time,
        cc.campaign_create_date_time,
        cc.campaign_last_updated_date_time,
        cc.campaign_number_of_creators_required,
        cc.campaign_number_of_creators_accepted,
        cc.campaign_number_of_creators_with_content_submitted,
        cc.campaign_commission_percentage,
        cc.campaign_promotional_payment_percentage,
        cc.campaign_total_budget,
        cc.campaign_remaining_budget,
        cc.campaign_currency_code,
        cc.campaign_clicks,
        cc.campaign_orders,
        cc.campaign_sales,
        cc.campaign_spend,
        cc.ad_id,
        cc.advertiser_id,
        cc.store_id,
        cc.advertiser_type,
        cc.creator_profile_name,
        cc.creator_avatar,
        cc.ad_status,
        cc.ad_create_date_time,
        cc.ad_start_date_time,
        cc.ad_scheduled_end_date_time,
        cc.ad_actual_end_date_time,
        cc.ad_accepted_date_time,
        cc.ad_last_updated_date_time,
        cc.ad_commission_percentage,
        cc.ad_promotional_payment_percentage,
        cc.ad_total_budget,
        cc.ad_remaining_budget,
        cc.ad_currency_code,
        cc.ad_clicks,
        cc.ad_orders,
        cc.ad_sales,
        cc.ad_spend,
        cc.creator_content_details,
        cc.campaign_stats,
        cc.asin_stats,
        cc.file_name,
        'dwh.staging.merge_scas_content_creator' AS data_source,
        'DAG: fact_scas_content_creator' AS created_by,
        'DAG: fact_scas_content_creator' AS updated_by,
        cc.record_created_timestamp_utc,
        cc.record_updated_timestamp_utc
    FROM $stage_db.merge_scas_content_creator cc
) AS src ON tgt.pk = src.pk WHEN MATCHED
AND src.record_updated_timestamp_utc > tgt.record_updated_timestamp_utc THEN
UPDATE
SET
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.scraper_id = src.scraper_id,
    tgt.seller_id = src.seller_id,
    tgt.country = src.country,
    tgt.marketplace_id = src.marketplace_id,
    tgt.brand_entity_id = src.brand_entity_id,
    tgt.brand_customer_id = src.brand_customer_id,
    tgt.brand_name = src.brand_name,
    tgt.brand_lbr_node_id = src.brand_lbr_node_id,
    tgt.brand_id = src.brand_id,
    tgt.brand_campaign_stats = src.brand_campaign_stats,
    tgt.campaign_id = src.campaign_id,
    tgt.campaign_rank = src.campaign_rank,
    tgt.campaign_qualifier = src.campaign_qualifier,
    tgt.campaign_name = src.campaign_name,
    tgt.campaign_status = src.campaign_status,
    tgt.campaign_type = src.campaign_type,
    tgt.campaign_asins = src.campaign_asins,
    tgt.campaign_marketplace_id = src.campaign_marketplace_id,
    tgt.campaign_interest_tags = src.campaign_interest_tags,
    tgt.campaign_providing_sample = src.campaign_providing_sample,
    tgt.campaign_fully_claimed = src.campaign_fully_claimed,
    tgt.campaign_edited = src.campaign_edited,
    tgt.campaign_content_submission_required = src.campaign_content_submission_required,
    tgt.campaign_start_date_time = src.campaign_start_date_time,
    tgt.campaign_scheduled_end_date_time = src.campaign_scheduled_end_date_time,
    tgt.campaign_actual_end_date_time = src.campaign_actual_end_date_time,
    tgt.campaign_create_date_time = src.campaign_create_date_time,
    tgt.campaign_last_updated_date_time = src.campaign_last_updated_date_time,
    tgt.campaign_number_of_creators_required = src.campaign_number_of_creators_required,
    tgt.campaign_number_of_creators_accepted = src.campaign_number_of_creators_accepted,
    tgt.campaign_number_of_creators_with_content_submitted = src.campaign_number_of_creators_with_content_submitted,
    tgt.campaign_commission_percentage = src.campaign_commission_percentage,
    tgt.campaign_promotional_payment_percentage = src.campaign_promotional_payment_percentage,
    tgt.campaign_total_budget = src.campaign_total_budget,
    tgt.campaign_remaining_budget = src.campaign_remaining_budget,
    tgt.campaign_currency_code = src.campaign_currency_code,
    tgt.campaign_clicks = src.campaign_clicks,
    tgt.campaign_orders = src.campaign_orders,
    tgt.campaign_sales = src.campaign_sales,
    tgt.campaign_spend = src.campaign_spend,
    tgt.ad_id = src.ad_id,
    tgt.advertiser_id = src.advertiser_id,
    tgt.store_id = src.store_id,
    tgt.advertiser_type = src.advertiser_type,
    tgt.creator_profile_name = src.creator_profile_name,
    tgt.creator_avatar = src.creator_avatar,
    tgt.ad_status = src.ad_status,
    tgt.ad_create_date_time = src.ad_create_date_time,
    tgt.ad_start_date_time = src.ad_start_date_time,
    tgt.ad_scheduled_end_date_time = src.ad_scheduled_end_date_time,
    tgt.ad_actual_end_date_time = src.ad_actual_end_date_time,
    tgt.ad_accepted_date_time = src.ad_accepted_date_time,
    tgt.ad_last_updated_date_time = src.ad_last_updated_date_time,
    tgt.ad_commission_percentage = src.ad_commission_percentage,
    tgt.ad_promotional_payment_percentage = src.ad_promotional_payment_percentage,
    tgt.ad_total_budget = src.ad_total_budget,
    tgt.ad_remaining_budget = src.ad_remaining_budget,
    tgt.ad_currency_code = src.ad_currency_code,
    tgt.ad_clicks = src.ad_clicks,
    tgt.ad_orders = src.ad_orders,
    tgt.ad_sales = src.ad_sales,
    tgt.ad_spend = src.ad_spend,
    tgt.creator_content_details = src.creator_content_details,
    tgt.campaign_stats = src.campaign_stats,
    tgt.asin_stats = src.asin_stats,
    tgt.file_name = src.file_name,
    tgt.data_source = src.data_source,
    tgt.updated_by = src.updated_by,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
        pk,
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        country,
        marketplace_id,
        brand_entity_id,
        brand_customer_id,
        brand_name,
        brand_lbr_node_id,
        brand_id,
        brand_campaign_stats,
        campaign_id,
        campaign_rank,
        campaign_qualifier,
        campaign_name,
        campaign_status,
        campaign_type,
        campaign_asins,
        campaign_marketplace_id,
        campaign_interest_tags,
        campaign_providing_sample,
        campaign_fully_claimed,
        campaign_edited,
        campaign_content_submission_required,
        campaign_start_date_time,
        campaign_scheduled_end_date_time,
        campaign_actual_end_date_time,
        campaign_create_date_time,
        campaign_last_updated_date_time,
        campaign_number_of_creators_required,
        campaign_number_of_creators_accepted,
        campaign_number_of_creators_with_content_submitted,
        campaign_commission_percentage,
        campaign_promotional_payment_percentage,
        campaign_total_budget,
        campaign_remaining_budget,
        campaign_currency_code,
        campaign_clicks,
        campaign_orders,
        campaign_sales,
        campaign_spend,
        ad_id,
        advertiser_id,
        store_id,
        advertiser_type,
        creator_profile_name,
        creator_avatar,
        ad_status,
        ad_create_date_time,
        ad_start_date_time,
        ad_scheduled_end_date_time,
        ad_actual_end_date_time,
        ad_accepted_date_time,
        ad_last_updated_date_time,
        ad_commission_percentage,
        ad_promotional_payment_percentage,
        ad_total_budget,
        ad_remaining_budget,
        ad_currency_code,
        ad_clicks,
        ad_orders,
        ad_sales,
        ad_spend,
        creator_content_details,
        campaign_stats,
        asin_stats,
        file_name,
        data_source,
        created_by,
        updated_by,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    )
VALUES (
        src.pk,
        src.report_fetched_and_loaded_at,
        src.scraper_id,
        src.seller_id,
        src.country,
        src.marketplace_id,
        src.brand_entity_id,
        src.brand_customer_id,
        src.brand_name,
        src.brand_lbr_node_id,
        src.brand_id,
        src.brand_campaign_stats,
        src.campaign_id,
        src.campaign_rank,
        src.campaign_qualifier,
        src.campaign_name,
        src.campaign_status,
        src.campaign_type,
        src.campaign_asins,
        src.campaign_marketplace_id,
        src.campaign_interest_tags,
        src.campaign_providing_sample,
        src.campaign_fully_claimed,
        src.campaign_edited,
        src.campaign_content_submission_required,
        src.campaign_start_date_time,
        src.campaign_scheduled_end_date_time,
        src.campaign_actual_end_date_time,
        src.campaign_create_date_time,
        src.campaign_last_updated_date_time,
        src.campaign_number_of_creators_required,
        src.campaign_number_of_creators_accepted,
        src.campaign_number_of_creators_with_content_submitted,
        src.campaign_commission_percentage,
        src.campaign_promotional_payment_percentage,
        src.campaign_total_budget,
        src.campaign_remaining_budget,
        src.campaign_currency_code,
        src.campaign_clicks,
        src.campaign_orders,
        src.campaign_sales,
        src.campaign_spend,
        src.ad_id,
        src.advertiser_id,
        src.store_id,
        src.advertiser_type,
        src.creator_profile_name,
        src.creator_avatar,
        src.ad_status,
        src.ad_create_date_time,
        src.ad_start_date_time,
        src.ad_scheduled_end_date_time,
        src.ad_actual_end_date_time,
        src.ad_accepted_date_time,
        src.ad_last_updated_date_time,
        src.ad_commission_percentage,
        src.ad_promotional_payment_percentage,
        src.ad_total_budget,
        src.ad_remaining_budget,
        src.ad_currency_code,
        src.ad_clicks,
        src.ad_orders,
        src.ad_sales,
        src.ad_spend,
        src.creator_content_details,
        src.campaign_stats,
        src.asin_stats,
        src.file_name,
        src.data_source,
        src.created_by,
        src.updated_by,
        SYSDATE(),
        SYSDATE()
    );
CREATE TABLE IF NOT EXISTS $curated_db.fact_content_creator_campaigns (
    ingested_at TIMESTAMP,
    ingest_date DATE,
    creation_date TIMESTAMP,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    last_updated_date TIMESTAMP,
    seller_id VARCHAR,
    marketplace_id VARCHAR,
    brand VARCHAR,
    marketplace_country_code VARCHAR,
    campaign_id VARCHAR,
    rank INTEGER,
    campaign_qualifier VARCHAR,
    advertiser_id VARCHAR,
    brand_id VARCHAR,
    advertiser_type VARCHAR,
    campaign_name VARCHAR,
    campaign_status VARCHAR,
    providing_sample BOOLEAN,
    fully_claimed BOOLEAN,
    campaign_type VARCHAR,
    campaign_edited BOOLEAN,
    content_submission_required BOOLEAN,
    number_of_creators_required INTEGER,
    number_of_creators_accepted INTEGER,
    number_of_creators_with_content_submitted INTEGER,
    commission_percentage DECIMAL(5,2),
    promotional_payment_percentage DECIMAL(5,2),
    total_budget DECIMAL(15,2),
    remaining_budget DECIMAL(15,2),
    currency_code VARCHAR(3),
    clicks INTEGER,
    orders INTEGER,
    sales DECIMAL(15,2),
    spend DECIMAL(15,2),
    campaign_asins VARCHAR,
    record_created_timestamp_utc TIMESTAMP,
    record_updated_timestamp_utc TIMESTAMP
);

MERGE INTO $curated_db.fact_content_creator_campaigns AS target
USING (
    SELECT
        report_fetched_and_loaded_at as  ingested_at,
        date(report_fetched_and_loaded_at) as ingest_date,
        campaign_create_date_time as creation_date,
        campaign_start_date_time as start_date,
        COALESCE(campaign_actual_end_date_time, campaign_scheduled_end_date_time) as end_date,
        campaign_last_updated_date_time as last_updated_date,
        seller_id,
        campaign_marketplace_id as marketplace_id,
        brand_name as brand,
        country as marketplace_country_code,
        campaign_id,
        campaign_rank as  rank,
        campaign_qualifier,
        advertiser_id,
        brand_id,
        advertiser_type,
        campaign_name,
        campaign_status,
        campaign_providing_sample as providing_sample,
        campaign_fully_claimed as fully_claimed,
        campaign_type,
        campaign_edited,
        campaign_content_submission_required as content_submission_required,
        campaign_number_of_creators_required as number_of_creators_required,
        campaign_number_of_creators_accepted as number_of_creators_accepted,
        campaign_number_of_creators_with_content_submitted as number_of_creators_with_content_submitted,
        campaign_commission_percentage as commission_percentage,
        campaign_promotional_payment_percentage as promotional_payment_percentage,
        campaign_total_budget as total_budget,
        campaign_remaining_budget as remaining_budget,
        campaign_currency_code as currency_code,
        campaign_clicks as clicks,
        campaign_orders as orders,
        campaign_sales as sales,
        campaign_spend as spend,
        campaign_asins,
        etl_batch_run_time
    FROM
        $stage_db.dedupe_scas_content_creator
    QUALIFY ROW_NUMBER() OVER(
            PARTITION BY campaign_id
            ORDER BY report_fetched_and_loaded_at DESC NULLS LAST) = 1
) AS source
ON target.campaign_id = source.campaign_id
WHEN MATCHED THEN
    UPDATE SET
        ingested_at = source.ingested_at,
        ingest_date = source.ingest_date,
        creation_date = source.creation_date,
        start_date = source.start_date,
        end_date = source.end_date,
        last_updated_date = source.last_updated_date,
        seller_id = source.seller_id,
        marketplace_id = source.marketplace_id,
        brand = source.brand,
        marketplace_country_code = source.marketplace_country_code,
        rank = source.rank,
        campaign_qualifier = source.campaign_qualifier,
        advertiser_id = source.advertiser_id,
        brand_id = source.brand_id,
        advertiser_type = source.advertiser_type,
        campaign_name = source.campaign_name,
        campaign_status = source.campaign_status,
        providing_sample = source.providing_sample,
        fully_claimed = source.fully_claimed,
        campaign_type = source.campaign_type,
        campaign_edited = source.campaign_edited,
        content_submission_required = source.content_submission_required,
        number_of_creators_required = source.number_of_creators_required,
        number_of_creators_accepted = source.number_of_creators_accepted,
        number_of_creators_with_content_submitted = source.number_of_creators_with_content_submitted,
        commission_percentage = source.commission_percentage,
        promotional_payment_percentage = source.promotional_payment_percentage,
        total_budget = source.total_budget,
        remaining_budget = source.remaining_budget,
        currency_code = source.currency_code,
        clicks = source.clicks,
        orders = source.orders,
        sales = source.sales,
        spend = source.spend,
        campaign_asins = source.campaign_asins,
        record_created_timestamp_utc = source.etl_batch_run_time,
        record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
    INSERT (
        ingested_at,
        ingest_date,
        creation_date,
        start_date,
        end_date,
        last_updated_date,
        seller_id,
        marketplace_id,
        brand,
        marketplace_country_code,
        campaign_id,
        rank,
        campaign_qualifier,
        advertiser_id,
        brand_id,
        advertiser_type,
        campaign_name,
        campaign_status,
        providing_sample,
        fully_claimed,
        campaign_type,
        campaign_edited,
        content_submission_required,
        number_of_creators_required,
        number_of_creators_accepted,
        number_of_creators_with_content_submitted,
        commission_percentage,
        promotional_payment_percentage,
        total_budget,
        remaining_budget,
        currency_code,
        clicks,
        orders,
        sales,
        spend,
        campaign_asins,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    )
    VALUES (
        source.ingested_at,
        source.ingest_date,
        source.creation_date,
        source.start_date,
        source.end_date,
        source.last_updated_date,
        source.seller_id,
        source.marketplace_id,
        source.brand,
        source.marketplace_country_code,
        source.campaign_id,
        source.rank,
        source.campaign_qualifier,
        source.advertiser_id,
        source.brand_id,
        source.advertiser_type,
        source.campaign_name,
        source.campaign_status,
        source.providing_sample,
        source.fully_claimed,
        source.campaign_type,
        source.campaign_edited,
        source.content_submission_required,
        source.number_of_creators_required,
        source.number_of_creators_accepted,
        source.number_of_creators_with_content_submitted,
        source.commission_percentage,
        source.promotional_payment_percentage,
        source.total_budget,
        source.remaining_budget,
        source.currency_code,
        source.clicks,
        source.orders,
        source.sales,
        source.spend,
        source.campaign_asins,
        SYSDATE(),
        SYSDATE()
    );

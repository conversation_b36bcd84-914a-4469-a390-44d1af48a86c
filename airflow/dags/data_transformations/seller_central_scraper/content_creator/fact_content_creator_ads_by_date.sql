CREATE TABLE IF NOT EXISTS $curated_db.fact_content_creator_ads_by_date (
    ingested_at TIMESTAMP,
    ingest_date DATE,
    advertiser_id VARCHAR,
    seller_id VARCHAR,
    marketplace_id VARCHAR,
    country VARCHAR,
    brand_id VARCHAR,
    brand VARCHAR,
    advertiser_type VARCHAR,
    ad_id VARCHAR,
    campaign_id VARCHAR,
    campaign_name VA<PERSON>HA<PERSON>,
    campaign_status VARCHAR,
    campaign_type VA<PERSON>HA<PERSON>,
    creator VA<PERSON><PERSON><PERSON>,
    creation_date DATE,
    start_date DATE,
    end_date DATE,
    last_updated_date DATE,
    content_id VARCHAR,
    content_type VARCHAR,
    content_url VARCHAR,
    commission_percentage VARCHAR,
    report_date DATE,
    clicks INTEGER,
    orders INTEGER,
    spend VARCHAR,
    sales VARCHAR,
    record_created_timestamp_utc TIMESTAMP,
    record_updated_timestamp_utc TIMESTAMP
);

MERGE INTO $curated_db.fact_content_creator_ads_by_date AS target
USING (
SELECT
        report_fetched_and_loaded_at as  ingested_at,
        date(report_fetched_and_loaded_at) as ingest_date,
        advertiser_id,
        seller_id,
        marketplace_id,
        country,
        brand_id,
        brand_name as brand,
        advertiser_type,
        ad_id,
        campaign_id,
        campaign_name,
        campaign_status,
        campaign_type,
        creator_profile_name as creator,
        date(ad_create_date_time) as creation_date,
        date(campaign_start_date_time) as start_date,
        date(campaign_scheduled_end_date_time) as end_date,
        date(campaign_last_updated_date_time) as last_updated_date,
        ccd.value:contentId::VARCHAR as content_id,
        ccd.value:contentType::VARCHAR as content_type,
        ccd.value:contentUrl::VARCHAR as content_url,
        ad_commission_percentage as commission_percentage,
        cs.value:DATE::DATE as report_date,
        cs.value:CLICKS::INTEGER as clicks,
        cs.value:ORDERS::INTEGER as orders,
        cs.value:SPEND::VARCHAR as spend,
        cs.value:SALES::VARCHAR as sales,
        etl_batch_run_time
from $stage_db.dedupe_scas_content_creator,
LATERAL FLATTEN(input => TRY_PARSE_JSON(
    REPLACE(REPLACE(REPLACE(REPLACE(creator_content_details, 
        '\'', '"'),
        'None', 'null'),
        'True', 'true'),
        'False', 'false')
)) AS ccd,
LATERAL FLATTEN(input => TRY_PARSE_JSON(campaign_stats)) AS cs
QUALIFY ROW_NUMBER() OVER (PARTITION BY brand_id, campaign_id, ad_id, content_id, report_date ORDER BY ingested_at DESC) = 1
) AS source
ON target.brand_id = source.brand_id
   AND target.campaign_id = source.campaign_id
   AND target.ad_id = source.ad_id
   AND target.content_id = source.content_id
   AND target.report_date = source.report_date
WHEN MATCHED THEN
    UPDATE SET
        ingested_at = source.ingested_at,
        ingest_date = source.ingest_date,
        advertiser_id = source.advertiser_id,
        seller_id = source.seller_id,
        marketplace_id = source.marketplace_id,
        country = source.country,
        brand = source.brand,
        advertiser_type = source.advertiser_type,
        campaign_name = source.campaign_name,
        campaign_status = source.campaign_status,
        campaign_type = source.campaign_type,
        creator = source.creator,
        creation_date = source.creation_date,
        start_date = source.start_date,
        end_date = source.end_date,
        last_updated_date = source.last_updated_date,
        content_type = source.content_type,
        content_url = source.content_url,
        commission_percentage = source.commission_percentage,
        clicks = source.clicks,
        orders = source.orders,
        spend = source.spend,
        sales = source.sales,
        target.RECORD_CREATED_TIMESTAMP_UTC = source.etl_batch_run_time,
        target.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
    INSERT (
        ingested_at,
        ingest_date,
        advertiser_id,
        seller_id,
        marketplace_id,
        country,
        brand_id,
        brand,
        advertiser_type,
        ad_id,
        campaign_id,
        campaign_name,
        campaign_status,
        campaign_type,
        creator,
        creation_date,
        start_date,
        end_date,
        last_updated_date,
        content_id,
        content_type,
        content_url,
        commission_percentage,
        report_date,
        clicks,
        orders,
        spend,
        sales,
        record_created_timestamp_utc,
        record_updated_timestamp_utc
    )
    VALUES (
        source.ingested_at,
        source.ingest_date,
        source.advertiser_id,
        source.seller_id,
        source.marketplace_id,
        source.country,
        source.brand_id,
        source.brand,
        source.advertiser_type,
        source.ad_id,
        source.campaign_id,
        source.campaign_name,
        source.campaign_status,
        source.campaign_type,
        source.creator,
        source.creation_date,
        source.start_date,
        source.end_date,
        source.last_updated_date,
        source.content_id,
        source.content_type,
        source.content_url,
        source.commission_percentage,
        source.report_date,
        source.clicks,
        source.orders,
        source.spend,
        source.sales,
        SYSDATE(),
        SYSDATE()
    );

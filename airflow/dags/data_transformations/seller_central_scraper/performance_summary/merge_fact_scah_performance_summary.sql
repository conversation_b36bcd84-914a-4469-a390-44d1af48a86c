CREATE TABLE IF NOT EXISTS $curated_db.fact_scah_performance_summary (
	PK VARCHAR(32),
	REPORT_FETCHED_AND_LOADED_AT VARCHAR,
	SCRAPER_ID VARCHAR,
	SELLER_ID VARCHAR,
    SELLER_NAME VARCHAR,
	COUNTRY_CODE VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_START VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_END VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_COUNT NUMBER(38,0),
	ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_WITH_DEFECTS_COUNT NUMBER(38,0),
	ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_WITH_DEFECTS_STATUS VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_CLAIMS_COUNT NUMBER(38,0),
	ORDER_DEFECT_METRICS_METRICS_AFN_CLAIMS_STATUS VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_CHARGEBACKS_COUNT NUMBER(38,0),
	ORDER_DEFECT_METRICS_METRICS_AFN_CHARGEBACKS_STATUS VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_NEGATIVE_FEEDBACK_COUNT NUMBER(38,0),
	ORDER_DEFECT_METRICS_METRICS_AFN_NEGATIVE_FEEDBACK_STATUS VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_FULFILLMENT_TYPE VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_VALUE NUMBER(10,4),
	ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_CONDITION_TYPE VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_TARGET_TYPE VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_RATE NUMBER(10,8),
	ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_OVERALL_STATUS VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_VALUE_VALUE NUMBER(10,8),
	ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_VALUE_TYPE VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_VALUE NUMBER(10,4),
	ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_CONDITION_TYPE VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_TARGET_TYPE VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_START VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_END VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_COUNT NUMBER(38,0),
	ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_WITH_DEFECTS_COUNT NUMBER(38,0),
	ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_WITH_DEFECTS_STATUS VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_CLAIMS_COUNT NUMBER(38,0),
	ORDER_DEFECT_METRICS_METRICS_MFN_CLAIMS_STATUS VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_CHARGEBACKS_COUNT NUMBER(38,0),
	ORDER_DEFECT_METRICS_METRICS_MFN_CHARGEBACKS_STATUS VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_NEGATIVE_FEEDBACK_COUNT NUMBER(38,0),
	ORDER_DEFECT_METRICS_METRICS_MFN_NEGATIVE_FEEDBACK_STATUS VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_FULFILLMENT_TYPE VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_VALUE NUMBER(10,4),
	ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_CONDITION_TYPE VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_TARGET_TYPE VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_RATE NUMBER(10,8),
	ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_OVERALL_STATUS VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_VALUE_VALUE NUMBER(10,8),
	ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_VALUE_TYPE VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_VALUE NUMBER(10,4),
	ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_CONDITION_TYPE VARCHAR,
	ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_TARGET_TYPE VARCHAR,
	INVOICE_DEFECT_METRIC VARCHAR,
	LATE_SHIP_METRIC_TIME_FRAME_START VARCHAR,
	LATE_SHIP_METRIC_TIME_FRAME_END VARCHAR,
	LATE_SHIP_METRIC_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	LATE_SHIP_METRIC_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	LATE_SHIP_METRIC_ORDER_COUNT NUMBER(38,0),
	LATE_SHIP_METRIC_LATE_SHIPMENT_COUNT NUMBER(38,0),
	LATE_SHIP_METRIC_LATE_SHIPMENT_STATUS VARCHAR,
	LATE_SHIP_METRIC_MARKETPLACE_ID VARCHAR,
	LATE_SHIP_METRIC_TARGET_VALUE NUMBER(10,4),
	LATE_SHIP_METRIC_TARGET_CONDITION_TYPE VARCHAR,
	LATE_SHIP_METRIC_TARGET_TARGET_TYPE VARCHAR,
	LATE_SHIP_METRIC_RATE NUMBER(10,8),
	LATE_SHIP_METRIC_METRIC_SUMMARY_OVERALL_STATUS VARCHAR,
	LATE_SHIP_METRIC_METRIC_SUMMARY_VALUE_VALUE NUMBER(10,8),
	LATE_SHIP_METRIC_METRIC_SUMMARY_VALUE_TYPE VARCHAR,
	LATE_SHIP_METRIC_METRIC_SUMMARY_TARGET_VALUE NUMBER(10,4),
	LATE_SHIP_METRIC_METRIC_SUMMARY_TARGET_CONDITION_TYPE VARCHAR,
	LATE_SHIP_METRIC_METRIC_SUMMARY_TARGET_TARGET_TYPE VARCHAR,
	PREFULFILLMENT_CANCELLATION_METRIC_TIME_FRAME_START VARCHAR,
	PREFULFILLMENT_CANCELLATION_METRIC_TIME_FRAME_END VARCHAR,
	PREFULFILLMENT_CANCELLATION_METRIC_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	PREFULFILLMENT_CANCELLATION_METRIC_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	PREFULFILLMENT_CANCELLATION_METRIC_ORDER_COUNT NUMBER(38,0),
	PREFULFILLMENT_CANCELLATION_METRIC_CANCELLATION_COUNT NUMBER(38,0),
	PREFULFILLMENT_CANCELLATION_METRIC_CANCELLATION_STATUS VARCHAR,
	PREFULFILLMENT_CANCELLATION_METRIC_MARKETPLACE_ID VARCHAR,
	PREFULFILLMENT_CANCELLATION_METRIC_TARGET_VALUE NUMBER(10,4),
	PREFULFILLMENT_CANCELLATION_METRIC_TARGET_CONDITION_TYPE VARCHAR,
	PREFULFILLMENT_CANCELLATION_METRIC_TARGET_TARGET_TYPE VARCHAR,
	PREFULFILLMENT_CANCELLATION_METRIC_RATE NUMBER(10,8),
	PREFULFILLMENT_CANCELLATION_METRIC_METRIC_SUMMARY_OVERALL_STATUS VARCHAR,
	PREFULFILLMENT_CANCELLATION_METRIC_METRIC_SUMMARY_VALUE_VALUE NUMBER(10,8),
	PREFULFILLMENT_CANCELLATION_METRIC_METRIC_SUMMARY_VALUE_TYPE VARCHAR,
	PREFULFILLMENT_CANCELLATION_METRIC_METRIC_SUMMARY_TARGET_VALUE NUMBER(10,4),
	PREFULFILLMENT_CANCELLATION_METRIC_METRIC_SUMMARY_TARGET_CONDITION_TYPE VARCHAR,
	PREFULFILLMENT_CANCELLATION_METRIC_METRIC_SUMMARY_TARGET_TARGET_TYPE VARCHAR,
	VALID_TRACKING_METRICS_TIME_FRAME_START VARCHAR,
	VALID_TRACKING_METRICS_TIME_FRAME_END VARCHAR,
	VALID_TRACKING_METRICS_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	VALID_TRACKING_METRICS_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	VALID_TRACKING_METRICS_SHIPMENT_COUNT NUMBER(38,0),
	VALID_TRACKING_METRICS_VALID_TRACKING_COUNT NUMBER(38,0),
	VALID_TRACKING_METRICS_VALID_TRACKING_STATUS VARCHAR,
	VALID_TRACKING_METRICS_SHIPMENT_COUNT_WITH_EXEMPT_PACKAGES NUMBER(38,0),
	VALID_TRACKING_METRICS_EXEMPTED_SHIPMENT_COUNT NUMBER(38,0),
	VALID_TRACKING_METRICS_EXEMPTED_DEFECT_COUNT NUMBER(38,0),
	VALID_TRACKING_METRICS_DEFECT_COUNT NUMBER(38,0),
	VALID_TRACKING_METRICS_TARGET_VALUE NUMBER(10,4),
	VALID_TRACKING_METRICS_TARGET_CONDITION_TYPE VARCHAR,
	VALID_TRACKING_METRICS_TARGET_TARGET_TYPE VARCHAR,
	VALID_TRACKING_METRICS_RATE NUMBER(10,8),
	VALID_TRACKING_METRICS_METRIC_SUMMARY_OVERALL_STATUS VARCHAR,
	VALID_TRACKING_METRICS_METRIC_SUMMARY_VALUE_VALUE NUMBER(10,8),
	VALID_TRACKING_METRICS_METRIC_SUMMARY_VALUE_TYPE VARCHAR,
	VALID_TRACKING_METRICS_METRIC_SUMMARY_TARGET_VALUE NUMBER(10,4),
	VALID_TRACKING_METRICS_METRIC_SUMMARY_TARGET_CONDITION_TYPE VARCHAR,
	VALID_TRACKING_METRICS_METRIC_SUMMARY_TARGET_TARGET_TYPE VARCHAR,
	ON_TIME_DELIVERY_METRICS_TIME_FRAME_START VARCHAR,
	ON_TIME_DELIVERY_METRICS_TIME_FRAME_END VARCHAR,
	ON_TIME_DELIVERY_METRICS_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	ON_TIME_DELIVERY_METRICS_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	ON_TIME_DELIVERY_METRICS_SHIPMENT_COUNT_WITH_VALID_TRACKING NUMBER(38,0),
	ON_TIME_DELIVERY_METRICS_ON_TIME_DELIVERY_COUNT NUMBER(38,0),
	ON_TIME_DELIVERY_METRICS_ON_TIME_DELIVERY_STATUS VARCHAR,
	ON_TIME_DELIVERY_METRICS_TARGET_VALUE NUMBER(10,4),
	ON_TIME_DELIVERY_METRICS_TARGET_CONDITION_TYPE VARCHAR,
	ON_TIME_DELIVERY_METRICS_TARGET_TARGET_TYPE VARCHAR,
	ON_TIME_DELIVERY_METRICS_RATE NUMBER(10,8),
	ON_TIME_DELIVERY_METRICS_METRIC_SUMMARY_OVERALL_STATUS VARCHAR,
	ON_TIME_DELIVERY_METRICS_METRIC_SUMMARY_VALUE_VALUE NUMBER(10,8),
	ON_TIME_DELIVERY_METRICS_METRIC_SUMMARY_VALUE_TYPE VARCHAR,
	ON_TIME_DELIVERY_METRICS_METRIC_SUMMARY_TARGET_VALUE NUMBER(10,4),
	ON_TIME_DELIVERY_METRICS_METRIC_SUMMARY_TARGET_CONDITION_TYPE VARCHAR,
	ON_TIME_DELIVERY_METRICS_METRIC_SUMMARY_TARGET_TARGET_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_AUTOMATED_BRAND_PROTECTION_TIME_FRAME_START VARCHAR,
	LISTING_LEVEL_METRICS_AUTOMATED_BRAND_PROTECTION_TIME_FRAME_END VARCHAR,
	LISTING_LEVEL_METRICS_AUTOMATED_BRAND_PROTECTION_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	LISTING_LEVEL_METRICS_AUTOMATED_BRAND_PROTECTION_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	LISTING_LEVEL_METRICS_AUTOMATED_BRAND_PROTECTION_DEFECTS_COUNT NUMBER(38,0),
	LISTING_LEVEL_METRICS_AUTOMATED_BRAND_PROTECTION_DEFECTS_STATUS VARCHAR,
	LISTING_LEVEL_METRICS_AUTOMATED_BRAND_PROTECTION_TARGET_VALUE NUMBER(38,0),
	LISTING_LEVEL_METRICS_AUTOMATED_BRAND_PROTECTION_TARGET_CONDITION_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_AUTOMATED_BRAND_PROTECTION_TARGET_TARGET_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_CONDITION_TIME_FRAME_START VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_CONDITION_TIME_FRAME_END VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_CONDITION_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_CONDITION_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_CONDITION_DEFECTS_COUNT NUMBER(38,0),
	LISTING_LEVEL_METRICS_PRODUCT_CONDITION_DEFECTS_STATUS VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_CONDITION_TARGET_VALUE NUMBER(38,0),
	LISTING_LEVEL_METRICS_PRODUCT_CONDITION_TARGET_CONDITION_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_CONDITION_TARGET_TARGET_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_INTELLECTUAL_PROPERTY_TIME_FRAME_START VARCHAR,
	LISTING_LEVEL_METRICS_INTELLECTUAL_PROPERTY_TIME_FRAME_END VARCHAR,
	LISTING_LEVEL_METRICS_INTELLECTUAL_PROPERTY_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	LISTING_LEVEL_METRICS_INTELLECTUAL_PROPERTY_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	LISTING_LEVEL_METRICS_INTELLECTUAL_PROPERTY_DEFECTS_COUNT NUMBER(38,0),
	LISTING_LEVEL_METRICS_INTELLECTUAL_PROPERTY_DEFECTS_STATUS VARCHAR,
	LISTING_LEVEL_METRICS_INTELLECTUAL_PROPERTY_TARGET_VALUE NUMBER(38,0),
	LISTING_LEVEL_METRICS_INTELLECTUAL_PROPERTY_TARGET_CONDITION_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_INTELLECTUAL_PROPERTY_TARGET_TARGET_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_REGULATORY_COMPLIANCE_TIME_FRAME_START VARCHAR,
	LISTING_LEVEL_METRICS_REGULATORY_COMPLIANCE_TIME_FRAME_END VARCHAR,
	LISTING_LEVEL_METRICS_REGULATORY_COMPLIANCE_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	LISTING_LEVEL_METRICS_REGULATORY_COMPLIANCE_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	LISTING_LEVEL_METRICS_REGULATORY_COMPLIANCE_DEFECTS_COUNT NUMBER(38,0),
	LISTING_LEVEL_METRICS_REGULATORY_COMPLIANCE_DEFECTS_STATUS VARCHAR,
	LISTING_LEVEL_METRICS_REGULATORY_COMPLIANCE_TARGET_VALUE NUMBER(38,0),
	LISTING_LEVEL_METRICS_REGULATORY_COMPLIANCE_TARGET_CONDITION_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_REGULATORY_COMPLIANCE_TARGET_TARGET_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_AUTHENTICITY_TIME_FRAME_START VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_AUTHENTICITY_TIME_FRAME_END VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_AUTHENTICITY_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_AUTHENTICITY_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_AUTHENTICITY_DEFECTS_COUNT NUMBER(38,0),
	LISTING_LEVEL_METRICS_PRODUCT_AUTHENTICITY_DEFECTS_STATUS VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_AUTHENTICITY_TARGET_VALUE NUMBER(38,0),
	LISTING_LEVEL_METRICS_PRODUCT_AUTHENTICITY_TARGET_CONDITION_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_AUTHENTICITY_TARGET_TARGET_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_START VARCHAR,
	LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_END VARCHAR,
	LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_DEFECTS_COUNT NUMBER(38,0),
	LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_DEFECTS_STATUS VARCHAR,
	LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_VALUE NUMBER(38,0),
	LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_CONDITION_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_TARGET_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_START VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_END VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_DEFECTS_COUNT NUMBER(38,0),
	LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_DEFECTS_STATUS VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_VALUE NUMBER(38,0),
	LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_CONDITION_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_TARGET_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_START VARCHAR,
	LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_END VARCHAR,
	LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	LISTING_LEVEL_METRICS_LISTING_POLICY_DEFECTS_COUNT NUMBER(38,0),
	LISTING_LEVEL_METRICS_LISTING_POLICY_DEFECTS_STATUS VARCHAR,
	LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_VALUE NUMBER(38,0),
	LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_CONDITION_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_TARGET_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_START VARCHAR,
	LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_END VARCHAR,
	LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_DEFECTS_COUNT NUMBER(38,0),
	LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_DEFECTS_STATUS VARCHAR,
	LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_VALUE NUMBER(38,0),
	LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_CONDITION_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_TARGET_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_START VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_END VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_SAFETY_DEFECTS_COUNT NUMBER(38,0),
	LISTING_LEVEL_METRICS_PRODUCT_SAFETY_DEFECTS_STATUS VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_VALUE NUMBER(38,0),
	LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_CONDITION_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_TARGET_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_START VARCHAR,
	LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_END VARCHAR,
	LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_DURATION VARCHAR,
	LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_OFFSET VARCHAR,
	LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_DEFECTS_COUNT NUMBER(38,0),
	LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_DEFECTS_STATUS VARCHAR,
	LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_VALUE NUMBER(38,0),
	LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_CONDITION_TYPE VARCHAR,
	LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_TARGET_TYPE VARCHAR,
	AHR_RESPONSE_ACCOUNT_HEALTH_RATING_STATUS VARCHAR,
	AHR_RESPONSE_SELLER_FACING_SCORE NUMBER(10,2),
	SELLER_ENROLLED_PROGRAMS VARCHAR,
	SELECTED_VENDOR_CODE VARCHAR,
    FILE_NAME VARCHAR,
	DATA_SOURCE VARCHAR,
	CREATED_BY VARCHAR DEFAULT 'DAG: fact_scah_performance_summary',
	UPDATED_BY VARCHAR DEFAULT 'DAG: fact_scah_performance_summary',
	RECORD_CREATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE(),
	RECORD_UPDATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE()
);

MERGE INTO $curated_db.fact_scah_performance_summary AS tgt
USING (
    SELECT 
        ps.PK,
        ps.REPORT_FETCHED_AND_LOADED_AT,
        ps.SCRAPER_ID,
        ps.SELLER_ID,
        si.SELLER_NAME,
        ps.COUNTRY AS COUNTRY_CODE,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_START,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_END,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_DATE_OFFSET_DURATION,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_DATE_OFFSET_OFFSET,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_COUNT,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_WITH_DEFECTS_COUNT,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_WITH_DEFECTS_STATUS,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_CLAIMS_COUNT,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_CLAIMS_STATUS,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_CHARGEBACKS_COUNT,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_CHARGEBACKS_STATUS,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_NEGATIVE_FEEDBACK_COUNT,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_NEGATIVE_FEEDBACK_STATUS,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_FULFILLMENT_TYPE,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_VALUE,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_CONDITION_TYPE,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_TARGET_TYPE,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_RATE,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_OVERALL_STATUS,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_VALUE_VALUE,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_VALUE_TYPE,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_VALUE,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_CONDITION_TYPE,
        ps.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_TARGET_TYPE,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_START,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_END,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_DATE_OFFSET_DURATION,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_DATE_OFFSET_OFFSET,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_COUNT,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_WITH_DEFECTS_COUNT,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_WITH_DEFECTS_STATUS,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_CLAIMS_COUNT,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_CLAIMS_STATUS,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_CHARGEBACKS_COUNT,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_CHARGEBACKS_STATUS,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_NEGATIVE_FEEDBACK_COUNT,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_NEGATIVE_FEEDBACK_STATUS,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_FULFILLMENT_TYPE,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_VALUE,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_CONDITION_TYPE,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_TARGET_TYPE,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_RATE,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_OVERALL_STATUS,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_VALUE_VALUE,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_VALUE_TYPE,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_VALUE,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_CONDITION_TYPE,
        ps.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_TARGET_TYPE,
        ps.INVOICE_DEFECT_METRIC,
        ps.LATE_SHIP_METRIC_TIME_FRAME_START,
        ps.LATE_SHIP_METRIC_TIME_FRAME_END,
        ps.LATE_SHIP_METRIC_TIME_FRAME_DATE_OFFSET_DURATION,
        ps.LATE_SHIP_METRIC_TIME_FRAME_DATE_OFFSET_OFFSET,
        ps.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_START,
        ps.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_END,
        ps.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_DATE_OFFSET_DURATION,
        ps.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_DATE_OFFSET_OFFSET,
        ps.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_DEFECTS_COUNT,
        ps.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_DEFECTS_STATUS,
        ps.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_VALUE,
        ps.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_CONDITION_TYPE,
        ps.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_TARGET_TYPE,
        ps.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_START,
        ps.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_END,
        ps.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_DATE_OFFSET_DURATION,
        ps.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_DATE_OFFSET_OFFSET,
        ps.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_DEFECTS_COUNT,
        ps.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_DEFECTS_STATUS,
        ps.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_VALUE,
        ps.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_CONDITION_TYPE,
        ps.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_TARGET_TYPE,
        ps.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_START,
        ps.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_END,
        ps.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_DATE_OFFSET_DURATION,
        ps.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_DATE_OFFSET_OFFSET,
        ps.LISTING_LEVEL_METRICS_LISTING_POLICY_DEFECTS_COUNT,
        ps.LISTING_LEVEL_METRICS_LISTING_POLICY_DEFECTS_STATUS,
        ps.LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_VALUE,
        ps.LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_CONDITION_TYPE,
        ps.LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_TARGET_TYPE,
        ps.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_START,
        ps.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_END,
        ps.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_DATE_OFFSET_DURATION,
        ps.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_DATE_OFFSET_OFFSET,
        ps.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_DEFECTS_COUNT,
        ps.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_DEFECTS_STATUS,
        ps.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_VALUE,
        ps.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_CONDITION_TYPE,
        ps.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_TARGET_TYPE,
        ps.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_START,
        ps.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_END,
        ps.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_DURATION,
        ps.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_OFFSET,
        ps.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_DEFECTS_COUNT,
        ps.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_DEFECTS_STATUS,
        ps.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_VALUE,
        ps.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_CONDITION_TYPE,
        ps.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_TARGET_TYPE,
        ps.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_START,
        ps.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_END,
        ps.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_DURATION,
        ps.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_OFFSET,
        ps.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_DEFECTS_COUNT,
        ps.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_DEFECTS_STATUS,
        ps.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_VALUE,
        ps.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_CONDITION_TYPE,
        ps.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_TARGET_TYPE,
        ps.AHR_RESPONSE_ACCOUNT_HEALTH_RATING_STATUS,
        ps.AHR_RESPONSE_SELLER_FACING_SCORE,
        ps.SELLER_ENROLLED_PROGRAMS,
        ps.SELECTED_VENDOR_CODE,
        ps.FILE_NAME,
        'dwh.staging.merge_scah_performance_summary' AS DATA_SOURCE,
        'DAG: fact_scah_performance_summary' AS CREATED_BY,
        'DAG: fact_scah_performance_summary' AS UPDATED_BY,
        ps.RECORD_CREATED_TIMESTAMP_UTC,
        ps.RECORD_UPDATED_TIMESTAMP_UTC
    FROM $stage_db.merge_scah_performance_summary ps
    LEFT JOIN $stage_db.stg_seller_info si ON ps.SELLER_ID = si.SELLER_ID
) AS src
ON tgt.pk = src.pk
WHEN MATCHED 
    AND src.RECORD_UPDATED_TIMESTAMP_UTC > tgt.RECORD_UPDATED_TIMESTAMP_UTC
THEN UPDATE SET 
    tgt.REPORT_FETCHED_AND_LOADED_AT = src.REPORT_FETCHED_AND_LOADED_AT,
    tgt.SCRAPER_ID = src.SCRAPER_ID,
    tgt.SELLER_ID = src.SELLER_ID,
    tgt.SELLER_NAME = src.SELLER_NAME,
    tgt.COUNTRY_CODE = src.COUNTRY_CODE,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_START = src.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_START,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_END = src.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_END,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_DATE_OFFSET_DURATION = src.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_DATE_OFFSET_DURATION,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_DATE_OFFSET_OFFSET = src.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_DATE_OFFSET_OFFSET,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_COUNT = src.ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_COUNT,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_WITH_DEFECTS_COUNT = src.ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_WITH_DEFECTS_COUNT,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_WITH_DEFECTS_STATUS = src.ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_WITH_DEFECTS_STATUS,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_CLAIMS_COUNT = src.ORDER_DEFECT_METRICS_METRICS_AFN_CLAIMS_COUNT,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_CLAIMS_STATUS = src.ORDER_DEFECT_METRICS_METRICS_AFN_CLAIMS_STATUS,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_CHARGEBACKS_COUNT = src.ORDER_DEFECT_METRICS_METRICS_AFN_CHARGEBACKS_COUNT,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_CHARGEBACKS_STATUS = src.ORDER_DEFECT_METRICS_METRICS_AFN_CHARGEBACKS_STATUS,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_NEGATIVE_FEEDBACK_COUNT = src.ORDER_DEFECT_METRICS_METRICS_AFN_NEGATIVE_FEEDBACK_COUNT,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_NEGATIVE_FEEDBACK_STATUS = src.ORDER_DEFECT_METRICS_METRICS_AFN_NEGATIVE_FEEDBACK_STATUS,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_FULFILLMENT_TYPE = src.ORDER_DEFECT_METRICS_METRICS_AFN_FULFILLMENT_TYPE,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_VALUE = src.ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_VALUE,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_CONDITION_TYPE = src.ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_CONDITION_TYPE,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_TARGET_TYPE = src.ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_TARGET_TYPE,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_RATE = src.ORDER_DEFECT_METRICS_METRICS_AFN_RATE,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_OVERALL_STATUS = src.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_OVERALL_STATUS,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_VALUE_VALUE = src.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_VALUE_VALUE,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_VALUE_TYPE = src.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_VALUE_TYPE,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_VALUE = src.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_VALUE,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_CONDITION_TYPE = src.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_CONDITION_TYPE,
    tgt.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_TARGET_TYPE = src.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_TARGET_TYPE,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_START = src.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_START,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_END = src.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_END,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_DATE_OFFSET_DURATION = src.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_DATE_OFFSET_DURATION,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_DATE_OFFSET_OFFSET = src.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_DATE_OFFSET_OFFSET,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_COUNT = src.ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_COUNT,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_WITH_DEFECTS_COUNT = src.ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_WITH_DEFECTS_COUNT,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_WITH_DEFECTS_STATUS = src.ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_WITH_DEFECTS_STATUS,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_CLAIMS_COUNT = src.ORDER_DEFECT_METRICS_METRICS_MFN_CLAIMS_COUNT,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_CLAIMS_STATUS = src.ORDER_DEFECT_METRICS_METRICS_MFN_CLAIMS_STATUS,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_CHARGEBACKS_COUNT = src.ORDER_DEFECT_METRICS_METRICS_MFN_CHARGEBACKS_COUNT,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_CHARGEBACKS_STATUS = src.ORDER_DEFECT_METRICS_METRICS_MFN_CHARGEBACKS_STATUS,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_NEGATIVE_FEEDBACK_COUNT = src.ORDER_DEFECT_METRICS_METRICS_MFN_NEGATIVE_FEEDBACK_COUNT,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_NEGATIVE_FEEDBACK_STATUS = src.ORDER_DEFECT_METRICS_METRICS_MFN_NEGATIVE_FEEDBACK_STATUS,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_FULFILLMENT_TYPE = src.ORDER_DEFECT_METRICS_METRICS_MFN_FULFILLMENT_TYPE,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_VALUE = src.ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_VALUE,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_CONDITION_TYPE = src.ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_CONDITION_TYPE,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_TARGET_TYPE = src.ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_TARGET_TYPE,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_RATE = src.ORDER_DEFECT_METRICS_METRICS_MFN_RATE,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_OVERALL_STATUS = src.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_OVERALL_STATUS,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_VALUE_VALUE = src.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_VALUE_VALUE,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_VALUE_TYPE = src.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_VALUE_TYPE,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_VALUE = src.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_VALUE,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_CONDITION_TYPE = src.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_CONDITION_TYPE,
    tgt.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_TARGET_TYPE = src.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_TARGET_TYPE,
    tgt.INVOICE_DEFECT_METRIC = src.INVOICE_DEFECT_METRIC,
    tgt.LATE_SHIP_METRIC_TIME_FRAME_START = src.LATE_SHIP_METRIC_TIME_FRAME_START,
    tgt.LATE_SHIP_METRIC_TIME_FRAME_END = src.LATE_SHIP_METRIC_TIME_FRAME_END,
    tgt.LATE_SHIP_METRIC_TIME_FRAME_DATE_OFFSET_DURATION = src.LATE_SHIP_METRIC_TIME_FRAME_DATE_OFFSET_DURATION,
    tgt.LATE_SHIP_METRIC_TIME_FRAME_DATE_OFFSET_OFFSET = src.LATE_SHIP_METRIC_TIME_FRAME_DATE_OFFSET_OFFSET,
    tgt.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_END = src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_END,
    tgt.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_DATE_OFFSET_DURATION = src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_DATE_OFFSET_DURATION,
    tgt.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_DATE_OFFSET_OFFSET = src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_DATE_OFFSET_OFFSET,
    tgt.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_DEFECTS_COUNT = src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_DEFECTS_COUNT,
    tgt.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_DEFECTS_STATUS = src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_DEFECTS_STATUS,
    tgt.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_VALUE = src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_VALUE,
    tgt.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_CONDITION_TYPE = src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_CONDITION_TYPE,
    tgt.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_TARGET_TYPE = src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_TARGET_TYPE,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_START = src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_START,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_END = src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_END,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_DATE_OFFSET_DURATION = src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_DATE_OFFSET_DURATION,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_DATE_OFFSET_OFFSET = src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_DATE_OFFSET_OFFSET,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_DEFECTS_COUNT = src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_DEFECTS_COUNT,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_DEFECTS_STATUS = src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_DEFECTS_STATUS,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_VALUE = src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_VALUE,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_CONDITION_TYPE = src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_CONDITION_TYPE,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_TARGET_TYPE = src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_TARGET_TYPE,
    tgt.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_START = src.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_START,
    tgt.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_END = src.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_END,
    tgt.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_DATE_OFFSET_DURATION = src.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_DATE_OFFSET_DURATION,
    tgt.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_DATE_OFFSET_OFFSET = src.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_DATE_OFFSET_OFFSET,
    tgt.LISTING_LEVEL_METRICS_LISTING_POLICY_DEFECTS_COUNT = src.LISTING_LEVEL_METRICS_LISTING_POLICY_DEFECTS_COUNT,
    tgt.LISTING_LEVEL_METRICS_LISTING_POLICY_DEFECTS_STATUS = src.LISTING_LEVEL_METRICS_LISTING_POLICY_DEFECTS_STATUS,
    tgt.LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_VALUE = src.LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_VALUE,
    tgt.LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_CONDITION_TYPE = src.LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_CONDITION_TYPE,
    tgt.LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_TARGET_TYPE = src.LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_TARGET_TYPE,
    tgt.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_START = src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_START,
    tgt.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_END = src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_END,
    tgt.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_DATE_OFFSET_DURATION = src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_DATE_OFFSET_DURATION,
    tgt.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_DATE_OFFSET_OFFSET = src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_DATE_OFFSET_OFFSET,
    tgt.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_DEFECTS_COUNT = src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_DEFECTS_COUNT,
    tgt.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_DEFECTS_STATUS = src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_DEFECTS_STATUS,
    tgt.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_VALUE = src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_VALUE,
    tgt.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_CONDITION_TYPE = src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_CONDITION_TYPE,
    tgt.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_TARGET_TYPE = src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_TARGET_TYPE,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_START = src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_START,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_END = src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_END,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_DURATION = src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_DURATION,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_OFFSET = src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_OFFSET,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_DEFECTS_COUNT = src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_DEFECTS_COUNT,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_DEFECTS_STATUS = src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_DEFECTS_STATUS,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_VALUE = src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_VALUE,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_CONDITION_TYPE = src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_CONDITION_TYPE,
    tgt.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_TARGET_TYPE = src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_TARGET_TYPE,
    tgt.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_START = src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_START,
    tgt.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_END = src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_END,
    tgt.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_DURATION = src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_DURATION,
    tgt.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_OFFSET = src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_OFFSET,
    tgt.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_DEFECTS_COUNT = src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_DEFECTS_COUNT,
    tgt.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_DEFECTS_STATUS = src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_DEFECTS_STATUS,
    tgt.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_VALUE = src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_VALUE,
    tgt.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_CONDITION_TYPE = src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_CONDITION_TYPE,
    tgt.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_TARGET_TYPE = src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_TARGET_TYPE,
    tgt.AHR_RESPONSE_ACCOUNT_HEALTH_RATING_STATUS = src.AHR_RESPONSE_ACCOUNT_HEALTH_RATING_STATUS,
    tgt.AHR_RESPONSE_SELLER_FACING_SCORE = src.AHR_RESPONSE_SELLER_FACING_SCORE,
    tgt.SELLER_ENROLLED_PROGRAMS = src.SELLER_ENROLLED_PROGRAMS,
    tgt.SELECTED_VENDOR_CODE = src.SELECTED_VENDOR_CODE,
    tgt.FILE_NAME = src.FILE_NAME,
    tgt.DATA_SOURCE = src.DATA_SOURCE,
    tgt.UPDATED_BY = src.UPDATED_BY,
    tgt.RECORD_UPDATED_TIMESTAMP_UTC = SYSDATE()
WHEN NOT MATCHED THEN INSERT (
    PK,
    REPORT_FETCHED_AND_LOADED_AT,
    SCRAPER_ID,
    SELLER_ID,
    SELLER_NAME,
    COUNTRY_CODE,
    ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_START,
    ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_END,
    ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_DATE_OFFSET_DURATION,
    ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_DATE_OFFSET_OFFSET,
    ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_COUNT,
    ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_WITH_DEFECTS_COUNT,
    ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_WITH_DEFECTS_STATUS,
    ORDER_DEFECT_METRICS_METRICS_AFN_CLAIMS_COUNT,
    ORDER_DEFECT_METRICS_METRICS_AFN_CLAIMS_STATUS,
    ORDER_DEFECT_METRICS_METRICS_AFN_CHARGEBACKS_COUNT,
    ORDER_DEFECT_METRICS_METRICS_AFN_CHARGEBACKS_STATUS,
    ORDER_DEFECT_METRICS_METRICS_AFN_NEGATIVE_FEEDBACK_COUNT,
    ORDER_DEFECT_METRICS_METRICS_AFN_NEGATIVE_FEEDBACK_STATUS,
    ORDER_DEFECT_METRICS_METRICS_AFN_FULFILLMENT_TYPE,
    ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_VALUE,
    ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_CONDITION_TYPE,
    ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_TARGET_TYPE,
    ORDER_DEFECT_METRICS_METRICS_AFN_RATE,
    ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_OVERALL_STATUS,
    ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_VALUE_VALUE,
    ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_VALUE_TYPE,
    ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_VALUE,
    ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_CONDITION_TYPE,
    ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_TARGET_TYPE,
    ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_START,
    ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_END,
    ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_DATE_OFFSET_DURATION,
    ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_DATE_OFFSET_OFFSET,
    ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_COUNT,
    ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_WITH_DEFECTS_COUNT,
    ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_WITH_DEFECTS_STATUS,
    ORDER_DEFECT_METRICS_METRICS_MFN_CLAIMS_COUNT,
    ORDER_DEFECT_METRICS_METRICS_MFN_CLAIMS_STATUS,
    ORDER_DEFECT_METRICS_METRICS_MFN_CHARGEBACKS_COUNT,
    ORDER_DEFECT_METRICS_METRICS_MFN_CHARGEBACKS_STATUS,
    ORDER_DEFECT_METRICS_METRICS_MFN_NEGATIVE_FEEDBACK_COUNT,
    ORDER_DEFECT_METRICS_METRICS_MFN_NEGATIVE_FEEDBACK_STATUS,
    ORDER_DEFECT_METRICS_METRICS_MFN_FULFILLMENT_TYPE,
    ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_VALUE,
    ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_CONDITION_TYPE,
    ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_TARGET_TYPE,
    ORDER_DEFECT_METRICS_METRICS_MFN_RATE,
    ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_OVERALL_STATUS,
    ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_VALUE_VALUE,
    ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_VALUE_TYPE,
    ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_VALUE,
    ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_CONDITION_TYPE,
    ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_TARGET_TYPE,
    INVOICE_DEFECT_METRIC,
    LATE_SHIP_METRIC_TIME_FRAME_START,
    LATE_SHIP_METRIC_TIME_FRAME_END,
    LATE_SHIP_METRIC_TIME_FRAME_DATE_OFFSET_DURATION,
    LATE_SHIP_METRIC_TIME_FRAME_DATE_OFFSET_OFFSET,
    LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_START,
    LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_END,
    LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_DATE_OFFSET_DURATION,
    LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_DATE_OFFSET_OFFSET,
    LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_DEFECTS_COUNT,
    LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_DEFECTS_STATUS,
    LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_VALUE,
    LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_CONDITION_TYPE,
    LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_TARGET_TYPE,
    LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_START,
    LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_END,
    LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_DATE_OFFSET_DURATION,
    LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_DATE_OFFSET_OFFSET,
    LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_DEFECTS_COUNT,
    LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_DEFECTS_STATUS,
    LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_VALUE,
    LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_CONDITION_TYPE,
    LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_TARGET_TYPE,
    LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_START,
    LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_END,
    LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_DATE_OFFSET_DURATION,
    LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_DATE_OFFSET_OFFSET,
    LISTING_LEVEL_METRICS_LISTING_POLICY_DEFECTS_COUNT,
    LISTING_LEVEL_METRICS_LISTING_POLICY_DEFECTS_STATUS,
    LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_VALUE,
    LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_CONDITION_TYPE,
    LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_TARGET_TYPE,
    LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_START,
    LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_END,
    LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_DATE_OFFSET_DURATION,
    LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_DATE_OFFSET_OFFSET,
    LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_DEFECTS_COUNT,
    LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_DEFECTS_STATUS,
    LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_VALUE,
    LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_CONDITION_TYPE,
    LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_TARGET_TYPE,
    LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_START,
    LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_END,
    LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_DURATION,
    LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_OFFSET,
    LISTING_LEVEL_METRICS_PRODUCT_SAFETY_DEFECTS_COUNT,
    LISTING_LEVEL_METRICS_PRODUCT_SAFETY_DEFECTS_STATUS,
    LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_VALUE,
    LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_CONDITION_TYPE,
    LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_TARGET_TYPE,
    LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_START,
    LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_END,
    LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_DURATION,
    LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_OFFSET,
    LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_DEFECTS_COUNT,
    LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_DEFECTS_STATUS,
    LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_VALUE,
    LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_CONDITION_TYPE,
    LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_TARGET_TYPE,
    AHR_RESPONSE_ACCOUNT_HEALTH_RATING_STATUS,
    AHR_RESPONSE_SELLER_FACING_SCORE,
    SELLER_ENROLLED_PROGRAMS,
    SELECTED_VENDOR_CODE,
    FILE_NAME,
    DATA_SOURCE,
    CREATED_BY,
    UPDATED_BY,
    RECORD_CREATED_TIMESTAMP_UTC,
    RECORD_UPDATED_TIMESTAMP_UTC
)
VALUES (
    src.PK,
    src.REPORT_FETCHED_AND_LOADED_AT,
    src.SCRAPER_ID,
    src.SELLER_ID,
    src.SELLER_NAME,
    src.COUNTRY_CODE,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_START,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_END,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_DATE_OFFSET_DURATION,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_TIME_FRAME_DATE_OFFSET_OFFSET,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_COUNT,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_WITH_DEFECTS_COUNT,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_ORDER_WITH_DEFECTS_STATUS,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_CLAIMS_COUNT,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_CLAIMS_STATUS,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_CHARGEBACKS_COUNT,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_CHARGEBACKS_STATUS,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_NEGATIVE_FEEDBACK_COUNT,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_NEGATIVE_FEEDBACK_STATUS,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_FULFILLMENT_TYPE,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_VALUE,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_CONDITION_TYPE,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_TARGET_TARGET_TYPE,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_RATE,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_OVERALL_STATUS,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_VALUE_VALUE,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_VALUE_TYPE,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_VALUE,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_CONDITION_TYPE,
    src.ORDER_DEFECT_METRICS_METRICS_AFN_METRIC_SUMMARY_TARGET_TARGET_TYPE,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_START,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_END,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_DATE_OFFSET_DURATION,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_TIME_FRAME_DATE_OFFSET_OFFSET,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_COUNT,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_WITH_DEFECTS_COUNT,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_ORDER_WITH_DEFECTS_STATUS,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_CLAIMS_COUNT,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_CLAIMS_STATUS,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_CHARGEBACKS_COUNT,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_CHARGEBACKS_STATUS,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_NEGATIVE_FEEDBACK_COUNT,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_NEGATIVE_FEEDBACK_STATUS,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_FULFILLMENT_TYPE,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_VALUE,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_CONDITION_TYPE,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_TARGET_TARGET_TYPE,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_RATE,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_OVERALL_STATUS,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_VALUE_VALUE,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_VALUE_TYPE,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_VALUE,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_CONDITION_TYPE,
    src.ORDER_DEFECT_METRICS_METRICS_MFN_METRIC_SUMMARY_TARGET_TARGET_TYPE,
    src.INVOICE_DEFECT_METRIC,
    src.LATE_SHIP_METRIC_TIME_FRAME_START,
    src.LATE_SHIP_METRIC_TIME_FRAME_END,
    src.LATE_SHIP_METRIC_TIME_FRAME_DATE_OFFSET_DURATION,
    src.LATE_SHIP_METRIC_TIME_FRAME_DATE_OFFSET_OFFSET,
    src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_START,
    src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_END,
    src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_DATE_OFFSET_DURATION,
    src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TIME_FRAME_DATE_OFFSET_OFFSET,
    src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_DEFECTS_COUNT,
    src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_DEFECTS_STATUS,
    src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_VALUE,
    src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_CONDITION_TYPE,
    src.LISTING_LEVEL_METRICS_RESTRICTED_PRODUCTS_TARGET_TARGET_TYPE,
    src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_START,
    src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_END,
    src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_DATE_OFFSET_DURATION,
    src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TIME_FRAME_DATE_OFFSET_OFFSET,
    src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_DEFECTS_COUNT,
    src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_DEFECTS_STATUS,
    src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_VALUE,
    src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_CONDITION_TYPE,
    src.LISTING_LEVEL_METRICS_PRODUCT_REVIEW_ABUSE_TARGET_TARGET_TYPE,
    src.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_START,
    src.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_END,
    src.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_DATE_OFFSET_DURATION,
    src.LISTING_LEVEL_METRICS_LISTING_POLICY_TIME_FRAME_DATE_OFFSET_OFFSET,
    src.LISTING_LEVEL_METRICS_LISTING_POLICY_DEFECTS_COUNT,
    src.LISTING_LEVEL_METRICS_LISTING_POLICY_DEFECTS_STATUS,
    src.LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_VALUE,
    src.LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_CONDITION_TYPE,
    src.LISTING_LEVEL_METRICS_LISTING_POLICY_TARGET_TARGET_TYPE,
    src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_START,
    src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_END,
    src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_DATE_OFFSET_DURATION,
    src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TIME_FRAME_DATE_OFFSET_OFFSET,
    src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_DEFECTS_COUNT,
    src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_DEFECTS_STATUS,
    src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_VALUE,
    src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_CONDITION_TYPE,
    src.LISTING_LEVEL_METRICS_POSITIVE_CUSTOMER_EXPERIENCE_TARGET_TARGET_TYPE,
    src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_START,
    src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_END,
    src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_DURATION,
    src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_OFFSET,
    src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_DEFECTS_COUNT,
    src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_DEFECTS_STATUS,
    src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_VALUE,
    src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_CONDITION_TYPE,
    src.LISTING_LEVEL_METRICS_PRODUCT_SAFETY_TARGET_TARGET_TYPE,
    src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_START,
    src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_END,
    src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_DURATION,
    src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TIME_FRAME_DATE_OFFSET_OFFSET,
    src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_DEFECTS_COUNT,
    src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_DEFECTS_STATUS,
    src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_VALUE,
    src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_CONDITION_TYPE,
    src.LISTING_LEVEL_METRICS_FOOD_AND_PRODUCT_SAFETY_TARGET_TARGET_TYPE,
    src.AHR_RESPONSE_ACCOUNT_HEALTH_RATING_STATUS,
    src.AHR_RESPONSE_SELLER_FACING_SCORE,
    src.SELLER_ENROLLED_PROGRAMS,
    src.SELECTED_VENDOR_CODE,
    src.FILE_NAME,
    src.DATA_SOURCE,
    src.CREATED_BY,
    src.UPDATED_BY,
    SYSDATE(),
    SYSDATE()
);
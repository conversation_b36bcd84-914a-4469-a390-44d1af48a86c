
CREATE TABLE IF NOT EXISTS $stage_db.merge_scah_performance_summary AS
    SELECT 
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_scah_performance_summary
    WHERE 1 = 0;


MERGE INTO
    $stage_db.merge_scah_performance_summary AS tgt
USING
    $stage_db.dedupe_scah_performance_summary AS src
    
        ON 1 = 1
       AND src.pk = tgt.pk
       
WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.scraper_id = src.scraper_id,
    tgt.seller_id = src.seller_id,
    tgt.country = src.country,
    tgt.order_defect_metrics_metrics_afn_time_frame_start = src.order_defect_metrics_metrics_afn_time_frame_start,
    tgt.order_defect_metrics_metrics_afn_time_frame_end = src.order_defect_metrics_metrics_afn_time_frame_end,
    tgt.order_defect_metrics_metrics_afn_time_frame_date_offset_duration = src.order_defect_metrics_metrics_afn_time_frame_date_offset_duration,
    tgt.order_defect_metrics_metrics_afn_time_frame_date_offset_offset = src.order_defect_metrics_metrics_afn_time_frame_date_offset_offset,
    tgt.order_defect_metrics_metrics_afn_order_count = src.order_defect_metrics_metrics_afn_order_count,
    tgt.order_defect_metrics_metrics_afn_order_with_defects_count = src.order_defect_metrics_metrics_afn_order_with_defects_count,
    tgt.order_defect_metrics_metrics_afn_order_with_defects_status = src.order_defect_metrics_metrics_afn_order_with_defects_status,
    tgt.order_defect_metrics_metrics_afn_claims_count = src.order_defect_metrics_metrics_afn_claims_count,
    tgt.order_defect_metrics_metrics_afn_claims_status = src.order_defect_metrics_metrics_afn_claims_status,
    tgt.order_defect_metrics_metrics_afn_chargebacks_count = src.order_defect_metrics_metrics_afn_chargebacks_count,
    tgt.order_defect_metrics_metrics_afn_chargebacks_status = src.order_defect_metrics_metrics_afn_chargebacks_status,
    tgt.order_defect_metrics_metrics_afn_negative_feedback_count = src.order_defect_metrics_metrics_afn_negative_feedback_count,
    tgt.order_defect_metrics_metrics_afn_negative_feedback_status = src.order_defect_metrics_metrics_afn_negative_feedback_status,
    tgt.order_defect_metrics_metrics_afn_fulfillment_type = src.order_defect_metrics_metrics_afn_fulfillment_type,
    tgt.order_defect_metrics_metrics_afn_target_value = src.order_defect_metrics_metrics_afn_target_value,
    tgt.order_defect_metrics_metrics_afn_target_condition_type = src.order_defect_metrics_metrics_afn_target_condition_type,
    tgt.order_defect_metrics_metrics_afn_target_target_type = src.order_defect_metrics_metrics_afn_target_target_type,
    tgt.order_defect_metrics_metrics_afn_rate = src.order_defect_metrics_metrics_afn_rate,
    tgt.order_defect_metrics_metrics_afn_metric_summary_overall_status = src.order_defect_metrics_metrics_afn_metric_summary_overall_status,
    tgt.order_defect_metrics_metrics_afn_metric_summary_value_value = src.order_defect_metrics_metrics_afn_metric_summary_value_value,
    tgt.order_defect_metrics_metrics_afn_metric_summary_value_type = src.order_defect_metrics_metrics_afn_metric_summary_value_type,
    tgt.order_defect_metrics_metrics_afn_metric_summary_target_value = src.order_defect_metrics_metrics_afn_metric_summary_target_value,
    tgt.order_defect_metrics_metrics_afn_metric_summary_target_condition_type = src.order_defect_metrics_metrics_afn_metric_summary_target_condition_type,
    tgt.order_defect_metrics_metrics_afn_metric_summary_target_target_type = src.order_defect_metrics_metrics_afn_metric_summary_target_target_type,
    tgt.order_defect_metrics_metrics_mfn_time_frame_start = src.order_defect_metrics_metrics_mfn_time_frame_start,
    tgt.order_defect_metrics_metrics_mfn_time_frame_end = src.order_defect_metrics_metrics_mfn_time_frame_end,
    tgt.order_defect_metrics_metrics_mfn_time_frame_date_offset_duration = src.order_defect_metrics_metrics_mfn_time_frame_date_offset_duration,
    tgt.order_defect_metrics_metrics_mfn_time_frame_date_offset_offset = src.order_defect_metrics_metrics_mfn_time_frame_date_offset_offset,
    tgt.order_defect_metrics_metrics_mfn_order_count = src.order_defect_metrics_metrics_mfn_order_count,
    tgt.order_defect_metrics_metrics_mfn_order_with_defects_count = src.order_defect_metrics_metrics_mfn_order_with_defects_count,
    tgt.order_defect_metrics_metrics_mfn_order_with_defects_status = src.order_defect_metrics_metrics_mfn_order_with_defects_status,
    tgt.order_defect_metrics_metrics_mfn_claims_count = src.order_defect_metrics_metrics_mfn_claims_count,
    tgt.order_defect_metrics_metrics_mfn_claims_status = src.order_defect_metrics_metrics_mfn_claims_status,
    tgt.order_defect_metrics_metrics_mfn_chargebacks_count = src.order_defect_metrics_metrics_mfn_chargebacks_count,
    tgt.order_defect_metrics_metrics_mfn_chargebacks_status = src.order_defect_metrics_metrics_mfn_chargebacks_status,
    tgt.order_defect_metrics_metrics_mfn_negative_feedback_count = src.order_defect_metrics_metrics_mfn_negative_feedback_count,
    tgt.order_defect_metrics_metrics_mfn_negative_feedback_status = src.order_defect_metrics_metrics_mfn_negative_feedback_status,
    tgt.order_defect_metrics_metrics_mfn_fulfillment_type = src.order_defect_metrics_metrics_mfn_fulfillment_type,
    tgt.order_defect_metrics_metrics_mfn_target_value = src.order_defect_metrics_metrics_mfn_target_value,
    tgt.order_defect_metrics_metrics_mfn_target_condition_type = src.order_defect_metrics_metrics_mfn_target_condition_type,
    tgt.order_defect_metrics_metrics_mfn_target_target_type = src.order_defect_metrics_metrics_mfn_target_target_type,
    tgt.order_defect_metrics_metrics_mfn_rate = src.order_defect_metrics_metrics_mfn_rate,
    tgt.order_defect_metrics_metrics_mfn_metric_summary_overall_status = src.order_defect_metrics_metrics_mfn_metric_summary_overall_status,
    tgt.order_defect_metrics_metrics_mfn_metric_summary_value_value = src.order_defect_metrics_metrics_mfn_metric_summary_value_value,
    tgt.order_defect_metrics_metrics_mfn_metric_summary_value_type = src.order_defect_metrics_metrics_mfn_metric_summary_value_type,
    tgt.order_defect_metrics_metrics_mfn_metric_summary_target_value = src.order_defect_metrics_metrics_mfn_metric_summary_target_value,
    tgt.order_defect_metrics_metrics_mfn_metric_summary_target_condition_type = src.order_defect_metrics_metrics_mfn_metric_summary_target_condition_type,
    tgt.order_defect_metrics_metrics_mfn_metric_summary_target_target_type = src.order_defect_metrics_metrics_mfn_metric_summary_target_target_type,
    tgt.invoice_defect_metric = src.invoice_defect_metric,
    tgt.late_ship_metric_time_frame_start = src.late_ship_metric_time_frame_start,
    tgt.late_ship_metric_time_frame_end = src.late_ship_metric_time_frame_end,
    tgt.late_ship_metric_time_frame_date_offset_duration = src.late_ship_metric_time_frame_date_offset_duration,
    tgt.late_ship_metric_time_frame_date_offset_offset = src.late_ship_metric_time_frame_date_offset_offset,
    tgt.late_ship_metric_order_count = src.late_ship_metric_order_count,
    tgt.late_ship_metric_late_shipment_count = src.late_ship_metric_late_shipment_count,
    tgt.late_ship_metric_late_shipment_status = src.late_ship_metric_late_shipment_status,
    tgt.late_ship_metric_marketplace_id = src.late_ship_metric_marketplace_id,
    tgt.late_ship_metric_target_value = src.late_ship_metric_target_value,
    tgt.late_ship_metric_target_condition_type = src.late_ship_metric_target_condition_type,
    tgt.late_ship_metric_target_target_type = src.late_ship_metric_target_target_type,
    tgt.late_ship_metric_rate = src.late_ship_metric_rate,
    tgt.late_ship_metric_metric_summary_overall_status = src.late_ship_metric_metric_summary_overall_status,
    tgt.late_ship_metric_metric_summary_value_value = src.late_ship_metric_metric_summary_value_value,
    tgt.late_ship_metric_metric_summary_value_type = src.late_ship_metric_metric_summary_value_type,
    tgt.late_ship_metric_metric_summary_target_value = src.late_ship_metric_metric_summary_target_value,
    tgt.late_ship_metric_metric_summary_target_condition_type = src.late_ship_metric_metric_summary_target_condition_type,
    tgt.late_ship_metric_metric_summary_target_target_type = src.late_ship_metric_metric_summary_target_target_type,
    tgt.prefulfillment_cancellation_metric_time_frame_start = src.prefulfillment_cancellation_metric_time_frame_start,
    tgt.prefulfillment_cancellation_metric_time_frame_end = src.prefulfillment_cancellation_metric_time_frame_end,
    tgt.prefulfillment_cancellation_metric_time_frame_date_offset_duration = src.prefulfillment_cancellation_metric_time_frame_date_offset_duration,
    tgt.prefulfillment_cancellation_metric_time_frame_date_offset_offset = src.prefulfillment_cancellation_metric_time_frame_date_offset_offset,
    tgt.prefulfillment_cancellation_metric_order_count = src.prefulfillment_cancellation_metric_order_count,
    tgt.prefulfillment_cancellation_metric_cancellation_count = src.prefulfillment_cancellation_metric_cancellation_count,
    tgt.prefulfillment_cancellation_metric_cancellation_status = src.prefulfillment_cancellation_metric_cancellation_status,
    tgt.prefulfillment_cancellation_metric_marketplace_id = src.prefulfillment_cancellation_metric_marketplace_id,
    tgt.prefulfillment_cancellation_metric_target_value = src.prefulfillment_cancellation_metric_target_value,
    tgt.prefulfillment_cancellation_metric_target_condition_type = src.prefulfillment_cancellation_metric_target_condition_type,
    tgt.prefulfillment_cancellation_metric_target_target_type = src.prefulfillment_cancellation_metric_target_target_type,
    tgt.prefulfillment_cancellation_metric_rate = src.prefulfillment_cancellation_metric_rate,
    tgt.prefulfillment_cancellation_metric_metric_summary_overall_status = src.prefulfillment_cancellation_metric_metric_summary_overall_status,
    tgt.prefulfillment_cancellation_metric_metric_summary_value_value = src.prefulfillment_cancellation_metric_metric_summary_value_value,
    tgt.prefulfillment_cancellation_metric_metric_summary_value_type = src.prefulfillment_cancellation_metric_metric_summary_value_type,
    tgt.prefulfillment_cancellation_metric_metric_summary_target_value = src.prefulfillment_cancellation_metric_metric_summary_target_value,
    tgt.prefulfillment_cancellation_metric_metric_summary_target_condition_type = src.prefulfillment_cancellation_metric_metric_summary_target_condition_type,
    tgt.prefulfillment_cancellation_metric_metric_summary_target_target_type = src.prefulfillment_cancellation_metric_metric_summary_target_target_type,
    tgt.valid_tracking_metrics_time_frame_start = src.valid_tracking_metrics_time_frame_start,
    tgt.valid_tracking_metrics_time_frame_end = src.valid_tracking_metrics_time_frame_end,
    tgt.valid_tracking_metrics_time_frame_date_offset_duration = src.valid_tracking_metrics_time_frame_date_offset_duration,
    tgt.valid_tracking_metrics_time_frame_date_offset_offset = src.valid_tracking_metrics_time_frame_date_offset_offset,
    tgt.valid_tracking_metrics_shipment_count = src.valid_tracking_metrics_shipment_count,
    tgt.valid_tracking_metrics_valid_tracking_count = src.valid_tracking_metrics_valid_tracking_count,
    tgt.valid_tracking_metrics_valid_tracking_status = src.valid_tracking_metrics_valid_tracking_status,
    tgt.valid_tracking_metrics_shipment_count_with_exempt_packages = src.valid_tracking_metrics_shipment_count_with_exempt_packages,
    tgt.valid_tracking_metrics_exempted_shipment_count = src.valid_tracking_metrics_exempted_shipment_count,
    tgt.valid_tracking_metrics_exempted_defect_count = src.valid_tracking_metrics_exempted_defect_count,
    tgt.valid_tracking_metrics_defect_count = src.valid_tracking_metrics_defect_count,
    tgt.valid_tracking_metrics_target_value = src.valid_tracking_metrics_target_value,
    tgt.valid_tracking_metrics_target_condition_type = src.valid_tracking_metrics_target_condition_type,
    tgt.valid_tracking_metrics_target_target_type = src.valid_tracking_metrics_target_target_type,
    tgt.valid_tracking_metrics_rate = src.valid_tracking_metrics_rate,
    tgt.valid_tracking_metrics_metric_summary_overall_status = src.valid_tracking_metrics_metric_summary_overall_status,
    tgt.valid_tracking_metrics_metric_summary_value_value = src.valid_tracking_metrics_metric_summary_value_value,
    tgt.valid_tracking_metrics_metric_summary_value_type = src.valid_tracking_metrics_metric_summary_value_type,
    tgt.valid_tracking_metrics_metric_summary_target_value = src.valid_tracking_metrics_metric_summary_target_value,
    tgt.valid_tracking_metrics_metric_summary_target_condition_type = src.valid_tracking_metrics_metric_summary_target_condition_type,
    tgt.valid_tracking_metrics_metric_summary_target_target_type = src.valid_tracking_metrics_metric_summary_target_target_type,
    tgt.on_time_delivery_metrics_time_frame_start = src.on_time_delivery_metrics_time_frame_start,
    tgt.on_time_delivery_metrics_time_frame_end = src.on_time_delivery_metrics_time_frame_end,
    tgt.on_time_delivery_metrics_time_frame_date_offset_duration = src.on_time_delivery_metrics_time_frame_date_offset_duration,
    tgt.on_time_delivery_metrics_time_frame_date_offset_offset = src.on_time_delivery_metrics_time_frame_date_offset_offset,
    tgt.on_time_delivery_metrics_shipment_count_with_valid_tracking = src.on_time_delivery_metrics_shipment_count_with_valid_tracking,
    tgt.on_time_delivery_metrics_on_time_delivery_count = src.on_time_delivery_metrics_on_time_delivery_count,
    tgt.on_time_delivery_metrics_on_time_delivery_status = src.on_time_delivery_metrics_on_time_delivery_status,
    tgt.on_time_delivery_metrics_target_value = src.on_time_delivery_metrics_target_value,
    tgt.on_time_delivery_metrics_target_condition_type = src.on_time_delivery_metrics_target_condition_type,
    tgt.on_time_delivery_metrics_target_target_type = src.on_time_delivery_metrics_target_target_type,
    tgt.on_time_delivery_metrics_rate = src.on_time_delivery_metrics_rate,
    tgt.on_time_delivery_metrics_metric_summary_overall_status = src.on_time_delivery_metrics_metric_summary_overall_status,
    tgt.on_time_delivery_metrics_metric_summary_value_value = src.on_time_delivery_metrics_metric_summary_value_value,
    tgt.on_time_delivery_metrics_metric_summary_value_type = src.on_time_delivery_metrics_metric_summary_value_type,
    tgt.on_time_delivery_metrics_metric_summary_target_value = src.on_time_delivery_metrics_metric_summary_target_value,
    tgt.on_time_delivery_metrics_metric_summary_target_condition_type = src.on_time_delivery_metrics_metric_summary_target_condition_type,
    tgt.on_time_delivery_metrics_metric_summary_target_target_type = src.on_time_delivery_metrics_metric_summary_target_target_type,
    tgt.listing_level_metrics_automated_brand_protection_time_frame_start = src.listing_level_metrics_automated_brand_protection_time_frame_start,
    tgt.listing_level_metrics_automated_brand_protection_time_frame_end = src.listing_level_metrics_automated_brand_protection_time_frame_end,
    tgt.listing_level_metrics_automated_brand_protection_time_frame_date_offset_duration = src.listing_level_metrics_automated_brand_protection_time_frame_date_offset_duration,
    tgt.listing_level_metrics_automated_brand_protection_time_frame_date_offset_offset = src.listing_level_metrics_automated_brand_protection_time_frame_date_offset_offset,
    tgt.listing_level_metrics_automated_brand_protection_defects_count = src.listing_level_metrics_automated_brand_protection_defects_count,
    tgt.listing_level_metrics_automated_brand_protection_defects_status = src.listing_level_metrics_automated_brand_protection_defects_status,
    tgt.listing_level_metrics_automated_brand_protection_target_value = src.listing_level_metrics_automated_brand_protection_target_value,
    tgt.listing_level_metrics_automated_brand_protection_target_condition_type = src.listing_level_metrics_automated_brand_protection_target_condition_type,
    tgt.listing_level_metrics_automated_brand_protection_target_target_type = src.listing_level_metrics_automated_brand_protection_target_target_type,
    tgt.listing_level_metrics_product_condition_time_frame_start = src.listing_level_metrics_product_condition_time_frame_start,
    tgt.listing_level_metrics_product_condition_time_frame_end = src.listing_level_metrics_product_condition_time_frame_end,
    tgt.listing_level_metrics_product_condition_time_frame_date_offset_duration = src.listing_level_metrics_product_condition_time_frame_date_offset_duration,
    tgt.listing_level_metrics_product_condition_time_frame_date_offset_offset = src.listing_level_metrics_product_condition_time_frame_date_offset_offset,
    tgt.listing_level_metrics_product_condition_defects_count = src.listing_level_metrics_product_condition_defects_count,
    tgt.listing_level_metrics_product_condition_defects_status = src.listing_level_metrics_product_condition_defects_status,
    tgt.listing_level_metrics_product_condition_target_value = src.listing_level_metrics_product_condition_target_value,
    tgt.listing_level_metrics_product_condition_target_condition_type = src.listing_level_metrics_product_condition_target_condition_type,
    tgt.listing_level_metrics_product_condition_target_target_type = src.listing_level_metrics_product_condition_target_target_type,
    tgt.listing_level_metrics_intellectual_property_time_frame_start = src.listing_level_metrics_intellectual_property_time_frame_start,
    tgt.listing_level_metrics_intellectual_property_time_frame_end = src.listing_level_metrics_intellectual_property_time_frame_end,
    tgt.listing_level_metrics_intellectual_property_time_frame_date_offset_duration = src.listing_level_metrics_intellectual_property_time_frame_date_offset_duration,
    tgt.listing_level_metrics_intellectual_property_time_frame_date_offset_offset = src.listing_level_metrics_intellectual_property_time_frame_date_offset_offset,
    tgt.listing_level_metrics_intellectual_property_defects_count = src.listing_level_metrics_intellectual_property_defects_count,
    tgt.listing_level_metrics_intellectual_property_defects_status = src.listing_level_metrics_intellectual_property_defects_status,
    tgt.listing_level_metrics_intellectual_property_target_value = src.listing_level_metrics_intellectual_property_target_value,
    tgt.listing_level_metrics_intellectual_property_target_condition_type = src.listing_level_metrics_intellectual_property_target_condition_type,
    tgt.listing_level_metrics_intellectual_property_target_target_type = src.listing_level_metrics_intellectual_property_target_target_type,
    tgt.listing_level_metrics_regulatory_compliance_time_frame_start = src.listing_level_metrics_regulatory_compliance_time_frame_start,
    tgt.listing_level_metrics_regulatory_compliance_time_frame_end = src.listing_level_metrics_regulatory_compliance_time_frame_end,
    tgt.listing_level_metrics_regulatory_compliance_time_frame_date_offset_duration = src.listing_level_metrics_regulatory_compliance_time_frame_date_offset_duration,
    tgt.listing_level_metrics_regulatory_compliance_time_frame_date_offset_offset = src.listing_level_metrics_regulatory_compliance_time_frame_date_offset_offset,
    tgt.listing_level_metrics_regulatory_compliance_defects_count = src.listing_level_metrics_regulatory_compliance_defects_count,
    tgt.listing_level_metrics_regulatory_compliance_defects_status = src.listing_level_metrics_regulatory_compliance_defects_status,
    tgt.listing_level_metrics_regulatory_compliance_target_value = src.listing_level_metrics_regulatory_compliance_target_value,
    tgt.listing_level_metrics_regulatory_compliance_target_condition_type = src.listing_level_metrics_regulatory_compliance_target_condition_type,
    tgt.listing_level_metrics_regulatory_compliance_target_target_type = src.listing_level_metrics_regulatory_compliance_target_target_type,
    tgt.listing_level_metrics_product_authenticity_time_frame_start = src.listing_level_metrics_product_authenticity_time_frame_start,
    tgt.listing_level_metrics_product_authenticity_time_frame_end = src.listing_level_metrics_product_authenticity_time_frame_end,
    tgt.listing_level_metrics_product_authenticity_time_frame_date_offset_duration = src.listing_level_metrics_product_authenticity_time_frame_date_offset_duration,
    tgt.listing_level_metrics_product_authenticity_time_frame_date_offset_offset = src.listing_level_metrics_product_authenticity_time_frame_date_offset_offset,
    tgt.listing_level_metrics_product_authenticity_defects_count = src.listing_level_metrics_product_authenticity_defects_count,
    tgt.listing_level_metrics_product_authenticity_defects_status = src.listing_level_metrics_product_authenticity_defects_status,
    tgt.listing_level_metrics_product_authenticity_target_value = src.listing_level_metrics_product_authenticity_target_value,
    tgt.listing_level_metrics_product_authenticity_target_condition_type = src.listing_level_metrics_product_authenticity_target_condition_type,
    tgt.listing_level_metrics_product_authenticity_target_target_type = src.listing_level_metrics_product_authenticity_target_target_type,
    tgt.listing_level_metrics_restricted_products_time_frame_start = src.listing_level_metrics_restricted_products_time_frame_start,
    tgt.listing_level_metrics_restricted_products_time_frame_end = src.listing_level_metrics_restricted_products_time_frame_end,
    tgt.listing_level_metrics_restricted_products_time_frame_date_offset_duration = src.listing_level_metrics_restricted_products_time_frame_date_offset_duration,
    tgt.listing_level_metrics_restricted_products_time_frame_date_offset_offset = src.listing_level_metrics_restricted_products_time_frame_date_offset_offset,
    tgt.listing_level_metrics_restricted_products_defects_count = src.listing_level_metrics_restricted_products_defects_count,
    tgt.listing_level_metrics_restricted_products_defects_status = src.listing_level_metrics_restricted_products_defects_status,
    tgt.listing_level_metrics_restricted_products_target_value = src.listing_level_metrics_restricted_products_target_value,
    tgt.listing_level_metrics_restricted_products_target_condition_type = src.listing_level_metrics_restricted_products_target_condition_type,
    tgt.listing_level_metrics_restricted_products_target_target_type = src.listing_level_metrics_restricted_products_target_target_type,
    tgt.listing_level_metrics_product_review_abuse_time_frame_start = src.listing_level_metrics_product_review_abuse_time_frame_start,
    tgt.listing_level_metrics_product_review_abuse_time_frame_end = src.listing_level_metrics_product_review_abuse_time_frame_end,
    tgt.listing_level_metrics_product_review_abuse_time_frame_date_offset_duration = src.listing_level_metrics_product_review_abuse_time_frame_date_offset_duration,
    tgt.listing_level_metrics_product_review_abuse_time_frame_date_offset_offset = src.listing_level_metrics_product_review_abuse_time_frame_date_offset_offset,
    tgt.listing_level_metrics_product_review_abuse_defects_count = src.listing_level_metrics_product_review_abuse_defects_count,
    tgt.listing_level_metrics_product_review_abuse_defects_status = src.listing_level_metrics_product_review_abuse_defects_status,
    tgt.listing_level_metrics_product_review_abuse_target_value = src.listing_level_metrics_product_review_abuse_target_value,
    tgt.listing_level_metrics_product_review_abuse_target_condition_type = src.listing_level_metrics_product_review_abuse_target_condition_type,
    tgt.listing_level_metrics_product_review_abuse_target_target_type = src.listing_level_metrics_product_review_abuse_target_target_type,
    tgt.listing_level_metrics_listing_policy_time_frame_start = src.listing_level_metrics_listing_policy_time_frame_start,
    tgt.listing_level_metrics_listing_policy_time_frame_end = src.listing_level_metrics_listing_policy_time_frame_end,
    tgt.listing_level_metrics_listing_policy_time_frame_date_offset_duration = src.listing_level_metrics_listing_policy_time_frame_date_offset_duration,
    tgt.listing_level_metrics_listing_policy_time_frame_date_offset_offset = src.listing_level_metrics_listing_policy_time_frame_date_offset_offset,
    tgt.listing_level_metrics_listing_policy_defects_count = src.listing_level_metrics_listing_policy_defects_count,
    tgt.listing_level_metrics_listing_policy_defects_status = src.listing_level_metrics_listing_policy_defects_status,
    tgt.listing_level_metrics_listing_policy_target_value = src.listing_level_metrics_listing_policy_target_value,
    tgt.listing_level_metrics_listing_policy_target_condition_type = src.listing_level_metrics_listing_policy_target_condition_type,
    tgt.listing_level_metrics_listing_policy_target_target_type = src.listing_level_metrics_listing_policy_target_target_type,
    tgt.listing_level_metrics_positive_customer_experience_time_frame_start = src.listing_level_metrics_positive_customer_experience_time_frame_start,
    tgt.listing_level_metrics_positive_customer_experience_time_frame_end = src.listing_level_metrics_positive_customer_experience_time_frame_end,
    tgt.listing_level_metrics_positive_customer_experience_time_frame_date_offset_duration = src.listing_level_metrics_positive_customer_experience_time_frame_date_offset_duration,
    tgt.listing_level_metrics_positive_customer_experience_time_frame_date_offset_offset = src.listing_level_metrics_positive_customer_experience_time_frame_date_offset_offset,
    tgt.listing_level_metrics_positive_customer_experience_defects_count = src.listing_level_metrics_positive_customer_experience_defects_count,
    tgt.listing_level_metrics_positive_customer_experience_defects_status = src.listing_level_metrics_positive_customer_experience_defects_status,
    tgt.listing_level_metrics_positive_customer_experience_target_value = src.listing_level_metrics_positive_customer_experience_target_value,
    tgt.listing_level_metrics_positive_customer_experience_target_condition_type = src.listing_level_metrics_positive_customer_experience_target_condition_type,
    tgt.listing_level_metrics_positive_customer_experience_target_target_type = src.listing_level_metrics_positive_customer_experience_target_target_type,
    tgt.listing_level_metrics_product_safety_time_frame_start = src.listing_level_metrics_product_safety_time_frame_start,
    tgt.listing_level_metrics_product_safety_time_frame_end = src.listing_level_metrics_product_safety_time_frame_end,
    tgt.listing_level_metrics_product_safety_time_frame_date_offset_duration = src.listing_level_metrics_product_safety_time_frame_date_offset_duration,
    tgt.listing_level_metrics_product_safety_time_frame_date_offset_offset = src.listing_level_metrics_product_safety_time_frame_date_offset_offset,
    tgt.listing_level_metrics_product_safety_defects_count = src.listing_level_metrics_product_safety_defects_count,
    tgt.listing_level_metrics_product_safety_defects_status = src.listing_level_metrics_product_safety_defects_status,
    tgt.listing_level_metrics_product_safety_target_value = src.listing_level_metrics_product_safety_target_value,
    tgt.listing_level_metrics_product_safety_target_condition_type = src.listing_level_metrics_product_safety_target_condition_type,
    tgt.listing_level_metrics_product_safety_target_target_type = src.listing_level_metrics_product_safety_target_target_type,
    tgt.listing_level_metrics_food_and_product_safety_time_frame_start = src.listing_level_metrics_food_and_product_safety_time_frame_start,
    tgt.listing_level_metrics_food_and_product_safety_time_frame_end = src.listing_level_metrics_food_and_product_safety_time_frame_end,
    tgt.listing_level_metrics_food_and_product_safety_time_frame_date_offset_duration = src.listing_level_metrics_food_and_product_safety_time_frame_date_offset_duration,
    tgt.listing_level_metrics_food_and_product_safety_time_frame_date_offset_offset = src.listing_level_metrics_food_and_product_safety_time_frame_date_offset_offset,
    tgt.listing_level_metrics_food_and_product_safety_defects_count = src.listing_level_metrics_food_and_product_safety_defects_count,
    tgt.listing_level_metrics_food_and_product_safety_defects_status = src.listing_level_metrics_food_and_product_safety_defects_status,
    tgt.listing_level_metrics_food_and_product_safety_target_value = src.listing_level_metrics_food_and_product_safety_target_value,
    tgt.listing_level_metrics_food_and_product_safety_target_condition_type = src.listing_level_metrics_food_and_product_safety_target_condition_type,
    tgt.listing_level_metrics_food_and_product_safety_target_target_type = src.listing_level_metrics_food_and_product_safety_target_target_type,
    tgt.ahr_response_account_health_rating_status = src.ahr_response_account_health_rating_status,
    tgt.ahr_response_seller_facing_score = src.ahr_response_seller_facing_score,
    tgt.seller_enrolled_programs = src.seller_enrolled_programs,
    tgt.selected_vendor_code = src.selected_vendor_code,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    order_defect_metrics_metrics_afn_time_frame_start,
    order_defect_metrics_metrics_afn_time_frame_end,
    order_defect_metrics_metrics_afn_time_frame_date_offset_duration,
    order_defect_metrics_metrics_afn_time_frame_date_offset_offset,
    order_defect_metrics_metrics_afn_order_count,
    order_defect_metrics_metrics_afn_order_with_defects_count,
    order_defect_metrics_metrics_afn_order_with_defects_status,
    order_defect_metrics_metrics_afn_claims_count,
    order_defect_metrics_metrics_afn_claims_status,
    order_defect_metrics_metrics_afn_chargebacks_count,
    order_defect_metrics_metrics_afn_chargebacks_status,
    order_defect_metrics_metrics_afn_negative_feedback_count,
    order_defect_metrics_metrics_afn_negative_feedback_status,
    order_defect_metrics_metrics_afn_fulfillment_type,
    order_defect_metrics_metrics_afn_target_value,
    order_defect_metrics_metrics_afn_target_condition_type,
    order_defect_metrics_metrics_afn_target_target_type,
    order_defect_metrics_metrics_afn_rate,
    order_defect_metrics_metrics_afn_metric_summary_overall_status,
    order_defect_metrics_metrics_afn_metric_summary_value_value,
    order_defect_metrics_metrics_afn_metric_summary_value_type,
    order_defect_metrics_metrics_afn_metric_summary_target_value,
    order_defect_metrics_metrics_afn_metric_summary_target_condition_type,
    order_defect_metrics_metrics_afn_metric_summary_target_target_type,
    order_defect_metrics_metrics_mfn_time_frame_start,
    order_defect_metrics_metrics_mfn_time_frame_end,
    order_defect_metrics_metrics_mfn_time_frame_date_offset_duration,
    order_defect_metrics_metrics_mfn_time_frame_date_offset_offset,
    order_defect_metrics_metrics_mfn_order_count,
    order_defect_metrics_metrics_mfn_order_with_defects_count,
    order_defect_metrics_metrics_mfn_order_with_defects_status,
    order_defect_metrics_metrics_mfn_claims_count,
    order_defect_metrics_metrics_mfn_claims_status,
    order_defect_metrics_metrics_mfn_chargebacks_count,
    order_defect_metrics_metrics_mfn_chargebacks_status,
    order_defect_metrics_metrics_mfn_negative_feedback_count,
    order_defect_metrics_metrics_mfn_negative_feedback_status,
    order_defect_metrics_metrics_mfn_fulfillment_type,
    order_defect_metrics_metrics_mfn_target_value,
    order_defect_metrics_metrics_mfn_target_condition_type,
    order_defect_metrics_metrics_mfn_target_target_type,
    order_defect_metrics_metrics_mfn_rate,
    order_defect_metrics_metrics_mfn_metric_summary_overall_status,
    order_defect_metrics_metrics_mfn_metric_summary_value_value,
    order_defect_metrics_metrics_mfn_metric_summary_value_type,
    order_defect_metrics_metrics_mfn_metric_summary_target_value,
    order_defect_metrics_metrics_mfn_metric_summary_target_condition_type,
    order_defect_metrics_metrics_mfn_metric_summary_target_target_type,
    invoice_defect_metric,
    late_ship_metric_time_frame_start,
    late_ship_metric_time_frame_end,
    late_ship_metric_time_frame_date_offset_duration,
    late_ship_metric_time_frame_date_offset_offset,
    late_ship_metric_order_count,
    late_ship_metric_late_shipment_count,
    late_ship_metric_late_shipment_status,
    late_ship_metric_marketplace_id,
    late_ship_metric_target_value,
    late_ship_metric_target_condition_type,
    late_ship_metric_target_target_type,
    late_ship_metric_rate,
    late_ship_metric_metric_summary_overall_status,
    late_ship_metric_metric_summary_value_value,
    late_ship_metric_metric_summary_value_type,
    late_ship_metric_metric_summary_target_value,
    late_ship_metric_metric_summary_target_condition_type,
    late_ship_metric_metric_summary_target_target_type,
    prefulfillment_cancellation_metric_time_frame_start,
    prefulfillment_cancellation_metric_time_frame_end,
    prefulfillment_cancellation_metric_time_frame_date_offset_duration,
    prefulfillment_cancellation_metric_time_frame_date_offset_offset,
    prefulfillment_cancellation_metric_order_count,
    prefulfillment_cancellation_metric_cancellation_count,
    prefulfillment_cancellation_metric_cancellation_status,
    prefulfillment_cancellation_metric_marketplace_id,
    prefulfillment_cancellation_metric_target_value,
    prefulfillment_cancellation_metric_target_condition_type,
    prefulfillment_cancellation_metric_target_target_type,
    prefulfillment_cancellation_metric_rate,
    prefulfillment_cancellation_metric_metric_summary_overall_status,
    prefulfillment_cancellation_metric_metric_summary_value_value,
    prefulfillment_cancellation_metric_metric_summary_value_type,
    prefulfillment_cancellation_metric_metric_summary_target_value,
    prefulfillment_cancellation_metric_metric_summary_target_condition_type,
    prefulfillment_cancellation_metric_metric_summary_target_target_type,
    valid_tracking_metrics_time_frame_start,
    valid_tracking_metrics_time_frame_end,
    valid_tracking_metrics_time_frame_date_offset_duration,
    valid_tracking_metrics_time_frame_date_offset_offset,
    valid_tracking_metrics_shipment_count,
    valid_tracking_metrics_valid_tracking_count,
    valid_tracking_metrics_valid_tracking_status,
    valid_tracking_metrics_shipment_count_with_exempt_packages,
    valid_tracking_metrics_exempted_shipment_count,
    valid_tracking_metrics_exempted_defect_count,
    valid_tracking_metrics_defect_count,
    valid_tracking_metrics_target_value,
    valid_tracking_metrics_target_condition_type,
    valid_tracking_metrics_target_target_type,
    valid_tracking_metrics_rate,
    valid_tracking_metrics_metric_summary_overall_status,
    valid_tracking_metrics_metric_summary_value_value,
    valid_tracking_metrics_metric_summary_value_type,
    valid_tracking_metrics_metric_summary_target_value,
    valid_tracking_metrics_metric_summary_target_condition_type,
    valid_tracking_metrics_metric_summary_target_target_type,
    on_time_delivery_metrics_time_frame_start,
    on_time_delivery_metrics_time_frame_end,
    on_time_delivery_metrics_time_frame_date_offset_duration,
    on_time_delivery_metrics_time_frame_date_offset_offset,
    on_time_delivery_metrics_shipment_count_with_valid_tracking,
    on_time_delivery_metrics_on_time_delivery_count,
    on_time_delivery_metrics_on_time_delivery_status,
    on_time_delivery_metrics_target_value,
    on_time_delivery_metrics_target_condition_type,
    on_time_delivery_metrics_target_target_type,
    on_time_delivery_metrics_rate,
    on_time_delivery_metrics_metric_summary_overall_status,
    on_time_delivery_metrics_metric_summary_value_value,
    on_time_delivery_metrics_metric_summary_value_type,
    on_time_delivery_metrics_metric_summary_target_value,
    on_time_delivery_metrics_metric_summary_target_condition_type,
    on_time_delivery_metrics_metric_summary_target_target_type,
    listing_level_metrics_automated_brand_protection_time_frame_start,
    listing_level_metrics_automated_brand_protection_time_frame_end,
    listing_level_metrics_automated_brand_protection_time_frame_date_offset_duration,
    listing_level_metrics_automated_brand_protection_time_frame_date_offset_offset,
    listing_level_metrics_automated_brand_protection_defects_count,
    listing_level_metrics_automated_brand_protection_defects_status,
    listing_level_metrics_automated_brand_protection_target_value,
    listing_level_metrics_automated_brand_protection_target_condition_type,
    listing_level_metrics_automated_brand_protection_target_target_type,
    listing_level_metrics_product_condition_time_frame_start,
    listing_level_metrics_product_condition_time_frame_end,
    listing_level_metrics_product_condition_time_frame_date_offset_duration,
    listing_level_metrics_product_condition_time_frame_date_offset_offset,
    listing_level_metrics_product_condition_defects_count,
    listing_level_metrics_product_condition_defects_status,
    listing_level_metrics_product_condition_target_value,
    listing_level_metrics_product_condition_target_condition_type,
    listing_level_metrics_product_condition_target_target_type,
    listing_level_metrics_intellectual_property_time_frame_start,
    listing_level_metrics_intellectual_property_time_frame_end,
    listing_level_metrics_intellectual_property_time_frame_date_offset_duration,
    listing_level_metrics_intellectual_property_time_frame_date_offset_offset,
    listing_level_metrics_intellectual_property_defects_count,
    listing_level_metrics_intellectual_property_defects_status,
    listing_level_metrics_intellectual_property_target_value,
    listing_level_metrics_intellectual_property_target_condition_type,
    listing_level_metrics_intellectual_property_target_target_type,
    listing_level_metrics_regulatory_compliance_time_frame_start,
    listing_level_metrics_regulatory_compliance_time_frame_end,
    listing_level_metrics_regulatory_compliance_time_frame_date_offset_duration,
    listing_level_metrics_regulatory_compliance_time_frame_date_offset_offset,
    listing_level_metrics_regulatory_compliance_defects_count,
    listing_level_metrics_regulatory_compliance_defects_status,
    listing_level_metrics_regulatory_compliance_target_value,
    listing_level_metrics_regulatory_compliance_target_condition_type,
    listing_level_metrics_regulatory_compliance_target_target_type,
    listing_level_metrics_product_authenticity_time_frame_start,
    listing_level_metrics_product_authenticity_time_frame_end,
    listing_level_metrics_product_authenticity_time_frame_date_offset_duration,
    listing_level_metrics_product_authenticity_time_frame_date_offset_offset,
    listing_level_metrics_product_authenticity_defects_count,
    listing_level_metrics_product_authenticity_defects_status,
    listing_level_metrics_product_authenticity_target_value,
    listing_level_metrics_product_authenticity_target_condition_type,
    listing_level_metrics_product_authenticity_target_target_type,
    listing_level_metrics_restricted_products_time_frame_start,
    listing_level_metrics_restricted_products_time_frame_end,
    listing_level_metrics_restricted_products_time_frame_date_offset_duration,
    listing_level_metrics_restricted_products_time_frame_date_offset_offset,
    listing_level_metrics_restricted_products_defects_count,
    listing_level_metrics_restricted_products_defects_status,
    listing_level_metrics_restricted_products_target_value,
    listing_level_metrics_restricted_products_target_condition_type,
    listing_level_metrics_restricted_products_target_target_type,
    listing_level_metrics_product_review_abuse_time_frame_start,
    listing_level_metrics_product_review_abuse_time_frame_end,
    listing_level_metrics_product_review_abuse_time_frame_date_offset_duration,
    listing_level_metrics_product_review_abuse_time_frame_date_offset_offset,
    listing_level_metrics_product_review_abuse_defects_count,
    listing_level_metrics_product_review_abuse_defects_status,
    listing_level_metrics_product_review_abuse_target_value,
    listing_level_metrics_product_review_abuse_target_condition_type,
    listing_level_metrics_product_review_abuse_target_target_type,
    listing_level_metrics_listing_policy_time_frame_start,
    listing_level_metrics_listing_policy_time_frame_end,
    listing_level_metrics_listing_policy_time_frame_date_offset_duration,
    listing_level_metrics_listing_policy_time_frame_date_offset_offset,
    listing_level_metrics_listing_policy_defects_count,
    listing_level_metrics_listing_policy_defects_status,
    listing_level_metrics_listing_policy_target_value,
    listing_level_metrics_listing_policy_target_condition_type,
    listing_level_metrics_listing_policy_target_target_type,
    listing_level_metrics_positive_customer_experience_time_frame_start,
    listing_level_metrics_positive_customer_experience_time_frame_end,
    listing_level_metrics_positive_customer_experience_time_frame_date_offset_duration,
    listing_level_metrics_positive_customer_experience_time_frame_date_offset_offset,
    listing_level_metrics_positive_customer_experience_defects_count,
    listing_level_metrics_positive_customer_experience_defects_status,
    listing_level_metrics_positive_customer_experience_target_value,
    listing_level_metrics_positive_customer_experience_target_condition_type,
    listing_level_metrics_positive_customer_experience_target_target_type,
    listing_level_metrics_product_safety_time_frame_start,
    listing_level_metrics_product_safety_time_frame_end,
    listing_level_metrics_product_safety_time_frame_date_offset_duration,
    listing_level_metrics_product_safety_time_frame_date_offset_offset,
    listing_level_metrics_product_safety_defects_count,
    listing_level_metrics_product_safety_defects_status,
    listing_level_metrics_product_safety_target_value,
    listing_level_metrics_product_safety_target_condition_type,
    listing_level_metrics_product_safety_target_target_type,
    listing_level_metrics_food_and_product_safety_time_frame_start,
    listing_level_metrics_food_and_product_safety_time_frame_end,
    listing_level_metrics_food_and_product_safety_time_frame_date_offset_duration,
    listing_level_metrics_food_and_product_safety_time_frame_date_offset_offset,
    listing_level_metrics_food_and_product_safety_defects_count,
    listing_level_metrics_food_and_product_safety_defects_status,
    listing_level_metrics_food_and_product_safety_target_value,
    listing_level_metrics_food_and_product_safety_target_condition_type,
    listing_level_metrics_food_and_product_safety_target_target_type,
    ahr_response_account_health_rating_status,
    ahr_response_seller_facing_score,
    seller_enrolled_programs,
    selected_vendor_code,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src.report_fetched_and_loaded_at, 
    src.scraper_id, 
    src.seller_id, 
    src.country, 
    src.order_defect_metrics_metrics_afn_time_frame_start, 
    src.order_defect_metrics_metrics_afn_time_frame_end, 
    src.order_defect_metrics_metrics_afn_time_frame_date_offset_duration, 
    src.order_defect_metrics_metrics_afn_time_frame_date_offset_offset, 
    src.order_defect_metrics_metrics_afn_order_count, 
    src.order_defect_metrics_metrics_afn_order_with_defects_count, 
    src.order_defect_metrics_metrics_afn_order_with_defects_status, 
    src.order_defect_metrics_metrics_afn_claims_count, 
    src.order_defect_metrics_metrics_afn_claims_status, 
    src.order_defect_metrics_metrics_afn_chargebacks_count, 
    src.order_defect_metrics_metrics_afn_chargebacks_status, 
    src.order_defect_metrics_metrics_afn_negative_feedback_count, 
    src.order_defect_metrics_metrics_afn_negative_feedback_status, 
    src.order_defect_metrics_metrics_afn_fulfillment_type, 
    src.order_defect_metrics_metrics_afn_target_value, 
    src.order_defect_metrics_metrics_afn_target_condition_type, 
    src.order_defect_metrics_metrics_afn_target_target_type, 
    src.order_defect_metrics_metrics_afn_rate, 
    src.order_defect_metrics_metrics_afn_metric_summary_overall_status, 
    src.order_defect_metrics_metrics_afn_metric_summary_value_value, 
    src.order_defect_metrics_metrics_afn_metric_summary_value_type, 
    src.order_defect_metrics_metrics_afn_metric_summary_target_value, 
    src.order_defect_metrics_metrics_afn_metric_summary_target_condition_type, 
    src.order_defect_metrics_metrics_afn_metric_summary_target_target_type, 
    src.order_defect_metrics_metrics_mfn_time_frame_start, 
    src.order_defect_metrics_metrics_mfn_time_frame_end, 
    src.order_defect_metrics_metrics_mfn_time_frame_date_offset_duration, 
    src.order_defect_metrics_metrics_mfn_time_frame_date_offset_offset, 
    src.order_defect_metrics_metrics_mfn_order_count, 
    src.order_defect_metrics_metrics_mfn_order_with_defects_count, 
    src.order_defect_metrics_metrics_mfn_order_with_defects_status, 
    src.order_defect_metrics_metrics_mfn_claims_count, 
    src.order_defect_metrics_metrics_mfn_claims_status, 
    src.order_defect_metrics_metrics_mfn_chargebacks_count, 
    src.order_defect_metrics_metrics_mfn_chargebacks_status, 
    src.order_defect_metrics_metrics_mfn_negative_feedback_count, 
    src.order_defect_metrics_metrics_mfn_negative_feedback_status, 
    src.order_defect_metrics_metrics_mfn_fulfillment_type, 
    src.order_defect_metrics_metrics_mfn_target_value, 
    src.order_defect_metrics_metrics_mfn_target_condition_type, 
    src.order_defect_metrics_metrics_mfn_target_target_type, 
    src.order_defect_metrics_metrics_mfn_rate, 
    src.order_defect_metrics_metrics_mfn_metric_summary_overall_status, 
    src.order_defect_metrics_metrics_mfn_metric_summary_value_value, 
    src.order_defect_metrics_metrics_mfn_metric_summary_value_type, 
    src.order_defect_metrics_metrics_mfn_metric_summary_target_value, 
    src.order_defect_metrics_metrics_mfn_metric_summary_target_condition_type, 
    src.order_defect_metrics_metrics_mfn_metric_summary_target_target_type, 
    src.invoice_defect_metric, 
    src.late_ship_metric_time_frame_start, 
    src.late_ship_metric_time_frame_end, 
    src.late_ship_metric_time_frame_date_offset_duration, 
    src.late_ship_metric_time_frame_date_offset_offset, 
    src.late_ship_metric_order_count, 
    src.late_ship_metric_late_shipment_count, 
    src.late_ship_metric_late_shipment_status, 
    src.late_ship_metric_marketplace_id, 
    src.late_ship_metric_target_value, 
    src.late_ship_metric_target_condition_type, 
    src.late_ship_metric_target_target_type, 
    src.late_ship_metric_rate, 
    src.late_ship_metric_metric_summary_overall_status, 
    src.late_ship_metric_metric_summary_value_value, 
    src.late_ship_metric_metric_summary_value_type, 
    src.late_ship_metric_metric_summary_target_value, 
    src.late_ship_metric_metric_summary_target_condition_type, 
    src.late_ship_metric_metric_summary_target_target_type, 
    src.prefulfillment_cancellation_metric_time_frame_start, 
    src.prefulfillment_cancellation_metric_time_frame_end, 
    src.prefulfillment_cancellation_metric_time_frame_date_offset_duration, 
    src.prefulfillment_cancellation_metric_time_frame_date_offset_offset, 
    src.prefulfillment_cancellation_metric_order_count, 
    src.prefulfillment_cancellation_metric_cancellation_count, 
    src.prefulfillment_cancellation_metric_cancellation_status, 
    src.prefulfillment_cancellation_metric_marketplace_id, 
    src.prefulfillment_cancellation_metric_target_value, 
    src.prefulfillment_cancellation_metric_target_condition_type, 
    src.prefulfillment_cancellation_metric_target_target_type, 
    src.prefulfillment_cancellation_metric_rate, 
    src.prefulfillment_cancellation_metric_metric_summary_overall_status, 
    src.prefulfillment_cancellation_metric_metric_summary_value_value, 
    src.prefulfillment_cancellation_metric_metric_summary_value_type, 
    src.prefulfillment_cancellation_metric_metric_summary_target_value, 
    src.prefulfillment_cancellation_metric_metric_summary_target_condition_type, 
    src.prefulfillment_cancellation_metric_metric_summary_target_target_type, 
    src.valid_tracking_metrics_time_frame_start, 
    src.valid_tracking_metrics_time_frame_end, 
    src.valid_tracking_metrics_time_frame_date_offset_duration, 
    src.valid_tracking_metrics_time_frame_date_offset_offset, 
    src.valid_tracking_metrics_shipment_count, 
    src.valid_tracking_metrics_valid_tracking_count, 
    src.valid_tracking_metrics_valid_tracking_status, 
    src.valid_tracking_metrics_shipment_count_with_exempt_packages, 
    src.valid_tracking_metrics_exempted_shipment_count, 
    src.valid_tracking_metrics_exempted_defect_count, 
    src.valid_tracking_metrics_defect_count, 
    src.valid_tracking_metrics_target_value, 
    src.valid_tracking_metrics_target_condition_type, 
    src.valid_tracking_metrics_target_target_type, 
    src.valid_tracking_metrics_rate, 
    src.valid_tracking_metrics_metric_summary_overall_status, 
    src.valid_tracking_metrics_metric_summary_value_value, 
    src.valid_tracking_metrics_metric_summary_value_type, 
    src.valid_tracking_metrics_metric_summary_target_value, 
    src.valid_tracking_metrics_metric_summary_target_condition_type, 
    src.valid_tracking_metrics_metric_summary_target_target_type, 
    src.on_time_delivery_metrics_time_frame_start, 
    src.on_time_delivery_metrics_time_frame_end, 
    src.on_time_delivery_metrics_time_frame_date_offset_duration, 
    src.on_time_delivery_metrics_time_frame_date_offset_offset, 
    src.on_time_delivery_metrics_shipment_count_with_valid_tracking, 
    src.on_time_delivery_metrics_on_time_delivery_count, 
    src.on_time_delivery_metrics_on_time_delivery_status, 
    src.on_time_delivery_metrics_target_value, 
    src.on_time_delivery_metrics_target_condition_type, 
    src.on_time_delivery_metrics_target_target_type, 
    src.on_time_delivery_metrics_rate, 
    src.on_time_delivery_metrics_metric_summary_overall_status, 
    src.on_time_delivery_metrics_metric_summary_value_value, 
    src.on_time_delivery_metrics_metric_summary_value_type, 
    src.on_time_delivery_metrics_metric_summary_target_value, 
    src.on_time_delivery_metrics_metric_summary_target_condition_type, 
    src.on_time_delivery_metrics_metric_summary_target_target_type, 
    src.listing_level_metrics_automated_brand_protection_time_frame_start, 
    src.listing_level_metrics_automated_brand_protection_time_frame_end, 
    src.listing_level_metrics_automated_brand_protection_time_frame_date_offset_duration, 
    src.listing_level_metrics_automated_brand_protection_time_frame_date_offset_offset, 
    src.listing_level_metrics_automated_brand_protection_defects_count, 
    src.listing_level_metrics_automated_brand_protection_defects_status, 
    src.listing_level_metrics_automated_brand_protection_target_value, 
    src.listing_level_metrics_automated_brand_protection_target_condition_type, 
    src.listing_level_metrics_automated_brand_protection_target_target_type, 
    src.listing_level_metrics_product_condition_time_frame_start, 
    src.listing_level_metrics_product_condition_time_frame_end, 
    src.listing_level_metrics_product_condition_time_frame_date_offset_duration, 
    src.listing_level_metrics_product_condition_time_frame_date_offset_offset, 
    src.listing_level_metrics_product_condition_defects_count, 
    src.listing_level_metrics_product_condition_defects_status, 
    src.listing_level_metrics_product_condition_target_value, 
    src.listing_level_metrics_product_condition_target_condition_type, 
    src.listing_level_metrics_product_condition_target_target_type, 
    src.listing_level_metrics_intellectual_property_time_frame_start, 
    src.listing_level_metrics_intellectual_property_time_frame_end, 
    src.listing_level_metrics_intellectual_property_time_frame_date_offset_duration, 
    src.listing_level_metrics_intellectual_property_time_frame_date_offset_offset, 
    src.listing_level_metrics_intellectual_property_defects_count, 
    src.listing_level_metrics_intellectual_property_defects_status, 
    src.listing_level_metrics_intellectual_property_target_value, 
    src.listing_level_metrics_intellectual_property_target_condition_type, 
    src.listing_level_metrics_intellectual_property_target_target_type, 
    src.listing_level_metrics_regulatory_compliance_time_frame_start, 
    src.listing_level_metrics_regulatory_compliance_time_frame_end, 
    src.listing_level_metrics_regulatory_compliance_time_frame_date_offset_duration, 
    src.listing_level_metrics_regulatory_compliance_time_frame_date_offset_offset, 
    src.listing_level_metrics_regulatory_compliance_defects_count, 
    src.listing_level_metrics_regulatory_compliance_defects_status, 
    src.listing_level_metrics_regulatory_compliance_target_value, 
    src.listing_level_metrics_regulatory_compliance_target_condition_type, 
    src.listing_level_metrics_regulatory_compliance_target_target_type, 
    src.listing_level_metrics_product_authenticity_time_frame_start, 
    src.listing_level_metrics_product_authenticity_time_frame_end, 
    src.listing_level_metrics_product_authenticity_time_frame_date_offset_duration, 
    src.listing_level_metrics_product_authenticity_time_frame_date_offset_offset, 
    src.listing_level_metrics_product_authenticity_defects_count, 
    src.listing_level_metrics_product_authenticity_defects_status, 
    src.listing_level_metrics_product_authenticity_target_value, 
    src.listing_level_metrics_product_authenticity_target_condition_type, 
    src.listing_level_metrics_product_authenticity_target_target_type, 
    src.listing_level_metrics_restricted_products_time_frame_start, 
    src.listing_level_metrics_restricted_products_time_frame_end, 
    src.listing_level_metrics_restricted_products_time_frame_date_offset_duration, 
    src.listing_level_metrics_restricted_products_time_frame_date_offset_offset, 
    src.listing_level_metrics_restricted_products_defects_count, 
    src.listing_level_metrics_restricted_products_defects_status, 
    src.listing_level_metrics_restricted_products_target_value, 
    src.listing_level_metrics_restricted_products_target_condition_type, 
    src.listing_level_metrics_restricted_products_target_target_type, 
    src.listing_level_metrics_product_review_abuse_time_frame_start, 
    src.listing_level_metrics_product_review_abuse_time_frame_end, 
    src.listing_level_metrics_product_review_abuse_time_frame_date_offset_duration, 
    src.listing_level_metrics_product_review_abuse_time_frame_date_offset_offset, 
    src.listing_level_metrics_product_review_abuse_defects_count, 
    src.listing_level_metrics_product_review_abuse_defects_status, 
    src.listing_level_metrics_product_review_abuse_target_value, 
    src.listing_level_metrics_product_review_abuse_target_condition_type, 
    src.listing_level_metrics_product_review_abuse_target_target_type, 
    src.listing_level_metrics_listing_policy_time_frame_start, 
    src.listing_level_metrics_listing_policy_time_frame_end, 
    src.listing_level_metrics_listing_policy_time_frame_date_offset_duration, 
    src.listing_level_metrics_listing_policy_time_frame_date_offset_offset, 
    src.listing_level_metrics_listing_policy_defects_count, 
    src.listing_level_metrics_listing_policy_defects_status, 
    src.listing_level_metrics_listing_policy_target_value, 
    src.listing_level_metrics_listing_policy_target_condition_type, 
    src.listing_level_metrics_listing_policy_target_target_type, 
    src.listing_level_metrics_positive_customer_experience_time_frame_start, 
    src.listing_level_metrics_positive_customer_experience_time_frame_end, 
    src.listing_level_metrics_positive_customer_experience_time_frame_date_offset_duration, 
    src.listing_level_metrics_positive_customer_experience_time_frame_date_offset_offset, 
    src.listing_level_metrics_positive_customer_experience_defects_count, 
    src.listing_level_metrics_positive_customer_experience_defects_status, 
    src.listing_level_metrics_positive_customer_experience_target_value, 
    src.listing_level_metrics_positive_customer_experience_target_condition_type, 
    src.listing_level_metrics_positive_customer_experience_target_target_type, 
    src.listing_level_metrics_product_safety_time_frame_start, 
    src.listing_level_metrics_product_safety_time_frame_end, 
    src.listing_level_metrics_product_safety_time_frame_date_offset_duration, 
    src.listing_level_metrics_product_safety_time_frame_date_offset_offset, 
    src.listing_level_metrics_product_safety_defects_count, 
    src.listing_level_metrics_product_safety_defects_status, 
    src.listing_level_metrics_product_safety_target_value, 
    src.listing_level_metrics_product_safety_target_condition_type, 
    src.listing_level_metrics_product_safety_target_target_type, 
    src.listing_level_metrics_food_and_product_safety_time_frame_start, 
    src.listing_level_metrics_food_and_product_safety_time_frame_end, 
    src.listing_level_metrics_food_and_product_safety_time_frame_date_offset_duration, 
    src.listing_level_metrics_food_and_product_safety_time_frame_date_offset_offset, 
    src.listing_level_metrics_food_and_product_safety_defects_count, 
    src.listing_level_metrics_food_and_product_safety_defects_status, 
    src.listing_level_metrics_food_and_product_safety_target_value, 
    src.listing_level_metrics_food_and_product_safety_target_condition_type, 
    src.listing_level_metrics_food_and_product_safety_target_target_type, 
    src.ahr_response_account_health_rating_status, 
    src.ahr_response_seller_facing_score, 
    src.seller_enrolled_programs, 
    src.selected_vendor_code, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
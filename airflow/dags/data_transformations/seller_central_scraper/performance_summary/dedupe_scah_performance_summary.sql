CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_scah_performance_summary AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(seller_id AS VARCHAR), ''), '-',
            COALESCE(CAST(country AS VARCHAR), ''), '-',
            COALESCE(CAST(report_fetched_and_loaded_at::date AS VARCHAR), '')
            )) AS pk,
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        country,
        order_defect_metrics_metrics_afn_time_frame_start,
        order_defect_metrics_metrics_afn_time_frame_end,
        order_defect_metrics_metrics_afn_time_frame_date_offset_duration,
        order_defect_metrics_metrics_afn_time_frame_date_offset_offset,
        TRY_TO_NUMBER(order_defect_metrics_metrics_afn_order_count) as order_defect_metrics_metrics_afn_order_count,
        TRY_TO_NUMBER(order_defect_metrics_metrics_afn_order_with_defects_count) as order_defect_metrics_metrics_afn_order_with_defects_count,
        order_defect_metrics_metrics_afn_order_with_defects_status,
        TRY_TO_NUMBER(order_defect_metrics_metrics_afn_claims_count) as order_defect_metrics_metrics_afn_claims_count,
        order_defect_metrics_metrics_afn_claims_status,
        TRY_TO_NUMBER(order_defect_metrics_metrics_afn_chargebacks_count) as order_defect_metrics_metrics_afn_chargebacks_count,
        order_defect_metrics_metrics_afn_chargebacks_status,
        TRY_TO_NUMBER(order_defect_metrics_metrics_afn_negative_feedback_count) as order_defect_metrics_metrics_afn_negative_feedback_count,
        order_defect_metrics_metrics_afn_negative_feedback_status,
        order_defect_metrics_metrics_afn_fulfillment_type,
        TRY_TO_DECIMAL(order_defect_metrics_metrics_afn_target_value, 10, 4) as order_defect_metrics_metrics_afn_target_value,
        order_defect_metrics_metrics_afn_target_condition_type,
        order_defect_metrics_metrics_afn_target_target_type,
        TRY_TO_DECIMAL(order_defect_metrics_metrics_afn_rate, 10, 8) as order_defect_metrics_metrics_afn_rate,
        order_defect_metrics_metrics_afn_metric_summary_overall_status,
        TRY_TO_DECIMAL(order_defect_metrics_metrics_afn_metric_summary_value_value, 10, 8) as order_defect_metrics_metrics_afn_metric_summary_value_value,
        order_defect_metrics_metrics_afn_metric_summary_value_type,
        TRY_TO_DECIMAL(order_defect_metrics_metrics_afn_metric_summary_target_value, 10, 4) as order_defect_metrics_metrics_afn_metric_summary_target_value,
        order_defect_metrics_metrics_afn_metric_summary_target_condition_type,
        order_defect_metrics_metrics_afn_metric_summary_target_target_type,
        order_defect_metrics_metrics_mfn_time_frame_start,
        order_defect_metrics_metrics_mfn_time_frame_end,
        order_defect_metrics_metrics_mfn_time_frame_date_offset_duration,
        order_defect_metrics_metrics_mfn_time_frame_date_offset_offset,
        TRY_TO_NUMBER(order_defect_metrics_metrics_mfn_order_count) as order_defect_metrics_metrics_mfn_order_count,
        TRY_TO_NUMBER(order_defect_metrics_metrics_mfn_order_with_defects_count) as order_defect_metrics_metrics_mfn_order_with_defects_count,
        order_defect_metrics_metrics_mfn_order_with_defects_status,
        TRY_TO_NUMBER(order_defect_metrics_metrics_mfn_claims_count) as order_defect_metrics_metrics_mfn_claims_count,
        order_defect_metrics_metrics_mfn_claims_status,
        TRY_TO_NUMBER(order_defect_metrics_metrics_mfn_chargebacks_count) as order_defect_metrics_metrics_mfn_chargebacks_count,
        order_defect_metrics_metrics_mfn_chargebacks_status,
        TRY_TO_NUMBER(order_defect_metrics_metrics_mfn_negative_feedback_count) as order_defect_metrics_metrics_mfn_negative_feedback_count,
        order_defect_metrics_metrics_mfn_negative_feedback_status,
        order_defect_metrics_metrics_mfn_fulfillment_type,
        TRY_TO_DECIMAL(order_defect_metrics_metrics_mfn_target_value, 10, 4) as order_defect_metrics_metrics_mfn_target_value,
        order_defect_metrics_metrics_mfn_target_condition_type,
        order_defect_metrics_metrics_mfn_target_target_type,
        TRY_TO_DECIMAL(order_defect_metrics_metrics_mfn_rate, 10, 8) as order_defect_metrics_metrics_mfn_rate,
        order_defect_metrics_metrics_mfn_metric_summary_overall_status,
        TRY_TO_DECIMAL(order_defect_metrics_metrics_mfn_metric_summary_value_value, 10, 8) as order_defect_metrics_metrics_mfn_metric_summary_value_value,
        order_defect_metrics_metrics_mfn_metric_summary_value_type,
        TRY_TO_DECIMAL(order_defect_metrics_metrics_mfn_metric_summary_target_value, 10, 4) as order_defect_metrics_metrics_mfn_metric_summary_target_value,
        order_defect_metrics_metrics_mfn_metric_summary_target_condition_type,
        order_defect_metrics_metrics_mfn_metric_summary_target_target_type,
        invoice_defect_metric,
        late_ship_metric_time_frame_start,
        late_ship_metric_time_frame_end,
        late_ship_metric_time_frame_date_offset_duration,
        late_ship_metric_time_frame_date_offset_offset,
        TRY_TO_NUMBER(late_ship_metric_order_count) as late_ship_metric_order_count,
        TRY_TO_NUMBER(late_ship_metric_late_shipment_count) as late_ship_metric_late_shipment_count,
        late_ship_metric_late_shipment_status,
        late_ship_metric_marketplace_id,
        TRY_TO_DECIMAL(late_ship_metric_target_value, 10, 4) as late_ship_metric_target_value,
        late_ship_metric_target_condition_type,
        late_ship_metric_target_target_type,
        TRY_TO_DECIMAL(late_ship_metric_rate, 10, 8) as late_ship_metric_rate,
        late_ship_metric_metric_summary_overall_status,
        TRY_TO_DECIMAL(late_ship_metric_metric_summary_value_value, 10, 8) as late_ship_metric_metric_summary_value_value,
        late_ship_metric_metric_summary_value_type,
        TRY_TO_DECIMAL(late_ship_metric_metric_summary_target_value, 10, 4) as late_ship_metric_metric_summary_target_value,
        late_ship_metric_metric_summary_target_condition_type,
        late_ship_metric_metric_summary_target_target_type,
        prefulfillment_cancellation_metric_time_frame_start,
        prefulfillment_cancellation_metric_time_frame_end,
        prefulfillment_cancellation_metric_time_frame_date_offset_duration,
        prefulfillment_cancellation_metric_time_frame_date_offset_offset,
        TRY_TO_NUMBER(prefulfillment_cancellation_metric_order_count) as prefulfillment_cancellation_metric_order_count,
        TRY_TO_NUMBER(prefulfillment_cancellation_metric_cancellation_count) as prefulfillment_cancellation_metric_cancellation_count,
        prefulfillment_cancellation_metric_cancellation_status,
        prefulfillment_cancellation_metric_marketplace_id,
        TRY_TO_DECIMAL(prefulfillment_cancellation_metric_target_value, 10, 4) as prefulfillment_cancellation_metric_target_value,
        prefulfillment_cancellation_metric_target_condition_type,
        prefulfillment_cancellation_metric_target_target_type,
        TRY_TO_DECIMAL(prefulfillment_cancellation_metric_rate, 10, 8) as prefulfillment_cancellation_metric_rate,
        prefulfillment_cancellation_metric_metric_summary_overall_status,
        TRY_TO_DECIMAL(prefulfillment_cancellation_metric_metric_summary_value_value, 10, 8) as prefulfillment_cancellation_metric_metric_summary_value_value,
        prefulfillment_cancellation_metric_metric_summary_value_type,
        TRY_TO_DECIMAL(prefulfillment_cancellation_metric_metric_summary_target_value, 10, 4) as prefulfillment_cancellation_metric_metric_summary_target_value,
        prefulfillment_cancellation_metric_metric_summary_target_condition_type,
        prefulfillment_cancellation_metric_metric_summary_target_target_type,
        valid_tracking_metrics_time_frame_start,
        valid_tracking_metrics_time_frame_end,
        valid_tracking_metrics_time_frame_date_offset_duration,
        valid_tracking_metrics_time_frame_date_offset_offset,
        TRY_TO_NUMBER(valid_tracking_metrics_shipment_count) as valid_tracking_metrics_shipment_count,
        TRY_TO_NUMBER(valid_tracking_metrics_valid_tracking_count) as valid_tracking_metrics_valid_tracking_count,
        valid_tracking_metrics_valid_tracking_status,
        TRY_TO_NUMBER(valid_tracking_metrics_shipment_count_with_exempt_packages) as valid_tracking_metrics_shipment_count_with_exempt_packages,
        TRY_TO_NUMBER(valid_tracking_metrics_exempted_shipment_count) as valid_tracking_metrics_exempted_shipment_count,
        TRY_TO_NUMBER(valid_tracking_metrics_exempted_defect_count) as valid_tracking_metrics_exempted_defect_count,
        TRY_TO_NUMBER(valid_tracking_metrics_defect_count) as valid_tracking_metrics_defect_count,
        TRY_TO_DECIMAL(valid_tracking_metrics_target_value, 10, 4) as valid_tracking_metrics_target_value,
        valid_tracking_metrics_target_condition_type,
        valid_tracking_metrics_target_target_type,
        TRY_TO_DECIMAL(valid_tracking_metrics_rate, 10, 8) as valid_tracking_metrics_rate,
        valid_tracking_metrics_metric_summary_overall_status,
        TRY_TO_DECIMAL(valid_tracking_metrics_metric_summary_value_value, 10, 8) as valid_tracking_metrics_metric_summary_value_value,
        valid_tracking_metrics_metric_summary_value_type,
        TRY_TO_DECIMAL(valid_tracking_metrics_metric_summary_target_value, 10, 4) as valid_tracking_metrics_metric_summary_target_value,
        valid_tracking_metrics_metric_summary_target_condition_type,
        valid_tracking_metrics_metric_summary_target_target_type,
        on_time_delivery_metrics_time_frame_start,
        on_time_delivery_metrics_time_frame_end,
        on_time_delivery_metrics_time_frame_date_offset_duration,
        on_time_delivery_metrics_time_frame_date_offset_offset,
        TRY_TO_NUMBER(on_time_delivery_metrics_shipment_count_with_valid_tracking) as on_time_delivery_metrics_shipment_count_with_valid_tracking,
        TRY_TO_NUMBER(on_time_delivery_metrics_on_time_delivery_count) as on_time_delivery_metrics_on_time_delivery_count,
        on_time_delivery_metrics_on_time_delivery_status,
        TRY_TO_DECIMAL(on_time_delivery_metrics_target_value, 10, 4) as on_time_delivery_metrics_target_value,
        on_time_delivery_metrics_target_condition_type,
        on_time_delivery_metrics_target_target_type,
        TRY_TO_DECIMAL(on_time_delivery_metrics_rate, 10, 8) as on_time_delivery_metrics_rate,
        on_time_delivery_metrics_metric_summary_overall_status,
        TRY_TO_DECIMAL(on_time_delivery_metrics_metric_summary_value_value, 10, 8) as on_time_delivery_metrics_metric_summary_value_value,
        on_time_delivery_metrics_metric_summary_value_type,
        TRY_TO_DECIMAL(on_time_delivery_metrics_metric_summary_target_value, 10, 4) as on_time_delivery_metrics_metric_summary_target_value,
        on_time_delivery_metrics_metric_summary_target_condition_type,
        on_time_delivery_metrics_metric_summary_target_target_type,
        listing_level_metrics_automated_brand_protection_time_frame_start,
        listing_level_metrics_automated_brand_protection_time_frame_end,
        listing_level_metrics_automated_brand_protection_time_frame_date_offset_duration,
        listing_level_metrics_automated_brand_protection_time_frame_date_offset_offset,
        TRY_TO_NUMBER(listing_level_metrics_automated_brand_protection_defects_count) as listing_level_metrics_automated_brand_protection_defects_count,
        listing_level_metrics_automated_brand_protection_defects_status,
        TRY_TO_NUMBER(listing_level_metrics_automated_brand_protection_target_value) as listing_level_metrics_automated_brand_protection_target_value,
        listing_level_metrics_automated_brand_protection_target_condition_type,
        listing_level_metrics_automated_brand_protection_target_target_type,
        listing_level_metrics_product_condition_time_frame_start,
        listing_level_metrics_product_condition_time_frame_end,
        listing_level_metrics_product_condition_time_frame_date_offset_duration,
        listing_level_metrics_product_condition_time_frame_date_offset_offset,
        TRY_TO_NUMBER(listing_level_metrics_product_condition_defects_count) as listing_level_metrics_product_condition_defects_count,
        listing_level_metrics_product_condition_defects_status,
        TRY_TO_NUMBER(listing_level_metrics_product_condition_target_value) as listing_level_metrics_product_condition_target_value,
        listing_level_metrics_product_condition_target_condition_type,
        listing_level_metrics_product_condition_target_target_type,
        listing_level_metrics_intellectual_property_time_frame_start,
        listing_level_metrics_intellectual_property_time_frame_end,
        listing_level_metrics_intellectual_property_time_frame_date_offset_duration,
        listing_level_metrics_intellectual_property_time_frame_date_offset_offset,
        TRY_TO_NUMBER(listing_level_metrics_intellectual_property_defects_count) as listing_level_metrics_intellectual_property_defects_count,
        listing_level_metrics_intellectual_property_defects_status,
        TRY_TO_NUMBER(listing_level_metrics_intellectual_property_target_value) as listing_level_metrics_intellectual_property_target_value,
        listing_level_metrics_intellectual_property_target_condition_type,
        listing_level_metrics_intellectual_property_target_target_type,
        listing_level_metrics_regulatory_compliance_time_frame_start,
        listing_level_metrics_regulatory_compliance_time_frame_end,
        listing_level_metrics_regulatory_compliance_time_frame_date_offset_duration,
        listing_level_metrics_regulatory_compliance_time_frame_date_offset_offset,
        TRY_TO_NUMBER(listing_level_metrics_regulatory_compliance_defects_count) as listing_level_metrics_regulatory_compliance_defects_count,
        listing_level_metrics_regulatory_compliance_defects_status,
        TRY_TO_NUMBER(listing_level_metrics_regulatory_compliance_target_value) as listing_level_metrics_regulatory_compliance_target_value,
        listing_level_metrics_regulatory_compliance_target_condition_type,
        listing_level_metrics_regulatory_compliance_target_target_type,
        listing_level_metrics_product_authenticity_time_frame_start,
        listing_level_metrics_product_authenticity_time_frame_end,
        listing_level_metrics_product_authenticity_time_frame_date_offset_duration,
        listing_level_metrics_product_authenticity_time_frame_date_offset_offset,
        TRY_TO_NUMBER(listing_level_metrics_product_authenticity_defects_count) as listing_level_metrics_product_authenticity_defects_count,
        listing_level_metrics_product_authenticity_defects_status,
        TRY_TO_NUMBER(listing_level_metrics_product_authenticity_target_value) as listing_level_metrics_product_authenticity_target_value,
        listing_level_metrics_product_authenticity_target_condition_type,
        listing_level_metrics_product_authenticity_target_target_type,
        listing_level_metrics_restricted_products_time_frame_start,
        listing_level_metrics_restricted_products_time_frame_end,
        listing_level_metrics_restricted_products_time_frame_date_offset_duration,
        listing_level_metrics_restricted_products_time_frame_date_offset_offset,
        TRY_TO_NUMBER(listing_level_metrics_restricted_products_defects_count) as listing_level_metrics_restricted_products_defects_count,
        listing_level_metrics_restricted_products_defects_status,
        TRY_TO_NUMBER(listing_level_metrics_restricted_products_target_value) as listing_level_metrics_restricted_products_target_value,
        listing_level_metrics_restricted_products_target_condition_type,
        listing_level_metrics_restricted_products_target_target_type,
        listing_level_metrics_product_review_abuse_time_frame_start,
        listing_level_metrics_product_review_abuse_time_frame_end,
        listing_level_metrics_product_review_abuse_time_frame_date_offset_duration,
        listing_level_metrics_product_review_abuse_time_frame_date_offset_offset,
        TRY_TO_NUMBER(listing_level_metrics_product_review_abuse_defects_count) as listing_level_metrics_product_review_abuse_defects_count,
        listing_level_metrics_product_review_abuse_defects_status,
        TRY_TO_NUMBER(listing_level_metrics_product_review_abuse_target_value) as listing_level_metrics_product_review_abuse_target_value,
        listing_level_metrics_product_review_abuse_target_condition_type,
        listing_level_metrics_product_review_abuse_target_target_type,
        listing_level_metrics_listing_policy_time_frame_start,
        listing_level_metrics_listing_policy_time_frame_end,
        listing_level_metrics_listing_policy_time_frame_date_offset_duration,
        listing_level_metrics_listing_policy_time_frame_date_offset_offset,
        TRY_TO_NUMBER(listing_level_metrics_listing_policy_defects_count) as listing_level_metrics_listing_policy_defects_count,
        listing_level_metrics_listing_policy_defects_status,
        TRY_TO_NUMBER(listing_level_metrics_listing_policy_target_value) as listing_level_metrics_listing_policy_target_value,
        listing_level_metrics_listing_policy_target_condition_type,
        listing_level_metrics_listing_policy_target_target_type,
        listing_level_metrics_positive_customer_experience_time_frame_start,
        listing_level_metrics_positive_customer_experience_time_frame_end,
        listing_level_metrics_positive_customer_experience_time_frame_date_offset_duration,
        listing_level_metrics_positive_customer_experience_time_frame_date_offset_offset,
        TRY_TO_NUMBER(listing_level_metrics_positive_customer_experience_defects_count) as listing_level_metrics_positive_customer_experience_defects_count,
        listing_level_metrics_positive_customer_experience_defects_status,
        TRY_TO_NUMBER(listing_level_metrics_positive_customer_experience_target_value) as listing_level_metrics_positive_customer_experience_target_value,
        listing_level_metrics_positive_customer_experience_target_condition_type,
        listing_level_metrics_positive_customer_experience_target_target_type,
        listing_level_metrics_product_safety_time_frame_start,
        listing_level_metrics_product_safety_time_frame_end,
        listing_level_metrics_product_safety_time_frame_date_offset_duration,
        listing_level_metrics_product_safety_time_frame_date_offset_offset,
        TRY_TO_NUMBER(listing_level_metrics_product_safety_defects_count) as listing_level_metrics_product_safety_defects_count,
        listing_level_metrics_product_safety_defects_status,
        TRY_TO_NUMBER(listing_level_metrics_product_safety_target_value) as listing_level_metrics_product_safety_target_value,
        listing_level_metrics_product_safety_target_condition_type,
        listing_level_metrics_product_safety_target_target_type,
        listing_level_metrics_food_and_product_safety_time_frame_start,
        listing_level_metrics_food_and_product_safety_time_frame_end,
        listing_level_metrics_food_and_product_safety_time_frame_date_offset_duration,
        listing_level_metrics_food_and_product_safety_time_frame_date_offset_offset,
        TRY_TO_NUMBER(listing_level_metrics_food_and_product_safety_defects_count) as listing_level_metrics_food_and_product_safety_defects_count,
        listing_level_metrics_food_and_product_safety_defects_status,
        TRY_TO_NUMBER(listing_level_metrics_food_and_product_safety_target_value) as listing_level_metrics_food_and_product_safety_target_value,
        listing_level_metrics_food_and_product_safety_target_condition_type,
        listing_level_metrics_food_and_product_safety_target_target_type,
        ahr_response_account_health_rating_status,
        TRY_TO_DECIMAL(ahr_response_seller_facing_score, 10, 2) as ahr_response_seller_facing_score,
        seller_enrolled_programs,
        selected_vendor_code,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_scah_performance_summary
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY seller_id, country, report_fetched_and_loaded_at::date
        ORDER BY report_fetched_and_loaded_at DESC NULLS LAST) = 1
);
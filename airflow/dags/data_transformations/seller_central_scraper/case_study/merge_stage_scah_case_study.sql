CREATE TABLE IF NOT EXISTS $stage_db.merge_scah_case_study AS
SELECT
    *,
    '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
    '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
FROM $stage_db.dedupe_scah_case_study
WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_scah_case_study AS tgt
USING
    $stage_db.dedupe_scah_case_study AS src
    ON 1 = 1
    AND src.pk = tgt.pk
WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    -- metadata fields
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.scraper_id = src.scraper_id,
    tgt.seller_id = src.seller_id,
    tgt.scraper_country = src.scraper_country,
    tgt.scraper_marketplace_id = src.scraper_marketplace_id,
    -- case study fields
    tgt.case_id = src.case_id,
    tgt.thread_id = src.thread_id,
    tgt.topic_id_source = src.topic_id_source,
    tgt.topic = src.topic,
    tgt.status = src.status,
    tgt.orders = src.orders,
    tgt.asins = src.asins,
    tgt.open_date_utc = src.open_date_utc,
    tgt.close_date_utc = src.close_date_utc,
    tgt.last_updated_date_utc = src.last_updated_date_utc,
    tgt.subject = src.subject,
    tgt.buyer_name = src.buyer_name,
    tgt.buyer_lop = src.buyer_lop,
    tgt.is_business_user = src.is_business_user,
    tgt.sla_label = src.sla_label,
    tgt.is_over_sla = src.is_over_sla,
    tgt.case_study_action_type = src.case_study_action_type,
    tgt.case_study_action_creation_date_utc = src.case_study_action_creation_date_utc,
    tgt.case_study_action_due_date_utc = src.case_study_action_due_date_utc,
    tgt.marketplace_display_name = src.marketplace_display_name,
    tgt.case_study_marketplace_id = src.case_study_marketplace_id,
    tgt.case_study_country_code = src.case_study_country_code,
    -- etl fields
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    -- metadata fields
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    scraper_country,
    scraper_marketplace_id,
    -- case study fields
    case_id,
    thread_id,
    topic_id_source,
    topic,
    status,
    orders,
    asins,
    open_date_utc,
    close_date_utc,
    last_updated_date_utc,
    subject,
    buyer_name,
    buyer_lop,
    is_business_user,
    sla_label,
    is_over_sla,
    case_study_action_type,
    case_study_action_creation_date_utc,
    case_study_action_due_date_utc,
    marketplace_display_name,
    case_study_marketplace_id,
    case_study_country_code,
    -- etl fields
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES (
    src.pk,
    -- metadata fields
    src.report_fetched_and_loaded_at,
    src.scraper_id,
    src.seller_id,
    src.scraper_country,
    src.scraper_marketplace_id,
    -- case study fields
    src.case_id,
    src.thread_id,
    src.topic_id_source,
    src.topic,
    src.status,
    src.orders,
    src.asins,
    src.open_date_utc,
    src.close_date_utc,
    src.last_updated_date_utc,
    src.subject,
    src.buyer_name,
    src.buyer_lop,
    src.is_business_user,
    src.sla_label,
    src.is_over_sla,
    src.case_study_action_type,
    src.case_study_action_creation_date_utc,
    src.case_study_action_due_date_utc,
    src.marketplace_display_name,
    src.case_study_marketplace_id,
    src.case_study_country_code,
    -- etl fields
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);
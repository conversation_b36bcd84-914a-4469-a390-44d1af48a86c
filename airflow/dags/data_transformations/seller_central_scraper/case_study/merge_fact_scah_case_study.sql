CREATE TABLE IF NOT EXISTS $curated_db.fact_scah_case_study (
    PK VARCHAR(32),
    -- metadata fields
    REPORT_FETCHED_AND_LOADED_AT VARCHAR,
    SCRA<PERSON>ER_ID VARCHAR,
    SELLER_ID VARCHAR,
    SELLER_NAME VARCHAR,
    <PERSON><PERSON><PERSON>ER_COUNTRY_CODE VARCHAR,
    SCRA<PERSON>ER_MARKETPLACE_ID VARCHAR,
    -- case study fields
    CASE_ID VARCHAR,
    THREAD_ID VARCHAR,
    TOPIC_ID_SOURCE VARCHAR,
    TOPI<PERSON> VARCHAR,
    STATUS VARCHAR,
    ORDERS VARCHAR,
    ASINS VARCHAR,
    OPEN_DATE_UTC TIMESTAMP,
    CLOSE_DATE_UTC TIMESTAMP,
    LAST_UPDATED_DATE_UTC TIMESTAMP,
    SUBJECT VARCHAR,
    BUYER_NAME VARCHAR,
    BUYER_LOP VARCHAR,
    IS_BUSINESS_USER BOOLEAN,
    SLA_LABEL VARCHAR,
    IS_OVER_SLA BOOLEAN,
    CASE_STUDY_ACTION_TYPE VARCHAR,
    CASE_STUDY_ACTION_CREATION_DATE_UTC TIMESTAMP,
    CASE_STUDY_ACTION_DUE_DATE_UTC TIMESTAMP,
    MARKETPLACE_DISPLAY_NAME VARCHAR,
    CASE_STUDY_MARKETPLACE_ID VARCHAR,
    CASE_STUDY_COUNTRY_CODE VARCHAR,
    -- etl fields
    FILE_NAME VARCHAR,
    ETL_BATCH_RUN_TIME TIMESTAMP_NTZ,
    DATA_SOURCE VARCHAR,
    CREATED_BY VARCHAR DEFAULT 'DAG: fact_scah_case_study',
    UPDATED_BY VARCHAR DEFAULT 'DAG: fact_scah_case_study',
    RECORD_CREATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE(),
    RECORD_UPDATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE()
);

MERGE INTO $curated_db.fact_scah_case_study target
USING (
    SELECT
        cr.PK,
        cr.REPORT_FETCHED_AND_LOADED_AT,
        cr.SCRAPER_ID,
        cr.SELLER_ID,
        cr.SCRAPER_MARKETPLACE_ID,
        si.SELLER_NAME,
        cr.SCRAPER_COUNTRY AS SCRAPER_COUNTRY_CODE,
        cr.CASE_ID,
        cr.THREAD_ID,
        cr.TOPIC_ID_SOURCE,
        cr.TOPIC,
        cr.STATUS,
        cr.ORDERS,
        cr.ASINS,
        cr.OPEN_DATE_UTC,
        cr.CLOSE_DATE_UTC,
        cr.LAST_UPDATED_DATE_UTC,
        cr.SUBJECT,
        cr.BUYER_NAME,
        cr.BUYER_LOP,
        cr.IS_BUSINESS_USER,
        cr.SLA_LABEL,
        cr.IS_OVER_SLA,
        cr.CASE_STUDY_ACTION_TYPE,
        cr.CASE_STUDY_ACTION_CREATION_DATE_UTC,
        cr.CASE_STUDY_ACTION_DUE_DATE_UTC,
        cr.MARKETPLACE_DISPLAY_NAME,
        cr.CASE_STUDY_MARKETPLACE_ID,
        cr.CASE_STUDY_COUNTRY_CODE,
        cr.FILE_NAME,
        cr.ETL_BATCH_RUN_TIME,
        'dwh.staging.merge_scah_case_study' AS DATA_SOURCE,
        'DAG: fact_scah_case_study' AS CREATED_BY,
        'DAG: fact_scah_case_study' AS UPDATED_BY,
        cr.RECORD_CREATED_TIMESTAMP_UTC,
        cr.RECORD_UPDATED_TIMESTAMP_UTC
    FROM $stage_db.merge_scah_case_study cr
    LEFT JOIN $stage_db.stg_seller_info si ON cr.SELLER_ID = si.SELLER_ID
) source
ON target.PK = source.PK
WHEN MATCHED THEN
    UPDATE SET
        -- metadata fields
        target.REPORT_FETCHED_AND_LOADED_AT = source.REPORT_FETCHED_AND_LOADED_AT,
        target.SCRAPER_ID = source.SCRAPER_ID,
        target.SELLER_ID = source.SELLER_ID,
        target.SELLER_NAME = source.SELLER_NAME,
        target.SCRAPER_COUNTRY_CODE = source.SCRAPER_COUNTRY_CODE,
        target.SCRAPER_MARKETPLACE_ID = source.SCRAPER_MARKETPLACE_ID,
        -- case study fields
        target.CASE_ID = source.CASE_ID,
        target.THREAD_ID = source.THREAD_ID,
        target.TOPIC_ID_SOURCE = source.TOPIC_ID_SOURCE,
        target.TOPIC = source.TOPIC,
        target.STATUS = source.STATUS,
        target.ORDERS = source.ORDERS,
        target.ASINS = source.ASINS,
        target.OPEN_DATE_UTC = source.OPEN_DATE_UTC,
        target.CLOSE_DATE_UTC = source.CLOSE_DATE_UTC,
        target.LAST_UPDATED_DATE_UTC = source.LAST_UPDATED_DATE_UTC,
        target.SUBJECT = source.SUBJECT,
        target.BUYER_NAME = source.BUYER_NAME,
        target.BUYER_LOP = source.BUYER_LOP,
        target.IS_BUSINESS_USER = source.IS_BUSINESS_USER,
        target.SLA_LABEL = source.SLA_LABEL,
        target.IS_OVER_SLA = source.IS_OVER_SLA,
        target.CASE_STUDY_ACTION_TYPE = source.CASE_STUDY_ACTION_TYPE,
        target.CASE_STUDY_ACTION_CREATION_DATE_UTC = source.CASE_STUDY_ACTION_CREATION_DATE_UTC,
        target.CASE_STUDY_ACTION_DUE_DATE_UTC = source.CASE_STUDY_ACTION_DUE_DATE_UTC,
        target.MARKETPLACE_DISPLAY_NAME = source.MARKETPLACE_DISPLAY_NAME,
        target.CASE_STUDY_MARKETPLACE_ID = source.CASE_STUDY_MARKETPLACE_ID,
        target.CASE_STUDY_COUNTRY_CODE = source.CASE_STUDY_COUNTRY_CODE,
        -- etl fields
        target.FILE_NAME = source.FILE_NAME,
        target.ETL_BATCH_RUN_TIME = source.ETL_BATCH_RUN_TIME,
        target.DATA_SOURCE = source.DATA_SOURCE,
        target.UPDATED_BY = source.UPDATED_BY,
        target.RECORD_UPDATED_TIMESTAMP_UTC = SYSDATE()
WHEN NOT MATCHED THEN
    INSERT (
        PK,
        -- metadata fields
        REPORT_FETCHED_AND_LOADED_AT,
        SCRAPER_ID,
        SELLER_ID,
        SELLER_NAME,
        SCRAPER_COUNTRY_CODE,
        SCRAPER_MARKETPLACE_ID,
        -- case study fields
        CASE_ID,
        THREAD_ID,
        TOPIC_ID_SOURCE,
        TOPIC,
        STATUS,
        ORDERS,
        ASINS,
        OPEN_DATE_UTC,
        CLOSE_DATE_UTC,
        LAST_UPDATED_DATE_UTC,
        SUBJECT,
        BUYER_NAME,
        BUYER_LOP,
        IS_BUSINESS_USER,
        SLA_LABEL,
        IS_OVER_SLA,
        CASE_STUDY_ACTION_TYPE,
        CASE_STUDY_ACTION_CREATION_DATE_UTC,
        CASE_STUDY_ACTION_DUE_DATE_UTC,
        MARKETPLACE_DISPLAY_NAME,
        CASE_STUDY_MARKETPLACE_ID,
        CASE_STUDY_COUNTRY_CODE,
        -- etl fields
        FILE_NAME,
        ETL_BATCH_RUN_TIME,
        DATA_SOURCE,
        CREATED_BY,
        UPDATED_BY,
        RECORD_CREATED_TIMESTAMP_UTC,
        RECORD_UPDATED_TIMESTAMP_UTC
    )
    VALUES (
        source.PK,
        -- metadata fields
        source.REPORT_FETCHED_AND_LOADED_AT,
        source.SCRAPER_ID,
        source.SELLER_ID,
        source.SELLER_NAME,
        source.SCRAPER_COUNTRY_CODE,
        source.SCRAPER_MARKETPLACE_ID,
        -- case study fields
        source.CASE_ID,
        source.THREAD_ID,
        source.TOPIC_ID_SOURCE,
        source.TOPIC,
        source.STATUS,
        source.ORDERS,
        source.ASINS,
        source.OPEN_DATE_UTC,
        source.CLOSE_DATE_UTC,
        source.LAST_UPDATED_DATE_UTC,
        source.SUBJECT,
        source.BUYER_NAME,
        source.BUYER_LOP,
        source.IS_BUSINESS_USER,
        source.SLA_LABEL,
        source.IS_OVER_SLA,
        source.CASE_STUDY_ACTION_TYPE,
        source.CASE_STUDY_ACTION_CREATION_DATE_UTC,
        source.CASE_STUDY_ACTION_DUE_DATE_UTC,
        source.MARKETPLACE_DISPLAY_NAME,
        source.CASE_STUDY_MARKETPLACE_ID,
        source.CASE_STUDY_COUNTRY_CODE,
        -- etl fields
        source.FILE_NAME,
        source.ETL_BATCH_RUN_TIME,
        source.DATA_SOURCE,
        source.CREATED_BY,
        source.UPDATED_BY,
        SYSDATE(),
        SYSDATE()
    );
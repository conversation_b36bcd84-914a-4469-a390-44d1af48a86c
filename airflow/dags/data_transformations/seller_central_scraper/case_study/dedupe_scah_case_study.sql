CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_scah_case_study AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(case_id AS VARCHAR), ''), '-',
            COALESCE(CAST(thread_id AS VARCHAR), ''), '-',
            COALESCE(CAST(scraper_country AS VARCHAR), ''), '-',
            COALESCE(CAST(seller_id AS VARCHAR), ''), '-',
            COALESCE(CAST(scraper_marketplace_id AS VARCHAR), ''), '-',
            COALESCE(CAST(report_fetched_and_loaded_at::date AS VARCHAR), '')
            )) AS pk,
        --  metadata fields  --
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        scraper_marketplace_id,
        scraper_country,
        -- case study fields
        case_id,
        thread_id,
        topic_id_source,
        topic,
        status,
        orders,
        asins,
        TRY_TO_TIMESTAMP(open_date) AS open_date_utc,
        TRY_TO_TIMESTAMP(close_date) AS close_date_utc,
        TRY_TO_TIMESTAMP(last_updated_date) AS last_updated_date_utc,
        subject,
        buyer_name,
        buyer_lop,
        TRY_TO_BOOLEAN(is_business_user) AS is_business_user,
        sla_label,
        TRY_TO_BOOLEAN(is_over_sla) AS is_over_sla,
        case_study_action_type,
        TRY_TO_TIMESTAMP(case_study_action_creation_date) AS case_study_action_creation_date_utc,
        TRY_TO_TIMESTAMP(case_study_action_due_date) AS case_study_action_due_date_utc,
        marketplace_display_name,
        case_study_marketplace_id,
        case_study_country_code,
        --  etl fields  --
        file_name,
        etl_batch_run_time

    FROM $raw_db.raw_scah_case_study
    WHERE 1=1
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY report_fetched_and_loaded_at DESC NULLS LAST) = 1
);
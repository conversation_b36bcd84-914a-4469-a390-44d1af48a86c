CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_scah_case_study AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_scah_case_study
    WHERE 1 = 0;

INSERT INTO $raw_db.log_scah_case_study (
    -- metadata fields
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    scraper_marketplace_id,
    scraper_country,
    -- case study fields
    case_id,
    thread_id,
    topic_id_source,
    topic,
    status,
    orders,
    asins,
    open_date,
    close_date,
    last_updated_date,
    subject,
    buyer_name,
    buyer_lop,
    is_business_user,
    sla_label,
    is_over_sla,
    case_study_action_type,
    case_study_action_creation_date,
    case_study_action_due_date,
    marketplace_display_name,
    case_study_marketplace_id,
    case_study_country_code,
    -- etl fields
    file_name,
    etl_batch_run_time,
    log_timestamp_utc
)
    SELECT
        -- metadata fields
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        scraper_marketplace_id,
        scraper_country,
        -- case study fields
        case_id,
        thread_id,
        topic_id_source,
        topic,
        status,
        orders,
        asins,
        open_date,
        close_date,
        last_updated_date,
        subject,
        buyer_name,
        buyer_lop,
        is_business_user,
        sla_label,
        is_over_sla,
        case_study_action_type,
        case_study_action_creation_date,
        case_study_action_due_date,
        marketplace_display_name,
        case_study_marketplace_id,
        case_study_country_code,
        -- etl fields
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM
    $raw_db.raw_scah_case_study;
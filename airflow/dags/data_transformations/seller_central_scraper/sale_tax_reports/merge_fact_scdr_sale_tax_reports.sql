CREATE TABLE IF NOT EXISTS $curated_db.fact_scdr_sale_tax_reports (
    PK VARCHAR(32),
    -- metadata fields
    REPORT_FETCHED_AND_LOADED_AT VARCHAR,
    <PERSON>RA<PERSON>ER_ID VARCHAR,
    SELLER_ID VARCHAR,
    <PERSON><PERSON>ER_NAME VARCHAR,
    COUNTRY_CODE VARCHAR,
    MAR<PERSON><PERSON>LACE_ID VARCHAR,
    MERCHANT_ID VARCHAR,
    -- sale tax report fields
    ORDER_ID VARCHAR,
    ORDER_DATE VARCHAR,
    SHIPMENT_ID VARCHAR,
    SHIPMENT_DATE VARCHAR,
    TAX_CALCULATED_DATE VARCHAR,
    POSTED_DATE VARCHAR,
    MARKETPLACE VARCHAR,
    FU<PERSON><PERSON><PERSON>ENT VARCHAR,
    ASIN VARCHAR,
    SKU VARCHAR,
    TRANSACTION_TYPE VARCHAR,
    TAX_COLLECTION_MODEL VARCHAR,
    TAX_COLLECTION_RESPONSIBLE_PARTY VARCHAR,
    PRODUCT_TAX_CODE VARCHAR,
    QU<PERSON><PERSON><PERSON> NUMBER,
    <PERSON><PERSON>RENC<PERSON> VARCHAR,
    BUY<PERSON>_EXEMPTION_CODE VARCHAR,
    BUYER_EXEMPTION_DOMAIN VARCHAR,
    BUYER_EXEMPTION_CERTIFICATE_ID VARCHAR,
    DISPLAY_PRICE VARCHAR,
    DISPLAY_PRICE_TAX_INCLUSIVE VARCHAR,
    TAX_EXCLUSIVE_SELLING_PRICE VARCHAR,
    TOTAL_TAX VARCHAR,
    TOTAL_TAX_COLLECTED_BY_AMAZON VARCHAR,
    FINANCIAL_COMPONENT VARCHAR,
    SHIP_FROM_CITY VARCHAR,
    SHIP_FROM_STATE VARCHAR,
    SHIP_FROM_COUNTRY VARCHAR,
    SHIP_FROM_POSTAL_CODE VARCHAR,
    SHIP_FROM_TAX_LOCATION_CODE VARCHAR,
    SHIP_TO_CITY VARCHAR,
    SHIP_TO_STATE VARCHAR,
    SHIP_TO_COUNTRY VARCHAR,
    SHIP_TO_POSTAL_CODE VARCHAR,
    SHIP_TO_LOCATION_CODE VARCHAR,
    TAXED_LOCATION_CODE VARCHAR,
    TAX_ADDRESS_ROLE VARCHAR,
    JURISDICTION_LEVEL VARCHAR,
    JURISDICTION_NAME VARCHAR,
    DISPLAY_PROMO_AMOUNT VARCHAR,
    DISPLAY_PROMO_TAX_INCLUSIVE VARCHAR,
    IS_PROMO_APPLIED BOOLEAN,
    POST_PROMO_TAXABLE_BASIS VARCHAR,
    PRE_PROMO_TAXABLE_BASIS VARCHAR,
    PROMO_AMOUNT_BASIS VARCHAR,
    PROMO_ID_DOMAIN VARCHAR,
    PROMO_AMOUNT_TAX VARCHAR,
    PROMOTION_IDENTIFIER VARCHAR,
    PROMO_RULE_REASON_CODE VARCHAR,
    PROMO_TAX_PRICE_TYPE VARCHAR,
    TAX_AMOUNT VARCHAR,
    TAX_AMOUNT_COLLECTED_BY_AMAZON VARCHAR,
    TAXED_JURISDICTION_TAX_RATE VARCHAR,
    TAX_CATEGORY VARCHAR,
    TAX_TYPE VARCHAR,
    TAX_CALCULATION_REASON_CODE VARCHAR,
    NON_TAXABLE_AMOUNT VARCHAR,
    TAXABLE_AMOUNT VARCHAR,
    -- etl fields
    FILE_NAME VARCHAR,
    ETL_BATCH_RUN_TIME TIMESTAMP_NTZ,
    DATA_SOURCE VARCHAR,
    CREATED_BY VARCHAR DEFAULT 'DAG: fact_scdr_sale_tax_reports',
    UPDATED_BY VARCHAR DEFAULT 'DAG: fact_scdr_sale_tax_reports',
    RECORD_CREATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE(),
    RECORD_UPDATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE()
);

MERGE INTO $curated_db.fact_scdr_sale_tax_reports target
USING (
    SELECT
        sltxr.PK,
        -- metadata fields
        sltxr.REPORT_FETCHED_AND_LOADED_AT,
        sltxr.SCRAPER_ID,
        sltxr.SELLER_ID,
        si.SELLER_NAME,
        sltxr.COUNTRY AS COUNTRY_CODE,
        sltxr.MARKETPLACE_ID,
        sltxr.MERCHANT_ID,
        -- sale tax report fields
        sltxr.ORDER_ID,
        sltxr.ORDER_DATE,
        sltxr.SHIPMENT_ID,
        sltxr.SHIPMENT_DATE,
        sltxr.TAX_CALCULATED_DATE,
        sltxr.POSTED_DATE,
        sltxr.MARKETPLACE,
        sltxr.FULFILLMENT,
        sltxr.ASIN,
        sltxr.SKU,
        sltxr.TRANSACTION_TYPE,
        sltxr.TAX_COLLECTION_MODEL,
        sltxr.TAX_COLLECTION_RESPONSIBLE_PARTY,
        sltxr.PRODUCT_TAX_CODE,
        sltxr.QUANTITY,
        sltxr.CURRENCY,
        sltxr.BUYER_EXEMPTION_CODE,
        sltxr.BUYER_EXEMPTION_DOMAIN,
        sltxr.BUYER_EXEMPTION_CERTIFICATE_ID,
        sltxr.DISPLAY_PRICE,
        sltxr.DISPLAY_PRICE_TAX_INCLUSIVE,
        sltxr.tax_exclusive_selling_price AS TAX_EXCLUSIVE_SELLING_PRICE,
        sltxr.TOTAL_TAX,
        sltxr.TOTAL_TAX_COLLECTED_BY_AMAZON,
        sltxr.FINANCIAL_COMPONENT,
        sltxr.SHIP_FROM_CITY,
        sltxr.SHIP_FROM_STATE,
        sltxr.SHIP_FROM_COUNTRY,
        sltxr.SHIP_FROM_POSTAL_CODE,
        sltxr.SHIP_FROM_TAX_LOCATION_CODE,
        sltxr.SHIP_TO_CITY,
        sltxr.SHIP_TO_STATE,
        sltxr.SHIP_TO_COUNTRY,
        sltxr.SHIP_TO_POSTAL_CODE,
        sltxr.SHIP_TO_LOCATION_CODE,
        sltxr.TAXED_LOCATION_CODE,
        sltxr.TAX_ADDRESS_ROLE,
        sltxr.JURISDICTION_LEVEL,
        sltxr.JURISDICTION_NAME,
        sltxr.DISPLAY_PROMO_AMOUNT,
        sltxr.DISPLAY_PROMO_TAX_INCLUSIVE,
        sltxr.IS_PROMO_APPLIED,
        sltxr.POST_PROMO_TAXABLE_BASIS,
        sltxr.PRE_PROMO_TAXABLE_BASIS,
        sltxr.PROMO_AMOUNT_BASIS,
        sltxr.PROMO_ID_DOMAIN,
        sltxr.PROMO_AMOUNT_TAX,
        sltxr.PROMOTION_IDENTIFIER,
        sltxr.PROMO_RULE_REASON_CODE,
        sltxr.PROMO_TAX_PRICE_TYPE,
        sltxr.TAX_AMOUNT,
        sltxr.TAX_AMOUNT_COLLECTED_BY_AMAZON,
        sltxr.TAXED_JURISDICTION_TAX_RATE,
        sltxr.TAX_CATEGORY,
        sltxr.TAX_TYPE,
        sltxr.TAX_CALCULATION_REASON_CODE,
        sltxr.NON_TAXABLE_AMOUNT,
        sltxr.TAXABLE_AMOUNT,
        -- etl fields
        sltxr.FILE_NAME,
        sltxr.ETL_BATCH_RUN_TIME,
        'dwh.staging.merge_scdr_sale_tax_reports' AS DATA_SOURCE,
        'DAG: fact_scdr_sale_tax_reports' AS CREATED_BY,
        'DAG: fact_scdr_sale_tax_reports' AS UPDATED_BY,
        sltxr.RECORD_CREATED_TIMESTAMP_UTC,
        sltxr.RECORD_UPDATED_TIMESTAMP_UTC
    FROM $stage_db.merge_scdr_sale_tax_reports sltxr
    LEFT JOIN $stage_db.stg_seller_info si ON sltxr.SELLER_ID = si.SELLER_ID
) source
ON target.PK = source.PK
WHEN MATCHED THEN
    UPDATE SET
        -- metadata fields
        target.REPORT_FETCHED_AND_LOADED_AT = source.REPORT_FETCHED_AND_LOADED_AT,
        target.SCRAPER_ID = source.SCRAPER_ID,
        target.SELLER_ID = source.SELLER_ID,
        target.SELLER_NAME = source.SELLER_NAME,
        target.COUNTRY_CODE = source.COUNTRY_CODE,
        target.MARKETPLACE_ID = source.MARKETPLACE_ID,
        target.MERCHANT_ID = source.MERCHANT_ID,
        -- sale tax report fields
        target.ORDER_ID = source.ORDER_ID,
        target.ORDER_DATE = source.ORDER_DATE,
        target.SHIPMENT_ID = source.SHIPMENT_ID,
        target.SHIPMENT_DATE = source.SHIPMENT_DATE,
        target.TAX_CALCULATED_DATE = source.TAX_CALCULATED_DATE,
        target.POSTED_DATE = source.POSTED_DATE,
        target.MARKETPLACE = source.MARKETPLACE,
        target.FULFILLMENT = source.FULFILLMENT,
        target.ASIN = source.ASIN,
        target.SKU = source.SKU,
        target.TRANSACTION_TYPE = source.TRANSACTION_TYPE,
        target.TAX_COLLECTION_MODEL = source.TAX_COLLECTION_MODEL,
        target.TAX_COLLECTION_RESPONSIBLE_PARTY = source.TAX_COLLECTION_RESPONSIBLE_PARTY,
        target.PRODUCT_TAX_CODE = source.PRODUCT_TAX_CODE,
        target.QUANTITY = source.QUANTITY,
        target.CURRENCY = source.CURRENCY,
        target.BUYER_EXEMPTION_CODE = source.BUYER_EXEMPTION_CODE,
        target.BUYER_EXEMPTION_DOMAIN = source.BUYER_EXEMPTION_DOMAIN,
        target.BUYER_EXEMPTION_CERTIFICATE_ID = source.BUYER_EXEMPTION_CERTIFICATE_ID,
        target.DISPLAY_PRICE = source.DISPLAY_PRICE,
        target.DISPLAY_PRICE_TAX_INCLUSIVE = source.DISPLAY_PRICE_TAX_INCLUSIVE,
        target.TAX_EXCLUSIVE_SELLING_PRICE = source.TAX_EXCLUSIVE_SELLING_PRICE,
        target.TOTAL_TAX = source.TOTAL_TAX,
        target.TOTAL_TAX_COLLECTED_BY_AMAZON = source.TOTAL_TAX_COLLECTED_BY_AMAZON,
        target.FINANCIAL_COMPONENT = source.FINANCIAL_COMPONENT,
        target.SHIP_FROM_CITY = source.SHIP_FROM_CITY,
        target.SHIP_FROM_STATE = source.SHIP_FROM_STATE,
        target.SHIP_FROM_COUNTRY = source.SHIP_FROM_COUNTRY,
        target.SHIP_FROM_POSTAL_CODE = source.SHIP_FROM_POSTAL_CODE,
        target.SHIP_FROM_TAX_LOCATION_CODE = source.SHIP_FROM_TAX_LOCATION_CODE,
        target.SHIP_TO_CITY = source.SHIP_TO_CITY,
        target.SHIP_TO_STATE = source.SHIP_TO_STATE,
        target.SHIP_TO_COUNTRY = source.SHIP_TO_COUNTRY,
        target.SHIP_TO_POSTAL_CODE = source.SHIP_TO_POSTAL_CODE,
        target.SHIP_TO_LOCATION_CODE = source.SHIP_TO_LOCATION_CODE,
        target.TAXED_LOCATION_CODE = source.TAXED_LOCATION_CODE,
        target.TAX_ADDRESS_ROLE = source.TAX_ADDRESS_ROLE,
        target.JURISDICTION_LEVEL = source.JURISDICTION_LEVEL,
        target.JURISDICTION_NAME = source.JURISDICTION_NAME,
        target.DISPLAY_PROMO_AMOUNT = source.DISPLAY_PROMO_AMOUNT,
        target.DISPLAY_PROMO_TAX_INCLUSIVE = source.DISPLAY_PROMO_TAX_INCLUSIVE,
        target.IS_PROMO_APPLIED = source.IS_PROMO_APPLIED,
        target.POST_PROMO_TAXABLE_BASIS = source.POST_PROMO_TAXABLE_BASIS,
        target.PRE_PROMO_TAXABLE_BASIS = source.PRE_PROMO_TAXABLE_BASIS,
        target.PROMO_AMOUNT_BASIS = source.PROMO_AMOUNT_BASIS,
        target.PROMO_ID_DOMAIN = source.PROMO_ID_DOMAIN,
        target.PROMO_AMOUNT_TAX = source.PROMO_AMOUNT_TAX,
        target.PROMOTION_IDENTIFIER = source.PROMOTION_IDENTIFIER,
        target.PROMO_RULE_REASON_CODE = source.PROMO_RULE_REASON_CODE,
        target.PROMO_TAX_PRICE_TYPE = source.PROMO_TAX_PRICE_TYPE,
        target.TAX_AMOUNT = source.TAX_AMOUNT,
        target.TAX_AMOUNT_COLLECTED_BY_AMAZON = source.TAX_AMOUNT_COLLECTED_BY_AMAZON,
        target.TAXED_JURISDICTION_TAX_RATE = source.TAXED_JURISDICTION_TAX_RATE,
        target.TAX_CATEGORY = source.TAX_CATEGORY,
        target.TAX_TYPE = source.TAX_TYPE,
        target.TAX_CALCULATION_REASON_CODE = source.TAX_CALCULATION_REASON_CODE,
        target.NON_TAXABLE_AMOUNT = source.NON_TAXABLE_AMOUNT,
        target.TAXABLE_AMOUNT = source.TAXABLE_AMOUNT,
        -- etl fields
        target.FILE_NAME = source.FILE_NAME,
        target.ETL_BATCH_RUN_TIME = source.ETL_BATCH_RUN_TIME,
        target.DATA_SOURCE = source.DATA_SOURCE,
        target.UPDATED_BY = source.UPDATED_BY,
        target.RECORD_UPDATED_TIMESTAMP_UTC = SYSDATE()
WHEN NOT MATCHED THEN
    INSERT (
        PK,
        -- metadata fields
        REPORT_FETCHED_AND_LOADED_AT,
        SCRAPER_ID,
        SELLER_ID,
        SELLER_NAME,
        COUNTRY_CODE,
        MARKETPLACE_ID,
        MERCHANT_ID,
        -- sale tax report fields
        ORDER_ID,
        ORDER_DATE,
        SHIPMENT_ID,
        SHIPMENT_DATE,
        TAX_CALCULATED_DATE,
        POSTED_DATE,
        MARKETPLACE,
        FULFILLMENT,
        ASIN,
        SKU,
        TRANSACTION_TYPE,
        TAX_COLLECTION_MODEL,
        TAX_COLLECTION_RESPONSIBLE_PARTY,
        PRODUCT_TAX_CODE,
        QUANTITY,
        CURRENCY,
        BUYER_EXEMPTION_CODE,
        BUYER_EXEMPTION_DOMAIN,
        BUYER_EXEMPTION_CERTIFICATE_ID,
        DISPLAY_PRICE,
        DISPLAY_PRICE_TAX_INCLUSIVE,
        TAX_EXCLUSIVE_SELLING_PRICE,
        TOTAL_TAX,
        TOTAL_TAX_COLLECTED_BY_AMAZON,
        FINANCIAL_COMPONENT,
        SHIP_FROM_CITY,
        SHIP_FROM_STATE,
        SHIP_FROM_COUNTRY,
        SHIP_FROM_POSTAL_CODE,
        SHIP_FROM_TAX_LOCATION_CODE,
        SHIP_TO_CITY,
        SHIP_TO_STATE,
        SHIP_TO_COUNTRY,
        SHIP_TO_POSTAL_CODE,
        SHIP_TO_LOCATION_CODE,
        TAXED_LOCATION_CODE,
        TAX_ADDRESS_ROLE,
        JURISDICTION_LEVEL,
        JURISDICTION_NAME,
        DISPLAY_PROMO_AMOUNT,
        DISPLAY_PROMO_TAX_INCLUSIVE,
        IS_PROMO_APPLIED,
        POST_PROMO_TAXABLE_BASIS,
        PRE_PROMO_TAXABLE_BASIS,
        PROMO_AMOUNT_BASIS,
        PROMO_ID_DOMAIN,
        PROMO_AMOUNT_TAX,
        PROMOTION_IDENTIFIER,
        PROMO_RULE_REASON_CODE,
        PROMO_TAX_PRICE_TYPE,
        TAX_AMOUNT,
        TAX_AMOUNT_COLLECTED_BY_AMAZON,
        TAXED_JURISDICTION_TAX_RATE,
        TAX_CATEGORY,
        TAX_TYPE,
        TAX_CALCULATION_REASON_CODE,
        NON_TAXABLE_AMOUNT,
        TAXABLE_AMOUNT,
        -- etl fields
        FILE_NAME,
        ETL_BATCH_RUN_TIME,
        DATA_SOURCE,
        CREATED_BY,
        UPDATED_BY,
        RECORD_CREATED_TIMESTAMP_UTC,
        RECORD_UPDATED_TIMESTAMP_UTC
    )
    VALUES (
        source.PK,
        -- metadata fields
        source.REPORT_FETCHED_AND_LOADED_AT,
        source.SCRAPER_ID,
        source.SELLER_ID,
        source.SELLER_NAME,
        source.COUNTRY_CODE,
        source.MARKETPLACE_ID,
        source.MERCHANT_ID,
        -- sale tax report fields
        source.ORDER_ID,
        source.ORDER_DATE,
        source.SHIPMENT_ID,
        source.SHIPMENT_DATE,
        source.TAX_CALCULATED_DATE,
        source.POSTED_DATE,
        source.MARKETPLACE,
        source.FULFILLMENT,
        source.ASIN,
        source.SKU,
        source.TRANSACTION_TYPE,
        source.TAX_COLLECTION_MODEL,
        source.TAX_COLLECTION_RESPONSIBLE_PARTY,
        source.PRODUCT_TAX_CODE,
        source.QUANTITY,
        source.CURRENCY,
        source.BUYER_EXEMPTION_CODE,
        source.BUYER_EXEMPTION_DOMAIN,
        source.BUYER_EXEMPTION_CERTIFICATE_ID,
        source.DISPLAY_PRICE,
        source.DISPLAY_PRICE_TAX_INCLUSIVE,
        source.TAX_EXCLUSIVE_SELLING_PRICE,
        source.TOTAL_TAX,
        source.TOTAL_TAX_COLLECTED_BY_AMAZON,
        source.FINANCIAL_COMPONENT,
        source.SHIP_FROM_CITY,
        source.SHIP_FROM_STATE,
        source.SHIP_FROM_COUNTRY,
        source.SHIP_FROM_POSTAL_CODE,
        source.SHIP_FROM_TAX_LOCATION_CODE,
        source.SHIP_TO_CITY,
        source.SHIP_TO_STATE,
        source.SHIP_TO_COUNTRY,
        source.SHIP_TO_POSTAL_CODE,
        source.SHIP_TO_LOCATION_CODE,
        source.TAXED_LOCATION_CODE,
        source.TAX_ADDRESS_ROLE,
        source.JURISDICTION_LEVEL,
        source.JURISDICTION_NAME,
        source.DISPLAY_PROMO_AMOUNT,
        source.DISPLAY_PROMO_TAX_INCLUSIVE,
        source.IS_PROMO_APPLIED,
        source.POST_PROMO_TAXABLE_BASIS,
        source.PRE_PROMO_TAXABLE_BASIS,
        source.PROMO_AMOUNT_BASIS,
        source.PROMO_ID_DOMAIN,
        source.PROMO_AMOUNT_TAX,
        source.PROMOTION_IDENTIFIER,
        source.PROMO_RULE_REASON_CODE,
        source.PROMO_TAX_PRICE_TYPE,
        source.TAX_AMOUNT,
        source.TAX_AMOUNT_COLLECTED_BY_AMAZON,
        source.TAXED_JURISDICTION_TAX_RATE,
        source.TAX_CATEGORY,
        source.TAX_TYPE,
        source.TAX_CALCULATION_REASON_CODE,
        source.NON_TAXABLE_AMOUNT,
        source.TAXABLE_AMOUNT,
        -- etl fields
        source.FILE_NAME,
        source.ETL_BATCH_RUN_TIME,
        source.DATA_SOURCE,
        source.CREATED_BY,
        source.UPDATED_BY,
        SYSDATE(),
        SYSDATE()
    );
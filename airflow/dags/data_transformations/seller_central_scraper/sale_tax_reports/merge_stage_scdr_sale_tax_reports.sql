CREATE TABLE IF NOT EXISTS $stage_db.merge_scdr_sale_tax_reports AS
SELECT
    *,
    '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
    '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
FROM $stage_db.dedupe_scdr_sale_tax_reports
WHERE 1 = 0;

MERGE INTO
    $stage_db.merge_scdr_sale_tax_reports AS tgt
USING
    $stage_db.dedupe_scdr_sale_tax_reports AS src
    ON 1 = 1
    AND src.pk = tgt.pk
WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    -- metadata fields
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.scraper_id = src.scraper_id,
    tgt.seller_id = src.seller_id,
    tgt.country = src.country,
    tgt.marketplace_id = src.marketplace_id,
    tgt.merchant_id = src.merchant_id,
    -- sale tax report fields
    tgt.order_id = src.order_id,
    tgt.order_date = src.order_date,
    tgt.shipment_id = src.shipment_id,
    tgt.shipment_date = src.shipment_date,
    tgt.tax_calculated_date = src.tax_calculated_date,
    tgt.posted_date = src.posted_date,
    tgt.marketplace = src.marketplace,
    tgt.fulfillment = src.fulfillment,
    tgt.asin = src.asin,
    tgt.sku = src.sku,
    tgt.transaction_type = src.transaction_type,
    tgt.tax_collection_model = src.tax_collection_model,
    tgt.tax_collection_responsible_party = src.tax_collection_responsible_party,
    tgt.product_tax_code = src.product_tax_code,
    tgt.quantity = src.quantity,
    tgt.currency = src.currency,
    tgt.buyer_exemption_code = src.buyer_exemption_code,
    tgt.buyer_exemption_domain = src.buyer_exemption_domain,
    tgt.buyer_exemption_certificate_id = src.buyer_exemption_certificate_id,
    tgt.display_price = src.display_price,
    tgt.display_price_tax_inclusive = src.display_price_tax_inclusive,
    tgt.tax_exclusive_selling_price = src.tax_exclusive_selling_price,
    tgt.total_tax = src.total_tax,
    tgt.total_tax_collected_by_amazon = src.total_tax_collected_by_amazon,
    tgt.financial_component = src.financial_component,
    tgt.ship_from_city = src.ship_from_city,
    tgt.ship_from_state = src.ship_from_state,
    tgt.ship_from_country = src.ship_from_country,
    tgt.ship_from_postal_code = src.ship_from_postal_code,
    tgt.ship_from_tax_location_code = src.ship_from_tax_location_code,
    tgt.ship_to_city = src.ship_to_city,
    tgt.ship_to_state = src.ship_to_state,
    tgt.ship_to_country = src.ship_to_country,
    tgt.ship_to_postal_code = src.ship_to_postal_code,
    tgt.ship_to_location_code = src.ship_to_location_code,
    tgt.taxed_location_code = src.taxed_location_code,
    tgt.tax_address_role = src.tax_address_role,
    tgt.jurisdiction_level = src.jurisdiction_level,
    tgt.jurisdiction_name = src.jurisdiction_name,
    tgt.display_promo_amount = src.display_promo_amount,
    tgt.display_promo_tax_inclusive = src.display_promo_tax_inclusive,
    tgt.is_promo_applied = src.is_promo_applied,
    tgt.post_promo_taxable_basis = src.post_promo_taxable_basis,
    tgt.pre_promo_taxable_basis = src.pre_promo_taxable_basis,
    tgt.promo_amount_basis = src.promo_amount_basis,
    tgt.promo_id_domain = src.promo_id_domain,
    tgt.promo_amount_tax = src.promo_amount_tax,
    tgt.promotion_identifier = src.promotion_identifier,
    tgt.promo_rule_reason_code = src.promo_rule_reason_code,
    tgt.promo_tax_price_type = src.promo_tax_price_type,
    tgt.tax_amount = src.tax_amount,
    tgt.tax_amount_collected_by_amazon = src.tax_amount_collected_by_amazon,
    tgt.taxed_jurisdiction_tax_rate = src.taxed_jurisdiction_tax_rate,
    tgt.tax_category = src.tax_category,
    tgt.tax_type = src.tax_type,
    tgt.tax_calculation_reason_code = src.tax_calculation_reason_code,
    tgt.non_taxable_amount = src.non_taxable_amount,
    tgt.taxable_amount = src.taxable_amount,
    -- etl fields
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    -- metadata fields
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    marketplace_id,
    merchant_id,
    -- sale tax report fields
    order_id,
    order_date,
    shipment_id,
    shipment_date,
    tax_calculated_date,
    posted_date,
    marketplace,
    fulfillment,
    asin,
    sku,
    transaction_type,
    tax_collection_model,
    tax_collection_responsible_party,
    product_tax_code,
    quantity,
    currency,
    buyer_exemption_code,
    buyer_exemption_domain,
    buyer_exemption_certificate_id,
    display_price,
    display_price_tax_inclusive,
    tax_exclusive_selling_price,
    total_tax,
    total_tax_collected_by_amazon,
    financial_component,
    ship_from_city,
    ship_from_state,
    ship_from_country,
    ship_from_postal_code,
    ship_from_tax_location_code,
    ship_to_city,
    ship_to_state,
    ship_to_country,
    ship_to_postal_code,
    ship_to_location_code,
    taxed_location_code,
    tax_address_role,
    jurisdiction_level,
    jurisdiction_name,
    display_promo_amount,
    display_promo_tax_inclusive,
    is_promo_applied,
    post_promo_taxable_basis,
    pre_promo_taxable_basis,
    promo_amount_basis,
    promo_id_domain,
    promo_amount_tax,
    promotion_identifier,
    promo_rule_reason_code,
    promo_tax_price_type,
    tax_amount,
    tax_amount_collected_by_amazon,
    taxed_jurisdiction_tax_rate,
    tax_category,
    tax_type,
    tax_calculation_reason_code,
    non_taxable_amount,
    taxable_amount,
    -- etl fields
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES (
    src.pk,
    -- metadata fields
    src.report_fetched_and_loaded_at,
    src.scraper_id,
    src.seller_id,
    src.country,
    src.marketplace_id,
    src.merchant_id,
    -- sale tax report fields
    src.order_id,
    src.order_date,
    src.shipment_id,
    src.shipment_date,
    src.tax_calculated_date,
    src.posted_date,
    src.marketplace,
    src.fulfillment,
    src.asin,
    src.sku,
    src.transaction_type,
    src.tax_collection_model,
    src.tax_collection_responsible_party,
    src.product_tax_code,
    src.quantity,
    src.currency,
    src.buyer_exemption_code,
    src.buyer_exemption_domain,
    src.buyer_exemption_certificate_id,
    src.display_price,
    src.display_price_tax_inclusive,
    src.tax_exclusive_selling_price,
    src.total_tax,
    src.total_tax_collected_by_amazon,
    src.financial_component,
    src.ship_from_city,
    src.ship_from_state,
    src.ship_from_country,
    src.ship_from_postal_code,
    src.ship_from_tax_location_code,
    src.ship_to_city,
    src.ship_to_state,
    src.ship_to_country,
    src.ship_to_postal_code,
    src.ship_to_location_code,
    src.taxed_location_code,
    src.tax_address_role,
    src.jurisdiction_level,
    src.jurisdiction_name,
    src.display_promo_amount,
    src.display_promo_tax_inclusive,
    src.is_promo_applied,
    src.post_promo_taxable_basis,
    src.pre_promo_taxable_basis,
    src.promo_amount_basis,
    src.promo_id_domain,
    src.promo_amount_tax,
    src.promotion_identifier,
    src.promo_rule_reason_code,
    src.promo_tax_price_type,
    src.tax_amount,
    src.tax_amount_collected_by_amazon,
    src.taxed_jurisdiction_tax_rate,
    src.tax_category,
    src.tax_type,
    src.tax_calculation_reason_code,
    src.non_taxable_amount,
    src.taxable_amount,
    -- etl fields
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);
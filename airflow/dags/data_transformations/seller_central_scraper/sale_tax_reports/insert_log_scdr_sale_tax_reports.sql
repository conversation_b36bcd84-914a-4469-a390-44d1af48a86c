CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_scdr_sale_tax_reports AS
    SELECT
         *
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_scdr_sale_tax_reports
    WHERE 1 = 0;

INSERT INTO $raw_db.log_scdr_sale_tax_reports (
    -- metadata fields
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    marketplace_id,
    -- sale tax report fields
    order_id,
    order_date,
    shipment_id,
    shipment_date,
    tax_calculated_date,
    posted_date,
    marketplace,
    merchant_id,
    fulfillment,
    asin,
    sku,
    transaction_type,
    tax_collection_model,
    tax_collection_responsible_party,
    product_tax_code,
    quantity,
    currency,
    buyer_exemption_code,
    buyer_exemption_domain,
    buyer_exemption_certificate_id,
    display_price,
    display_price_tax_inclusive,
    tax_exclusive_selling_price,
    total_tax,
    total_tax_collected_by_amazon,
    financial_component,
    ship_from_city,
    ship_from_state,
    ship_from_country,
    ship_from_postal_code,
    ship_from_tax_location_code,
    ship_to_city,
    ship_to_state,
    ship_to_country,
    ship_to_postal_code,
    ship_to_location_code,
    taxed_location_code,
    tax_address_role,
    jurisdiction_level,
    jurisdiction_name,
    display_promo_amount,
    display_promo_tax_inclusive,
    is_promo_applied,
    post_promo_taxable_basis,
    pre_promo_taxable_basis,
    promo_amount_basis,
    promo_id_domain,
    promo_amount_tax,
    promotion_identifier,
    promo_rule_reason_code,
    promo_tax_price_type,
    tax_amount,
    tax_amount_collected_by_amazon,
    taxed_jurisdiction_tax_rate,
    tax_category,
    tax_type,
    tax_calculation_reason_code,
    non_taxable_amount,
    taxable_amount,
    -- etl fields
    file_name,
    etl_batch_run_time,
    log_timestamp_utc
)
SELECT
    -- metadata fields
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    marketplace_id,
    -- sale tax report fields
    order_id,
    order_date,
    shipment_id,
    shipment_date,
    tax_calculated_date,
    posted_date,
    marketplace,
    merchant_id,
    fulfillment,
    asin,
    sku,
    transaction_type,
    tax_collection_model,
    tax_collection_responsible_party,
    product_tax_code,
    quantity,
    currency,
    buyer_exemption_code,
    buyer_exemption_domain,
    buyer_exemption_certificate_id,
    display_price,
    display_price_tax_inclusive,
    tax_exclusive_selling_price,
    total_tax,
    total_tax_collected_by_amazon,
    financial_component,
    ship_from_city,
    ship_from_state,
    ship_from_country,
    ship_from_postal_code,
    ship_from_tax_location_code,
    ship_to_city,
    ship_to_state,
    ship_to_country,
    ship_to_postal_code,
    ship_to_location_code,
    taxed_location_code,
    tax_address_role,
    jurisdiction_level,
    jurisdiction_name,
    display_promo_amount,
    display_promo_tax_inclusive,
    is_promo_applied,
    post_promo_taxable_basis,
    pre_promo_taxable_basis,
    promo_amount_basis,
    promo_id_domain,
    promo_amount_tax,
    promotion_identifier,
    promo_rule_reason_code,
    promo_tax_price_type,
    tax_amount,
    tax_amount_collected_by_amazon,
    taxed_jurisdiction_tax_rate,
    tax_category,
    tax_type,
    tax_calculation_reason_code,
    non_taxable_amount,
    taxable_amount,
    -- etl fields
    file_name,
    etl_batch_run_time,
    SYSDATE() AS log_timestamp_utc
FROM
    $raw_db.raw_scdr_sale_tax_reports;

CREATE TABLE IF NOT EXISTS $stage_db.merge_scdr_outlet_deals_eligibility AS
    SELECT

        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_scdr_outlet_deals_eligibility
    WHERE 1 = 0;


MERGE INTO
    $stage_db.merge_scdr_outlet_deals_eligibility AS tgt
USING
    $stage_db.dedupe_scdr_outlet_deals_eligibility AS src

        ON 1 = 1
       AND src.pk = tgt.pk

WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt.report_fetched_and_loaded_at = src.report_fetched_and_loaded_at,
    tgt.scraper_id = src.scraper_id,
    tgt.seller_id = src.seller_id,
    tgt.country = src.country,
    tgt.asin = src.asin,
    tgt.msku = src.msku,
    tgt.recommended_price_amount = src.recommended_price_amount,
    tgt.recommended_price_currency = src.recommended_price_currency,
    tgt.start_date = src.start_date,
    tgt.end_date = src.end_date,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    asin,
    msku,
    recommended_price_amount,
    recommended_price_currency,
    start_date,
    end_date,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src.report_fetched_and_loaded_at,
    src.scraper_id,
    src.seller_id,
    src.country,
    src.asin,
    src.msku,
    src.recommended_price_amount,
    src.recommended_price_currency,
    src.start_date,
    src.end_date,
    src.file_name,
    src.etl_batch_run_time,
    SYSDATE(),
    SYSDATE()
);
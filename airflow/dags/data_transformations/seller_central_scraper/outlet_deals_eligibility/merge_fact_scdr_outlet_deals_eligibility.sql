CREATE TABLE IF NOT EXISTS $curated_db.fact_scdr_outlet_deals_eligibility (
	PK VARCHAR(32),
	REPORT_FETCHED_AND_LOADED_AT VARCHAR,
	SCRAPER_ID VARCHAR,
	SELLER_ID VARCHAR,
    SELLER_NAME VARCHAR,
	COUNTRY_CODE VARCHAR,
    ASIN VARCHAR,
    MSKU VARCHAR,
    RECOMMENDED_PRICE_AMOUNT NUMBER(38,2),
    RECOMMENDED_PRICE_CURRENCY VARCHAR,
    START_DATE NUMBER(38,0),
    END_DATE NUMBER(38,0),
    FILE_NAME VARCHAR,
	DATA_SOURCE VARCHAR,
	CREATED_BY VARCHAR DEFAULT 'DAG: fact_scdr_outlet_deals_eligibility',
	UPDATED_BY VARCHAR DEFAULT 'DAG: fact_scdr_outlet_deals_eligibility',
	RECORD_CREATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE(),
	RECORD_UPDATED_TIMESTAMP_UTC TIMESTAMP DEFAULT SYSDATE()
);

MERGE INTO $curated_db.fact_scdr_outlet_deals_eligibility AS tgt
USING (
    SELECT
        ps.PK,
        ps.REPORT_FETCHED_AND_LOADED_AT,
        ps.SCRAPER_ID,
        ps.SELLER_ID,
        si.SELLER_NAME,
        ps.COUNTRY AS COUNTRY_CODE,
        ps.ASIN,
        ps.MSKU,
        ps.RECOMMENDED_PRICE_AMOUNT,
        ps.RECOMMENDED_PRICE_CURRENCY,
        ps.START_DATE,
        ps.END_DATE,
        ps.FILE_NAME,
        'dwh.staging.merge_scdr_outlet_deals_eligibility' AS DATA_SOURCE,
        'DAG: fact_scdr_outlet_deals_eligibility' AS CREATED_BY,
        'DAG: fact_scdr_outlet_deals_eligibility' AS UPDATED_BY,
        ps.RECORD_CREATED_TIMESTAMP_UTC,
        ps.RECORD_UPDATED_TIMESTAMP_UTC
    FROM $stage_db.merge_scdr_outlet_deals_eligibility ps
    LEFT JOIN $stage_db.stg_seller_info si ON ps.SELLER_ID = si.SELLER_ID
) AS src
ON tgt.pk = src.pk
WHEN MATCHED
    AND src.RECORD_UPDATED_TIMESTAMP_UTC > tgt.RECORD_UPDATED_TIMESTAMP_UTC
THEN UPDATE SET
    tgt.REPORT_FETCHED_AND_LOADED_AT = src.REPORT_FETCHED_AND_LOADED_AT,
    tgt.SCRAPER_ID = src.SCRAPER_ID,
    tgt.SELLER_ID = src.SELLER_ID,
    tgt.SELLER_NAME = src.SELLER_NAME,
    tgt.COUNTRY_CODE = src.COUNTRY_CODE,
    tgt.ASIN = src.ASIN,
    tgt.MSKU = src.MSKU,
    tgt.RECOMMENDED_PRICE_AMOUNT = src.RECOMMENDED_PRICE_AMOUNT,
    tgt.RECOMMENDED_PRICE_CURRENCY = src.RECOMMENDED_PRICE_CURRENCY,
    tgt.START_DATE = src.START_DATE,
    tgt.END_DATE = src.END_DATE,
    tgt.FILE_NAME = src.FILE_NAME,
    tgt.DATA_SOURCE = src.DATA_SOURCE,
    tgt.UPDATED_BY = src.UPDATED_BY,
    tgt.RECORD_UPDATED_TIMESTAMP_UTC = SYSDATE()
WHEN NOT MATCHED THEN INSERT (
    PK,
    REPORT_FETCHED_AND_LOADED_AT,
    SCRAPER_ID,
    SELLER_ID,
    SELLER_NAME,
    COUNTRY_CODE,
    ASIN,
    MSKU,
    RECOMMENDED_PRICE_AMOUNT,
    RECOMMENDED_PRICE_CURRENCY,
    START_DATE,
    END_DATE,
    FILE_NAME,
    DATA_SOURCE,
    CREATED_BY,
    UPDATED_BY,
    RECORD_CREATED_TIMESTAMP_UTC,
    RECORD_UPDATED_TIMESTAMP_UTC
)
VALUES (
    src.PK,
    src.REPORT_FETCHED_AND_LOADED_AT,
    src.SCRAPER_ID,
    src.SELLER_ID,
    src.SELLER_NAME,
    src.COUNTRY_CODE,
    src.ASIN,
    src.MSKU,
    src.RECOMMENDED_PRICE_AMOUNT,
    src.RECOMMENDED_PRICE_CURRENCY,
    src.START_DATE,
    src.END_DATE,
    src.FILE_NAME,
    src.DATA_SOURCE,
    src.CREATED_BY,
    src.UPDATED_BY,
    SYSDATE(),
    SYSDATE()
);
CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_scdr_outlet_deals_eligibility AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(seller_id AS VARCHAR), ''), '-',
            COALESCE(CAST(country AS VARCHAR), ''), '-',
            COALESCE(CAST(asin AS VARCHAR), ''), '-',
            COALESCE(CAST(msku AS VARCHAR), ''), '-',
            COALESCE(CAST(report_fetched_and_loaded_at::date AS VARCHAR), '')
            )) AS pk,
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        country,
        asin,
        msku,
        TRY_TO_NUMBER(recommended_price_amount) as recommended_price_amount,
        recommended_price_currency,
        TRY_TO_NUMBER(start_date) as start_date,
        TRY_TO_NUMBER(end_date) as end_date,
        file_name,
        etl_batch_run_time

    FROM $raw_db.raw_scdr_outlet_deals_eligibility
    WHERE 1=1
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY pk
        ORDER BY report_fetched_and_loaded_at DESC NULLS LAST) = 1
);
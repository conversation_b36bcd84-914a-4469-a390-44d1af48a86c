CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_scdr_outlet_deals_eligibility AS
    SELECT
         *
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_scdr_outlet_deals_eligibility
    WHERE 1 = 0;

INSERT INTO $raw_db.log_scdr_outlet_deals_eligibility (
    report_fetched_and_loaded_at,
    scraper_id,
    seller_id,
    country,
    asin,
    msku,
    recommended_price_amount,
    recommended_price_currency,
    start_date,
    end_date,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc
)
    SELECT
        report_fetched_and_loaded_at,
        scraper_id,
        seller_id,
        country,
        asin,
        msku,
        recommended_price_amount,
        recommended_price_currency,
        start_date,
        end_date,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM
    $raw_db.raw_scdr_outlet_deals_eligibility;
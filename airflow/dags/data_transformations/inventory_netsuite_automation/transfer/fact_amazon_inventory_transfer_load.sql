CREATE OR R<PERSON>LACE TEMPORARY TABLE $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_TEMP AS (
WITH amz_rec AS (SELECT DATE as posting_date
                      , REPLACE(COUNTRY, 'GB', 'UK')  as country_code
                      , SELLER_ID
                      , REFERENCE_ID
                      , EVENT_TYPE
                      , trim(SKU, ' ') as SKU
                      , SUM(QUANTITY)                    AS quantity
                 FROM DWH.STAGING.AMAZON_LEDGER_DETAILED_INVENTORY_V2
                 WHERE (DATE >= TO_TIMESTAMP_NTZ('$start_ts')::date - 1
                    OR DATE IN (
                            SELECT DISTINCT posting_date
                            FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR
                            WHERE posted_flag IN ('not ready to post', 'ready to post')
                        )
                 )
                 AND EVENT_TYPE = 'Receipts'
                 GROUP BY 1, 2, 3, 4, 5, 6
                )
   , daily_inventory_transfers_data AS (
        SELECT CASE
                WHEN t.quantity <= 0 THEN m.from_location_qty_leq_zero
                ELSE m.from_location_qty_geq_zero
            END AS from_location_name
            , CASE
                WHEN t.quantity <= 0 THEN m.to_location_qty_leq_zero
                ELSE m.to_location_qty_geq_zero
            END AS to_location_name
            , t.*
            FROM amz_rec as t
            LEFT JOIN DWH.RAW.NETSUITE_INVENTORY_TRANSFER_LOCATION_MAPPING as m
            ON t.seller_id = m.seller AND t.country_code = m.country
)
, location_mapping as (
    SELECT
        lm.*,
        ncf.location_int_id AS from_location_internal_id,
        nct.location_int_id AS to_location_internal_id
    FROM daily_inventory_transfers_data AS lm
    LEFT JOIN netsuite.netsuite.netsuite_locations as ncf on upper(ncf.location_name)=upper(lm.from_location_name)
    LEFT JOIN netsuite.netsuite.netsuite_locations as nct on upper(nct.location_name)=upper(lm.to_location_name)
)
, metadata_mappings as (
    select td.*,
        ssm.netsuite_subsidiary,
        ssm.netsuite_subsidiary_internal_id,
        ssm.amazon_seller_account_name as seller_name,
        ssm.marketplace as geography,
        ng.geography_int_id as geography_id
    from location_mapping td
    left join DWH.RAW.NRA_AMAZON_SELLER_SUBSIDIARY_MAPPING ssm on td.seller_id=ssm.seller_id and td.country_code=ssm.country_code and ssm.fulfillment_type='FBA'
    left join netsuite.netsuite.netsuite_geographies ng on lower(ssm.marketplace)=lower(ng.geography_name) or lower(ssm.marketplace)=lower(ng.geography_iso_code)
)
, sku_mapping as (
select ltm.*,
    coalesce(skum.brand, am.brand) as brand,
    coalesce(skum.class_id, am.class_id) as class_id,
    coalesce(skum.netsuite_item_type, am.netsuite_item_type) as netsuite_item_type,
    coalesce(skum.netsuite_item_number, am.netsuite_item_number) as ns_item_number,
    coalesce(skum.item_id, am.item_id) as item_id
from metadata_mappings ltm
left join netsuite.netsuite.netsuite_postings_sku_mapping skum on ltm.sku=skum.sku
left join netsuite.netsuite.netsuite_postings_asin_as_sku_mapping as am on ltm.sku=am.sku and ltm.country_code=am.country_code
)
, final_data as (
SELECT  md5(CAST(
                 COALESCE(CAST(s.posting_date 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.REFERENCE_ID 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.seller_id			 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.country_code 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.event_type 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.sku 		 	 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.from_location_name 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.to_location_name 		 	 AS varchar), '') AS varchar
            )) 								 AS inventory_transfer_pk
        ,s.posting_date
        ,s.reference_id
        ,s.seller_id
        ,s.seller_name
        ,s.class_id as netsuite_brand_id
        ,s.brand
        ,s.geography
        ,s.geography_id
        ,s.country_code
        ,s.from_location_name
        ,s.from_location_internal_id
        ,s.to_location_name
        ,s.to_location_internal_id
        ,s.netsuite_item_type as item_type
        ,s.ns_item_number as netsuite_item_number
        ,s.item_id as netsuite_id
        ,concat(s.netsuite_item_type, ' : ', coalesce(s.ns_item_number, '')) as item
        ,s.netsuite_subsidiary_internal_id
        ,s.netsuite_subsidiary
        ,md5(CAST(COALESCE(CAST(s.posting_date	                    AS varchar), '') || '-' ||
                  COALESCE(CAST(s.country_code		            AS varchar), '') || '-' ||
                  COALESCE(CAST(s.seller_id		            AS varchar), '') || '-' ||
                  COALESCE(CAST(s.reference_id		            AS varchar), '') || '-' ||
                  COALESCE(CAST(s.class_id		            AS varchar), '') || '-' ||
                  COALESCE(CAST(s.netsuite_subsidiary_internal_id     AS varchar), '') || '-' ||
                  COALESCE(CAST(s.from_location_internal_id     AS varchar), '') || '-' ||
                  COALESCE(CAST(s.to_location_internal_id   AS varchar), '')
                AS varchar)) 								        AS netsuite_inventory_transfer_post_key
        ,md5(CAST(COALESCE(CAST(s.posting_date	            AS varchar), '') || '-' ||
                    COALESCE(CAST(s.country_code 		        AS varchar), '') || '-' ||
                    COALESCE(CAST(s.seller_id 	        AS varchar), '') || '-' ||
                    COALESCE(CAST(s.reference_id 	        AS varchar), '') || '-' ||
                    COALESCE(CAST(s.class_id 	        AS varchar), '') || '-' ||
                    COALESCE(CAST(s.netsuite_subsidiary_internal_id AS varchar), '') || '-' ||
                    COALESCE(CAST(s.from_location_internal_id AS varchar), '') || '-' ||
                    COALESCE(CAST(s.to_location_internal_id AS varchar), '') || '-' ||
                    COALESCE(CAST(coalesce(netsuite_item_number, '') 		    AS varchar), '')
                AS varchar)) 								        AS inventory_transfer_key
        ,NULL as netsuite_inventory_transfer_id
        ,NULL as netsuite_inventory_transfer_document_number
        ,s.event_type
        ,s.sku
        ,s.quantity
        ,NULL as posted_flag
        ,SYSDATE()        			AS etl_batch_runtime
        ,SYSDATE()        			AS record_created_timestamp_utc
        ,NULL 						AS record_updated_timestamp_utc
        ,NULL 						AS inventory_transfer_post_updated_timestamp_utc
FROM sku_mapping s
)
select f.*, coalesce(p.rw, -1) as rw, coalesce(p.group_id, -1) as group_id
from final_data as f
left join (
select
    distinct
    netsuite_inventory_transfer_post_key,
    inventory_transfer_key,
    dense_rank() over (partition by netsuite_inventory_transfer_post_key order by inventory_transfer_key) as rw,
    floor(rw/400)+1 as group_id
from final_data
where quantity <> 0
) as p on p.netsuite_inventory_transfer_post_key=f.netsuite_inventory_transfer_post_key
and p.inventory_transfer_key=f.inventory_transfer_key
);

CREATE OR REPLACE TEMPORARY TABLE $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_GROUPING_TEMP AS (
with inventory_transfer_posted_group_info as (
       select
            distinct netsuite_inventory_transfer_post_key,
            inventory_transfer_key,
            CAST(split_part(uuid, '_', 2) AS int) as group_id,
       from $curated_db.fact_amazon_inventory_transfer_essor_aggregate
       where posting_status IS NOT NULL
)
, newly_available_records as (
       select a.*
       from $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_TEMP a
       left join inventory_transfer_posted_group_info b
       on a.netsuite_inventory_transfer_post_key=b.netsuite_inventory_transfer_post_key
       and a.inventory_transfer_key=b.inventory_transfer_key
       and a.group_id=b.group_id
       where b.netsuite_inventory_transfer_post_key is null
)
select f.* EXCLUDE(rw, group_id)
       , coalesce(p.rw, -1) as rw
       , coalesce(p.group_id + coalesce(pa.max_group_id, 0), -1) as group_id
       from newly_available_records as f
left join (
   select
       distinct
       netsuite_inventory_transfer_post_key,
       inventory_transfer_key,
       dense_rank() over (partition by netsuite_inventory_transfer_post_key order by inventory_transfer_key) as rw,
       floor(rw/400)+1 as group_id
   from newly_available_records
   where quantity <> 0
) as p on p.netsuite_inventory_transfer_post_key=f.netsuite_inventory_transfer_post_key
and p.inventory_transfer_key=f.inventory_transfer_key
left join (
   select
       netsuite_inventory_transfer_post_key, max(group_id) as max_group_id
   from inventory_transfer_posted_group_info
   group by all
) pa on pa.netsuite_inventory_transfer_post_key=f.netsuite_inventory_transfer_post_key
);

--assign new group id for newly records/new updated mapping records found in the inventory table
UPDATE $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_TEMP tgt
SET rw = src.rw, group_id = src.group_id
FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_GROUPING_TEMP src
WHERE tgt.inventory_transfer_pk=src.inventory_transfer_pk;


--assign flags for all the rows based on the mapping data
UPDATE $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_TEMP  
SET posted_flag = CASE WHEN quantity = 0
                            THEN 'do not post'
                            WHEN netsuite_id IS NULL OR netsuite_item_number IS NULL OR netsuite_subsidiary_internal_id IS NULL OR from_location_internal_id IS NULL OR geography_id is NULL OR to_location_internal_id IS NULL
                            THEN 'not ready to post' 
                            ELSE 'ready to post'
                    END;

BEGIN TRANSACTION;

--insert only when record is not already present 
INSERT INTO $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR (
    INVENTORY_TRANSFER_PK,
    POSTING_DATE,
    REFERENCE_ID,
    SELLER_ID,
    SELLER_NAME,
    NETSUITE_BRAND_ID,
    BRAND,
    GEOGRAPHY,
    GEOGRAPHY_ID,
    COUNTRY_CODE,
    FROM_LOCATION_NAME,
    FROM_LOCATION_INTERNAL_ID,
    TO_LOCATION_NAME,
    TO_LOCATION_INTERNAL_ID,
    ITEM_TYPE,
    NETSUITE_ITEM_NUMBER,
    NETSUITE_ID,
    ITEM,
    NETSUITE_SUBSIDIARY_INTERNAL_ID,
    NETSUITE_SUBSIDIARY,
    NETSUITE_INVENTORY_TRANSFER_POST_KEY,
    INVENTORY_TRANSFER_KEY,
    NETSUITE_INVENTORY_TRANSFER_ID,
    NETSUITE_INVENTORY_TRANSFER_DOCUMENT_NUMBER,
    EVENT_TYPE,
    SKU,
    QUANTITY,
    POSTED_FLAG,
    ETL_BATCH_RUNTIME,
    RECORD_CREATED_TIMESTAMP_UTC,
    RECORD_UPDATED_TIMESTAMP_UTC,
    INVENTORY_TRANSFER_POST_UPDATED_TIMESTAMP_UTC,
    RW,
    GROUP_ID
)
SELECT s.* 
FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_TEMP s
LEFT JOIN $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR tgt
ON s.inventory_transfer_pk = tgt.inventory_transfer_pk 
WHERE tgt.inventory_transfer_pk IS NULL; 

--update only when the mapping or amounts have changed 
UPDATE $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR S
SET  from_location_name                    = T.from_location_name
    ,from_location_internal_id            = T.from_location_internal_id
    ,to_location_name                     = T.to_location_name
    ,to_location_internal_id              = T.to_location_internal_id
    ,netsuite_subsidiary_internal_id      = T.netsuite_subsidiary_internal_id
    ,netsuite_subsidiary                  = T.netsuite_subsidiary
    ,seller_name                          = T.seller_name
    ,netsuite_item_number                 = T.netsuite_item_number
    ,netsuite_id                          = T.netsuite_id                       
    ,item                                 = T.item
    ,netsuite_inventory_transfer_post_key = T.netsuite_inventory_transfer_post_key
    ,inventory_transfer_key               = T.inventory_transfer_key
    ,quantity                             = T.quantity
    ,posted_flag						  = T.posted_flag
    ,netsuite_brand_id					  = T.netsuite_brand_id
    ,brand								  = T.brand
    ,geography							  = T.geography
    ,geography_id						  = T.geography_id
    , group_id                            = T.group_id
    , rw                                  = T.rw
    ,record_updated_timestamp_utc         = SYSDATE()
FROM (
    SELECT T.*
    FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_TEMP T
    JOIN $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR S
        ON S.inventory_transfer_pk  = T.inventory_transfer_pk
    LEFT JOIN  $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_AGGREGATE A
		ON S.inventory_transfer_key = A.inventory_transfer_key
    WHERE   S.posted_flag IN ('ready to post','not ready to post','do not post','DISPUTED_TRANSACTION')
        AND A.posting_status IS NULL --avoid updates if inventory transfers are in the middle of posting
        AND(
            COALESCE(S.netsuite_subsidiary_internal_id,0) != COALESCE(T.netsuite_subsidiary_internal_id,0)  OR
            COALESCE(S.from_location_internal_id,0) != COALESCE(T.from_location_internal_id,0) OR
            COALESCE(S.to_location_internal_id,0) != COALESCE(T.to_location_internal_id,0) OR
            COALESCE(S.geography_id,0) != COALESCE(T.geography_id,0) OR
            COALESCE(S.netsuite_item_number,'')		  != COALESCE(T.netsuite_item_number,'')  		 OR 
            COALESCE(S.brand,'')		  				  != COALESCE(T.brand,'')  		 				 OR
            COALESCE(S.netsuite_brand_id,0)		  	  != COALESCE(T.netsuite_brand_id,0)  		 	 OR
            COALESCE(S.quantity,0)					  != COALESCE(T.quantity,0) OR 
            COALESCE(S.posted_flag,'')		  		  != COALESCE(T.posted_flag,'')  
        )
    ) T
WHERE S.inventory_transfer_pk  = T.inventory_transfer_pk;

--this update is to get the additional mapping updates that are just used for reference and do not impact netsuite posting. this could happen after a transaction is posted and we do not want to reprocess
UPDATE $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR S
SET  netsuite_subsidiary        = T.netsuite_subsidiary
    ,from_location_name         = T.from_location_name
    ,to_location_name           = T.to_location_name
    ,seller_name                = T.seller_name
    ,geography                  = T.geography
    ,netsuite_item_number       = T.netsuite_item_number
FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_TEMP T
WHERE  S.inventory_transfer_pk  = T.inventory_transfer_pk
AND   (
        COALESCE(S.netsuite_subsidiary,'') 		!= COALESCE(T.netsuite_subsidiary,'')  		OR
        COALESCE(S.from_location_name,'') 		!= COALESCE(T.from_location_name,'')  		OR
        COALESCE(S.to_location_name,'') 		!= COALESCE(T.to_location_name,'')  		OR
        COALESCE(S.seller_name,'') 		        != COALESCE(T.seller_name,'')  		        OR
        COALESCE(S.geography,'') 		        != COALESCE(T.geography,'')  		        OR
        COALESCE(S.netsuite_id,0)		  		!= COALESCE(T.netsuite_id,0)
        );


-- Update flag for late arriving transactions
CREATE OR REPLACE TEMPORARY TABLE $stage_db.netsuite_essor_amazon_disputed_inventory_transfers AS
SELECT  aggr.netsuite_inventory_transfer_post_key,
        aggr.inventory_transfer_key,
        aggr.posting_status
FROM (
    SELECT  netsuite_inventory_transfer_post_key,
            inventory_transfer_key,
            posting_status,
            MIN(CAST(split_part(uuid, '_', 2) AS int)) as group_id,
            NVL(SUM(quantity), 0) AS tot_quantity
   	FROM $curated_db.fact_amazon_inventory_transfer_essor_aggregate
   	WHERE posting_date >= DATEADD(DAY, -61, TO_DATE(CURRENT_TIMESTAMP()))
   	GROUP BY netsuite_inventory_transfer_post_key, inventory_transfer_key, posting_status
) aggr
LEFT JOIN (
	SELECT	netsuite_inventory_transfer_post_key,
	        inventory_transfer_key,
	        MIN(group_id) AS group_id,
            NVL(SUM(quantity), 0) AS tot_quantity
   	FROM $curated_db.fact_amazon_inventory_transfer_essor
	WHERE posted_flag <> 'do not post'
		AND posting_date >= DATEADD(DAY, -61, TO_DATE(CURRENT_TIMESTAMP()))
		AND posting_date <= current_date() - 3
	GROUP BY netsuite_inventory_transfer_post_key, inventory_transfer_key
) sl ON aggr.netsuite_inventory_transfer_post_key = sl.netsuite_inventory_transfer_post_key
    AND aggr.inventory_transfer_key = sl.inventory_transfer_key
WHERE TO_NUMERIC(COALESCE(aggr.tot_quantity, 0), 18, 2) != TO_NUMERIC(COALESCE(sl.tot_quantity, 0), 18, 2)
OR TO_NUMERIC(COALESCE(aggr.group_id, 0), 18, 2) != TO_NUMERIC(COALESCE(sl.group_id, 0), 18, 2);


UPDATE $curated_db.fact_amazon_inventory_transfer_essor
SET posted_flag = 'DISPUTED_TRANSACTION',
    record_updated_timestamp_utc = SYSDATE()
WHERE netsuite_inventory_transfer_post_key IN (
        SELECT DISTINCT netsuite_inventory_transfer_post_key
        FROM $stage_db.netsuite_essor_amazon_disputed_inventory_transfers
        WHERE UPPER(posting_status) IN ('POSTED', 'MANUAL_POST','SUBMITTED_TO_NETSUITE')
    )
    AND LOWER(posted_flag) != 'do not post';

DROP TABLE $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_TEMP;
DROP TABLE $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_GROUPING_TEMP;
DROP TABLE $stage_db.netsuite_essor_amazon_disputed_inventory_transfers;

COMMIT;

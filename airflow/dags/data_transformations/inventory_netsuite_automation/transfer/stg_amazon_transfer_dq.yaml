---
test_id: "fact_amazon_inventory_transfer_dup_check"
enabled: true
query: |
  WITH amz_rec AS
  (SELECT DATE AS posting_date ,
          COUNTRY AS country_code ,
          SELLER_ID ,
          REFERENCE_ID ,
          EVENT_TYPE ,
          SKU ,
          SUM(QUANTITY) AS quantity
   FROM DWH.STAGING.AMAZON_LEDGER_DETAILED_INVENTORY_V2
   WHERE DATE >= DATEADD(DAY, -5, TO_DATE(CURRENT_TIMESTAMP()))
     AND EVENT_TYPE = 'Receipts'
   GROUP BY 1,
            2,
            3,
            4,
            5,
            6) ,
     daily_inventory_transfers_data AS
  (SELECT CASE
              WHEN t.quantity <= 0 THEN m.from_location_qty_leq_zero
              ELSE m.from_location_qty_geq_zero
          END AS from_location_name ,
          CASE
              WHEN t.quantity <= 0 THEN m.to_location_qty_leq_zero
              ELSE m.to_location_qty_geq_zero
          END AS to_location_name ,
          t.*
   FROM amz_rec AS t
   LEFT JOIN DWH.RAW.NETSUITE_INVENTORY_TRANSFER_LOCATION_MAPPING AS m ON t.seller_id = m.seller
   AND t.country_code = m.country),
     location_mapping AS
  (SELECT lm.*,
          ncf.location_int_id AS from_location_internal_id,
          nct.location_int_id AS to_location_internal_id
   FROM daily_inventory_transfers_data AS lm
   LEFT JOIN netsuite.netsuite.netsuite_locations AS ncf ON upper(ncf.location_name)=upper(lm.from_location_name)
   LEFT JOIN netsuite.netsuite.netsuite_locations AS nct ON upper(nct.location_name)=upper(lm.to_location_name)),
     metadata_mappings AS
  (SELECT td.*,
          ssm.netsuite_subsidiary,
          ssm.netsuite_subsidiary_internal_id,
          ssm.amazon_seller_account_name AS seller_name,
          ssm.marketplace AS geography,
          ng.geography_int_id AS geography_id
   FROM location_mapping td
   LEFT JOIN DWH.RAW.NRA_AMAZON_SELLER_SUBSIDIARY_MAPPING ssm ON td.seller_id=ssm.seller_id
   AND td.country_code=ssm.country_code
   AND ssm.fulfillment_type='FBA'
   LEFT JOIN netsuite.netsuite.netsuite_geographies ng ON lower(ssm.marketplace)=lower(ng.geography_name)
   OR lower(ssm.marketplace)=lower(ng.geography_iso_code)),
     sku_mapping AS
  (SELECT ltm.*,
          coalesce(skum.brand, am.brand) AS brand,
          coalesce(skum.class_id, am.class_id) AS class_id,
          coalesce(skum.netsuite_item_type, am.netsuite_item_type) AS netsuite_item_type,
          coalesce(skum.netsuite_item_number, am.netsuite_item_number) AS ns_item_number,
          coalesce(skum.item_id, am.item_id) AS item_id
   FROM metadata_mappings ltm
   LEFT JOIN netsuite.netsuite.netsuite_postings_sku_mapping skum ON ltm.sku=skum.sku
   LEFT JOIN netsuite.netsuite.netsuite_postings_asin_as_sku_mapping AS am ON ltm.sku=am.sku
   AND ltm.country_code=am.country_code),
  a as  (
  select count(*) as cnt
  from daily_inventory_transfers_data a
  ), 
  b as (select count(*) as cnt from metadata_mappings b
  ),
  c as (
  select count(*) as cnt from location_mapping c
  ),
  d as ( select count(*) as cnt from 
  sku_mapping d )
  select 
  (case when a.cnt = b.cnt
  and a.cnt = c.cnt
  and a.cnt = d.cnt
  then 0 else 2 end) as "result"
  from a 
  join b on 1=1
  join c on 1=1
  join d on 1=1




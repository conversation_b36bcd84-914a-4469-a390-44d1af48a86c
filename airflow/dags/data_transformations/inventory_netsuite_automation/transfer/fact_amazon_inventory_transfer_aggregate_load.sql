BEGIN TRANSACTION;

SET min_posting_date = (
    SELECT MIN(posting_date) AS posting_date
    FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR
    WHERE record_updated_timestamp_utc >= TO_TIMESTAMP_NTZ('$start_ts')
    OR record_created_timestamp_utc  >= TO_TIMESTAMP_NTZ('$start_ts')
    OR posting_date >= current_date() - 3
);

--SLA for updates is 60 days so the incremental is set to that number
CREATE OR REPLACE TEMPORARY TABLE $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_AGGREGATE_TEMP AS
WITH exclude_inventory_transfer AS (
    --exclude all rma and sale orders if any transaction within that rma and sale order is not ready to post
    SELECT DISTINCT netsuite_inventory_transfer_post_key, group_id
    FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR
    WHERE 	lower(posted_flag)  IN ('not ready to post','posted','manual_post')
)
SELECT (select class_int_id from netsuite.netsuite.netsuite_classes where lower(class_name) = 'multiple brands' )  as class
    , (select SALES_CHANNEL_INT_ID from netsuite.netsuite.netsuite_sales_channels where lower(SALES_CHANNEL_CODE) = 'amazon') as sales_channel_id
    , 12 as department_id
    , split_part(S.item, ' : ', 1) as item_type
    , CONCAT( 'Amazontransfer_', S.posting_date, '_', S.seller_id, '_', S.geography, '_', S.reference_id, '_', S.brand, '_', S.from_location_name, '_', S.to_location_name, '_', S.group_id) AS external_id
    , S.seller_id
    , S.reference_id
    , S.posting_date
    , S.geography
    , S.geography_id
    , S.item
    , S.netsuite_id
    , S.netsuite_item_number
    , S.brand
    , S.netsuite_brand_id
    , S.netsuite_subsidiary_internal_id as subsidiary_internal_id
    , S.netsuite_subsidiary as netsuite_subsidiary_name
    , S.from_location_internal_id
    , S.from_location_name
    , S.to_location_internal_id
    , S.to_location_name
    , S.netsuite_inventory_transfer_post_key
    , concat(S.netsuite_inventory_transfer_post_key, '_', S.group_id) as uuid
    , S.inventory_transfer_key
    , SUM(S.quantity) as quantity
    , SYSDATE()          			AS record_created_timestamp_utc
    , NULL 						AS record_updated_timestamp_utc
FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR S
LEFT JOIN exclude_inventory_transfer C
    ON S.netsuite_inventory_transfer_post_key = C.netsuite_inventory_transfer_post_key
    AND S.group_id = C.group_id
WHERE 	S.posted_flag  <> 'do not post'
AND posting_date >= $min_posting_date
AND posting_date <= current_date() - 3 -- T-3 day lag for posting
AND C.netsuite_inventory_transfer_post_key IS NULL
GROUP BY  class
        , sales_channel_id
        , department_id
        , S.item
        , S.posting_date
        , S.seller_id
        , S.reference_id
        , S.geography
        , S.geography_id
        , S.netsuite_id
        , S.netsuite_item_number
        , S.brand
        , S.netsuite_brand_id
        , S.netsuite_subsidiary_internal_id
        , S.netsuite_subsidiary
        , S.from_location_internal_id
        , S.from_location_name
        , S.to_location_internal_id
        , S.to_location_name
        , S.netsuite_inventory_transfer_post_key
        , S.group_id
        , S.inventory_transfer_key;

--avoid updating any records that are in the middle of posting
UPDATE $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_AGGREGATE A
SET  A.quantity	   					= T.quantity
    ,A.netsuite_subsidiary_name			= T.netsuite_subsidiary_name
    ,A.from_location_name				= T.from_location_name
    ,A.to_location_name				= T.to_location_name
    ,A.geography				= T.geography
    ,A.netsuite_id					= T.netsuite_id
    ,A.record_updated_timestamp_utc  	= SYSDATE()
FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_AGGREGATE_TEMP T
WHERE 	A.inventory_transfer_key 		 				 =  T.inventory_transfer_key
    AND  A.posting_status IS NULL
    AND	(A.quantity	  						    <> T.quantity  							OR
            coalesce(A.netsuite_subsidiary_name,'') <> coalesce(T.netsuite_subsidiary_name,'')  OR
            coalesce(A.from_location_name,'') 	    <> coalesce(T.from_location_name,'')  		OR
            coalesce(A.to_location_name,'') 	    <> coalesce(T.to_location_name,'')  		OR
            coalesce(A.geography,'') 	    <> coalesce(T.geography,'')  		OR
            coalesce(A.netsuite_item_number,'')		  	    <> coalesce(T.netsuite_item_number,'')
        );

-- Delete disputed transaction from the aggregate table
DELETE FROM $curated_db.fact_amazon_inventory_transfer_essor_aggregate
WHERE netsuite_inventory_transfer_post_key IN (
            SELECT DISTINCT netsuite_inventory_transfer_post_key
            FROM $curated_db.fact_amazon_inventory_transfer_essor
            WHERE posted_flag = 'DISPUTED_TRANSACTION'
                AND posting_date >= $min_posting_date
       );

--insert only when inventory_transfer_key does not exist in the aggr table	
INSERT INTO $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_AGGREGATE (
class, sales_channel_id, department_id, item_type, external_id, seller_id, reference_id, posting_date, geography,
geography_id, item, netsuite_id, netsuite_item_number, brand, netsuite_brand_id, subsidiary_internal_id,
netsuite_subsidiary_name, from_location_internal_id, from_location_name, to_location_internal_id, to_location_name,
netsuite_inventory_transfer_post_key, uuid, inventory_transfer_key, quantity, record_created_timestamp_utc,
record_updated_timestamp_utc
)
SELECT s.*
FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_AGGREGATE_TEMP s
LEFT JOIN $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_AGGREGATE a
    ON s.inventory_transfer_key = a.inventory_transfer_key 
WHERE  a.inventory_transfer_key IS NULL;


-- Update posting status for disputed rma and sale orders
UPDATE $curated_db.fact_amazon_inventory_transfer_essor_aggregate
SET posting_status = 'DISPUTED_TRANSACTION',
    record_updated_timestamp_utc = SYSDATE()
WHERE netsuite_inventory_transfer_post_key IN (
            SELECT DISTINCT netsuite_inventory_transfer_post_key
            FROM $curated_db.fact_amazon_inventory_transfer_essor
            WHERE posted_flag = 'DISPUTED_TRANSACTION'
            AND posting_date >= $min_posting_date
       );


/*remove any records that are present in the aggregate tables but not in inventory_transfer. This case could happen when an incorrect mapping information is entered for records and updated later. 
The inventory_transfer_key would change in that case and the old one will no longer be valid for posting */
DELETE 
FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_AGGREGATE S
USING (
    SELECT S.* 
    FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_AGGREGATE S
    LEFT JOIN  $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR T
    on S.inventory_transfer_key = T.inventory_transfer_key 
    WHERE  T.inventory_transfer_key IS NULL
    AND S.posting_status IS NULL)  c
WHERE S.inventory_transfer_key = c.inventory_transfer_key ;

/*edge case: when a transaction is marked from 'ready to post' to 'do not post' or 'not ready to post' , remove the entire rma and sale order from aggr table to prevent it from posting.
the rma and sale order will get added back automatically after all the transactions for it are ready to post again*/
DELETE 
FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR_AGGREGATE S
USING (
    SELECT DISTINCT netsuite_inventory_transfer_post_key
    FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR
    WHERE 	posted_flag  = 'not ready to post'
) T
WHERE S.netsuite_inventory_transfer_post_key = T.netsuite_inventory_transfer_post_key
AND S.posting_status IS NULL;

COMMIT;

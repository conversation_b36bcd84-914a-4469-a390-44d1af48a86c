---
test_id: "fact_amazon_inventory_transfer_dup_check"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS "result" FROM 
  (
  SELECT inventory_transfer_pk 
  FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR  
  GROUP BY 1
  HAVING COUNT(1) >1 
  LIMIT 1
  )

---
test_id: "amazon_inventory_transfer_and_shipment_comparison"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS "result"
  FROM 
  (
  SELECT 
    COALESCE(stg.seller_id, fact.seller_id) AS seller_id,
    stg.tot_quantity  AS stg_tot_quantity,
    fact.tot_quantity AS fact_tot_quantity
  FROM 
  (
    SELECT 	s.seller_id ,sum(s.quantity) AS tot_quantity
    FROM (
      SELECT DATE as posting_date
          , REPLACE(COUNTRY, 'GB', 'UK')  as country_code
          , SELLER_ID
          , REFERENCE_ID
          , EVENT_TYPE
          , SKU
          , SUM(QUANTITY)                    AS quantity
          FROM DWH.STAGING.AMAZON_LEDGER_DETAILED_INVENTORY_V2 INV            
        WHERE 1=1 and DATE >= DATEADD(Day ,-50, current_date)
        AND EVENT_TYPE = 'Receipts'
        GROUP BY 1, 2, 3, 4, 5, 6
      ) s
    LEFT JOIN (
      (SELECT * FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR WHERE posted_flag ='not ready to post')) f
      ON  to_date(s.posting_date) = f.posting_date 		
      AND s.seller_id = f.seller_id 			
      AND s.reference_id = f.reference_id 			
      AND s.country_code 	= f.country_code
      AND COALESCE(s.event_type,'')	= COALESCE(f.event_type,'')	 		
      AND COALESCE(s.sku,'') = COALESCE(f.sku,'')
    WHERE   f.seller_id IS NULL
    GROUP BY s.seller_id	
  )stg
  full outer join 
  (
    SELECT sum(quantity) tot_quantity,seller_id 
    FROM  $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR 
    WHERE 1=1 and posting_date >= DATEADD(Day ,-50, current_date)
    AND posted_flag <> 'not ready to post'
    GROUP BY seller_id	
  )fact
    ON stg.seller_id = fact.seller_id
  where abs(to_numeric(coalesce(stg.tot_quantity,0),18,2) - to_numeric(coalesce(fact.tot_quantity,0),18,2)) > 0.01
  limit 1
  ) T

---
test_id: "additional_transactions_on_posted_inventory_transfers"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
    SELECT   DISTINCT netsuite_inventory_transfer_post_key
    FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR
    WHERE lower(posted_flag) <> 'do not post' 
    GROUP BY netsuite_inventory_transfer_post_key
    HAVING COUNT(DISTINCT CASE WHEN lower(posted_flag) = 'manual_post' THEN 'posted' 
                              WHEN lower(posted_flag) IN  ('ready to post','not ready to post') THEN 'not posted' 
                  ELSE lower(posted_flag) 
                END) > 1
    LIMIT 1
  )

---
test_id: "amazon_inventory_transfer_missing_netsuite_brand_id"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT 1
   FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR 
   WHERE netsuite_brand_id IS NULL 
   AND posted_flag = 'not ready to post'
   LIMIT 1
  )

---
test_id: "amazon_inventory_transfer_missing_netsuite_metadata_mapping_id"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT DISTINCT brand
   FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR 
   WHERE (netsuite_subsidiary_internal_id IS NULL 
   OR geography_id IS NULL 
   OR from_location_internal_id IS NULL 
   OR to_location_internal_id IS NULL)
   AND posted_flag = 'not ready to post'
   LIMIT 1
  )

---
test_id: "amazon_inventory_transfer_missing_netsuite_item_number"
enabled: false
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
   SELECT DISTINCT sku
   FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR 
   WHERE netsuite_item_number IS NULL 
   AND posted_flag = 'not ready to post'
   LIMIT 1
  )

---
test_id: "amazon_inventory_transfer_quantity_on_do_not_post_entries"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" 
  FROM 
  (
    SELECT 1
    FROM $curated_db.FACT_AMAZON_INVENTORY_TRANSFER_ESSOR 
    WHERE LOWER(posted_flag) = 'do not post'
    AND quantity <> 0
    AND posting_date >= DATEADD(D,-30,current_date)
    LIMIT 1
  );

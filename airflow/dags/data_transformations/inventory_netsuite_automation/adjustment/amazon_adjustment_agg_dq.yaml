---
test_id: "amazon_inventory_adjustment_aggregate_dup_check"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS "result" FROM 
  (
  SELECT inventory_adjustment_key FROM $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR_AGGREGATE
  GROUP BY 1
  HAVING COUNT(1) >1 
  LIMIT 1
  )

---
test_id: "amazon_inventory_adjustment_and_aggregate_comparison"
enabled: true
query: |
  SELECT CASE WHEN COUNT(1)=1 THEN 2 ELSE 0 END AS "result"
  FROM (
      SELECT  COALESCE(aggr.seller_id, sl.seller_id) AS seller_id,
              aggr.tot_quantity  AS stg_tot_quantity,
              sl.tot_quantity AS fact_tot_quantity
      FROM (
          SELECT  S.seller_id,
                  IFNULL(SUM(S.quantity),0) AS tot_quantity
          FROM $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR S
          LEFT JOIN (
              SELECT DISTINCT netsuite_inventory_adjustment_post_key
              FROM $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR
              WHERE posted_flag = 'not ready to post'
          ) C ON S.netsuite_inventory_adjustment_post_key = C.netsuite_inventory_adjustment_post_key
          LEFT JOIN (
              SELECT DISTINCT split_part(uuid, '_', 1) as netsuite_inventory_adjustment_post_key, split_part(uuid, '_', -1) as group_id
              FROM $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR_AGGREGATE
              WHERE UPPER(posting_status) IN ('PROCESSING', 'SUBMITTED_TO_NETSUITE')
          ) A ON S.netsuite_inventory_adjustment_post_key = A.netsuite_inventory_adjustment_post_key AND S.group_id=A.group_id
      WHERE S.posted_flag NOT IN ('do not post')
          AND posting_date >= DATEADD(DAY,-50,TO_DATE(CURRENT_TIMESTAMP()))
          AND posting_date <= current_date() - 3
          AND C.netsuite_inventory_adjustment_post_key IS NULL
          AND A.netsuite_inventory_adjustment_post_key IS NULL
      GROUP BY S.seller_id
      ) aggr
      FULL OUTER JOIN (
          SELECT seller_id ,sum(quantity) tot_quantity
      FROM $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR_AGGREGATE
      WHERE posting_date >= DATEADD(DAY,-50,TO_DATE(CURRENT_TIMESTAMP()))
          AND (posting_status IS NULL OR UPPER(posting_status) IN ('POSTED', 'MANUAL_POST', 'DISPUTED_TRANSACTION'))
      GROUP BY seller_id
      ) sl
          ON aggr.seller_id = sl.seller_id
    WHERE to_numeric(coalesce(aggr.tot_quantity,0),18,2) <> to_numeric(coalesce(sl.tot_quantity,0),18,2)
    LIMIT 1
  ) T
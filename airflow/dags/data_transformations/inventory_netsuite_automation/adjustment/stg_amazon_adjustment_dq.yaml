---
test_id: "fact_amazon_subledger_dup_check"
enabled: true
query: |
  WITH daily_inventory_adjustments_data AS
  (SELECT EVENT_TYPE,
          SELLER_ID,
          CASE
              WHEN EVENT_TYPE = 'Adjustments' THEN '518000 Cost Of Goods Sold : AMZ Inventory Adjustments'
              WHEN EVENT_TYPE = 'VendorReturns' THEN '518010 Cost Of Goods Sold : AMZ Returns Disposals (Vendor Returns)'
              WHEN EVENT_TYPE = 'CustomerReturns' THEN '518020 Cost Of Goods Sold : Refunds and Customer Returns'
              ELSE NULL
          END AS ACCOUNT_NAME,
          CASE
              WHEN EVENT_TYPE = 'Adjustments' THEN 549
              WHEN EVENT_TYPE = 'VendorReturns' THEN 2822
              WHEN EVENT_TYPE = 'CustomerReturns' THEN 2823
              ELSE NULL
          END AS ACCOUNT_INTERNAL_ID,
          DATE AS posting_date,
          SKU,
          COUNTRY AS country_code,
          BRAND,
          sum(QUANTITY) AS QUANTITY
   FROM DWH.STAGING.AMAZON_LEDGER_DETAILED_INVENTORY_V2 INV
   WHERE DATE >= DATEADD(DAY,-5,TO_DATE(CURRENT_TIMESTAMP()))  
     AND EVENT_TYPE IN ('Adjustments',
                        'CustomerReturns',
                        'VendorReturns')
   GROUP BY 1,
            2,
            3,
            4,
            5,
            6,
            7,
            8),
     metadata_mappings AS
  (SELECT td.*,
          ssm.netsuite_subsidiary,
          ssm.netsuite_subsidiary_internal_id,
          ssm.netsuite_location,
          ssm.netsuite_location_internal_id,
          ssm.amazon_seller_account_name AS seller_name,
          ssm.sales_channel,
          ssm.marketplace AS geography,
          ng.geography_int_id AS geography_id
   FROM daily_inventory_adjustments_data td
   LEFT JOIN DWH.RAW.NRA_AMAZON_SELLER_SUBSIDIARY_MAPPING ssm ON td.seller_id=ssm.seller_id
   AND td.country_code=ssm.country_code
   AND ssm.fulfillment_type='FBA'
   LEFT JOIN netsuite.netsuite.netsuite_geographies ng ON lower(ssm.marketplace)=lower(ng.geography_name)
   OR lower(ssm.marketplace)=lower(ng.geography_iso_code)),
     sku_mapping AS
  (SELECT ltm.*,
          coalesce(skum.brand, am.brand) AS item_brand,
          coalesce(skum.class_id, am.class_id) AS class_id,
          coalesce(skum.netsuite_item_type, am.netsuite_item_type) AS netsuite_item_type,
          coalesce(skum.netsuite_item_number, am.netsuite_item_number) AS ns_item_number,
          coalesce(skum.item_id, am.item_id) AS item_id
   FROM metadata_mappings ltm
   LEFT JOIN netsuite.netsuite.netsuite_postings_sku_mapping skum ON ltm.sku=skum.sku
   LEFT JOIN netsuite.netsuite.netsuite_postings_asin_as_sku_mapping AS am ON ltm.sku=am.sku
   AND ltm.country_code=am.country_code),
  a as  (
  select count(*) as cnt
  from daily_inventory_adjustments_data a
  ), 
  b as (select count(*) as cnt from metadata_mappings b
  ),
  c as ( select count(*) as cnt from 
  sku_mapping d )
  select 
  (case when a.cnt = b.cnt
  and a.cnt = c.cnt
  then 0 else 2 end) as "result"
  from a 
  join b on 1=1
  join c on 1=1




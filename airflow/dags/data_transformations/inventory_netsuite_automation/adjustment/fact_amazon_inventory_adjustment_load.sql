CREATE OR REPLACE TEMPORARY TABLE $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR_TEMP AS (
with daily_inventory_adjustments_data as (
SELECT      EVENT_TYPE,
            SELLER_ID,
            CASE
                WHEN EVENT_TYPE = 'Adjustments'
                    THEN '518000 Cost Of Goods Sold : AMZ Inventory Adjustments'
                WHEN EVENT_TYPE = 'VendorReturns'
                    THEN '518010 Cost Of Goods Sold : AMZ Returns Disposals (Vendor Returns)'
                WHEN EVENT_TYPE = 'CustomerReturns'
                    THEN '518020 Cost Of Goods Sold : Refunds and Customer Returns'
                ELSE NULL
                END       AS                                             ACCOUNT_NAME,
            CASE
                WHEN EVENT_TYPE = 'Adjustments'
                    THEN 549
                WHEN EVENT_TYPE = 'VendorReturns'
                    THEN 2822
                WHEN EVENT_TYPE = 'CustomerReturns'
                    THEN 2823
                ELSE NULL
                END       AS                                             ACCOUNT_INTERNAL_ID,
            DATE as posting_date,
            trim(SKU, ' ') as SKU,
            REPLACE(COUNTRY, 'GB', 'UK')  as country_code,
            BRAND,
            sum(QUANTITY) AS                                             QUANTITY
     FROM DWH.STAGING.AMAZON_LEDGER_DETAILED_INVENTORY_V2 INV
      where (DATE >= TO_TIMESTAMP_NTZ('$start_ts')::date - 1
        OR DATE IN (
                SELECT DISTINCT posting_date
                FROM $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR
                WHERE posted_flag IN ('not ready to post', 'ready to post')
            )
        )
       AND EVENT_TYPE in ('Adjustments', 'CustomerReturns', 'VendorReturns')
     GROUP BY 1, 2, 3, 4, 5, 6, 7, 8
)
, metadata_mappings as (
    select td.*,
        ssm.netsuite_subsidiary,
        ssm.netsuite_subsidiary_internal_id,
        ssm.netsuite_location,
        ssm.netsuite_location_internal_id,
        ssm.amazon_seller_account_name as seller_name,
        ssm.sales_channel,
        ssm.marketplace as geography,
        ng.geography_int_id as geography_id
    from daily_inventory_adjustments_data td
    left join DWH.RAW.NRA_AMAZON_SELLER_SUBSIDIARY_MAPPING ssm on td.seller_id=ssm.seller_id and td.country_code=ssm.country_code and ssm.fulfillment_type='FBA'
    left join netsuite.netsuite.netsuite_geographies ng on lower(ssm.marketplace)=lower(ng.geography_name) or lower(ssm.marketplace)=lower(ng.geography_iso_code)
)
, sku_mapping as (
select ltm.*,
    coalesce(skum.brand, am.brand) as item_brand,
    coalesce(skum.class_id, am.class_id) as class_id,
    coalesce(skum.netsuite_item_type, am.netsuite_item_type) as netsuite_item_type,
    coalesce(skum.netsuite_item_number, am.netsuite_item_number) as ns_item_number,
    coalesce(skum.item_id, am.item_id) as item_id
from metadata_mappings ltm
left join netsuite.netsuite.netsuite_postings_sku_mapping skum on ltm.sku=skum.sku
left join netsuite.netsuite.netsuite_postings_asin_as_sku_mapping as am on ltm.sku=am.sku and ltm.country_code=am.country_code
)
, final_data as (
SELECT  md5(CAST(
                 COALESCE(CAST(s.posting_date 		 	 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.seller_id			 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.country_code 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.event_type 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.account_name 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.account_internal_id 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.sku 		 	 		 AS varchar), '') || '-' ||
                 COALESCE(CAST(s.brand 		 	 AS varchar), '') AS varchar
            )) 								 AS inventory_adjustment_pk
        ,s.posting_date
        ,s.seller_id
        ,s.seller_name
        ,s.class_id as netsuite_brand_id
        ,s.brand
        ,s.item_brand
        ,s.geography
        ,s.geography_id
        ,s.country_code
        ,s.account_name
        ,s.account_internal_id
        ,s.netsuite_location
        ,s.netsuite_location_internal_id
        ,s.netsuite_item_type as item_type
        ,s.ns_item_number as netsuite_item_number
        ,s.item_id as netsuite_id
        ,concat(s.netsuite_item_type, ' : ', coalesce(s.ns_item_number, '')) as item
        ,s.netsuite_subsidiary_internal_id
        ,s.netsuite_subsidiary
        ,md5(CAST(COALESCE(CAST(s.posting_date	                    AS varchar), '') || '-' ||
                  COALESCE(CAST(s.country_code		            AS varchar), '') || '-' ||
                  COALESCE(CAST(s.seller_id		            AS varchar), '') || '-' ||
                  COALESCE(CAST(s.event_type		            AS varchar), '') || '-' ||
                  COALESCE(CAST(s.netsuite_subsidiary_internal_id     AS varchar), '') || '-' ||
                  COALESCE(CAST(s.netsuite_location_internal_id   AS varchar), '')
                AS varchar)) 								        AS netsuite_inventory_adjustment_post_key
        ,md5(CAST(COALESCE(CAST(s.posting_date	            AS varchar), '') || '-' ||
                    COALESCE(CAST(s.country_code 		        AS varchar), '') || '-' ||
                    COALESCE(CAST(s.seller_id 	        AS varchar), '') || '-' ||
                    COALESCE(CAST(s.event_type		            AS varchar), '') || '-' ||
                    COALESCE(CAST(s.class_id 	        AS varchar), '') || '-' ||
                    COALESCE(CAST(s.netsuite_subsidiary_internal_id AS varchar), '') || '-' ||
                    COALESCE(CAST(s.netsuite_location_internal_id AS varchar), '') || '-' ||
                    COALESCE(CAST(coalesce(netsuite_item_number, '') 		    AS varchar), '')
                AS varchar)) 								        AS inventory_adjustment_key
        ,NULL as netsuite_inventory_adjustment_id
        ,NULL as netsuite_inventory_adjustment_document_number
        ,s.event_type
        ,s.sku
        ,s.quantity
        ,NULL as posted_flag
        ,SYSDATE()        			AS etl_batch_runtime
        ,SYSDATE()        			AS record_created_timestamp_utc
        ,NULL 						AS record_updated_timestamp_utc
        ,NULL 						AS inventory_adjustment_post_updated_timestamp_utc
FROM sku_mapping s
)
select f.*, coalesce(p.rw, -1) as rw, coalesce(p.group_id, -1) as group_id
from final_data as f
left join (
select
    distinct
    netsuite_inventory_adjustment_post_key,
    inventory_adjustment_key,
    dense_rank() over (partition by netsuite_inventory_adjustment_post_key order by inventory_adjustment_key) as rw,
    floor(rw/400)+1 as group_id
from final_data
where quantity <> 0
) as p on p.netsuite_inventory_adjustment_post_key=f.netsuite_inventory_adjustment_post_key
and p.inventory_adjustment_key=f.inventory_adjustment_key
);
       
CREATE OR REPLACE TEMPORARY TABLE $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_GROUPING_TEMP AS (
with inventory_adjustment_posted_group_info as (
       select
            distinct netsuite_inventory_adjustment_post_key,
            inventory_adjustment_key,
            CAST(split_part(uuid, '_', 2) AS int) as group_id,
       from $curated_db.fact_amazon_inventory_adjustment_essor_aggregate
       where posting_status IS NOT NULL
)
, newly_available_records as (
       select a.*
       from $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR_TEMP a
       left join inventory_adjustment_posted_group_info b
       on a.netsuite_inventory_adjustment_post_key=b.netsuite_inventory_adjustment_post_key
       and a.inventory_adjustment_key=b.inventory_adjustment_key
       and a.group_id=b.group_id
       where b.netsuite_inventory_adjustment_post_key is null
)
select f.* EXCLUDE(rw, group_id)
       , coalesce(p.rw, -1) as rw
       , coalesce(p.group_id + coalesce(pa.max_group_id, 0), -1) as group_id
       from newly_available_records as f
left join (
   select
       distinct
       netsuite_inventory_adjustment_post_key,
       inventory_adjustment_key,
       dense_rank() over (partition by netsuite_inventory_adjustment_post_key order by inventory_adjustment_key) as rw,
       floor(rw/400)+1 as group_id
   from newly_available_records
   where quantity <> 0
) as p on p.netsuite_inventory_adjustment_post_key=f.netsuite_inventory_adjustment_post_key
and p.inventory_adjustment_key=f.inventory_adjustment_key
left join (
   select
       netsuite_inventory_adjustment_post_key, max(group_id) as max_group_id
   from inventory_adjustment_posted_group_info
   group by all
) pa on pa.netsuite_inventory_adjustment_post_key=f.netsuite_inventory_adjustment_post_key
);

--assign new group id for newly records/new updated mapping records found in the inventory table
UPDATE $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR_TEMP tgt
SET rw = src.rw, group_id = src.group_id
FROM $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_GROUPING_TEMP src
WHERE tgt.inventory_adjustment_pk=src.inventory_adjustment_pk;



--assign flags for all the rows based on the mapping data
UPDATE $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR_TEMP
SET posted_flag = CASE
                      WHEN quantity = 0
                          THEN 'do not post'
                      WHEN netsuite_id IS NULL OR netsuite_item_number IS NULL OR netsuite_subsidiary_internal_id IS NULL OR netsuite_location_internal_id IS NULL OR geography_id is NULL
                          THEN 'not ready to post'
                      ELSE 'ready to post'
    END;

BEGIN TRANSACTION;

--insert only when record is not already present
INSERT INTO $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR (INVENTORY_ADJUSTMENT_PK,
                                                                POSTING_DATE,
                                                                SELLER_ID,
                                                                SELLER_NAME,
                                                                NETSUITE_BRAND_ID,
                                                                BRAND,
                                                                ITEM_BRAND,
                                                                GEOGRAPHY,
                                                                GEOGRAPHY_ID,
                                                                COUNTRY_CODE,
                                                                ACCOUNT_NAME,
                                                                ACCOUNT_INTERNAL_ID,
                                                                NETSUITE_LOCATION,
                                                                NETSUITE_LOCATION_INTERNAL_ID,
                                                                ITEM_TYPE,
                                                                NETSUITE_ITEM_NUMBER,
                                                                NETSUITE_ID,
                                                                ITEM,
                                                                NETSUITE_SUBSIDIARY_INTERNAL_ID,
                                                                NETSUITE_SUBSIDIARY,
                                                                NETSUITE_INVENTORY_ADJUSTMENT_POST_KEY,
                                                                INVENTORY_ADJUSTMENT_KEY,
                                                                NETSUITE_INVENTORY_ADJUSTMENT_ID,
                                                                NETSUITE_INVENTORY_ADJUSTMENT_DOCUMENT_NUMBER,
                                                                EVENT_TYPE,
                                                                SKU,
                                                                QUANTITY,
                                                                POSTED_FLAG,
                                                                ETL_BATCH_RUNTIME,
                                                                RECORD_CREATED_TIMESTAMP_UTC,
                                                                RECORD_UPDATED_TIMESTAMP_UTC,
                                                                INVENTORY_ADJUSTMENT_POST_UPDATED_TIMESTAMP_UTC,
                                                                RW,
                                                                GROUP_ID)
SELECT s.*
FROM $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR_TEMP s
         LEFT JOIN $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR tgt
                   ON s.inventory_adjustment_pk = tgt.inventory_adjustment_pk
WHERE tgt.inventory_adjustment_pk IS NULL;

--update only when the mapping or amounts have changed
UPDATE $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR S
SET netsuite_location                      = T.netsuite_location
  , netsuite_location_internal_id          = T.netsuite_location_internal_id
  , netsuite_subsidiary_internal_id        = T.netsuite_subsidiary_internal_id
  , netsuite_subsidiary                    = T.netsuite_subsidiary
  , account_name                           = T.account_name
  , seller_name                            = T.seller_name
  , account_internal_id                    = T.account_internal_id
  , netsuite_item_number                   = T.netsuite_item_number
  , netsuite_id                            = T.netsuite_id
  , item                                   = T.item
  , netsuite_inventory_adjustment_post_key = T.netsuite_inventory_adjustment_post_key
  , inventory_adjustment_key               = T.inventory_adjustment_key
  , quantity                               = T.quantity
  , posted_flag                            = T.posted_flag
  , netsuite_brand_id                      = T.netsuite_brand_id
  , brand                                  = T.brand
  , item_brand                             = T.item_brand
  , geography                              = T.geography
  , geography_id                           = T.geography_id
  , group_id                               = T.group_id
  , rw                                     = T.rw
  , record_updated_timestamp_utc           = SYSDATE() FROM (
    SELECT T.*
    FROM $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR_TEMP T
    JOIN $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR S
        ON S.inventory_adjustment_pk  = T.inventory_adjustment_pk
    LEFT JOIN  $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR_AGGREGATE A
		ON S.inventory_adjustment_key = A.inventory_adjustment_key
    WHERE   S.posted_flag IN ('ready to post','not ready to post','do not post','DISPUTED_TRANSACTION')
        AND A.posting_status IS NULL --avoid updates if inventory adjustment are in the middle of posting
        AND(
            COALESCE(S.netsuite_subsidiary_internal_id,0) != COALESCE(T.netsuite_subsidiary_internal_id,0)  OR
            COALESCE(S.netsuite_location_internal_id,0) != COALESCE(T.netsuite_location_internal_id,0) OR
            COALESCE(S.account_internal_id,0) != COALESCE(T.account_internal_id,0) OR
            COALESCE(S.geography_id,0) != COALESCE(T.geography_id,0) OR
            COALESCE(S.netsuite_item_number,'')		  != COALESCE(T.netsuite_item_number,'')  		 OR
            COALESCE(S.brand,'')		  		      != COALESCE(T.brand,'')  		 				 OR
            COALESCE(S.netsuite_brand_id,0)		  	  != COALESCE(T.netsuite_brand_id,0)  		 	 OR
            COALESCE(S.quantity,0)					  != COALESCE(T.quantity,0) OR
            COALESCE(S.posted_flag,'')		  		  != COALESCE(T.posted_flag,'')
        )
    ) T
WHERE S.inventory_adjustment_pk = T.inventory_adjustment_pk;

--this update is to get the additional mapping updates that are just used for reference and do not impact netsuite posting. this could happen after a transaction is posted and we do not want to reprocess
UPDATE $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR S
SET netsuite_subsidiary  = T.netsuite_subsidiary
  , netsuite_location    = T.netsuite_location
  , account_name         = T.account_name
  , seller_name          = T.seller_name
  , geography            = T.geography
  , netsuite_item_number = T.netsuite_item_number FROM $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR_TEMP T
WHERE S.inventory_adjustment_pk = T.inventory_adjustment_pk
  AND (
    COALESCE (S.netsuite_subsidiary, '') != COALESCE (T.netsuite_subsidiary, '')
   OR
    COALESCE (S.netsuite_location, '') != COALESCE (T.netsuite_location, '')
   OR
    COALESCE (S.account_name, '') != COALESCE (T.account_name, '')
   OR
    COALESCE (S.seller_name, '') != COALESCE (T.seller_name, '')
   OR
    COALESCE (S.geography, '') != COALESCE (T.geography, '')
   OR
    COALESCE (S.netsuite_id, 0) != COALESCE (T.netsuite_id, 0)
    );


-- Update flag for late arriving transactions
CREATE
OR REPLACE TEMPORARY TABLE $stage_db.netsuite_essor_amazon_disputed_inventory_adjustments AS
SELECT aggr.netsuite_inventory_adjustment_post_key,
       aggr.inventory_adjustment_key,
       aggr.posting_status
FROM (SELECT netsuite_inventory_adjustment_post_key,
             inventory_adjustment_key,
             posting_status,
             MIN(CAST(split_part(uuid, '_', 2) AS int)) as group_id,
             NVL(SUM(quantity), 0) AS tot_quantity
      FROM $curated_db.fact_amazon_inventory_adjustment_essor_aggregate
      WHERE posting_date >= DATEADD(DAY, -61, TO_DATE(CURRENT_TIMESTAMP()))
      GROUP BY netsuite_inventory_adjustment_post_key, inventory_adjustment_key, posting_status) aggr
         LEFT JOIN (SELECT netsuite_inventory_adjustment_post_key,
                           inventory_adjustment_key,
                           MIN(group_id) AS group_id,
                           NVL(SUM(quantity), 0) AS tot_quantity
                    FROM $curated_db.fact_amazon_inventory_adjustment_essor
                    WHERE posted_flag <> 'do not post'
                      AND posting_date >= DATEADD(DAY, -61, TO_DATE(CURRENT_TIMESTAMP()))
                      AND posting_date <= current_date() - 3
                    GROUP BY netsuite_inventory_adjustment_post_key, inventory_adjustment_key) sl
                   ON aggr.netsuite_inventory_adjustment_post_key = sl.netsuite_inventory_adjustment_post_key
                       AND aggr.inventory_adjustment_key = sl.inventory_adjustment_key
WHERE TO_NUMERIC(COALESCE(aggr.tot_quantity, 0), 18, 2) != TO_NUMERIC(COALESCE(sl.tot_quantity, 0), 18, 2)
OR TO_NUMERIC(COALESCE(aggr.group_id, 0), 18, 2) != TO_NUMERIC(COALESCE(sl.group_id, 0), 18, 2);


UPDATE $curated_db.fact_amazon_inventory_adjustment_essor
SET posted_flag                  = 'DISPUTED_TRANSACTION',
    record_updated_timestamp_utc = SYSDATE()
WHERE netsuite_inventory_adjustment_post_key IN (SELECT DISTINCT netsuite_inventory_adjustment_post_key
                                                 FROM $stage_db.netsuite_essor_amazon_disputed_inventory_adjustments
                                                 WHERE UPPER(posting_status) IN
                                                       ('POSTED', 'MANUAL_POST', 'SUBMITTED_TO_NETSUITE'))
  AND LOWER(posted_flag) != 'do not post';

DROP TABLE $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_ESSOR_TEMP;
DROP TABLE $curated_db.FACT_AMAZON_INVENTORY_ADJUSTMENT_GROUPING_TEMP;
DROP TABLE $stage_db.netsuite_essor_amazon_disputed_inventory_adjustments;

COMMIT;

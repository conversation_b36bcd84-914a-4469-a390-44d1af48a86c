import json
import logging
import uuid
import tasklib.sql as tlsql

from airflow import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.http.operators.http import SimpleHttpOperator
from tasklib import alerts
from datetime import datetime
from airflow.models import Variable
import tasklib.dq as tldq


log = logging.getLogger(__name__)

DAG_ID = 'amazon_shopify_pricing_sync'
API_ENDPOINT_CONN_ID = 'dynamic_pricing_root_api_endpoint'

# where to define this variable
WF_PARAMS_EXPR = {
        'wf_name': DAG_ID.upper()
}

def check_response(response):
    try:
        if response.status_code == 204:
            return True
        else:
            return False
    except Exception as e:
        log.error(f'API ERROR - {e}')
    return False

with DAG(
        dag_id="amazon_shopify_pricing_sync",
        start_date=datetime(2025, 5, 15),
        schedule_interval="30 10 */2 * *",
        catchup=False,
        max_active_runs=1,
        params={"workflow_name": "amazon_shopify_pricing_sync", "author": "sunny"},
        tags=["sunny"],
) as dag:
    begin = DummyOperator(task_id="begin")

    generate_uuid = PythonOperator(
        task_id='amazon_shopify_price_sync_automation_generate_uuid',
        python_callable=lambda: str(uuid.uuid4())
    )

    load_amazon_shopify_price_match_rules = PythonOperator(
        task_id="load_amazon_shopify_price_match_rules",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "pricing/shopify/load_amazon_shopify_price_match.sql",
            "wf_params": json.dumps({**WF_PARAMS_EXPR,
                                     'request_id': "{{ti.xcom_pull(task_ids='amazon_b2b_price_automation_generate_uuid')}}"})
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_amazon_shopify_price_match_rules = PythonOperator(
        task_id="run_dq_amazon_shopify_price_match_rules",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'run_dq_amazon_shopify_price_match_rules',
            'query_file': "pricing/shopify/dq_amazon_shopify_price_match_rules.yaml",
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    amazon_shopify_sku_pricing_map = PythonOperator(
        task_id="shopify_amazon_sku_pricing_map",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "pricing/shopify/create_amazon_shopify_sku_pricing.sql",
            "wf_params": json.dumps({**WF_PARAMS_EXPR,
                                     'request_id': "{{ti.xcom_pull(task_ids='amazon_shopify_price_sync_automation_generate_uuid')}}"})
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    invoke_amazon_to_shopify_price_sync_api = SimpleHttpOperator(
        task_id="invoke_pricing_list_price_api",
        http_conn_id=API_ENDPOINT_CONN_ID,
        endpoint="asin-price-change/amazon-shopify-price/update?batch_id={{ti.xcom_pull(task_ids='amazon_shopify_price_sync_automation_generate_uuid')}}",
        method="POST",
        data={},
        headers={
            "Content-Type": "application/json",
        },
        response_check=lambda response: check_response(response),
        log_response=True,

    )

    end = DummyOperator(task_id="end")

    (begin >> generate_uuid >> load_amazon_shopify_price_match_rules >> run_dq_amazon_shopify_price_match_rules >> amazon_shopify_sku_pricing_map >> invoke_amazon_to_shopify_price_sync_api >> end)

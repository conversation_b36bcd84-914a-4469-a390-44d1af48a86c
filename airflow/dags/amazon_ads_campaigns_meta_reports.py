"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.utils.task_group import TaskGroup
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib.loader import check_for_new_files
from tasklib import alerts
from tasklib.loader import S3ToSnowflake
load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'amazon_ads_campaigns_meta_reports'
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2021, 1, 1),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'AMAZON_ADS_CAMPAIGNS_META_REPORTS',
            'author': 'sauvik'},
    tags=['sauvik', 'Raptor', 'Goessor']
) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
        
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_params = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load amz_sp_placementcampaignsreport ---
    
    with TaskGroup(group_id='load_amz_sp_placementcampaignsreport') as tg_amz_sp_placementcampaignsreport:
        list_s3_files_amz_sp_placementcampaignsreport_task = PythonOperator(
            task_id="list_s3_files_amz_sp_placementcampaignsreport",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_sp_placementcampaignsreport.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_amz_sp_placementcampaignsreport_task_goessor = PythonOperator(
            task_id="list_s3_files_amz_sp_placementcampaignsreport_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_sp_placementcampaignsreport_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_amz_sp_placementcampaignsreport_task = DummyOperator(task_id="begin_insert_amz_sp_placementcampaignsreport")
        skip_insert_amz_sp_placementcampaignsreport_task = DummyOperator(task_id="skip_insert_amz_sp_placementcampaignsreport")
        end_insert_amz_sp_placementcampaignsreport_task = DummyOperator(task_id="end_insert_amz_sp_placementcampaignsreport")
        
        s3_to_snowflake_amz_sp_placementcampaignsreport_task = PythonOperator(
            task_id="s3_to_snowflake_amz_sp_placementcampaignsreport",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_sp_placementcampaignsreport.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_amz_sp_placementcampaignsreport_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_amz_sp_placementcampaignsreport_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_sp_placementcampaignsreport_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amz_sp_placementcampaignsreport_task = BranchPythonOperator(
            task_id='check_new_files_found_amz_sp_placementcampaignsreport',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_sp_placementcampaignsreport.yaml",
                       "args_file_second": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_sp_placementcampaignsreport_goessor.yaml",
                       "skip_task_id": "load_amz_sp_placementcampaignsreport.skip_insert_amz_sp_placementcampaignsreport",
                       "next_task_id": "load_amz_sp_placementcampaignsreport.begin_insert_amz_sp_placementcampaignsreport"
            },
        )

        insert_log_amz_sp_placementcampaignsreport_task = PythonOperator(
            task_id="insert_log_amz_sp_placementcampaignsreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/insert_log_amz_sp_placementcampaignsreport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amz_sp_placementcampaignsreport_task = PythonOperator(
            task_id="dedupe_amz_sp_placementcampaignsreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/dedupe_amz_sp_placementcampaignsreport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amz_sp_placementcampaignsreport_task = PythonOperator(
            task_id="merge_stage_amz_sp_placementcampaignsreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/merge_stage_amz_sp_placementcampaignsreport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amz_sp_placementcampaignsreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amz_sp_placementcampaignsreport', 
                    field_list=['reportdate', 'accountid', 'campaignid', 'placement'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sp_placementcampaignsreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amz_sp_placementcampaignsreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sp_placementcampaignsreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amz_sp_placementcampaignsreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sp_placementcampaignsreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_sp_placementcampaignsreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sp_placementcampaignsreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amz_sp_placementcampaignsreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amz_sp_placementcampaignsreport_task = PythonOperator(
            task_id="run_audit_amz_sp_placementcampaignsreport",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amz_sp_placementcampaignsreport", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_amz_sp_placementcampaignsreport_task, list_s3_files_amz_sp_placementcampaignsreport_task_goessor] >>
            check_new_files_found_amz_sp_placementcampaignsreport_task >>
            [begin_insert_amz_sp_placementcampaignsreport_task, skip_insert_amz_sp_placementcampaignsreport_task]
        )

        (
            begin_insert_amz_sp_placementcampaignsreport_task >> 
            s3_to_snowflake_amz_sp_placementcampaignsreport_task >>
            s3_to_snowflake_amz_sp_placementcampaignsreport_task_goessor >>
            insert_log_amz_sp_placementcampaignsreport_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_amz_sp_placementcampaignsreport_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_amz_sp_placementcampaignsreport_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_amz_sp_placementcampaignsreport_task >>
            end_insert_amz_sp_placementcampaignsreport_task
        )

    #  --- Load amz_sb_placementcampaignsvideoreport ---
    
    with TaskGroup(group_id='load_amz_sb_placementcampaignsvideoreport') as tg_amz_sb_placementcampaignsvideoreport:
        list_s3_files_amz_sb_placementcampaignsvideoreport_task = PythonOperator(
            task_id="list_s3_files_amz_sb_placementcampaignsvideoreport",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_sb_placementcampaignsvideoreport.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            
        )

        list_s3_files_amz_sb_placementcampaignsvideoreport_task_goessor = PythonOperator(
            task_id="list_s3_files_amz_sb_placementcampaignsvideoreport_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_sb_placementcampaignsvideoreport_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_amz_sb_placementcampaignsvideoreport_task = DummyOperator(task_id="begin_insert_amz_sb_placementcampaignsvideoreport")
        skip_insert_amz_sb_placementcampaignsvideoreport_task = DummyOperator(task_id="skip_insert_amz_sb_placementcampaignsvideoreport")
        end_insert_amz_sb_placementcampaignsvideoreport_task = DummyOperator(task_id="end_insert_amz_sb_placementcampaignsvideoreport")
        
        s3_to_snowflake_amz_sb_placementcampaignsvideoreport_task = PythonOperator(
            task_id="s3_to_snowflake_amz_sb_placementcampaignsvideoreport",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_sb_placementcampaignsvideoreport.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_amz_sb_placementcampaignsvideoreport_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_amz_sb_placementcampaignsvideoreport_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_sb_placementcampaignsvideoreport_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amz_sb_placementcampaignsvideoreport_task = BranchPythonOperator(
            task_id='check_new_files_found_amz_sb_placementcampaignsvideoreport',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_sb_placementcampaignsvideoreport.yaml",
                       "args_file_second": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_sb_placementcampaignsvideoreport_goessor.yaml",
                       "skip_task_id": "load_amz_sb_placementcampaignsvideoreport.skip_insert_amz_sb_placementcampaignsvideoreport",
                       "next_task_id": "load_amz_sb_placementcampaignsvideoreport.begin_insert_amz_sb_placementcampaignsvideoreport"
            },
        )

        insert_log_amz_sb_placementcampaignsvideoreport_task = PythonOperator(
            task_id="insert_log_amz_sb_placementcampaignsvideoreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/insert_log_amz_sb_placementcampaignsvideoreport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amz_sb_placementcampaignsvideoreport_task = PythonOperator(
            task_id="dedupe_amz_sb_placementcampaignsvideoreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/dedupe_amz_sb_placementcampaignsvideoreport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amz_sb_placementcampaignsvideoreport_task = PythonOperator(
            task_id="merge_stage_amz_sb_placementcampaignsvideoreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/merge_stage_amz_sb_placementcampaignsvideoreport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amz_sb_placementcampaignsvideoreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amz_sb_placementcampaignsvideoreport', 
                    field_list=['reportdate', 'accountid', 'campaignid', 'placement'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sb_placementcampaignsvideoreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amz_sb_placementcampaignsvideoreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sb_placementcampaignsvideoreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amz_sb_placementcampaignsvideoreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sb_placementcampaignsvideoreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_sb_placementcampaignsvideoreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sb_placementcampaignsvideoreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amz_sb_placementcampaignsvideoreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amz_sb_placementcampaignsvideoreport_task = PythonOperator(
            task_id="run_audit_amz_sb_placementcampaignsvideoreport",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amz_sb_placementcampaignsvideoreport", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_amz_sb_placementcampaignsvideoreport_task, list_s3_files_amz_sb_placementcampaignsvideoreport_task] >>
            check_new_files_found_amz_sb_placementcampaignsvideoreport_task >>
            [begin_insert_amz_sb_placementcampaignsvideoreport_task, skip_insert_amz_sb_placementcampaignsvideoreport_task]
        )

        (
            begin_insert_amz_sb_placementcampaignsvideoreport_task >> 
            s3_to_snowflake_amz_sb_placementcampaignsvideoreport_task >>
            s3_to_snowflake_amz_sb_placementcampaignsvideoreport_task_goessor >>
            insert_log_amz_sb_placementcampaignsvideoreport_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_amz_sb_placementcampaignsvideoreport_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_amz_sb_placementcampaignsvideoreport_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_amz_sb_placementcampaignsvideoreport_task >>
            end_insert_amz_sb_placementcampaignsvideoreport_task
        )

    #  --- Load amz_sp_campaign ---
    
    with TaskGroup(group_id='load_amz_sp_campaign') as tg_amz_sp_campaign:
        list_s3_files_amz_sp_campaign_task = PythonOperator(
            task_id="list_s3_files_amz_sp_campaign",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_sp_campaign.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_amz_sp_campaign_task_goessor = PythonOperator(
            task_id="list_s3_files_amz_sp_campaign_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_sp_campaign_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_amz_sp_campaign_task = DummyOperator(task_id="begin_insert_amz_sp_campaign")
        skip_insert_amz_sp_campaign_task = DummyOperator(task_id="skip_insert_amz_sp_campaign")
        end_insert_amz_sp_campaign_task = DummyOperator(task_id="end_insert_amz_sp_campaign")
        
        s3_to_snowflake_amz_sp_campaign_task = PythonOperator(
            task_id="s3_to_snowflake_amz_sp_campaign",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_sp_campaign.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_amz_sp_campaign_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_amz_sp_campaign_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_sp_campaign_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amz_sp_campaign_task = BranchPythonOperator(
            task_id='check_new_files_found_amz_sp_campaign',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_sp_campaign.yaml",
                       "args_file_second": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_sp_campaign_goessor.yaml",
                       "skip_task_id": "load_amz_sp_campaign.skip_insert_amz_sp_campaign",
                       "next_task_id": "load_amz_sp_campaign.begin_insert_amz_sp_campaign"
            },
        )

        insert_log_amz_sp_campaign_task = PythonOperator(
            task_id="insert_log_amz_sp_campaign",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/insert_log_amz_sp_campaign.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amz_sp_campaign_task = PythonOperator(
            task_id="dedupe_amz_sp_campaign",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/dedupe_amz_sp_campaign.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amz_sp_campaign_task = PythonOperator(
            task_id="merge_stage_amz_sp_campaign",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/merge_stage_amz_sp_campaign.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amz_sp_campaign',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amz_sp_campaign', 
                    field_list=['fetchdate', 'accountid', 'campaignid'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sp_campaign',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amz_sp_campaign', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sp_campaign',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amz_sp_campaign', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sp_campaign',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_sp_campaign', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sp_campaign',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amz_sp_campaign', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amz_sp_campaign_task = PythonOperator(
            task_id="run_audit_amz_sp_campaign",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amz_sp_campaign", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_amz_sp_campaign_task, list_s3_files_amz_sp_campaign_task_goessor] >>
            check_new_files_found_amz_sp_campaign_task >>
            [begin_insert_amz_sp_campaign_task, skip_insert_amz_sp_campaign_task]
        )

        (
            begin_insert_amz_sp_campaign_task >> 
            s3_to_snowflake_amz_sp_campaign_task >>
            s3_to_snowflake_amz_sp_campaign_task_goessor >>
            insert_log_amz_sp_campaign_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_amz_sp_campaign_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_amz_sp_campaign_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_amz_sp_campaign_task >>
            end_insert_amz_sp_campaign_task
        )

    #  --- Load amz_sd_campaign ---
    
    with TaskGroup(group_id='load_amz_sd_campaign') as tg_amz_sd_campaign:
        list_s3_files_amz_sd_campaign_task = PythonOperator(
            task_id="list_s3_files_amz_sd_campaign",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_sd_campaign.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_amz_sd_campaign_task_goessor = PythonOperator(
            task_id="list_s3_files_amz_sd_campaign_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_sd_campaign_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_amz_sd_campaign_task = DummyOperator(task_id="begin_insert_amz_sd_campaign")
        skip_insert_amz_sd_campaign_task = DummyOperator(task_id="skip_insert_amz_sd_campaign")
        end_insert_amz_sd_campaign_task = DummyOperator(task_id="end_insert_amz_sd_campaign")
        
        s3_to_snowflake_amz_sd_campaign_task = PythonOperator(
            task_id="s3_to_snowflake_amz_sd_campaign",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_sd_campaign.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_amz_sd_campaign_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_amz_sd_campaign_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_sd_campaign_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amz_sd_campaign_task = BranchPythonOperator(
            task_id='check_new_files_found_amz_sd_campaign',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_sd_campaign.yaml",
                       "args_file_second": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_sd_campaign_goessor.yaml",
                       "skip_task_id": "load_amz_sd_campaign.skip_insert_amz_sd_campaign",
                       "next_task_id": "load_amz_sd_campaign.begin_insert_amz_sd_campaign"
            },
        )

        insert_log_amz_sd_campaign_task = PythonOperator(
            task_id="insert_log_amz_sd_campaign",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/insert_log_amz_sd_campaign.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amz_sd_campaign_task = PythonOperator(
            task_id="dedupe_amz_sd_campaign",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/dedupe_amz_sd_campaign.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amz_sd_campaign_task = PythonOperator(
            task_id="merge_stage_amz_sd_campaign",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/merge_stage_amz_sd_campaign.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amz_sd_campaign',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amz_sd_campaign', 
                    field_list=['fetchdate', 'accountid', 'campaignid'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sd_campaign',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amz_sd_campaign', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sd_campaign',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amz_sd_campaign', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sd_campaign',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_sd_campaign', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sd_campaign',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amz_sd_campaign', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amz_sd_campaign_task = PythonOperator(
            task_id="run_audit_amz_sd_campaign",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amz_sd_campaign", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_amz_sd_campaign_task, list_s3_files_amz_sd_campaign_task_goessor] >>
            check_new_files_found_amz_sd_campaign_task >>
            [begin_insert_amz_sd_campaign_task, skip_insert_amz_sd_campaign_task]
        )

        (
            begin_insert_amz_sd_campaign_task >> 
            s3_to_snowflake_amz_sd_campaign_task >>
            s3_to_snowflake_amz_sd_campaign_task_goessor >>
            insert_log_amz_sd_campaign_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_amz_sd_campaign_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_amz_sd_campaign_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_amz_sd_campaign_task >>
            end_insert_amz_sd_campaign_task
        )

    #  --- Load amz_sb_campaign ---
    
    with TaskGroup(group_id='load_amz_sb_campaign') as tg_amz_sb_campaign:
        list_s3_files_amz_sb_campaign_task = PythonOperator(
            task_id="list_s3_files_amz_sb_campaign",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_sb_campaign.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_amz_sb_campaign_task_goessor = PythonOperator(
            task_id="list_s3_files_amz_sb_campaign_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_sb_campaign_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_amz_sb_campaign_task = DummyOperator(task_id="begin_insert_amz_sb_campaign")
        skip_insert_amz_sb_campaign_task = DummyOperator(task_id="skip_insert_amz_sb_campaign")
        end_insert_amz_sb_campaign_task = DummyOperator(task_id="end_insert_amz_sb_campaign")
        
        s3_to_snowflake_amz_sb_campaign_task = PythonOperator(
            task_id="s3_to_snowflake_amz_sb_campaign",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_sb_campaign.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_amz_sb_campaign_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_amz_sb_campaign_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_sb_campaign_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amz_sb_campaign_task = BranchPythonOperator(
            task_id='check_new_files_found_amz_sb_campaign',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_sb_campaign.yaml",
                       "args_file_second": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_sb_campaign_goessor.yaml",
                       "skip_task_id": "load_amz_sb_campaign.skip_insert_amz_sb_campaign",
                       "next_task_id": "load_amz_sb_campaign.begin_insert_amz_sb_campaign"
            },
        )

        insert_log_amz_sb_campaign_task = PythonOperator(
            task_id="insert_log_amz_sb_campaign",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/insert_log_amz_sb_campaign.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amz_sb_campaign_task = PythonOperator(
            task_id="dedupe_amz_sb_campaign",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/dedupe_amz_sb_campaign.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amz_sb_campaign_task = PythonOperator(
            task_id="merge_stage_amz_sb_campaign",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/merge_stage_amz_sb_campaign.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amz_sb_campaign',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amz_sb_campaign', 
                    field_list=['fetchdate', 'accountid', 'campaignid'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sb_campaign',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amz_sb_campaign', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sb_campaign',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amz_sb_campaign', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sb_campaign',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_sb_campaign', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sb_campaign',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amz_sb_campaign', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amz_sb_campaign_task = PythonOperator(
            task_id="run_audit_amz_sb_campaign",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amz_sb_campaign", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_amz_sb_campaign_task, list_s3_files_amz_sb_campaign_task_goessor] >>
            check_new_files_found_amz_sb_campaign_task >>
            [begin_insert_amz_sb_campaign_task, skip_insert_amz_sb_campaign_task]
        )

        (
            begin_insert_amz_sb_campaign_task >> 
            s3_to_snowflake_amz_sb_campaign_task >>
            s3_to_snowflake_amz_sb_campaign_task_goessor >>
            insert_log_amz_sb_campaign_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_amz_sb_campaign_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_amz_sb_campaign_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_amz_sb_campaign_task >>
            end_insert_amz_sb_campaign_task
        )

    #  --- Load amz_portfolio ---
    
    with TaskGroup(group_id='load_amz_portfolio') as tg_amz_portfolio:
        list_s3_files_amz_portfolio_task = PythonOperator(
            task_id="list_s3_files_amz_portfolio",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_portfolio.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_amz_portfolio_task_goessor = PythonOperator(
            task_id="list_s3_files_amz_portfolio_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_portfolio_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_amz_portfolio_task = DummyOperator(task_id="begin_insert_amz_portfolio")
        skip_insert_amz_portfolio_task = DummyOperator(task_id="skip_insert_amz_portfolio")
        end_insert_amz_portfolio_task = DummyOperator(task_id="end_insert_amz_portfolio")
        
        s3_to_snowflake_amz_portfolio_task = PythonOperator(
            task_id="s3_to_snowflake_amz_portfolio",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_portfolio.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_amz_portfolio_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_amz_portfolio_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_portfolio_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amz_portfolio_task = BranchPythonOperator(
            task_id='check_new_files_found_amz_portfolio',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_portfolio.yaml",
                       "args_file_second": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_portfolio_goessor.yaml",
                       "skip_task_id": "load_amz_portfolio.skip_insert_amz_portfolio",
                       "next_task_id": "load_amz_portfolio.begin_insert_amz_portfolio"
            },
        )

        insert_log_amz_portfolio_task = PythonOperator(
            task_id="insert_log_amz_portfolio",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/insert_log_amz_portfolio.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amz_portfolio_task = PythonOperator(
            task_id="dedupe_amz_portfolio",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/dedupe_amz_portfolio.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amz_portfolio_task = PythonOperator(
            task_id="merge_stage_amz_portfolio",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/merge_stage_amz_portfolio.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amz_portfolio',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amz_portfolio', 
                    field_list=['fetchdate', 'accountid', 'portfolioid'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_portfolio',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amz_portfolio', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_portfolio',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amz_portfolio', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_portfolio',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_portfolio', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_portfolio',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amz_portfolio', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amz_portfolio_task = PythonOperator(
            task_id="run_audit_amz_portfolio",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amz_portfolio", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_amz_portfolio_task, list_s3_files_amz_portfolio_task_goessor] >>
            check_new_files_found_amz_portfolio_task >>
            [begin_insert_amz_portfolio_task, skip_insert_amz_portfolio_task]
        )

        (
            begin_insert_amz_portfolio_task >> 
            s3_to_snowflake_amz_portfolio_task >>
            s3_to_snowflake_amz_portfolio_task_goessor >>
            insert_log_amz_portfolio_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_amz_portfolio_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_amz_portfolio_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_amz_portfolio_task >>
            end_insert_amz_portfolio_task
        )

    #  --- Load amz_sb_placementcampaignsreport ---
    
    with TaskGroup(group_id='load_amz_sb_placementcampaignsreport') as tg_amz_sb_placementcampaignsreport:
        list_s3_files_amz_sb_placementcampaignsreport_task = PythonOperator(
            task_id="list_s3_files_amz_sb_placementcampaignsreport",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_sb_placementcampaignsreport.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_amz_sb_placementcampaignsreport_task_goessor = PythonOperator(
            task_id="list_s3_files_amz_sb_placementcampaignsreport_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_sb_placementcampaignsreport_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_amz_sb_placementcampaignsreport_task = DummyOperator(task_id="begin_insert_amz_sb_placementcampaignsreport")
        skip_insert_amz_sb_placementcampaignsreport_task = DummyOperator(task_id="skip_insert_amz_sb_placementcampaignsreport")
        end_insert_amz_sb_placementcampaignsreport_task = DummyOperator(task_id="end_insert_amz_sb_placementcampaignsreport")
        
        s3_to_snowflake_amz_sb_placementcampaignsreport_task = PythonOperator(
            task_id="s3_to_snowflake_amz_sb_placementcampaignsreport",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_sb_placementcampaignsreport.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_amz_sb_placementcampaignsreport_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_amz_sb_placementcampaignsreport_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_sb_placementcampaignsreport_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amz_sb_placementcampaignsreport_task = BranchPythonOperator(
            task_id='check_new_files_found_amz_sb_placementcampaignsreport',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_sb_placementcampaignsreport.yaml",
                       "args_file_second": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_sb_placementcampaignsreport_goessor.yaml",
                       "skip_task_id": "load_amz_sb_placementcampaignsreport.skip_insert_amz_sb_placementcampaignsreport",
                       "next_task_id": "load_amz_sb_placementcampaignsreport.begin_insert_amz_sb_placementcampaignsreport"
            },
        )

        insert_log_amz_sb_placementcampaignsreport_task = PythonOperator(
            task_id="insert_log_amz_sb_placementcampaignsreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/insert_log_amz_sb_placementcampaignsreport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amz_sb_placementcampaignsreport_task = PythonOperator(
            task_id="dedupe_amz_sb_placementcampaignsreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/dedupe_amz_sb_placementcampaignsreport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amz_sb_placementcampaignsreport_task = PythonOperator(
            task_id="merge_stage_amz_sb_placementcampaignsreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/merge_stage_amz_sb_placementcampaignsreport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )


        run_dq_is_null_dedupe_task = PythonOperator(
            task_id="run_dq_is_null_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sb_placementcampaignsreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_sb_placementcampaignsreport', 
                    field_list=['reportdate', 'accountid', 'campaignid', 'placement'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )


        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sb_placementcampaignsreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amz_sb_placementcampaignsreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sb_placementcampaignsreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amz_sb_placementcampaignsreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sb_placementcampaignsreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_sb_placementcampaignsreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sb_placementcampaignsreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amz_sb_placementcampaignsreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amz_sb_placementcampaignsreport_task = PythonOperator(
            task_id="run_audit_amz_sb_placementcampaignsreport",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amz_sb_placementcampaignsreport", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_amz_sb_placementcampaignsreport_task, list_s3_files_amz_sb_placementcampaignsreport_task_goessor] >>
            check_new_files_found_amz_sb_placementcampaignsreport_task >>
            [begin_insert_amz_sb_placementcampaignsreport_task, skip_insert_amz_sb_placementcampaignsreport_task]
        )

        (
            begin_insert_amz_sb_placementcampaignsreport_task >> 
            s3_to_snowflake_amz_sb_placementcampaignsreport_task >>
            s3_to_snowflake_amz_sb_placementcampaignsreport_task_goessor >>
            insert_log_amz_sb_placementcampaignsreport_task >>
            dedupe_amz_sb_placementcampaignsreport_task >>
            
                ( run_dq_is_null_dedupe_task
              
                , run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_amz_sb_placementcampaignsreport_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_amz_sb_placementcampaignsreport_task >>
            end_insert_amz_sb_placementcampaignsreport_task
        )


    with TaskGroup(group_id='load_amz_sd_bidrecommendations') as tg_amz_sd_bidrecommendations:
        list_s3_files_amz_sd_bidrecommendations_task = PythonOperator(
            task_id="list_s3_files_amz_sd_bidrecommendations",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_sd_bidrecommendations.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_amz_sd_bidrecommendations_task_goessor = PythonOperator(
            task_id="list_s3_files_amz_sd_bidrecommendations_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/list_s3_amz_sd_bidrecommendations_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a 'begin' dummy operator for branching
        begin_insert_amz_sd_bidrecommendations_task = DummyOperator(
            task_id="begin_insert_amz_sd_bidrecommendations")
        skip_insert_amz_sd_bidrecommendations_task = DummyOperator(
            task_id="skip_insert_amz_sd_bidrecommendations")
        end_insert_amz_sd_bidrecommendations_task = DummyOperator(
            task_id="end_insert_amz_sd_bidrecommendations")

        s3_to_snowflake_amz_sd_bidrecommendations_task = PythonOperator(
            task_id="s3_to_snowflake_amz_sd_bidrecommendations",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_sd_bidrecommendations.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_amz_sd_bidrecommendations_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_amz_sd_bidrecommendations_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/s3_to_sf_raw_amz_sd_bidrecommendations_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amz_sd_bidrecommendations_task = BranchPythonOperator(
            task_id='check_new_files_found_amz_sd_bidrecommendations',
            python_callable=check_for_new_files,
            op_kwargs={
                "args_file": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_sd_bidrecommendations.yaml",
                "args_file_second": "amazon_ads_campaigns_meta_reports/s3_to_snowflake_amz_sd_bidrecommendations_goessor.yaml",
                "skip_task_id": "load_amz_sd_bidrecommendations.skip_insert_amz_sd_bidrecommendations",
                "next_task_id": "load_amz_sd_bidrecommendations.begin_insert_amz_sd_bidrecommendations"
                },
        )

        insert_log_amz_sd_bidrecommendations_task = PythonOperator(
            task_id="insert_log_amz_sd_bidrecommendations",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/insert_log_amz_sd_bidrecommendations.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amz_sd_bidrecommendations_task = PythonOperator(
            task_id="dedupe_amz_sd_bidrecommendations",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/dedupe_amz_sd_bidrecommendations.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        merge_stage_amz_sd_bidrecommendations_task = PythonOperator(
            task_id="merge_stage_amz_sd_bidrecommendations",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ads_campaigns_meta_reports/merge_stage_amz_sd_bidrecommendations.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_null_dedupe_task = PythonOperator(
            task_id="run_dq_is_null_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sd_bidrecommendations',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_sd_bidrecommendations',
                    field_list=['fetch_date', 'account_id', 'profile_id', 'targeting_clauses'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sd_bidrecommendations',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amz_sd_bidrecommendations',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sd_bidrecommendations',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amz_sd_bidrecommendations',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_sd_bidrecommendations',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_sd_bidrecommendations',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_sd_bidrecommendations',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amz_sd_bidrecommendations',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_audit_amz_sd_bidrecommendations_task = PythonOperator(
            task_id="run_audit_amz_sd_bidrecommendations",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amz_sd_bidrecommendations",  # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
                [list_s3_files_amz_sd_bidrecommendations_task, list_s3_files_amz_sd_bidrecommendations_task_goessor] >>
                check_new_files_found_amz_sd_bidrecommendations_task >>
                [begin_insert_amz_sd_bidrecommendations_task, skip_insert_amz_sd_bidrecommendations_task]
        )

        (
                begin_insert_amz_sd_bidrecommendations_task >>
                s3_to_snowflake_amz_sd_bidrecommendations_task >>
                s3_to_snowflake_amz_sd_bidrecommendations_task_goessor >>
                insert_log_amz_sd_bidrecommendations_task >>
                dedupe_amz_sd_bidrecommendations_task >>

                (run_dq_is_null_dedupe_task

                 , run_dq_is_unique_dedupe_pk_task

                 , run_dq_is_null_dedupe_pk_task
                 ) >> merge_stage_amz_sd_bidrecommendations_task >>

                (run_dq_is_unique_merge_pk_task

                 , run_dq_is_null_merge_pk_task
                 ) >> run_audit_amz_sd_bidrecommendations_task >>
                end_insert_amz_sd_bidrecommendations_task
        )


    task_list = [
        tg_amz_sp_placementcampaignsreport
        , tg_amz_sb_placementcampaignsvideoreport
        , tg_amz_sp_campaign
        , tg_amz_sd_campaign
        , tg_amz_sb_campaign
        , tg_amz_portfolio
        , tg_amz_sb_placementcampaignsreport
        , tg_amz_sd_bidrecommendations
    ]

    # ---- Main branch ----
    begin >> get_workflow_params >> task_list >> update_workflow_params >> end
    
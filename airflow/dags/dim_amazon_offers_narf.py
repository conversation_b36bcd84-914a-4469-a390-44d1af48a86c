from airflow import DAG
from datetime import datetime
from airflow.operators.python import <PERSON><PERSON><PERSON>honOperator, PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging
import tasklib.dq as tldq
from airflow.utils.trigger_rule import TriggerRule

log = logging.getLogger(__name__)

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"
DAG_ID='dim_amazon_offers_narf'
with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2023, 5, 8),
    schedule_interval="@daily",
    catchup=False,
    max_active_runs=1,
    tags=['Vikas','RETIRED'],
    params={
        "workflow_name": "dim_amazon_offers_narf",
        "author": "vikas"
    },
) as dag:
    import tasklib.workflow as tlw
    import tasklib.sql as tlsql
    from tasklib import alerts
    from tasklib.loader import S3ToSnowflake
    load_obj = S3ToSnowflake()

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    # list_s3_modified_files = PythonOperator(
    #     task_id="task_list_s3_modified_files",
    #     python_callable=tls3.list_s3_modified_files,
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    #     op_kwargs={
    #         "args_file": "",
    #         "wf_params": WF_PARAMS_EXPR,
    #     },
    # )

    #skip_merge = DummyOperator(task_id='skip_merge')

    # check_new_files_found = BranchPythonOperator(
    #     task_id='check_new_files_found',
    #     python_callable=check_for_new_files,
    #     op_kwargs={
    #         'args_file': S3_TO_SNOWFLAKE_YAML,
    #         'skip_task_id': 'skip_merge',
    #         'next_task_id': 'transfer_s3_to_snowflake',
    #     }
    # )

    # transfer_s3_to_snowflake = PythonOperator(
    #     task_id="transfer_s3_to_snowflake",
    #     python_callable=load_obj.s3_to_snowflake_load,
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    #     op_kwargs={
    #         "args_file": "dim_shopify_products/s3_to_sf_raw_dim_shopify_products.yaml"},
    # )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    run_dq_raw_narf = PythonOperator(
        task_id="run_dq_raw_narf",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            "wk_name": DAG_ID,
            "run_id": "task_run_dq_tests_fact_inventory_stage",
            "tb_name": "$raw_db.RAW_REMOTE_FULFILLMENT",
            "query_file": "inventory/amazon/fba_inventory/narf/dq_raw_offer_narf.yaml",
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    dedupe_remote_fulfillment = PythonOperator(
        task_id="dedupe_remote_fulfillment",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "inventory/amazon/fba_inventory/narf/dedupe_remote_fulfillment.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
        task_id="run_dq_is_unique_dedupe_pk_hard",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': '$stage_db.dedupe_amazon_offer_narf',
            'test_name': 'is_unique',
            'sql_query': tldq.gen_check_unique_key(
                tb_name='$stage_db.dedupe_amazon_offer_narf',
                field_list=['asin', 'sku', 'seller_id'],
                hard_alert=True,
            )
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    stg_log_remote_fulfillment = PythonOperator(
        task_id="stg_log_remote_fulfillment",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "inventory/amazon/fba_inventory/narf/stg_log_remote_fulfillment.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    latest_remote_fulfillment = PythonOperator(
        task_id="latest_remote_fulfillment",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "inventory/amazon/fba_inventory/narf/latest_remote_fulfillment.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_dq_latest_remote_pre_curated = PythonOperator(
        task_id="run_dq_latest_remote_pre_curated",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            "wk_name": DAG_ID,
            "run_id": "task_run_dq_tests_fact_inventory_stage",
            "tb_name": "$stage_db.stg_amazon_offer_narf_flags",
            "query_file": "inventory/amazon/fba_inventory/narf/dq_stg_latest_offer_narf.yaml",
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    update_dim_remote_fulfillment = PythonOperator(
        task_id="dim_remote_fulfillment",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "inventory/amazon/fba_inventory/narf/dim_remote_fulfillment.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_dq_dim_remote_curated = PythonOperator(
        task_id="run_dq_dim_remote_curated",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            "wk_name": DAG_ID,
            "run_id": "task_run_dq_tests_fact_inventory_stage",
            "tb_name": "$curated_db.dim_amazon_offers_narf",
            "query_file": "inventory/amazon/fba_inventory/narf/dq_dim_amazon_offer_narf.yaml",
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    begin = DummyOperator(
        task_id="begin", depends_on_past=True, wait_for_downstream=True
    )
    end = DummyOperator(task_id="end")

    chain(
        begin,
        get_workflow_params,
        run_dq_raw_narf,
        dedupe_remote_fulfillment,
        run_dq_is_unique_dedupe_pk_hard_task,
        stg_log_remote_fulfillment,
        latest_remote_fulfillment,
        run_dq_latest_remote_pre_curated,
        update_dim_remote_fulfillment,
        run_dq_dim_remote_curated,
        update_workflow_params,
        end
    )

import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'brand_ebitda_affiliates_cost'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '15 4 * * *')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2025, 4, 15),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'BRAND_EBITDA_AFFILIATES_COST',
            'author': 'leav'},
    tags=['leav', 'Raptor']
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}

    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load brand_ebitda_affiliates_cost ---
    with TaskGroup(group_id='load_brand_ebitda_affiliates_cost') as tg_brand_ebitda_affiliates_cost:
        list_s3_files_brand_ebitda_affiliates_cost_task = PythonOperator(
            task_id="list_s3_files_brand_ebitda_affiliates_cost",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "brand_ebitda_framework/list_s3_brand_ebitda_affiliates_cost.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        begin_insert_brand_ebitda_affiliates_cost_task = DummyOperator(task_id="begin_insert_brand_ebitda_affiliates_cost")
        end_insert_brand_ebitda_affiliates_cost_task = DummyOperator(task_id="end_insert_brand_ebitda_affiliates_cost")
        end_brand_ebitda_affiliates_cost_task = DummyOperator(
            task_id="end_brand_ebitda_affiliates_cost",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        s3_to_snowflake_brand_ebitda_affiliates_cost_task = PythonOperator(
            task_id="s3_to_snowflake_brand_ebitda_affiliates_cost",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "brand_ebitda_framework/s3_to_sf_raw_brand_ebitda_affiliates_cost.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_brand_ebitda_affiliates_cost_task = PythonOperator(
            task_id="dedupe_brand_ebitda_affiliates_cost",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/affiliates/dedupe_brand_ebitda_affiliates_cost.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_brand_ebitda_affiliates_cost',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_brand_ebitda_affiliates_cost',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_soft_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_soft",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_brand_ebitda_affiliates_cost',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_brand_ebitda_affiliates_cost',
                    field_list=['pk'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_brand_ebitda_affiliates_cost_task = PythonOperator(
            task_id="merge_stage_brand_ebitda_affiliates_cost",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/affiliates/merge_stage_brand_ebitda_affiliates_cost.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_brand_ebitda_affiliates_cost',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_brand_ebitda_affiliates_cost',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_brand_ebitda_affiliates_cost',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_brand_ebitda_affiliates_cost',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_brand_ebitda_affiliates_cost_task = PythonOperator(
            task_id="run_audit_brand_ebitda_affiliates_cost",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_brand_ebitda_affiliates_cost",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_brand_ebitda_affiliates_cost_task >>
            begin_insert_brand_ebitda_affiliates_cost_task >>
            s3_to_snowflake_brand_ebitda_affiliates_cost_task >>
            dedupe_brand_ebitda_affiliates_cost_task >> # Dependency updated
            (run_dq_is_unique_dedupe_pk_hard_task, run_dq_is_null_dedupe_pk_soft_task) >>
            merge_stage_brand_ebitda_affiliates_cost_task >>
            (run_dq_is_unique_merge_pk_hard_task, run_dq_is_null_merge_pk_hard_task) >>
            run_audit_brand_ebitda_affiliates_cost_task >>
            end_insert_brand_ebitda_affiliates_cost_task >>
            end_brand_ebitda_affiliates_cost_task
        )

    #  --- Load brand_ebitda_affiliates_cost_estimate ---
    with TaskGroup(group_id='load_brand_ebitda_affiliates_cost_estimate') as tg_brand_ebitda_affiliates_cost_estimate:
        list_s3_files_brand_ebitda_affiliates_cost_estimate_task = PythonOperator(
            task_id="list_s3_files_brand_ebitda_affiliates_cost_estimate",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "brand_ebitda_framework/list_s3_brand_ebitda_affiliates_cost_estimate.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        begin_insert_brand_ebitda_affiliates_cost_estimate_task = DummyOperator(task_id="begin_insert_brand_ebitda_affiliates_cost_estimate")
        end_insert_brand_ebitda_affiliates_cost_estimate_task = DummyOperator(task_id="end_insert_brand_ebitda_affiliates_cost_estimate")
        end_brand_ebitda_affiliates_cost_estimate_task = DummyOperator(
            task_id="end_brand_ebitda_affiliates_cost_estimate",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        s3_to_snowflake_brand_ebitda_affiliates_cost_estimate_task = PythonOperator(
            task_id="s3_to_snowflake_brand_ebitda_affiliates_cost_estimate",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "brand_ebitda_framework/s3_to_sf_raw_brand_ebitda_affiliates_cost_estimate.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_brand_ebitda_affiliates_cost_estimate_task = PythonOperator(
            task_id="dedupe_brand_ebitda_affiliates_cost_estimate",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/affiliates/dedupe_brand_ebitda_affiliates_cost_estimate.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_brand_ebitda_affiliates_cost_estimate',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_brand_ebitda_affiliates_cost_estimate',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_soft_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_soft",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_brand_ebitda_affiliates_cost_estimate',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_brand_ebitda_affiliates_cost_estimate',
                    field_list=['pk'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_brand_ebitda_affiliates_cost_estimate_task = PythonOperator(
            task_id="merge_stage_brand_ebitda_affiliates_cost_estimate",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/affiliates/merge_stage_brand_ebitda_affiliates_cost_estimate.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_brand_ebitda_affiliates_cost_estimate',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_brand_ebitda_affiliates_cost_estimate',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_brand_ebitda_affiliates_cost_estimate',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_brand_ebitda_affiliates_cost_estimate',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_brand_ebitda_affiliates_cost_estimate_task = PythonOperator(
            task_id="run_audit_brand_ebitda_affiliates_cost_estimate",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_brand_ebitda_affiliates_cost_estimate",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_brand_ebitda_affiliates_cost_estimate_task >>
            begin_insert_brand_ebitda_affiliates_cost_estimate_task >>
            s3_to_snowflake_brand_ebitda_affiliates_cost_estimate_task >>
            dedupe_brand_ebitda_affiliates_cost_estimate_task >> # Dependency updated
            (run_dq_is_unique_dedupe_pk_hard_task, run_dq_is_null_dedupe_pk_soft_task) >>
            merge_stage_brand_ebitda_affiliates_cost_estimate_task >>
            (run_dq_is_unique_merge_pk_hard_task, run_dq_is_null_merge_pk_hard_task) >>
            run_audit_brand_ebitda_affiliates_cost_estimate_task >>
            end_insert_brand_ebitda_affiliates_cost_estimate_task >>
            end_brand_ebitda_affiliates_cost_estimate_task
        )

    # ---- Main branch ----
    chain(
        begin,
        get_workflow_parameters,
        tg_brand_ebitda_affiliates_cost,
        tg_brand_ebitda_affiliates_cost_estimate,
        update_workflow_parameters,
        end
    )

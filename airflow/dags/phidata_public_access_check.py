from airflow import D<PERSON>
from datetime import datetime
from airflow.utils.task_group import TaskGroup
from airflow.operators.python import Python<PERSON><PERSON><PERSON>
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain

from tasklib import alerts

import logging
import json
import boto3
from botocore.config import Config
from botocore.exceptions import ClientError

log = logging.getLogger(__name__)

__LOAD_BALANCER='LoadBalancers'
__SECURITY_GROUPS='SecurityGroups'
__GROUP_ID='GroupId'

__DAG_ID='phidata_public_access_check'

__BOTO_CLIENT_EC2='ec2'
__BOTO_CLIENT_ELASTIC_LOAD_BALANCER='elbv2'

__AWS_REGION_NAME = 'us-east-2'

# Name of the VPC that has the Phidata installation
__AWS_VPC_ID_KEY='vpc-id'
__AWS_VPC_ID_VAL='vpc-0ad6eec89fc4a6eab'

# Name of the EC2 security group that restricts access to inbound traffic from VPN 
__AWS_SG_GROUP_KEY='group-name'
__AWS_SG_GROUP_VAL='vpn-data-platform-access-only'

# Name of the phidata ingress load balancer
__AWS_INGRESS_LOAD_BALANCER='hydy-data-platform-ingress'

def load_balancer_sg_check(
        aws_region_name=__AWS_REGION_NAME,
        aws_vpc_id=__AWS_VPC_ID_VAL,
        aws_security_group_name=__AWS_SG_GROUP_VAL,
        aws_ingress_load_balancer=__AWS_INGRESS_LOAD_BALANCER):

    boto3_config=Config(region_name = aws_region_name)
    ec2 = boto3.client(__BOTO_CLIENT_EC2, config=boto3_config)

    try:
        response = ec2.describe_security_groups(
                       Filters = [{'Name':__AWS_VPC_ID_KEY,
                                   'Values':[aws_vpc_id]
                                  },{
                                   'Name':__AWS_SG_GROUP_KEY, 
                                   'Values':[aws_security_group_name]
                                  }
                                 ] 
                       )
    except ClientError as e:
        log.error(e)
        raise 

    if not response:
        err_msg='Boto client security group call resonse is invalid.'
        raise Exception(err_msg)

    if not response[__SECURITY_GROUPS]:
        err_msg=f'Boto client security group call resonse is invalid. Security group list is empty in {response}'
        raise Exception(err_msg)
 
    sec_grp=response[__SECURITY_GROUPS][0][__GROUP_ID]
    if not sec_grp:
        err_msg=f'Unable to read security group id from {response}.'
        raise Exception(err_msg)

    log.info(f'Group Id of Security Group {aws_security_group_name} is {sec_grp}')

    elbv2 = boto3.client(__BOTO_CLIENT_ELASTIC_LOAD_BALANCER, config=boto3_config)

    try:
        response = elbv2.describe_load_balancers(Names=[aws_ingress_load_balancer] )
    except ClientError as e:
        log.error(e)
        raise 

    if not response:
        err_msg='Boto client load balancer call resonse is invalid.'
        raise Exception(err_msg)

    if not response[__LOAD_BALANCER]:
        err_msg=f'Boto client load balancer call resonse is invalid. Load balancer group list is empty in {response}'
        raise Exception(err_msg)

    lb_sec_grps=response[__LOAD_BALANCER][0][__SECURITY_GROUPS]
    if not lb_sec_grps:
        err_msg=f'Load balancer security group list is empty in {response}'
        raise Exception(err_msg)

    log.info(f'Security groups configured for ingress load balancer {aws_ingress_load_balancer} : {lb_sec_grps}')

    if sec_grp not in lb_sec_grps:
        log.error(f'Ingress load balancer {aws_ingress_load_balancer} is missing security group {aws_security_group_name}')
        log.error('Phidata applications may be open to public')
        err_msg=f'Ingress load balancer security group check failed.'
        raise Exception(err_msg)
    else:
        print(f'Ingress load balancer security group check success.')
        print(f'Ingress load balancer {aws_ingress_load_balancer} has security group {aws_security_group_name} to allow access from VPN traffic only.')

with DAG(
    dag_id=__DAG_ID,
    start_date=datetime(2023,10,25),
    schedule_interval='@hourly',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': __DAG_ID.upper(),
            'author':'srichand'
           },
    tags=['RETIRED','srichand'],
) as dag:

    is_phidata_public_access_disabled=PythonOperator(
        task_id="task_is_phidata_public_access_disabled",
        python_callable=load_balancer_sg_check,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator( task_id="end")

    chain(
        begin,
        is_phidata_public_access_disabled,
        end
    )

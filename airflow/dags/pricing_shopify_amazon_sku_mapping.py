from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain

import logging
from tasklib import alerts
import tasklib.sql as tlsql
import tasklib.replication as tlr

log = logging.getLogger(__name__)

BUILD_NUM = '1'
RELEASE_DEF = '1'
DAG_ID = 'pricing_shopify_amazon_sku_mapping'
WF_PARAMS_EXPR = "{'wf_name': '" + DAG_ID.upper() + "'}"

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2023, 5, 1),
    schedule_interval='@hourly',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'ayush'}
) as dag:

    create_stg_pricing_shopify_amazon_sku_mapping = PythonOperator(
        task_id="create_stg_pricing_shopify_amazon_sku_mapping",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "pricing/asin_variant_mapping/create_stg_pricing_shopify_amazon_sku_mapping.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_pricing_shopify_amazon_sku_mapping = PythonOperator(
        task_id="create_pricing_shopify_amazon_sku_mapping",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "pricing/asin_variant_mapping/create_pricing_shopify_amazon_sku_mapping.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    replicate_pricing_shopify_amazon_sku_mapping = PythonOperator(
        task_id="replicate_pricing_shopify_amazon_sku_mapping",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/pricing_shopify_amazon_sku_mapping.yaml"}
    )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    chain(
        begin,
        create_stg_pricing_shopify_amazon_sku_mapping,
        create_pricing_shopify_amazon_sku_mapping,
        replicate_pricing_shopify_amazon_sku_mapping,
        end,
    )

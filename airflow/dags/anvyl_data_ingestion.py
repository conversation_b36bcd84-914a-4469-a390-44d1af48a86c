"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import <PERSON><PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib.loader import check_for_new_files
from tasklib import alerts

from tasklib.loader import S3ToSnowflake
load_obj = S3ToSnowflake()
BUILD_NUM = '1'
DAG_ID = 'anvyl_data_ingestion'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '30 0,6,12,18 * * *')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2023, 1, 1),
    schedule_interval='@hourly',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'ANVYL-DATA-INGESTION',
            'author': 'vikas'},
    tags=['Vikas', 'Raptor']
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
        
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load anvyl_orders ---
    
    with TaskGroup(group_id='load_anvyl_orders') as tg_anvyl_orders:
        list_s3_files_anvyl_orders_task = PythonOperator(
            task_id="list_s3_files_anvyl_orders",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "anvyl-data-ingestion/list_s3_anvyl_orders.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            
        )

        # Need a begin dummy operator for branching
        begin_insert_anvyl_orders_task = DummyOperator(task_id="begin_insert_anvyl_orders")
        skip_insert_anvyl_orders_task = DummyOperator(task_id="skip_insert_anvyl_orders")
        end_insert_anvyl_orders_task = DummyOperator(task_id="end_insert_anvyl_orders")
        end_anvyl_orders_task = DummyOperator(
            task_id="end_anvyl_orders",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_anvyl_orders_task = BranchPythonOperator(
            task_id='check_new_files_found_anvyl_orders',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_snowflake_anvyl_orders.yaml",
                       "skip_task_id": "load_anvyl_orders.skip_insert_anvyl_orders",
                       "next_task_id": "load_anvyl_orders.begin_insert_anvyl_orders"
            },
        )

        s3_to_snowflake_anvyl_orders_task = PythonOperator(
            task_id="s3_to_snowflake_anvyl_orders",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_sf_raw_anvyl_orders.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        

        insert_log_anvyl_orders_task = PythonOperator(
            task_id="insert_log_anvyl_orders",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "anvyl-data-ingestion/orders/insert_log_anvyl_orders.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_anvyl_orders_task = PythonOperator(
            task_id="dedupe_anvyl_orders",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "anvyl-data-ingestion/orders/dedupe_anvyl_orders.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_unique_dedupe_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_orders',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_anvyl_orders', 
                    field_list=['id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_orders',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_anvyl_orders', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_orders',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_anvyl_orders', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        merge_stage_anvyl_orders_task = PythonOperator(
            task_id="merge_stage_anvyl_orders",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "anvyl-data-ingestion/orders/merge_stage_anvyl_orders.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        
        run_dq_is_unique_merge_task = PythonOperator(
            task_id="run_dq_is_unique_merge",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_orders',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_orders',
                    field_list=['id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_orders',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$anvyl_db.anvyl_orders',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_orders',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_orders',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        run_audit_anvyl_orders_task = PythonOperator(
            task_id="run_audit_anvyl_orders",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$anvyl_db.anvyl_orders", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        (
            list_s3_files_anvyl_orders_task >>
            check_new_files_found_anvyl_orders_task >>
            [begin_insert_anvyl_orders_task, skip_insert_anvyl_orders_task]
        )

        (
            begin_insert_anvyl_orders_task >> 
            s3_to_snowflake_anvyl_orders_task >>
            
            insert_log_anvyl_orders_task >>
            dedupe_anvyl_orders_task >>
            
         ( run_dq_is_unique_dedupe_task, 
    
         run_dq_is_null_dedupe_pk_hard_task, 
    
         run_dq_is_unique_dedupe_pk_hard_task) >>
    
            merge_stage_anvyl_orders_task >>
            
         ( run_dq_is_unique_merge_task, 
    
         run_dq_is_null_merge_pk_hard_task, 
    
         run_dq_is_unique_merge_pk_hard_task) >>
    
            run_audit_anvyl_orders_task >>
            end_insert_anvyl_orders_task >>
            end_anvyl_orders_task
        )

        skip_insert_anvyl_orders_task >> end_anvyl_orders_task

    #  --- Load anvyl_order_items ---
    
    with TaskGroup(group_id='load_anvyl_order_items') as tg_anvyl_order_items:
        list_s3_files_anvyl_order_items_task = PythonOperator(
            task_id="list_s3_files_anvyl_order_items",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "anvyl-data-ingestion/list_s3_anvyl_order_items.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_anvyl_order_items_task = DummyOperator(task_id="begin_insert_anvyl_order_items")
        skip_insert_anvyl_order_items_task = DummyOperator(task_id="skip_insert_anvyl_order_items")
        end_insert_anvyl_order_items_task = DummyOperator(task_id="end_insert_anvyl_order_items")
        end_anvyl_order_items_task = DummyOperator(
            task_id="end_anvyl_order_items",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_anvyl_order_items_task = BranchPythonOperator(
            task_id='check_new_files_found_anvyl_order_items',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_snowflake_anvyl_order_items.yaml",
                       "skip_task_id": "load_anvyl_order_items.skip_insert_anvyl_order_items",
                       "next_task_id": "load_anvyl_order_items.begin_insert_anvyl_order_items"
            },
        )

        s3_to_snowflake_anvyl_order_items_task = PythonOperator(
            task_id="s3_to_snowflake_anvyl_order_items",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_sf_raw_anvyl_order_items.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        

        insert_log_anvyl_order_items_task = PythonOperator(
            task_id="insert_log_anvyl_order_items",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_items/insert_log_anvyl_order_items.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_anvyl_order_items_task = PythonOperator(
            task_id="dedupe_anvyl_order_items",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_items/dedupe_anvyl_order_items.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_unique_dedupe_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_order_items',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_anvyl_order_items', 
                    field_list=['id','part_id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_order_items',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_anvyl_order_items', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_order_items',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_anvyl_order_items', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        merge_stage_anvyl_order_items_task = PythonOperator(
            task_id="merge_stage_anvyl_order_items",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_items/merge_stage_anvyl_order_items.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        
        run_dq_is_unique_merge_task = PythonOperator(
            task_id="run_dq_is_unique_merge",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_order_items',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_order_items',
                    field_list=['id','part_id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_order_items',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$anvyl_db.anvyl_order_items',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_order_items',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_order_items',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        run_audit_anvyl_order_items_task = PythonOperator(
            task_id="run_audit_anvyl_order_items",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$anvyl_db.anvyl_order_items", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        (
            list_s3_files_anvyl_order_items_task >>
            check_new_files_found_anvyl_order_items_task >>
            [begin_insert_anvyl_order_items_task, skip_insert_anvyl_order_items_task]
        )

        (
            begin_insert_anvyl_order_items_task >> 
            s3_to_snowflake_anvyl_order_items_task >>
            
            insert_log_anvyl_order_items_task >>
            dedupe_anvyl_order_items_task >>
            
         ( run_dq_is_unique_dedupe_task, 
    
         run_dq_is_null_dedupe_pk_hard_task, 
    
         run_dq_is_unique_dedupe_pk_hard_task) >>
    
            merge_stage_anvyl_order_items_task >>
            
         ( run_dq_is_unique_merge_task, 
    
         run_dq_is_null_merge_pk_hard_task, 
    
         run_dq_is_unique_merge_pk_hard_task) >>
    
            run_audit_anvyl_order_items_task >>
            end_insert_anvyl_order_items_task >>
            end_anvyl_order_items_task
        )

        skip_insert_anvyl_order_items_task >> end_anvyl_order_items_task

    #  --- Load anvyl_order_collections ---
    
    with TaskGroup(group_id='load_anvyl_order_collections') as tg_anvyl_order_collections:
        list_s3_files_anvyl_order_collections_task = PythonOperator(
            task_id="list_s3_files_anvyl_order_collections",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "anvyl-data-ingestion/list_s3_anvyl_order_collections.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_anvyl_order_collections_task = DummyOperator(task_id="begin_insert_anvyl_order_collections")
        skip_insert_anvyl_order_collections_task = DummyOperator(task_id="skip_insert_anvyl_order_collections")
        end_insert_anvyl_order_collections_task = DummyOperator(task_id="end_insert_anvyl_order_collections")
        end_anvyl_order_collections_task = DummyOperator(
            task_id="end_anvyl_order_collections",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_anvyl_order_collections_task = BranchPythonOperator(
            task_id='check_new_files_found_anvyl_order_collections',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_snowflake_anvyl_order_collections.yaml",
                       "skip_task_id": "load_anvyl_order_collections.skip_insert_anvyl_order_collections",
                       "next_task_id": "load_anvyl_order_collections.begin_insert_anvyl_order_collections"
            },
        )

        s3_to_snowflake_anvyl_order_collections_task = PythonOperator(
            task_id="s3_to_snowflake_anvyl_order_collections",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_sf_raw_anvyl_order_collections.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        

        insert_log_anvyl_order_collections_task = PythonOperator(
            task_id="insert_log_anvyl_order_collections",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_collections/insert_log_anvyl_order_collections.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_anvyl_order_collections_task = PythonOperator(
            task_id="dedupe_anvyl_order_collections",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_collections/dedupe_anvyl_order_collections.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_order_collections',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_anvyl_order_collections', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_order_collections',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_anvyl_order_collections', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        merge_stage_anvyl_order_collections_task = PythonOperator(
            task_id="merge_stage_anvyl_order_collections",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_collections/merge_stage_anvyl_order_collection.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        

        (
            list_s3_files_anvyl_order_collections_task >>
            check_new_files_found_anvyl_order_collections_task >>
            [begin_insert_anvyl_order_collections_task, skip_insert_anvyl_order_collections_task]
        )

        (
            begin_insert_anvyl_order_collections_task >> 
            s3_to_snowflake_anvyl_order_collections_task >>
            
            insert_log_anvyl_order_collections_task >>
            dedupe_anvyl_order_collections_task >>
            
         ( run_dq_is_null_dedupe_pk_hard_task, 
    
         run_dq_is_unique_dedupe_pk_hard_task) >>
    
            merge_stage_anvyl_order_collections_task >>

            end_insert_anvyl_order_collections_task >>
            end_anvyl_order_collections_task
        )

        skip_insert_anvyl_order_collections_task >> end_anvyl_order_collections_task

    #  --- Load anvyl_order_milestone ---
    
    with TaskGroup(group_id='load_anvyl_order_milestone') as tg_anvyl_order_milestone:
        list_s3_files_anvyl_order_milestone_task = PythonOperator(
            task_id="list_s3_files_anvyl_order_milestone",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "anvyl-data-ingestion/list_s3_anvyl_order_milestone.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_anvyl_order_milestone_task = DummyOperator(task_id="begin_insert_anvyl_order_milestone")
        skip_insert_anvyl_order_milestone_task = DummyOperator(task_id="skip_insert_anvyl_order_milestone")
        end_insert_anvyl_order_milestone_task = DummyOperator(task_id="end_insert_anvyl_order_milestone")
        end_anvyl_order_milestone_task = DummyOperator(
            task_id="end_anvyl_order_milestone",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_anvyl_order_milestone_task = BranchPythonOperator(
            task_id='check_new_files_found_anvyl_order_milestone',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_snowflake_anvyl_order_milestone.yaml",
                       "skip_task_id": "load_anvyl_order_milestone.skip_insert_anvyl_order_milestone",
                       "next_task_id": "load_anvyl_order_milestone.begin_insert_anvyl_order_milestone"
            },
        )

        s3_to_snowflake_anvyl_order_milestone_task = PythonOperator(
            task_id="s3_to_snowflake_anvyl_order_milestone",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_sf_raw_anvyl_order_milestone.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        

        insert_log_anvyl_order_milestone_task = PythonOperator(
            task_id="insert_log_anvyl_order_milestone",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_milestone/insert_log_anvyl_order_milestone.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_anvyl_order_milestone_task = PythonOperator(
            task_id="dedupe_anvyl_order_milestone",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_milestone/dedupe_anvyl_order_milestone.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_order_milestone',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_anvyl_order_milestone', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_order_milestone',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_anvyl_order_milestone', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        merge_stage_anvyl_order_milestone_task = PythonOperator(
            task_id="merge_stage_anvyl_order_milestone",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_milestone/merge_stage_anvyl_order_milestone.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_order_milestone',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$anvyl_db.anvyl_order_milestone',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_order_milestone',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_order_milestone',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        run_audit_anvyl_order_milestone_task = PythonOperator(
            task_id="run_audit_anvyl_order_milestone",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$anvyl_db.anvyl_order_milestone", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        (
            list_s3_files_anvyl_order_milestone_task >>
            check_new_files_found_anvyl_order_milestone_task >>
            [begin_insert_anvyl_order_milestone_task, skip_insert_anvyl_order_milestone_task]
        )

        (
            begin_insert_anvyl_order_milestone_task >> 
            s3_to_snowflake_anvyl_order_milestone_task >>
            
            insert_log_anvyl_order_milestone_task >>
            dedupe_anvyl_order_milestone_task >>
            
         ( run_dq_is_null_dedupe_pk_hard_task, 
    
         run_dq_is_unique_dedupe_pk_hard_task) >>
    
            merge_stage_anvyl_order_milestone_task >>
            
         ( run_dq_is_null_merge_pk_hard_task, 
    
         run_dq_is_unique_merge_pk_hard_task) >>
    
            run_audit_anvyl_order_milestone_task >>
            end_insert_anvyl_order_milestone_task >>
            end_anvyl_order_milestone_task
        )

        skip_insert_anvyl_order_milestone_task >> end_anvyl_order_milestone_task\

    #  --- Load anvyl_shipments ---

    with TaskGroup(group_id='load_anvyl_shipments') as tg_anvyl_shipments:
        list_s3_files_anvyl_shipments_task = PythonOperator(
            task_id="list_s3_files_anvyl_shipments",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "anvyl-data-ingestion/list_s3_anvyl_shipments.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_anvyl_shipments_task = DummyOperator(task_id="begin_insert_anvyl_shipments")
        skip_insert_anvyl_shipments_task = DummyOperator(task_id="skip_insert_anvyl_shipments")
        end_insert_anvyl_shipments_task = DummyOperator(task_id="end_insert_anvyl_shipments")
        end_anvyl_shipments_task = DummyOperator(
            task_id="end_anvyl_shipments",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_anvyl_shipments_task = BranchPythonOperator(
            task_id='check_new_files_found_anvyl_shipments',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_snowflake_anvyl_shipments.yaml",
                       "skip_task_id": "load_anvyl_shipments.skip_insert_anvyl_shipments",
                       "next_task_id": "load_anvyl_shipments.begin_insert_anvyl_shipments"
                       },
        )

        s3_to_snowflake_anvyl_shipments_task = PythonOperator(
            task_id="s3_to_snowflake_anvyl_shipments",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_sf_raw_anvyl_shipments.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_anvyl_shipments_task = PythonOperator(
            task_id="insert_log_anvyl_shipments",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/shipments/insert_log_anvyl_shipments.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_anvyl_shipments_task = PythonOperator(
            task_id="dedupe_anvyl_shipments",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/shipments/dedupe_anvyl_shipments.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_shipments',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_anvyl_shipments',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_shipments',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_anvyl_shipments',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_anvyl_shipments_task = PythonOperator(
            task_id="merge_stage_anvyl_shipments",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/shipments/merge_stage_anvyl_shipments.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_task = PythonOperator(
            task_id="run_dq_is_unique_merge",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_shipments',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_shipments',
                    field_list=['id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_shipments',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$anvyl_db.anvyl_shipments',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_shipments',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_shipments',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_anvyl_shipments_task = PythonOperator(
            task_id="run_audit_anvyl_shipments",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$anvyl_db.anvyl_shipments",  # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                list_s3_files_anvyl_shipments_task >>
                check_new_files_found_anvyl_shipments_task >>
                [begin_insert_anvyl_shipments_task, skip_insert_anvyl_shipments_task]
        )

        (
                begin_insert_anvyl_shipments_task >>
                s3_to_snowflake_anvyl_shipments_task >>

                insert_log_anvyl_shipments_task >>
                dedupe_anvyl_shipments_task >>

                (run_dq_is_null_dedupe_pk_hard_task,

                 run_dq_is_unique_dedupe_pk_hard_task) >>

                merge_stage_anvyl_shipments_task >>

                (run_dq_is_unique_merge_task,

                 run_dq_is_null_merge_pk_hard_task,

                 run_dq_is_unique_merge_pk_hard_task) >>

                run_audit_anvyl_shipments_task >>
                end_insert_anvyl_shipments_task >>
                end_anvyl_shipments_task
        )

        skip_insert_anvyl_shipments_task >> end_anvyl_shipments_task

    #  --- Load anvyl_ship_to_location ---

    with TaskGroup(group_id='load_anvyl_ship_to_location') as tg_anvyl_ship_to_location:
        list_s3_files_anvyl_ship_to_location_task = PythonOperator(
            task_id="list_s3_files_anvyl_ship_to_location",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "anvyl-data-ingestion/list_s3_anvyl_ship_to_location.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_anvyl_ship_to_location_task = DummyOperator(task_id="begin_insert_anvyl_ship_to_location")
        skip_insert_anvyl_ship_to_location_task = DummyOperator(task_id="skip_insert_anvyl_ship_to_location")
        end_insert_anvyl_ship_to_location_task = DummyOperator(task_id="end_insert_anvyl_ship_to_location")
        end_anvyl_ship_to_location_task = DummyOperator(
            task_id="end_anvyl_ship_to_location",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_anvyl_ship_to_location_task = BranchPythonOperator(
            task_id='check_new_files_found_anvyl_ship_to_location',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_snowflake_anvyl_ship_to_location.yaml",
                       "skip_task_id": "load_anvyl_ship_to_location.skip_insert_anvyl_ship_to_location",
                       "next_task_id": "load_anvyl_ship_to_location.begin_insert_anvyl_ship_to_location"
                       },
        )

        s3_to_snowflake_anvyl_ship_to_location_task = PythonOperator(
            task_id="s3_to_snowflake_anvyl_ship_to_location",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_sf_raw_anvyl_tracked_shipment.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_anvyl_ship_to_location_task = PythonOperator(
            task_id="insert_log_anvyl_ship_to_location",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/ship_to_location/insert_log_anvyl_ship_to_location.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_anvyl_ship_to_location_task = PythonOperator(
            task_id="dedupe_anvyl_ship_to_location",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/ship_to_location/dedupe_anvyl_ship_to_location.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_ship_to_location',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_anvyl_ship_to_location',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_ship_to_location',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_anvyl_ship_to_location',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_anvyl_ship_to_location_task = PythonOperator(
            task_id="merge_stage_anvyl_ship_to_location",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/ship_to_location/merge_stage_anvyl_ship_to_location.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_ship_to_location',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$anvyl_db.anvyl_ship_to_location',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_ship_to_location',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_ship_to_location',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_anvyl_ship_to_location_task = PythonOperator(
            task_id="run_audit_anvyl_ship_to_location",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$anvyl_db.anvyl_ship_to_location",  # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                list_s3_files_anvyl_ship_to_location_task >>
                check_new_files_found_anvyl_ship_to_location_task >>
                [begin_insert_anvyl_ship_to_location_task, skip_insert_anvyl_ship_to_location_task]
        )

        (
                begin_insert_anvyl_ship_to_location_task >>
                s3_to_snowflake_anvyl_ship_to_location_task >>

                insert_log_anvyl_ship_to_location_task >>
                dedupe_anvyl_ship_to_location_task >>

                (run_dq_is_null_dedupe_pk_hard_task,

                 run_dq_is_unique_dedupe_pk_hard_task) >>

                merge_stage_anvyl_ship_to_location_task >>

                (run_dq_is_null_merge_pk_hard_task,

                 run_dq_is_unique_merge_pk_hard_task) >>

                run_audit_anvyl_ship_to_location_task >>
                end_insert_anvyl_ship_to_location_task >>
                end_anvyl_ship_to_location_task
        )

        skip_insert_anvyl_ship_to_location_task >> end_anvyl_ship_to_location_task

    #  --- Load anvyl_tracked_shipment ---

    with TaskGroup(group_id='load_anvyl_tracked_shipment') as tg_anvyl_tracked_shipment:
        list_s3_files_anvyl_tracked_shipment_task = PythonOperator(
            task_id="list_s3_files_anvyl_tracked_shipment",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "anvyl-data-ingestion/list_s3_anvyl_tracked_shipment.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_anvyl_tracked_shipment_task = DummyOperator(task_id="begin_insert_anvyl_tracked_shipment")
        skip_insert_anvyl_tracked_shipment_task = DummyOperator(task_id="skip_insert_anvyl_tracked_shipment")
        end_insert_anvyl_tracked_shipment_task = DummyOperator(task_id="end_insert_anvyl_tracked_shipment")
        end_anvyl_tracked_shipment_task = DummyOperator(
            task_id="end_anvyl_tracked_shipment",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_anvyl_tracked_shipment_task = BranchPythonOperator(
            task_id='check_new_files_found_anvyl_tracked_shipment',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_snowflake_anvyl_tracked_shipment.yaml",
                       "skip_task_id": "load_anvyl_tracked_shipment.skip_insert_anvyl_tracked_shipment",
                       "next_task_id": "load_anvyl_tracked_shipment.begin_insert_anvyl_tracked_shipment"
                       },
        )

        s3_to_snowflake_anvyl_tracked_shipment_task = PythonOperator(
            task_id="s3_to_snowflake_anvyl_tracked_shipment",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_sf_raw_anvyl_tracked_shipment.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_anvyl_tracked_shipment_task = PythonOperator(
            task_id="insert_log_anvyl_tracked_shipment",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/tracked_shipment/insert_log_anvyl_tracked_shipment.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_anvyl_tracked_shipment_task = PythonOperator(
            task_id="dedupe_anvyl_tracked_shipment",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/tracked_shipment/dedupe_anvyl_tracked_shipment.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_tracked_shipment',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_anvyl_tracked_shipment',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_tracked_shipment',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_anvyl_tracked_shipment',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_anvyl_tracked_shipment_task = PythonOperator(
            task_id="merge_stage_anvyl_tracked_shipment",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/tracked_shipment/merge_stage_anvyl_tracked_shipment.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_tracked_shipment',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$anvyl_db.anvyl_tracked_shipment',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_tracked_shipment',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_tracked_shipment',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_anvyl_tracked_shipment_task = PythonOperator(
            task_id="run_audit_anvyl_tracked_shipment",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$anvyl_db.anvyl_tracked_shipment",  # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                list_s3_files_anvyl_tracked_shipment_task >>
                check_new_files_found_anvyl_tracked_shipment_task >>
                [begin_insert_anvyl_tracked_shipment_task, skip_insert_anvyl_tracked_shipment_task]
        )

        (
                begin_insert_anvyl_tracked_shipment_task >>
                s3_to_snowflake_anvyl_tracked_shipment_task >>

                insert_log_anvyl_tracked_shipment_task >>
                dedupe_anvyl_tracked_shipment_task >>

                (run_dq_is_null_dedupe_pk_hard_task,

                 run_dq_is_unique_dedupe_pk_hard_task) >>

                merge_stage_anvyl_tracked_shipment_task >>

                (run_dq_is_null_merge_pk_hard_task,

                 run_dq_is_unique_merge_pk_hard_task) >>

                run_audit_anvyl_tracked_shipment_task >>
                end_insert_anvyl_tracked_shipment_task >>
                end_anvyl_tracked_shipment_task
        )

        skip_insert_anvyl_tracked_shipment_task >> end_anvyl_tracked_shipment_task

    #  --- Load anvyl_custom_fields ---

    with TaskGroup(group_id='load_anvyl_custom_fields') as tg_anvyl_custom_fields:
        list_s3_files_anvyl_custom_fields_task = PythonOperator(
            task_id="list_s3_files_anvyl_custom_fields",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "anvyl-data-ingestion/list_s3_anvyl_custom_fields.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_anvyl_custom_fields_task = DummyOperator(task_id="begin_insert_anvyl_custom_fields")
        skip_insert_anvyl_custom_fields_task = DummyOperator(task_id="skip_insert_anvyl_custom_fields")
        end_insert_anvyl_custom_fields_task = DummyOperator(task_id="end_insert_anvyl_custom_fields")
        end_anvyl_custom_fields_task = DummyOperator(
            task_id="end_anvyl_custom_fields",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_anvyl_custom_fields_task = BranchPythonOperator(
            task_id='check_new_files_found_anvyl_custom_fields',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_snowflake_anvyl_custom_fields.yaml",
                       "skip_task_id": "load_anvyl_custom_fields.skip_insert_anvyl_custom_fields",
                       "next_task_id": "load_anvyl_custom_fields.begin_insert_anvyl_custom_fields"
                       },
        )

        s3_to_snowflake_anvyl_custom_fields_task = PythonOperator(
            task_id="s3_to_snowflake_anvyl_custom_fields",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_sf_raw_anvyl_custom_fields.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_anvyl_custom_fields_task = PythonOperator(
            task_id="insert_log_anvyl_custom_fields",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/custom_field/insert_log_anvyl_custom_fields.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_anvyl_custom_fields_task = PythonOperator(
            task_id="dedupe_anvyl_custom_fields",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/custom_field/dedupe_anvyl_custom_fields.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_custom_fields',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_anvyl_custom_fields',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_custom_fields',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_anvyl_custom_fields',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_anvyl_custom_fields_task = PythonOperator(
            task_id="merge_stage_anvyl_custom_fields",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/custom_field/merge_stage_anvyl_custom_fields.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_custom_fields',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$anvyl_db.anvyl_custom_fields',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_custom_fields',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_custom_fields',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_anvyl_custom_fields_task = PythonOperator(
            task_id="run_audit_anvyl_custom_fields",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$anvyl_db.anvyl_custom_fields",  # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                list_s3_files_anvyl_custom_fields_task >>
                check_new_files_found_anvyl_custom_fields_task >>
                [begin_insert_anvyl_custom_fields_task, skip_insert_anvyl_custom_fields_task]
        )

        (
                begin_insert_anvyl_custom_fields_task >>
                s3_to_snowflake_anvyl_custom_fields_task >>

                insert_log_anvyl_custom_fields_task >>
                dedupe_anvyl_custom_fields_task >>

                (run_dq_is_null_dedupe_pk_hard_task,

                 run_dq_is_unique_dedupe_pk_hard_task) >>

                merge_stage_anvyl_custom_fields_task >>

                (run_dq_is_null_merge_pk_hard_task,

                 run_dq_is_unique_merge_pk_hard_task) >>

                run_audit_anvyl_custom_fields_task >>
                end_insert_anvyl_custom_fields_task >>
                end_anvyl_custom_fields_task
        )

        skip_insert_anvyl_custom_fields_task >> end_anvyl_custom_fields_task

    with TaskGroup(group_id='load_anvyl_single_order') as tg_anvyl_single_order:
        list_s3_files_anvyl_single_order_task = PythonOperator(
            task_id="list_s3_files_anvyl_single_order",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "anvyl-data-ingestion/list_s3_anvyl_single_order.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_anvyl_single_order_task = DummyOperator(task_id="begin_insert_anvyl_single_order")
        skip_insert_anvyl_single_order_task = DummyOperator(task_id="skip_insert_anvyl_single_order")
        end_insert_anvyl_single_order_task = DummyOperator(task_id="end_insert_anvyl_single_order")
        end_anvyl_single_order_task = DummyOperator(
            task_id="end_anvyl_single_order",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_anvyl_single_order_task = BranchPythonOperator(
            task_id='check_new_files_found_anvyl_single_order',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_snowflake_anvyl_single_order.yaml",
                       "skip_task_id": "load_anvyl_single_order.skip_insert_anvyl_single_order",
                       "next_task_id": "load_anvyl_single_order.begin_insert_anvyl_single_order"
                       },
        )

        s3_to_snowflake_anvyl_single_order_task = PythonOperator(
            task_id="s3_to_snowflake_anvyl_single_order",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_sf_raw_anvyl_single_order.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_anvyl_single_order_task = PythonOperator(
            task_id="insert_log_anvyl_single_order",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/single_order/insert_log_anvyl_single_order.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_anvyl_single_order_task = PythonOperator(
            task_id="dedupe_anvyl_single_order",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/single_order/dedupe_anvyl_single_order.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_single_order',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_anvyl_single_order',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_single_order',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_anvyl_single_order',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_anvyl_single_order_task = PythonOperator(
            task_id="merge_stage_anvyl_single_order",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/single_order/merge_stage_anvyl_single_order.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_single_order',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$anvyl_db.anvyl_single_order',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_single_order',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_single_order',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_anvyl_single_order_task = PythonOperator(
            task_id="run_audit_anvyl_single_order",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$anvyl_db.anvyl_single_order",  # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                list_s3_files_anvyl_single_order_task >>
                check_new_files_found_anvyl_single_order_task >>
                [begin_insert_anvyl_single_order_task, skip_insert_anvyl_single_order_task]
        )

        (
                begin_insert_anvyl_single_order_task >>
                s3_to_snowflake_anvyl_single_order_task >>

                insert_log_anvyl_single_order_task >>
                dedupe_anvyl_single_order_task >>

                (run_dq_is_null_dedupe_pk_hard_task,

                 run_dq_is_unique_dedupe_pk_hard_task) >>

                merge_stage_anvyl_single_order_task >>

                (run_dq_is_null_merge_pk_hard_task,

                 run_dq_is_unique_merge_pk_hard_task) >>

                run_audit_anvyl_single_order_task >>
                end_insert_anvyl_single_order_task >>
                end_anvyl_single_order_task
        )

        skip_insert_anvyl_single_order_task >> end_anvyl_single_order_task

    #  --- Load anvyl_single_parts ---

    with TaskGroup(group_id='load_anvyl_single_parts') as tg_anvyl_single_parts:
        list_s3_files_anvyl_single_parts_task = PythonOperator(
            task_id="list_s3_files_anvyl_single_parts",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "anvyl-data-ingestion/list_s3_anvyl_single_parts.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_anvyl_single_parts_task = DummyOperator(task_id="begin_insert_anvyl_single_parts")
        skip_insert_anvyl_single_parts_task = DummyOperator(task_id="skip_insert_anvyl_single_parts")
        end_insert_anvyl_single_parts_task = DummyOperator(task_id="end_insert_anvyl_single_parts")
        end_anvyl_single_parts_task = DummyOperator(
            task_id="end_anvyl_single_parts",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_anvyl_single_parts_task = BranchPythonOperator(
            task_id='check_new_files_found_anvyl_single_parts',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_snowflake_anvyl_single_parts.yaml",
                       "skip_task_id": "load_anvyl_single_parts.skip_insert_anvyl_single_parts",
                       "next_task_id": "load_anvyl_single_parts.begin_insert_anvyl_single_parts"
                       },
        )

        s3_to_snowflake_anvyl_single_parts_task = PythonOperator(
            task_id="s3_to_snowflake_anvyl_single_parts",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_sf_raw_anvyl_single_parts.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_anvyl_single_parts_task = PythonOperator(
            task_id="insert_log_anvyl_single_parts",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/single_parts/insert_log_anvyl_single_parts.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_anvyl_single_parts_task = PythonOperator(
            task_id="dedupe_anvyl_single_parts",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/single_parts/dedupe_anvyl_single_parts.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_single_parts',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_anvyl_single_parts',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_single_parts',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_anvyl_single_parts',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_anvyl_single_parts_task = PythonOperator(
            task_id="merge_stage_anvyl_single_parts",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/single_parts/merge_stage_anvyl_single_parts.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_single_parts',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$anvyl_db.anvyl_single_parts',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_single_parts',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_single_parts',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_anvyl_single_parts_task = PythonOperator(
            task_id="run_audit_anvyl_single_parts",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$anvyl_db.anvyl_single_parts",  # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                list_s3_files_anvyl_single_parts_task >>
                check_new_files_found_anvyl_single_parts_task >>
                [begin_insert_anvyl_single_parts_task, skip_insert_anvyl_single_parts_task]
        )

        (
                begin_insert_anvyl_single_parts_task >>
                s3_to_snowflake_anvyl_single_parts_task >>

                insert_log_anvyl_single_parts_task >>
                dedupe_anvyl_single_parts_task >>

                (run_dq_is_null_dedupe_pk_hard_task,

                 run_dq_is_unique_dedupe_pk_hard_task) >>

                merge_stage_anvyl_single_parts_task >>

                (run_dq_is_null_merge_pk_hard_task,

                 run_dq_is_unique_merge_pk_hard_task) >>

                run_audit_anvyl_single_parts_task >>
                end_insert_anvyl_single_parts_task >>
                end_anvyl_single_parts_task
        )

        skip_insert_anvyl_single_parts_task >> end_anvyl_single_parts_task

    #  --- Load anvyl_shipment_routes ---

    with TaskGroup(group_id='load_anvyl_shipment_routes') as tg_anvyl_shipment_routes:
        list_s3_files_anvyl_shipment_routes_task = PythonOperator(
            task_id="list_s3_files_anvyl_shipment_routes",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "anvyl-data-ingestion/list_s3_anvyl_shipment_routes.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_anvyl_shipment_routes_task = DummyOperator(task_id="begin_insert_anvyl_shipment_routes")
        skip_insert_anvyl_shipment_routes_task = DummyOperator(task_id="skip_insert_anvyl_shipment_routes")
        end_insert_anvyl_shipment_routes_task = DummyOperator(task_id="end_insert_anvyl_shipment_routes")
        end_anvyl_shipment_routes_task = DummyOperator(
            task_id="end_anvyl_shipment_routes",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_anvyl_shipment_routes_task = BranchPythonOperator(
            task_id='check_new_files_found_anvyl_shipment_routes',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_snowflake_anvyl_shipment_routes.yaml",
                       "skip_task_id": "load_anvyl_shipment_routes.skip_insert_anvyl_shipment_routes",
                       "next_task_id": "load_anvyl_shipment_routes.begin_insert_anvyl_shipment_routes"
                       },
        )

        s3_to_snowflake_anvyl_shipment_routes_task = PythonOperator(
            task_id="s3_to_snowflake_anvyl_shipment_routes",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_sf_raw_anvyl_shipment_routes.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_anvyl_shipment_routes_task = PythonOperator(
            task_id="insert_log_anvyl_shipment_routes",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/shipment_routes/insert_log_anvyl_shipment_routes.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_anvyl_shipment_routes_task = PythonOperator(
            task_id="dedupe_anvyl_shipment_routes",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/shipment_routes/dedupe_anvyl_shipment_routes.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_shipment_routes',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_anvyl_shipment_routes',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_shipment_routes',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_anvyl_shipment_routes',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_anvyl_shipment_routes_task = PythonOperator(
            task_id="merge_stage_anvyl_shipment_routes",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/shipment_routes/merge_stage_anvyl_shipment_routes.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_shipment_routes',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$anvyl_db.anvyl_shipment_routes',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_shipment_routes',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_shipment_routes',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_anvyl_shipment_routes_task = PythonOperator(
            task_id="run_audit_anvyl_shipment_routes",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$anvyl_db.anvyl_shipment_routes",  # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                list_s3_files_anvyl_shipment_routes_task >>
                check_new_files_found_anvyl_shipment_routes_task >>
                [begin_insert_anvyl_shipment_routes_task, skip_insert_anvyl_shipment_routes_task]
        )

        (
                begin_insert_anvyl_shipment_routes_task >>
                s3_to_snowflake_anvyl_shipment_routes_task >>

                insert_log_anvyl_shipment_routes_task >>
                dedupe_anvyl_shipment_routes_task >>

                (run_dq_is_null_dedupe_pk_hard_task,

                 run_dq_is_unique_dedupe_pk_hard_task) >>

                merge_stage_anvyl_shipment_routes_task >>

                (run_dq_is_null_merge_pk_hard_task,

                 run_dq_is_unique_merge_pk_hard_task) >>

                run_audit_anvyl_shipment_routes_task >>
                end_insert_anvyl_shipment_routes_task >>
                end_anvyl_shipment_routes_task
        )

        skip_insert_anvyl_shipment_routes_task >> end_anvyl_shipment_routes_task

    #  --- Load anvyl_parts ---

    with TaskGroup(group_id='load_anvyl_parts') as tg_anvyl_parts:
        list_s3_files_anvyl_parts_task = PythonOperator(
            task_id="list_s3_files_anvyl_parts",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "anvyl-data-ingestion/list_s3_anvyl_parts.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_anvyl_parts_task = DummyOperator(task_id="begin_insert_anvyl_parts")
        skip_insert_anvyl_parts_task = DummyOperator(task_id="skip_insert_anvyl_parts")
        end_insert_anvyl_parts_task = DummyOperator(task_id="end_insert_anvyl_parts")
        end_anvyl_parts_task = DummyOperator(
            task_id="end_anvyl_parts",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_anvyl_parts_task = BranchPythonOperator(
            task_id='check_new_files_found_anvyl_parts',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_snowflake_anvyl_parts.yaml",
                       "skip_task_id": "load_anvyl_parts.skip_insert_anvyl_parts",
                       "next_task_id": "load_anvyl_parts.begin_insert_anvyl_parts"
                       },
        )

        s3_to_snowflake_anvyl_parts_task = PythonOperator(
            task_id="s3_to_snowflake_anvyl_parts",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "anvyl-data-ingestion/s3_to_sf_raw_anvyl_parts.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_anvyl_parts_task = PythonOperator(
            task_id="insert_log_anvyl_parts",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_parts/insert_log_anvyl_parts.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_anvyl_parts_task = PythonOperator(
            task_id="dedupe_anvyl_parts",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_parts/dedupe_anvyl_parts.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_parts',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_anvyl_parts',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_anvyl_parts',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_anvyl_parts',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_anvyl_parts_task = PythonOperator(
            task_id="merge_stage_anvyl_parts",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_parts/merge_stage_anvyl_parts.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_parts',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$anvyl_db.anvyl_parts',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_parts',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_parts',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_anvyl_parts_task = PythonOperator(
            task_id="run_audit_anvyl_parts",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$anvyl_db.anvyl_parts",  # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                list_s3_files_anvyl_parts_task >>
                check_new_files_found_anvyl_parts_task >>
                [begin_insert_anvyl_parts_task, skip_insert_anvyl_parts_task]
        )

        (
                begin_insert_anvyl_parts_task >>
                s3_to_snowflake_anvyl_parts_task >>

                insert_log_anvyl_parts_task >>
                dedupe_anvyl_parts_task >>

                (run_dq_is_null_dedupe_pk_hard_task,

                 run_dq_is_unique_dedupe_pk_hard_task) >>

                merge_stage_anvyl_parts_task >>

                (run_dq_is_null_merge_pk_hard_task,

                 run_dq_is_unique_merge_pk_hard_task) >>

                run_audit_anvyl_parts_task >>
                end_insert_anvyl_parts_task >>
                end_anvyl_parts_task
        )

        skip_insert_anvyl_parts_task >> end_anvyl_parts_task

    #  --- Load order unified ---
    with TaskGroup(group_id='load_order_unified') as tg_anvyl_order_unified:

        # Need a begin dummy operator for branching
        begin_insert_anvyl_order_unified_task = DummyOperator(task_id="begin_insert_anvyl_order_unified")
        end_anvyl_order_unified_task = DummyOperator(
            task_id="end_anvyl_order_unified",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )


        stg_anvyl_order_unified_task = PythonOperator(
            task_id="stg_anvyl_order_unified",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_unified/stg_order_shipment_unified.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.stg_anvyl_order_unified',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.stg_anvyl_order_unified',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.stg_anvyl_order_unified',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.stg_anvyl_order_unified',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_anvyl_order_unified_task = PythonOperator(
            task_id="merge_stage_anvyl_order_unified",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_unified/merge_order_shipment_unified.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        update_anvyl_order_unified_task = PythonOperator(
            task_id="update_anvyl_order_unified",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "anvyl-data-ingestion/order_unified/update_anvyl_order_unified.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_inbound_shipment',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$anvyl_db.anvyl_inbound_shipment',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$anvyl_db.anvyl_inbound_shipment',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$anvyl_db.anvyl_inbound_shipment',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        # create_trim_procedure_task = PythonOperator(
        #     task_id="create_trim_procedure",
        #     python_callable=tlsql.run_query_file,
        #     op_kwargs={
        #         "connection": "Snowflake",
        #         "sql_file": "anvyl-data-ingestion/order_unified/create_trim_procedure.sql",
        #         "wf_params": WF_PARAMS_EXPR
        #     },
        #     provide_context=True,
        #     on_failure_callback=alerts.send_failure_alert,
        # )

        trim_anvyl_inbound_shipment_unified_task = PythonOperator(
            task_id="trim_anvyl_inbound_shipment_unified",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "anvyl-data-ingestion/order_unified/trim_anvyl_inbound_shipment_unified.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_anvyl_order_unified_task = PythonOperator(
            task_id="run_audit_anvyl_order_unified",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$anvyl_db.anvyl_inbound_shipment",  # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )


        (
                begin_insert_anvyl_order_unified_task >>

                stg_anvyl_order_unified_task >>

                (run_dq_is_null_dedupe_pk_hard_task,

                  run_dq_is_unique_dedupe_pk_hard_task) >>

                merge_stage_anvyl_order_unified_task >>

                (run_dq_is_null_merge_pk_hard_task,

                 run_dq_is_unique_merge_pk_hard_task) >>
                update_anvyl_order_unified_task >>
                # create_trim_procedure_task >>
                trim_anvyl_inbound_shipment_unified_task >>
                run_audit_anvyl_order_unified_task >>
                end_anvyl_order_unified_task
        )


    # ---- Main branch ----
    chain(
       begin,
       get_workflow_parameters,
       [
        tg_anvyl_orders,
        tg_anvyl_order_items,
        tg_anvyl_order_collections,
        tg_anvyl_order_milestone,
        tg_anvyl_shipments,
        tg_anvyl_ship_to_location,
        tg_anvyl_tracked_shipment,
        tg_anvyl_custom_fields,
        tg_anvyl_single_order,
        tg_anvyl_single_parts,
        tg_anvyl_shipment_routes,
        tg_anvyl_parts
        ],
        tg_anvyl_order_unified,
       update_workflow_parameters,
       end
    )
    
"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.utils.task_group import TaskGroup
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON>ythonOperator, PythonOperator
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib.loader import check_for_new_files
from tasklib import alerts

BUILD_NUM = '1'
DAG_ID = 'amazon_search_term_keyword_report'
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2021, 1, 1),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'AMAZON_SEARCH_TERM_KEYWORD_REPORT',
            'author': 'prashanjeet'},
    tags=['prashanjeet', 'Raptor']
) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
        
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_params = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load amazon_sb_search_term_keyword_report ---
    
    with TaskGroup(group_id='load_amazon_sb_search_term_keyword_report') as tg_amazon_sb_search_term_keyword_report:
        list_s3_files_amazon_sb_search_term_keyword_report_task = PythonOperator(
            task_id="list_s3_files_amazon_sb_search_term_keyword_report",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_search_term_keyword_report/list_s3_amazon_sb_search_term_keyword_report.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            
        )

        # Need a begin dummy operator for branching
        begin_insert_amazon_sb_search_term_keyword_report_task = DummyOperator(task_id="begin_insert_amazon_sb_search_term_keyword_report")
        skip_insert_amazon_sb_search_term_keyword_report_task = DummyOperator(task_id="skip_insert_amazon_sb_search_term_keyword_report")
        end_insert_amazon_sb_search_term_keyword_report_task = DummyOperator(task_id="end_insert_amazon_sb_search_term_keyword_report")

        from tasklib.loader import S3ToSnowflake
        load_obj = S3ToSnowflake()
        s3_to_snowflake_amazon_sb_search_term_keyword_report_task = PythonOperator(
            task_id="s3_to_snowflake_amazon_sb_search_term_keyword_report",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_search_term_keyword_report/s3_to_sf_raw_amazon_sb_search_term_keyword_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amazon_sb_search_term_keyword_report_task = BranchPythonOperator(
            task_id='check_new_files_found_amazon_sb_search_term_keyword_report',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_search_term_keyword_report/s3_to_snowflake_amazon_sb_search_term_keyword_report.yaml",
                       "skip_task_id": "load_amazon_sb_search_term_keyword_report.skip_insert_amazon_sb_search_term_keyword_report",
                       "next_task_id": "load_amazon_sb_search_term_keyword_report.begin_insert_amazon_sb_search_term_keyword_report"
            },
        )

        insert_log_amazon_sb_search_term_keyword_report_task = PythonOperator(
            task_id="insert_log_amazon_sb_search_term_keyword_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/insert_log_amazon_sb_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amazon_sb_search_term_keyword_report_task = PythonOperator(
            task_id="dedupe_amazon_sb_search_term_keyword_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/dedupe_amazon_sb_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amazon_sb_search_term_keyword_report_task = PythonOperator(
            task_id="merge_stage_amazon_sb_search_term_keyword_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/merge_stage_amazon_sb_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amazon_sb_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amazon_sb_search_term_keyword_report', 
                    field_list=['reportdate', 'adgroupid', 'accountid', 'campaignid'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_sb_search_term_keyword_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amazon_sb_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amazon_sb_search_term_keyword_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amazon_sb_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_sb_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amazon_sb_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amazon_sb_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amazon_sb_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amazon_sb_search_term_keyword_report_task = PythonOperator(
            task_id="run_audit_amazon_sb_search_term_keyword_report",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amazon_sb_search_term_keyword_report", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            list_s3_files_amazon_sb_search_term_keyword_report_task >>
            check_new_files_found_amazon_sb_search_term_keyword_report_task >>
            [begin_insert_amazon_sb_search_term_keyword_report_task, skip_insert_amazon_sb_search_term_keyword_report_task]
        )

        (
            begin_insert_amazon_sb_search_term_keyword_report_task >> 
            s3_to_snowflake_amazon_sb_search_term_keyword_report_task >>
            insert_log_amazon_sb_search_term_keyword_report_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_amazon_sb_search_term_keyword_report_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_amazon_sb_search_term_keyword_report_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_amazon_sb_search_term_keyword_report_task >>
            end_insert_amazon_sb_search_term_keyword_report_task
        )
    
    #  --- Load amazon_sb_search_term_keyword_report_goessor ---
    
    with TaskGroup(group_id='load_amazon_sb_search_term_keyword_report_goessor') as tg_amazon_sb_search_term_keyword_report_goessor:
        list_s3_files_amazon_sb_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="list_s3_files_amazon_sb_search_term_keyword_report_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_search_term_keyword_report/list_s3_amazon_sb_search_term_keyword_report_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            
        )

        # Need a begin dummy operator for branching
        begin_insert_amazon_sb_search_term_keyword_report_goessor_task = DummyOperator(task_id="begin_insert_amazon_sb_search_term_keyword_report_goessor")
        skip_insert_amazon_sb_search_term_keyword_report_goessor_task = DummyOperator(task_id="skip_insert_amazon_sb_search_term_keyword_report_goessor")
        end_insert_amazon_sb_search_term_keyword_report_goessor_task = DummyOperator(task_id="end_insert_amazon_sb_search_term_keyword_report_goessor")

        from tasklib.loader import S3ToSnowflake
        load_obj = S3ToSnowflake()
        s3_to_snowflake_amazon_sb_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="s3_to_snowflake_amazon_sb_search_term_keyword_report_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_search_term_keyword_report/s3_to_sf_raw_amazon_sb_search_term_keyword_report_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amazon_sb_search_term_keyword_report_goessor_task = BranchPythonOperator(
            task_id='check_new_files_found_amazon_sb_search_term_keyword_report_goessor',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_search_term_keyword_report/s3_to_snowflake_amazon_sb_search_term_keyword_report_goessor.yaml",
                       "skip_task_id": "load_amazon_sb_search_term_keyword_report_goessor.skip_insert_amazon_sb_search_term_keyword_report_goessor",
                       "next_task_id": "load_amazon_sb_search_term_keyword_report_goessor.begin_insert_amazon_sb_search_term_keyword_report_goessor"
            },
        )

        insert_log_amazon_sb_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="insert_log_amazon_sb_search_term_keyword_report_goessor",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/insert_log_amazon_sb_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amazon_sb_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="dedupe_amazon_sb_search_term_keyword_report_goessor",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/dedupe_amazon_sb_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amazon_sb_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="merge_stage_amazon_sb_search_term_keyword_report_goessor",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/merge_stage_amazon_sb_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amazon_sb_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amazon_sb_search_term_keyword_report', 
                    field_list=['reportdate', 'adgroupid', 'accountid', 'campaignid'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_sb_search_term_keyword_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amazon_sb_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amazon_sb_search_term_keyword_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amazon_sb_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_sb_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amazon_sb_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amazon_sb_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amazon_sb_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amazon_sb_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="run_audit_amazon_sb_search_term_keyword_report_goessor",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amazon_sb_search_term_keyword_report", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            list_s3_files_amazon_sb_search_term_keyword_report_goessor_task >>
            check_new_files_found_amazon_sb_search_term_keyword_report_goessor_task >>
            [begin_insert_amazon_sb_search_term_keyword_report_goessor_task, skip_insert_amazon_sb_search_term_keyword_report_goessor_task]
        )

        (
            begin_insert_amazon_sb_search_term_keyword_report_goessor_task >> 
            s3_to_snowflake_amazon_sb_search_term_keyword_report_goessor_task >>
            insert_log_amazon_sb_search_term_keyword_report_goessor_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_amazon_sb_search_term_keyword_report_goessor_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_amazon_sb_search_term_keyword_report_goessor_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_amazon_sb_search_term_keyword_report_goessor_task >>
            end_insert_amazon_sb_search_term_keyword_report_goessor_task
        )

    #  --- Load amazon_sp_search_term_keyword_report ---
    
    with TaskGroup(group_id='load_amazon_sp_search_term_keyword_report') as tg_amazon_sp_search_term_keyword_report:
        list_s3_files_amazon_sp_search_term_keyword_report_task = PythonOperator(
            task_id="list_s3_files_amazon_sp_search_term_keyword_report",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_search_term_keyword_report/list_s3_amazon_sp_search_term_keyword_report.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_amazon_sp_search_term_keyword_report_task = DummyOperator(task_id="begin_insert_amazon_sp_search_term_keyword_report")
        skip_insert_amazon_sp_search_term_keyword_report_task = DummyOperator(task_id="skip_insert_amazon_sp_search_term_keyword_report")
        end_insert_amazon_sp_search_term_keyword_report_task = DummyOperator(task_id="end_insert_amazon_sp_search_term_keyword_report")

        from tasklib.loader import S3ToSnowflake
        load_obj = S3ToSnowflake()
        s3_to_snowflake_amazon_sp_search_term_keyword_report_task = PythonOperator(
            task_id="s3_to_snowflake_amazon_sp_search_term_keyword_report",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_search_term_keyword_report/s3_to_sf_raw_amazon_sp_search_term_keyword_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amazon_sp_search_term_keyword_report_task = BranchPythonOperator(
            task_id='check_new_files_found_amazon_sp_search_term_keyword_report',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_search_term_keyword_report/s3_to_snowflake_amazon_sp_search_term_keyword_report.yaml",
                       "skip_task_id": "load_amazon_sp_search_term_keyword_report.skip_insert_amazon_sp_search_term_keyword_report",
                       "next_task_id": "load_amazon_sp_search_term_keyword_report.begin_insert_amazon_sp_search_term_keyword_report"
            },
        )

        insert_log_amazon_sp_search_term_keyword_report_task = PythonOperator(
            task_id="insert_log_amazon_sp_search_term_keyword_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/insert_log_amazon_sp_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amazon_sp_search_term_keyword_report_task = PythonOperator(
            task_id="dedupe_amazon_sp_search_term_keyword_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/dedupe_amazon_sp_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amazon_sp_search_term_keyword_report_task = PythonOperator(
            task_id="merge_stage_amazon_sp_search_term_keyword_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/merge_stage_amazon_sp_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amazon_sp_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amazon_sp_search_term_keyword_report', 
                    field_list=['reportdate', 'adgroupid', 'accountid', 'campaignid'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_sp_search_term_keyword_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amazon_sp_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amazon_sp_search_term_keyword_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amazon_sp_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_sp_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amazon_sp_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amazon_sp_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amazon_sp_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amazon_sp_search_term_keyword_report_task = PythonOperator(
            task_id="run_audit_amazon_sp_search_term_keyword_report",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amazon_sp_search_term_keyword_report", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            list_s3_files_amazon_sp_search_term_keyword_report_task >>
            check_new_files_found_amazon_sp_search_term_keyword_report_task >>
            [begin_insert_amazon_sp_search_term_keyword_report_task, skip_insert_amazon_sp_search_term_keyword_report_task]
        )

        (
            begin_insert_amazon_sp_search_term_keyword_report_task >> 
            s3_to_snowflake_amazon_sp_search_term_keyword_report_task >>
            insert_log_amazon_sp_search_term_keyword_report_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_amazon_sp_search_term_keyword_report_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_amazon_sp_search_term_keyword_report_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_amazon_sp_search_term_keyword_report_task >>
            end_insert_amazon_sp_search_term_keyword_report_task
        )

    #  --- Load amazon_sp_search_term_keyword_report_goessor ---
    
    with TaskGroup(group_id='load_amazon_sp_search_term_keyword_report_goessor') as tg_amazon_sp_search_term_keyword_report_goessor:
        list_s3_files_amazon_sp_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="list_s3_files_amazon_sp_search_term_keyword_report_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_search_term_keyword_report/list_s3_amazon_sp_search_term_keyword_report_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_amazon_sp_search_term_keyword_report_goessor_task = DummyOperator(task_id="begin_insert_amazon_sp_search_term_keyword_report_goessor")
        skip_insert_amazon_sp_search_term_keyword_report_goessor_task = DummyOperator(task_id="skip_insert_amazon_sp_search_term_keyword_report_goessor")
        end_insert_amazon_sp_search_term_keyword_report_goessor_task = DummyOperator(task_id="end_insert_amazon_sp_search_term_keyword_report_goessor")

        from tasklib.loader import S3ToSnowflake
        load_obj = S3ToSnowflake()
        s3_to_snowflake_amazon_sp_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="s3_to_snowflake_amazon_sp_search_term_keyword_report_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_search_term_keyword_report/s3_to_sf_raw_amazon_sp_search_term_keyword_report_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amazon_sp_search_term_keyword_report_goessor_task = BranchPythonOperator(
            task_id='check_new_files_found_amazon_sp_search_term_keyword_report_goessor',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_search_term_keyword_report/s3_to_snowflake_amazon_sp_search_term_keyword_report_goessor.yaml",
                       "skip_task_id": "load_amazon_sp_search_term_keyword_report_goessor.skip_insert_amazon_sp_search_term_keyword_report_goessor",
                       "next_task_id": "load_amazon_sp_search_term_keyword_report_goessor.begin_insert_amazon_sp_search_term_keyword_report_goessor"
            },
        )

        insert_log_amazon_sp_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="insert_log_amazon_sp_search_term_keyword_report_goessor",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/insert_log_amazon_sp_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amazon_sp_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="dedupe_amazon_sp_search_term_keyword_report_goessor",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/dedupe_amazon_sp_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amazon_sp_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="merge_stage_amazon_sp_search_term_keyword_report_goessor",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/merge_stage_amazon_sp_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amazon_sp_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amazon_sp_search_term_keyword_report', 
                    field_list=['reportdate', 'adgroupid', 'accountid', 'campaignid'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_sp_search_term_keyword_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amazon_sp_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amazon_sp_search_term_keyword_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amazon_sp_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_sp_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amazon_sp_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amazon_sp_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amazon_sp_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amazon_sp_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="run_audit_amazon_sp_search_term_keyword_report_goessor",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amazon_sp_search_term_keyword_report", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            list_s3_files_amazon_sp_search_term_keyword_report_goessor_task >>
            check_new_files_found_amazon_sp_search_term_keyword_report_goessor_task >>
            [begin_insert_amazon_sp_search_term_keyword_report_goessor_task, skip_insert_amazon_sp_search_term_keyword_report_goessor_task]
        )

        (
            begin_insert_amazon_sp_search_term_keyword_report_goessor_task >> 
            s3_to_snowflake_amazon_sp_search_term_keyword_report_goessor_task >>
            insert_log_amazon_sp_search_term_keyword_report_goessor_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_amazon_sp_search_term_keyword_report_goessor_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_amazon_sp_search_term_keyword_report_goessor_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_amazon_sp_search_term_keyword_report_goessor_task >>
            end_insert_amazon_sp_search_term_keyword_report_goessor_task
        )

    #  --- Load amazon_sbv_search_term_keyword_report ---
    
    with TaskGroup(group_id='load_amazon_sbv_search_term_keyword_report') as tg_amazon_sbv_search_term_keyword_report:
        list_s3_files_amazon_sbv_search_term_keyword_report_task = PythonOperator(
            task_id="list_s3_files_amazon_sbv_search_term_keyword_report",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_search_term_keyword_report/list_s3_amazon_sbv_search_term_keyword_report.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_amazon_sbv_search_term_keyword_report_task = DummyOperator(task_id="begin_insert_amazon_sbv_search_term_keyword_report")
        skip_insert_amazon_sbv_search_term_keyword_report_task = DummyOperator(task_id="skip_insert_amazon_sbv_search_term_keyword_report")
        end_insert_amazon_sbv_search_term_keyword_report_task = DummyOperator(task_id="end_insert_amazon_sbv_search_term_keyword_report")

        from tasklib.loader import S3ToSnowflake
        load_obj = S3ToSnowflake()
        s3_to_snowflake_amazon_sbv_search_term_keyword_report_task = PythonOperator(
            task_id="s3_to_snowflake_amazon_sbv_search_term_keyword_report",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_search_term_keyword_report/s3_to_sf_raw_amazon_sbv_search_term_keyword_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amazon_sbv_search_term_keyword_report_task = BranchPythonOperator(
            task_id='check_new_files_found_amazon_sbv_search_term_keyword_report',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_search_term_keyword_report/s3_to_snowflake_amazon_sbv_search_term_keyword_report.yaml",
                       "skip_task_id": "load_amazon_sbv_search_term_keyword_report.skip_insert_amazon_sbv_search_term_keyword_report",
                       "next_task_id": "load_amazon_sbv_search_term_keyword_report.begin_insert_amazon_sbv_search_term_keyword_report"
            },
        )

        insert_log_amazon_sbv_search_term_keyword_report_task = PythonOperator(
            task_id="insert_log_amazon_sbv_search_term_keyword_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/insert_log_amazon_sbv_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amazon_sbv_search_term_keyword_report_task = PythonOperator(
            task_id="dedupe_amazon_sbv_search_term_keyword_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/dedupe_amazon_sbv_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amazon_sbv_search_term_keyword_report_task = PythonOperator(
            task_id="merge_stage_amazon_sbv_search_term_keyword_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/merge_stage_amazon_sbv_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amazon_sbv_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amazon_sbv_search_term_keyword_report', 
                    field_list=['reportdate', 'adgroupid', 'accountid', 'campaignid'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_sbv_search_term_keyword_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amazon_sbv_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amazon_sbv_search_term_keyword_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amazon_sbv_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_sbv_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amazon_sbv_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amazon_sbv_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amazon_sbv_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amazon_sbv_search_term_keyword_report_task = PythonOperator(
            task_id="run_audit_amazon_sbv_search_term_keyword_report",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amazon_sbv_search_term_keyword_report", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            list_s3_files_amazon_sbv_search_term_keyword_report_task >>
            check_new_files_found_amazon_sbv_search_term_keyword_report_task >>
            [begin_insert_amazon_sbv_search_term_keyword_report_task, skip_insert_amazon_sbv_search_term_keyword_report_task]
        )

        (
            begin_insert_amazon_sbv_search_term_keyword_report_task >> 
            s3_to_snowflake_amazon_sbv_search_term_keyword_report_task >>
            insert_log_amazon_sbv_search_term_keyword_report_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_amazon_sbv_search_term_keyword_report_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_amazon_sbv_search_term_keyword_report_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_amazon_sbv_search_term_keyword_report_task >>
            end_insert_amazon_sbv_search_term_keyword_report_task
        )

    #  --- Load amazon_sbv_search_term_keyword_report_goessor ---
    
    with TaskGroup(group_id='load_amazon_sbv_search_term_keyword_report_goessor') as tg_amazon_sbv_search_term_keyword_report_goessor:
        list_s3_files_amazon_sbv_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="list_s3_files_amazon_sbv_search_term_keyword_report_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_search_term_keyword_report/list_s3_amazon_sbv_search_term_keyword_report_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_amazon_sbv_search_term_keyword_report_goessor_task = DummyOperator(task_id="begin_insert_amazon_sbv_search_term_keyword_report_goessor")
        skip_insert_amazon_sbv_search_term_keyword_report_goessor_task = DummyOperator(task_id="skip_insert_amazon_sbv_search_term_keyword_report_goessor")
        end_insert_amazon_sbv_search_term_keyword_report_goessor_task = DummyOperator(task_id="end_insert_amazon_sbv_search_term_keyword_report_goessor")

        from tasklib.loader import S3ToSnowflake
        load_obj = S3ToSnowflake()
        s3_to_snowflake_amazon_sbv_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="s3_to_snowflake_amazon_sbv_search_term_keyword_report_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_search_term_keyword_report/s3_to_sf_raw_amazon_sbv_search_term_keyword_report_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_amazon_sbv_search_term_keyword_report_goessor_task = BranchPythonOperator(
            task_id='check_new_files_found_amazon_sbv_search_term_keyword_report_goessor',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_search_term_keyword_report/s3_to_snowflake_amazon_sbv_search_term_keyword_report_goessor.yaml",
                       "skip_task_id": "load_amazon_sbv_search_term_keyword_report_goessor.skip_insert_amazon_sbv_search_term_keyword_report_goessor",
                       "next_task_id": "load_amazon_sbv_search_term_keyword_report_goessor.begin_insert_amazon_sbv_search_term_keyword_report_goessor"
            },
        )

        insert_log_amazon_sbv_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="insert_log_amazon_sbv_search_term_keyword_report_goessor",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/insert_log_amazon_sbv_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_amazon_sbv_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="dedupe_amazon_sbv_search_term_keyword_report_goessor",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/dedupe_amazon_sbv_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_amazon_sbv_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="merge_stage_amazon_sbv_search_term_keyword_report_goessor",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_search_term_keyword_report/merge_stage_amazon_sbv_search_term_keyword_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amazon_sbv_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amazon_sbv_search_term_keyword_report', 
                    field_list=['reportdate', 'adgroupid', 'accountid', 'campaignid'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_sbv_search_term_keyword_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amazon_sbv_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amazon_sbv_search_term_keyword_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amazon_sbv_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_sbv_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amazon_sbv_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amazon_sbv_search_term_keyword_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amazon_sbv_search_term_keyword_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_amazon_sbv_search_term_keyword_report_goessor_task = PythonOperator(
            task_id="run_audit_amazon_sbv_search_term_keyword_report_goessor",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amazon_sbv_search_term_keyword_report", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            list_s3_files_amazon_sbv_search_term_keyword_report_goessor_task >>
            check_new_files_found_amazon_sbv_search_term_keyword_report_goessor_task >>
            [begin_insert_amazon_sbv_search_term_keyword_report_goessor_task, skip_insert_amazon_sbv_search_term_keyword_report_goessor_task]
        )

        (
            begin_insert_amazon_sbv_search_term_keyword_report_goessor_task >> 
            s3_to_snowflake_amazon_sbv_search_term_keyword_report_goessor_task >>
            insert_log_amazon_sbv_search_term_keyword_report_goessor_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_amazon_sbv_search_term_keyword_report_goessor_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_amazon_sbv_search_term_keyword_report_goessor_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_amazon_sbv_search_term_keyword_report_goessor_task >>
            end_insert_amazon_sbv_search_term_keyword_report_goessor_task
        )

    # ---- Main branch ----
    chain(
       begin,
       get_workflow_params,
       [tg_amazon_sb_search_term_keyword_report,tg_amazon_sp_search_term_keyword_report,tg_amazon_sbv_search_term_keyword_report,tg_amazon_sb_search_term_keyword_report_goessor,tg_amazon_sp_search_term_keyword_report_goessor,tg_amazon_sbv_search_term_keyword_report_goessor],
       update_workflow_params,
       end
    )
    
import logging
from airflow import DAG
from airflow.operators.dummy_operator import Dummy<PERSON>perator
from airflow.operators.python import Branch<PERSON><PERSON>honOperator, PythonOperator
from airflow.models.baseoperator import chain
from airflow.sensors.external_task import ExternalTaskSensor
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule
from datetime import datetime

from tasklib.loader import check_for_new_files

log = logging.getLogger(__name__)

S3_TO_SNOWFLAKE_YAML = 'amazon_fba_returns_report/s3_to_snowflake.yaml'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id="amazon_fba_returns_hourly",
    start_date=datetime(2022, 5, 8),
    schedule_interval="0 */4 * * *",
    catchup=False,
    max_active_runs=1,
    params={
        "workflow_name": "AMAZON_FBA_RETURNS_HOURLY",
        "author": "ruchira"
    },
    tags=['Ruchira'],
) as dag:
    import tasklib.workflow as tlw
    import tasklib.s3 as tls3
    import tasklib.glue as tlg
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    list_s3_modified_files = PythonOperator(
        task_id="task_list_s3_modified_files",
        python_callable=tls3.list_s3_modified_files,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "args_file": "amazon_fba_returns_report/s3_list_folders.yaml",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    list_s3_modified_files_goessor = PythonOperator(
        task_id="task_list_s3_modified_files_goessor",
        python_callable=tls3.list_s3_modified_files,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "args_file": "amazon_fba_returns_report/s3_list_folders_goessor.yaml",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    skip_returns = DummyOperator(task_id='skip_returns')

    check_new_files_found = BranchPythonOperator(
        task_id='check_new_files_found',
        python_callable=check_for_new_files,
        op_kwargs={
            'args_file': S3_TO_SNOWFLAKE_YAML,
            'skip_task_id': 'skip_returns',
            'next_task_id': 'amazon_fba_returns_report.begin_ingestion_amazon_fba_returns_report',
            'args_file_second': 'amazon_fba_returns_report/s3_to_snowflake_goessor.yaml'
        }
    )

    with TaskGroup(
        group_id="amazon_fba_returns_report"
    ) as amazon_fba_returns_report:

        begin_ingestion_amazon_fba_returns_report = DummyOperator(
            task_id="begin_ingestion_amazon_fba_returns_report"
        )
        end_ingestion_amazon_fba_returns_report = DummyOperator(
            task_id="end_ingestion_amazon_fba_returns_report"
        )

        from tasklib.loader import S3ToSnowflake
        load_obj = S3ToSnowflake()

        transfer_s3_to_snowflake = PythonOperator(
            task_id="task_transfer_s3_to_snowflake",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_fba_returns_report/s3_to_sf_raw_amazon_fba_returns_report.yaml",},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        transfer_s3_to_snowflake_goessor = PythonOperator(
            task_id="task_transfer_s3_to_snowflake_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_fba_returns_report/s3_to_sf_raw_amazon_fba_returns_report_goessor.yaml", },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        create_stg_transform_fba_amazon_returns_report = PythonOperator(
            task_id="create_stg_transform_fba_amazon_returns_report",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/create_stg_transform_fba_amazon_returns_report.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        insert_amazon_fba_returns_report_log = PythonOperator(
            task_id="insert_amazon_fba_returns_report_log",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/insert_amazon_fba_returns_report_log.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        (
            begin_ingestion_amazon_fba_returns_report
            >> transfer_s3_to_snowflake
            >> transfer_s3_to_snowflake_goessor
            >> create_stg_transform_fba_amazon_returns_report
            >> insert_amazon_fba_returns_report_log
            >> end_ingestion_amazon_fba_returns_report
        )
    
    with TaskGroup(
        group_id="fact_all_returns"
    ) as fact_all_returns:

        create_stg_amazon_fba_returns_report_agg = PythonOperator(
            task_id="create_stg_amazon_fba_returns_report_agg",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/create_stg_amazon_fba_returns_report_agg.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        create_stg_join_amazon_fba_returns_report_orders = PythonOperator(
            task_id="create_stg_join_amazon_fba_returns_report_orders",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/create_stg_join_amazon_fba_returns_report_orders.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )
        
        run_dq_tests_order_must_preceed_return = PythonOperator(
            task_id="run_dq_tests_order_must_preceed_return",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "c771611f-b094-4e2d-b696-80514cb58003"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        create_stg_fact_all_returns = PythonOperator(
            task_id="create_stg_fact_all_returns",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/create_stg_fact_all_returns.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_tests_returns_pk_is_unique = PythonOperator(
            task_id="run_dq_tests_returns_pk_is_unique",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "8dd9e7e0-8f6c-42a0-8fd6-ea6c760aa040"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        create_stg_fact_all_returns_v2 = PythonOperator(
            task_id="create_stg_fact_all_returns_v2",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/create_stg_fact_all_returns_v2.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_dq_tests_returns_pk_is_unique_v2 = PythonOperator(
            task_id="run_dq_tests_returns_pk_is_unique_v2",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "8dd9e7e0-8f6c-42a0-8fd6-ea6c760aa040"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_fact_all_returns = PythonOperator(
            task_id="merge_fact_all_returns",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/merge_fact_all_returns.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        merge_fact_all_returns_v2 = PythonOperator(
            task_id="merge_fact_all_returns_v2",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/merge_fact_all_returns_v2.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        update_fact_all_returns = PythonOperator(
            task_id="update_fact_all_returns",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/update_fact_all_returns.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        update_fact_all_returns_v2 = PythonOperator(
            task_id="update_fact_all_returns_v2",
            python_callable=tlsql.run_query_file,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "returns/update_fact_all_returns_v2.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
        )

        run_audit_fact_all_returns = PythonOperator(
            task_id="run_audit_fact_all_returns",
            python_callable=tla.run_audit,
            op_kwargs={"table_name": "$curated_db.fact_all_returns",
                       "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_audit_fact_all_returns_v2 = PythonOperator(
            task_id="run_audit_fact_all_returns_v2",
            python_callable=tla.run_audit,
            op_kwargs={"table_name": "$curated_db.fact_all_returns_v2",
                       "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            create_stg_amazon_fba_returns_report_agg
            >> create_stg_join_amazon_fba_returns_report_orders
            >> run_dq_tests_order_must_preceed_return
            >> create_stg_fact_all_returns
            >> run_dq_tests_returns_pk_is_unique
            >> merge_fact_all_returns
            >> update_fact_all_returns
            >> create_stg_fact_all_returns_v2
            >> run_dq_tests_returns_pk_is_unique_v2
            >> merge_fact_all_returns_v2
            >> update_fact_all_returns_v2
            >> run_audit_fact_all_returns
            >> run_audit_fact_all_returns_v2
        )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    wait_on_amazon_orders = ExternalTaskSensor(task_id="wait_on_amazon_orders",
                                               external_dag_id="amazon_orders",
                                               external_task_id="end",
                                               allowed_states=["success"],
                                               poke_interval=60 * 3,
                                               mode="reschedule",
                                               timeout=60 * 60 * 4,
                                               on_failure_callback=alerts.send_failure_alert
                                               )

    end = DummyOperator(task_id="end")

    chain(
        get_workflow_params,
        wait_on_amazon_orders,
        [list_s3_modified_files,list_s3_modified_files_goessor],
        check_new_files_found,
        [amazon_fba_returns_report, skip_returns],
    )

    amazon_fba_returns_report >> fact_all_returns
    [fact_all_returns, skip_returns] >> update_workflow_params >> end

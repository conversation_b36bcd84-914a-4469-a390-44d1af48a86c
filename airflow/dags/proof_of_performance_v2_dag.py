from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging
from tasklib import alerts
import tasklib.glue as agl

log = logging.getLogger(__name__)


with DAG(
    dag_id="proof_of_performance_v2",
    start_date=datetime(2022, 8, 1),
    schedule_interval='@daily',
    catchup=False,
    max_active_runs=1,
    params={"author":"Chetan"},
    tags=['RETIRED']
) as dag:

    sync_app_tables_to_snowflake_job = PythonOperator(
        task_id="sync_app_tables_to_snowflake_job",
        python_callable=agl.run_glue_job,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"glue_job_name": "pop_v2_sync_app_tables_to_snowflake_job",
                   "glue_job_args": {"--RUNENV": "PROD", "--GLUE_REGION": "us-east-2"}}
    )

    kpi_calculator_job = PythonOperator(
        task_id="kpi_calculator_job",
        python_callable=agl.run_glue_job,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"glue_job_name": "pop_v2_kpi_calculator_job",
                   "glue_job_args": {"--RUNENV": "PROD", "--GLUE_REGION": "us-east-2"}}
    )

    kpi_aggregator_by_tracker_job = PythonOperator(
        task_id="kpi_aggregator_by_tracker_job",
        python_callable=agl.run_glue_job,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"glue_job_name": "pop_v2_kpi_aggregator_by_tracker_job",
                   "glue_job_args": {"--RUNENV": "PROD", "--GLUE_REGION": "us-east-2"}}
    )

    sync_pop_tables_to_postgres_job = PythonOperator(
        task_id="sync_pop_tables_to_postgres_job",
        python_callable=agl.run_glue_job,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"glue_job_name": "pop_v2_sync_pop_tables_to_postgres_job",
                   "glue_job_args": {"--RUNENV": "PROD", "--GLUE_REGION": "us-east-2"}}
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    chain(
        begin,
        sync_app_tables_to_snowflake_job,
        kpi_calculator_job,
        kpi_aggregator_by_tracker_job,
        sync_pop_tables_to_postgres_job,
        end,
    )

""" Data transaformation code generator for Brand EBIDTDA framework """

import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake


BUILD_NUM = '1'
DAG_ID = 'ppc_incrementality'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2024, 1, 1),
    # schedule_interval='20 */4 * * *', 
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'ppc_incrementality',
            'author': 'carlos'},
    tags=['carlos', 'Raptor', 'ppc']
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}

    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    #  --- process brand edbidta metrics for _report ---

    with TaskGroup(group_id='process_ppc_incrementality_framework') as tg_ppc_incrementality_framework:
        ppc_incrementality_asin_mapping_task = PythonOperator(
            task_id="process_ppc_incrementality_asin_mapping",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/incrementality/ppc_incrementality_asin_mapping.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        ppc_incrementality_latest_sv = PythonOperator(
            task_id="process_ppc_incrementality_latest_sv",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/incrementality/ppc_incrementality_latest_sv.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        ppc_incrementality_okr_weekly = PythonOperator(
            task_id="process_ppc_incrementality_okr_weekly",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/incrementality/ppc_incrementality_okr_weekly.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        ppc_incrementality_keywords = PythonOperator(
            task_id="process_ppc_incrementality_keywords",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/incrementality/ppc_incrementality_keywords.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        ppc_incrementality_targets = PythonOperator(
            task_id="process_ppc_incrementality_targets",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/incrementality/ppc_incrementality_targets.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        ppc_incrementality_sqp = PythonOperator(
            task_id="process_ppc_incrementality_sqp",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/sqp_by_asin_classification.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        ppc_incrementality = PythonOperator(
            task_id="process_ppc_incrementality",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/incrementality/ppc_incrementality.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

    # --- Data Quality Check for NULL values in critical fields ---
        # run_dq_is_null_metrics_task = PythonOperator(
        #     task_id="run_dq_is_null_metrics",
        #     python_callable=tldq.run_dq_string,
        #     op_kwargs={
        #         'wk_name': DAG_ID,
        #         'tb_name': '$stage_db.ppc_incrementality',
        #         'test_name': 'is_null',
        #         'sql_query': tldq.gen_check_if_nulls(
        #             tb_name='$stage_db.ppc_incrementality',
        #             field_list=['asin', 'country_code', 'sku'], 
        #             hard_alert=True,
        #         )
        #     },
        #     on_failure_callback=alerts.send_failure_alert,
        # )

        (
            ppc_incrementality_asin_mapping_task >>
            ppc_incrementality_latest_sv >>
            ppc_incrementality_okr_weekly >>
            ppc_incrementality_keywords >>
            ppc_incrementality_targets >>
            ppc_incrementality_sqp >>
            ppc_incrementality
            # run_dq_is_null_metrics_task
        )

    # --- Main DAG chain ---
    chain(
        begin,
        get_workflow_parameters,
        tg_ppc_incrementality_framework,  # This TaskGroup includes the processing and DQ tasks
        update_workflow_parameters,
        end
    )
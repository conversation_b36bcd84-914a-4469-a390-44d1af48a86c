import logging
from datetime import datetime

from airflow import DAG
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.sensors.external_task import ExternalTaskSensor
from airflow.utils.task_group import TaskGroup

import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts

DAG_ID = 'fact_amazon_ad_campaign_placements'
TRANSFORM_BASE = 'ads/amazon/' + DAG_ID
RELEASE_DEF = '1'
BUILD_NUM = '01'

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"

log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2022, 5, 8),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID,
            'author': 'sauvik'},
    tags=['Sauvik']
) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )


    with TaskGroup(group_id='tg_stg_sp_campaign_placements') as tg_stg_sp_campaign_placements:
        stg_bid_adjust_sp_campaign = PythonOperator(
            task_id="stg_bid_adjust_sp_campaign",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/stg_bid_adjust_sp_campaign.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        run_dq_stg_bid_adjust_sp_campaign = PythonOperator(
            task_id="run_dq_stg_bid_adjust_sp_campaign",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$stage_db.stg_bid_adjust_merge_amz_sp_campaign",
                'test_name': 'key_fields_not_duplicate',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.stg_bid_adjust_merge_amz_sp_campaign", 
                    field_list=['campaign_id', 'fetch_date'],
                    hard_alert=True
                )
                },
            on_failure_callback=alerts.send_failure_alert
        )

        merge_stg_bid_adjust_sp_campaign = PythonOperator(
            task_id="merge_stg_bid_adjust_sp_campaign",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/merge_stg_bid_adjust_sp_campaign.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        run_dq_merge_stg_bid_adjust_sp_campaign = PythonOperator(
            task_id="run_dq_merge_stg_bid_adjust_sp_campaign",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$stage_db.merge_stg_bid_adjust_sp_campaign",
                'test_name': 'key_fields_not_duplicate',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.merge_stg_bid_adjust_sp_campaign", 
                    field_list=['campaign_id', 'fetch_date'],
                    hard_alert=True
                )
                },
            on_failure_callback=alerts.send_failure_alert
        )
        stg_sp_campaign_placements = PythonOperator(
            task_id="stg_sp_campaign_placements",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/stg_sp_campaign_placements.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        run_dq_stg_sp_campaign_placements = PythonOperator(
            task_id="run_dq_stg_sp_campaign_placements",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$stage_db.stg_sp_campaign_placements",
                'test_name': 'key_fields_not_duplicate',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.stg_sp_campaign_placements", 
                    field_list=['pk'],
                    hard_alert=True
                )
                },
            on_failure_callback=alerts.send_failure_alert
        )

        stg_bid_adjust_sp_campaign >> run_dq_stg_bid_adjust_sp_campaign >> merge_stg_bid_adjust_sp_campaign >> run_dq_merge_stg_bid_adjust_sp_campaign >> stg_sp_campaign_placements >> run_dq_stg_sp_campaign_placements

    with TaskGroup(group_id='tg_stg_sb_campaign_placements_combined') as tg_stg_sb_campaign_placements_combined:
        stg_bid_adjust_sb_campaign = PythonOperator(
            task_id="stg_bid_adjust_sb_campaign",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/stg_bid_adjust_sb_campaign.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        run_dq_stg_bid_adjust_sb_campaign = PythonOperator(
            task_id="run_dq_stg_bid_adjust_sb_campaign",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$stage_db.stg_bid_adjust_merge_amz_sb_campaign",
                'test_name': 'key_fields_not_duplicate',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.stg_bid_adjust_merge_amz_sb_campaign",
                    field_list=['campaign_id', 'fetch_date'],
                    hard_alert=True
                )
                },
            on_failure_callback=alerts.send_failure_alert
        )

        merge_stg_bid_adjust_sb_campaign = PythonOperator(
            task_id="merge_stg_bid_adjust_sb_campaign",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/merge_stg_bid_adjust_sb_campaign.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        run_dq_merge_stg_bid_adjust_sb_campaign = PythonOperator(
            task_id="run_dq_merge_stg_bid_adjust_sb_campaign",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$stage_db.merge_stg_bid_adjust_sb_campaign",
                'test_name': 'key_fields_not_duplicate',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.merge_stg_bid_adjust_sb_campaign",
                    field_list=['campaign_id', 'fetch_date'],
                    hard_alert=True
                )
                },
            on_failure_callback=alerts.send_failure_alert
        )

        with TaskGroup(group_id='tg_stg_sb_campaign_placements') as tg_stg_sb_campaign_placements:
            stg_sb_campaign_placements = PythonOperator(
                task_id="stg_sb_campaign_placements",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection": "Snowflake",
                    "sql_file": TRANSFORM_BASE + "/stg_sb_campaign_placements.sql",
                    "wf_params": WF_PARAMS_EXPR},
                on_failure_callback=alerts.send_failure_alert,
                provide_context=True
            )

            run_dq_stg_sb_campaign_placements = PythonOperator(
                task_id="run_dq_stg_sb_campaign_placements",
                python_callable=tldq.run_dq_string,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'tb_name': "$stage_db.stg_sb_campaign_placements",
                    'test_name': 'key_fields_not_duplicate',
                    'sql_query': tldq.gen_check_unique_key(
                        tb_name="$stage_db.stg_sb_campaign_placements",
                        field_list=['pk'],
                        hard_alert=True
                    )
                    },
                on_failure_callback=alerts.send_failure_alert
            )

            stg_sb_campaign_placements >> run_dq_stg_sb_campaign_placements

        with TaskGroup(group_id='tg_stg_sbv_campaign_placements') as tg_stg_sbv_campaign_placements:
            stg_sbv_campaign_placements = PythonOperator(
                task_id="stg_sbv_campaign_placements",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection": "Snowflake",
                    "sql_file": TRANSFORM_BASE + "/stg_sbv_campaign_placements.sql",
                    "wf_params": WF_PARAMS_EXPR},
                on_failure_callback=alerts.send_failure_alert,
                provide_context=True
            )

            run_dq_stg_sbv_campaign_placements = PythonOperator(
                task_id="run_dq_stg_sbv_campaign_placements",
                python_callable=tldq.run_dq_string,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'tb_name': "$stage_db.stg_sbv_campaign_placements",
                    'test_name': 'key_fields_not_duplicate',
                    'sql_query': tldq.gen_check_unique_key(
                        tb_name="$stage_db.stg_sbv_campaign_placements",
                        field_list=['pk'],
                        hard_alert=True
                    )
                    },
                on_failure_callback=alerts.send_failure_alert
            )

            stg_sbv_campaign_placements >> run_dq_stg_sbv_campaign_placements

        stg_bid_adjust_sb_campaign >> run_dq_stg_bid_adjust_sb_campaign >> merge_stg_bid_adjust_sb_campaign >> run_dq_merge_stg_bid_adjust_sb_campaign >> [tg_stg_sb_campaign_placements, tg_stg_sbv_campaign_placements]

    with TaskGroup(group_id='tg_stg_sd_campaign_placements') as tg_stg_sd_campaign_placements:
        stg_sd_campaign_placements = PythonOperator(
            task_id="stg_sd_campaign_placements",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/stg_sd_campaign_placements.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        run_dq_stg_sd_campaign_placements = PythonOperator(
            task_id="run_dq_stg_sd_campaign_placements",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$stage_db.stg_sd_campaign_placements",
                'test_name': 'key_fields_not_duplicate',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.stg_sd_campaign_placements", 
                    field_list=['pk'],
                    hard_alert=True
                )
                },
            on_failure_callback=alerts.send_failure_alert
        )

        stg_sd_campaign_placements >> run_dq_stg_sd_campaign_placements

stg_all_campaign_placements = PythonOperator(
    task_id="stg_all_campaign_placements",
    python_callable=tlsql.run_query_file,
    op_kwargs={
        "connection": "Snowflake",
        "sql_file": TRANSFORM_BASE + "/stg_all_campaign_placements.sql",
        "wf_params": WF_PARAMS_EXPR},
    on_failure_callback=alerts.send_failure_alert,
    provide_context=True
)

run_dq_stg_all_campaign_placements = PythonOperator(
    task_id="run_dq_stg_all_campaign_placements",
    python_callable=tldq.run_dq_string,
    op_kwargs={
        'wk_name': DAG_ID,
        'tb_name': "$stage_db.stg_all_campaign_placements",
        'test_name': 'key_fields_not_duplicate',
        'sql_query': tldq.gen_check_unique_key(
            tb_name="$stage_db.stg_all_campaign_placements", 
            field_list=['pk'],
            hard_alert=True
        )
        },
    on_failure_callback=alerts.send_failure_alert
)

merge_all_campaign_placements = PythonOperator(
    task_id="merge_all_campaign_placements",
    python_callable=tlsql.run_query_file,
    op_kwargs={
        "connection": "Snowflake",
        "sql_file": TRANSFORM_BASE + "/merge_all_campaign_placements.sql",
        "wf_params": WF_PARAMS_EXPR},
    on_failure_callback=alerts.send_failure_alert,
    provide_context=True
)

# --- Audit the data checks ---

import tasklib.audit as tla

run_audit = PythonOperator(
    task_id="run_audit",
    python_callable=tla.run_audit,
    op_kwargs={"table_name": "$curated_db.fact_amazon_ad_campaign_placements",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                },
    on_failure_callback=alerts.send_failure_alert
)

# --- Update workflow status ---

update_workflow_params = PythonOperator(
    task_id="update_workflow_parameters",
    python_callable=tlw.update_workflow_params,
    op_kwargs={"wf_params": WF_PARAMS_EXPR},
    on_failure_callback=alerts.send_failure_alert,
    provide_context=True
)


begin = DummyOperator(task_id="begin")
end = DummyOperator(task_id="end")
end_wait = DummyOperator(task_id="end_wait")


wait_on_amazon_ads_campaigns_meta_reports = ExternalTaskSensor(task_id="wait_on_amazon_ads_campaigns_meta_reports",
                               external_dag_id="amazon_ads_campaigns_meta_reports",
                               external_task_id="end",
                               allowed_states=["success"],
                               timeout=60 * 60 * 4,
                               poke_interval=60 * 3,
                               mode="reschedule",
                               dag=dag
                               )

wait_on_adv_campaigns_report_hourly = ExternalTaskSensor(task_id="wait_on_adv_campaigns_report_hourly",
                               external_dag_id="adv_campaigns_report_hourly",
                               external_task_id="end",
                               allowed_states=["success"],
                               timeout=60 * 60 * 4,
                               poke_interval=60 * 3,
                               mode="reschedule",
                               dag=dag
                               )


begin >> get_workflow_params >> [wait_on_amazon_ads_campaigns_meta_reports,wait_on_adv_campaigns_report_hourly] >> end_wait >> [tg_stg_sp_campaign_placements, tg_stg_sd_campaign_placements, tg_stg_sb_campaign_placements_combined] >> stg_all_campaign_placements >> run_dq_stg_all_campaign_placements >> merge_all_campaign_placements >> run_audit >> update_workflow_params >> end
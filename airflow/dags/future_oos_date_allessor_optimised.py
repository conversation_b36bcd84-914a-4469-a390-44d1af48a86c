from airflow.models import DAG
from airflow.utils.dates import days_ago

from airflow.operators.bash import BashOperator
from airflow.operators.python import PythonOperator
from pathlib import Path
from tasklib.alerts import send_failure_alert_ops
from tasklib.config import get_env

import logging
from airflow.models import DAG
from airflow.providers.snowflake.operators.snowflake import SnowflakeOperator
from airflow.utils.dates import days_ago

from tasklib.config import get_env
from tasklib.alerts import send_failure_alert_ops
from datetime import datetime, timezone, timedelta
from airflow.exceptions import AirflowFailException

import tasklib.dq as tldq
from tasklib import alerts
import papermill as pm
import tasklib.sql as tlsql


#change check1
DAG_ID = "future_oos_date_stage_two_allessor_prod"
WF_PARAMS_EXPR = "{}"

workflows_dir = Path(__file__)
print('workflows_dir: ',workflows_dir)
notebooks_dir = workflows_dir.parent.joinpath("notebooks")
print('workflows_dir',workflows_dir)

with DAG(
 dag_id=DAG_ID,
    description="Main simulation of channel future OOS data All Essor",
    start_date = days_ago(1),
    tags=["ods"],
    catchup=False,
    # Schedule interval hourly.
    schedule_interval="30 14 * * *",
    params={'author': 'chirag'}
) as dag:


    base_cmd = "papermill --log-output --log-level DEBUG --progress-bar -p msg \"Ran from Airflow at {{ execution_date }}\" "

    run_timestamp_dq_checks = PythonOperator(
        task_id="run_timestamp_dq_checks",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'run_timestamp_dq_checks',
            'tb_name': "SANDBOX.OPS_DEV.ALLOCATION_INBOUND_INV_INBOUND_MASTER",
            'query_file': "future_oos/gsr_dq_yaml/test_dq.yaml",
            'use_default_path': True
        },
        on_failure_callback=send_failure_alert_ops,
    )
    

    notebook_run = BashOperator(
        queue="ops_queue",
        pool="ops_notebook_pool",
        task_id="dummy_notebook_run2",
        bash_command=base_cmd + str(notebooks_dir.joinpath("future_oos", "future_oos_channel_all_essor_v7.ipynb")) + " -",
    )

    run_data_dq_checks = PythonOperator(
        task_id="run_data_dq_checks",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'run_data_dq_checks',
            'tb_name': "DWH_DEV.STAGING.CHANNEL_FUTURE_OOS_DATE_ALLESSOR_TEMP",
            'query_file': "future_oos/gsr_dq_yaml/test_dq_post.yaml",
            'use_default_path': True
        },
        on_failure_callback=send_failure_alert_ops,
    )

    future_oos_date_channel_allessor_write = PythonOperator(
        task_id="future_oos_date_channel_allessor_write",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "future_oos/other_sql_codes/future_oos_data_insertion.sql",
            "wf_params": WF_PARAMS_EXPR
    },
    provide_context=True,
    on_failure_callback=send_failure_alert_ops)


    future_oos_date_channel_allessor_alerts = BashOperator(
        queue="ops_queue",
        pool="ops_notebook_pool",
        task_id="future_oos_date_channel_allessor_alerts",
        bash_command=base_cmd + str(notebooks_dir.joinpath("future_oos","Alerts", "gsr_alerts.ipynb")) + " -",
    )

    run_timestamp_dq_checks >> notebook_run  >> run_data_dq_checks >> future_oos_date_channel_allessor_write >> future_oos_date_channel_allessor_alerts

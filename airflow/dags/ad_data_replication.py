from tasklib import alerts
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow import DAG
from airflow.models.baseoperator import chain
import logging
from datetime import datetime
import warnings

warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)

with DAG(
        dag_id="ad_data_replication",
        start_date=datetime(2022, 12, 5),
        schedule_interval='@daily',
        catchup=False,
        max_active_runs=1,
        params={"author": "manibabu"},
        tags=['RETIRED']
) as dag:
    import tasklib.replication as tlr

    task_fact_keyword_features = PythonOperator(
        task_id="task_fact_keyword_features_replication",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/config_keyword_features.yaml"}
    )

    task_ml_bid_inference = PythonOperator(
        task_id="task_ml_bid_inference_replication",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/config_bid_inference.yaml"}
    )

    task_ad_group_bid_inference = PythonOperator(
        task_id="task_ml_ad_group_bid_inference_replication",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/config_ad_group_bid_inference.yaml"}
    )

    task_ad_campaign_bid_inference = PythonOperator(
        task_id="task_ml_ad_campaign_bid_inference_replication",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/config_ad_campaign_bid_inference.yaml"}
    )

    task_ad_target_bid_inference = PythonOperator(
        task_id="task_ml_ad_target_bid_inference_replication",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/config_ad_target_bid_inference.yaml"}
    )

    task_fact_amazon_ad_asins = PythonOperator(
        task_id="task_fact_amazon_ad_asins_replication",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/config_amazon_ad_asins.yaml"}
    )

    task_fact_amazon_ad_campaigns_placements = PythonOperator(
        task_id="task_fact_amazon_ad_campaigns_placements_replication",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/config_amazon_ad_campaigns_placements.yaml"}
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    chain(
        begin,
        [task_ml_bid_inference,
         task_ad_group_bid_inference,
         task_ad_campaign_bid_inference,
         task_ad_target_bid_inference,
         task_fact_amazon_ad_campaigns_placements,
         task_fact_amazon_ad_asins,
         ],
        # task_fact_keyword_features, TODO - This needs a plugin update will be done in next iteration
        end
    )

from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.models.baseoperator import chain
from airflow.operators.dummy_operator import DummyOperator
from airflow.models import Variable
from airflow.sensors.external_task import ExternalTaskSensor

import logging
from tasklib import alerts
import tasklib.workflow as tlw
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.dq as tldq
from tasklib.loader import S3ToSnowflake

load_obj = S3ToSnowflake()
log = logging.getLogger(__name__)

DAG_ID = 'amazon_netsuite_inventory_adjustment_automation'
WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022,5,8),
    schedule_interval=Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 */4 * * *'),
    catchup=False,
    max_active_runs=1,
    params={'workflow_name':'AMAZON_NETSUITE_INVENTORY_ADJUSTMENT_AUTOMATION'
            ,'author':"ayush"},
    tags=['Ayush','Sahil']
) as dag:

    get_workflow_params=PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )

    run_dq_stg_fact_amazon_inventory_adjustment = PythonOperator(
        task_id="task_run_dq_stg_fact_amazon_inventory_adjustment",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': "amazon_netsuite_inventory_adjustment_automation",
            'run_id': 'task_run_dq_stg_tests_fact_amazon_inventory_adjustment',
            'query_file': "inventory_netsuite_automation/adjustment/stg_amazon_adjustment_dq.yaml"
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    #inventory_adjustment load
    fact_amazon_inventory_adjustment_load=PythonOperator(
        task_id="task_fact_amazon_inventory_adjustment_load",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"inventory_netsuite_automation/adjustment/fact_amazon_inventory_adjustment_load.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_fact_amazon_inventory_adjustment =PythonOperator(
        task_id="task_run_dq_tests_fact_amazon_inventory_adjustment",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': "amazon_netsuite_inventory_adjustment_automation",
            'run_id': 'task_run_dq_tests_fact_amazon_inventory_adjustment',
            'query_file': "inventory_netsuite_automation/adjustment/amazon_adjustment_dq.yaml"
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    #inventory_adjustment aggregate load
    fact_amazon_inventory_adjustment_aggregate_load=PythonOperator(
        task_id="task_fact_amazon_inventory_adjustment_aggregate_load",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"inventory_netsuite_automation/adjustment/fact_amazon_inventory_adjustment_aggregate_load.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_fact_amazon_inventory_adjustment_aggregate = PythonOperator(
        task_id="task_run_dq_tests_fact_amazon_inventory_adjustment_aggregate",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': 'amazon_netsuite_inventory_adjustment_automation',
            'run_id': 'task_run_dq_tests_fact_amazon_inventory_adjustment_aggregate',
            'query_file': "inventory_netsuite_automation/adjustment/amazon_adjustment_agg_dq.yaml"
        },
        on_failure_callback=alerts.send_failure_alert
    )

    #workflow update
    update_workflow_params=PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )


    begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    # wait_on_amazon_ledger_detail_inventory = ExternalTaskSensor(
    #     task_id="wait_on_amazon_shipments_hourly",
    #     external_dag_id="amazon_shipments_hourly",
    #     external_task_id="end",
    #     allowed_states=["success"],
    #     poke_interval=60 * 3,
    #     mode="reschedule",
    #     timeout=60 * 60 * 4,
    #     on_failure_callback=alerts.send_failure_alert)

    chain(
         begin,
         get_workflow_params,
         # wait_on_amazon_ledger_detail_inventory,
         run_dq_stg_fact_amazon_inventory_adjustment,
         fact_amazon_inventory_adjustment_load,
         run_dq_tests_fact_amazon_inventory_adjustment,
         fact_amazon_inventory_adjustment_aggregate_load,
         run_dq_tests_fact_amazon_inventory_adjustment_aggregate,
         update_workflow_params,
         end,
    )


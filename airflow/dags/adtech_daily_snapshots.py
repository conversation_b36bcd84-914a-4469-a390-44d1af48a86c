from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain

import logging
from tasklib import alerts
import tasklib.sql as tlsql
import tasklib.replication as tlr

log = logging.getLogger(__name__)

BUILD_NUM = '1'
RELEASE_DEF = '1'
DAG_ID = 'adtech_daily_snapshot'
WF_PARAMS_EXPR = "{'wf_name': '" + DAG_ID.upper() + "'}"

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2024, 5, 1),
    schedule_interval='@daily',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'ayush'}
) as dag:

    ad_groups_daily_snapshot = PythonOperator(
        task_id="ad_groups_daily_snapshot",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/adtech/daily_snapshot/ad_groups_daily_snapshot.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    campaigns_daily_snapshot = PythonOperator(
        task_id="campaigns_daily_snapshot",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/adtech/daily_snapshot/campaigns_daily_snapshot.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    keywords_daily_snapshot = PythonOperator(
        task_id="keywords_daily_snapshot",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/adtech/daily_snapshot/keywords_daily_snapshot.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    targeting_clauses_daily_snapshot = PythonOperator(
        task_id="targeting_clauses_daily_snapshot",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/adtech/daily_snapshot/targeting_clauses_daily_snapshot.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    ad_product_daily_snapshot = PythonOperator(
        task_id="ad_product_daily_snapshot",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/adtech/daily_snapshot/ad_product_daily_snapshot.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    chain(
        begin,
        [
            ad_groups_daily_snapshot,
            campaigns_daily_snapshot,
            keywords_daily_snapshot,
            targeting_clauses_daily_snapshot,
            ad_product_daily_snapshot
        ],
        end,
    )

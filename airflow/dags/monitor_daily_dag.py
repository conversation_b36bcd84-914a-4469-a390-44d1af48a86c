from airflow import D<PERSON>
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.operators.email_operator import EmailOperator
from airflow.models import Variable
import logging
from tasklib import alerts
import tasklib.workflow as tlw
import tasklib.s3 as tls3
import tasklib.glue as tlg
import tasklib.sql as tlsql
import tasklib.audit as tla
import tasklib.dq as tldq

WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

_DAG_SCHEDULE = Variable.get("monitor_daily_dag_schedule", "33 23 * * *")

with DAG(
    dag_id="monitor_daily",
    start_date=datetime(2022, 11, 20),
    schedule_interval=_DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    tags=['Brian', 'Monitor'],
    params={
      "workflow_name": "MONITOR_DAILY",
      "author": "brian.nunes"
    }
) as dag:

    get_workflow_params=PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )

    monitor_daily_business_sql=PythonOperator(
        task_id="task_processing_daily_business_metadata",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"monitor/monitor_business.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    monitor_daily_sql=PythonOperator(
        task_id="task_processing_daily_metadata",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"monitor/monitor_daily.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_workflow_params=PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")
    
    #Assemble the dag 
    chain(
        begin, 
        get_workflow_params, 
        monitor_daily_business_sql,
        monitor_daily_sql, 
        update_workflow_params, 
        end,
    )

from airflow import DAG
from datetime import datetime
from airflow.operators.python import Python<PERSON>perator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging
from tasklib import alerts
import tasklib.sql as tlsql
from airflow.models import Variable
from airflow.sensors.external_task import ExternalTaskSensor

log = logging.getLogger(__name__)

BUILD_NUM = '1'
RELEASE_DEF = '1'
DAG_ID = 'product_ad_top_10_asin_by_parent_asin_sku_mapping'
WF_PARAMS_EXPR = "{'wf_name': '" + DAG_ID.upper() + "'}"

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2024, 6, 1),
    schedule_interval=Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily'),
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'ayush'}
) as dag:

    create_stg_map_base = PythonOperator(
        task_id="create_stg_map_base",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "mappings/product_ad_top_10_asin_by_parent_asin_sku_mapping/create_stg_pa_sku_map_base.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_asin_discount_depth = PythonOperator(
        task_id="create_stg_asin_discount_depth",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "mappings/product_ad_top_10_asin_by_parent_asin_sku_mapping/create_stg_pa_sku_map_asin_discount_depth.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_asin_ad_sales = PythonOperator(
        task_id="create_stg_asin_ad_sales",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "mappings/product_ad_top_10_asin_by_parent_asin_sku_mapping/create_stg_pa_sku_map_asin_sales.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_asin_inventory = PythonOperator(
        task_id="create_stg_asin_inventory",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "mappings/product_ad_top_10_asin_by_parent_asin_sku_mapping/create_stg_pa_sku_map_asin_inventory.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )


    delete_insert_parent_asin_sku_mapping = PythonOperator(
        task_id="delete_insert_parent_asin_sku_mapping",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "mappings/product_ad_top_10_asin_by_parent_asin_sku_mapping/delete_insert_product_ad_top_10_asin_by_parent_asin_sku_mapping.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    wait_on_fact_amazon_inventory = ExternalTaskSensor(
        task_id="wait_on_fact_amazon_inventory",
        external_dag_id="fact_amazon_inventory",
        external_task_id="end",
        allowed_states=["success"],
        poke_interval=60 * 5,
        mode="reschedule",
        timeout=60 * 60 * 4,
        on_failure_callback=alerts.send_failure_alert
    )

    wait_on_fact_amazon_promotions = ExternalTaskSensor(
        task_id="wait_on_fact_amazon_promotions",
        external_dag_id="fact_amazon_promotions",
        external_task_id="end",
        allowed_states=["success"],
        poke_interval=60 * 5,
        mode="reschedule",
        timeout=60 * 60 * 4,
        on_failure_callback=alerts.send_failure_alert
    )

    wait_on_fact_all_cp = ExternalTaskSensor(
        task_id="wait_on_fact_all_cp",
        external_dag_id="fact_all_cp",
        external_task_id="end",
        allowed_states=["success"],
        poke_interval=60 * 5,
        mode="reschedule",
        timeout=60 * 60 * 4,
        on_failure_callback=alerts.send_failure_alert
    )

    chain(
        begin,
        [
            wait_on_fact_amazon_inventory,
            wait_on_fact_amazon_promotions,
            wait_on_fact_all_cp
        ],
        create_stg_map_base,
        [
            create_stg_asin_discount_depth,
            create_stg_asin_ad_sales,
            create_stg_asin_inventory
        ],
        delete_insert_parent_asin_sku_mapping,
        end
    )

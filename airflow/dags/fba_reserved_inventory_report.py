"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.utils.task_group import TaskGroup
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON>ythonOperator, PythonOperator
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib.loader import check_for_new_files
from tasklib import alerts
from tasklib.loader import S3ToSnowflake
load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'fba_reserved_inventory_report'
RELEASE_DEF = '0'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2021, 1, 1),
    schedule_interval='@hourly',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'FBA_RESERVED_INVENTORY_REPORT',
            'author': 'vikas'},
    tags=['Vikas', 'Raptor']
) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}

    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_params = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load fba_reserved_inventory_report ---

    with TaskGroup(group_id='load_fba_reserved_inventory_report') as tg_fba_reserved_inventory_report:
        list_s3_files_fba_reserved_inventory_report_task = PythonOperator(
            task_id="list_s3_files_fba_reserved_inventory_report",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fba_reserved_inventory_report/list_s3_fba_reserved_inventory_report.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_fba_reserved_inventory_report_task = DummyOperator(task_id="begin_insert_fba_reserved_inventory_report")
        skip_insert_fba_reserved_inventory_report_task = DummyOperator(task_id="skip_insert_fba_reserved_inventory_report")
        end_insert_fba_reserved_inventory_report_task = DummyOperator(task_id="end_insert_fba_reserved_inventory_report")

        s3_to_snowflake_fba_reserved_inventory_report_task = PythonOperator(
            task_id="s3_to_snowflake_fba_reserved_inventory_report",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fba_reserved_inventory_report/s3_to_sf_raw_fba_reserved_inventory_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_fba_reserved_inventory_report_task = BranchPythonOperator(
            task_id='check_new_files_found_fba_reserved_inventory_report',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "fba_reserved_inventory_report/s3_to_snowflake_fba_reserved_inventory_report.yaml",
                       "skip_task_id": "load_fba_reserved_inventory_report.skip_insert_fba_reserved_inventory_report",
                       "next_task_id": "load_fba_reserved_inventory_report.begin_insert_fba_reserved_inventory_report"
            },
        )

        insert_raw_fba_reserved_inventory_report_log_task = PythonOperator(
            task_id="insert_raw_fba_reserved_inventory_report_log",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "fba_reserved_inventory_report/insert_fba_reserved_inventory_report_log.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_fba_reserved_inventory_report_task = PythonOperator(
            task_id="dedupe_fba_reserved_inventory_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "fba_reserved_inventory_report/dedupe_fba_reserved_inventory_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_fba_reserved_inventory_report_task = PythonOperator(
            task_id="merge_stage_fba_reserved_inventory_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "fba_reserved_inventory_report/merge_stage_fba_reserved_inventory_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )


        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_fba_reserved_inventory_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_fba_reserved_inventory_report',
                    field_list=['asin', 'fnsku', 'sku', 'marketplaceid', 'sellingpartnerid', 'file_name'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )


        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_fba_reserved_inventory_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_fba_reserved_inventory_report',
                    field_list=['country', 'connector_region', 'snapshot_date', 'sku', 'asin', 'fnsku', 'seller_id', 'marketplace_id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )


        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.fba_reserved_inventory_report_p',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.fba_reserved_inventory_report_p',
                    field_list=['snapshot_date', 'sku', 'asin', 'fnsku', 'seller_id', 'marketplace_id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )


        run_audit_fba_reserved_inventory_report_task = PythonOperator(
            task_id="run_audit_fba_reserved_inventory_report",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.fba_reserved_inventory_report_p", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            list_s3_files_fba_reserved_inventory_report_task >>
            check_new_files_found_fba_reserved_inventory_report_task >>
            [begin_insert_fba_reserved_inventory_report_task, skip_insert_fba_reserved_inventory_report_task]
        )

        (
            begin_insert_fba_reserved_inventory_report_task >>
            s3_to_snowflake_fba_reserved_inventory_report_task >>
            run_dq_is_null_raw_task >>
            insert_raw_fba_reserved_inventory_report_log_task >>
            dedupe_fba_reserved_inventory_report_task >>
            run_dq_is_unique_dedupe_pk_task >> 
            merge_stage_fba_reserved_inventory_report_task >>
            run_dq_is_unique_merge_pk_task >>
            run_audit_fba_reserved_inventory_report_task >>
            end_insert_fba_reserved_inventory_report_task
        )

    # ---- Main branch ----
    chain(
       begin,
       get_workflow_params,
       tg_fba_reserved_inventory_report,
       update_workflow_params,
       end
    )

"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.utils.task_group import TaskGroup
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON>ythonOperator, PythonOperator
from airflow.utils.trigger_rule import TriggerRule

import tasklib.dq as tldq
import tasklib.audit as tla
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib.loader import check_for_new_files
from tasklib import alerts
from tasklib.loader import S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'amazon_ledger_inventory'
RELEASE_DEF = '1'

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"

log = logging.getLogger(__name__)

with DAG(
        dag_id=DAG_ID,
        description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),

        start_date=datetime(2022, 12, 1),
        schedule_interval="@daily",
        catchup=False,
        max_active_runs=1,
        params={'workflow_name': 'AMAZON_LEDGER_INVENTORY',
                'author': 'Vikas'},
        tags=['Vikas', 'Raptor', 'L1', 'Goessor']
) as dag:
    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}

    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_params = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load amazon_ledger_detailed_inventory ---

    with TaskGroup(group_id='load_amazon_ledger_detailed_inventory') as tg_amazon_ledger_detailed_inventory:
        list_s3_files_amazon_ledger_detailed_inventory_task = PythonOperator(
            task_id="list_s3_files_amazon_ledger_detailed_inventory",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ledger_detailed_inventory/list_s3_amazon_ledger_detailed_inventory.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_amazon_ledger_detailed_inventory_task_goessor = PythonOperator(
            task_id="list_s3_files_amazon_ledger_detailed_inventory_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ledger_detailed_inventory/list_s3_amazon_ledger_detailed_inventory_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_amazon_ledger_detailed_inventory_task = DummyOperator(
            task_id="begin_insert_amazon_ledger_detailed_inventory")
        skip_insert_amazon_ledger_detailed_inventory_task = DummyOperator(
            task_id="skip_insert_amazon_ledger_detailed_inventory")
        end_insert_amazon_ledger_detailed_inventory_task = DummyOperator(
            task_id="end_insert_amazon_ledger_detailed_inventory")

        s3_to_snowflake_amazon_ledger_detailed_inventory_task = PythonOperator(
            task_id="s3_to_snowflake_amazon_ledger_detailed_inventory",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "amazon_ledger_detailed_inventory/s3_to_sf_raw_amazon_ledger_detailed_inventory.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_amazon_ledger_detailed_inventory_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_amazon_ledger_detailed_inventory_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "amazon_ledger_detailed_inventory/s3_to_sf_raw_amazon_ledger_detailed_inventory_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        insert_log_ledger_detailed_inv_task = PythonOperator(
            task_id="insert_log_ledger_detailed_inv",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ledger_detailed_inventory/insert_log_amazon_ledger_detailed.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        check_new_files_found_amazon_ledger_detailed_inventory_task = BranchPythonOperator(
            task_id='check_new_files_found_amazon_ledger_detailed_inventory',
            python_callable=check_for_new_files,
            op_kwargs={
                "args_file": "amazon_ledger_detailed_inventory/s3_to_snowflake_amazon_ledger_detailed_inventory.yaml",
                "args_file_second": "amazon_ledger_detailed_inventory/s3_to_snowflake_amazon_ledger_detailed_inventory_goessor.yaml",
                "skip_task_id": "load_amazon_ledger_detailed_inventory.skip_insert_amazon_ledger_detailed_inventory",
                "next_task_id": "load_amazon_ledger_detailed_inventory.begin_insert_amazon_ledger_detailed_inventory"
                },
        )

        dedupe_amazon_ledger_detailed_inventory_task = PythonOperator(
            task_id="dedupe_amazon_ledger_detailed_inventory",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ledger_detailed_inventory/dedupe_amazon_ledger_detailed_inventory.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_amazon_ledger_detailed_inventory_task = PythonOperator(
            task_id="merge_amazon_ledger_detailed_inventory",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ledger_detailed_inventory/insert_staging_amazon_ledger_detailed_inventory.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # unique constraint is not applicable for this table

        run_dq_is_null_dedupe_task = PythonOperator(
            task_id="run_dq_is_null_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_ledger_detailed_inventory',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amazon_ledger_detailed_inventory',
                    field_list=['sku', 'fnsku', 'asin', 'country', 'fulfillment_center', 'event_type', 'disposition'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_null_merge_task = PythonOperator(
            task_id="run_dq_is_null_merge",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.amazon_ledger_detailed_inventory_v2',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.amazon_ledger_detailed_inventory_v2',
                    field_list=['etl_key'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        data_freshness_check_t_1 = PythonOperator(
            task_id="data_freshness_check_t_1",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'data_freshness_check_t_1',
                'tb_name': "$stage_db.amazon_ledger_detailed_inventory_v2",
                'query_file': "amazon_ledger_detailed_inventory/amazon_ledger_detailed_dq.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        (
                [list_s3_files_amazon_ledger_detailed_inventory_task, list_s3_files_amazon_ledger_detailed_inventory_task_goessor] >>
                check_new_files_found_amazon_ledger_detailed_inventory_task >>
                [begin_insert_amazon_ledger_detailed_inventory_task, skip_insert_amazon_ledger_detailed_inventory_task]
        )

        (
                begin_insert_amazon_ledger_detailed_inventory_task >>
                s3_to_snowflake_amazon_ledger_detailed_inventory_task >>
                s3_to_snowflake_amazon_ledger_detailed_inventory_task_goessor >>
                insert_log_ledger_detailed_inv_task >>

                dedupe_amazon_ledger_detailed_inventory_task >>

                run_dq_is_null_dedupe_task >>

                merge_amazon_ledger_detailed_inventory_task >>

                run_dq_is_null_merge_task >>
                data_freshness_check_t_1 >>
                end_insert_amazon_ledger_detailed_inventory_task
        )

        #  --- Load amazon_ledger_summary_inventory ---

    with TaskGroup(group_id='load_amazon_ledger_summary_inventory') as tg_amazon_ledger_summary_inventory:
        list_s3_files_amazon_ledger_summary_inventory_task = PythonOperator(
            task_id="list_s3_files_amazon_ledger_summary_inventory",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ledger_summary_inventory/list_s3_amazon_ledger_summary_inventory.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_amazon_ledger_summary_inventory_task_goessor = PythonOperator(
            task_id="list_s3_files_amazon_ledger_summary_inventory_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ledger_summary_inventory/list_s3_amazon_ledger_summary_inventory_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_amazon_ledger_summary_inventory_task = DummyOperator(
            task_id="begin_insert_amazon_ledger_summary_inventory")
        skip_insert_amazon_ledger_summary_inventory_task = DummyOperator(
            task_id="skip_insert_amazon_ledger_summary_inventory")
        end_insert_amazon_ledger_summary_inventory_task = DummyOperator(
            task_id="end_insert_amazon_ledger_summary_inventory")

        s3_to_snowflake_amazon_ledger_summary_inventory_task = PythonOperator(
            task_id="s3_to_snowflake_amazon_ledger_summary_inventory",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "amazon_ledger_summary_inventory/s3_to_sf_raw_amazon_ledger_summary_inventory.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_amazon_ledger_summary_inventory_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_amazon_ledger_summary_inventory_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "amazon_ledger_summary_inventory/s3_to_sf_raw_amazon_ledger_summary_inventory_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        insert_log_ledger_summary_task = PythonOperator(
            task_id="insert_log_ledger_summary",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ledger_summary_inventory/insert_log_amazon_ledger_summary.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        check_new_files_found_amazon_ledger_summary_inventory_task = BranchPythonOperator(
            task_id='check_new_files_found_amazon_ledger_summary_inventory',
            python_callable=check_for_new_files,
            op_kwargs={
                "args_file": "amazon_ledger_summary_inventory/s3_to_snowflake_amazon_ledger_summary_inventory.yaml",
                "args_file_second": "amazon_ledger_summary_inventory/s3_to_snowflake_amazon_ledger_summary_inventory_goessor.yaml",
                "skip_task_id": "load_amazon_ledger_summary_inventory.skip_insert_amazon_ledger_summary_inventory",
                "next_task_id": "load_amazon_ledger_summary_inventory.begin_insert_amazon_ledger_summary_inventory"
                },
        )

        dedupe_amazon_ledger_summary_inventory_task = PythonOperator(
            task_id="dedupe_amazon_ledger_summary_inventory",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ledger_summary_inventory/dedupe_amazon_ledger_summary_inventory.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_amazon_ledger_summary_inventory_task = PythonOperator(
            task_id="merge_amazon_ledger_summary_inventory",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ledger_summary_inventory/merge_staging_amazon_ledger_summary_inventory.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_unique_dedupe_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_ledger_summary_inventory',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amazon_ledger_summary_inventory',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_unique_merge_task = PythonOperator(
            task_id="run_dq_is_unique_merge",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.amazon_ledger_summary_inventory_v2',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.amazon_ledger_summary_inventory_v2',
                    field_list=['inventory_pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_null_dedupe_task = PythonOperator(
            task_id="run_dq_is_null_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_ledger_summary_inventory',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amazon_ledger_summary_inventory',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_null_merge_task = PythonOperator(
            task_id="run_dq_is_null_merge",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.amazon_ledger_summary_inventory_v2',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.amazon_ledger_summary_inventory_v2',
                    field_list=['inventory_pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        data_freshness_check_t_2 = PythonOperator(
            task_id="data_freshness_check_t_2",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'data_freshness_check_t_2',
                'tb_name': "$stage_db.amazon_ledger_summary_inventory_v2",
                'query_file': "amazon_ledger_summary_inventory/amazon_ledger_summary_dq.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        (
                [list_s3_files_amazon_ledger_summary_inventory_task, list_s3_files_amazon_ledger_summary_inventory_task_goessor] >>
                check_new_files_found_amazon_ledger_summary_inventory_task >>
                [begin_insert_amazon_ledger_summary_inventory_task,
                 skip_insert_amazon_ledger_summary_inventory_task]
        )

        (
                begin_insert_amazon_ledger_summary_inventory_task >>
                s3_to_snowflake_amazon_ledger_summary_inventory_task >>
                s3_to_snowflake_amazon_ledger_summary_inventory_task_goessor >>
                insert_log_ledger_summary_task >>
                dedupe_amazon_ledger_summary_inventory_task >>
                run_dq_is_unique_dedupe_task >>

                run_dq_is_null_dedupe_task >>

                merge_amazon_ledger_summary_inventory_task >>

                run_dq_is_unique_merge_task >>

                run_dq_is_null_merge_task >>
                data_freshness_check_t_2 >>
                end_insert_amazon_ledger_summary_inventory_task
        )

        #  --- Load fba_stranded_inv ---

    with TaskGroup(group_id='load_fba_stranded_inv') as tg_fba_stranded_inv:
        list_s3_files_fba_stranded_inv_task = PythonOperator(
            task_id="list_s3_files_fba_stranded_inv",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "stranded_fba_inv/list_s3_fba_stranded_inv.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_fba_stranded_inv_task_goessor = PythonOperator(
            task_id="list_s3_files_fba_stranded_inv_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "stranded_fba_inv/list_s3_fba_stranded_inv_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_fba_stranded_inv_task = DummyOperator(task_id="begin_insert_fba_stranded_inv")
        skip_insert_fba_stranded_inv_task = DummyOperator(task_id="skip_insert_fba_stranded_inv")
        end_insert_fba_stranded_inv_task = DummyOperator(task_id="end_insert_fba_stranded_inv")
        end_fba_stranded_inv_task = DummyOperator(
            task_id="end_fba_stranded_inv",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_fba_stranded_inv_task = BranchPythonOperator(
            task_id='check_new_files_found_fba_stranded_inv',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "stranded_fba_inv/s3_to_snowflake_fba_stranded_inv.yaml",
                       "args_file_second": "stranded_fba_inv/s3_to_snowflake_fba_stranded_inv_goessor.yaml",
                       "skip_task_id": "load_fba_stranded_inv.skip_insert_fba_stranded_inv",
                       "next_task_id": "load_fba_stranded_inv.begin_insert_fba_stranded_inv"
                       },
        )

        s3_to_snowflake_fba_stranded_inv_task = PythonOperator(
            task_id="s3_to_snowflake_fba_stranded_inv",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "stranded_fba_inv/s3_to_sf_raw_fba_stranded_inv.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_fba_stranded_inv_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_fba_stranded_inv_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "stranded_fba_inv/s3_to_sf_raw_fba_stranded_inv_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_fba_stranded_inv_task = PythonOperator(
            task_id="insert_log_fba_stranded_inv",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "inventory/amazon/fba_inventory/stranded_fba_inv/insert_log_fba_stranded_inv.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_fba_stranded_inv_task = PythonOperator(
            task_id="dedupe_fba_stranded_inv",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "inventory/amazon/fba_inventory/stranded_fba_inv/dedupe_fba_stranded_inv.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_fba_stranded_inv',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_fba_stranded_inv',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_fba_stranded_inv',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_fba_stranded_inv',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_fba_stranded_inv_task = PythonOperator(
            task_id="merge_stage_fba_stranded_inv",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "inventory/amazon/fba_inventory/stranded_fba_inv/merge_stage_fba_stranded_inv.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.fact_amazon_stranded_inventory',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$curated_db.fact_amazon_stranded_inventory',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.fact_amazon_stranded_inventory',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$curated_db.fact_amazon_stranded_inventory',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_fba_stranded_inv_task = PythonOperator(
            task_id="run_audit_fba_stranded_inv",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_amazon_stranded_inventory",  # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                [list_s3_files_fba_stranded_inv_task, list_s3_files_fba_stranded_inv_task_goessor] >>
                check_new_files_found_fba_stranded_inv_task >>
                [begin_insert_fba_stranded_inv_task, skip_insert_fba_stranded_inv_task]
        )

        (
                begin_insert_fba_stranded_inv_task >>
                s3_to_snowflake_fba_stranded_inv_task >>
                s3_to_snowflake_fba_stranded_inv_task_goessor >>

                insert_log_fba_stranded_inv_task >>
                dedupe_fba_stranded_inv_task >>

                (run_dq_is_unique_dedupe_pk_hard_task,

                 run_dq_is_null_dedupe_pk_hard_task) >>

                merge_stage_fba_stranded_inv_task >>

                (run_dq_is_unique_merge_pk_hard_task,

                 run_dq_is_null_merge_pk_hard_task) >>

                run_audit_fba_stranded_inv_task >>
                end_insert_fba_stranded_inv_task >>
                end_fba_stranded_inv_task
        )

        skip_insert_fba_stranded_inv_task >> end_fba_stranded_inv_task

    #  --- Load suppressed_listing_amazon ---

    with TaskGroup(group_id='load_suppressed_listing_amazon') as tg_suppressed_listing_amazon:
        list_s3_files_suppressed_listing_amazon_task = PythonOperator(
            task_id="list_s3_files_suppressed_listing_amazon",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "suppressed_listing/list_s3_suppressed_listing_amazon.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_suppressed_listing_amazon_task_goessor = PythonOperator(
            task_id="list_s3_files_suppressed_listing_amazon_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "suppressed_listing/list_s3_suppressed_listing_amazon_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_suppressed_listing_amazon_task = DummyOperator(
            task_id="begin_insert_suppressed_listing_amazon")
        skip_insert_suppressed_listing_amazon_task = DummyOperator(task_id="skip_insert_suppressed_listing_amazon")
        end_insert_suppressed_listing_amazon_task = DummyOperator(task_id="end_insert_suppressed_listing_amazon")
        end_suppressed_listing_amazon_task = DummyOperator(
            task_id="end_suppressed_listing_amazon",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_suppressed_listing_amazon_task = BranchPythonOperator(
            task_id='check_new_files_found_suppressed_listing_amazon',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "suppressed_listing/s3_to_snowflake_suppressed_listing_amazon.yaml",
                       "args_file_second": "suppressed_listing/s3_to_snowflake_suppressed_listing_amazon_goessor.yaml",
                       "skip_task_id": "load_suppressed_listing_amazon.skip_insert_suppressed_listing_amazon",
                       "next_task_id": "load_suppressed_listing_amazon.begin_insert_suppressed_listing_amazon"
                       },
        )

        s3_to_snowflake_suppressed_listing_amazon_task = PythonOperator(
            task_id="s3_to_snowflake_suppressed_listing_amazon",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "suppressed_listing/s3_to_sf_raw_suppressed_listing_amazon.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_suppressed_listing_amazon_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_suppressed_listing_amazon_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "suppressed_listing/s3_to_sf_raw_suppressed_listing_amazon_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_suppressed_listing_amazon_task = PythonOperator(
            task_id="insert_log_suppressed_listing_amazon",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "suppressed_listing/insert_log_suppressed_listing_amazon.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_suppressed_listing_amazon_task = PythonOperator(
            task_id="dedupe_suppressed_listing_amazon",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "suppressed_listing/dedupe_suppressed_listing_amazon.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_suppressed_listing_amazon',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_suppressed_listing_amazon',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_suppressed_listing_amazon',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_suppressed_listing_amazon',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_suppressed_listing_amazon_task = PythonOperator(
            task_id="merge_stage_suppressed_listing_amazon",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "suppressed_listing/merge_stage_suppressed_listing_amazon.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.fact_amazon_suppressed_listing',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$curated_db.fact_amazon_suppressed_listing',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.fact_amazon_suppressed_listing',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$curated_db.fact_amazon_suppressed_listing',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_suppressed_listing_amazon_task = PythonOperator(
            task_id="run_audit_suppressed_listing_amazon",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_amazon_suppressed_listing",
                # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                [list_s3_files_suppressed_listing_amazon_task, list_s3_files_suppressed_listing_amazon_task_goessor] >>
                check_new_files_found_suppressed_listing_amazon_task >>
                [begin_insert_suppressed_listing_amazon_task, skip_insert_suppressed_listing_amazon_task]
        )

        (
                begin_insert_suppressed_listing_amazon_task >>
                s3_to_snowflake_suppressed_listing_amazon_task >>
                s3_to_snowflake_suppressed_listing_amazon_task_goessor >>

                insert_log_suppressed_listing_amazon_task >>
                dedupe_suppressed_listing_amazon_task >>

                (run_dq_is_unique_dedupe_pk_hard_task,

                 run_dq_is_null_dedupe_pk_hard_task) >>

                merge_stage_suppressed_listing_amazon_task >>

                (run_dq_is_unique_merge_pk_hard_task,

                 run_dq_is_null_merge_pk_hard_task) >>

                run_audit_suppressed_listing_amazon_task >>
                end_insert_suppressed_listing_amazon_task >>
                end_suppressed_listing_amazon_task
        )

        skip_insert_suppressed_listing_amazon_task >> end_suppressed_listing_amazon_task
    # ---- Main branch ----
    chain(
        begin,
        get_workflow_params,
        [tg_amazon_ledger_detailed_inventory,tg_amazon_ledger_summary_inventory,tg_fba_stranded_inv,tg_suppressed_listing_amazon],
        update_workflow_params,
        end
    )

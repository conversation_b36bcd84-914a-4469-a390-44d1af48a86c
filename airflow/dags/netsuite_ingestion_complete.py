"""
Complete NetSuite Ingestion DAG with all table task groups
Generated from the original netsuite_ingestion.py pattern
"""
import logging
from datetime import datetime, timedelta
from airflow import DAG

from airflow.models import Variable
# Removed unused chain import
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON>ythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = '2'
DAG_ID = 'netsuite_ingestion_complete'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@hourly')
RELEASE_DEF = '2'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)


def run_query_file(sql_file: str = None, wf_params: str = None, connection: str = 'Snowflake', **context):
    import ast
    from db_connectors.sf_connector import Snowflake
    from helpers.helper import get_absolute_sql_file_path, load_db_variables, multi_replace_string, set_query_tag

    # read schema, database names from application configuration for variables used in the sql
    sql_vars_dict = load_db_variables()

    sql_file_abs_path = get_absolute_sql_file_path(sql_file)
    log.info(f"sql file is {sql_file_abs_path}")

    # read query file contents
    sql_text = ""
    with open(sql_file_abs_path, 'r') as f:
        sql_text = f.read()

    # remove leading and trailing spaces, tabs
    if sql_text:
        sql_text = sql_text.strip()

    # convert params dictionary string to dictionary
    wf_params_dict = ast.literal_eval(wf_params)

    sql_vars_dict['start_ts'] = wf_params_dict.get('wf_start_ts_iso_utc')
    sql_vars_dict['end_ts'] = wf_params_dict.get('wf_end_ts_iso_utc')

    # replace variables names with the appropriate values
    sql_text2 = multi_replace_string(sql_text, sql_vars_dict)
    sql_text2 = multi_replace_string(sql_text2, wf_params_dict)

    log.info("queries to be executed:")
    log.info(sql_text2)

    query_tag = set_query_tag(sql_file=sql_file, **context)

    # make a list of queries using ; as query delimiter
    query_list = sql_text2.split(';')

    # invoke library call to execute the queries
    sf = Snowflake('netsuite_conn')
    sf.execute_multi_statements(query_list, query_tag=query_tag)


with DAG(
    dag_id=DAG_ID,
    description=f'Complete NetSuite Ingestion DAG - Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2025, 1, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    dagrun_timeout=timedelta(minutes=180),  # Increased timeout for all tables
    max_active_runs=1,
    params={'workflow_name': 'NETSUITE_INGESTION_COMPLETE',
            'author': 'thinhnd'},
    tags=['thinhnd', 'Raptor', 'NetSuite', 'Complete']
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

    begin = DummyOperator(task_id="begin", depends_on_past=False, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

        #  ----[ The task groups for each table load ]----

    #  --- Load raw_account ---
    
    # with TaskGroup(group_id='load_raw_account') as tg_raw_account:
    #     list_s3_files_raw_account_task = PythonOperator(
    #         task_id="list_s3_account",
    #         python_callable=tls3.list_s3_modified_files,
    #         op_kwargs={
    #             "args_file": "netsuite_ingestion/list_s3_account.yaml",
    #             "wf_params": WF_PARAMS_EXPR},
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #
    #     )
    #
    #     # Need a begin dummy operator for branching
    #     begin_insert_raw_account_task = DummyOperator(task_id="begin_insert_raw_account")
    #     skip_insert_raw_account_task = DummyOperator(task_id="skip_insert_raw_account")
    #     end_insert_raw_account_task = DummyOperator(task_id="end_insert_raw_account")
    #     end_raw_account_task = DummyOperator(
    #         task_id="end_raw_account",
    #         trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    #     )
    #
    #     check_new_files_found_raw_account_task = BranchPythonOperator(
    #         task_id='check_new_files_found_raw_account',
    #         python_callable=check_for_new_files,
    #         op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_account.yaml",
    #                    "skip_task_id": "load_raw_account.skip_insert_raw_account",
    #                    "next_task_id": "load_raw_account.begin_insert_raw_account"
    #         },
    #     )
    #
    #     s3_to_snowflake_raw_account_task = PythonOperator(
    #         task_id="s3_to_snowflake_raw_account",
    #         python_callable=load_obj.s3_to_snowflake_load,
    #         op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_account.yaml"},
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     insert_log_raw_account_task = PythonOperator(
    #         task_id="insert_log_raw_account",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={"connection":"Snowflake",
    #                    "sql_file": "netsuite_ingestion/insert_log_account.sql",
    #                    "wf_params": WF_PARAMS_EXPR
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     append_to_account_table = PythonOperator(
    #         task_id="insert_account",
    #         python_callable=run_query_file,
    #         op_kwargs={"connection":"Snowflake",
    #                    "sql_file": "netsuite_ingestion/insert_account.sql",
    #                    "wf_params": WF_PARAMS_EXPR
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     (
    #         list_s3_files_raw_account_task >>
    #         check_new_files_found_raw_account_task >>
    #         [begin_insert_raw_account_task, skip_insert_raw_account_task]
    #     )
    #
    #     (
    #             begin_insert_raw_account_task >>
    #             s3_to_snowflake_raw_account_task >>
    #             insert_log_raw_account_task >>
    #             append_to_account_table >>
    #             end_insert_raw_account_task >>
    #             end_raw_account_task
    #     )
    #
    #     skip_insert_raw_account_task >> end_raw_account_task

    #  --- Load raw_accountingbook ---
    
    with TaskGroup(group_id='load_raw_accountingbook') as tg_raw_accountingbook:
        list_s3_files_raw_accountingbook_task = PythonOperator(
            task_id="list_s3_accountingbook",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_accountingbook.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            
        )

        # Need a begin dummy operator for branching
        begin_insert_raw_accountingbook_task = DummyOperator(task_id="begin_insert_raw_accountingbook")
        skip_insert_raw_accountingbook_task = DummyOperator(task_id="skip_insert_raw_accountingbook")
        end_insert_raw_accountingbook_task = DummyOperator(task_id="end_insert_raw_accountingbook")
        end_raw_accountingbook_task = DummyOperator(
            task_id="end_raw_accountingbook",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_raw_accountingbook_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_accountingbook',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_accountingbook.yaml",
                       "skip_task_id": "load_raw_accountingbook.skip_insert_raw_accountingbook",
                       "next_task_id": "load_raw_accountingbook.begin_insert_raw_accountingbook"
            },
        )

        s3_to_snowflake_raw_accountingbook_task = PythonOperator(
            task_id="s3_to_snowflake_raw_accountingbook",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_accountingbook.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_accountingbook_task = PythonOperator(
            task_id="insert_log_raw_accountingbook",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_accountingbook.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_accountingbook_table = PythonOperator(
            task_id="insert_accountingbook",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_accountingbook.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_accountingbook_task >>
            check_new_files_found_raw_accountingbook_task >>
            [begin_insert_raw_accountingbook_task, skip_insert_raw_accountingbook_task]
        )

        (
                begin_insert_raw_accountingbook_task >>
                s3_to_snowflake_raw_accountingbook_task >>
                insert_log_raw_accountingbook_task >>
                append_to_accountingbook_table >>
                end_insert_raw_accountingbook_task >>
                end_raw_accountingbook_task
        )

        skip_insert_raw_accountingbook_task >> end_raw_accountingbook_task

    #  --- Load raw_accountingperiod ---
    
    # with TaskGroup(group_id='load_raw_accountingperiod') as tg_raw_accountingperiod:
    #     list_s3_files_raw_accountingperiod_task = PythonOperator(
    #         task_id="list_s3_accountingperiod",
    #         python_callable=tls3.list_s3_modified_files,
    #         op_kwargs={
    #             "args_file": "netsuite_ingestion/list_s3_accountingperiod.yaml",
    #             "wf_params": WF_PARAMS_EXPR},
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #
    #     )
    #
    #     # Need a begin dummy operator for branching
    #     begin_insert_raw_accountingperiod_task = DummyOperator(task_id="begin_insert_raw_accountingperiod")
    #     skip_insert_raw_accountingperiod_task = DummyOperator(task_id="skip_insert_raw_accountingperiod")
    #     end_insert_raw_accountingperiod_task = DummyOperator(task_id="end_insert_raw_accountingperiod")
    #     end_raw_accountingperiod_task = DummyOperator(
    #         task_id="end_raw_accountingperiod",
    #         trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    #     )
    #
    #     check_new_files_found_raw_accountingperiod_task = BranchPythonOperator(
    #         task_id='check_new_files_found_raw_accountingperiod',
    #         python_callable=check_for_new_files,
    #         op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_accountingperiod.yaml",
    #                    "skip_task_id": "load_raw_accountingperiod.skip_insert_raw_accountingperiod",
    #                    "next_task_id": "load_raw_accountingperiod.begin_insert_raw_accountingperiod"
    #         },
    #     )
    #
    #     s3_to_snowflake_raw_accountingperiod_task = PythonOperator(
    #         task_id="s3_to_snowflake_raw_accountingperiod",
    #         python_callable=load_obj.s3_to_snowflake_load,
    #         op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_accountingperiod.yaml"},
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     insert_log_raw_accountingperiod_task = PythonOperator(
    #         task_id="insert_log_raw_accountingperiod",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={"connection":"Snowflake",
    #                    "sql_file": "netsuite_ingestion/insert_log_accountingperiod.sql",
    #                    "wf_params": WF_PARAMS_EXPR
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     append_to_accountingperiod_table = PythonOperator(
    #         task_id="insert_accountingperiod",
    #         python_callable=run_query_file,
    #         op_kwargs={"connection":"Snowflake",
    #                    "sql_file": "netsuite_ingestion/insert_accountingperiod.sql",
    #                    "wf_params": WF_PARAMS_EXPR
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     (
    #         list_s3_files_raw_accountingperiod_task >>
    #         check_new_files_found_raw_accountingperiod_task >>
    #         [begin_insert_raw_accountingperiod_task, skip_insert_raw_accountingperiod_task]
    #     )
    #
    #     (
    #             begin_insert_raw_accountingperiod_task >>
    #             s3_to_snowflake_raw_accountingperiod_task >>
    #             insert_log_raw_accountingperiod_task >>
    #             append_to_accountingperiod_table >>
    #             end_insert_raw_accountingperiod_task >>
    #             end_raw_accountingperiod_task
    #     )
    #
    #     skip_insert_raw_accountingperiod_task >> end_raw_accountingperiod_task

    #  --- Load raw_approvalstatus ---
    
    # with TaskGroup(group_id='load_raw_approvalstatus') as tg_raw_approvalstatus:
    #     list_s3_files_raw_approvalstatus_task = PythonOperator(
    #         task_id="list_s3_approvalstatus",
    #         python_callable=tls3.list_s3_modified_files,
    #         op_kwargs={
    #             "args_file": "netsuite_ingestion/list_s3_approvalstatus.yaml",
    #             "wf_params": WF_PARAMS_EXPR},
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #
    #     )
    #
    #     # Need a begin dummy operator for branching
    #     begin_insert_raw_approvalstatus_task = DummyOperator(task_id="begin_insert_raw_approvalstatus")
    #     skip_insert_raw_approvalstatus_task = DummyOperator(task_id="skip_insert_raw_approvalstatus")
    #     end_insert_raw_approvalstatus_task = DummyOperator(task_id="end_insert_raw_approvalstatus")
    #     end_raw_approvalstatus_task = DummyOperator(
    #         task_id="end_raw_approvalstatus",
    #         trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    #     )
    #
    #     check_new_files_found_raw_approvalstatus_task = BranchPythonOperator(
    #         task_id='check_new_files_found_raw_approvalstatus',
    #         python_callable=check_for_new_files,
    #         op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_approvalstatus.yaml",
    #                    "skip_task_id": "load_raw_approvalstatus.skip_insert_raw_approvalstatus",
    #                    "next_task_id": "load_raw_approvalstatus.begin_insert_raw_approvalstatus"
    #         },
    #     )
    #
    #     s3_to_snowflake_raw_approvalstatus_task = PythonOperator(
    #         task_id="s3_to_snowflake_raw_approvalstatus",
    #         python_callable=load_obj.s3_to_snowflake_load,
    #         op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_approvalstatus.yaml"},
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     insert_log_raw_approvalstatus_task = PythonOperator(
    #         task_id="insert_log_raw_approvalstatus",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={"connection":"Snowflake",
    #                    "sql_file": "netsuite_ingestion/insert_log_approvalstatus.sql",
    #                    "wf_params": WF_PARAMS_EXPR
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     append_to_approvalstatus_table = PythonOperator(
    #         task_id="insert_approvalstatus",
    #         python_callable=run_query_file,
    #         op_kwargs={"connection":"Snowflake",
    #                    "sql_file": "netsuite_ingestion/insert_approvalstatus.sql",
    #                    "wf_params": WF_PARAMS_EXPR
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     (
    #         list_s3_files_raw_approvalstatus_task >>
    #         check_new_files_found_raw_approvalstatus_task >>
    #         [begin_insert_raw_approvalstatus_task, skip_insert_raw_approvalstatus_task]
    #     )
    #
    #     (
    #             begin_insert_raw_approvalstatus_task >>
    #             s3_to_snowflake_raw_approvalstatus_task >>
    #             insert_log_raw_approvalstatus_task >>
    #             append_to_approvalstatus_table >>
    #             end_insert_raw_approvalstatus_task >>
    #             end_raw_approvalstatus_task
    #     )
    #
    #     skip_insert_raw_approvalstatus_task >> end_raw_approvalstatus_task

    #  --- Load raw_bom ---

    with TaskGroup(group_id='load_raw_bom') as tg_raw_bom:
        list_s3_files_raw_bom_task = PythonOperator(
            task_id="list_s3_bom",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_bom.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_bom_task = DummyOperator(task_id="begin_insert_raw_bom")
        skip_insert_raw_bom_task = DummyOperator(task_id="skip_insert_raw_bom")
        end_insert_raw_bom_task = DummyOperator(task_id="end_insert_raw_bom")
        end_raw_bom_task = DummyOperator(
            task_id="end_raw_bom",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_bom_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_bom',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_bom.yaml",
                       "skip_task_id": "load_raw_bom.skip_insert_raw_bom",
                       "next_task_id": "load_raw_bom.begin_insert_raw_bom"
            },
        )

        s3_to_snowflake_raw_bom_task = PythonOperator(
            task_id="s3_to_snowflake_raw_bom",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_bom.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_bom_task = PythonOperator(
            task_id="insert_log_raw_bom",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_bom.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_bom_table = PythonOperator(
            task_id="insert_bom",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_bom.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_bom_task >>
            check_new_files_found_raw_bom_task >>
            [begin_insert_raw_bom_task, skip_insert_raw_bom_task]
        )

        (
                begin_insert_raw_bom_task >>
                s3_to_snowflake_raw_bom_task >>
                insert_log_raw_bom_task >>
                append_to_bom_table >>
                end_insert_raw_bom_task >>
                end_raw_bom_task
        )

        skip_insert_raw_bom_task >> end_raw_bom_task

    #  --- Load raw_bomrevision ---

    with TaskGroup(group_id='load_raw_bomrevision') as tg_raw_bomrevision:
        list_s3_files_raw_bomrevision_task = PythonOperator(
            task_id="list_s3_bomrevision",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_bomrevision.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_bomrevision_task = DummyOperator(task_id="begin_insert_raw_bomrevision")
        skip_insert_raw_bomrevision_task = DummyOperator(task_id="skip_insert_raw_bomrevision")
        end_insert_raw_bomrevision_task = DummyOperator(task_id="end_insert_raw_bomrevision")
        end_raw_bomrevision_task = DummyOperator(
            task_id="end_raw_bomrevision",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_bomrevision_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_bomrevision',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_bomrevision.yaml",
                       "skip_task_id": "load_raw_bomrevision.skip_insert_raw_bomrevision",
                       "next_task_id": "load_raw_bomrevision.begin_insert_raw_bomrevision"
            },
        )

        s3_to_snowflake_raw_bomrevision_task = PythonOperator(
            task_id="s3_to_snowflake_raw_bomrevision",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_bomrevision.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_bomrevision_task = PythonOperator(
            task_id="insert_log_raw_bomrevision",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_bomrevision.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_bomrevision_table = PythonOperator(
            task_id="insert_bomrevision",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_bomrevision.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_bomrevision_task >>
            check_new_files_found_raw_bomrevision_task >>
            [begin_insert_raw_bomrevision_task, skip_insert_raw_bomrevision_task]
        )

        (
                begin_insert_raw_bomrevision_task >>
                s3_to_snowflake_raw_bomrevision_task >>
                insert_log_raw_bomrevision_task >>
                append_to_bomrevision_table >>
                end_insert_raw_bomrevision_task >>
                end_raw_bomrevision_task
        )

        skip_insert_raw_bomrevision_task >> end_raw_bomrevision_task

    #  --- Load raw_classification ---

    with TaskGroup(group_id='load_raw_classification') as tg_raw_classification:
        list_s3_files_raw_classification_task = PythonOperator(
            task_id="list_s3_classification",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_classification.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_classification_task = DummyOperator(task_id="begin_insert_raw_classification")
        skip_insert_raw_classification_task = DummyOperator(task_id="skip_insert_raw_classification")
        end_insert_raw_classification_task = DummyOperator(task_id="end_insert_raw_classification")
        end_raw_classification_task = DummyOperator(
            task_id="end_raw_classification",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_classification_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_classification',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_classification.yaml",
                       "skip_task_id": "load_raw_classification.skip_insert_raw_classification",
                       "next_task_id": "load_raw_classification.begin_insert_raw_classification"
            },
        )

        s3_to_snowflake_raw_classification_task = PythonOperator(
            task_id="s3_to_snowflake_raw_classification",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_classification.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_classification_task = PythonOperator(
            task_id="insert_log_raw_classification",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_classification.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_classification_table = PythonOperator(
            task_id="insert_classification",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_classification.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_classification_task >>
            check_new_files_found_raw_classification_task >>
            [begin_insert_raw_classification_task, skip_insert_raw_classification_task]
        )

        (
                begin_insert_raw_classification_task >>
                s3_to_snowflake_raw_classification_task >>
                insert_log_raw_classification_task >>
                append_to_classification_table >>
                end_insert_raw_classification_task >>
                end_raw_classification_task
        )

        skip_insert_raw_classification_task >> end_raw_classification_task

    #  --- Load raw_currency ---

    with TaskGroup(group_id='load_raw_currency') as tg_raw_currency:
        list_s3_files_raw_currency_task = PythonOperator(
            task_id="list_s3_currency",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_currency.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_currency_task = DummyOperator(task_id="begin_insert_raw_currency")
        skip_insert_raw_currency_task = DummyOperator(task_id="skip_insert_raw_currency")
        end_insert_raw_currency_task = DummyOperator(task_id="end_insert_raw_currency")
        end_raw_currency_task = DummyOperator(
            task_id="end_raw_currency",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_currency_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_currency',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_currency.yaml",
                       "skip_task_id": "load_raw_currency.skip_insert_raw_currency",
                       "next_task_id": "load_raw_currency.begin_insert_raw_currency"
            },
        )

        s3_to_snowflake_raw_currency_task = PythonOperator(
            task_id="s3_to_snowflake_raw_currency",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_currency.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_currency_task = PythonOperator(
            task_id="insert_log_raw_currency",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_currency.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_currency_table = PythonOperator(
            task_id="insert_currency",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_currency.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_currency_task >>
            check_new_files_found_raw_currency_task >>
            [begin_insert_raw_currency_task, skip_insert_raw_currency_task]
        )

        (
                begin_insert_raw_currency_task >>
                s3_to_snowflake_raw_currency_task >>
                insert_log_raw_currency_task >>
                append_to_currency_table >>
                end_insert_raw_currency_task >>
                end_raw_currency_task
        )

        skip_insert_raw_currency_task >> end_raw_currency_task

    #  --- Load raw_currencyrate ---

    with TaskGroup(group_id='load_raw_currencyrate') as tg_raw_currencyrate:
        list_s3_files_raw_currencyrate_task = PythonOperator(
            task_id="list_s3_currencyrate",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_currencyrate.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_currencyrate_task = DummyOperator(task_id="begin_insert_raw_currencyrate")
        skip_insert_raw_currencyrate_task = DummyOperator(task_id="skip_insert_raw_currencyrate")
        end_insert_raw_currencyrate_task = DummyOperator(task_id="end_insert_raw_currencyrate")
        end_raw_currencyrate_task = DummyOperator(
            task_id="end_raw_currencyrate",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_currencyrate_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_currencyrate',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_currencyrate.yaml",
                       "skip_task_id": "load_raw_currencyrate.skip_insert_raw_currencyrate",
                       "next_task_id": "load_raw_currencyrate.begin_insert_raw_currencyrate"
            },
        )

        s3_to_snowflake_raw_currencyrate_task = PythonOperator(
            task_id="s3_to_snowflake_raw_currencyrate",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_currencyrate.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_currencyrate_task = PythonOperator(
            task_id="insert_log_raw_currencyrate",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_currencyrate.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_currencyrate_table = PythonOperator(
            task_id="insert_currencyrate",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_currencyrate.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_currencyrate_task >>
            check_new_files_found_raw_currencyrate_task >>
            [begin_insert_raw_currencyrate_task, skip_insert_raw_currencyrate_task]
        )

        (
                begin_insert_raw_currencyrate_task >>
                s3_to_snowflake_raw_currencyrate_task >>
                insert_log_raw_currencyrate_task >>
                append_to_currencyrate_table >>
                end_insert_raw_currencyrate_task >>
                end_raw_currencyrate_task
        )

        skip_insert_raw_currencyrate_task >> end_raw_currencyrate_task

    #  --- Load raw_customer ---

    # with TaskGroup(group_id='load_raw_customer') as tg_raw_customer:
    #     list_s3_files_raw_customer_task = PythonOperator(
    #         task_id="list_s3_customer",
    #         python_callable=tls3.list_s3_modified_files,
    #         op_kwargs={
    #             "args_file": "netsuite_ingestion/list_s3_customer.yaml",
    #             "wf_params": WF_PARAMS_EXPR},
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #
    #     )
    #
    #     # Need a begin dummy operator for branching
    #     begin_insert_raw_customer_task = DummyOperator(task_id="begin_insert_raw_customer")
    #     skip_insert_raw_customer_task = DummyOperator(task_id="skip_insert_raw_customer")
    #     end_insert_raw_customer_task = DummyOperator(task_id="end_insert_raw_customer")
    #     end_raw_customer_task = DummyOperator(
    #         task_id="end_raw_customer",
    #         trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    #     )
    #
    #     check_new_files_found_raw_customer_task = BranchPythonOperator(
    #         task_id='check_new_files_found_raw_customer',
    #         python_callable=check_for_new_files,
    #         op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customer.yaml",
    #                    "skip_task_id": "load_raw_customer.skip_insert_raw_customer",
    #                    "next_task_id": "load_raw_customer.begin_insert_raw_customer"
    #         },
    #     )
    #
    #     s3_to_snowflake_raw_customer_task = PythonOperator(
    #         task_id="s3_to_snowflake_raw_customer",
    #         python_callable=load_obj.s3_to_snowflake_load,
    #         op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customer.yaml"},
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     insert_log_raw_customer_task = PythonOperator(
    #         task_id="insert_log_raw_customer",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={"connection":"Snowflake",
    #                    "sql_file": "netsuite_ingestion/insert_log_customer.sql",
    #                    "wf_params": WF_PARAMS_EXPR
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     append_to_customer_table = PythonOperator(
    #         task_id="insert_customer",
    #         python_callable=run_query_file,
    #         op_kwargs={"connection":"Snowflake",
    #                    "sql_file": "netsuite_ingestion/insert_customer.sql",
    #                    "wf_params": WF_PARAMS_EXPR
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     (
    #         list_s3_files_raw_customer_task >>
    #         check_new_files_found_raw_customer_task >>
    #         [begin_insert_raw_customer_task, skip_insert_raw_customer_task]
    #     )
    #
    #     (
    #             begin_insert_raw_customer_task >>
    #             s3_to_snowflake_raw_customer_task >>
    #             insert_log_raw_customer_task >>
    #             append_to_customer_table >>
    #             end_insert_raw_customer_task >>
    #             end_raw_customer_task
    #     )
    #
    #     skip_insert_raw_customer_task >> end_raw_customer_task

    #  --- Load raw_customlist1702 ---

    with TaskGroup(group_id='load_raw_customlist1702') as tg_raw_customlist1702:
        list_s3_files_raw_customlist1702_task = PythonOperator(
            task_id="list_s3_customlist1702",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist1702.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist1702_task = DummyOperator(task_id="begin_insert_raw_customlist1702")
        skip_insert_raw_customlist1702_task = DummyOperator(task_id="skip_insert_raw_customlist1702")
        end_insert_raw_customlist1702_task = DummyOperator(task_id="end_insert_raw_customlist1702")
        end_raw_customlist1702_task = DummyOperator(
            task_id="end_raw_customlist1702",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist1702_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist1702',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist1702.yaml",
                       "skip_task_id": "load_raw_customlist1702.skip_insert_raw_customlist1702",
                       "next_task_id": "load_raw_customlist1702.begin_insert_raw_customlist1702"
            },
        )

        s3_to_snowflake_raw_customlist1702_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist1702",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist1702.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist1702_task = PythonOperator(
            task_id="insert_log_raw_customlist1702",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist1702.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist1702_table = PythonOperator(
            task_id="insert_customlist1702",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist1702.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist1702_task >>
            check_new_files_found_raw_customlist1702_task >>
            [begin_insert_raw_customlist1702_task, skip_insert_raw_customlist1702_task]
        )

        (
                begin_insert_raw_customlist1702_task >>
                s3_to_snowflake_raw_customlist1702_task >>
                insert_log_raw_customlist1702_task >>
                append_to_customlist1702_table >>
                end_insert_raw_customlist1702_task >>
                end_raw_customlist1702_task
        )

        skip_insert_raw_customlist1702_task >> end_raw_customlist1702_task

    #  --- Load raw_customlist_bd_itemmoqgroup ---

    with TaskGroup(group_id='load_raw_customlist_bd_itemmoqgroup') as tg_raw_customlist_bd_itemmoqgroup:
        list_s3_files_raw_customlist_bd_itemmoqgroup_task = PythonOperator(
            task_id="list_s3_customlist_bd_itemmoqgroup",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_bd_itemmoqgroup.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_bd_itemmoqgroup_task = DummyOperator(task_id="begin_insert_raw_customlist_bd_itemmoqgroup")
        skip_insert_raw_customlist_bd_itemmoqgroup_task = DummyOperator(task_id="skip_insert_raw_customlist_bd_itemmoqgroup")
        end_insert_raw_customlist_bd_itemmoqgroup_task = DummyOperator(task_id="end_insert_raw_customlist_bd_itemmoqgroup")
        end_raw_customlist_bd_itemmoqgroup_task = DummyOperator(
            task_id="end_raw_customlist_bd_itemmoqgroup",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_bd_itemmoqgroup_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_bd_itemmoqgroup',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_bd_itemmoqgroup.yaml",
                       "skip_task_id": "load_raw_customlist_bd_itemmoqgroup.skip_insert_raw_customlist_bd_itemmoqgroup",
                       "next_task_id": "load_raw_customlist_bd_itemmoqgroup.begin_insert_raw_customlist_bd_itemmoqgroup"
            },
        )

        s3_to_snowflake_raw_customlist_bd_itemmoqgroup_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_bd_itemmoqgroup",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_bd_itemmoqgroup.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_bd_itemmoqgroup_task = PythonOperator(
            task_id="insert_log_raw_customlist_bd_itemmoqgroup",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_bd_itemmoqgroup.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_bd_itemmoqgroup_table = PythonOperator(
            task_id="insert_customlist_bd_itemmoqgroup",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_bd_itemmoqgroup.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_bd_itemmoqgroup_task >>
            check_new_files_found_raw_customlist_bd_itemmoqgroup_task >>
            [begin_insert_raw_customlist_bd_itemmoqgroup_task, skip_insert_raw_customlist_bd_itemmoqgroup_task]
        )

        (
                begin_insert_raw_customlist_bd_itemmoqgroup_task >>
                s3_to_snowflake_raw_customlist_bd_itemmoqgroup_task >>
                insert_log_raw_customlist_bd_itemmoqgroup_task >>
                append_to_customlist_bd_itemmoqgroup_table >>
                end_insert_raw_customlist_bd_itemmoqgroup_task >>
                end_raw_customlist_bd_itemmoqgroup_task
        )

        skip_insert_raw_customlist_bd_itemmoqgroup_task >> end_raw_customlist_bd_itemmoqgroup_task

    #  --- Load raw_customlist_bd_pomoqgroup ---

    with TaskGroup(group_id='load_raw_customlist_bd_pomoqgroup') as tg_raw_customlist_bd_pomoqgroup:
        list_s3_files_raw_customlist_bd_pomoqgroup_task = PythonOperator(
            task_id="list_s3_customlist_bd_pomoqgroup",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_bd_pomoqgroup.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_bd_pomoqgroup_task = DummyOperator(task_id="begin_insert_raw_customlist_bd_pomoqgroup")
        skip_insert_raw_customlist_bd_pomoqgroup_task = DummyOperator(task_id="skip_insert_raw_customlist_bd_pomoqgroup")
        end_insert_raw_customlist_bd_pomoqgroup_task = DummyOperator(task_id="end_insert_raw_customlist_bd_pomoqgroup")
        end_raw_customlist_bd_pomoqgroup_task = DummyOperator(
            task_id="end_raw_customlist_bd_pomoqgroup",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_bd_pomoqgroup_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_bd_pomoqgroup',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_bd_pomoqgroup.yaml",
                       "skip_task_id": "load_raw_customlist_bd_pomoqgroup.skip_insert_raw_customlist_bd_pomoqgroup",
                       "next_task_id": "load_raw_customlist_bd_pomoqgroup.begin_insert_raw_customlist_bd_pomoqgroup"
            },
        )

        s3_to_snowflake_raw_customlist_bd_pomoqgroup_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_bd_pomoqgroup",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_bd_pomoqgroup.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_bd_pomoqgroup_task = PythonOperator(
            task_id="insert_log_raw_customlist_bd_pomoqgroup",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_bd_pomoqgroup.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_bd_pomoqgroup_table = PythonOperator(
            task_id="insert_customlist_bd_pomoqgroup",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_bd_pomoqgroup.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_bd_pomoqgroup_task >>
            check_new_files_found_raw_customlist_bd_pomoqgroup_task >>
            [begin_insert_raw_customlist_bd_pomoqgroup_task, skip_insert_raw_customlist_bd_pomoqgroup_task]
        )

        (
                begin_insert_raw_customlist_bd_pomoqgroup_task >>
                s3_to_snowflake_raw_customlist_bd_pomoqgroup_task >>
                insert_log_raw_customlist_bd_pomoqgroup_task >>
                append_to_customlist_bd_pomoqgroup_table >>
                end_insert_raw_customlist_bd_pomoqgroup_task >>
                end_raw_customlist_bd_pomoqgroup_task
        )

        skip_insert_raw_customlist_bd_pomoqgroup_task >> end_raw_customlist_bd_pomoqgroup_task

    #  --- Load raw_customlist_brd_gp ---

    with TaskGroup(group_id='load_raw_customlist_brd_gp') as tg_raw_customlist_brd_gp:
        list_s3_files_raw_customlist_brd_gp_task = PythonOperator(
            task_id="list_s3_customlist_brd_gp",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_brd_gp.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_brd_gp_task = DummyOperator(task_id="begin_insert_raw_customlist_brd_gp")
        skip_insert_raw_customlist_brd_gp_task = DummyOperator(task_id="skip_insert_raw_customlist_brd_gp")
        end_insert_raw_customlist_brd_gp_task = DummyOperator(task_id="end_insert_raw_customlist_brd_gp")
        end_raw_customlist_brd_gp_task = DummyOperator(
            task_id="end_raw_customlist_brd_gp",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_brd_gp_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_brd_gp',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_brd_gp.yaml",
                       "skip_task_id": "load_raw_customlist_brd_gp.skip_insert_raw_customlist_brd_gp",
                       "next_task_id": "load_raw_customlist_brd_gp.begin_insert_raw_customlist_brd_gp"
            },
        )

        s3_to_snowflake_raw_customlist_brd_gp_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_brd_gp",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_brd_gp.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_brd_gp_task = PythonOperator(
            task_id="insert_log_raw_customlist_brd_gp",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_brd_gp.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_brd_gp_table = PythonOperator(
            task_id="insert_customlist_brd_gp",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_brd_gp.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_brd_gp_task >>
            check_new_files_found_raw_customlist_brd_gp_task >>
            [begin_insert_raw_customlist_brd_gp_task, skip_insert_raw_customlist_brd_gp_task]
        )

        (
                begin_insert_raw_customlist_brd_gp_task >>
                s3_to_snowflake_raw_customlist_brd_gp_task >>
                insert_log_raw_customlist_brd_gp_task >>
                append_to_customlist_brd_gp_table >>
                end_insert_raw_customlist_brd_gp_task >>
                end_raw_customlist_brd_gp_task
        )

        skip_insert_raw_customlist_brd_gp_task >> end_raw_customlist_brd_gp_task

    #  --- Load raw_customlist_brnd_packaging_type ---

    with TaskGroup(group_id='load_raw_customlist_brnd_packaging_type') as tg_raw_customlist_brnd_packaging_type:
        list_s3_files_raw_customlist_brnd_packaging_type_task = PythonOperator(
            task_id="list_s3_customlist_brnd_packaging_type",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_brnd_packaging_type.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_brnd_packaging_type_task = DummyOperator(task_id="begin_insert_raw_customlist_brnd_packaging_type")
        skip_insert_raw_customlist_brnd_packaging_type_task = DummyOperator(task_id="skip_insert_raw_customlist_brnd_packaging_type")
        end_insert_raw_customlist_brnd_packaging_type_task = DummyOperator(task_id="end_insert_raw_customlist_brnd_packaging_type")
        end_raw_customlist_brnd_packaging_type_task = DummyOperator(
            task_id="end_raw_customlist_brnd_packaging_type",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_brnd_packaging_type_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_brnd_packaging_type',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_brnd_packaging_type.yaml",
                       "skip_task_id": "load_raw_customlist_brnd_packaging_type.skip_insert_raw_customlist_brnd_packaging_type",
                       "next_task_id": "load_raw_customlist_brnd_packaging_type.begin_insert_raw_customlist_brnd_packaging_type"
            },
        )

        s3_to_snowflake_raw_customlist_brnd_packaging_type_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_brnd_packaging_type",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_brnd_packaging_type.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_brnd_packaging_type_task = PythonOperator(
            task_id="insert_log_raw_customlist_brnd_packaging_type",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_brnd_packaging_type.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_brnd_packaging_type_table = PythonOperator(
            task_id="insert_customlist_brnd_packaging_type",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_brnd_packaging_type.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_brnd_packaging_type_task >>
            check_new_files_found_raw_customlist_brnd_packaging_type_task >>
            [begin_insert_raw_customlist_brnd_packaging_type_task, skip_insert_raw_customlist_brnd_packaging_type_task]
        )

        (
                begin_insert_raw_customlist_brnd_packaging_type_task >>
                s3_to_snowflake_raw_customlist_brnd_packaging_type_task >>
                insert_log_raw_customlist_brnd_packaging_type_task >>
                append_to_customlist_brnd_packaging_type_table >>
                end_insert_raw_customlist_brnd_packaging_type_task >>
                end_raw_customlist_brnd_packaging_type_task
        )

        skip_insert_raw_customlist_brnd_packaging_type_task >> end_raw_customlist_brnd_packaging_type_task

    #  --- Load raw_customlist_consumerunitbarcode_type ---

    with TaskGroup(group_id='load_raw_customlist_consumerunitbarcode_type') as tg_raw_customlist_consumerunitbarcode_type:
        list_s3_files_raw_customlist_consumerunitbarcode_type_task = PythonOperator(
            task_id="list_s3_customlist_consumerunitbarcode_type",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_consumerunitbarcode_type.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_consumerunitbarcode_type_task = DummyOperator(task_id="begin_insert_raw_customlist_consumerunitbarcode_type")
        skip_insert_raw_customlist_consumerunitbarcode_type_task = DummyOperator(task_id="skip_insert_raw_customlist_consumerunitbarcode_type")
        end_insert_raw_customlist_consumerunitbarcode_type_task = DummyOperator(task_id="end_insert_raw_customlist_consumerunitbarcode_type")
        end_raw_customlist_consumerunitbarcode_type_task = DummyOperator(
            task_id="end_raw_customlist_consumerunitbarcode_type",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_consumerunitbarcode_type_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_consumerunitbarcode_type',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_consumerunitbarcode_type.yaml",
                       "skip_task_id": "load_raw_customlist_consumerunitbarcode_type.skip_insert_raw_customlist_consumerunitbarcode_type",
                       "next_task_id": "load_raw_customlist_consumerunitbarcode_type.begin_insert_raw_customlist_consumerunitbarcode_type"
            },
        )

        s3_to_snowflake_raw_customlist_consumerunitbarcode_type_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_consumerunitbarcode_type",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_consumerunitbarcode_type.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_consumerunitbarcode_type_task = PythonOperator(
            task_id="insert_log_raw_customlist_consumerunitbarcode_type",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_consumerunitbarcode_type.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_consumerunitbarcode_type_table = PythonOperator(
            task_id="insert_customlist_consumerunitbarcode_type",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_consumerunitbarcode_type.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_consumerunitbarcode_type_task >>
            check_new_files_found_raw_customlist_consumerunitbarcode_type_task >>
            [begin_insert_raw_customlist_consumerunitbarcode_type_task, skip_insert_raw_customlist_consumerunitbarcode_type_task]
        )

        (
                begin_insert_raw_customlist_consumerunitbarcode_type_task >>
                s3_to_snowflake_raw_customlist_consumerunitbarcode_type_task >>
                insert_log_raw_customlist_consumerunitbarcode_type_task >>
                append_to_customlist_consumerunitbarcode_type_table >>
                end_insert_raw_customlist_consumerunitbarcode_type_task >>
                end_raw_customlist_consumerunitbarcode_type_task
        )

        skip_insert_raw_customlist_consumerunitbarcode_type_task >> end_raw_customlist_consumerunitbarcode_type_task

    #  --- Load raw_customlist_jb_jj_84aj_pdstatus ---

    with TaskGroup(group_id='load_raw_customlist_jb_jj_84aj_pdstatus') as tg_raw_customlist_jb_jj_84aj_pdstatus:
        list_s3_files_raw_customlist_jb_jj_84aj_pdstatus_task = PythonOperator(
            task_id="list_s3_customlist_jb_jj_84aj_pdstatus",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_jb_jj_84aj_pdstatus.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_jb_jj_84aj_pdstatus_task = DummyOperator(task_id="begin_insert_raw_customlist_jb_jj_84aj_pdstatus")
        skip_insert_raw_customlist_jb_jj_84aj_pdstatus_task = DummyOperator(task_id="skip_insert_raw_customlist_jb_jj_84aj_pdstatus")
        end_insert_raw_customlist_jb_jj_84aj_pdstatus_task = DummyOperator(task_id="end_insert_raw_customlist_jb_jj_84aj_pdstatus")
        end_raw_customlist_jb_jj_84aj_pdstatus_task = DummyOperator(
            task_id="end_raw_customlist_jb_jj_84aj_pdstatus",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_jb_jj_84aj_pdstatus_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_jb_jj_84aj_pdstatus',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_jb_jj_84aj_pdstatus.yaml",
                       "skip_task_id": "load_raw_customlist_jb_jj_84aj_pdstatus.skip_insert_raw_customlist_jb_jj_84aj_pdstatus",
                       "next_task_id": "load_raw_customlist_jb_jj_84aj_pdstatus.begin_insert_raw_customlist_jb_jj_84aj_pdstatus"
            },
        )

        s3_to_snowflake_raw_customlist_jb_jj_84aj_pdstatus_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_jb_jj_84aj_pdstatus",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_jb_jj_84aj_pdstatus.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_jb_jj_84aj_pdstatus_task = PythonOperator(
            task_id="insert_log_raw_customlist_jb_jj_84aj_pdstatus",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_jb_jj_84aj_pdstatus.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_jb_jj_84aj_pdstatus_table = PythonOperator(
            task_id="insert_customlist_jb_jj_84aj_pdstatus",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_jb_jj_84aj_pdstatus.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_jb_jj_84aj_pdstatus_task >>
            check_new_files_found_raw_customlist_jb_jj_84aj_pdstatus_task >>
            [begin_insert_raw_customlist_jb_jj_84aj_pdstatus_task, skip_insert_raw_customlist_jb_jj_84aj_pdstatus_task]
        )

        (
                begin_insert_raw_customlist_jb_jj_84aj_pdstatus_task >>
                s3_to_snowflake_raw_customlist_jb_jj_84aj_pdstatus_task >>
                insert_log_raw_customlist_jb_jj_84aj_pdstatus_task >>
                append_to_customlist_jb_jj_84aj_pdstatus_table >>
                end_insert_raw_customlist_jb_jj_84aj_pdstatus_task >>
                end_raw_customlist_jb_jj_84aj_pdstatus_task
        )

        skip_insert_raw_customlist_jb_jj_84aj_pdstatus_task >> end_raw_customlist_jb_jj_84aj_pdstatus_task

    #  --- Load raw_customlist_jj_84_aj_pol ---

    with TaskGroup(group_id='load_raw_customlist_jj_84_aj_pol') as tg_raw_customlist_jj_84_aj_pol:
        list_s3_files_raw_customlist_jj_84_aj_pol_task = PythonOperator(
            task_id="list_s3_customlist_jj_84_aj_pol",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_jj_84_aj_pol.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_jj_84_aj_pol_task = DummyOperator(task_id="begin_insert_raw_customlist_jj_84_aj_pol")
        skip_insert_raw_customlist_jj_84_aj_pol_task = DummyOperator(task_id="skip_insert_raw_customlist_jj_84_aj_pol")
        end_insert_raw_customlist_jj_84_aj_pol_task = DummyOperator(task_id="end_insert_raw_customlist_jj_84_aj_pol")
        end_raw_customlist_jj_84_aj_pol_task = DummyOperator(
            task_id="end_raw_customlist_jj_84_aj_pol",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_jj_84_aj_pol_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_jj_84_aj_pol',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_jj_84_aj_pol.yaml",
                       "skip_task_id": "load_raw_customlist_jj_84_aj_pol.skip_insert_raw_customlist_jj_84_aj_pol",
                       "next_task_id": "load_raw_customlist_jj_84_aj_pol.begin_insert_raw_customlist_jj_84_aj_pol"
            },
        )

        s3_to_snowflake_raw_customlist_jj_84_aj_pol_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_jj_84_aj_pol",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_jj_84_aj_pol.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_jj_84_aj_pol_task = PythonOperator(
            task_id="insert_log_raw_customlist_jj_84_aj_pol",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_jj_84_aj_pol.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_jj_84_aj_pol_table = PythonOperator(
            task_id="insert_customlist_jj_84_aj_pol",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_jj_84_aj_pol.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_jj_84_aj_pol_task >>
            check_new_files_found_raw_customlist_jj_84_aj_pol_task >>
            [begin_insert_raw_customlist_jj_84_aj_pol_task, skip_insert_raw_customlist_jj_84_aj_pol_task]
        )

        (
                begin_insert_raw_customlist_jj_84_aj_pol_task >>
                s3_to_snowflake_raw_customlist_jj_84_aj_pol_task >>
                insert_log_raw_customlist_jj_84_aj_pol_task >>
                append_to_customlist_jj_84_aj_pol_table >>
                end_insert_raw_customlist_jj_84_aj_pol_task >>
                end_raw_customlist_jj_84_aj_pol_task
        )

        skip_insert_raw_customlist_jj_84_aj_pol_task >> end_raw_customlist_jj_84_aj_pol_task

    #  --- Load raw_customlist_jj_asin_relationship ---

    with TaskGroup(group_id='load_raw_customlist_jj_asin_relationship') as tg_raw_customlist_jj_asin_relationship:
        list_s3_files_raw_customlist_jj_asin_relationship_task = PythonOperator(
            task_id="list_s3_customlist_jj_asin_relationship",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_jj_asin_relationship.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_jj_asin_relationship_task = DummyOperator(task_id="begin_insert_raw_customlist_jj_asin_relationship")
        skip_insert_raw_customlist_jj_asin_relationship_task = DummyOperator(task_id="skip_insert_raw_customlist_jj_asin_relationship")
        end_insert_raw_customlist_jj_asin_relationship_task = DummyOperator(task_id="end_insert_raw_customlist_jj_asin_relationship")
        end_raw_customlist_jj_asin_relationship_task = DummyOperator(
            task_id="end_raw_customlist_jj_asin_relationship",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_jj_asin_relationship_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_jj_asin_relationship',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_jj_asin_relationship.yaml",
                       "skip_task_id": "load_raw_customlist_jj_asin_relationship.skip_insert_raw_customlist_jj_asin_relationship",
                       "next_task_id": "load_raw_customlist_jj_asin_relationship.begin_insert_raw_customlist_jj_asin_relationship"
            },
        )

        s3_to_snowflake_raw_customlist_jj_asin_relationship_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_jj_asin_relationship",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_jj_asin_relationship.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_jj_asin_relationship_task = PythonOperator(
            task_id="insert_log_raw_customlist_jj_asin_relationship",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_jj_asin_relationship.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_jj_asin_relationship_table = PythonOperator(
            task_id="insert_customlist_jj_asin_relationship",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_jj_asin_relationship.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_jj_asin_relationship_task >>
            check_new_files_found_raw_customlist_jj_asin_relationship_task >>
            [begin_insert_raw_customlist_jj_asin_relationship_task, skip_insert_raw_customlist_jj_asin_relationship_task]
        )

        (
                begin_insert_raw_customlist_jj_asin_relationship_task >>
                s3_to_snowflake_raw_customlist_jj_asin_relationship_task >>
                insert_log_raw_customlist_jj_asin_relationship_task >>
                append_to_customlist_jj_asin_relationship_table >>
                end_insert_raw_customlist_jj_asin_relationship_task >>
                end_raw_customlist_jj_asin_relationship_task
        )

        skip_insert_raw_customlist_jj_asin_relationship_task >> end_raw_customlist_jj_asin_relationship_task

    #  --- Load raw_customlist_jj_brandcategorylist ---

    with TaskGroup(group_id='load_raw_customlist_jj_brandcategorylist') as tg_raw_customlist_jj_brandcategorylist:
        list_s3_files_raw_customlist_jj_brandcategorylist_task = PythonOperator(
            task_id="list_s3_customlist_jj_brandcategorylist",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_jj_brandcategorylist.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_jj_brandcategorylist_task = DummyOperator(task_id="begin_insert_raw_customlist_jj_brandcategorylist")
        skip_insert_raw_customlist_jj_brandcategorylist_task = DummyOperator(task_id="skip_insert_raw_customlist_jj_brandcategorylist")
        end_insert_raw_customlist_jj_brandcategorylist_task = DummyOperator(task_id="end_insert_raw_customlist_jj_brandcategorylist")
        end_raw_customlist_jj_brandcategorylist_task = DummyOperator(
            task_id="end_raw_customlist_jj_brandcategorylist",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_jj_brandcategorylist_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_jj_brandcategorylist',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_jj_brandcategorylist.yaml",
                       "skip_task_id": "load_raw_customlist_jj_brandcategorylist.skip_insert_raw_customlist_jj_brandcategorylist",
                       "next_task_id": "load_raw_customlist_jj_brandcategorylist.begin_insert_raw_customlist_jj_brandcategorylist"
            },
        )

        s3_to_snowflake_raw_customlist_jj_brandcategorylist_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_jj_brandcategorylist",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_jj_brandcategorylist.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_jj_brandcategorylist_task = PythonOperator(
            task_id="insert_log_raw_customlist_jj_brandcategorylist",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_jj_brandcategorylist.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_jj_brandcategorylist_table = PythonOperator(
            task_id="insert_customlist_jj_brandcategorylist",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_jj_brandcategorylist.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_jj_brandcategorylist_task >>
            check_new_files_found_raw_customlist_jj_brandcategorylist_task >>
            [begin_insert_raw_customlist_jj_brandcategorylist_task, skip_insert_raw_customlist_jj_brandcategorylist_task]
        )

        (
                begin_insert_raw_customlist_jj_brandcategorylist_task >>
                s3_to_snowflake_raw_customlist_jj_brandcategorylist_task >>
                insert_log_raw_customlist_jj_brandcategorylist_task >>
                append_to_customlist_jj_brandcategorylist_table >>
                end_insert_raw_customlist_jj_brandcategorylist_task >>
                end_raw_customlist_jj_brandcategorylist_task
        )

        skip_insert_raw_customlist_jj_brandcategorylist_task >> end_raw_customlist_jj_brandcategorylist_task

    #  --- Load raw_customlist_jj_brandclusterlist ---

    with TaskGroup(group_id='load_raw_customlist_jj_brandclusterlist') as tg_raw_customlist_jj_brandclusterlist:
        list_s3_files_raw_customlist_jj_brandclusterlist_task = PythonOperator(
            task_id="list_s3_customlist_jj_brandclusterlist",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_jj_brandclusterlist.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_jj_brandclusterlist_task = DummyOperator(task_id="begin_insert_raw_customlist_jj_brandclusterlist")
        skip_insert_raw_customlist_jj_brandclusterlist_task = DummyOperator(task_id="skip_insert_raw_customlist_jj_brandclusterlist")
        end_insert_raw_customlist_jj_brandclusterlist_task = DummyOperator(task_id="end_insert_raw_customlist_jj_brandclusterlist")
        end_raw_customlist_jj_brandclusterlist_task = DummyOperator(
            task_id="end_raw_customlist_jj_brandclusterlist",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_jj_brandclusterlist_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_jj_brandclusterlist',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_jj_brandclusterlist.yaml",
                       "skip_task_id": "load_raw_customlist_jj_brandclusterlist.skip_insert_raw_customlist_jj_brandclusterlist",
                       "next_task_id": "load_raw_customlist_jj_brandclusterlist.begin_insert_raw_customlist_jj_brandclusterlist"
            },
        )

        s3_to_snowflake_raw_customlist_jj_brandclusterlist_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_jj_brandclusterlist",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_jj_brandclusterlist.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_jj_brandclusterlist_task = PythonOperator(
            task_id="insert_log_raw_customlist_jj_brandclusterlist",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_jj_brandclusterlist.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_jj_brandclusterlist_table = PythonOperator(
            task_id="insert_customlist_jj_brandclusterlist",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_jj_brandclusterlist.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_jj_brandclusterlist_task >>
            check_new_files_found_raw_customlist_jj_brandclusterlist_task >>
            [begin_insert_raw_customlist_jj_brandclusterlist_task, skip_insert_raw_customlist_jj_brandclusterlist_task]
        )

        (
                begin_insert_raw_customlist_jj_brandclusterlist_task >>
                s3_to_snowflake_raw_customlist_jj_brandclusterlist_task >>
                insert_log_raw_customlist_jj_brandclusterlist_task >>
                append_to_customlist_jj_brandclusterlist_table >>
                end_insert_raw_customlist_jj_brandclusterlist_task >>
                end_raw_customlist_jj_brandclusterlist_task
        )

        skip_insert_raw_customlist_jj_brandclusterlist_task >> end_raw_customlist_jj_brandclusterlist_task

    #  --- Load raw_customlist_jj_brandgroupinglist ---

    with TaskGroup(group_id='load_raw_customlist_jj_brandgroupinglist') as tg_raw_customlist_jj_brandgroupinglist:
        list_s3_files_raw_customlist_jj_brandgroupinglist_task = PythonOperator(
            task_id="list_s3_customlist_jj_brandgroupinglist",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_jj_brandgroupinglist.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_jj_brandgroupinglist_task = DummyOperator(task_id="begin_insert_raw_customlist_jj_brandgroupinglist")
        skip_insert_raw_customlist_jj_brandgroupinglist_task = DummyOperator(task_id="skip_insert_raw_customlist_jj_brandgroupinglist")
        end_insert_raw_customlist_jj_brandgroupinglist_task = DummyOperator(task_id="end_insert_raw_customlist_jj_brandgroupinglist")
        end_raw_customlist_jj_brandgroupinglist_task = DummyOperator(
            task_id="end_raw_customlist_jj_brandgroupinglist",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_jj_brandgroupinglist_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_jj_brandgroupinglist',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_jj_brandgroupinglist.yaml",
                       "skip_task_id": "load_raw_customlist_jj_brandgroupinglist.skip_insert_raw_customlist_jj_brandgroupinglist",
                       "next_task_id": "load_raw_customlist_jj_brandgroupinglist.begin_insert_raw_customlist_jj_brandgroupinglist"
            },
        )

        s3_to_snowflake_raw_customlist_jj_brandgroupinglist_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_jj_brandgroupinglist",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_jj_brandgroupinglist.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_jj_brandgroupinglist_task = PythonOperator(
            task_id="insert_log_raw_customlist_jj_brandgroupinglist",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_jj_brandgroupinglist.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_jj_brandgroupinglist_table = PythonOperator(
            task_id="insert_customlist_jj_brandgroupinglist",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_jj_brandgroupinglist.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_jj_brandgroupinglist_task >>
            check_new_files_found_raw_customlist_jj_brandgroupinglist_task >>
            [begin_insert_raw_customlist_jj_brandgroupinglist_task, skip_insert_raw_customlist_jj_brandgroupinglist_task]
        )

        (
                begin_insert_raw_customlist_jj_brandgroupinglist_task >>
                s3_to_snowflake_raw_customlist_jj_brandgroupinglist_task >>
                insert_log_raw_customlist_jj_brandgroupinglist_task >>
                append_to_customlist_jj_brandgroupinglist_table >>
                end_insert_raw_customlist_jj_brandgroupinglist_task >>
                end_raw_customlist_jj_brandgroupinglist_task
        )

        skip_insert_raw_customlist_jj_brandgroupinglist_task >> end_raw_customlist_jj_brandgroupinglist_task

    #  --- Load raw_customlist_jj_brandstatuslist ---

    with TaskGroup(group_id='load_raw_customlist_jj_brandstatuslist') as tg_raw_customlist_jj_brandstatuslist:
        list_s3_files_raw_customlist_jj_brandstatuslist_task = PythonOperator(
            task_id="list_s3_customlist_jj_brandstatuslist",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_jj_brandstatuslist.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_jj_brandstatuslist_task = DummyOperator(task_id="begin_insert_raw_customlist_jj_brandstatuslist")
        skip_insert_raw_customlist_jj_brandstatuslist_task = DummyOperator(task_id="skip_insert_raw_customlist_jj_brandstatuslist")
        end_insert_raw_customlist_jj_brandstatuslist_task = DummyOperator(task_id="end_insert_raw_customlist_jj_brandstatuslist")
        end_raw_customlist_jj_brandstatuslist_task = DummyOperator(
            task_id="end_raw_customlist_jj_brandstatuslist",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_jj_brandstatuslist_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_jj_brandstatuslist',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_jj_brandstatuslist.yaml",
                       "skip_task_id": "load_raw_customlist_jj_brandstatuslist.skip_insert_raw_customlist_jj_brandstatuslist",
                       "next_task_id": "load_raw_customlist_jj_brandstatuslist.begin_insert_raw_customlist_jj_brandstatuslist"
            },
        )

        s3_to_snowflake_raw_customlist_jj_brandstatuslist_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_jj_brandstatuslist",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_jj_brandstatuslist.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_jj_brandstatuslist_task = PythonOperator(
            task_id="insert_log_raw_customlist_jj_brandstatuslist",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_jj_brandstatuslist.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_jj_brandstatuslist_table = PythonOperator(
            task_id="insert_customlist_jj_brandstatuslist",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_jj_brandstatuslist.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_jj_brandstatuslist_task >>
            check_new_files_found_raw_customlist_jj_brandstatuslist_task >>
            [begin_insert_raw_customlist_jj_brandstatuslist_task, skip_insert_raw_customlist_jj_brandstatuslist_task]
        )

        (
                begin_insert_raw_customlist_jj_brandstatuslist_task >>
                s3_to_snowflake_raw_customlist_jj_brandstatuslist_task >>
                insert_log_raw_customlist_jj_brandstatuslist_task >>
                append_to_customlist_jj_brandstatuslist_table >>
                end_insert_raw_customlist_jj_brandstatuslist_task >>
                end_raw_customlist_jj_brandstatuslist_task
        )

        skip_insert_raw_customlist_jj_brandstatuslist_task >> end_raw_customlist_jj_brandstatuslist_task

    #  --- Load raw_customlist_jj_fba_storage_category_ ---

    with TaskGroup(group_id='load_raw_customlist_jj_fba_storage_category_') as tg_raw_customlist_jj_fba_storage_category_:
        list_s3_files_raw_customlist_jj_fba_storage_category__task = PythonOperator(
            task_id="list_s3_customlist_jj_fba_storage_category_",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_jj_fba_storage_category_.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_jj_fba_storage_category__task = DummyOperator(task_id="begin_insert_raw_customlist_jj_fba_storage_category_")
        skip_insert_raw_customlist_jj_fba_storage_category__task = DummyOperator(task_id="skip_insert_raw_customlist_jj_fba_storage_category_")
        end_insert_raw_customlist_jj_fba_storage_category__task = DummyOperator(task_id="end_insert_raw_customlist_jj_fba_storage_category_")
        end_raw_customlist_jj_fba_storage_category__task = DummyOperator(
            task_id="end_raw_customlist_jj_fba_storage_category_",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_jj_fba_storage_category__task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_jj_fba_storage_category_',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_jj_fba_storage_category_.yaml",
                       "skip_task_id": "load_raw_customlist_jj_fba_storage_category_.skip_insert_raw_customlist_jj_fba_storage_category_",
                       "next_task_id": "load_raw_customlist_jj_fba_storage_category_.begin_insert_raw_customlist_jj_fba_storage_category_"
            },
        )

        s3_to_snowflake_raw_customlist_jj_fba_storage_category__task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_jj_fba_storage_category_",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_jj_fba_storage_category_.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_jj_fba_storage_category__task = PythonOperator(
            task_id="insert_log_raw_customlist_jj_fba_storage_category_",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_jj_fba_storage_category_.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_jj_fba_storage_category__table = PythonOperator(
            task_id="insert_customlist_jj_fba_storage_category_",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_jj_fba_storage_category_.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_jj_fba_storage_category__task >>
            check_new_files_found_raw_customlist_jj_fba_storage_category__task >>
            [begin_insert_raw_customlist_jj_fba_storage_category__task, skip_insert_raw_customlist_jj_fba_storage_category__task]
        )

        (
                begin_insert_raw_customlist_jj_fba_storage_category__task >>
                s3_to_snowflake_raw_customlist_jj_fba_storage_category__task >>
                insert_log_raw_customlist_jj_fba_storage_category__task >>
                append_to_customlist_jj_fba_storage_category__table >>
                end_insert_raw_customlist_jj_fba_storage_category__task >>
                end_raw_customlist_jj_fba_storage_category__task
        )

        skip_insert_raw_customlist_jj_fba_storage_category__task >> end_raw_customlist_jj_fba_storage_category__task

    #  --- Load raw_customlist_jj_inventory_status ---

    with TaskGroup(group_id='load_raw_customlist_jj_inventory_status') as tg_raw_customlist_jj_inventory_status:
        list_s3_files_raw_customlist_jj_inventory_status_task = PythonOperator(
            task_id="list_s3_customlist_jj_inventory_status",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_jj_inventory_status.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_jj_inventory_status_task = DummyOperator(task_id="begin_insert_raw_customlist_jj_inventory_status")
        skip_insert_raw_customlist_jj_inventory_status_task = DummyOperator(task_id="skip_insert_raw_customlist_jj_inventory_status")
        end_insert_raw_customlist_jj_inventory_status_task = DummyOperator(task_id="end_insert_raw_customlist_jj_inventory_status")
        end_raw_customlist_jj_inventory_status_task = DummyOperator(
            task_id="end_raw_customlist_jj_inventory_status",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_jj_inventory_status_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_jj_inventory_status',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_jj_inventory_status.yaml",
                       "skip_task_id": "load_raw_customlist_jj_inventory_status.skip_insert_raw_customlist_jj_inventory_status",
                       "next_task_id": "load_raw_customlist_jj_inventory_status.begin_insert_raw_customlist_jj_inventory_status"
            },
        )

        s3_to_snowflake_raw_customlist_jj_inventory_status_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_jj_inventory_status",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_jj_inventory_status.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_jj_inventory_status_task = PythonOperator(
            task_id="insert_log_raw_customlist_jj_inventory_status",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_jj_inventory_status.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_jj_inventory_status_table = PythonOperator(
            task_id="insert_customlist_jj_inventory_status",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_jj_inventory_status.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_jj_inventory_status_task >>
            check_new_files_found_raw_customlist_jj_inventory_status_task >>
            [begin_insert_raw_customlist_jj_inventory_status_task, skip_insert_raw_customlist_jj_inventory_status_task]
        )

        (
                begin_insert_raw_customlist_jj_inventory_status_task >>
                s3_to_snowflake_raw_customlist_jj_inventory_status_task >>
                insert_log_raw_customlist_jj_inventory_status_task >>
                append_to_customlist_jj_inventory_status_table >>
                end_insert_raw_customlist_jj_inventory_status_task >>
                end_raw_customlist_jj_inventory_status_task
        )

        skip_insert_raw_customlist_jj_inventory_status_task >> end_raw_customlist_jj_inventory_status_task

    #  --- Load raw_customlist_jj_sc_outbound_type ---

    with TaskGroup(group_id='load_raw_customlist_jj_sc_outbound_type') as tg_raw_customlist_jj_sc_outbound_type:
        list_s3_files_raw_customlist_jj_sc_outbound_type_task = PythonOperator(
            task_id="list_s3_customlist_jj_sc_outbound_type",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_jj_sc_outbound_type.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_jj_sc_outbound_type_task = DummyOperator(task_id="begin_insert_raw_customlist_jj_sc_outbound_type")
        skip_insert_raw_customlist_jj_sc_outbound_type_task = DummyOperator(task_id="skip_insert_raw_customlist_jj_sc_outbound_type")
        end_insert_raw_customlist_jj_sc_outbound_type_task = DummyOperator(task_id="end_insert_raw_customlist_jj_sc_outbound_type")
        end_raw_customlist_jj_sc_outbound_type_task = DummyOperator(
            task_id="end_raw_customlist_jj_sc_outbound_type",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_jj_sc_outbound_type_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_jj_sc_outbound_type',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_jj_sc_outbound_type.yaml",
                       "skip_task_id": "load_raw_customlist_jj_sc_outbound_type.skip_insert_raw_customlist_jj_sc_outbound_type",
                       "next_task_id": "load_raw_customlist_jj_sc_outbound_type.begin_insert_raw_customlist_jj_sc_outbound_type"
            },
        )

        s3_to_snowflake_raw_customlist_jj_sc_outbound_type_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_jj_sc_outbound_type",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_jj_sc_outbound_type.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_jj_sc_outbound_type_task = PythonOperator(
            task_id="insert_log_raw_customlist_jj_sc_outbound_type",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_jj_sc_outbound_type.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_jj_sc_outbound_type_table = PythonOperator(
            task_id="insert_customlist_jj_sc_outbound_type",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_jj_sc_outbound_type.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_jj_sc_outbound_type_task >>
            check_new_files_found_raw_customlist_jj_sc_outbound_type_task >>
            [begin_insert_raw_customlist_jj_sc_outbound_type_task, skip_insert_raw_customlist_jj_sc_outbound_type_task]
        )

        (
                begin_insert_raw_customlist_jj_sc_outbound_type_task >>
                s3_to_snowflake_raw_customlist_jj_sc_outbound_type_task >>
                insert_log_raw_customlist_jj_sc_outbound_type_task >>
                append_to_customlist_jj_sc_outbound_type_table >>
                end_insert_raw_customlist_jj_sc_outbound_type_task >>
                end_raw_customlist_jj_sc_outbound_type_task
        )

        skip_insert_raw_customlist_jj_sc_outbound_type_task >> end_raw_customlist_jj_sc_outbound_type_task

    #  --- Load raw_customlist_jj_sc_transportation_mode ---

    with TaskGroup(group_id='load_raw_customlist_jj_sc_transportation_mode') as tg_raw_customlist_jj_sc_transportation_mode:
        list_s3_files_raw_customlist_jj_sc_transportation_mode_task = PythonOperator(
            task_id="list_s3_customlist_jj_sc_transportation_mode",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_jj_sc_transportation_mode.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_jj_sc_transportation_mode_task = DummyOperator(task_id="begin_insert_raw_customlist_jj_sc_transportation_mode")
        skip_insert_raw_customlist_jj_sc_transportation_mode_task = DummyOperator(task_id="skip_insert_raw_customlist_jj_sc_transportation_mode")
        end_insert_raw_customlist_jj_sc_transportation_mode_task = DummyOperator(task_id="end_insert_raw_customlist_jj_sc_transportation_mode")
        end_raw_customlist_jj_sc_transportation_mode_task = DummyOperator(
            task_id="end_raw_customlist_jj_sc_transportation_mode",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_jj_sc_transportation_mode_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_jj_sc_transportation_mode',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_jj_sc_transportation_mode.yaml",
                       "skip_task_id": "load_raw_customlist_jj_sc_transportation_mode.skip_insert_raw_customlist_jj_sc_transportation_mode",
                       "next_task_id": "load_raw_customlist_jj_sc_transportation_mode.begin_insert_raw_customlist_jj_sc_transportation_mode"
            },
        )

        s3_to_snowflake_raw_customlist_jj_sc_transportation_mode_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_jj_sc_transportation_mode",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_jj_sc_transportation_mode.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_jj_sc_transportation_mode_task = PythonOperator(
            task_id="insert_log_raw_customlist_jj_sc_transportation_mode",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_jj_sc_transportation_mode.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_jj_sc_transportation_mode_table = PythonOperator(
            task_id="insert_customlist_jj_sc_transportation_mode",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_jj_sc_transportation_mode.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_jj_sc_transportation_mode_task >>
            check_new_files_found_raw_customlist_jj_sc_transportation_mode_task >>
            [begin_insert_raw_customlist_jj_sc_transportation_mode_task, skip_insert_raw_customlist_jj_sc_transportation_mode_task]
        )

        (
                begin_insert_raw_customlist_jj_sc_transportation_mode_task >>
                s3_to_snowflake_raw_customlist_jj_sc_transportation_mode_task >>
                insert_log_raw_customlist_jj_sc_transportation_mode_task >>
                append_to_customlist_jj_sc_transportation_mode_table >>
                end_insert_raw_customlist_jj_sc_transportation_mode_task >>
                end_raw_customlist_jj_sc_transportation_mode_task
        )

        skip_insert_raw_customlist_jj_sc_transportation_mode_task >> end_raw_customlist_jj_sc_transportation_mode_task

    #  --- Load raw_customlist_jj_sc_work_order_type ---

    with TaskGroup(group_id='load_raw_customlist_jj_sc_work_order_type') as tg_raw_customlist_jj_sc_work_order_type:
        list_s3_files_raw_customlist_jj_sc_work_order_type_task = PythonOperator(
            task_id="list_s3_customlist_jj_sc_work_order_type",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_jj_sc_work_order_type.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_jj_sc_work_order_type_task = DummyOperator(task_id="begin_insert_raw_customlist_jj_sc_work_order_type")
        skip_insert_raw_customlist_jj_sc_work_order_type_task = DummyOperator(task_id="skip_insert_raw_customlist_jj_sc_work_order_type")
        end_insert_raw_customlist_jj_sc_work_order_type_task = DummyOperator(task_id="end_insert_raw_customlist_jj_sc_work_order_type")
        end_raw_customlist_jj_sc_work_order_type_task = DummyOperator(
            task_id="end_raw_customlist_jj_sc_work_order_type",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_jj_sc_work_order_type_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_jj_sc_work_order_type',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_jj_sc_work_order_type.yaml",
                       "skip_task_id": "load_raw_customlist_jj_sc_work_order_type.skip_insert_raw_customlist_jj_sc_work_order_type",
                       "next_task_id": "load_raw_customlist_jj_sc_work_order_type.begin_insert_raw_customlist_jj_sc_work_order_type"
            },
        )

        s3_to_snowflake_raw_customlist_jj_sc_work_order_type_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_jj_sc_work_order_type",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_jj_sc_work_order_type.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_jj_sc_work_order_type_task = PythonOperator(
            task_id="insert_log_raw_customlist_jj_sc_work_order_type",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_jj_sc_work_order_type.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_jj_sc_work_order_type_table = PythonOperator(
            task_id="insert_customlist_jj_sc_work_order_type",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_jj_sc_work_order_type.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_jj_sc_work_order_type_task >>
            check_new_files_found_raw_customlist_jj_sc_work_order_type_task >>
            [begin_insert_raw_customlist_jj_sc_work_order_type_task, skip_insert_raw_customlist_jj_sc_work_order_type_task]
        )

        (
                begin_insert_raw_customlist_jj_sc_work_order_type_task >>
                s3_to_snowflake_raw_customlist_jj_sc_work_order_type_task >>
                insert_log_raw_customlist_jj_sc_work_order_type_task >>
                append_to_customlist_jj_sc_work_order_type_table >>
                end_insert_raw_customlist_jj_sc_work_order_type_task >>
                end_raw_customlist_jj_sc_work_order_type_task
        )

        skip_insert_raw_customlist_jj_sc_work_order_type_task >> end_raw_customlist_jj_sc_work_order_type_task

    #  --- Load raw_customlist_nbsabr_integritystatus ---

    with TaskGroup(group_id='load_raw_customlist_nbsabr_integritystatus') as tg_raw_customlist_nbsabr_integritystatus:
        list_s3_files_raw_customlist_nbsabr_integritystatus_task = PythonOperator(
            task_id="list_s3_customlist_nbsabr_integritystatus",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_nbsabr_integritystatus.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_nbsabr_integritystatus_task = DummyOperator(task_id="begin_insert_raw_customlist_nbsabr_integritystatus")
        skip_insert_raw_customlist_nbsabr_integritystatus_task = DummyOperator(task_id="skip_insert_raw_customlist_nbsabr_integritystatus")
        end_insert_raw_customlist_nbsabr_integritystatus_task = DummyOperator(task_id="end_insert_raw_customlist_nbsabr_integritystatus")
        end_raw_customlist_nbsabr_integritystatus_task = DummyOperator(
            task_id="end_raw_customlist_nbsabr_integritystatus",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_nbsabr_integritystatus_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_nbsabr_integritystatus',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_nbsabr_integritystatus.yaml",
                       "skip_task_id": "load_raw_customlist_nbsabr_integritystatus.skip_insert_raw_customlist_nbsabr_integritystatus",
                       "next_task_id": "load_raw_customlist_nbsabr_integritystatus.begin_insert_raw_customlist_nbsabr_integritystatus"
            },
        )

        s3_to_snowflake_raw_customlist_nbsabr_integritystatus_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_nbsabr_integritystatus",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_nbsabr_integritystatus.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_nbsabr_integritystatus_task = PythonOperator(
            task_id="insert_log_raw_customlist_nbsabr_integritystatus",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_nbsabr_integritystatus.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_nbsabr_integritystatus_table = PythonOperator(
            task_id="insert_customlist_nbsabr_integritystatus",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_nbsabr_integritystatus.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_nbsabr_integritystatus_task >>
            check_new_files_found_raw_customlist_nbsabr_integritystatus_task >>
            [begin_insert_raw_customlist_nbsabr_integritystatus_task, skip_insert_raw_customlist_nbsabr_integritystatus_task]
        )

        (
                begin_insert_raw_customlist_nbsabr_integritystatus_task >>
                s3_to_snowflake_raw_customlist_nbsabr_integritystatus_task >>
                insert_log_raw_customlist_nbsabr_integritystatus_task >>
                append_to_customlist_nbsabr_integritystatus_table >>
                end_insert_raw_customlist_nbsabr_integritystatus_task >>
                end_raw_customlist_nbsabr_integritystatus_task
        )

        skip_insert_raw_customlist_nbsabr_integritystatus_task >> end_raw_customlist_nbsabr_integritystatus_task

    #  --- Load raw_customlist_nbsabr_recordtype ---

    with TaskGroup(group_id='load_raw_customlist_nbsabr_recordtype') as tg_raw_customlist_nbsabr_recordtype:
        list_s3_files_raw_customlist_nbsabr_recordtype_task = PythonOperator(
            task_id="list_s3_customlist_nbsabr_recordtype",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_nbsabr_recordtype.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_nbsabr_recordtype_task = DummyOperator(task_id="begin_insert_raw_customlist_nbsabr_recordtype")
        skip_insert_raw_customlist_nbsabr_recordtype_task = DummyOperator(task_id="skip_insert_raw_customlist_nbsabr_recordtype")
        end_insert_raw_customlist_nbsabr_recordtype_task = DummyOperator(task_id="end_insert_raw_customlist_nbsabr_recordtype")
        end_raw_customlist_nbsabr_recordtype_task = DummyOperator(
            task_id="end_raw_customlist_nbsabr_recordtype",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_nbsabr_recordtype_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_nbsabr_recordtype',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_nbsabr_recordtype.yaml",
                       "skip_task_id": "load_raw_customlist_nbsabr_recordtype.skip_insert_raw_customlist_nbsabr_recordtype",
                       "next_task_id": "load_raw_customlist_nbsabr_recordtype.begin_insert_raw_customlist_nbsabr_recordtype"
            },
        )

        s3_to_snowflake_raw_customlist_nbsabr_recordtype_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_nbsabr_recordtype",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_nbsabr_recordtype.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_nbsabr_recordtype_task = PythonOperator(
            task_id="insert_log_raw_customlist_nbsabr_recordtype",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_nbsabr_recordtype.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_nbsabr_recordtype_table = PythonOperator(
            task_id="insert_customlist_nbsabr_recordtype",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_nbsabr_recordtype.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_nbsabr_recordtype_task >>
            check_new_files_found_raw_customlist_nbsabr_recordtype_task >>
            [begin_insert_raw_customlist_nbsabr_recordtype_task, skip_insert_raw_customlist_nbsabr_recordtype_task]
        )

        (
                begin_insert_raw_customlist_nbsabr_recordtype_task >>
                s3_to_snowflake_raw_customlist_nbsabr_recordtype_task >>
                insert_log_raw_customlist_nbsabr_recordtype_task >>
                append_to_customlist_nbsabr_recordtype_table >>
                end_insert_raw_customlist_nbsabr_recordtype_task >>
                end_raw_customlist_nbsabr_recordtype_task
        )

        skip_insert_raw_customlist_nbsabr_recordtype_task >> end_raw_customlist_nbsabr_recordtype_task

    #  --- Load raw_customlist_nbsabr_status ---

    with TaskGroup(group_id='load_raw_customlist_nbsabr_status') as tg_raw_customlist_nbsabr_status:
        list_s3_files_raw_customlist_nbsabr_status_task = PythonOperator(
            task_id="list_s3_customlist_nbsabr_status",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_nbsabr_status.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_nbsabr_status_task = DummyOperator(task_id="begin_insert_raw_customlist_nbsabr_status")
        skip_insert_raw_customlist_nbsabr_status_task = DummyOperator(task_id="skip_insert_raw_customlist_nbsabr_status")
        end_insert_raw_customlist_nbsabr_status_task = DummyOperator(task_id="end_insert_raw_customlist_nbsabr_status")
        end_raw_customlist_nbsabr_status_task = DummyOperator(
            task_id="end_raw_customlist_nbsabr_status",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_nbsabr_status_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_nbsabr_status',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_nbsabr_status.yaml",
                       "skip_task_id": "load_raw_customlist_nbsabr_status.skip_insert_raw_customlist_nbsabr_status",
                       "next_task_id": "load_raw_customlist_nbsabr_status.begin_insert_raw_customlist_nbsabr_status"
            },
        )

        s3_to_snowflake_raw_customlist_nbsabr_status_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_nbsabr_status",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_nbsabr_status.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_nbsabr_status_task = PythonOperator(
            task_id="insert_log_raw_customlist_nbsabr_status",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_nbsabr_status.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_nbsabr_status_table = PythonOperator(
            task_id="insert_customlist_nbsabr_status",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_nbsabr_status.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_nbsabr_status_task >>
            check_new_files_found_raw_customlist_nbsabr_status_task >>
            [begin_insert_raw_customlist_nbsabr_status_task, skip_insert_raw_customlist_nbsabr_status_task]
        )

        (
                begin_insert_raw_customlist_nbsabr_status_task >>
                s3_to_snowflake_raw_customlist_nbsabr_status_task >>
                insert_log_raw_customlist_nbsabr_status_task >>
                append_to_customlist_nbsabr_status_table >>
                end_insert_raw_customlist_nbsabr_status_task >>
                end_raw_customlist_nbsabr_status_task
        )

        skip_insert_raw_customlist_nbsabr_status_task >> end_raw_customlist_nbsabr_status_task

    #  --- Load raw_customlist_outercasebarcode_type ---

    with TaskGroup(group_id='load_raw_customlist_outercasebarcode_type') as tg_raw_customlist_outercasebarcode_type:
        list_s3_files_raw_customlist_outercasebarcode_type_task = PythonOperator(
            task_id="list_s3_customlist_outercasebarcode_type",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_outercasebarcode_type.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_outercasebarcode_type_task = DummyOperator(task_id="begin_insert_raw_customlist_outercasebarcode_type")
        skip_insert_raw_customlist_outercasebarcode_type_task = DummyOperator(task_id="skip_insert_raw_customlist_outercasebarcode_type")
        end_insert_raw_customlist_outercasebarcode_type_task = DummyOperator(task_id="end_insert_raw_customlist_outercasebarcode_type")
        end_raw_customlist_outercasebarcode_type_task = DummyOperator(
            task_id="end_raw_customlist_outercasebarcode_type",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_outercasebarcode_type_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_outercasebarcode_type',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_outercasebarcode_type.yaml",
                       "skip_task_id": "load_raw_customlist_outercasebarcode_type.skip_insert_raw_customlist_outercasebarcode_type",
                       "next_task_id": "load_raw_customlist_outercasebarcode_type.begin_insert_raw_customlist_outercasebarcode_type"
            },
        )

        s3_to_snowflake_raw_customlist_outercasebarcode_type_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_outercasebarcode_type",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_outercasebarcode_type.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_outercasebarcode_type_task = PythonOperator(
            task_id="insert_log_raw_customlist_outercasebarcode_type",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_outercasebarcode_type.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_outercasebarcode_type_table = PythonOperator(
            task_id="insert_customlist_outercasebarcode_type",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_outercasebarcode_type.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_outercasebarcode_type_task >>
            check_new_files_found_raw_customlist_outercasebarcode_type_task >>
            [begin_insert_raw_customlist_outercasebarcode_type_task, skip_insert_raw_customlist_outercasebarcode_type_task]
        )

        (
                begin_insert_raw_customlist_outercasebarcode_type_task >>
                s3_to_snowflake_raw_customlist_outercasebarcode_type_task >>
                insert_log_raw_customlist_outercasebarcode_type_task >>
                append_to_customlist_outercasebarcode_type_table >>
                end_insert_raw_customlist_outercasebarcode_type_task >>
                end_raw_customlist_outercasebarcode_type_task
        )

        skip_insert_raw_customlist_outercasebarcode_type_task >> end_raw_customlist_outercasebarcode_type_task

    #  --- Load raw_customlist_product_type ---

    with TaskGroup(group_id='load_raw_customlist_product_type') as tg_raw_customlist_product_type:
        list_s3_files_raw_customlist_product_type_task = PythonOperator(
            task_id="list_s3_customlist_product_type",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_product_type.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_product_type_task = DummyOperator(task_id="begin_insert_raw_customlist_product_type")
        skip_insert_raw_customlist_product_type_task = DummyOperator(task_id="skip_insert_raw_customlist_product_type")
        end_insert_raw_customlist_product_type_task = DummyOperator(task_id="end_insert_raw_customlist_product_type")
        end_raw_customlist_product_type_task = DummyOperator(
            task_id="end_raw_customlist_product_type",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_product_type_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_product_type',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_product_type.yaml",
                       "skip_task_id": "load_raw_customlist_product_type.skip_insert_raw_customlist_product_type",
                       "next_task_id": "load_raw_customlist_product_type.begin_insert_raw_customlist_product_type"
            },
        )

        s3_to_snowflake_raw_customlist_product_type_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_product_type",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_product_type.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_product_type_task = PythonOperator(
            task_id="insert_log_raw_customlist_product_type",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_product_type.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_product_type_table = PythonOperator(
            task_id="insert_customlist_product_type",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_product_type.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_product_type_task >>
            check_new_files_found_raw_customlist_product_type_task >>
            [begin_insert_raw_customlist_product_type_task, skip_insert_raw_customlist_product_type_task]
        )

        (
                begin_insert_raw_customlist_product_type_task >>
                s3_to_snowflake_raw_customlist_product_type_task >>
                insert_log_raw_customlist_product_type_task >>
                append_to_customlist_product_type_table >>
                end_insert_raw_customlist_product_type_task >>
                end_raw_customlist_product_type_task
        )

        skip_insert_raw_customlist_product_type_task >> end_raw_customlist_product_type_task

    #  --- Load raw_customlist_scm_lc_cost_alloc_method ---

    with TaskGroup(group_id='load_raw_customlist_scm_lc_cost_alloc_method') as tg_raw_customlist_scm_lc_cost_alloc_method:
        list_s3_files_raw_customlist_scm_lc_cost_alloc_method_task = PythonOperator(
            task_id="list_s3_customlist_scm_lc_cost_alloc_method",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_scm_lc_cost_alloc_method.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_scm_lc_cost_alloc_method_task = DummyOperator(task_id="begin_insert_raw_customlist_scm_lc_cost_alloc_method")
        skip_insert_raw_customlist_scm_lc_cost_alloc_method_task = DummyOperator(task_id="skip_insert_raw_customlist_scm_lc_cost_alloc_method")
        end_insert_raw_customlist_scm_lc_cost_alloc_method_task = DummyOperator(task_id="end_insert_raw_customlist_scm_lc_cost_alloc_method")
        end_raw_customlist_scm_lc_cost_alloc_method_task = DummyOperator(
            task_id="end_raw_customlist_scm_lc_cost_alloc_method",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_scm_lc_cost_alloc_method_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_scm_lc_cost_alloc_method',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_scm_lc_cost_alloc_method.yaml",
                       "skip_task_id": "load_raw_customlist_scm_lc_cost_alloc_method.skip_insert_raw_customlist_scm_lc_cost_alloc_method",
                       "next_task_id": "load_raw_customlist_scm_lc_cost_alloc_method.begin_insert_raw_customlist_scm_lc_cost_alloc_method"
            },
        )

        s3_to_snowflake_raw_customlist_scm_lc_cost_alloc_method_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_scm_lc_cost_alloc_method",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_scm_lc_cost_alloc_method.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_scm_lc_cost_alloc_method_task = PythonOperator(
            task_id="insert_log_raw_customlist_scm_lc_cost_alloc_method",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_scm_lc_cost_alloc_method.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_scm_lc_cost_alloc_method_table = PythonOperator(
            task_id="insert_customlist_scm_lc_cost_alloc_method",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_scm_lc_cost_alloc_method.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_scm_lc_cost_alloc_method_task >>
            check_new_files_found_raw_customlist_scm_lc_cost_alloc_method_task >>
            [begin_insert_raw_customlist_scm_lc_cost_alloc_method_task, skip_insert_raw_customlist_scm_lc_cost_alloc_method_task]
        )

        (
                begin_insert_raw_customlist_scm_lc_cost_alloc_method_task >>
                s3_to_snowflake_raw_customlist_scm_lc_cost_alloc_method_task >>
                insert_log_raw_customlist_scm_lc_cost_alloc_method_task >>
                append_to_customlist_scm_lc_cost_alloc_method_table >>
                end_insert_raw_customlist_scm_lc_cost_alloc_method_task >>
                end_raw_customlist_scm_lc_cost_alloc_method_task
        )

        skip_insert_raw_customlist_scm_lc_cost_alloc_method_task >> end_raw_customlist_scm_lc_cost_alloc_method_task

    #  --- Load raw_customlist_selleraccountlist ---

    with TaskGroup(group_id='load_raw_customlist_selleraccountlist') as tg_raw_customlist_selleraccountlist:
        list_s3_files_raw_customlist_selleraccountlist_task = PythonOperator(
            task_id="list_s3_customlist_selleraccountlist",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlist_selleraccountlist.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlist_selleraccountlist_task = DummyOperator(task_id="begin_insert_raw_customlist_selleraccountlist")
        skip_insert_raw_customlist_selleraccountlist_task = DummyOperator(task_id="skip_insert_raw_customlist_selleraccountlist")
        end_insert_raw_customlist_selleraccountlist_task = DummyOperator(task_id="end_insert_raw_customlist_selleraccountlist")
        end_raw_customlist_selleraccountlist_task = DummyOperator(
            task_id="end_raw_customlist_selleraccountlist",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlist_selleraccountlist_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlist_selleraccountlist',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlist_selleraccountlist.yaml",
                       "skip_task_id": "load_raw_customlist_selleraccountlist.skip_insert_raw_customlist_selleraccountlist",
                       "next_task_id": "load_raw_customlist_selleraccountlist.begin_insert_raw_customlist_selleraccountlist"
            },
        )

        s3_to_snowflake_raw_customlist_selleraccountlist_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlist_selleraccountlist",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlist_selleraccountlist.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlist_selleraccountlist_task = PythonOperator(
            task_id="insert_log_raw_customlist_selleraccountlist",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlist_selleraccountlist.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlist_selleraccountlist_table = PythonOperator(
            task_id="insert_customlist_selleraccountlist",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlist_selleraccountlist.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlist_selleraccountlist_task >>
            check_new_files_found_raw_customlist_selleraccountlist_task >>
            [begin_insert_raw_customlist_selleraccountlist_task, skip_insert_raw_customlist_selleraccountlist_task]
        )

        (
                begin_insert_raw_customlist_selleraccountlist_task >>
                s3_to_snowflake_raw_customlist_selleraccountlist_task >>
                insert_log_raw_customlist_selleraccountlist_task >>
                append_to_customlist_selleraccountlist_table >>
                end_insert_raw_customlist_selleraccountlist_task >>
                end_raw_customlist_selleraccountlist_task
        )

        skip_insert_raw_customlist_selleraccountlist_task >> end_raw_customlist_selleraccountlist_task

    #  --- Load raw_customlistjj_interg_ptnr ---

    with TaskGroup(group_id='load_raw_customlistjj_interg_ptnr') as tg_raw_customlistjj_interg_ptnr:
        list_s3_files_raw_customlistjj_interg_ptnr_task = PythonOperator(
            task_id="list_s3_customlistjj_interg_ptnr",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customlistjj_interg_ptnr.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customlistjj_interg_ptnr_task = DummyOperator(task_id="begin_insert_raw_customlistjj_interg_ptnr")
        skip_insert_raw_customlistjj_interg_ptnr_task = DummyOperator(task_id="skip_insert_raw_customlistjj_interg_ptnr")
        end_insert_raw_customlistjj_interg_ptnr_task = DummyOperator(task_id="end_insert_raw_customlistjj_interg_ptnr")
        end_raw_customlistjj_interg_ptnr_task = DummyOperator(
            task_id="end_raw_customlistjj_interg_ptnr",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customlistjj_interg_ptnr_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customlistjj_interg_ptnr',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customlistjj_interg_ptnr.yaml",
                       "skip_task_id": "load_raw_customlistjj_interg_ptnr.skip_insert_raw_customlistjj_interg_ptnr",
                       "next_task_id": "load_raw_customlistjj_interg_ptnr.begin_insert_raw_customlistjj_interg_ptnr"
            },
        )

        s3_to_snowflake_raw_customlistjj_interg_ptnr_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customlistjj_interg_ptnr",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customlistjj_interg_ptnr.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customlistjj_interg_ptnr_task = PythonOperator(
            task_id="insert_log_raw_customlistjj_interg_ptnr",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customlistjj_interg_ptnr.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customlistjj_interg_ptnr_table = PythonOperator(
            task_id="insert_customlistjj_interg_ptnr",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customlistjj_interg_ptnr.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customlistjj_interg_ptnr_task >>
            check_new_files_found_raw_customlistjj_interg_ptnr_task >>
            [begin_insert_raw_customlistjj_interg_ptnr_task, skip_insert_raw_customlistjj_interg_ptnr_task]
        )

        (
                begin_insert_raw_customlistjj_interg_ptnr_task >>
                s3_to_snowflake_raw_customlistjj_interg_ptnr_task >>
                insert_log_raw_customlistjj_interg_ptnr_task >>
                append_to_customlistjj_interg_ptnr_table >>
                end_insert_raw_customlistjj_interg_ptnr_task >>
                end_raw_customlistjj_interg_ptnr_task
        )

        skip_insert_raw_customlistjj_interg_ptnr_task >> end_raw_customlistjj_interg_ptnr_task

    #  --- Load raw_customrecord_cseg_geography ---

    with TaskGroup(group_id='load_raw_customrecord_cseg_geography') as tg_raw_customrecord_cseg_geography:
        list_s3_files_raw_customrecord_cseg_geography_task = PythonOperator(
            task_id="list_s3_customrecord_cseg_geography",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_cseg_geography.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_cseg_geography_task = DummyOperator(task_id="begin_insert_raw_customrecord_cseg_geography")
        skip_insert_raw_customrecord_cseg_geography_task = DummyOperator(task_id="skip_insert_raw_customrecord_cseg_geography")
        end_insert_raw_customrecord_cseg_geography_task = DummyOperator(task_id="end_insert_raw_customrecord_cseg_geography")
        end_raw_customrecord_cseg_geography_task = DummyOperator(
            task_id="end_raw_customrecord_cseg_geography",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_cseg_geography_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_cseg_geography',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_cseg_geography.yaml",
                       "skip_task_id": "load_raw_customrecord_cseg_geography.skip_insert_raw_customrecord_cseg_geography",
                       "next_task_id": "load_raw_customrecord_cseg_geography.begin_insert_raw_customrecord_cseg_geography"
            },
        )

        s3_to_snowflake_raw_customrecord_cseg_geography_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_cseg_geography",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_cseg_geography.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_cseg_geography_task = PythonOperator(
            task_id="insert_log_raw_customrecord_cseg_geography",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_cseg_geography.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_cseg_geography_table = PythonOperator(
            task_id="insert_customrecord_cseg_geography",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_cseg_geography.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_cseg_geography_task >>
            check_new_files_found_raw_customrecord_cseg_geography_task >>
            [begin_insert_raw_customrecord_cseg_geography_task, skip_insert_raw_customrecord_cseg_geography_task]
        )

        (
                begin_insert_raw_customrecord_cseg_geography_task >>
                s3_to_snowflake_raw_customrecord_cseg_geography_task >>
                insert_log_raw_customrecord_cseg_geography_task >>
                append_to_customrecord_cseg_geography_table >>
                end_insert_raw_customrecord_cseg_geography_task >>
                end_raw_customrecord_cseg_geography_task
        )

        skip_insert_raw_customrecord_cseg_geography_task >> end_raw_customrecord_cseg_geography_task

    #  --- Load raw_customrecord_cseg_npdtype ---

    with TaskGroup(group_id='load_raw_customrecord_cseg_npdtype') as tg_raw_customrecord_cseg_npdtype:
        list_s3_files_raw_customrecord_cseg_npdtype_task = PythonOperator(
            task_id="list_s3_customrecord_cseg_npdtype",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_cseg_npdtype.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_cseg_npdtype_task = DummyOperator(task_id="begin_insert_raw_customrecord_cseg_npdtype")
        skip_insert_raw_customrecord_cseg_npdtype_task = DummyOperator(task_id="skip_insert_raw_customrecord_cseg_npdtype")
        end_insert_raw_customrecord_cseg_npdtype_task = DummyOperator(task_id="end_insert_raw_customrecord_cseg_npdtype")
        end_raw_customrecord_cseg_npdtype_task = DummyOperator(
            task_id="end_raw_customrecord_cseg_npdtype",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_cseg_npdtype_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_cseg_npdtype',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_cseg_npdtype.yaml",
                       "skip_task_id": "load_raw_customrecord_cseg_npdtype.skip_insert_raw_customrecord_cseg_npdtype",
                       "next_task_id": "load_raw_customrecord_cseg_npdtype.begin_insert_raw_customrecord_cseg_npdtype"
            },
        )

        s3_to_snowflake_raw_customrecord_cseg_npdtype_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_cseg_npdtype",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_cseg_npdtype.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_cseg_npdtype_task = PythonOperator(
            task_id="insert_log_raw_customrecord_cseg_npdtype",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_cseg_npdtype.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_cseg_npdtype_table = PythonOperator(
            task_id="insert_customrecord_cseg_npdtype",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_cseg_npdtype.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_cseg_npdtype_task >>
            check_new_files_found_raw_customrecord_cseg_npdtype_task >>
            [begin_insert_raw_customrecord_cseg_npdtype_task, skip_insert_raw_customrecord_cseg_npdtype_task]
        )

        (
                begin_insert_raw_customrecord_cseg_npdtype_task >>
                s3_to_snowflake_raw_customrecord_cseg_npdtype_task >>
                insert_log_raw_customrecord_cseg_npdtype_task >>
                append_to_customrecord_cseg_npdtype_table >>
                end_insert_raw_customrecord_cseg_npdtype_task >>
                end_raw_customrecord_cseg_npdtype_task
        )

        skip_insert_raw_customrecord_cseg_npdtype_task >> end_raw_customrecord_cseg_npdtype_task

    #  --- Load raw_customrecord_cseg_projectcode ---

    with TaskGroup(group_id='load_raw_customrecord_cseg_projectcode') as tg_raw_customrecord_cseg_projectcode:
        list_s3_files_raw_customrecord_cseg_projectcode_task = PythonOperator(
            task_id="list_s3_customrecord_cseg_projectcode",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_cseg_projectcode.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_cseg_projectcode_task = DummyOperator(task_id="begin_insert_raw_customrecord_cseg_projectcode")
        skip_insert_raw_customrecord_cseg_projectcode_task = DummyOperator(task_id="skip_insert_raw_customrecord_cseg_projectcode")
        end_insert_raw_customrecord_cseg_projectcode_task = DummyOperator(task_id="end_insert_raw_customrecord_cseg_projectcode")
        end_raw_customrecord_cseg_projectcode_task = DummyOperator(
            task_id="end_raw_customrecord_cseg_projectcode",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_cseg_projectcode_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_cseg_projectcode',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_cseg_projectcode.yaml",
                       "skip_task_id": "load_raw_customrecord_cseg_projectcode.skip_insert_raw_customrecord_cseg_projectcode",
                       "next_task_id": "load_raw_customrecord_cseg_projectcode.begin_insert_raw_customrecord_cseg_projectcode"
            },
        )

        s3_to_snowflake_raw_customrecord_cseg_projectcode_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_cseg_projectcode",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_cseg_projectcode.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_cseg_projectcode_task = PythonOperator(
            task_id="insert_log_raw_customrecord_cseg_projectcode",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_cseg_projectcode.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_cseg_projectcode_table = PythonOperator(
            task_id="insert_customrecord_cseg_projectcode",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_cseg_projectcode.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_cseg_projectcode_task >>
            check_new_files_found_raw_customrecord_cseg_projectcode_task >>
            [begin_insert_raw_customrecord_cseg_projectcode_task, skip_insert_raw_customrecord_cseg_projectcode_task]
        )

        (
                begin_insert_raw_customrecord_cseg_projectcode_task >>
                s3_to_snowflake_raw_customrecord_cseg_projectcode_task >>
                insert_log_raw_customrecord_cseg_projectcode_task >>
                append_to_customrecord_cseg_projectcode_table >>
                end_insert_raw_customrecord_cseg_projectcode_task >>
                end_raw_customrecord_cseg_projectcode_task
        )

        skip_insert_raw_customrecord_cseg_projectcode_task >> end_raw_customrecord_cseg_projectcode_task

    #  --- Load raw_customrecord_cseg_saleschannel ---

    with TaskGroup(group_id='load_raw_customrecord_cseg_saleschannel') as tg_raw_customrecord_cseg_saleschannel:
        list_s3_files_raw_customrecord_cseg_saleschannel_task = PythonOperator(
            task_id="list_s3_customrecord_cseg_saleschannel",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_cseg_saleschannel.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_cseg_saleschannel_task = DummyOperator(task_id="begin_insert_raw_customrecord_cseg_saleschannel")
        skip_insert_raw_customrecord_cseg_saleschannel_task = DummyOperator(task_id="skip_insert_raw_customrecord_cseg_saleschannel")
        end_insert_raw_customrecord_cseg_saleschannel_task = DummyOperator(task_id="end_insert_raw_customrecord_cseg_saleschannel")
        end_raw_customrecord_cseg_saleschannel_task = DummyOperator(
            task_id="end_raw_customrecord_cseg_saleschannel",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_cseg_saleschannel_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_cseg_saleschannel',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_cseg_saleschannel.yaml",
                       "skip_task_id": "load_raw_customrecord_cseg_saleschannel.skip_insert_raw_customrecord_cseg_saleschannel",
                       "next_task_id": "load_raw_customrecord_cseg_saleschannel.begin_insert_raw_customrecord_cseg_saleschannel"
            },
        )

        s3_to_snowflake_raw_customrecord_cseg_saleschannel_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_cseg_saleschannel",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_cseg_saleschannel.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_cseg_saleschannel_task = PythonOperator(
            task_id="insert_log_raw_customrecord_cseg_saleschannel",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_cseg_saleschannel.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_cseg_saleschannel_table = PythonOperator(
            task_id="insert_customrecord_cseg_saleschannel",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_cseg_saleschannel.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_cseg_saleschannel_task >>
            check_new_files_found_raw_customrecord_cseg_saleschannel_task >>
            [begin_insert_raw_customrecord_cseg_saleschannel_task, skip_insert_raw_customrecord_cseg_saleschannel_task]
        )

        (
                begin_insert_raw_customrecord_cseg_saleschannel_task >>
                s3_to_snowflake_raw_customrecord_cseg_saleschannel_task >>
                insert_log_raw_customrecord_cseg_saleschannel_task >>
                append_to_customrecord_cseg_saleschannel_table >>
                end_insert_raw_customrecord_cseg_saleschannel_task >>
                end_raw_customrecord_cseg_saleschannel_task
        )

        skip_insert_raw_customrecord_cseg_saleschannel_task >> end_raw_customrecord_cseg_saleschannel_task

    #  --- Load raw_customrecord_jj_item_alias_details ---

    with TaskGroup(group_id='load_raw_customrecord_jj_item_alias_details') as tg_raw_customrecord_jj_item_alias_details:
        list_s3_files_raw_customrecord_jj_item_alias_details_task = PythonOperator(
            task_id="list_s3_customrecord_jj_item_alias_details",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_jj_item_alias_details.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_jj_item_alias_details_task = DummyOperator(task_id="begin_insert_raw_customrecord_jj_item_alias_details")
        skip_insert_raw_customrecord_jj_item_alias_details_task = DummyOperator(task_id="skip_insert_raw_customrecord_jj_item_alias_details")
        end_insert_raw_customrecord_jj_item_alias_details_task = DummyOperator(task_id="end_insert_raw_customrecord_jj_item_alias_details")
        end_raw_customrecord_jj_item_alias_details_task = DummyOperator(
            task_id="end_raw_customrecord_jj_item_alias_details",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_jj_item_alias_details_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_jj_item_alias_details',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_jj_item_alias_details.yaml",
                       "skip_task_id": "load_raw_customrecord_jj_item_alias_details.skip_insert_raw_customrecord_jj_item_alias_details",
                       "next_task_id": "load_raw_customrecord_jj_item_alias_details.begin_insert_raw_customrecord_jj_item_alias_details"
            },
        )

        s3_to_snowflake_raw_customrecord_jj_item_alias_details_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_jj_item_alias_details",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_jj_item_alias_details.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_jj_item_alias_details_task = PythonOperator(
            task_id="insert_log_raw_customrecord_jj_item_alias_details",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_jj_item_alias_details.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_jj_item_alias_details_table = PythonOperator(
            task_id="insert_customrecord_jj_item_alias_details",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_jj_item_alias_details.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_jj_item_alias_details_task >>
            check_new_files_found_raw_customrecord_jj_item_alias_details_task >>
            [begin_insert_raw_customrecord_jj_item_alias_details_task, skip_insert_raw_customrecord_jj_item_alias_details_task]
        )

        (
                begin_insert_raw_customrecord_jj_item_alias_details_task >>
                s3_to_snowflake_raw_customrecord_jj_item_alias_details_task >>
                insert_log_raw_customrecord_jj_item_alias_details_task >>
                append_to_customrecord_jj_item_alias_details_table >>
                end_insert_raw_customrecord_jj_item_alias_details_task >>
                end_raw_customrecord_jj_item_alias_details_task
        )

        skip_insert_raw_customrecord_jj_item_alias_details_task >> end_raw_customrecord_jj_item_alias_details_task

    #  --- Load raw_customrecord_jj_vendor_price ---

    with TaskGroup(group_id='load_raw_customrecord_jj_vendor_price') as tg_raw_customrecord_jj_vendor_price:
        list_s3_files_raw_customrecord_jj_vendor_price_task = PythonOperator(
            task_id="list_s3_customrecord_jj_vendor_price",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_jj_vendor_price.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_jj_vendor_price_task = DummyOperator(task_id="begin_insert_raw_customrecord_jj_vendor_price")
        skip_insert_raw_customrecord_jj_vendor_price_task = DummyOperator(task_id="skip_insert_raw_customrecord_jj_vendor_price")
        end_insert_raw_customrecord_jj_vendor_price_task = DummyOperator(task_id="end_insert_raw_customrecord_jj_vendor_price")
        end_raw_customrecord_jj_vendor_price_task = DummyOperator(
            task_id="end_raw_customrecord_jj_vendor_price",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_jj_vendor_price_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_jj_vendor_price',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_jj_vendor_price.yaml",
                       "skip_task_id": "load_raw_customrecord_jj_vendor_price.skip_insert_raw_customrecord_jj_vendor_price",
                       "next_task_id": "load_raw_customrecord_jj_vendor_price.begin_insert_raw_customrecord_jj_vendor_price"
            },
        )

        s3_to_snowflake_raw_customrecord_jj_vendor_price_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_jj_vendor_price",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_jj_vendor_price.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_jj_vendor_price_task = PythonOperator(
            task_id="insert_log_raw_customrecord_jj_vendor_price",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_jj_vendor_price.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_jj_vendor_price_table = PythonOperator(
            task_id="insert_customrecord_jj_vendor_price",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_jj_vendor_price.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_jj_vendor_price_task >>
            check_new_files_found_raw_customrecord_jj_vendor_price_task >>
            [begin_insert_raw_customrecord_jj_vendor_price_task, skip_insert_raw_customrecord_jj_vendor_price_task]
        )

        (
                begin_insert_raw_customrecord_jj_vendor_price_task >>
                s3_to_snowflake_raw_customrecord_jj_vendor_price_task >>
                insert_log_raw_customrecord_jj_vendor_price_task >>
                append_to_customrecord_jj_vendor_price_table >>
                end_insert_raw_customrecord_jj_vendor_price_task >>
                end_raw_customrecord_jj_vendor_price_task
        )

        skip_insert_raw_customrecord_jj_vendor_price_task >> end_raw_customrecord_jj_vendor_price_task

    #  --- Load raw_customrecord_nbsabr_accountsetup ---

    with TaskGroup(group_id='load_raw_customrecord_nbsabr_accountsetup') as tg_raw_customrecord_nbsabr_accountsetup:
        list_s3_files_raw_customrecord_nbsabr_accountsetup_task = PythonOperator(
            task_id="list_s3_customrecord_nbsabr_accountsetup",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_nbsabr_accountsetup.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_nbsabr_accountsetup_task = DummyOperator(task_id="begin_insert_raw_customrecord_nbsabr_accountsetup")
        skip_insert_raw_customrecord_nbsabr_accountsetup_task = DummyOperator(task_id="skip_insert_raw_customrecord_nbsabr_accountsetup")
        end_insert_raw_customrecord_nbsabr_accountsetup_task = DummyOperator(task_id="end_insert_raw_customrecord_nbsabr_accountsetup")
        end_raw_customrecord_nbsabr_accountsetup_task = DummyOperator(
            task_id="end_raw_customrecord_nbsabr_accountsetup",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_nbsabr_accountsetup_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_nbsabr_accountsetup',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_nbsabr_accountsetup.yaml",
                       "skip_task_id": "load_raw_customrecord_nbsabr_accountsetup.skip_insert_raw_customrecord_nbsabr_accountsetup",
                       "next_task_id": "load_raw_customrecord_nbsabr_accountsetup.begin_insert_raw_customrecord_nbsabr_accountsetup"
            },
        )

        s3_to_snowflake_raw_customrecord_nbsabr_accountsetup_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_nbsabr_accountsetup",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_nbsabr_accountsetup.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_nbsabr_accountsetup_task = PythonOperator(
            task_id="insert_log_raw_customrecord_nbsabr_accountsetup",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_nbsabr_accountsetup.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_nbsabr_accountsetup_table = PythonOperator(
            task_id="insert_customrecord_nbsabr_accountsetup",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_nbsabr_accountsetup.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_nbsabr_accountsetup_task >>
            check_new_files_found_raw_customrecord_nbsabr_accountsetup_task >>
            [begin_insert_raw_customrecord_nbsabr_accountsetup_task, skip_insert_raw_customrecord_nbsabr_accountsetup_task]
        )

        (
                begin_insert_raw_customrecord_nbsabr_accountsetup_task >>
                s3_to_snowflake_raw_customrecord_nbsabr_accountsetup_task >>
                insert_log_raw_customrecord_nbsabr_accountsetup_task >>
                append_to_customrecord_nbsabr_accountsetup_table >>
                end_insert_raw_customrecord_nbsabr_accountsetup_task >>
                end_raw_customrecord_nbsabr_accountsetup_task
        )

        skip_insert_raw_customrecord_nbsabr_accountsetup_task >> end_raw_customrecord_nbsabr_accountsetup_task

    #  --- Load raw_customrecord_nbsabr_bankstatement ---

    with TaskGroup(group_id='load_raw_customrecord_nbsabr_bankstatement') as tg_raw_customrecord_nbsabr_bankstatement:
        list_s3_files_raw_customrecord_nbsabr_bankstatement_task = PythonOperator(
            task_id="list_s3_customrecord_nbsabr_bankstatement",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_nbsabr_bankstatement.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_nbsabr_bankstatement_task = DummyOperator(task_id="begin_insert_raw_customrecord_nbsabr_bankstatement")
        skip_insert_raw_customrecord_nbsabr_bankstatement_task = DummyOperator(task_id="skip_insert_raw_customrecord_nbsabr_bankstatement")
        end_insert_raw_customrecord_nbsabr_bankstatement_task = DummyOperator(task_id="end_insert_raw_customrecord_nbsabr_bankstatement")
        end_raw_customrecord_nbsabr_bankstatement_task = DummyOperator(
            task_id="end_raw_customrecord_nbsabr_bankstatement",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_nbsabr_bankstatement_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_nbsabr_bankstatement',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_nbsabr_bankstatement.yaml",
                       "skip_task_id": "load_raw_customrecord_nbsabr_bankstatement.skip_insert_raw_customrecord_nbsabr_bankstatement",
                       "next_task_id": "load_raw_customrecord_nbsabr_bankstatement.begin_insert_raw_customrecord_nbsabr_bankstatement"
            },
        )

        s3_to_snowflake_raw_customrecord_nbsabr_bankstatement_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_nbsabr_bankstatement",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_nbsabr_bankstatement.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_nbsabr_bankstatement_task = PythonOperator(
            task_id="insert_log_raw_customrecord_nbsabr_bankstatement",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_nbsabr_bankstatement.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_nbsabr_bankstatement_table = PythonOperator(
            task_id="insert_customrecord_nbsabr_bankstatement",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_nbsabr_bankstatement.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_nbsabr_bankstatement_task >>
            check_new_files_found_raw_customrecord_nbsabr_bankstatement_task >>
            [begin_insert_raw_customrecord_nbsabr_bankstatement_task, skip_insert_raw_customrecord_nbsabr_bankstatement_task]
        )

        (
                begin_insert_raw_customrecord_nbsabr_bankstatement_task >>
                s3_to_snowflake_raw_customrecord_nbsabr_bankstatement_task >>
                insert_log_raw_customrecord_nbsabr_bankstatement_task >>
                append_to_customrecord_nbsabr_bankstatement_table >>
                end_insert_raw_customrecord_nbsabr_bankstatement_task >>
                end_raw_customrecord_nbsabr_bankstatement_task
        )

        skip_insert_raw_customrecord_nbsabr_bankstatement_task >> end_raw_customrecord_nbsabr_bankstatement_task

    #  --- Load raw_customrecord_nbsabr_bankstatementline ---

    with TaskGroup(group_id='load_raw_customrecord_nbsabr_bankstatementline') as tg_raw_customrecord_nbsabr_bankstatementline:
        list_s3_files_raw_customrecord_nbsabr_bankstatementline_task = PythonOperator(
            task_id="list_s3_customrecord_nbsabr_bankstatementline",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_nbsabr_bankstatementline.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_nbsabr_bankstatementline_task = DummyOperator(task_id="begin_insert_raw_customrecord_nbsabr_bankstatementline")
        skip_insert_raw_customrecord_nbsabr_bankstatementline_task = DummyOperator(task_id="skip_insert_raw_customrecord_nbsabr_bankstatementline")
        end_insert_raw_customrecord_nbsabr_bankstatementline_task = DummyOperator(task_id="end_insert_raw_customrecord_nbsabr_bankstatementline")
        end_raw_customrecord_nbsabr_bankstatementline_task = DummyOperator(
            task_id="end_raw_customrecord_nbsabr_bankstatementline",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_nbsabr_bankstatementline_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_nbsabr_bankstatementline',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_nbsabr_bankstatementline.yaml",
                       "skip_task_id": "load_raw_customrecord_nbsabr_bankstatementline.skip_insert_raw_customrecord_nbsabr_bankstatementline",
                       "next_task_id": "load_raw_customrecord_nbsabr_bankstatementline.begin_insert_raw_customrecord_nbsabr_bankstatementline"
            },
        )

        s3_to_snowflake_raw_customrecord_nbsabr_bankstatementline_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_nbsabr_bankstatementline",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_nbsabr_bankstatementline.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_nbsabr_bankstatementline_task = PythonOperator(
            task_id="insert_log_raw_customrecord_nbsabr_bankstatementline",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_nbsabr_bankstatementline.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_nbsabr_bankstatementline_table = PythonOperator(
            task_id="insert_customrecord_nbsabr_bankstatementline",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_nbsabr_bankstatementline.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_nbsabr_bankstatementline_task >>
            check_new_files_found_raw_customrecord_nbsabr_bankstatementline_task >>
            [begin_insert_raw_customrecord_nbsabr_bankstatementline_task, skip_insert_raw_customrecord_nbsabr_bankstatementline_task]
        )

        (
                begin_insert_raw_customrecord_nbsabr_bankstatementline_task >>
                s3_to_snowflake_raw_customrecord_nbsabr_bankstatementline_task >>
                insert_log_raw_customrecord_nbsabr_bankstatementline_task >>
                append_to_customrecord_nbsabr_bankstatementline_table >>
                end_insert_raw_customrecord_nbsabr_bankstatementline_task >>
                end_raw_customrecord_nbsabr_bankstatementline_task
        )

        skip_insert_raw_customrecord_nbsabr_bankstatementline_task >> end_raw_customrecord_nbsabr_bankstatementline_task

    #  --- Load raw_customrecord_nbsabr_reconciliationstate ---

    with TaskGroup(group_id='load_raw_customrecord_nbsabr_reconciliationstate') as tg_raw_customrecord_nbsabr_reconciliationstate:
        list_s3_files_raw_customrecord_nbsabr_reconciliationstate_task = PythonOperator(
            task_id="list_s3_customrecord_nbsabr_reconciliationstate",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_nbsabr_reconciliationstate.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_nbsabr_reconciliationstate_task = DummyOperator(task_id="begin_insert_raw_customrecord_nbsabr_reconciliationstate")
        skip_insert_raw_customrecord_nbsabr_reconciliationstate_task = DummyOperator(task_id="skip_insert_raw_customrecord_nbsabr_reconciliationstate")
        end_insert_raw_customrecord_nbsabr_reconciliationstate_task = DummyOperator(task_id="end_insert_raw_customrecord_nbsabr_reconciliationstate")
        end_raw_customrecord_nbsabr_reconciliationstate_task = DummyOperator(
            task_id="end_raw_customrecord_nbsabr_reconciliationstate",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_nbsabr_reconciliationstate_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_nbsabr_reconciliationstate',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_nbsabr_reconciliationstate.yaml",
                       "skip_task_id": "load_raw_customrecord_nbsabr_reconciliationstate.skip_insert_raw_customrecord_nbsabr_reconciliationstate",
                       "next_task_id": "load_raw_customrecord_nbsabr_reconciliationstate.begin_insert_raw_customrecord_nbsabr_reconciliationstate"
            },
        )

        s3_to_snowflake_raw_customrecord_nbsabr_reconciliationstate_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_nbsabr_reconciliationstate",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_nbsabr_reconciliationstate.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_nbsabr_reconciliationstate_task = PythonOperator(
            task_id="insert_log_raw_customrecord_nbsabr_reconciliationstate",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_nbsabr_reconciliationstate.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_nbsabr_reconciliationstate_table = PythonOperator(
            task_id="insert_customrecord_nbsabr_reconciliationstate",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_nbsabr_reconciliationstate.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_nbsabr_reconciliationstate_task >>
            check_new_files_found_raw_customrecord_nbsabr_reconciliationstate_task >>
            [begin_insert_raw_customrecord_nbsabr_reconciliationstate_task, skip_insert_raw_customrecord_nbsabr_reconciliationstate_task]
        )

        (
                begin_insert_raw_customrecord_nbsabr_reconciliationstate_task >>
                s3_to_snowflake_raw_customrecord_nbsabr_reconciliationstate_task >>
                insert_log_raw_customrecord_nbsabr_reconciliationstate_task >>
                append_to_customrecord_nbsabr_reconciliationstate_table >>
                end_insert_raw_customrecord_nbsabr_reconciliationstate_task >>
                end_raw_customrecord_nbsabr_reconciliationstate_task
        )

        skip_insert_raw_customrecord_nbsabr_reconciliationstate_task >> end_raw_customrecord_nbsabr_reconciliationstate_task

    #  --- Load raw_customrecord_nbsabr_statementhistory ---

    with TaskGroup(group_id='load_raw_customrecord_nbsabr_statementhistory') as tg_raw_customrecord_nbsabr_statementhistory:
        list_s3_files_raw_customrecord_nbsabr_statementhistory_task = PythonOperator(
            task_id="list_s3_customrecord_nbsabr_statementhistory",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_nbsabr_statementhistory.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_nbsabr_statementhistory_task = DummyOperator(task_id="begin_insert_raw_customrecord_nbsabr_statementhistory")
        skip_insert_raw_customrecord_nbsabr_statementhistory_task = DummyOperator(task_id="skip_insert_raw_customrecord_nbsabr_statementhistory")
        end_insert_raw_customrecord_nbsabr_statementhistory_task = DummyOperator(task_id="end_insert_raw_customrecord_nbsabr_statementhistory")
        end_raw_customrecord_nbsabr_statementhistory_task = DummyOperator(
            task_id="end_raw_customrecord_nbsabr_statementhistory",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_nbsabr_statementhistory_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_nbsabr_statementhistory',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_nbsabr_statementhistory.yaml",
                       "skip_task_id": "load_raw_customrecord_nbsabr_statementhistory.skip_insert_raw_customrecord_nbsabr_statementhistory",
                       "next_task_id": "load_raw_customrecord_nbsabr_statementhistory.begin_insert_raw_customrecord_nbsabr_statementhistory"
            },
        )

        s3_to_snowflake_raw_customrecord_nbsabr_statementhistory_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_nbsabr_statementhistory",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_nbsabr_statementhistory.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_nbsabr_statementhistory_task = PythonOperator(
            task_id="insert_log_raw_customrecord_nbsabr_statementhistory",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_nbsabr_statementhistory.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_nbsabr_statementhistory_table = PythonOperator(
            task_id="insert_customrecord_nbsabr_statementhistory",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_nbsabr_statementhistory.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_nbsabr_statementhistory_task >>
            check_new_files_found_raw_customrecord_nbsabr_statementhistory_task >>
            [begin_insert_raw_customrecord_nbsabr_statementhistory_task, skip_insert_raw_customrecord_nbsabr_statementhistory_task]
        )

        (
                begin_insert_raw_customrecord_nbsabr_statementhistory_task >>
                s3_to_snowflake_raw_customrecord_nbsabr_statementhistory_task >>
                insert_log_raw_customrecord_nbsabr_statementhistory_task >>
                append_to_customrecord_nbsabr_statementhistory_table >>
                end_insert_raw_customrecord_nbsabr_statementhistory_task >>
                end_raw_customrecord_nbsabr_statementhistory_task
        )

        skip_insert_raw_customrecord_nbsabr_statementhistory_task >> end_raw_customrecord_nbsabr_statementhistory_task

    #  --- Load raw_customrecord_nbsabr_trntypedefn ---

    with TaskGroup(group_id='load_raw_customrecord_nbsabr_trntypedefn') as tg_raw_customrecord_nbsabr_trntypedefn:
        list_s3_files_raw_customrecord_nbsabr_trntypedefn_task = PythonOperator(
            task_id="list_s3_customrecord_nbsabr_trntypedefn",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_nbsabr_trntypedefn.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_nbsabr_trntypedefn_task = DummyOperator(task_id="begin_insert_raw_customrecord_nbsabr_trntypedefn")
        skip_insert_raw_customrecord_nbsabr_trntypedefn_task = DummyOperator(task_id="skip_insert_raw_customrecord_nbsabr_trntypedefn")
        end_insert_raw_customrecord_nbsabr_trntypedefn_task = DummyOperator(task_id="end_insert_raw_customrecord_nbsabr_trntypedefn")
        end_raw_customrecord_nbsabr_trntypedefn_task = DummyOperator(
            task_id="end_raw_customrecord_nbsabr_trntypedefn",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_nbsabr_trntypedefn_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_nbsabr_trntypedefn',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_nbsabr_trntypedefn.yaml",
                       "skip_task_id": "load_raw_customrecord_nbsabr_trntypedefn.skip_insert_raw_customrecord_nbsabr_trntypedefn",
                       "next_task_id": "load_raw_customrecord_nbsabr_trntypedefn.begin_insert_raw_customrecord_nbsabr_trntypedefn"
            },
        )

        s3_to_snowflake_raw_customrecord_nbsabr_trntypedefn_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_nbsabr_trntypedefn",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_nbsabr_trntypedefn.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_nbsabr_trntypedefn_task = PythonOperator(
            task_id="insert_log_raw_customrecord_nbsabr_trntypedefn",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_nbsabr_trntypedefn.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_nbsabr_trntypedefn_table = PythonOperator(
            task_id="insert_customrecord_nbsabr_trntypedefn",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_nbsabr_trntypedefn.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_nbsabr_trntypedefn_task >>
            check_new_files_found_raw_customrecord_nbsabr_trntypedefn_task >>
            [begin_insert_raw_customrecord_nbsabr_trntypedefn_task, skip_insert_raw_customrecord_nbsabr_trntypedefn_task]
        )

        (
                begin_insert_raw_customrecord_nbsabr_trntypedefn_task >>
                s3_to_snowflake_raw_customrecord_nbsabr_trntypedefn_task >>
                insert_log_raw_customrecord_nbsabr_trntypedefn_task >>
                append_to_customrecord_nbsabr_trntypedefn_table >>
                end_insert_raw_customrecord_nbsabr_trntypedefn_task >>
                end_raw_customrecord_nbsabr_trntypedefn_task
        )

        skip_insert_raw_customrecord_nbsabr_trntypedefn_task >> end_raw_customrecord_nbsabr_trntypedefn_task

    #  --- Load raw_customrecord_scm_lc_mapping ---

    with TaskGroup(group_id='load_raw_customrecord_scm_lc_mapping') as tg_raw_customrecord_scm_lc_mapping:
        list_s3_files_raw_customrecord_scm_lc_mapping_task = PythonOperator(
            task_id="list_s3_customrecord_scm_lc_mapping",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_scm_lc_mapping.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_scm_lc_mapping_task = DummyOperator(task_id="begin_insert_raw_customrecord_scm_lc_mapping")
        skip_insert_raw_customrecord_scm_lc_mapping_task = DummyOperator(task_id="skip_insert_raw_customrecord_scm_lc_mapping")
        end_insert_raw_customrecord_scm_lc_mapping_task = DummyOperator(task_id="end_insert_raw_customrecord_scm_lc_mapping")
        end_raw_customrecord_scm_lc_mapping_task = DummyOperator(
            task_id="end_raw_customrecord_scm_lc_mapping",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_scm_lc_mapping_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_scm_lc_mapping',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_scm_lc_mapping.yaml",
                       "skip_task_id": "load_raw_customrecord_scm_lc_mapping.skip_insert_raw_customrecord_scm_lc_mapping",
                       "next_task_id": "load_raw_customrecord_scm_lc_mapping.begin_insert_raw_customrecord_scm_lc_mapping"
            },
        )

        s3_to_snowflake_raw_customrecord_scm_lc_mapping_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_scm_lc_mapping",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_scm_lc_mapping.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_scm_lc_mapping_task = PythonOperator(
            task_id="insert_log_raw_customrecord_scm_lc_mapping",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_scm_lc_mapping.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_scm_lc_mapping_table = PythonOperator(
            task_id="insert_customrecord_scm_lc_mapping",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_scm_lc_mapping.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_scm_lc_mapping_task >>
            check_new_files_found_raw_customrecord_scm_lc_mapping_task >>
            [begin_insert_raw_customrecord_scm_lc_mapping_task, skip_insert_raw_customrecord_scm_lc_mapping_task]
        )

        (
                begin_insert_raw_customrecord_scm_lc_mapping_task >>
                s3_to_snowflake_raw_customrecord_scm_lc_mapping_task >>
                insert_log_raw_customrecord_scm_lc_mapping_task >>
                append_to_customrecord_scm_lc_mapping_table >>
                end_insert_raw_customrecord_scm_lc_mapping_task >>
                end_raw_customrecord_scm_lc_mapping_task
        )

        skip_insert_raw_customrecord_scm_lc_mapping_task >> end_raw_customrecord_scm_lc_mapping_task

    #  --- Load raw_customrecord_scm_lc_profile ---

    with TaskGroup(group_id='load_raw_customrecord_scm_lc_profile') as tg_raw_customrecord_scm_lc_profile:
        list_s3_files_raw_customrecord_scm_lc_profile_task = PythonOperator(
            task_id="list_s3_customrecord_scm_lc_profile",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_scm_lc_profile.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_scm_lc_profile_task = DummyOperator(task_id="begin_insert_raw_customrecord_scm_lc_profile")
        skip_insert_raw_customrecord_scm_lc_profile_task = DummyOperator(task_id="skip_insert_raw_customrecord_scm_lc_profile")
        end_insert_raw_customrecord_scm_lc_profile_task = DummyOperator(task_id="end_insert_raw_customrecord_scm_lc_profile")
        end_raw_customrecord_scm_lc_profile_task = DummyOperator(
            task_id="end_raw_customrecord_scm_lc_profile",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_scm_lc_profile_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_scm_lc_profile',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_scm_lc_profile.yaml",
                       "skip_task_id": "load_raw_customrecord_scm_lc_profile.skip_insert_raw_customrecord_scm_lc_profile",
                       "next_task_id": "load_raw_customrecord_scm_lc_profile.begin_insert_raw_customrecord_scm_lc_profile"
            },
        )

        s3_to_snowflake_raw_customrecord_scm_lc_profile_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_scm_lc_profile",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_scm_lc_profile.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_scm_lc_profile_task = PythonOperator(
            task_id="insert_log_raw_customrecord_scm_lc_profile",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_scm_lc_profile.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_scm_lc_profile_table = PythonOperator(
            task_id="insert_customrecord_scm_lc_profile",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_scm_lc_profile.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_scm_lc_profile_task >>
            check_new_files_found_raw_customrecord_scm_lc_profile_task >>
            [begin_insert_raw_customrecord_scm_lc_profile_task, skip_insert_raw_customrecord_scm_lc_profile_task]
        )

        (
                begin_insert_raw_customrecord_scm_lc_profile_task >>
                s3_to_snowflake_raw_customrecord_scm_lc_profile_task >>
                insert_log_raw_customrecord_scm_lc_profile_task >>
                append_to_customrecord_scm_lc_profile_table >>
                end_insert_raw_customrecord_scm_lc_profile_task >>
                end_raw_customrecord_scm_lc_profile_task
        )

        skip_insert_raw_customrecord_scm_lc_profile_task >> end_raw_customrecord_scm_lc_profile_task

    #  --- Load raw_customrecord_scm_lc_profile_detail ---

    with TaskGroup(group_id='load_raw_customrecord_scm_lc_profile_detail') as tg_raw_customrecord_scm_lc_profile_detail:
        list_s3_files_raw_customrecord_scm_lc_profile_detail_task = PythonOperator(
            task_id="list_s3_customrecord_scm_lc_profile_detail",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_customrecord_scm_lc_profile_detail.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_customrecord_scm_lc_profile_detail_task = DummyOperator(task_id="begin_insert_raw_customrecord_scm_lc_profile_detail")
        skip_insert_raw_customrecord_scm_lc_profile_detail_task = DummyOperator(task_id="skip_insert_raw_customrecord_scm_lc_profile_detail")
        end_insert_raw_customrecord_scm_lc_profile_detail_task = DummyOperator(task_id="end_insert_raw_customrecord_scm_lc_profile_detail")
        end_raw_customrecord_scm_lc_profile_detail_task = DummyOperator(
            task_id="end_raw_customrecord_scm_lc_profile_detail",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_customrecord_scm_lc_profile_detail_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_customrecord_scm_lc_profile_detail',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_customrecord_scm_lc_profile_detail.yaml",
                       "skip_task_id": "load_raw_customrecord_scm_lc_profile_detail.skip_insert_raw_customrecord_scm_lc_profile_detail",
                       "next_task_id": "load_raw_customrecord_scm_lc_profile_detail.begin_insert_raw_customrecord_scm_lc_profile_detail"
            },
        )

        s3_to_snowflake_raw_customrecord_scm_lc_profile_detail_task = PythonOperator(
            task_id="s3_to_snowflake_raw_customrecord_scm_lc_profile_detail",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_customrecord_scm_lc_profile_detail.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_customrecord_scm_lc_profile_detail_task = PythonOperator(
            task_id="insert_log_raw_customrecord_scm_lc_profile_detail",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_customrecord_scm_lc_profile_detail.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_customrecord_scm_lc_profile_detail_table = PythonOperator(
            task_id="insert_customrecord_scm_lc_profile_detail",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_customrecord_scm_lc_profile_detail.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_customrecord_scm_lc_profile_detail_task >>
            check_new_files_found_raw_customrecord_scm_lc_profile_detail_task >>
            [begin_insert_raw_customrecord_scm_lc_profile_detail_task, skip_insert_raw_customrecord_scm_lc_profile_detail_task]
        )

        (
                begin_insert_raw_customrecord_scm_lc_profile_detail_task >>
                s3_to_snowflake_raw_customrecord_scm_lc_profile_detail_task >>
                insert_log_raw_customrecord_scm_lc_profile_detail_task >>
                append_to_customrecord_scm_lc_profile_detail_table >>
                end_insert_raw_customrecord_scm_lc_profile_detail_task >>
                end_raw_customrecord_scm_lc_profile_detail_task
        )

        skip_insert_raw_customrecord_scm_lc_profile_detail_task >> end_raw_customrecord_scm_lc_profile_detail_task

    #  --- Load raw_deletedrecord ---

    with TaskGroup(group_id='load_raw_deletedrecord') as tg_raw_deletedrecord:
        list_s3_files_raw_deletedrecord_task = PythonOperator(
            task_id="list_s3_deletedrecord",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_deletedrecord.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_deletedrecord_task = DummyOperator(task_id="begin_insert_raw_deletedrecord")
        skip_insert_raw_deletedrecord_task = DummyOperator(task_id="skip_insert_raw_deletedrecord")
        end_insert_raw_deletedrecord_task = DummyOperator(task_id="end_insert_raw_deletedrecord")
        end_raw_deletedrecord_task = DummyOperator(
            task_id="end_raw_deletedrecord",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_deletedrecord_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_deletedrecord',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_deletedrecord.yaml",
                       "skip_task_id": "load_raw_deletedrecord.skip_insert_raw_deletedrecord",
                       "next_task_id": "load_raw_deletedrecord.begin_insert_raw_deletedrecord"
            },
        )

        s3_to_snowflake_raw_deletedrecord_task = PythonOperator(
            task_id="s3_to_snowflake_raw_deletedrecord",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_deletedrecord.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_deletedrecord_task = PythonOperator(
            task_id="insert_log_raw_deletedrecord",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_deletedrecord.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_deletedrecord_table = PythonOperator(
            task_id="insert_deletedrecord",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_deletedrecord.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_deletedrecord_task >>
            check_new_files_found_raw_deletedrecord_task >>
            [begin_insert_raw_deletedrecord_task, skip_insert_raw_deletedrecord_task]
        )

        (
                begin_insert_raw_deletedrecord_task >>
                s3_to_snowflake_raw_deletedrecord_task >>
                insert_log_raw_deletedrecord_task >>
                append_to_deletedrecord_table >>
                end_insert_raw_deletedrecord_task >>
                end_raw_deletedrecord_task
        )

        skip_insert_raw_deletedrecord_task >> end_raw_deletedrecord_task

    #  --- Load raw_department ---

    with TaskGroup(group_id='load_raw_department') as tg_raw_department:
        list_s3_files_raw_department_task = PythonOperator(
            task_id="list_s3_department",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_department.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_department_task = DummyOperator(task_id="begin_insert_raw_department")
        skip_insert_raw_department_task = DummyOperator(task_id="skip_insert_raw_department")
        end_insert_raw_department_task = DummyOperator(task_id="end_insert_raw_department")
        end_raw_department_task = DummyOperator(
            task_id="end_raw_department",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_department_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_department',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_department.yaml",
                       "skip_task_id": "load_raw_department.skip_insert_raw_department",
                       "next_task_id": "load_raw_department.begin_insert_raw_department"
            },
        )

        s3_to_snowflake_raw_department_task = PythonOperator(
            task_id="s3_to_snowflake_raw_department",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_department.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_department_task = PythonOperator(
            task_id="insert_log_raw_department",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_department.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_department_table = PythonOperator(
            task_id="insert_department",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_department.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_department_task >>
            check_new_files_found_raw_department_task >>
            [begin_insert_raw_department_task, skip_insert_raw_department_task]
        )

        (
                begin_insert_raw_department_task >>
                s3_to_snowflake_raw_department_task >>
                insert_log_raw_department_task >>
                append_to_department_table >>
                end_insert_raw_department_task >>
                end_raw_department_task
        )

        skip_insert_raw_department_task >> end_raw_department_task

    #  --- Load raw_employee ---

    with TaskGroup(group_id='load_raw_employee') as tg_raw_employee:
        list_s3_files_raw_employee_task = PythonOperator(
            task_id="list_s3_employee",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_employee.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_employee_task = DummyOperator(task_id="begin_insert_raw_employee")
        skip_insert_raw_employee_task = DummyOperator(task_id="skip_insert_raw_employee")
        end_insert_raw_employee_task = DummyOperator(task_id="end_insert_raw_employee")
        end_raw_employee_task = DummyOperator(
            task_id="end_raw_employee",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_employee_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_employee',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_employee.yaml",
                       "skip_task_id": "load_raw_employee.skip_insert_raw_employee",
                       "next_task_id": "load_raw_employee.begin_insert_raw_employee"
            },
        )

        s3_to_snowflake_raw_employee_task = PythonOperator(
            task_id="s3_to_snowflake_raw_employee",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_employee.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_employee_task = PythonOperator(
            task_id="insert_log_raw_employee",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_employee.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_employee_table = PythonOperator(
            task_id="insert_employee",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_employee.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_employee_task >>
            check_new_files_found_raw_employee_task >>
            [begin_insert_raw_employee_task, skip_insert_raw_employee_task]
        )

        (
                begin_insert_raw_employee_task >>
                s3_to_snowflake_raw_employee_task >>
                insert_log_raw_employee_task >>
                append_to_employee_table >>
                end_insert_raw_employee_task >>
                end_raw_employee_task
        )

        skip_insert_raw_employee_task >> end_raw_employee_task

    #  --- Load raw_entity ---

    # with TaskGroup(group_id='load_raw_entity') as tg_raw_entity:
    #     list_s3_files_raw_entity_task = PythonOperator(
    #         task_id="list_s3_entity",
    #         python_callable=tls3.list_s3_modified_files,
    #         op_kwargs={
    #             "args_file": "netsuite_ingestion/list_s3_entity.yaml",
    #             "wf_params": WF_PARAMS_EXPR},
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #
    #     )
    #
    #     # Need a begin dummy operator for branching
    #     begin_insert_raw_entity_task = DummyOperator(task_id="begin_insert_raw_entity")
    #     skip_insert_raw_entity_task = DummyOperator(task_id="skip_insert_raw_entity")
    #     end_insert_raw_entity_task = DummyOperator(task_id="end_insert_raw_entity")
    #     end_raw_entity_task = DummyOperator(
    #         task_id="end_raw_entity",
    #         trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    #     )
    #
    #     check_new_files_found_raw_entity_task = BranchPythonOperator(
    #         task_id='check_new_files_found_raw_entity',
    #         python_callable=check_for_new_files,
    #         op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_entity.yaml",
    #                    "skip_task_id": "load_raw_entity.skip_insert_raw_entity",
    #                    "next_task_id": "load_raw_entity.begin_insert_raw_entity"
    #         },
    #     )
    #
    #     s3_to_snowflake_raw_entity_task = PythonOperator(
    #         task_id="s3_to_snowflake_raw_entity",
    #         python_callable=load_obj.s3_to_snowflake_load,
    #         op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_entity.yaml"},
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     insert_log_raw_entity_task = PythonOperator(
    #         task_id="insert_log_raw_entity",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={"connection":"Snowflake",
    #                    "sql_file": "netsuite_ingestion/insert_log_entity.sql",
    #                    "wf_params": WF_PARAMS_EXPR
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     append_to_entity_table = PythonOperator(
    #         task_id="insert_entity",
    #         python_callable=run_query_file,
    #         op_kwargs={"connection":"Snowflake",
    #                    "sql_file": "netsuite_ingestion/insert_entity.sql",
    #                    "wf_params": WF_PARAMS_EXPR
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     (
    #         list_s3_files_raw_entity_task >>
    #         check_new_files_found_raw_entity_task >>
    #         [begin_insert_raw_entity_task, skip_insert_raw_entity_task]
    #     )
    #
    #     (
    #             begin_insert_raw_entity_task >>
    #             s3_to_snowflake_raw_entity_task >>
    #             insert_log_raw_entity_task >>
    #             append_to_entity_table >>
    #             end_insert_raw_entity_task >>
    #             end_raw_entity_task
    #     )
    #
    #     skip_insert_raw_entity_task >> end_raw_entity_task

    #  --- Load raw_entityaddress ---

    with TaskGroup(group_id='load_raw_entityaddress') as tg_raw_entityaddress:
        list_s3_files_raw_entityaddress_task = PythonOperator(
            task_id="list_s3_entityaddress",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_entityaddress.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_entityaddress_task = DummyOperator(task_id="begin_insert_raw_entityaddress")
        skip_insert_raw_entityaddress_task = DummyOperator(task_id="skip_insert_raw_entityaddress")
        end_insert_raw_entityaddress_task = DummyOperator(task_id="end_insert_raw_entityaddress")
        end_raw_entityaddress_task = DummyOperator(
            task_id="end_raw_entityaddress",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_entityaddress_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_entityaddress',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_entityaddress.yaml",
                       "skip_task_id": "load_raw_entityaddress.skip_insert_raw_entityaddress",
                       "next_task_id": "load_raw_entityaddress.begin_insert_raw_entityaddress"
            },
        )

        s3_to_snowflake_raw_entityaddress_task = PythonOperator(
            task_id="s3_to_snowflake_raw_entityaddress",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_entityaddress.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_entityaddress_task = PythonOperator(
            task_id="insert_log_raw_entityaddress",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_entityaddress.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_entityaddress_table = PythonOperator(
            task_id="insert_entityaddress",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_entityaddress.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_entityaddress_task >>
            check_new_files_found_raw_entityaddress_task >>
            [begin_insert_raw_entityaddress_task, skip_insert_raw_entityaddress_task]
        )

        (
                begin_insert_raw_entityaddress_task >>
                s3_to_snowflake_raw_entityaddress_task >>
                insert_log_raw_entityaddress_task >>
                append_to_entityaddress_table >>
                end_insert_raw_entityaddress_task >>
                end_raw_entityaddress_task
        )

        skip_insert_raw_entityaddress_task >> end_raw_entityaddress_task

    #  --- Load raw_incoterm ---

    with TaskGroup(group_id='load_raw_incoterm') as tg_raw_incoterm:
        list_s3_files_raw_incoterm_task = PythonOperator(
            task_id="list_s3_incoterm",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_incoterm.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_incoterm_task = DummyOperator(task_id="begin_insert_raw_incoterm")
        skip_insert_raw_incoterm_task = DummyOperator(task_id="skip_insert_raw_incoterm")
        end_insert_raw_incoterm_task = DummyOperator(task_id="end_insert_raw_incoterm")
        end_raw_incoterm_task = DummyOperator(
            task_id="end_raw_incoterm",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_incoterm_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_incoterm',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_incoterm.yaml",
                       "skip_task_id": "load_raw_incoterm.skip_insert_raw_incoterm",
                       "next_task_id": "load_raw_incoterm.begin_insert_raw_incoterm"
            },
        )

        s3_to_snowflake_raw_incoterm_task = PythonOperator(
            task_id="s3_to_snowflake_raw_incoterm",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_incoterm.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_incoterm_task = PythonOperator(
            task_id="insert_log_raw_incoterm",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_incoterm.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_incoterm_table = PythonOperator(
            task_id="insert_incoterm",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_incoterm.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_incoterm_task >>
            check_new_files_found_raw_incoterm_task >>
            [begin_insert_raw_incoterm_task, skip_insert_raw_incoterm_task]
        )

        (
                begin_insert_raw_incoterm_task >>
                s3_to_snowflake_raw_incoterm_task >>
                insert_log_raw_incoterm_task >>
                append_to_incoterm_table >>
                end_insert_raw_incoterm_task >>
                end_raw_incoterm_task
        )

        skip_insert_raw_incoterm_task >> end_raw_incoterm_task

    #  --- Load raw_item ---

    with TaskGroup(group_id='load_raw_item') as tg_raw_item:
        list_s3_files_raw_item_task = PythonOperator(
            task_id="list_s3_item",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_item.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_item_task = DummyOperator(task_id="begin_insert_raw_item")
        skip_insert_raw_item_task = DummyOperator(task_id="skip_insert_raw_item")
        end_insert_raw_item_task = DummyOperator(task_id="end_insert_raw_item")
        end_raw_item_task = DummyOperator(
            task_id="end_raw_item",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_item_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_item',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_item.yaml",
                       "skip_task_id": "load_raw_item.skip_insert_raw_item",
                       "next_task_id": "load_raw_item.begin_insert_raw_item"
            },
        )

        s3_to_snowflake_raw_item_task = PythonOperator(
            task_id="s3_to_snowflake_raw_item",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_item.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_item_task = PythonOperator(
            task_id="insert_log_raw_item",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_item.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_item_table = PythonOperator(
            task_id="insert_item",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_item.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_item_task >>
            check_new_files_found_raw_item_task >>
            [begin_insert_raw_item_task, skip_insert_raw_item_task]
        )

        (
                begin_insert_raw_item_task >>
                s3_to_snowflake_raw_item_task >>
                insert_log_raw_item_task >>
                append_to_item_table >>
                end_insert_raw_item_task >>
                end_raw_item_task
        )

        skip_insert_raw_item_task >> end_raw_item_task

    #  --- Load raw_location ---

    with TaskGroup(group_id='load_raw_location') as tg_raw_location:
        list_s3_files_raw_location_task = PythonOperator(
            task_id="list_s3_location",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_location.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_location_task = DummyOperator(task_id="begin_insert_raw_location")
        skip_insert_raw_location_task = DummyOperator(task_id="skip_insert_raw_location")
        end_insert_raw_location_task = DummyOperator(task_id="end_insert_raw_location")
        end_raw_location_task = DummyOperator(
            task_id="end_raw_location",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_location_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_location',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_location.yaml",
                       "skip_task_id": "load_raw_location.skip_insert_raw_location",
                       "next_task_id": "load_raw_location.begin_insert_raw_location"
            },
        )

        s3_to_snowflake_raw_location_task = PythonOperator(
            task_id="s3_to_snowflake_raw_location",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_location.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_location_task = PythonOperator(
            task_id="insert_log_raw_location",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_location.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_location_table = PythonOperator(
            task_id="insert_location",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_location.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_location_task >>
            check_new_files_found_raw_location_task >>
            [begin_insert_raw_location_task, skip_insert_raw_location_task]
        )

        (
                begin_insert_raw_location_task >>
                s3_to_snowflake_raw_location_task >>
                insert_log_raw_location_task >>
                append_to_location_table >>
                end_insert_raw_location_task >>
                end_raw_location_task
        )

        skip_insert_raw_location_task >> end_raw_location_task

    #  --- Load raw_locationmainaddress ---

    with TaskGroup(group_id='load_raw_locationmainaddress') as tg_raw_locationmainaddress:
        list_s3_files_raw_locationmainaddress_task = PythonOperator(
            task_id="list_s3_locationmainaddress",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_locationmainaddress.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_locationmainaddress_task = DummyOperator(task_id="begin_insert_raw_locationmainaddress")
        skip_insert_raw_locationmainaddress_task = DummyOperator(task_id="skip_insert_raw_locationmainaddress")
        end_insert_raw_locationmainaddress_task = DummyOperator(task_id="end_insert_raw_locationmainaddress")
        end_raw_locationmainaddress_task = DummyOperator(
            task_id="end_raw_locationmainaddress",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_locationmainaddress_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_locationmainaddress',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_locationmainaddress.yaml",
                       "skip_task_id": "load_raw_locationmainaddress.skip_insert_raw_locationmainaddress",
                       "next_task_id": "load_raw_locationmainaddress.begin_insert_raw_locationmainaddress"
            },
        )

        s3_to_snowflake_raw_locationmainaddress_task = PythonOperator(
            task_id="s3_to_snowflake_raw_locationmainaddress",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_locationmainaddress.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_locationmainaddress_task = PythonOperator(
            task_id="insert_log_raw_locationmainaddress",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_locationmainaddress.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_locationmainaddress_table = PythonOperator(
            task_id="insert_locationmainaddress",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_locationmainaddress.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_locationmainaddress_task >>
            check_new_files_found_raw_locationmainaddress_task >>
            [begin_insert_raw_locationmainaddress_task, skip_insert_raw_locationmainaddress_task]
        )

        (
                begin_insert_raw_locationmainaddress_task >>
                s3_to_snowflake_raw_locationmainaddress_task >>
                insert_log_raw_locationmainaddress_task >>
                append_to_locationmainaddress_table >>
                end_insert_raw_locationmainaddress_task >>
                end_raw_locationmainaddress_task
        )

        skip_insert_raw_locationmainaddress_task >> end_raw_locationmainaddress_task

    #  --- Load raw_locationtype ---

    with TaskGroup(group_id='load_raw_locationtype') as tg_raw_locationtype:
        list_s3_files_raw_locationtype_task = PythonOperator(
            task_id="list_s3_locationtype",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_locationtype.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_locationtype_task = DummyOperator(task_id="begin_insert_raw_locationtype")
        skip_insert_raw_locationtype_task = DummyOperator(task_id="skip_insert_raw_locationtype")
        end_insert_raw_locationtype_task = DummyOperator(task_id="end_insert_raw_locationtype")
        end_raw_locationtype_task = DummyOperator(
            task_id="end_raw_locationtype",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_locationtype_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_locationtype',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_locationtype.yaml",
                       "skip_task_id": "load_raw_locationtype.skip_insert_raw_locationtype",
                       "next_task_id": "load_raw_locationtype.begin_insert_raw_locationtype"
            },
        )

        s3_to_snowflake_raw_locationtype_task = PythonOperator(
            task_id="s3_to_snowflake_raw_locationtype",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_locationtype.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_locationtype_task = PythonOperator(
            task_id="insert_log_raw_locationtype",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_locationtype.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_locationtype_table = PythonOperator(
            task_id="insert_locationtype",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_locationtype.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_locationtype_task >>
            check_new_files_found_raw_locationtype_task >>
            [begin_insert_raw_locationtype_task, skip_insert_raw_locationtype_task]
        )

        (
                begin_insert_raw_locationtype_task >>
                s3_to_snowflake_raw_locationtype_task >>
                insert_log_raw_locationtype_task >>
                append_to_locationtype_table >>
                end_insert_raw_locationtype_task >>
                end_raw_locationtype_task
        )

        skip_insert_raw_locationtype_task >> end_raw_locationtype_task

    #  --- Load raw_oa_columns ---

    with TaskGroup(group_id='load_raw_oa_columns') as tg_raw_oa_columns:
        list_s3_files_raw_oa_columns_task = PythonOperator(
            task_id="list_s3_oa_columns",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_oa_columns.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_oa_columns_task = DummyOperator(task_id="begin_insert_raw_oa_columns")
        skip_insert_raw_oa_columns_task = DummyOperator(task_id="skip_insert_raw_oa_columns")
        end_insert_raw_oa_columns_task = DummyOperator(task_id="end_insert_raw_oa_columns")
        end_raw_oa_columns_task = DummyOperator(
            task_id="end_raw_oa_columns",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_oa_columns_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_oa_columns',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_oa_columns.yaml",
                       "skip_task_id": "load_raw_oa_columns.skip_insert_raw_oa_columns",
                       "next_task_id": "load_raw_oa_columns.begin_insert_raw_oa_columns"
            },
        )

        s3_to_snowflake_raw_oa_columns_task = PythonOperator(
            task_id="s3_to_snowflake_raw_oa_columns",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_oa_columns.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_oa_columns_task = PythonOperator(
            task_id="insert_log_raw_oa_columns",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_oa_columns.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_oa_columns_table = PythonOperator(
            task_id="insert_oa_columns",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_oa_columns.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_oa_columns_task >>
            check_new_files_found_raw_oa_columns_task >>
            [begin_insert_raw_oa_columns_task, skip_insert_raw_oa_columns_task]
        )

        (
                begin_insert_raw_oa_columns_task >>
                s3_to_snowflake_raw_oa_columns_task >>
                insert_log_raw_oa_columns_task >>
                append_to_oa_columns_table >>
                end_insert_raw_oa_columns_task >>
                end_raw_oa_columns_task
        )

        skip_insert_raw_oa_columns_task >> end_raw_oa_columns_task

    #  --- Load raw_subsidiary ---

    with TaskGroup(group_id='load_raw_subsidiary') as tg_raw_subsidiary:
        list_s3_files_raw_subsidiary_task = PythonOperator(
            task_id="list_s3_subsidiary",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_subsidiary.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_subsidiary_task = DummyOperator(task_id="begin_insert_raw_subsidiary")
        skip_insert_raw_subsidiary_task = DummyOperator(task_id="skip_insert_raw_subsidiary")
        end_insert_raw_subsidiary_task = DummyOperator(task_id="end_insert_raw_subsidiary")
        end_raw_subsidiary_task = DummyOperator(
            task_id="end_raw_subsidiary",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_subsidiary_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_subsidiary',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_subsidiary.yaml",
                       "skip_task_id": "load_raw_subsidiary.skip_insert_raw_subsidiary",
                       "next_task_id": "load_raw_subsidiary.begin_insert_raw_subsidiary"
            },
        )

        s3_to_snowflake_raw_subsidiary_task = PythonOperator(
            task_id="s3_to_snowflake_raw_subsidiary",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_subsidiary.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_subsidiary_task = PythonOperator(
            task_id="insert_log_raw_subsidiary",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_subsidiary.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_subsidiary_table = PythonOperator(
            task_id="insert_subsidiary",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_subsidiary.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_subsidiary_task >>
            check_new_files_found_raw_subsidiary_task >>
            [begin_insert_raw_subsidiary_task, skip_insert_raw_subsidiary_task]
        )

        (
                begin_insert_raw_subsidiary_task >>
                s3_to_snowflake_raw_subsidiary_task >>
                insert_log_raw_subsidiary_task >>
                append_to_subsidiary_table >>
                end_insert_raw_subsidiary_task >>
                end_raw_subsidiary_task
        )

        skip_insert_raw_subsidiary_task >> end_raw_subsidiary_task

    #  --- Load raw_systemnote ---

    with TaskGroup(group_id='load_raw_systemnote') as tg_raw_systemnote:
        list_s3_files_raw_systemnote_task = PythonOperator(
            task_id="list_s3_systemnote",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_systemnote.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_systemnote_task = DummyOperator(task_id="begin_insert_raw_systemnote")
        skip_insert_raw_systemnote_task = DummyOperator(task_id="skip_insert_raw_systemnote")
        end_insert_raw_systemnote_task = DummyOperator(task_id="end_insert_raw_systemnote")
        end_raw_systemnote_task = DummyOperator(
            task_id="end_raw_systemnote",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_systemnote_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_systemnote',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_systemnote.yaml",
                       "skip_task_id": "load_raw_systemnote.skip_insert_raw_systemnote",
                       "next_task_id": "load_raw_systemnote.begin_insert_raw_systemnote"
            },
        )

        s3_to_snowflake_raw_systemnote_task = PythonOperator(
            task_id="s3_to_snowflake_raw_systemnote",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_systemnote.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_systemnote_task = PythonOperator(
            task_id="insert_log_raw_systemnote",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_systemnote.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_systemnote_table = PythonOperator(
            task_id="insert_systemnote",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_systemnote.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_systemnote_task >>
            check_new_files_found_raw_systemnote_task >>
            [begin_insert_raw_systemnote_task, skip_insert_raw_systemnote_task]
        )

        (
                begin_insert_raw_systemnote_task >>
                s3_to_snowflake_raw_systemnote_task >>
                insert_log_raw_systemnote_task >>
                append_to_systemnote_table >>
                end_insert_raw_systemnote_task >>
                end_raw_systemnote_task
        )

        skip_insert_raw_systemnote_task >> end_raw_systemnote_task

    #  --- Load raw_term ---

    with TaskGroup(group_id='load_raw_term') as tg_raw_term:
        list_s3_files_raw_term_task = PythonOperator(
            task_id="list_s3_term",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_term.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_term_task = DummyOperator(task_id="begin_insert_raw_term")
        skip_insert_raw_term_task = DummyOperator(task_id="skip_insert_raw_term")
        end_insert_raw_term_task = DummyOperator(task_id="end_insert_raw_term")
        end_raw_term_task = DummyOperator(
            task_id="end_raw_term",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_term_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_term',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_term.yaml",
                       "skip_task_id": "load_raw_term.skip_insert_raw_term",
                       "next_task_id": "load_raw_term.begin_insert_raw_term"
            },
        )

        s3_to_snowflake_raw_term_task = PythonOperator(
            task_id="s3_to_snowflake_raw_term",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_term.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_term_task = PythonOperator(
            task_id="insert_log_raw_term",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_term.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_term_table = PythonOperator(
            task_id="insert_term",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_term.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_term_task >>
            check_new_files_found_raw_term_task >>
            [begin_insert_raw_term_task, skip_insert_raw_term_task]
        )

        (
                begin_insert_raw_term_task >>
                s3_to_snowflake_raw_term_task >>
                insert_log_raw_term_task >>
                append_to_term_table >>
                end_insert_raw_term_task >>
                end_raw_term_task
        )

        skip_insert_raw_term_task >> end_raw_term_task

    #  --- Load raw_transaction ---

    # with TaskGroup(group_id='load_raw_transaction') as tg_raw_transaction:
    #     list_s3_files_raw_transaction_task = PythonOperator(
    #         task_id="list_s3_transaction",
    #         python_callable=tls3.list_s3_modified_files,
    #         op_kwargs={
    #             "args_file": "netsuite_ingestion/list_s3_transaction.yaml",
    #             "wf_params": WF_PARAMS_EXPR},
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #
    #     )
    #
    #     # Need a begin dummy operator for branching
    #     begin_insert_raw_transaction_task = DummyOperator(task_id="begin_insert_raw_transaction")
    #     skip_insert_raw_transaction_task = DummyOperator(task_id="skip_insert_raw_transaction")
    #     end_insert_raw_transaction_task = DummyOperator(task_id="end_insert_raw_transaction")
    #     end_raw_transaction_task = DummyOperator(
    #         task_id="end_raw_transaction",
    #         trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    #     )
    #
    #     check_new_files_found_raw_transaction_task = BranchPythonOperator(
    #         task_id='check_new_files_found_raw_transaction',
    #         python_callable=check_for_new_files,
    #         op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_transaction.yaml",
    #                    "skip_task_id": "load_raw_transaction.skip_insert_raw_transaction",
    #                    "next_task_id": "load_raw_transaction.begin_insert_raw_transaction"
    #         },
    #     )
    #
    #     s3_to_snowflake_raw_transaction_task = PythonOperator(
    #         task_id="s3_to_snowflake_raw_transaction",
    #         python_callable=load_obj.s3_to_snowflake_load,
    #         op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_transaction.yaml"},
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     insert_log_raw_transaction_task = PythonOperator(
    #         task_id="insert_log_raw_transaction",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={"connection":"Snowflake",
    #                    "sql_file": "netsuite_ingestion/insert_log_transaction.sql",
    #                    "wf_params": WF_PARAMS_EXPR
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     append_to_transaction_table = PythonOperator(
    #         task_id="insert_transaction",
    #         python_callable=run_query_file,
    #         op_kwargs={"connection":"Snowflake",
    #                    "sql_file": "netsuite_ingestion/insert_transaction.sql",
    #                    "wf_params": WF_PARAMS_EXPR
    #         },
    #         provide_context=True,
    #         on_failure_callback=alerts.send_failure_alert,
    #     )
    #
    #     (
    #         list_s3_files_raw_transaction_task >>
    #         check_new_files_found_raw_transaction_task >>
    #         [begin_insert_raw_transaction_task, skip_insert_raw_transaction_task]
    #     )
    #
    #     (
    #             begin_insert_raw_transaction_task >>
    #             s3_to_snowflake_raw_transaction_task >>
    #             insert_log_raw_transaction_task >>
    #             append_to_transaction_table >>
    #             end_insert_raw_transaction_task >>
    #             end_raw_transaction_task
    #     )
    #
    #     skip_insert_raw_transaction_task >> end_raw_transaction_task

    #  --- Load raw_transactionline ---

    with TaskGroup(group_id='load_raw_transactionline') as tg_raw_transactionline:
        list_s3_files_raw_transactionline_task = PythonOperator(
            task_id="list_s3_transactionline",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_transactionline.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_transactionline_task = DummyOperator(task_id="begin_insert_raw_transactionline")
        skip_insert_raw_transactionline_task = DummyOperator(task_id="skip_insert_raw_transactionline")
        end_insert_raw_transactionline_task = DummyOperator(task_id="end_insert_raw_transactionline")
        end_raw_transactionline_task = DummyOperator(
            task_id="end_raw_transactionline",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_transactionline_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_transactionline',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_transactionline.yaml",
                       "skip_task_id": "load_raw_transactionline.skip_insert_raw_transactionline",
                       "next_task_id": "load_raw_transactionline.begin_insert_raw_transactionline"
            },
        )

        s3_to_snowflake_raw_transactionline_task = PythonOperator(
            task_id="s3_to_snowflake_raw_transactionline",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_transactionline.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_transactionline_task = PythonOperator(
            task_id="insert_log_raw_transactionline",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_transactionline.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_transactionline_table = PythonOperator(
            task_id="insert_transactionline",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_transactionline.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_transactionline_task >>
            check_new_files_found_raw_transactionline_task >>
            [begin_insert_raw_transactionline_task, skip_insert_raw_transactionline_task]
        )

        (
                begin_insert_raw_transactionline_task >>
                s3_to_snowflake_raw_transactionline_task >>
                insert_log_raw_transactionline_task >>
                append_to_transactionline_table >>
                end_insert_raw_transactionline_task >>
                end_raw_transactionline_task
        )

        skip_insert_raw_transactionline_task >> end_raw_transactionline_task

    #  --- Load raw_vendor ---

    with TaskGroup(group_id='load_raw_vendor') as tg_raw_vendor:
        list_s3_files_raw_vendor_task = PythonOperator(
            task_id="list_s3_vendor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_vendor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_vendor_task = DummyOperator(task_id="begin_insert_raw_vendor")
        skip_insert_raw_vendor_task = DummyOperator(task_id="skip_insert_raw_vendor")
        end_insert_raw_vendor_task = DummyOperator(task_id="end_insert_raw_vendor")
        end_raw_vendor_task = DummyOperator(
            task_id="end_raw_vendor",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_vendor_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_vendor',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_vendor.yaml",
                       "skip_task_id": "load_raw_vendor.skip_insert_raw_vendor",
                       "next_task_id": "load_raw_vendor.begin_insert_raw_vendor"
            },
        )

        s3_to_snowflake_raw_vendor_task = PythonOperator(
            task_id="s3_to_snowflake_raw_vendor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_vendor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_vendor_task = PythonOperator(
            task_id="insert_log_raw_vendor",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_vendor.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_vendor_table = PythonOperator(
            task_id="insert_vendor",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_vendor.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_vendor_task >>
            check_new_files_found_raw_vendor_task >>
            [begin_insert_raw_vendor_task, skip_insert_raw_vendor_task]
        )

        (
                begin_insert_raw_vendor_task >>
                s3_to_snowflake_raw_vendor_task >>
                insert_log_raw_vendor_task >>
                append_to_vendor_table >>
                end_insert_raw_vendor_task >>
                end_raw_vendor_task
        )

        skip_insert_raw_vendor_task >> end_raw_vendor_task

    #  --- Load raw_vendorcategory ---

    with TaskGroup(group_id='load_raw_vendorcategory') as tg_raw_vendorcategory:
        list_s3_files_raw_vendorcategory_task = PythonOperator(
            task_id="list_s3_vendorcategory",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_vendorcategory.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_raw_vendorcategory_task = DummyOperator(task_id="begin_insert_raw_vendorcategory")
        skip_insert_raw_vendorcategory_task = DummyOperator(task_id="skip_insert_raw_vendorcategory")
        end_insert_raw_vendorcategory_task = DummyOperator(task_id="end_insert_raw_vendorcategory")
        end_raw_vendorcategory_task = DummyOperator(
            task_id="end_raw_vendorcategory",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_raw_vendorcategory_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_vendorcategory',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_vendorcategory.yaml",
                       "skip_task_id": "load_raw_vendorcategory.skip_insert_raw_vendorcategory",
                       "next_task_id": "load_raw_vendorcategory.begin_insert_raw_vendorcategory"
            },
        )

        s3_to_snowflake_raw_vendorcategory_task = PythonOperator(
            task_id="s3_to_snowflake_raw_vendorcategory",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_vendorcategory.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_raw_vendorcategory_task = PythonOperator(
            task_id="insert_log_raw_vendorcategory",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_vendorcategory.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_vendorcategory_table = PythonOperator(
            task_id="insert_vendorcategory",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_vendorcategory.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_vendorcategory_task >>
            check_new_files_found_raw_vendorcategory_task >>
            [begin_insert_raw_vendorcategory_task, skip_insert_raw_vendorcategory_task]
        )

        (
                begin_insert_raw_vendorcategory_task >>
                s3_to_snowflake_raw_vendorcategory_task >>
                insert_log_raw_vendorcategory_task >>
                append_to_vendorcategory_table >>
                end_insert_raw_vendorcategory_task >>
                end_raw_vendorcategory_task
        )

        skip_insert_raw_vendorcategory_task >> end_raw_vendorcategory_task



    # ---- Main branch ----
    # Set up initial dependencies
    begin >> get_workflow_parameters

    # All task groups run in parallel after get_workflow_parameters
    task_groups = [
        # tg_raw_account,
        tg_raw_accountingbook,
        # tg_raw_accountingperiod,
        # tg_raw_approvalstatus,
        tg_raw_bom,
        tg_raw_bomrevision,
        tg_raw_classification,
        tg_raw_currency,
        tg_raw_currencyrate,
        # tg_raw_customer,
        tg_raw_customlist1702,
        tg_raw_customlist_bd_itemmoqgroup,
        tg_raw_customlist_bd_pomoqgroup,
        tg_raw_customlist_brd_gp,
        tg_raw_customlist_brnd_packaging_type,
        tg_raw_customlist_consumerunitbarcode_type,
        tg_raw_customlist_jb_jj_84aj_pdstatus,
        tg_raw_customlist_jj_84_aj_pol,
        tg_raw_customlist_jj_asin_relationship,
        tg_raw_customlist_jj_brandcategorylist,
        tg_raw_customlist_jj_brandclusterlist,
        tg_raw_customlist_jj_brandgroupinglist,
        tg_raw_customlist_jj_brandstatuslist,
        tg_raw_customlist_jj_fba_storage_category_,
        tg_raw_customlist_jj_inventory_status,
        tg_raw_customlist_jj_sc_outbound_type,
        tg_raw_customlist_jj_sc_transportation_mode,
        tg_raw_customlist_jj_sc_work_order_type,
        tg_raw_customlist_nbsabr_integritystatus,
        tg_raw_customlist_nbsabr_recordtype,
        tg_raw_customlist_nbsabr_status,
        tg_raw_customlist_outercasebarcode_type,
        tg_raw_customlist_product_type,
        tg_raw_customlist_scm_lc_cost_alloc_method,
        tg_raw_customlist_selleraccountlist,
        tg_raw_customlistjj_interg_ptnr,
        tg_raw_customrecord_cseg_geography,
        tg_raw_customrecord_cseg_npdtype,
        tg_raw_customrecord_cseg_projectcode,
        tg_raw_customrecord_cseg_saleschannel,
        tg_raw_customrecord_jj_item_alias_details,
        tg_raw_customrecord_jj_vendor_price,
        tg_raw_customrecord_nbsabr_accountsetup,
        tg_raw_customrecord_nbsabr_bankstatement,
        tg_raw_customrecord_nbsabr_bankstatementline,
        tg_raw_customrecord_nbsabr_reconciliationstate,
        tg_raw_customrecord_nbsabr_statementhistory,
        tg_raw_customrecord_nbsabr_trntypedefn,
        tg_raw_customrecord_scm_lc_mapping,
        tg_raw_customrecord_scm_lc_profile,
        tg_raw_customrecord_scm_lc_profile_detail,
        tg_raw_deletedrecord,
        tg_raw_department,
        tg_raw_employee,
        # tg_raw_entity,
        tg_raw_entityaddress,
        tg_raw_incoterm,
        tg_raw_item,
        tg_raw_location,
        tg_raw_locationmainaddress,
        tg_raw_locationtype,
        tg_raw_oa_columns,
        tg_raw_subsidiary,
        tg_raw_systemnote,
        tg_raw_term,
        # tg_raw_transaction,
        tg_raw_transactionline,
        tg_raw_vendor,
        tg_raw_vendorcategory
    ]
    
    # Connect all task groups to run in parallel
    get_workflow_parameters >> task_groups
    task_groups >> update_workflow_parameters >> end

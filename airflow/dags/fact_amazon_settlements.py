import logging
from datetime import datetime

from airflow import DAG
# noinspection PyDeprecation
from airflow.models import Variable
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from db_connectors.sf_connector import Snowflake
from helpers.helper import get_absolute_sql_file_path, read_and_parse_sql_file
from tasklib.loader import check_for_new_files
from tasklib import alerts

BUILD_NUM = '09'
DAG_ID = 'fact_amazon_settlements'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily')
GLUE_DIR = DAG_ID
RELEASE_DEF = '1'
SQL_DIR = DAG_ID
WF_PARAMS_EXPR = '{{ ti.xcom_pull(task_ids="get_workflow_parameters") }}'

log = logging.getLogger(__name__)


def check_historical_diff(wf_params:str, **context)->None:
    sql_file = f'{SQL_DIR}/check_historical_diff.sql'
    sql_file_abs_path = get_absolute_sql_file_path(sql_file)
    query = read_and_parse_sql_file(sql_file_abs_path, wf_params)

    sn_obj = Snowflake()
    result, _ = sn_obj.get_data(query)
    log.info(f'result: {result}')
    if result[0][0]:
        return 'skip_insert_historical_incremental_data'
    return 'begin_insert_historical_incremental_data'


with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2022, 11, 28),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'FACT_AMAZON_SETTLEMENTS',
            'author': 'harshad'},
    tags=['Harshad']
) as dag:

    begin_task = DummyOperator(task_id='begin', depends_on_past=True, wait_for_downstream=True)

    get_workflow_params = PythonOperator(
        task_id='get_workflow_parameters',
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            'workflow_name': '{{ params.get("workflow_name") }}'
        },
    )

    list_s3_modified_files_task = PythonOperator(
        task_id='list_s3_modified_files',
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={
            'args_file': f'{GLUE_DIR}/s3_list_folders.yaml',
            'wf_params': WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_goessor_task = PythonOperator(
        task_id='list_s3_modified_files_goessor',
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={
            'args_file': f'{GLUE_DIR}/s3_list_folders_essor.yaml',
            'wf_params': WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    check_new_files_found_task = BranchPythonOperator(
        task_id='check_new_files_found',
        python_callable=check_for_new_files,
        op_kwargs={
            'args_file': f'{GLUE_DIR}/s3_to_snowflake.yaml',
            'skip_task_id': 'skip_transfer_s3_data',
            'next_task_id': 'begin_transfer_s3_data',
        },
    )

    check_new_files_found_goessor_task = BranchPythonOperator(
        task_id='check_new_files_found_goessor',
        python_callable=check_for_new_files,
        op_kwargs={
            'args_file': f'{GLUE_DIR}/s3_to_snowflake_essor.yaml',
            'skip_task_id': 'skip_transfer_s3_goessor_data',
            'next_task_id': 'begin_transfer_s3_goessor_data',
        }
    )

    ########## BEGIN INCREMENTAL TASKS ##########

    # Need a begin dummy operator for branching
    begin_transfer_s3_data_task = DummyOperator(task_id='begin_transfer_s3_data')
    skip_transfer_s3_data_task = DummyOperator(task_id='skip_transfer_s3_data')
    end_transfer_s3_data_task = DummyOperator(task_id='end_transfer_s3_data')
    
    with TaskGroup(group_id=f'transfer_s3_data') as transfer_s3_data_task:
        from tasklib.loader import S3ToSnowflake
        load_obj = S3ToSnowflake()
        transfer_s3_to_snowflake_task = PythonOperator(
            task_id='transfer_s3_to_snowflake',
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                'args_file': f'{GLUE_DIR}/s3_to_sf_raw_amazon_settlements.yaml'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_tests_raw=PythonOperator(
            task_id='task_run_dq_tests_raw',
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amazon_settlements',
                'test_name': 'Primary fields are not null in Raw',
                'sql_query': '''
                    SELECT CASE WHEN COUNT(1) = COUNT(sellingpartnerid) 
                                 AND COUNT(1) = COUNT(settlement_id)
                                 AND COUNT(1) = COUNT(_DATON_BATCH_RUNTIME) 
                                THEN 0
                                ELSE 1
                    END AS "result"
                    FROM $raw_db.raw_amazon_settlements
                '''
                },
            on_failure_callback=alerts.send_failure_alert
        )

        insert_log_incremental_task = PythonOperator(
            task_id='insert_log_incremental',
            python_callable=tlsql.run_query_file,
            op_kwargs={
                'connection': 'Snowflake',
                'sql_file': f'{SQL_DIR}/log_amazon_settlements_incremental.sql',
                'wf_params': WF_PARAMS_EXPR
            },
            provide_context=True
        )

        (
            transfer_s3_to_snowflake_task >>
            run_dq_tests_raw >>
            insert_log_incremental_task
        )

    begin_transfer_s3_goessor_data_task = DummyOperator(task_id='begin_transfer_s3_goessor_data')
    skip_transfer_s3_goessor_data_task = DummyOperator(task_id='skip_transfer_s3_goessor_data')
    end_transfer_s3_goessor_data_task = DummyOperator(task_id='end_transfer_s3_goessor_data')

    with TaskGroup(group_id=f'transfer_s3_goessor_data') as transfer_s3_goessor_data_task:
        from tasklib.loader import S3ToSnowflake
        load_obj = S3ToSnowflake()
        transfer_s3_to_snowflake_task = PythonOperator(
            task_id='transfer_s3_to_snowflake_goessor',
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                'args_file': f'{GLUE_DIR}/s3_to_sf_raw_amazon_settlements_goessor.yaml'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_tests_raw=PythonOperator(
            task_id='task_run_dq_tests_raw_goessor',
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amazon_settlements',
                'test_name': 'Primary fields are not null in Raw',
                'sql_query': '''
                    SELECT CASE WHEN COUNT(1) = COUNT(sellingpartnerid) 
                                 AND COUNT(1) = COUNT(settlement_id)
                                 AND COUNT(1) = COUNT(_DATON_BATCH_RUNTIME) 
                                THEN 0
                                ELSE 1
                    END AS "result"
                    FROM $raw_db.raw_amazon_settlements
                '''
                },
            on_failure_callback=alerts.send_failure_alert
        )

        insert_log_incremental_task = PythonOperator(
            task_id='insert_log_incremental_goessor',
            python_callable=tlsql.run_query_file,
            op_kwargs={
                'connection': 'Snowflake',
                'sql_file': f'{SQL_DIR}/log_amazon_settlements_incremental.sql',
                'wf_params': WF_PARAMS_EXPR
            },
            provide_context=True
        )

        (
            transfer_s3_to_snowflake_task >>
            run_dq_tests_raw >>
            insert_log_incremental_task
        )

    with TaskGroup(group_id=f'insert_incremental_data') as insert_incremental_data_task:

        create_deduped_incremental_task = PythonOperator(
            task_id='create_deduped_incremental',
            python_callable=tlsql.run_query_file,
            op_kwargs={
                'connection': 'Snowflake',
                'sql_file': f'{SQL_DIR}/deduped_amazon_settlements_incremental.sql',
                'wf_params': WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        create_staging_incremental_task = PythonOperator(
            task_id='create_staging_incremental',
            python_callable=tlsql.run_query_file,
            op_kwargs={
                'connection': 'Snowflake',
                'sql_file': f'{SQL_DIR}/staging_amazon_settlements_incremental.sql',
                'wf_params': WF_PARAMS_EXPR
            },
            provide_context=True
        )

        run_dq_staging_task_deduped = PythonOperator(
            task_id='run_dq_deduped_vs_staging',
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_staging',
                'tb_name': '$curated_db.FACT_AMAZON_AD_KEYWORDS',
                'query_file': f'{SQL_DIR}/dq_check_deduped_vs_staging.yaml'
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_staging_task_staging = PythonOperator(
            task_id='run_dq_staging_vs_merged_staging',
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_staging',
                'tb_name': '$curated_db.FACT_AMAZON_AD_KEYWORDS',
                'query_file': f'{SQL_DIR}/dq_check_staging_vs_merged_staging.yaml'
            },
            on_failure_callback=alerts.send_failure_alert
        )

        insert_merged_staging_incremental_task = PythonOperator(
            task_id='insert_merged_staging_incremental',
            python_callable=tlsql.run_query_file,
            op_kwargs={
                'connection': 'Snowflake',
                'sql_file': f'{SQL_DIR}/merged_staging_amazon_settlements_incremental.sql',
                'wf_params': WF_PARAMS_EXPR
            },
            provide_context=True
        )

        insert_fact_incremental_task = PythonOperator(
            task_id='insert_fact_incremental',
            python_callable=tlsql.run_query_file,
            op_kwargs={
                'connection': 'Snowflake',
                'sql_file': f'{SQL_DIR}/fact_amazon_settlements_incremental.sql',
                'wf_params': WF_PARAMS_EXPR
            },
            provide_context=True
        )

        update_item_mapping_task = PythonOperator(
            task_id='update_item_mapping',
            python_callable=tlsql.run_query_file,
            op_kwargs={
                'connection': 'Snowflake',
                'sql_file': f'{SQL_DIR}/update_item_mapping.sql',
                'wf_params': WF_PARAMS_EXPR
            },
            provide_context=True
        )

        run_audit = PythonOperator(
            task_id='task_run_audit',
            python_callable=tla.run_audit,
            op_kwargs={
                        "table_name": "$curated_db.fact_amazon_settlements", 
                        "wf_params": WF_PARAMS_EXPR,
                        "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                        "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC' 
                    },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            create_deduped_incremental_task >>
            create_staging_incremental_task >>
            (run_dq_staging_task_deduped, run_dq_staging_task_staging) >>
            insert_merged_staging_incremental_task >>
            insert_fact_incremental_task >>
            update_item_mapping_task >>
            run_audit
        )


    ########## END INCREMENTAL TASKS ##########

    check_historical_diff_task = BranchPythonOperator(
        task_id='check_historical_diff',
        python_callable=check_historical_diff,
        op_kwargs={
            'wf_params': WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    ########## BEGIN HISTORICAL INCREMENTAL TASKS ##########

    # Need a begin dummy operator for branching
    begin_insert_historical_incremental_data_task = DummyOperator(task_id='begin_insert_historical_incremental_data')
    skip_insert_historical_incremental_data_task = DummyOperator(task_id='skip_insert_historical_incremental_data')
    end_insert_historical_incremental_data_task = DummyOperator(task_id='end_insert_historical_incremental_data')

    with TaskGroup(group_id=f'insert_historical_incremental_data') as insert_historical_incremental_data_task:
        create_staging_historical_incremental_task = PythonOperator(
            task_id='create_staging_historical_incremental',
            python_callable=tlsql.run_query_file,
            op_kwargs={
                'connection': 'Snowflake',
                'sql_file': f'{SQL_DIR}/staging_amazon_settlements_historical_incremental.sql',
                'wf_params': WF_PARAMS_EXPR
            },
            provide_context=True,
        )

        insert_merged_staging_historical_incremental_task = PythonOperator(
            task_id='insert_merged_staging_historical_incremental',
            python_callable=tlsql.run_query_file,
            op_kwargs={
                'connection': 'Snowflake',
                'sql_file': f'{SQL_DIR}/merged_staging_historical_incremental.sql',
                'wf_params': WF_PARAMS_EXPR
            },
            provide_context=True,
        )

        insert_fact_historical_incremental_task = PythonOperator(
            task_id='insert_fact_historical_incremental',
            python_callable=tlsql.run_query_file,
            op_kwargs={
                'connection': 'Snowflake',
                'sql_file': f'{SQL_DIR}/fact_amazon_settlements_historical_incremental.sql',
                'wf_params': WF_PARAMS_EXPR
            },
            provide_context=True,
        )

        create_historical_snapshot_task = PythonOperator(
            task_id='create_historical_snapshot',
            python_callable=tlsql.run_query_file,
            op_kwargs={
                'connection': 'Snowflake',
                'sql_file': f'{SQL_DIR}/amazon_settlements_historical_snapshot.sql',
                'wf_params': WF_PARAMS_EXPR
            },
            provide_context=True
        )

        (
            create_staging_historical_incremental_task >>
            insert_merged_staging_historical_incremental_task >>
            insert_fact_historical_incremental_task >>
            create_historical_snapshot_task
        )

    ########## END HISTORICAL INCREMENTAL TASKS ##########

    update_workflow_params_task = PythonOperator(
        task_id='update_workflow_parameters',
        python_callable=tlw.update_workflow_params,
        op_kwargs={'wf_params': WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    end_task = DummyOperator(task_id='end')

    # --- Assemble the dag ---
    (   begin_task >>
        get_workflow_params >>
        [list_s3_modified_files_task, list_s3_modified_files_goessor_task]
    )

    (
        list_s3_modified_files_task >>
        check_new_files_found_task >>
        [begin_transfer_s3_data_task, skip_transfer_s3_data_task]
    )
    
    begin_transfer_s3_data_task >> transfer_s3_data_task >> end_transfer_s3_data_task
    
    (
        list_s3_modified_files_goessor_task >>
        check_new_files_found_goessor_task >>
        [begin_transfer_s3_goessor_data_task, skip_transfer_s3_goessor_data_task]
    )

    begin_transfer_s3_goessor_data_task >> transfer_s3_goessor_data_task >> end_transfer_s3_goessor_data_task

    # Add a join task to merge the paths with trigger rule
    join_paths_task = DummyOperator(
        task_id='join_paths',
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS
    )

    # Connect both paths to the join task
    end_transfer_s3_data_task >> join_paths_task
    skip_transfer_s3_data_task >> join_paths_task
    end_transfer_s3_goessor_data_task >> join_paths_task
    skip_transfer_s3_goessor_data_task >> join_paths_task

    # Continue with the rest of the DAG
    (
        join_paths_task >>
        insert_incremental_data_task >>
        check_historical_diff_task >>
        [begin_insert_historical_incremental_data_task, skip_insert_historical_incremental_data_task]
    )

    begin_insert_historical_incremental_data_task >> insert_historical_incremental_data_task >> end_insert_historical_incremental_data_task
    
    (
        (skip_insert_historical_incremental_data_task, end_insert_historical_incremental_data_task) >>
        update_workflow_params_task >> end_task
    )



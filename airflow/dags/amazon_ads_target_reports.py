"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.utils.task_group import TaskGroup
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib.loader import check_for_new_files, S3ToSnowflake
from tasklib import alerts

BUILD_NUM = '1'
DAG_ID = 'amazon_ads_target_reports'
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)
load_obj = S3ToSnowflake()

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2021, 1, 1),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'AMAZON_ADS_TARGET_REPORTS',
            'author': 'abhishek'},
    tags=['Abhishek', 'Raptor', 'Goessor']
) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
        
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_params = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load sponsoredbrands_targetreport ---
    
    with TaskGroup(group_id='load_sponsoredbrands_targetreport') as tg_sponsoredbrands_targetreport:
        list_s3_files_sponsoredbrands_targetreport_task = PythonOperator(
            task_id="list_s3_files_sponsoredbrands_targetreport",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/list_s3_SponsoredBrands_TargetReport.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_sponsoredbrands_targetreport_task_goessor = PythonOperator(
            task_id="list_s3_files_sponsoredbrands_targetreport_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/list_s3_SponsoredBrands_TargetReport_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_sponsoredbrands_targetreport_task = DummyOperator(task_id="begin_insert_sponsoredbrands_targetreport")
        skip_insert_sponsoredbrands_targetreport_task = DummyOperator(task_id="skip_insert_sponsoredbrands_targetreport")
        end_insert_sponsoredbrands_targetreport_task = DummyOperator(task_id="end_insert_sponsoredbrands_targetreport")
        
        s3_to_snowflake_sponsoredbrands_targetreport_task = PythonOperator(
            task_id="s3_to_snowflake_sponsoredbrands_targetreport",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_sf_raw_sponsoredbrands_targetreport.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_sponsoredbrands_targetreport_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_sponsoredbrands_targetreport_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_sf_raw_sponsoredbrands_targetreport_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_sponsoredbrands_targetreport_task = BranchPythonOperator(
            task_id='check_new_files_found_sponsoredbrands_targetreport',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_snowflake_SponsoredBrands_TargetReport.yaml",
                       "args_file_second": "amazon_ads_target_reports/s3_to_snowflake_SponsoredBrands_TargetReport_goessor.yaml",
                       "skip_task_id": "load_sponsoredbrands_targetreport.skip_insert_sponsoredbrands_targetreport",
                       "next_task_id": "load_sponsoredbrands_targetreport.begin_insert_sponsoredbrands_targetreport"
            },
        )

        insert_log_sponsoredbrands_targetreport_task = PythonOperator(
            task_id="insert_log_sponsoredbrands_targetreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/insert_log_SponsoredBrands_TargetReport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_sponsoredbrands_targetreport_task = PythonOperator(
            task_id="dedupe_sponsoredbrands_targetreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/dedupe_SponsoredBrands_TargetReport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_sponsoredbrands_targetreport_task = PythonOperator(
            task_id="merge_stage_sponsoredbrands_targetreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/merge_stage_SponsoredBrands_TargetReport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_sponsoredbrands_targetreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_sponsoredbrands_targetreport', 
                    field_list=['adgroupid', 'campaignid', 'accountid', 'reportdate'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sponsoredbrands_targetreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_sponsoredbrands_targetreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_sponsoredbrands_targetreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_sponsoredbrands_targetreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sponsoredbrands_targetreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_sponsoredbrands_targetreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_sponsoredbrands_targetreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_sponsoredbrands_targetreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_sponsoredbrands_targetreport_task = PythonOperator(
            task_id="run_audit_sponsoredbrands_targetreport",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_sponsoredbrands_targetreport", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_sponsoredbrands_targetreport_task, list_s3_files_sponsoredbrands_targetreport_task_goessor] >>
            check_new_files_found_sponsoredbrands_targetreport_task >>
            [begin_insert_sponsoredbrands_targetreport_task, skip_insert_sponsoredbrands_targetreport_task]
        )

        (
            begin_insert_sponsoredbrands_targetreport_task >> 
            s3_to_snowflake_sponsoredbrands_targetreport_task >>
            s3_to_snowflake_sponsoredbrands_targetreport_task_goessor >>
            insert_log_sponsoredbrands_targetreport_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_sponsoredbrands_targetreport_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_sponsoredbrands_targetreport_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_sponsoredbrands_targetreport_task >>
            end_insert_sponsoredbrands_targetreport_task
        )

    #  --- Load sponsoredbrands_targetvideoreport ---
    
    with TaskGroup(group_id='load_sponsoredbrands_targetvideoreport') as tg_sponsoredbrands_targetvideoreport:
        list_s3_files_sponsoredbrands_targetvideoreport_task = PythonOperator(
            task_id="list_s3_files_sponsoredbrands_targetvideoreport",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/list_s3_SponsoredBrands_TargetVideoReport.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_sponsoredbrands_targetvideoreport_task_goessor = PythonOperator(
            task_id="list_s3_files_sponsoredbrands_targetvideoreport_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/list_s3_SponsoredBrands_TargetVideoReport_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_sponsoredbrands_targetvideoreport_task = DummyOperator(task_id="begin_insert_sponsoredbrands_targetvideoreport")
        skip_insert_sponsoredbrands_targetvideoreport_task = DummyOperator(task_id="skip_insert_sponsoredbrands_targetvideoreport")
        end_insert_sponsoredbrands_targetvideoreport_task = DummyOperator(task_id="end_insert_sponsoredbrands_targetvideoreport")
        
        s3_to_snowflake_sponsoredbrands_targetvideoreport_task = PythonOperator(
            task_id="s3_to_snowflake_sponsoredbrands_targetvideoreport",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_sf_raw_sponsoredbrands_targetvideoreport.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_sponsoredbrands_targetvideoreport_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_sponsoredbrands_targetvideoreport_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_sf_raw_sponsoredbrands_targetvideoreport_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_sponsoredbrands_targetvideoreport_task = BranchPythonOperator(
            task_id='check_new_files_found_sponsoredbrands_targetvideoreport',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_snowflake_SponsoredBrands_TargetVideoReport.yaml",
                       "args_file_second": "amazon_ads_target_reports/s3_to_snowflake_SponsoredBrands_TargetVideoReport_goessor.yaml",
                       "skip_task_id": "load_sponsoredbrands_targetvideoreport.skip_insert_sponsoredbrands_targetvideoreport",
                       "next_task_id": "load_sponsoredbrands_targetvideoreport.begin_insert_sponsoredbrands_targetvideoreport"
            },
        )

        insert_log_sponsoredbrands_targetvideoreport_task = PythonOperator(
            task_id="insert_log_sponsoredbrands_targetvideoreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/insert_log_SponsoredBrands_TargetVideoReport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_sponsoredbrands_targetvideoreport_task = PythonOperator(
            task_id="dedupe_sponsoredbrands_targetvideoreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/dedupe_SponsoredBrands_TargetVideoReport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_sponsoredbrands_targetvideoreport_task = PythonOperator(
            task_id="merge_stage_sponsoredbrands_targetvideoreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/merge_stage_SponsoredBrands_TargetVideoReport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_sponsoredbrands_targetvideoreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_sponsoredbrands_targetvideoreport', 
                    field_list=['adgroupid', 'campaignid', 'accountid', 'reportdate'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sponsoredbrands_targetvideoreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_sponsoredbrands_targetvideoreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_sponsoredbrands_targetvideoreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_sponsoredbrands_targetvideoreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sponsoredbrands_targetvideoreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_sponsoredbrands_targetvideoreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_sponsoredbrands_targetvideoreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_sponsoredbrands_targetvideoreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_sponsoredbrands_targetvideoreport_task = PythonOperator(
            task_id="run_audit_sponsoredbrands_targetvideoreport",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_sponsoredbrands_targetvideoreport", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_sponsoredbrands_targetvideoreport_task, list_s3_files_sponsoredbrands_targetvideoreport_task_goessor] >>
            check_new_files_found_sponsoredbrands_targetvideoreport_task >>
            [begin_insert_sponsoredbrands_targetvideoreport_task, skip_insert_sponsoredbrands_targetvideoreport_task]
        )

        (
            begin_insert_sponsoredbrands_targetvideoreport_task >> 
            s3_to_snowflake_sponsoredbrands_targetvideoreport_task >>
            s3_to_snowflake_sponsoredbrands_targetvideoreport_task_goessor >>
            insert_log_sponsoredbrands_targetvideoreport_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_sponsoredbrands_targetvideoreport_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_sponsoredbrands_targetvideoreport_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_sponsoredbrands_targetvideoreport_task >>
            end_insert_sponsoredbrands_targetvideoreport_task
        )

    #  --- Load sponsoredbrands_targets ---
    
    with TaskGroup(group_id='load_sponsoredbrands_targets') as tg_sponsoredbrands_targets:
        list_s3_files_sponsoredbrands_targets_task = PythonOperator(
            task_id="list_s3_files_sponsoredbrands_targets",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/list_s3_SponsoredBrands_Targets.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_sponsoredbrands_targets_task_goessor = PythonOperator(
            task_id="list_s3_files_sponsoredbrands_targets_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/list_s3_SponsoredBrands_Targets_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_sponsoredbrands_targets_task = DummyOperator(task_id="begin_insert_sponsoredbrands_targets")
        skip_insert_sponsoredbrands_targets_task = DummyOperator(task_id="skip_insert_sponsoredbrands_targets")
        end_insert_sponsoredbrands_targets_task = DummyOperator(task_id="end_insert_sponsoredbrands_targets")
        
        s3_to_snowflake_sponsoredbrands_targets_task = PythonOperator(
            task_id="s3_to_snowflake_sponsoredbrands_targets",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_sf_raw_sponsoredbrands_targets.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_sponsoredbrands_targets_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_sponsoredbrands_targets_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_sf_raw_sponsoredbrands_targets_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_sponsoredbrands_targets_task = BranchPythonOperator(
            task_id='check_new_files_found_sponsoredbrands_targets',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_snowflake_SponsoredBrands_Targets.yaml",
                       "args_file_second": "amazon_ads_target_reports/s3_to_snowflake_SponsoredBrands_Targets_goessor.yaml",
                       "skip_task_id": "load_sponsoredbrands_targets.skip_insert_sponsoredbrands_targets",
                       "next_task_id": "load_sponsoredbrands_targets.begin_insert_sponsoredbrands_targets"
            },
        )

        insert_log_sponsoredbrands_targets_task = PythonOperator(
            task_id="insert_log_sponsoredbrands_targets",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/insert_log_SponsoredBrands_Targets.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_sponsoredbrands_targets_task = PythonOperator(
            task_id="dedupe_sponsoredbrands_targets",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/dedupe_SponsoredBrands_Targets.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_sponsoredbrands_targets_task = PythonOperator(
            task_id="merge_stage_sponsoredbrands_targets",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/merge_stage_SponsoredBrands_Targets.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_sponsoredbrands_targets',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_sponsoredbrands_targets', 
                    field_list=['adgroupid', 'campaignid', 'accountid', 'fetchdate'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sponsoredbrands_targets',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_sponsoredbrands_targets', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_sponsoredbrands_targets',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_sponsoredbrands_targets', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sponsoredbrands_targets',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_sponsoredbrands_targets', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_sponsoredbrands_targets',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_sponsoredbrands_targets', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_sponsoredbrands_targets_task = PythonOperator(
            task_id="run_audit_sponsoredbrands_targets",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_sponsoredbrands_targets", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_sponsoredbrands_targets_task, list_s3_files_sponsoredbrands_targets_task_goessor] >>
            check_new_files_found_sponsoredbrands_targets_task >>
            [begin_insert_sponsoredbrands_targets_task, skip_insert_sponsoredbrands_targets_task]
        )

        (
            begin_insert_sponsoredbrands_targets_task >> 
            s3_to_snowflake_sponsoredbrands_targets_task >>
            s3_to_snowflake_sponsoredbrands_targets_task_goessor >>
            insert_log_sponsoredbrands_targets_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_sponsoredbrands_targets_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_sponsoredbrands_targets_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_sponsoredbrands_targets_task >>
            end_insert_sponsoredbrands_targets_task
        )

    #  --- Load sponsoreddisplay_producttargetingreport ---
    
    with TaskGroup(group_id='load_sponsoreddisplay_producttargetingreport') as tg_sponsoreddisplay_producttargetingreport:
        list_s3_files_sponsoreddisplay_producttargetingreport_task = PythonOperator(
            task_id="list_s3_files_sponsoreddisplay_producttargetingreport",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/list_s3_SponsoredDisplay_ProductTargetingReport.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_sponsoreddisplay_producttargetingreport_task_goessor = PythonOperator(
            task_id="list_s3_files_sponsoreddisplay_producttargetingreport_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/list_s3_SponsoredDisplay_ProductTargetingReport_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_sponsoreddisplay_producttargetingreport_task = DummyOperator(task_id="begin_insert_sponsoreddisplay_producttargetingreport")
        skip_insert_sponsoreddisplay_producttargetingreport_task = DummyOperator(task_id="skip_insert_sponsoreddisplay_producttargetingreport")
        end_insert_sponsoreddisplay_producttargetingreport_task = DummyOperator(task_id="end_insert_sponsoreddisplay_producttargetingreport")
        
        s3_to_snowflake_sponsoreddisplay_producttargetingreport_task = PythonOperator(
            task_id="s3_to_snowflake_sponsoreddisplay_producttargetingreport",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_sf_raw_sponsoreddisplay_producttargetingreport.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_sponsoreddisplay_producttargetingreport_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_sponsoreddisplay_producttargetingreport_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/s3_to_sf_raw_sponsoreddisplay_producttargetingreport_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_sponsoreddisplay_producttargetingreport_task = BranchPythonOperator(
            task_id='check_new_files_found_sponsoreddisplay_producttargetingreport',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_snowflake_SponsoredDisplay_ProductTargetingReport.yaml",
                       "args_file_second": "amazon_ads_target_reports/s3_to_snowflake_SponsoredDisplay_ProductTargetingReport_goessor.yaml",
                       "skip_task_id": "load_sponsoreddisplay_producttargetingreport.skip_insert_sponsoreddisplay_producttargetingreport",
                       "next_task_id": "load_sponsoreddisplay_producttargetingreport.begin_insert_sponsoreddisplay_producttargetingreport"
            },
        )

        insert_log_sponsoreddisplay_producttargetingreport_task = PythonOperator(
            task_id="insert_log_sponsoreddisplay_producttargetingreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/insert_log_SponsoredDisplay_ProductTargetingReport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_sponsoreddisplay_producttargetingreport_task = PythonOperator(
            task_id="dedupe_sponsoreddisplay_producttargetingreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/dedupe_SponsoredDisplay_ProductTargetingReport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_sponsoreddisplay_producttargetingreport_task = PythonOperator(
            task_id="merge_stage_sponsoreddisplay_producttargetingreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/merge_stage_SponsoredDisplay_ProductTargetingReport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_sponsoreddisplay_producttargetingreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_sponsoreddisplay_producttargetingreport', 
                    field_list=['adgroupid', 'campaignid', 'accountid', 'reportdate'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sponsoreddisplay_producttargetingreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_sponsoreddisplay_producttargetingreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_sponsoreddisplay_producttargetingreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_sponsoreddisplay_producttargetingreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sponsoreddisplay_producttargetingreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_sponsoreddisplay_producttargetingreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_sponsoreddisplay_producttargetingreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_sponsoreddisplay_producttargetingreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_sponsoreddisplay_producttargetingreport_task = PythonOperator(
            task_id="run_audit_sponsoreddisplay_producttargetingreport",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_sponsoreddisplay_producttargetingreport", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_sponsoreddisplay_producttargetingreport_task, list_s3_files_sponsoreddisplay_producttargetingreport_task_goessor] >>
            check_new_files_found_sponsoreddisplay_producttargetingreport_task >>
            [begin_insert_sponsoreddisplay_producttargetingreport_task, skip_insert_sponsoreddisplay_producttargetingreport_task]
        )

        (
            begin_insert_sponsoreddisplay_producttargetingreport_task >>
            s3_to_snowflake_sponsoreddisplay_producttargetingreport_task >>
            s3_to_snowflake_sponsoreddisplay_producttargetingreport_task_goessor >>
            insert_log_sponsoreddisplay_producttargetingreport_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_sponsoreddisplay_producttargetingreport_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_sponsoreddisplay_producttargetingreport_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_sponsoreddisplay_producttargetingreport_task >>
            end_insert_sponsoreddisplay_producttargetingreport_task
        )

    #  --- Load sponsoreddisplay_targetingclause ---
    
    with TaskGroup(group_id='load_sponsoreddisplay_targetingclause') as tg_sponsoreddisplay_targetingclause:
        list_s3_files_sponsoreddisplay_targetingclause_task = PythonOperator(
            task_id="list_s3_files_sponsoreddisplay_targetingclause",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/list_s3_SponsoredDisplay_TargetingClause.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_sponsoreddisplay_targetingclause_task_goessor = PythonOperator(
            task_id="list_s3_files_sponsoreddisplay_targetingclause_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/list_s3_SponsoredDisplay_TargetingClause_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_sponsoreddisplay_targetingclause_task = DummyOperator(task_id="begin_insert_sponsoreddisplay_targetingclause")
        skip_insert_sponsoreddisplay_targetingclause_task = DummyOperator(task_id="skip_insert_sponsoreddisplay_targetingclause")
        end_insert_sponsoreddisplay_targetingclause_task = DummyOperator(task_id="end_insert_sponsoreddisplay_targetingclause")
        
        s3_to_snowflake_sponsoreddisplay_targetingclause_task = PythonOperator(
            task_id="s3_to_snowflake_sponsoreddisplay_targetingclause",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_sf_raw_sponsoreddisplay_targetingclause.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_sponsoreddisplay_targetingclause_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_sponsoreddisplay_targetingclause_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_sf_raw_sponsoreddisplay_targetingclause_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_sponsoreddisplay_targetingclause_task = BranchPythonOperator(
            task_id='check_new_files_found_sponsoreddisplay_targetingclause',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_snowflake_SponsoredDisplay_TargetingClause.yaml",
                       "args_file_second": "amazon_ads_target_reports/s3_to_snowflake_SponsoredDisplay_TargetingClause_goessor.yaml",
                       "skip_task_id": "load_sponsoreddisplay_targetingclause.skip_insert_sponsoreddisplay_targetingclause",
                       "next_task_id": "load_sponsoreddisplay_targetingclause.begin_insert_sponsoreddisplay_targetingclause"
            },
        )

        insert_log_sponsoreddisplay_targetingclause_task = PythonOperator(
            task_id="insert_log_sponsoreddisplay_targetingclause",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/insert_log_SponsoredDisplay_TargetingClause.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_sponsoreddisplay_targetingclause_task = PythonOperator(
            task_id="dedupe_sponsoreddisplay_targetingclause",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/dedupe_SponsoredDisplay_TargetingClause.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_sponsoreddisplay_targetingclause_task = PythonOperator(
            task_id="merge_stage_sponsoreddisplay_targetingclause",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/merge_stage_SponsoredDisplay_TargetingClause.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_sponsoreddisplay_targetingclause',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_sponsoreddisplay_targetingclause', 
                    field_list=['adgroupid', 'accountid', 'fetchdate'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sponsoreddisplay_targetingclause',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_sponsoreddisplay_targetingclause', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_sponsoreddisplay_targetingclause',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_sponsoreddisplay_targetingclause', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sponsoreddisplay_targetingclause',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_sponsoreddisplay_targetingclause', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_sponsoreddisplay_targetingclause',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_sponsoreddisplay_targetingclause', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_sponsoreddisplay_targetingclause_task = PythonOperator(
            task_id="run_audit_sponsoreddisplay_targetingclause",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_sponsoreddisplay_targetingclause", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_sponsoreddisplay_targetingclause_task, list_s3_files_sponsoreddisplay_targetingclause_task_goessor] >>
            check_new_files_found_sponsoreddisplay_targetingclause_task >>
            [begin_insert_sponsoreddisplay_targetingclause_task, skip_insert_sponsoreddisplay_targetingclause_task]
        )

        (
            begin_insert_sponsoreddisplay_targetingclause_task >> 
            s3_to_snowflake_sponsoreddisplay_targetingclause_task >>
            s3_to_snowflake_sponsoreddisplay_targetingclause_task_goessor >>
            insert_log_sponsoreddisplay_targetingclause_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_sponsoreddisplay_targetingclause_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_sponsoreddisplay_targetingclause_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_sponsoreddisplay_targetingclause_task >>
            end_insert_sponsoreddisplay_targetingclause_task
        )

    #  --- Load sponsoredproducts_producttargetingreport ---
    
    with TaskGroup(group_id='load_sponsoredproducts_producttargetingreport') as tg_sponsoredproducts_producttargetingreport:
        list_s3_files_sponsoredproducts_producttargetingreport_task = PythonOperator(
            task_id="list_s3_files_sponsoredproducts_producttargetingreport",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/list_s3_SponsoredProducts_ProductTargetingReport.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_sponsoredproducts_producttargetingreport_task_goessor = PythonOperator(
            task_id="list_s3_files_sponsoredproducts_producttargetingreport_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/list_s3_SponsoredProducts_ProductTargetingReport_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_sponsoredproducts_producttargetingreport_task = DummyOperator(task_id="begin_insert_sponsoredproducts_producttargetingreport")
        skip_insert_sponsoredproducts_producttargetingreport_task = DummyOperator(task_id="skip_insert_sponsoredproducts_producttargetingreport")
        end_insert_sponsoredproducts_producttargetingreport_task = DummyOperator(task_id="end_insert_sponsoredproducts_producttargetingreport")
        
        s3_to_snowflake_sponsoredproducts_producttargetingreport_task = PythonOperator(
            task_id="s3_to_snowflake_sponsoredproducts_producttargetingreport",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_sf_raw_sponsoredproducts_producttargetingreport.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_sponsoredproducts_producttargetingreport_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_sponsoredproducts_producttargetingreport_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/s3_to_sf_raw_sponsoredproducts_producttargetingreport_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_sponsoredproducts_producttargetingreport_task = BranchPythonOperator(
            task_id='check_new_files_found_sponsoredproducts_producttargetingreport',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_snowflake_SponsoredProducts_ProductTargetingReport.yaml",
                       "args_file_second": "amazon_ads_target_reports/s3_to_snowflake_SponsoredProducts_ProductTargetingReport_goessor.yaml",
                       "skip_task_id": "load_sponsoredproducts_producttargetingreport.skip_insert_sponsoredproducts_producttargetingreport",
                       "next_task_id": "load_sponsoredproducts_producttargetingreport.begin_insert_sponsoredproducts_producttargetingreport"
            },
        )

        insert_log_sponsoredproducts_producttargetingreport_task = PythonOperator(
            task_id="insert_log_sponsoredproducts_producttargetingreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/insert_log_SponsoredProducts_ProductTargetingReport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_sponsoredproducts_producttargetingreport_task = PythonOperator(
            task_id="dedupe_sponsoredproducts_producttargetingreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/dedupe_SponsoredProducts_ProductTargetingReport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_sponsoredproducts_producttargetingreport_task = PythonOperator(
            task_id="merge_stage_sponsoredproducts_producttargetingreport",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/merge_stage_SponsoredProducts_ProductTargetingReport.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_sponsoredproducts_producttargetingreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_sponsoredproducts_producttargetingreport', 
                    field_list=['adgroupid', 'campaignid', 'accountid', 'reportdate'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sponsoredproducts_producttargetingreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_sponsoredproducts_producttargetingreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_sponsoredproducts_producttargetingreport',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_sponsoredproducts_producttargetingreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sponsoredproducts_producttargetingreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_sponsoredproducts_producttargetingreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_sponsoredproducts_producttargetingreport',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_sponsoredproducts_producttargetingreport', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_sponsoredproducts_producttargetingreport_task = PythonOperator(
            task_id="run_audit_sponsoredproducts_producttargetingreport",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_sponsoredproducts_producttargetingreport", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_sponsoredproducts_producttargetingreport_task, list_s3_files_sponsoredproducts_producttargetingreport_task_goessor] >>
            check_new_files_found_sponsoredproducts_producttargetingreport_task >>
            [begin_insert_sponsoredproducts_producttargetingreport_task, skip_insert_sponsoredproducts_producttargetingreport_task]
        )

        (
            begin_insert_sponsoredproducts_producttargetingreport_task >> 
            s3_to_snowflake_sponsoredproducts_producttargetingreport_task >>
            s3_to_snowflake_sponsoredproducts_producttargetingreport_task_goessor >>
            insert_log_sponsoredproducts_producttargetingreport_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_sponsoredproducts_producttargetingreport_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_sponsoredproducts_producttargetingreport_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_sponsoredproducts_producttargetingreport_task >>
            end_insert_sponsoredproducts_producttargetingreport_task
        )

    #  --- Load sponsoredproducts_targetingclause ---
    
    with TaskGroup(group_id='load_sponsoredproducts_targetingclause') as tg_sponsoredproducts_targetingclause:
        list_s3_files_sponsoredproducts_targetingclause_task = PythonOperator(
            task_id="list_s3_files_sponsoredproducts_targetingclause",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/list_s3_SponsoredProducts_TargetingClause.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_sponsoredproducts_targetingclause_task_goessor = PythonOperator(
            task_id="list_s3_files_sponsoredproducts_targetingclause_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_target_reports/list_s3_SponsoredProducts_TargetingClause_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_sponsoredproducts_targetingclause_task = DummyOperator(task_id="begin_insert_sponsoredproducts_targetingclause")
        skip_insert_sponsoredproducts_targetingclause_task = DummyOperator(task_id="skip_insert_sponsoredproducts_targetingclause")
        end_insert_sponsoredproducts_targetingclause_task = DummyOperator(task_id="end_insert_sponsoredproducts_targetingclause")
        
        s3_to_snowflake_sponsoredproducts_targetingclause_task = PythonOperator(
            task_id="s3_to_snowflake_sponsoredproducts_targetingclause",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_sf_raw_sponsoredproducts_targetingclause.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_sponsoredproducts_targetingclause_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_sponsoredproducts_targetingclause_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_sf_raw_sponsoredproducts_targetingclause_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_sponsoredproducts_targetingclause_task = BranchPythonOperator(
            task_id='check_new_files_found_sponsoredproducts_targetingclause',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_target_reports/s3_to_snowflake_SponsoredProducts_TargetingClause.yaml",
                       "args_file_second": "amazon_ads_target_reports/s3_to_snowflake_SponsoredProducts_TargetingClause_goessor.yaml",
                       "skip_task_id": "load_sponsoredproducts_targetingclause.skip_insert_sponsoredproducts_targetingclause",
                       "next_task_id": "load_sponsoredproducts_targetingclause.begin_insert_sponsoredproducts_targetingclause"
            },
        )

        insert_log_sponsoredproducts_targetingclause_task = PythonOperator(
            task_id="insert_log_sponsoredproducts_targetingclause",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/insert_log_SponsoredProducts_TargetingClause.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_sponsoredproducts_targetingclause_task = PythonOperator(
            task_id="dedupe_sponsoredproducts_targetingclause",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/dedupe_SponsoredProducts_TargetingClause.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_sponsoredproducts_targetingclause_task = PythonOperator(
            task_id="merge_stage_sponsoredproducts_targetingclause",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "ads/amazon/amazon_ads_target_reports/merge_stage_SponsoredProducts_TargetingClause.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_sponsoredproducts_targetingclause',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_sponsoredproducts_targetingclause', 
                    field_list=['adgroupid', 'campaignid', 'accountid', 'fetchdate'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sponsoredproducts_targetingclause',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_sponsoredproducts_targetingclause', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_sponsoredproducts_targetingclause',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_sponsoredproducts_targetingclause', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_sponsoredproducts_targetingclause',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_sponsoredproducts_targetingclause', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_sponsoredproducts_targetingclause',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_sponsoredproducts_targetingclause', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        run_audit_sponsoredproducts_targetingclause_task = PythonOperator(
            task_id="run_audit_sponsoredproducts_targetingclause",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_sponsoredproducts_targetingclause", # REPLACE WITH FACT TABLE
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_sponsoredproducts_targetingclause_task, list_s3_files_sponsoredproducts_targetingclause_task_goessor] >>
            check_new_files_found_sponsoredproducts_targetingclause_task >>
            [begin_insert_sponsoredproducts_targetingclause_task, skip_insert_sponsoredproducts_targetingclause_task]
        )

        (
            begin_insert_sponsoredproducts_targetingclause_task >> 
            s3_to_snowflake_sponsoredproducts_targetingclause_task >>
            s3_to_snowflake_sponsoredproducts_targetingclause_task_goessor >>
            insert_log_sponsoredproducts_targetingclause_task >>
            
                ( run_dq_is_null_raw_task
                ) >> dedupe_sponsoredproducts_targetingclause_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_sponsoredproducts_targetingclause_task >>
            
                ( run_dq_is_unique_merge_pk_task
              
                , run_dq_is_null_merge_pk_task
              ) >> run_audit_sponsoredproducts_targetingclause_task >>
            end_insert_sponsoredproducts_targetingclause_task
        )

    # ---- Main branch ----
    begin >> get_workflow_params >> [tg_sponsoredbrands_targetreport , tg_sponsoredbrands_targetvideoreport , tg_sponsoredbrands_targets , tg_sponsoreddisplay_producttargetingreport , tg_sponsoreddisplay_targetingclause , tg_sponsoredproducts_producttargetingreport , tg_sponsoredproducts_targetingclause ] >> update_workflow_params >> end
    
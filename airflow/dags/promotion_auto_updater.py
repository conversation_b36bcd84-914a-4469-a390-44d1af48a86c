import logging
import warnings
import uuid
import json
from datetime import datetime
from airflow import DAG
from airflow.models import Variable
from airflow.providers.http.operators.http import SimpleHttpOperator
from airflow.operators.python import PythonOperator, BranchPythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.providers.http.sensors.http import HttpSensor
from airflow.utils.trigger_rule import TriggerRule
from tasklib import alerts
import tasklib.s3 as tls3
from tasklib.loader import S3ToSnowflake
import tasklib.workflow as tlw

load_obj = S3ToSnowflake()

warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)

DAG_ID = 'promotion_auto_updater'
API_ENDPOINT_CONN_ID = 'promotions_api_endpoint'
INSIGHTS_API_ENDPOINT_CONN_ID = 'insights_engine_api_endpoint'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily')  # Default to hourly runs
REQUEST_ID = str(uuid.uuid4())
WF_PARAMS_EXPR_MOD = json.dumps({"request_id": f'\'{REQUEST_ID}\''})
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"


def get_snapshot_date():
    '''
    Gets the snapshot date to run.
    '''
    # return datetime.strftime(datetime.utcnow() - timedelta(hours=18), '%Y-%m-%d')
    import pytz
    utc_time = datetime.utcnow()
    local_time_adjusted = pytz.utc.localize(utc_time).astimezone(pytz.timezone('US/Pacific'))
    return datetime.strftime(local_time_adjusted, '%Y-%m-%d')


def insights_generate_payload():
    '''
    Generate the payload for the Insights API call. The snapshot_date is chosen to be one day prior,
    based on UTC time.
    '''
    insight_request_id = str(uuid.uuid4())
    snapshot_date = get_snapshot_date()
    payload = {
        "request_id": insight_request_id,
        "snapshot_date": snapshot_date,
        "entity_type": "PROMOTION_PLANNING_INSIGHTS",
        "user_id": "hydy"
    }
    return json.dumps(payload)


def check_if_data_ready(**kwargs):
    from db_connectors.sf_connector import Snowflake
    sf_client = Snowflake()
    snapshot_date = get_snapshot_date()
    query = f"""SELECT * FROM dwh.staging.PROMOTION_AUTOMATION"""
    _, num_rows = sf_client.get_data(query)
    if num_rows > 0:
        return 'promotion_api_call_task'
    return 'skip_promotion_api_call'

def generate_payload():
    '''
    Generate the payload for the Insights API call. The snapshot_date is chosen to be one day prior,
    based on UTC time.
    '''
    payload = {
        "batch_id": json.loads(WF_PARAMS_EXPR_MOD)['request_id']
    }
    return json.dumps(payload)


def check_response(response):
    try:
        if response.status_code == 204:
            return True
        else:
            return False
    except Exception as e:
        logger.error(f'API ERROR - {e}')
    return False


def check_insights_response(response):
    '''
    Monitor the Insights API job status.
    The function returns True if status="completed" else it returns False.
    Any error in reading status does not cause the job to fail, as we might
    see intermittent errors in the API call
    '''
    try:
        json_response = response.json()
        print(f'RESPONSE: {json_response}')
        status = json_response.get('status', '').lower()
        if status == 'completed':
            return True
    except Exception as e:
        print(f'API ERROR - {e}')
    return False


with DAG(
        dag_id=DAG_ID,
        start_date=datetime(2023, 6, 22),
        schedule_interval=DAG_SCHEDULE,
        catchup=False,
        max_active_runs=1,
        params={"author": "nagesh", 'workflow_name': DAG_ID.upper()},
        tags=['Nagesh'],
) as dag:
    import tasklib.replication as tlr
    import tasklib.sql as tlsql

    # get_workflow_params = PythonOperator(
    #     task_id="task_get_workflow_parameters",
    #     python_callable=tlw.get_workflow_params,
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    #     op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    # )
    #
    # update_workflow_params = PythonOperator(
    #     task_id="task_update_workflow_parameters",
    #     python_callable=tlw.update_workflow_params,
    #     op_kwargs={"wf_params": WF_PARAMS_EXPR},
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    #     trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    # )

    # # Asins that needs to be excluded from insights/automation
    # list_s3_exclude_asins_from_insights = PythonOperator(
    #     task_id="task_list_s3_exclude_asins_from_insights",
    #     python_callable=tls3.list_s3_modified_files,
    # op_kwargs={"args_file": "dynamic_promotions/exclude_asins_from_insights_s3_list_folders.yaml",
    #                "wf_params": WF_PARAMS_EXPR},
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert
    # )
    #
    # transfer_s3_to_snowflake_exclude_asin_from_insights = PythonOperator(
    #     task_id="transfer_s3_to_snowflake_exclude_asin_from_insights",
    #     python_callable=load_obj.s3_to_snowflake_load,
    #     op_kwargs={
    #         "args_file": "dynamic_promotions/s3_to_sf_raw_exclude_asins_from_insights.yaml"},
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert
    # )
    #
    # create_stg_exclude_asin_from_insights = PythonOperator(
    #     task_id="create_stg_exclude_asin_from_insights",
    #     python_callable=tlsql.run_query_file,
    #     op_kwargs={
    #         "connection": "Snowflake",
    #         "sql_file": "dynamic_promotions/insights/create_stg_exclude_asins_from_insights.sql",
    #         "wf_params": WF_PARAMS_EXPR,
    #     },
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    # )
    #
    # # Deal asin exception tracker
    # list_s3_deal_asins_exception_tracker = PythonOperator(
    #     task_id="task_list_s3_deal_asins_exception_tracker",
    #     python_callable=tls3.list_s3_modified_files,
    #     op_kwargs={"args_file": "dynamic_promotions/deal_asins_units_tracker_s3_to_snowflake.yaml",
    #                "wf_params": WF_PARAMS_EXPR},
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert
    # )
    #
    # transfer_s3_to_snowflake_deal_asins_exception_tracker = PythonOperator(
    #     task_id="transfer_s3_to_snowflake_deal_asins_exception_tracker",
    #     python_callable=load_obj.s3_to_snowflake_load,
    #     op_kwargs={
    #         "args_file": "dynamic_promotions/s3_to_sf_raw_deal_asins_units_tracker.yaml"},
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert
    # )
    #
    # create_stg_deal_asins_exception_tracker = PythonOperator(
    #     task_id="create_stg_deal_asins_exception_tracker",
    #     python_callable=tlsql.run_query_file,
    #     op_kwargs={
    #         "connection": "Snowflake",
    #         "sql_file": "dynamic_promotions/insights/create_stg_deal_asins_units_tracker.sql",
    #         "wf_params": WF_PARAMS_EXPR,
    #     },
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    # )

    create_auto_update_promotion = PythonOperator(
        task_id="create_auto_update_promotion",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "promotions/automation/auto_update_promo.sql",
            "wf_params": WF_PARAMS_EXPR_MOD,
        },
    )

    auto_committed_units_exception_tracker = PythonOperator(
        task_id="auto_committed_units_exception_tracker",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "promotions/automation/auto_committed_units_exception_tracker.sql",
            "wf_params": WF_PARAMS_EXPR_MOD,
        },
    )

    replicate_promotion_creation_sf_to_pg_task = PythonOperator(
        task_id="replicate_promotion_creation_sf_to_pg_task",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/promotion_auto_update.yaml"}
    )

    replicate_auto_committed_units_exception_tracker_sf_to_pg_task = PythonOperator(
        task_id="replicate_auto_committed_units_exception_tracker_sf_to_pg_task",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/auto_committed_units_exception_tracker.yaml"}
    )

    check_data_ready = BranchPythonOperator(
        task_id='check_if_data_ready',
        python_callable=check_if_data_ready,
        provide_context=True,
    )

    skip_promotion_api_call = DummyOperator(task_id="skip_promotion_api_call")

    promotion_api_call_task = SimpleHttpOperator(
        task_id="promotion_api_call",
        http_conn_id=API_ENDPOINT_CONN_ID,
        endpoint='/update/auto-update',
        method='PUT',
        data=generate_payload(),
        headers={"Content-Type": "application/json", "Authorization": "Basic dGVzdDp0ZXN0"},
        response_check=lambda response: check_response(response),
        log_response=True,
    )

    begin_task = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end_task = DummyOperator(
        task_id="end",
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    begin_task >> create_auto_update_promotion >> auto_committed_units_exception_tracker >> replicate_promotion_creation_sf_to_pg_task >> \
    replicate_auto_committed_units_exception_tracker_sf_to_pg_task >> check_data_ready >> [promotion_api_call_task, skip_promotion_api_call] >> end_task
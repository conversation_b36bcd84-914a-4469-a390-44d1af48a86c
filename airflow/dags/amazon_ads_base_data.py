import logging
from datetime import datetime

from airflow import DAG
from airflow.models import Variable
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON>ython<PERSON>perator, PythonOperator
from airflow.utils.trigger_rule import TriggerRule

import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib.loader import check_for_new_files
from tasklib import alerts

from tasklib.loader import S3ToSnowflake
load_obj = S3ToSnowflake()
DAG_ID = 'amazon_ads_base_data'
TRANSFORM_BASE = 'ads/amazon/' + DAG_ID
RELEASE_DEF = '1'
BUILD_NUM = '01'

S3_TO_SNOWFLAKE_DISPLAY_ADGROUP_YAML = DAG_ID + '/display_adgroup_s3_to_snowflake.yaml'
S3_TO_SNOWFLAKE_DISPLAY_ADGROUP_YAML_GOESSOR = DAG_ID + '/display_adgroup_s3_to_snowflake_goessor.yaml'
S3_TO_SNOWFLAKE_SPONSORED_DISPLAY_ADGROUP_REPORT_YAML = DAG_ID + '/sponsored_display_adgroup_report_s3_to_snowflake.yaml'
S3_TO_SNOWFLAKE_SPONSORED_DISPLAY_ADGROUP_REPORT_YAML_GOESSOR = DAG_ID + '/sponsored_display_adgroup_report_s3_to_snowflake_goessor.yaml'
S3_TO_SNOWFLAKE_PRODUCTS_ADGROUP_YAML = DAG_ID + '/products_adgroup_s3_to_snowflake.yaml'
S3_TO_SNOWFLAKE_PRODUCTS_ADGROUP_YAML_GOESSOR = DAG_ID + '/products_adgroup_s3_to_snowflake_goessor.yaml'
S3_TO_SNOWFLAKE_PRODUCTS_BIDDABLE_YAML = DAG_ID + '/products_biddable_s3_to_snowflake.yaml'
S3_TO_SNOWFLAKE_PRODUCTS_BIDDABLE_YAML_GOESSOR = DAG_ID + '/products_biddable_s3_to_snowflake_goessor.yaml'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"

log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2022, 5, 8),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'sauvik'},
    tags=['Sauvik', 'Goessor'],
) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

    list_s3_modified_files_products_biddable = PythonOperator(
        task_id="list_s3_modified_files_products_biddable",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/products_biddable_list_s3.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    list_s3_modified_files_products_biddable_goessor = PythonOperator(
        task_id="list_s3_modified_files_products_biddable_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/products_biddable_list_s3_goessor.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    skip_data_products_biddable = DummyOperator(task_id='skip_data_products_biddable')

    check_new_files_found_products_biddable = BranchPythonOperator(
        task_id='check_new_files_found_products_biddable',
        python_callable=check_for_new_files,
        op_kwargs={
            'args_file': S3_TO_SNOWFLAKE_PRODUCTS_BIDDABLE_YAML,
            'args_file_second': S3_TO_SNOWFLAKE_PRODUCTS_BIDDABLE_YAML_GOESSOR,
            'skip_task_id': 'skip_data_products_biddable',
            'next_task_id': 'transfer_s3_to_snowflake_products_biddable',
        }
    )

    transfer_s3_to_snowflake_products_biddable = PythonOperator(
        task_id="transfer_s3_to_snowflake_products_biddable",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_ads_base_data/s3_to_sf_raw_amazon_sponsoredproducts_biddablekeyword.yaml"},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    transfer_s3_to_snowflake_products_biddable_goessor = PythonOperator(
        task_id="transfer_s3_to_snowflake_products_biddable_goessor",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_ads_base_data/s3_to_sf_raw_amazon_sponsoredproducts_biddablekeyword_goessor.yaml"},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_transfer_s3_to_snowflake_products_biddable = PythonOperator(
        task_id="run_dq_transfer_s3_to_snowflake_products_biddable",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$raw_db.RAW_AMAZON_SPONSOREDPRODUCTS_BIDDABLEKEYWORD",
            'test_name': 'identifiers_not_nulls',
            'sql_query': tldq.gen_check_if_nulls(
                tb_name="$raw_db.RAW_AMAZON_SPONSOREDPRODUCTS_BIDDABLEKEYWORD", 
                field_list=['_DATON_BATCH_RUNTIME','KEYWORDID','ADGROUPID','ACCOUNTID','CAMPAIGNID','FETCHDATE'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_display_adgroup = PythonOperator(
        task_id="list_s3_modified_files_display_adgroup",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/display_adgroup_list_s3.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    list_s3_modified_files_display_adgroup_goessor = PythonOperator(
        task_id="list_s3_modified_files_display_adgroup_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/display_adgroup_list_s3_goessor.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    skip_data_display_adgroup = DummyOperator(task_id='skip_data_display_adgroup')

    check_new_files_found_display_adgroup = BranchPythonOperator(
        task_id='check_new_files_found_display_adgroup',
        python_callable=check_for_new_files,
        op_kwargs={
            'args_file': S3_TO_SNOWFLAKE_DISPLAY_ADGROUP_YAML,
            'args_file_second': S3_TO_SNOWFLAKE_DISPLAY_ADGROUP_YAML_GOESSOR,
            'skip_task_id': 'skip_data_display_adgroup',
            'next_task_id': 'transfer_s3_to_snowflake_display_adgroup',
        }
    )

    transfer_s3_to_snowflake_display_adgroup = PythonOperator(
        task_id="transfer_s3_to_snowflake_display_adgroup",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_ads_base_data/s3_to_sf_raw_amazon_sponsoreddisplay_adgroup.yaml"},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    transfer_s3_to_snowflake_display_adgroup_goessor = PythonOperator(
        task_id="transfer_s3_to_snowflake_display_adgroup_goessor",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_ads_base_data/s3_to_sf_raw_amazon_sponsoreddisplay_adgroup_goessor.yaml"},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_transfer_s3_to_snowflake_display_adgroup = PythonOperator(
        task_id="run_dq_transfer_s3_to_snowflake_display_adgroup",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$raw_db.RAW_AMAZON_SPONSOREDDISPLAY_ADGROUP",
            'test_name': 'identifiers_not_nulls',
            'sql_query': tldq.gen_check_if_nulls(
                tb_name="$raw_db.RAW_AMAZON_SPONSOREDDISPLAY_ADGROUP", 
                field_list=['_DATON_BATCH_RUNTIME','ADGROUPID','ACCOUNTID','CAMPAIGNID','FETCHDATE'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_products_adgroup = PythonOperator(
        task_id="list_s3_modified_files_products_adgroup",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/products_adgroup_list_s3.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    list_s3_modified_files_products_adgroup_goessor = PythonOperator(
        task_id="list_s3_modified_files_products_adgroup_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/products_adgroup_list_s3_goessor.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    skip_data_products_adgroup = DummyOperator(task_id='skip_data_products_adgroup')

    check_new_files_found_products_adgroup = BranchPythonOperator(
        task_id='check_new_files_found_products_adgroup',
        python_callable=check_for_new_files,
        op_kwargs={
            'args_file': S3_TO_SNOWFLAKE_PRODUCTS_ADGROUP_YAML,
            'args_file_second': S3_TO_SNOWFLAKE_PRODUCTS_ADGROUP_YAML_GOESSOR,
            'skip_task_id': 'skip_data_products_adgroup',
            'next_task_id': 'transfer_s3_to_snowflake_products_adgroup',
        }
    )

    transfer_s3_to_snowflake_products_adgroup = PythonOperator(
        task_id="transfer_s3_to_snowflake_products_adgroup",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_ads_base_data/s3_to_sf_raw_amazon_sponsoredproducts_adgroup.yaml"},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    transfer_s3_to_snowflake_products_adgroup_goessor = PythonOperator(
        task_id="transfer_s3_to_snowflake_products_adgroup_goessor",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_ads_base_data/s3_to_sf_raw_amazon_sponsoredproducts_adgroup_goessor.yaml"},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_transfer_s3_to_snowflake_products_adgroup = PythonOperator(
        task_id="run_dq_transfer_s3_to_snowflake_products_adgroup",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$raw_db.RAW_AMAZON_SPONSOREDPRODUCTS_ADGROUP",
            'test_name': 'identifiers_not_nulls',
            'sql_query': tldq.gen_check_if_nulls(
                tb_name="$raw_db.RAW_AMAZON_SPONSOREDPRODUCTS_ADGROUP", 
                field_list=['_DATON_BATCH_RUNTIME','ADGROUPID','ACCOUNTID','CAMPAIGNID','FETCHDATE'],
                hard_alert=True
                )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    dedup_products_biddable = PythonOperator(
        task_id="dedup_products_biddable",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/dedup_products_biddable.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_dedup_products_biddable = PythonOperator(
        task_id="run_dq_dedup_products_biddable",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.DEDUP_AMAZON_SPONSOREDPRODUCTS_BIDDABLEKEYWORD",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.DEDUP_AMAZON_SPONSOREDPRODUCTS_BIDDABLEKEYWORD", 
                field_list=['advertisement_pk'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    merge_products_biddable = PythonOperator(
        task_id="merge_products_biddable",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/merge_products_biddable.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_merge_products_biddable = PythonOperator(
        task_id="run_dq_merge_products_biddable",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.MERGED_AMAZON_SPONSOREDPRODUCTS_BIDDABLEKEYWORD",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.MERGED_AMAZON_SPONSOREDPRODUCTS_BIDDABLEKEYWORD", 
                field_list=['advertisement_pk'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )    

    dedup_display_adgroup = PythonOperator(
        task_id="dedup_display_adgroup",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/dedup_display_adgroup.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_dedup_display_adgroup = PythonOperator(
        task_id="run_dq_dedup_display_adgroup",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.DEDUP_AMAZON_SPONSOREDDISPLAY_ADGROUP",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.DEDUP_AMAZON_SPONSOREDDISPLAY_ADGROUP", 
                field_list=['advertisement_pk'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )    

    merge_display_adgroup = PythonOperator(
        task_id="merge_display_adgroup",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/merge_display_adgroup.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_merge_display_adgroup = PythonOperator(
        task_id="run_dq_merge_display_adgroup",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.MERGED_AMAZON_SPONSOREDDISPLAY_ADGROUP",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.MERGED_AMAZON_SPONSOREDDISPLAY_ADGROUP", 
                field_list=['advertisement_pk'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    dedup_products_adgroup = PythonOperator(
        task_id="dedup_products_adgroup",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/dedup_products_adgroup.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_dedup_products_adgroup = PythonOperator(
        task_id="run_dq_dedup_products_adgroup",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.DEDUP_AMAZON_SPONSOREDPRODUCTS_ADGROUP",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.DEDUP_AMAZON_SPONSOREDPRODUCTS_ADGROUP", 
                field_list=['advertisement_pk'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    merge_products_adgroup = PythonOperator(
        task_id="merge_products_adgroup",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/merge_products_adgroup.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_merge_products_adgroup = PythonOperator(
        task_id="run_dq_merge_products_adgroup",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.MERGED_AMAZON_SPONSOREDPRODUCTS_ADGROUP",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.MERGED_AMAZON_SPONSOREDPRODUCTS_ADGROUP", 
                field_list=['advertisement_pk'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )


    list_s3_modified_files_sponsored_display_adgroup_report = PythonOperator(
        task_id="list_s3_modified_files_sponsored_display_adgroup_report",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/sponsored_display_adgroup_report_list_s3.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    list_s3_modified_files_sponsored_display_adgroup_report_goessor = PythonOperator(
        task_id="list_s3_modified_files_sponsored_display_adgroup_report_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/sponsored_display_adgroup_report_list_s3_goessor.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    skip_data_sponsored_display_adgroup_report = DummyOperator(task_id='skip_data_sponsored_display_adgroup_report')

    check_new_files_found_sponsored_display_adgroup_report = BranchPythonOperator(
        task_id='check_new_files_found_sponsored_display_adgroup_report',
        python_callable=check_for_new_files,
        op_kwargs={
            'args_file': S3_TO_SNOWFLAKE_SPONSORED_DISPLAY_ADGROUP_REPORT_YAML,
            'args_file_second': S3_TO_SNOWFLAKE_SPONSORED_DISPLAY_ADGROUP_REPORT_YAML_GOESSOR,
            'skip_task_id': 'skip_data_sponsored_display_adgroup_report',
            'next_task_id': 'transfer_s3_to_snowflake_sponsored_display_adgroup_report',
        }
    )

    transfer_s3_to_snowflake_sponsored_display_adgroup_report = PythonOperator(
        task_id="transfer_s3_to_snowflake_sponsored_display_adgroup_report",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_ads_base_data/s3_to_sf_raw_amazon_sponsoreddisplay_adgroup_report.yaml"},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    transfer_s3_to_snowflake_sponsored_display_adgroup_report_goessor = PythonOperator(
        task_id="transfer_s3_to_snowflake_sponsored_display_adgroup_report_goessor",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_ads_base_data/s3_to_sf_raw_amazon_sponsoreddisplay_adgroup_report_goessor.yaml"},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_transfer_s3_to_snowflake_sponsored_display_adgroup_report = PythonOperator(
        task_id="run_dq_transfer_s3_to_snowflake_sponsored_display_adgroup_report",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$raw_db.raw_amazon_sponsored_display_adgroup_report",
            'test_name': 'identifiers_not_nulls',
            'sql_query': tldq.gen_check_if_nulls(
                tb_name="$raw_db.raw_amazon_sponsored_display_adgroup_report",
                field_list=['requesttime', 'accountid', 'adgroupid', 'campaignid', 'profileid', 'reportdate'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    dedup_sponsored_display_adgroup_report = PythonOperator(
        task_id="dedup_sponsored_display_adgroup_report",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/dedup_sponsored_display_adgroup_report.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_dedup_sponsored_display_adgroup_report = PythonOperator(
        task_id="run_dq_dedup_sponsored_display_adgroup_report",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.dedup_amazon_sponsored_display_adgroup_report",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.dedup_amazon_sponsored_display_adgroup_report",
                field_list=['advertisement_pk'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    merge_sponsored_display_adgroup_report = PythonOperator(
        task_id="merge_sponsored_display_adgroup_report",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/merge_sponsored_display_adgroup_report.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_merge_sponsored_display_adgroup_report = PythonOperator(
        task_id="run_dq_merge_sponsored_display_adgroup_report",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.merged_amazon_sponsored_display_adgroup_report",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.merged_amazon_sponsored_display_adgroup_report",
                field_list=['advertisement_pk'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    # --- Audit the data checks ---

    import tasklib.audit as tla

    run_audit_products_biddable = PythonOperator(
        task_id="run_audit_products_biddable",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$stage_db.MERGED_AMAZON_SPONSOREDPRODUCTS_BIDDABLEKEYWORD",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}",
                   "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                   "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                   },
        on_failure_callback=alerts.send_failure_alert
    )
    run_audit_products_adgroup = PythonOperator(
        task_id="run_audit_products_adgroup",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$stage_db.MERGED_AMAZON_SPONSOREDPRODUCTS_ADGROUP",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}",
                   "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                   "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                   },
        on_failure_callback=alerts.send_failure_alert
    )
    
    run_audit_display_adgroup = PythonOperator(
        task_id="run_audit_display_adgroup",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$stage_db.MERGED_AMAZON_SPONSOREDDISPLAY_ADGROUP",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}",
                   "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                   "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                   },
        on_failure_callback=alerts.send_failure_alert
    )

    run_audit_sponsored_display_adgroup_report = PythonOperator(
        task_id="run_audit_sponsored_display_adgroup_report",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$stage_db.merged_amazon_sponsored_display_adgroup_report",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}",
                   "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                   "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                   },
        on_failure_callback=alerts.send_failure_alert
    )

    # --- Update workflow status ---

    update_workflow_params = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )


    start_products_biddable = DummyOperator(task_id="start_products_biddable")
    end_products_biddable = DummyOperator(
        task_id="end_products_biddable",
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    (
        start_products_biddable >>
        [list_s3_modified_files_products_biddable, list_s3_modified_files_products_biddable_goessor] >>
        check_new_files_found_products_biddable >>
        [transfer_s3_to_snowflake_products_biddable, skip_data_products_biddable]
    )

    (
        transfer_s3_to_snowflake_products_biddable >>
        transfer_s3_to_snowflake_products_biddable_goessor >>
        run_dq_transfer_s3_to_snowflake_products_biddable >>
        dedup_products_biddable >>
        run_dq_dedup_products_biddable >>
        merge_products_biddable >>
        run_dq_merge_products_biddable >>
        run_audit_products_biddable
    )

    [skip_data_products_biddable, run_audit_products_biddable] >> end_products_biddable

    start_products_adgroup = DummyOperator(task_id="start_products_adgroup")
    end_products_adgroup = DummyOperator(
        task_id="end_products_adgroup",
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    (
        start_products_adgroup >>
        [list_s3_modified_files_products_adgroup, list_s3_modified_files_products_adgroup_goessor] >>
        check_new_files_found_products_adgroup >>
        [transfer_s3_to_snowflake_products_adgroup, skip_data_products_adgroup]
    )

    (
        transfer_s3_to_snowflake_products_adgroup >>
        transfer_s3_to_snowflake_products_adgroup_goessor >>
        run_dq_transfer_s3_to_snowflake_products_adgroup >>
        dedup_products_adgroup >>
        run_dq_dedup_products_adgroup >>
        merge_products_adgroup >>
        run_dq_merge_products_adgroup >>
        run_audit_products_adgroup
    )

    [skip_data_products_adgroup, run_audit_products_adgroup] >> end_products_adgroup


    start_display_adgroup = DummyOperator(task_id="start_display_adgroup")
    end_display_adgroup = DummyOperator(
        task_id="end_display_adgroup",
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    (
        start_display_adgroup >>
        [list_s3_modified_files_display_adgroup, list_s3_modified_files_display_adgroup_goessor] >>
        check_new_files_found_display_adgroup >>
        [transfer_s3_to_snowflake_display_adgroup, skip_data_display_adgroup]
    )
    
    (
        transfer_s3_to_snowflake_display_adgroup >>
        transfer_s3_to_snowflake_display_adgroup_goessor >>
        run_dq_transfer_s3_to_snowflake_display_adgroup >>
        dedup_display_adgroup >>
        run_dq_dedup_display_adgroup >>
        merge_display_adgroup >>
        run_dq_merge_display_adgroup >>
        run_audit_display_adgroup        
    )

    [skip_data_display_adgroup, run_audit_display_adgroup] >> end_display_adgroup

    start_sponsored_display_adgroup_report = DummyOperator(task_id="start_sponsored_display_adgroup_report")
    end_sponsored_display_adgroup_report = DummyOperator(
        task_id="end_sponsored_display_adgroup_report",
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    (
            start_sponsored_display_adgroup_report >>
            [list_s3_modified_files_sponsored_display_adgroup_report, list_s3_modified_files_sponsored_display_adgroup_report_goessor] >>
            check_new_files_found_sponsored_display_adgroup_report >>
            [transfer_s3_to_snowflake_sponsored_display_adgroup_report, skip_data_sponsored_display_adgroup_report]
    )

    (
            transfer_s3_to_snowflake_sponsored_display_adgroup_report >>
            transfer_s3_to_snowflake_sponsored_display_adgroup_report_goessor >>
            run_dq_transfer_s3_to_snowflake_sponsored_display_adgroup_report >>
            dedup_sponsored_display_adgroup_report >>
            run_dq_dedup_sponsored_display_adgroup_report >>
            merge_sponsored_display_adgroup_report >>
            run_dq_merge_sponsored_display_adgroup_report >>
            run_audit_sponsored_display_adgroup_report
    )

    [skip_data_sponsored_display_adgroup_report, run_audit_sponsored_display_adgroup_report] >> end_sponsored_display_adgroup_report

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")
    # all_loads_completed = DummyOperator(task_id="all_loads_completed")

    begin >> get_workflow_params
    get_workflow_params >> [start_products_biddable, start_products_adgroup, start_display_adgroup, start_sponsored_display_adgroup_report]
    [end_products_biddable, end_products_adgroup, end_display_adgroup, end_sponsored_display_adgroup_report] >> update_workflow_params >> end

"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime, timedelta
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON>ythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'netsuite_ingestion'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@hourly')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)


def run_query_file(sql_file: str = None, wf_params: str = None, connection: str = 'Snowflake', **context):
    import ast
    from db_connectors.sf_connector import Snowflake
    from helpers.helper import get_absolute_sql_file_path, load_db_variables, multi_replace_string, set_query_tag

    # read schema, database names from application configuration for variables used in the sql
    sql_vars_dict = load_db_variables()

    sql_file_abs_path = get_absolute_sql_file_path(sql_file)
    log.info(f"sql file is {sql_file_abs_path}")

    # read query file contents
    sql_text = ""
    with open(sql_file_abs_path, 'r') as f:
        sql_text = f.read()

    # remove leading and trailing spaces, tabs
    if sql_text:
        sql_text = sql_text.strip()

    # convert params dictionary string to dictionary
    wf_params_dict = ast.literal_eval(wf_params)

    sql_vars_dict['start_ts'] = wf_params_dict.get('wf_start_ts_iso_utc')
    sql_vars_dict['end_ts'] = wf_params_dict.get('wf_end_ts_iso_utc')

    # replace variables names with the appropriate values
    sql_text2 = multi_replace_string(sql_text, sql_vars_dict)
    sql_text2 = multi_replace_string(sql_text2, wf_params_dict)

    log.info("queries to be executed:")
    log.info(sql_text2)

    query_tag = set_query_tag(sql_file=sql_file, **context)

    # make a list of queries using ; as query delimiter
    query_list = sql_text2.split(';')

    # invoke library call to execute the queries
    sf = Snowflake('netsuite_conn')
    sf.execute_multi_statements(query_list, query_tag=query_tag)


with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2025, 1, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    dagrun_timeout=timedelta(minutes=30),
    max_active_runs=1,
    params={'workflow_name': 'NETSUITE_INGESTION',
            'author': 'thinhnd'},
    tags=['thinhnd', 'Raptor']
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
        
    )

    begin = DummyOperator(task_id="begin", depends_on_past=False, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load raw_transaction ---
    
    with TaskGroup(group_id='load_raw_transaction') as tg_raw_transaction:
        list_s3_files_raw_transaction_task = PythonOperator(
            task_id="list_s3_files_raw_transaction",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_ingestion/list_s3_raw_transaction.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            
        )

        # Need a begin dummy operator for branching
        begin_insert_raw_transaction_task = DummyOperator(task_id="begin_insert_raw_transaction")
        skip_insert_raw_transaction_task = DummyOperator(task_id="skip_insert_raw_transaction")
        end_insert_raw_transaction_task = DummyOperator(task_id="end_insert_raw_transaction")
        end_raw_transaction_task = DummyOperator(
            task_id="end_raw_transaction",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_raw_transaction_task = BranchPythonOperator(
            task_id='check_new_files_found_raw_transaction',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_snowflake_raw_transaction.yaml",
                       "skip_task_id": "load_raw_transaction.skip_insert_raw_transaction",
                       "next_task_id": "load_raw_transaction.begin_insert_raw_transaction"
            },
        )

        s3_to_snowflake_raw_transaction_task = PythonOperator(
            task_id="s3_to_snowflake_raw_transaction",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_ingestion/s3_to_sf_raw_transaction.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        

        insert_log_raw_transaction_task = PythonOperator(
            task_id="insert_log_raw_transaction",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_log_raw_transaction.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        append_to_transaction_table = PythonOperator(
            task_id="insert_transaction",
            python_callable=run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "netsuite_ingestion/insert_transaction.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_raw_transaction_task >>
            check_new_files_found_raw_transaction_task >>
            [begin_insert_raw_transaction_task, skip_insert_raw_transaction_task]
        )

        (
                begin_insert_raw_transaction_task >>
                s3_to_snowflake_raw_transaction_task >>

                insert_log_raw_transaction_task >>
                append_to_transaction_table >>

                end_insert_raw_transaction_task >>
                end_raw_transaction_task
        )

        skip_insert_raw_transaction_task >> end_raw_transaction_task

    


    # ---- Main branch ----
    chain(
       begin,
       get_workflow_parameters,
       tg_raw_transaction,
       update_workflow_parameters,
       end
    )
    
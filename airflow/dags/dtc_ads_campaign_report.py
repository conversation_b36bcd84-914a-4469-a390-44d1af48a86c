"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import <PERSON><PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'dtc_ads_campaign_report'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 */4 * * *') # Default to hourly
RELEASE_DEF = '1'
WF_PARAMS_EXPR_FB = "{{ ti.xcom_pull(task_ids='facebook_ads_campaign.get_workflow_parameters') }}"
WF_PARAMS_EXPR_GA = "{{ ti.xcom_pull(task_ids='google_ads_campaign.get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2021, 1, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'fb_workflow_name': 'FACEBOOK_ADS',
            'ga_workflow_name': 'GOOGLE_ADS',
            'author': 'harshad'},
    tags=['Harshad', 'Raptor']
) as dag:

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    create_brand_mapping = PythonOperator(
        task_id="create_brand_mapping",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file": "ads/facebook/facebook_ads/create_brand_mapping.sql",
            "wf_params": "{}",
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    # -- FACEBOOK WORKFLOW ------------------------------------------------------------------------------------------------------
    with TaskGroup(group_id='facebook_ads_campaign') as tg_facebook_ads:

        get_workflow_parameters = PythonOperator(
            task_id="get_workflow_parameters",
            python_callable=tlw.get_workflow_params,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "workflow_name": "{{ params.get('fb_workflow_name') }}",
            }
        )

        update_workflow_parameters = PythonOperator(
            task_id="update_workflow_parameters",
            python_callable=tlw.update_workflow_params,
            op_kwargs={
                "wf_params": WF_PARAMS_EXPR_FB,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )

        # Load facebook_adinsights
        with TaskGroup(group_id='load_adinsights') as tg_load_adinsights:
            list_s3_files_adinsights = PythonOperator(
                task_id="list_s3_files_adinsights",
                python_callable=tls3.list_s3_modified_files,
                op_kwargs={
                    "args_file": "facebook_ads/list_s3_files_adinsights.yaml",
                    "wf_params": WF_PARAMS_EXPR_FB,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            # Need a begin dummy operator for branching
            begin_insert_adinsights = DummyOperator(task_id="begin_insert_adinsights")
            skip_insert_adinsights = DummyOperator(task_id="skip_insert_adinsights")
            end_insert_adinsights = DummyOperator(task_id="end_insert_adinsights")
            end_adinsights = DummyOperator(
                task_id="end_adinsights",
                trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
            )

            check_new_files_found_adinsights = BranchPythonOperator(
                task_id='check_new_files_found_adinsights',
                python_callable=check_for_new_files,
                op_kwargs={
                    "args_file": "facebook_ads/s3_to_snowflake_adinsights.yaml",
                    "skip_task_id": "facebook_ads_campaign.load_adinsights.skip_insert_adinsights",
                    "next_task_id": "facebook_ads_campaign.load_adinsights.begin_insert_adinsights",
                },
            )

            s3_to_sf_raw_adinsights = PythonOperator(
                task_id="s3_to_sf_raw_adinsights",
                python_callable=load_obj.s3_to_snowflake_load,
                op_kwargs={
                    "args_file": "facebook_ads/s3_to_sf_raw_adinsights.yaml",
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert
            )

            run_dq_null_pk_raw_adinsights = PythonOperator(
                task_id="run_dq_null_pk_raw_adinsights",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_null_pk_raw_adinsights',
                    'query_file': "ads/facebook/facebook_ads/dq_null_pk_raw_adinsights.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            insert_log_adinsights = PythonOperator(
                task_id="insert_log_adinsights",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/facebook/facebook_ads/insert_log_adinsights.sql",
                    "wf_params": WF_PARAMS_EXPR_FB,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            dedupe_adinsights = PythonOperator(
                task_id="dedupe_adinsights",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/facebook/facebook_ads/dedupe_adinsights.sql",
                    "wf_params": WF_PARAMS_EXPR_FB,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            run_dq_dedupe_adinsights = PythonOperator(
                task_id="run_dq_dedupe_adinsights",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_dedupe_adinsights',
                    'query_file': "ads/facebook/facebook_ads/dq_dedupe_adinsights.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            merge_adinsights = PythonOperator(
                task_id="merge_adinsights",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/facebook/facebook_ads/merge_adinsights.sql",
                    "wf_params": WF_PARAMS_EXPR_FB,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            update_merge_adinsights = PythonOperator(
                task_id="update_merge_adinsights",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/facebook/facebook_ads/update_merge_adinsights.sql",
                    "wf_params": WF_PARAMS_EXPR_FB,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            run_dq_merge_adinsights = PythonOperator(
                task_id="run_dq_merge_adinsights",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_merge_adinsights',
                    'query_file': "ads/facebook/facebook_ads/dq_merge_adinsights.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            insert_ads_campaign_report = PythonOperator(
                task_id="insert_ads_campaign_report",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/facebook/facebook_ads/insert_ads_campaign_report.sql",
                    "wf_params": WF_PARAMS_EXPR_FB,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            run_dq_ads_campaign_report = PythonOperator(
                task_id="run_dq_ads_campaign_report",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_ads_campaign_report',
                    'query_file': "ads/facebook/facebook_ads/dq_merge_adinsights.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            run_audit_adinsights = PythonOperator(
                task_id="run_audit_adinsights",
                python_callable=tla.run_audit,
                op_kwargs={
                    "table_name": "$curated_db.facebook_ads_campaign_report",
                    "wf_params": WF_PARAMS_EXPR_FB,
                    "ts_created_field": 'record_created_timestamp_utc',
                    "ts_updated_field": 'record_updated_timestamp_utc',
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            # Ad campaign workflow
            chain(
                list_s3_files_adinsights,
                check_new_files_found_adinsights,
                [begin_insert_adinsights, skip_insert_adinsights]
            )

            chain(
                begin_insert_adinsights,
                s3_to_sf_raw_adinsights,
                run_dq_null_pk_raw_adinsights,
                insert_log_adinsights,
                dedupe_adinsights,
                run_dq_dedupe_adinsights,
                merge_adinsights,
                update_merge_adinsights,
                run_dq_merge_adinsights,
                insert_ads_campaign_report,
                run_dq_ads_campaign_report,
                run_audit_adinsights,
                end_insert_adinsights,
                end_adinsights
            )

            skip_insert_adinsights >> end_adinsights

        # Facebook workflow
        chain(
            get_workflow_parameters,
            tg_load_adinsights,
            update_workflow_parameters,
        )

    # -- GOOGLE WORKFLOW --------------------------------------------------------------------------------------------------------
    with TaskGroup(group_id='google_ads_campaign') as tg_google_ads_campaign:

        get_workflow_parameters = PythonOperator(
            task_id="get_workflow_parameters",
            python_callable=tlw.get_workflow_params,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={
                "workflow_name": "{{ params.get('ga_workflow_name') }}",
            }
        )

        update_workflow_parameters = PythonOperator(
            task_id="update_workflow_parameters",
            python_callable=tlw.update_workflow_params,
            op_kwargs={
                "wf_params": WF_PARAMS_EXPR_GA,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )

        # -- Load Google Ads Campaign -------------------------------------------------------------
        with TaskGroup(group_id='load_campaign') as tg_load_campaign:
            list_s3_files_campaign = PythonOperator(
                task_id="list_s3_files_campaign",
                python_callable=tls3.list_s3_modified_files,
                op_kwargs={
                    "args_file": "google_ads/list_s3_files_campaign.yaml",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            # Need a begin dummy operator for branching
            begin_insert_campaign = DummyOperator(task_id="begin_insert_campaign")
            skip_insert_campaign = DummyOperator(task_id="skip_insert_campaign")
            end_insert_campaign = DummyOperator(task_id="end_insert_campaign")
            end_campaign = DummyOperator(
                task_id="end_campaign",
                trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
            )

            check_new_files_found_campaign = BranchPythonOperator(
                task_id='check_new_files_found_campaign',
                python_callable=check_for_new_files,
                op_kwargs={
                    "args_file": "google_ads/s3_to_snowflake_campaign.yaml",
                    "skip_task_id": "google_ads_campaign.load_campaign.skip_insert_campaign",
                    "next_task_id": "google_ads_campaign.load_campaign.begin_insert_campaign",
                },
            )

            s3_to_sf_raw_campaign = PythonOperator(
                task_id="s3_to_sf_raw_campaign",
                python_callable=load_obj.s3_to_snowflake_load,
                op_kwargs={
                    "args_file": "google_ads/s3_to_sf_raw_campaign.yaml",
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert
            )

            run_dq_null_raw_campaign = PythonOperator(
                task_id="run_dq_null_raw_campaign",
                python_callable=tldq.run_dq_string,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'test_name': 'is_null',
                    'sql_query': tldq.gen_check_if_nulls(
                        tb_name='$raw_db.raw_google_ads_campaign',
                        field_list=['campaign'],
                        hard_alert=False,
                    )
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            insert_log_campaign = PythonOperator(
                task_id="insert_log_campaign",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/google/google_ads/insert_log_campaign.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            create_raw_flatten_campaign = PythonOperator(
                task_id="create_raw_flatten_campaign",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection": "Snowflake",
                    "sql_file": "ads/google/google_ads/create_raw_flatten_campaign.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert
            )

            run_dq_null_pk_raw_flatten_campaign = PythonOperator(
                task_id="run_dq_null_pk_raw_flatten_campaign",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_null_pk_raw_flatten_campaign',
                    'query_file': "ads/google/google_ads/dq_null_pk_raw_flatten_campaign.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            dedupe_campaign = PythonOperator(
                task_id="dedupe_campaign",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/google/google_ads/dedupe_campaign.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            run_dq_dedupe_campaign = PythonOperator(
                task_id="run_dq_dedupe_campaign",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_dedupe_campaign',
                    'query_file': "ads/google/google_ads/dq_dedupe_campaign.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            merge_campaign = PythonOperator(
                task_id="merge_campaign",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/google/google_ads/merge_campaign.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            run_dq_merge_campaign = PythonOperator(
                task_id="run_dq_merge_campaign",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_merge_campaign',
                    'query_file': "ads/google/google_ads/dq_merge_campaign.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            insert_campaign_report = PythonOperator(
                task_id="insert_campaign_report",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/google/google_ads/insert_campaign_report.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            run_dq_ads_campaign_report = PythonOperator(
                task_id="run_dq_ads_campaign_report",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_ads_campaign_report',
                    'query_file': "ads/google/google_ads/dq_ads_campaign_report.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            run_audit_campaign = PythonOperator(
                task_id="run_audit_campaign",
                python_callable=tla.run_audit,
                op_kwargs={
                    "table_name": "$curated_db.google_ads_campaign_report",
                    "wf_params": WF_PARAMS_EXPR_GA,
                    "ts_created_field": 'record_created_timestamp_utc',
                    "ts_updated_field": 'record_updated_timestamp_utc',
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            # Ads campaign workflow
            chain(
                list_s3_files_campaign,
                check_new_files_found_campaign,
                [begin_insert_campaign, skip_insert_campaign]
            )

            chain(
                begin_insert_campaign,
                s3_to_sf_raw_campaign,
                run_dq_null_raw_campaign,
                insert_log_campaign,
                create_raw_flatten_campaign,
                run_dq_null_pk_raw_flatten_campaign,
                dedupe_campaign,
                run_dq_dedupe_campaign,
                merge_campaign,
                run_dq_merge_campaign,
                insert_campaign_report,
                run_dq_ads_campaign_report,
                run_audit_campaign,
                end_insert_campaign,
                end_campaign,
            )

            skip_insert_campaign >> end_campaign

        # -- Load Google Ad Group Ad --------------------------------------------------------------
        with TaskGroup(group_id='load_ad_group_ad') as tg_load_ad_group_ad:
            list_s3_files_ad_group_ad = PythonOperator(
                task_id="list_s3_files_ad_group_ad",
                python_callable=tls3.list_s3_modified_files,
                op_kwargs={
                    "args_file": "google_ads/list_s3_files_ad_group_ad.yaml",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            # Need a begin dummy operator for branching
            begin_insert_ad_group_ad = DummyOperator(task_id="begin_insert_ad_group_ad")
            skip_insert_ad_group_ad = DummyOperator(task_id="skip_insert_ad_group_ad")
            end_insert_ad_group_ad = DummyOperator(task_id="end_insert_ad_group_ad")
            end_ad_group_ad = DummyOperator(
                task_id="end_ad_group_ad",
                trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
            )

            check_new_files_found_ad_group_ad = BranchPythonOperator(
                task_id='check_new_files_found_ad_group_ad',
                python_callable=check_for_new_files,
                op_kwargs={
                    "args_file": "google_ads/s3_to_snowflake_ad_group_ad.yaml",
                    "skip_task_id": "google_ads_campaign.load_ad_group_ad.skip_insert_ad_group_ad",
                    "next_task_id": "google_ads_campaign.load_ad_group_ad.begin_insert_ad_group_ad",
                },
            )

            s3_to_sf_raw_ad_group_ad = PythonOperator(
                task_id="s3_to_sf_raw_ad_group_ad",
                python_callable=load_obj.s3_to_snowflake_load,
                op_kwargs={
                    "args_file": "google_ads/s3_to_sf_raw_ad_group_ad.yaml",
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert
            )

            run_dq_null_raw_ad_group_ad = PythonOperator(
                task_id="run_dq_null_raw_ad_group_ad",
                python_callable=tldq.run_dq_string,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'test_name': 'is_null',
                    'sql_query': tldq.gen_check_if_nulls(
                        tb_name='$raw_db.raw_google_ads_ad_group_ad',
                        field_list=['ad_group_ad'],
                        hard_alert=False,
                    )
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            insert_log_ad_group_ad = PythonOperator(
                task_id="insert_log_ad_group_ad",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/google/google_ads/insert_log_ad_group_ad.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            create_raw_flatten_ad_group_ad = PythonOperator(
                task_id="create_raw_flatten_ad_group_ad",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection": "Snowflake",
                    "sql_file": "ads/google/google_ads/create_raw_flatten_ad_group_ad.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert
            )

            run_dq_null_pk_raw_flatten_ad_group_ad = PythonOperator(
                task_id="run_dq_null_pk_raw_flatten_ad_group_ad",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_null_pk_raw_flatten_ad_group_ad',
                    'query_file': "ads/google/google_ads/dq_null_pk_raw_flatten_ad_group_ad.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            dedupe_ad_group_ad = PythonOperator(
                task_id="dedupe_ad_group_ad",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/google/google_ads/dedupe_ad_group_ad.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            run_dq_dedupe_ad_group_ad = PythonOperator(
                task_id="run_dq_dedupe_ad_group_ad",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_dedupe_ad_group_ad',
                    'query_file': "ads/google/google_ads/dq_dedupe_ad_group_ad.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            merge_ad_group_ad = PythonOperator(
                task_id="merge_ad_group_ad",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/google/google_ads/merge_ad_group_ad.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            run_dq_merge_ad_group_ad = PythonOperator(
                task_id="run_dq_merge_ad_group_ad",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_merge_ad_group_ad',
                    'query_file': "ads/google/google_ads/dq_merge_ad_group_ad.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            run_audit_ad_group_ad = PythonOperator(
                task_id="run_audit_ad_group_ad",
                python_callable=tla.run_audit,
                op_kwargs={
                    "table_name": "$stage_db.merge_google_ads_ad_group_ad",
                    "wf_params": WF_PARAMS_EXPR_GA,
                    "ts_created_field": 'record_created_timestamp_utc',
                    "ts_updated_field": 'record_updated_timestamp_utc',
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            # Ads group ad workflow
            chain(
                list_s3_files_ad_group_ad,
                check_new_files_found_ad_group_ad,
                [begin_insert_ad_group_ad, skip_insert_ad_group_ad]
            )

            chain(
                begin_insert_ad_group_ad,
                s3_to_sf_raw_ad_group_ad,
                run_dq_null_raw_ad_group_ad,
                insert_log_ad_group_ad,
                create_raw_flatten_ad_group_ad,
                run_dq_null_pk_raw_flatten_ad_group_ad,
                dedupe_ad_group_ad,
                run_dq_dedupe_ad_group_ad,
                merge_ad_group_ad,
                run_dq_merge_ad_group_ad,
                run_audit_ad_group_ad,
                end_insert_ad_group_ad,
                end_ad_group_ad
            )

            skip_insert_ad_group_ad >> end_ad_group_ad

        # -- Load Google Adgroup ------------------------------------------------------------------
        with TaskGroup(group_id='load_adgroup') as tg_load_adgroup:
            list_s3_files_adgroup = PythonOperator(
                task_id="list_s3_files_adgroup",
                python_callable=tls3.list_s3_modified_files,
                op_kwargs={
                    "args_file": "google_ads/list_s3_files_adgroup.yaml",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            # Need a begin dummy operator for branching
            begin_insert_adgroup = DummyOperator(task_id="begin_insert_adgroup")
            skip_insert_adgroup = DummyOperator(task_id="skip_insert_adgroup")
            end_insert_adgroup = DummyOperator(task_id="end_insert_adgroup")
            end_adgroup = DummyOperator(
                task_id="end_adgroup",
                trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
            )

            check_new_files_found_adgroup = BranchPythonOperator(
                task_id='check_new_files_found_adgroup',
                python_callable=check_for_new_files,
                op_kwargs={
                    "args_file": "google_ads/s3_to_snowflake_adgroup.yaml",
                    "skip_task_id": "google_ads_campaign.load_adgroup.skip_insert_adgroup",
                    "next_task_id": "google_ads_campaign.load_adgroup.begin_insert_adgroup",
                },
            )

            s3_to_sf_raw_adgroup = PythonOperator(
                task_id="s3_to_sf_raw_adgroup",
                python_callable=load_obj.s3_to_snowflake_load,
                op_kwargs={
                    "args_file": "google_ads/s3_to_sf_raw_adgroup.yaml",
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert
            )

            run_dq_null_raw_adgroup = PythonOperator(
                task_id="run_dq_null_raw_adgroup",
                python_callable=tldq.run_dq_string,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'test_name': 'is_null',
                    'sql_query': tldq.gen_check_if_nulls(
                        tb_name='$raw_db.raw_google_ads_adgroup',
                        field_list=['ad_group'],
                        hard_alert=False,
                    )
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            insert_log_adgroup = PythonOperator(
                task_id="insert_log_adgroup",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/google/google_ads/insert_log_adgroup.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            create_raw_flatten_adgroup = PythonOperator(
                task_id="create_raw_flatten_adgroup",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection": "Snowflake",
                    "sql_file": "ads/google/google_ads/create_raw_flatten_adgroup.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert
            )

            run_dq_null_pk_raw_flatten_adgroup = PythonOperator(
                task_id="run_dq_null_pk_raw_flatten_adgroup",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_null_pk_raw_flatten_adgroup',
                    'query_file': "ads/google/google_ads/dq_null_pk_raw_flatten_adgroup.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            dedupe_adgroup = PythonOperator(
                task_id="dedupe_adgroup",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/google/google_ads/dedupe_adgroup.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            run_dq_dedupe_adgroup = PythonOperator(
                task_id="run_dq_dedupe_adgroup",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_dedupe_adgroup',
                    'query_file': "ads/google/google_ads/dq_dedupe_adgroup.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            merge_adgroup = PythonOperator(
                task_id="merge_adgroup",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/google/google_ads/merge_adgroup.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            run_dq_merge_adgroup = PythonOperator(
                task_id="run_dq_merge_adgroup",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_merge_adgroup',
                    'query_file': "ads/google/google_ads/dq_merge_adgroup.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            run_audit_adgroup = PythonOperator(
                task_id="run_audit_adgroup",
                python_callable=tla.run_audit,
                op_kwargs={
                    "table_name": "$stage_db.merge_google_ads_adgroup", # Replace with final table (usually FACT table)
                    "wf_params": WF_PARAMS_EXPR_GA,
                    "ts_created_field": 'record_created_timestamp_utc',
                    "ts_updated_field": 'record_updated_timestamp_utc',
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            # Adgroup workflow
            chain(
                list_s3_files_adgroup,
                check_new_files_found_adgroup,
                [begin_insert_adgroup, skip_insert_adgroup]
            )

            chain(
                begin_insert_adgroup,
                s3_to_sf_raw_adgroup,
                run_dq_null_raw_adgroup,
                insert_log_adgroup,
                create_raw_flatten_adgroup,
                run_dq_null_pk_raw_flatten_adgroup,
                dedupe_adgroup,
                run_dq_dedupe_adgroup,
                merge_adgroup,
                run_dq_merge_adgroup,
                run_audit_adgroup,
                end_insert_adgroup,
                end_adgroup
            )

            skip_insert_adgroup >> end_adgroup

        # -- Load Google Product Group --------------------------------------------------------
        with TaskGroup(group_id='load_product_group') as tg_load_product_group:
            list_s3_files_product_group = PythonOperator(
                task_id="list_s3_files_product_group",
                python_callable=tls3.list_s3_modified_files,
                op_kwargs={
                    "args_file": "google_ads/list_s3_files_product_group.yaml",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            # Need a begin dummy operator for branching
            begin_insert_product_group = DummyOperator(task_id="begin_insert_product_group")
            skip_insert_product_group = DummyOperator(task_id="skip_insert_product_group")
            end_insert_product_group = DummyOperator(task_id="end_insert_product_group")
            end_product_group = DummyOperator(
                task_id="end_product_group",
                trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
            )

            check_new_files_found_product_group = BranchPythonOperator(
                task_id='check_new_files_found_product_group',
                python_callable=check_for_new_files,
                op_kwargs={
                    "args_file": "google_ads/s3_to_snowflake_product_group.yaml",
                    "skip_task_id": "google_ads_campaign.load_product_group.skip_insert_product_group",
                    "next_task_id": "google_ads_campaign.load_product_group.begin_insert_product_group",
                },
            )

            s3_to_sf_raw_product_group = PythonOperator(
                task_id="s3_to_sf_raw_product_group",
                python_callable=load_obj.s3_to_snowflake_load,
                op_kwargs={
                    "args_file": "google_ads/s3_to_sf_raw_product_group.yaml",
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert
            )

            run_dq_null_raw_product_group = PythonOperator(
                task_id="run_dq_null_raw_product_group",
                python_callable=tldq.run_dq_string,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'test_name': 'is_null',
                    'sql_query': tldq.gen_check_if_nulls(
                        tb_name='$raw_db.raw_google_ads_product_group',
                        field_list=['product_group_view'],
                        hard_alert=False,
                    )
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            insert_log_product_group = PythonOperator(
                task_id="insert_log_product_group",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/google/google_ads/insert_log_product_group.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            create_raw_flatten_product_group = PythonOperator(
                task_id="create_raw_flatten_product_group",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection": "Snowflake",
                    "sql_file": "ads/google/google_ads/create_raw_flatten_product_group.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert
            )

            run_dq_null_pk_raw_flatten_product_group = PythonOperator(
                task_id="run_dq_null_pk_raw_flatten_product_group",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_null_pk_raw_flatten_product_group',
                    'query_file': "ads/google/google_ads/dq_null_pk_raw_flatten_product_group.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            dedupe_product_group = PythonOperator(
                task_id="dedupe_product_group",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/google/google_ads/dedupe_product_group.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            run_dq_dedupe_product_group = PythonOperator(
                task_id="run_dq_dedupe_product_group",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_dedupe_product_group',
                    'query_file': "ads/google/google_ads/dq_dedupe_product_group.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            merge_product_group = PythonOperator(
                task_id="merge_product_group",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection":"Snowflake",
                    "sql_file": "ads/google/google_ads/merge_product_group.sql",
                    "wf_params": WF_PARAMS_EXPR_GA,
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            run_dq_merge_product_group = PythonOperator(
                task_id="run_dq_merge_product_group",
                python_callable=tldq.run_dq_file,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'run_id': 'run_dq_merge_product_group',
                    'query_file': "ads/google/google_ads/dq_merge_product_group.yaml",
                },
                on_failure_callback=alerts.send_failure_alert,
            )

            run_audit_product_group = PythonOperator(
                task_id="run_audit_product_group",
                python_callable=tla.run_audit,
                op_kwargs={
                    "table_name": "$stage_db.merge_google_ads_product_group", # Replace with final table (usually FACT table)
                    "wf_params": WF_PARAMS_EXPR_GA,
                    "ts_created_field": 'record_created_timestamp_utc',
                    "ts_updated_field": 'record_updated_timestamp_utc',
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
            )

            # Product group workflow
            chain(
                list_s3_files_product_group,
                check_new_files_found_product_group,
                [begin_insert_product_group, skip_insert_product_group]
            )

            chain(
                begin_insert_product_group,
                s3_to_sf_raw_product_group,
                run_dq_null_raw_product_group,
                insert_log_product_group,
                create_raw_flatten_product_group,
                run_dq_null_pk_raw_flatten_product_group,
                dedupe_product_group,
                run_dq_dedupe_product_group,
                merge_product_group,
                run_dq_merge_product_group,
                run_audit_product_group,
                end_insert_product_group,
                end_product_group
            )

            skip_insert_product_group >> end_product_group

        # Google workflow
        chain(
            get_workflow_parameters,
            [
                tg_load_ad_group_ad,
                tg_load_adgroup,
                tg_load_campaign,
                tg_load_product_group
            ],
            update_workflow_parameters,
        )

    # Main workflow
    chain(
        begin,
        create_brand_mapping,
        (
            tg_facebook_ads,
            tg_google_ads_campaign,
        ),
        end
    )

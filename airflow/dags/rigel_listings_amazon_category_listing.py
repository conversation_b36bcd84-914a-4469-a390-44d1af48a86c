from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.models import Variable
import logging
from tasklib import alerts
import tasklib.sql as tlsql

log = logging.getLogger(__name__)

BUILD_NUM = '1'
RELEASE_DEF = '1'
DAG_ID = 'rigel_listings_amazon_category_listing'
WF_PARAMS_EXPR = "{'wf_name': '" + DAG_ID.upper() + "'}"

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2023, 5, 1),
    schedule_interval=Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily'),
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'ayush'},
    tags=['Rigel', 'Ayush']

) as dag:

    create_listings_amazon_category_listing = PythonOperator(
        task_id="create_listings_amazon_category_listing",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "rigel/listings/amazon_category_listing/create_listings_amazon_category_listing.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    chain(
         begin,
         create_listings_amazon_category_listing,
         end,
    )

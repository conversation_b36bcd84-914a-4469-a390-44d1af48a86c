import logging
from datetime import datetime, timedelta

from airflow import DAG
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.sensors.external_task import ExternalTaskSensor

import tasklib.dq as tldq
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts

DAG_ID = 'fact_amazon_ad_targets'
TRANSFORM_BASE = 'ads/amazon/' + DAG_ID
RELEASE_DEF = '1'
BUILD_NUM = '01'

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"

log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2021, 1, 1),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID,
            'author': 'abhishek'},
    tags=['Abhishek']
) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

stg_amazon_ad_all_targets = PythonOperator(
    task_id="stg_amazon_ad_all_targets",
    python_callable=tlsql.run_query_file,
    op_kwargs={
        "connection": "Snowflake",
        "sql_file": TRANSFORM_BASE + "/stg_amazon_ad_all_targets.sql",
        "wf_params": WF_PARAMS_EXPR},
    on_failure_callback=alerts.send_failure_alert,
    provide_context=True
)

run_dq_stg_amazon_ad_all_targets = PythonOperator(
    task_id="run_dq_stg_amazon_ad_all_targets",
    python_callable=tldq.run_dq_string,
    op_kwargs={
        'wk_name': DAG_ID,
        'tb_name': "$stage_db.stg_amazon_ad_all_targets",
        'test_name': 'key_fields_not_duplicate',
        'sql_query': tldq.gen_check_unique_key(
            tb_name="$stage_db.stg_amazon_ad_all_targets",
            field_list=['pk'],
            hard_alert=True
        )
        },
    on_failure_callback=alerts.send_failure_alert
)

merge_amazon_ad_all_targets = PythonOperator(
    task_id="merge_amazon_ad_all_targets",
    python_callable=tlsql.run_query_file,
    op_kwargs={
        "connection": "Snowflake",
        "sql_file": TRANSFORM_BASE + "/merge_amazon_ad_all_targets.sql",
        "wf_params": WF_PARAMS_EXPR},
    on_failure_callback=alerts.send_failure_alert,
    provide_context=True
)

# --- Audit the data checks ---

import tasklib.audit as tla

run_audit = PythonOperator(
    task_id="run_audit",
    python_callable=tla.run_audit,
    op_kwargs={"table_name": "$curated_db.fact_amazon_ad_targets",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                },
    on_failure_callback=alerts.send_failure_alert
)

# --- Update workflow status ---

update_workflow_params = PythonOperator(
    task_id="update_workflow_parameters",
    python_callable=tlw.update_workflow_params,
    op_kwargs={"wf_params": WF_PARAMS_EXPR},
    on_failure_callback=alerts.send_failure_alert,
    provide_context=True
)


begin = DummyOperator(task_id="begin")
end = DummyOperator(task_id="end")

wait_on_amazon_ads_target_reports = ExternalTaskSensor(
    task_id="wait_on_amazon_ads_target_reports",
    external_dag_id="amazon_ads_target_reports",
    external_task_id="end",
    allowed_states=["success"],
    #execution_delta=timedelta(minutes=15),
    poke_interval=60 * 3,
    mode="reschedule",
    timeout=60 * 60 * 2,
    on_failure_callback=alerts.send_failure_alert
)

wait_on_amazon_ads_base_data = ExternalTaskSensor(
    task_id="wait_on_amazon_ads_base_data",
    external_dag_id="amazon_ads_base_data",
    external_task_id="end",
    allowed_states=["success"],
    #execution_delta=timedelta(minutes=45),
    poke_interval=60 * 3,
    mode="reschedule",
    timeout=60 * 60 * 2,
    on_failure_callback=alerts.send_failure_alert
)

wait_on_amazon_ads_sp_search_targeting_reports = ExternalTaskSensor(
    task_id="wait_on_amazon_ads_sp_search_targeting_reports",
    external_dag_id="amazon_ads_sp_search_targeting_reports",
    external_task_id="end",
    allowed_states=["success"],
    #execution_delta=timedelta(minutes=45),
    poke_interval=60 * 3,
    mode="reschedule",
    timeout=60 * 60 * 2,
    on_failure_callback=alerts.send_failure_alert
)

begin >> get_workflow_params >> [wait_on_amazon_ads_target_reports, wait_on_amazon_ads_base_data, wait_on_amazon_ads_sp_search_targeting_reports] >> stg_amazon_ad_all_targets >> run_dq_stg_amazon_ad_all_targets >> merge_amazon_ad_all_targets >> run_audit >> update_workflow_params >> end

from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.sensors.external_task import ExternalTaskSensor
import logging

log = logging.getLogger(__name__)

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"
DAG_ID ="AMAZON_TRANSACTIONAL_RECORDS"
with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022, 12, 8),
    schedule_interval="30 2,8,14,18 * * *",
    catchup=False,
    max_active_runs=1,
    tags=['amazon', 'transactions', 'orders','Vikas'],
    params={
        "workflow_name": "AMAZON_TRANSACTIONAL_RECORDS",
        "author": "vikas"
    },
) as dag:
    import tasklib.workflow as tlw
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    create_stg_inventory_transactional_records = PythonOperator(
        task_id="create_stg_inventory_transactional_records",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "transactional_records/amazon/create_stg_inventory_transactional_records.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )
    # DQ to check if country is null

    create_stg_order_transactional_records = PythonOperator(
        task_id="create_stg_order_transactional_records",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "transactional_records/amazon/create_stg_order_transactional_records.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_amazon_transactional_records = PythonOperator(
        task_id="create_stg_amazon_transactional_records",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "transactional_records/amazon/create_stg_amazon_transactional_records.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_settlement_transactional_records = PythonOperator(
        task_id="create_stg_settlement_transactional_records",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "transactional_records/amazon/create_stg_settlement_transactional_records.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    publish_final_curated = PythonOperator(
        task_id="publish_final_curated",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "transactional_records/amazon/publish_final_curated.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_audit_amazon_transactional_records = PythonOperator(
            task_id="run_audit_amazon_transactional_records",
            python_callable=tla.run_audit,
            op_kwargs={"table_name": "$curated_db.amazon_transactional_records",
                       "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
    )

    run_dq_is_unique_constraint_task = PythonOperator(
        task_id="run_dq_is_unique_constraint_task",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': '$curated_db.amazon_transactional_records',
            'test_name': 'is_unique',
            'sql_query': tldq.gen_check_unique_key(
                tb_name='$curated_db.amazon_transactional_records',
                field_list=['"item_pk"'],
                hard_alert=True,
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_is_null_column_task = PythonOperator(
        task_id="run_dq_is_null_column_task",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': '$curated_db.amazon_transactional_records',
            'test_name': 'is_null',
            'sql_query': tldq.gen_check_if_nulls(
                tb_name='$curated_db.amazon_transactional_records',
                field_list=['"sku"','"source_type"','"country"'],
                hard_alert=True,
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(
        task_id="begin"
    )

    end = DummyOperator(task_id="end")
    dummy = DummyOperator(task_id="dummy")

    chain(
        begin,
        get_workflow_params,
        dummy,
        [create_stg_order_transactional_records, create_stg_inventory_transactional_records],
        create_stg_amazon_transactional_records,
        create_stg_settlement_transactional_records,
        publish_final_curated,
        run_dq_is_null_column_task,
        run_dq_is_unique_constraint_task,
        run_audit_amazon_transactional_records,
        update_workflow_params,
        end,
    )

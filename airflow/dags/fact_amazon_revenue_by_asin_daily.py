import json
import logging
import os
from airflow import DAG
from airflow.operators.dummy_operator import Dummy<PERSON>perator
from airflow.operators.python import Python<PERSON>perator
from airflow.providers.amazon.aws.transfers.s3_to_sftp import S3ToSFTPOperator
from airflow.sensors.external_task import ExternalTaskSensor
from datetime import datetime

from tasklib.alerts import send_failure_alert
from tasklib.config import get_s3_staging_bucket
from tasklib.s3 import write_snowflake_data_to_s3
from tasklib.sql import run_query_file

log = logging.getLogger(__name__)

_WF_PARAMS_EXPR = json.dumps({'run_date':'{{ ds }}'})
_S3_BUCKET = get_s3_staging_bucket()
_S3_KEY = 'data_exchange/outbound/skai/revenue/asin_revenue_data_{{ ds }}.tsv'

with DAG(
    dag_id='fact_amazon_revenue_by_asin_daily',
    start_date=datetime(2022, 1, 1),
    schedule_interval='@daily',
    catchup=False,
    max_active_runs=1,
    params={
        'workflow_name': 'FACT_REVENUE_BY_ASIN_DAILY',
        'author': 'harshad'
    },
    tags=['Harshad'],
) as dag:

    begin = DummyOperator(task_id='begin')

    wait_on_amazon_orders_task = ExternalTaskSensor(
        task_id='wait_on_amazon_orders',
        external_dag_id='amazon_orders',
        external_task_id='end',
        timeout=60 * 60 * 4,
        allowed_states=['success'])

    update_revenue_task = PythonOperator(
        task_id='update_revenue',
        python_callable=run_query_file,
        provide_context=True,
        op_kwargs={
            'connection': 'Snowflake',
            'sql_file': 'revenue_by_asin/update_revenue_data.sql',
            'wf_params': _WF_PARAMS_EXPR,
        },
        on_failure_callback=send_failure_alert,
    )

    write_snowflake_data_to_s3_task = PythonOperator(
        task_id='write_data_to_s3',
        python_callable=write_snowflake_data_to_s3,
        provide_context=True,
        op_kwargs={
            'sql_file': 'revenue_by_asin/unload_revenue_data.sql',
            'wf_params': _WF_PARAMS_EXPR,
            's3_bucket': _S3_BUCKET,
            's3_key': _S3_KEY,
        },
        on_failure_callback=send_failure_alert,
    )

    send_data_s3_to_sftp_task = S3ToSFTPOperator(
        task_id='send_data_s3_to_sftp',
        sftp_conn_id='skai_sftp_conn_id',
        sftp_path=os.path.basename(_S3_KEY),
        s3_bucket=_S3_BUCKET,
        s3_key=_S3_KEY,
        on_failure_callback=send_failure_alert,
    )

    end = DummyOperator(task_id='end')

    (
        begin >>
        wait_on_amazon_orders_task >>
        update_revenue_task >>
        write_snowflake_data_to_s3_task >>
        send_data_s3_to_sftp_task >>
        end
    )


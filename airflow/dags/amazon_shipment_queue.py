"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.utils.task_group import TaskGroup
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON>ythonOperator, PythonOperator
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib.loader import check_for_new_files

from tasklib import alerts
from tasklib.loader import S3ToSnowflake
load_obj = S3ToSnowflake()
BUILD_NUM = '1'
DAG_ID = 'amazon_shipping_queue'
RELEASE_DEF = '1'

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"


log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    
    start_date=datetime(2021, 12, 1),
    schedule_interval='@daily',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'FACT_AMAZON_INBOUND_SHIPMENTS',
            'author': 'Brian'},
    tags=['Srichand', 'Raptor', 'Goessor']
) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
        
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_params = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load fact_amazon_inbound_shipments ---
    
    with TaskGroup(group_id='load_fact_amazon_inbound_shipments') as tg_fact_amazon_inbound_shipments:
        list_s3_files_fact_amazon_inbound_shipments_task = PythonOperator(
            task_id="list_s3_files_fact_amazon_inbound_shipments",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inbound_shipments/list_s3_fact_amazon_inbound_shipments.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            
        )

        list_s3_files_fact_amazon_inbound_shipments_task_goessor = PythonOperator(
            task_id="list_s3_files_fact_amazon_inbound_shipments_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inbound_shipments/list_s3_fact_amazon_inbound_shipments_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_fact_amazon_inbound_shipments_task = DummyOperator(task_id="begin_insert_fact_amazon_inbound_shipments")
        skip_insert_fact_amazon_inbound_shipments_task = DummyOperator(task_id="skip_insert_fact_amazon_inbound_shipments")
        end_insert_fact_amazon_inbound_shipments_task = DummyOperator(task_id="end_insert_fact_amazon_inbound_shipments")


        s3_to_snowflake_fact_amazon_inbound_shipments_task = PythonOperator(
            task_id="s3_to_snowflake_fact_amazon_inbound_shipments",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "shipping_queue/fact_amazon_inbound_shipments/s3_to_sf_raw_fact_amazon_inbound_shipments.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_fact_amazon_inbound_shipments_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_fact_amazon_inbound_shipments_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "shipping_queue/fact_amazon_inbound_shipments/s3_to_sf_raw_fact_amazon_inbound_shipments_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_fact_amazon_inbound_shipments_task = BranchPythonOperator(
            task_id='check_new_files_found_fact_amazon_inbound_shipments',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "fact_amazon_inbound_shipments/s3_to_snowflake_fact_amazon_inbound_shipments.yaml",
                       "args_file_second": "fact_amazon_inbound_shipments/s3_to_snowflake_fact_amazon_inbound_shipments_goessor.yaml",
                       "skip_task_id": "load_fact_amazon_inbound_shipments.skip_insert_fact_amazon_inbound_shipments",
                       "next_task_id": "load_fact_amazon_inbound_shipments.begin_insert_fact_amazon_inbound_shipments"
            },
        )

        insert_log_fact_amazon_inbound_shipments_task = PythonOperator(
            task_id="insert_log_fact_amazon_inbound_shipments",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments/insert_log_fact_amazon_inbound_shipments.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )


        run_uniqueness_check_stg_inbound_shipments_task = PythonOperator(
                task_id="run_uniqueness_check_stg_inbound_shipments",
                python_callable=tldq.run_dq_string,
                op_kwargs={
                    'wk_name': DAG_ID,
                    'tb_name': "$stage_db.STG_FACT_AMAZON_INBOUND_SHIPMENTS",
                    'test_name': 'pk_not_duplicate',
                    'sql_query': tldq.gen_check_unique_key(
                        tb_name="$stage_db.STG_FACT_AMAZON_INBOUND_SHIPMENTS", 
                        field_list=['SHIPMENT_ID','SHIPMENT_STATUS'],
                        hard_alert=True
                    )
                },
                on_failure_callback=alerts.send_failure_alert
            )

        dedupe_fact_amazon_inbound_shipments_task = PythonOperator(
            task_id="dedupe_fact_amazon_inbound_shipments",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments/dedupe_fact_amazon_inbound_shipments.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        run_dq_is_null_dedupe_task = PythonOperator(
            task_id="run_dq_is_null_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_fact_amazon_inbound_shipments',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_fact_amazon_inbound_shipments', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        run_dq_is_unique_dedupe_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_fact_amazon_inbound_shipments',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_fact_amazon_inbound_shipments', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        

        create_stg_fact_amazon_inbound_shipments_task = PythonOperator(
            task_id="create_stg_fact_amazon_inbound_shipments",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments/stg_fact_amazon_inbound_shipments.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        merge_fact_amazon_inbound_shipments_task = PythonOperator(
            task_id="merge_fact_amazon_inbound_shipments",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments/fact_amazon_inbound_shipments.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_audit_fact_amazon_inbound_shipments_task = PythonOperator(
            task_id="run_audit_fact_amazon_inbound_shipments",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_amazon_inbound_shipments",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            [list_s3_files_fact_amazon_inbound_shipments_task, list_s3_files_fact_amazon_inbound_shipments_task_goessor] >>
            check_new_files_found_fact_amazon_inbound_shipments_task >>
            [begin_insert_fact_amazon_inbound_shipments_task, skip_insert_fact_amazon_inbound_shipments_task]
        )

        (
            begin_insert_fact_amazon_inbound_shipments_task >> 
            s3_to_snowflake_fact_amazon_inbound_shipments_task >>
            s3_to_snowflake_fact_amazon_inbound_shipments_task_goessor >>
            insert_log_fact_amazon_inbound_shipments_task >>
            dedupe_fact_amazon_inbound_shipments_task >>
            [ run_dq_is_null_dedupe_task ,
            run_dq_is_unique_dedupe_task
            ] >>
            create_stg_fact_amazon_inbound_shipments_task >>
            run_uniqueness_check_stg_inbound_shipments_task >>
            merge_fact_amazon_inbound_shipments_task>>
            run_audit_fact_amazon_inbound_shipments_task >>
            end_insert_fact_amazon_inbound_shipments_task
        )

    with TaskGroup(group_id='load_fact_amazon_inbound_shipments_items') as tg_fact_amazon_inbound_shipments_items:
        list_s3_files_fact_amazon_inbound_shipments_items_task = PythonOperator(
            task_id="list_s3_files_fact_amazon_inbound_shipments_items",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inbound_shipments_items/list_s3_fact_amazon_inbound_shipments_items.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        list_s3_files_fact_amazon_inbound_shipments_items_task_goessor = PythonOperator(
            task_id="list_s3_files_fact_amazon_inbound_shipments_items_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inbound_shipments_items/list_s3_fact_amazon_inbound_shipments_items_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_fact_amazon_inbound_shipments_items_task = DummyOperator(
            task_id="begin_insert_fact_amazon_inbound_shipments_items")
        skip_insert_fact_amazon_inbound_shipments_items_task = DummyOperator(
            task_id="skip_insert_fact_amazon_inbound_shipments_items")
        end_insert_fact_amazon_inbound_shipments_items_task = DummyOperator(
            task_id="end_insert_fact_amazon_inbound_shipments_items")

        complete_fact_amazon_inbound_shipments_items_task = DummyOperator(
            task_id="complete_fact_amazon_inbound_shipments_items")

        s3_to_snowflake_fact_amazon_inbound_shipments_items_task = PythonOperator(
            task_id="s3_to_snowflake_fact_amazon_inbound_shipments_items",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "shipping_queue/fact_amazon_inbound_shipments_items/s3_to_sf_raw_fact_amazon_inbound_shipments_items.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_fact_amazon_inbound_shipments_items_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_fact_amazon_inbound_shipments_items_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "shipping_queue/fact_amazon_inbound_shipments_items/s3_to_sf_raw_fact_amazon_inbound_shipments_items_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_fact_amazon_inbound_shipments_items_task = BranchPythonOperator(
            task_id='check_new_files_found_fact_amazon_inbound_shipments_items',
            python_callable=check_for_new_files,
            op_kwargs={
                "args_file": "fact_amazon_inbound_shipments_items/s3_to_snowflake_fact_amazon_inbound_shipments_items.yaml",
                "args_file_second": "fact_amazon_inbound_shipments_items/s3_to_snowflake_fact_amazon_inbound_shipments_items_goessor.yaml",
                "skip_task_id": "load_fact_amazon_inbound_shipments_items.skip_insert_fact_amazon_inbound_shipments_items",
                "next_task_id": "load_fact_amazon_inbound_shipments_items.begin_insert_fact_amazon_inbound_shipments_items"
                },
        )

        insert_log_fact_amazon_inbound_shipments_items_task = PythonOperator(
            task_id="insert_log_fact_amazon_inbound_shipments_items",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments_items/insert_log_fact_amazon_inbound_shipments_items.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_uniqueness_check_stg_inbound_shipments_items = PythonOperator(
            task_id="run_uniqueness_check_stg_inbound_shipments_items",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$stage_db.STG_FACT_AMAZON_INBOUND_SHIPMENTS_ITEMS",
                'test_name': 'pk_not_duplicate',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.STG_FACT_AMAZON_INBOUND_SHIPMENTS_ITEMS",
                    field_list=['FNSKU', 'PREP_INSTRUCTION', 'SHIPMENT_ID', 'SELLER_ID'],
                    hard_alert=True
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_fact_amazon_inbound_shipments_items_task = PythonOperator(
            task_id="dedupe_fact_amazon_inbound_shipments_items",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments_items/dedupe_fact_amazon_inbound_shipments_items.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        create_stg_fact_amazon_inbound_shipments_items_task = PythonOperator(
            task_id="create_stg_fact_amazon_inbound_shipments_items",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments_items/stg_fact_amazon_inbound_shipments_items.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        merge_fact_amazon_inbound_shipments_items_task = PythonOperator(
            task_id="merge_fact_amazon_inbound_shipments_items",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments_items/fact_amazon_inbound_shipments_items.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        insert_fact_amazon_inbound_shipments_items_history_task = PythonOperator(
            task_id="insert_fact_amazon_inbound_shipments_items_history",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments_items/fact_amazon_inbound_shipments_items_w_history.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        update_fact_amazon_inbound_shipments_items_task = PythonOperator(
            task_id="update_fact_amazon_inbound_shipments_items",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments_items/update_fact_amazon_inbound_shipment_items.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        update_fact_amazon_inbound_shipments_items_history_task = PythonOperator(
            task_id="update_fact_amazon_inbound_shipments_items_history",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments_items/update_fact_amazon_shipment_item_w_history.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
                [list_s3_files_fact_amazon_inbound_shipments_items_task, list_s3_files_fact_amazon_inbound_shipments_items_task_goessor] >>
                check_new_files_found_fact_amazon_inbound_shipments_items_task >>
                [begin_insert_fact_amazon_inbound_shipments_items_task,
                 skip_insert_fact_amazon_inbound_shipments_items_task]
        )

        (
                begin_insert_fact_amazon_inbound_shipments_items_task >>
                s3_to_snowflake_fact_amazon_inbound_shipments_items_task >>
                s3_to_snowflake_fact_amazon_inbound_shipments_items_task_goessor >>
                insert_log_fact_amazon_inbound_shipments_items_task >>
                dedupe_fact_amazon_inbound_shipments_items_task >>
                create_stg_fact_amazon_inbound_shipments_items_task >>
                run_uniqueness_check_stg_inbound_shipments_items >>
                [
                    merge_fact_amazon_inbound_shipments_items_task,
                    insert_fact_amazon_inbound_shipments_items_history_task
                ] >>
                complete_fact_amazon_inbound_shipments_items_task >>
                [
                    update_fact_amazon_inbound_shipments_items_task,
                    update_fact_amazon_inbound_shipments_items_history_task
                ] >>
                end_insert_fact_amazon_inbound_shipments_items_task
        )

    with TaskGroup(
            group_id='load_fact_amazon_inbound_shipments_transport_details') as tg_fact_amazon_inbound_shipments_transport_details:
        list_s3_files_fact_amazon_inbound_shipments_transport_details_task = PythonOperator(
            task_id="list_s3_files_fact_amazon_inbound_shipments_transport_details",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inbound_shipments_transport_details/list_s3_fact_amazon_inbound_shipments_transport_details.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_fact_amazon_inbound_shipments_transport_details_task_goessor = PythonOperator(
            task_id="list_s3_files_fact_amazon_inbound_shipments_transport_details_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fact_amazon_inbound_shipments_transport_details/list_s3_fact_amazon_inbound_shipments_transport_details_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_fact_amazon_inbound_shipments_transport_details_task = DummyOperator(
            task_id="begin_insert_fact_amazon_inbound_shipments_transport_details")
        skip_insert_fact_amazon_inbound_shipments_transport_details_task = DummyOperator(
            task_id="skip_insert_fact_amazon_inbound_shipments_transport_details")
        end_insert_fact_amazon_inbound_shipments_transport_details_task = DummyOperator(
            task_id="end_insert_fact_amazon_inbound_shipments_transport_details")

        s3_to_snowflake_fact_amazon_inbound_shipments_transport_details_task = PythonOperator(
            task_id="s3_to_snowflake_fact_amazon_inbound_shipments_transport_details",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "shipping_queue/fact_amazon_inbound_shipments_transport_details/s3_to_sf_raw_fact_amazon_inbound_shipments_transport_details.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        s3_to_snowflake_fact_amazon_inbound_shipments_transport_details_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_fact_amazon_inbound_shipments_transport_details_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "shipping_queue/fact_amazon_inbound_shipments_transport_details/s3_to_sf_raw_fact_amazon_inbound_shipments_transport_details_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_fact_amazon_inbound_shipments_transport_details_task = BranchPythonOperator(
            task_id='check_new_files_found_fact_amazon_inbound_shipments_transport_details',
            python_callable=check_for_new_files,
            op_kwargs={
                "args_file": "fact_amazon_inbound_shipments_transport_details/s3_to_snowflake_fact_amazon_inbound_shipments_transport_details.yaml",
                "args_file_second": "fact_amazon_inbound_shipments_transport_details/s3_to_snowflake_fact_amazon_inbound_shipments_transport_details_goessor.yaml",
                "skip_task_id": "load_fact_amazon_inbound_shipments_transport_details.skip_insert_fact_amazon_inbound_shipments_transport_details",
                "next_task_id": "load_fact_amazon_inbound_shipments_transport_details.begin_insert_fact_amazon_inbound_shipments_transport_details"
                },
        )

        insert_log_fact_amazon_inbound_shipments_transport_details_task = PythonOperator(
            task_id="insert_log_fact_amazon_inbound_shipments_transport_details",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments_transport_details/insert_log_fact_amazon_inbound_shipments_transport_details.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        insert_stg_fact_amazon_inbound_shipments_transport_details_task = PythonOperator(
            task_id="insert_stg_fact_amazon_inbound_shipments_transport_details",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments_transport_details/stg_fact_amazon_inbound_shipments_transport_details.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        insert_fact_amazon_inbound_shipments_transport_details_task = PythonOperator(
            task_id="insert_fact_amazon_inbound_shipments_transport_details",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments_transport_details/fact_amazon_inbound_shipments_transport_details.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_uniqueness_check_stg_inbound_shipments_transport_details_task = PythonOperator(
            task_id="run_uniqueness_check_stg_inbound_shipments_transport_details",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$stage_db.STG_FACT_AMAZON_INBOUND_SHIPMENTS_TRANSPORT_DETAILS",
                'test_name': 'pk_not_duplicate',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name="$stage_db.STG_FACT_AMAZON_INBOUND_SHIPMENTS_TRANSPORT_DETAILS",
                    field_list=['SHIPMENT_ID', 'SELLER_ID'],
                    hard_alert=True
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_fact_amazon_inbound_shipments_transport_details_task = PythonOperator(
            task_id="dedupe_fact_amazon_inbound_shipments_transport_details",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "shipping_queue/fact_amazon_inbound_shipments_transport_details/dedupe_fact_amazon_inbound_shipments_transport_details.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
                [list_s3_files_fact_amazon_inbound_shipments_transport_details_task, list_s3_files_fact_amazon_inbound_shipments_transport_details_task_goessor] >>
                check_new_files_found_fact_amazon_inbound_shipments_transport_details_task >>
                [begin_insert_fact_amazon_inbound_shipments_transport_details_task,
                 skip_insert_fact_amazon_inbound_shipments_transport_details_task]
        )

        (
                begin_insert_fact_amazon_inbound_shipments_transport_details_task >>
                s3_to_snowflake_fact_amazon_inbound_shipments_transport_details_task >>
                s3_to_snowflake_fact_amazon_inbound_shipments_transport_details_task_goessor >>
                insert_log_fact_amazon_inbound_shipments_transport_details_task >>
                dedupe_fact_amazon_inbound_shipments_transport_details_task >>
                insert_stg_fact_amazon_inbound_shipments_transport_details_task >>
                run_uniqueness_check_stg_inbound_shipments_transport_details_task >>
                insert_fact_amazon_inbound_shipments_transport_details_task >>
                end_insert_fact_amazon_inbound_shipments_transport_details_task
        )

    # ---- Main branch ----
    chain(
       begin,
       get_workflow_params,
       [tg_fact_amazon_inbound_shipments,
        tg_fact_amazon_inbound_shipments_items,
        tg_fact_amazon_inbound_shipments_transport_details],
       update_workflow_params,
       end
    )
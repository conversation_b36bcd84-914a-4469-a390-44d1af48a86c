import json
import logging
import uuid
import warnings
from datetime import datetime, timedelta
from airflow import DAG
from airflow.models import Variable
from airflow.operators.dummy_operator import DummyOperator
from airflow.providers.http.operators.http import SimpleHttpOperator
from airflow.providers.http.sensors.http import HttpSensor
from airflow.operators.python import Branch<PERSON>ythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule
from tasklib import alerts

warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)

API_ENDPOINT_CONN_ID = 'insights_engine_api_endpoint'
DAG_ID = 'dsie_pricing_insights'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@hourly')  # Default to hourly runs


def get_snapshot_date():
    '''
    Gets the snapshot date to run. Uptil 6pm every day, return the previous date; after 6pm return the same date
    This is done to allow the daily jobs to catchup for today's data.
    '''
    # return datetime.strftime(datetime.utcnow() - timedelta(hours=18), '%Y-%m-%d')
    import pytz
    utc_time = datetime.utcnow()
    local_time_adjusted = pytz.utc.localize(utc_time).astimezone(pytz.timezone('US/Pacific')) - timedelta(hours=18)
    return datetime.strftime(local_time_adjusted, '%Y-%m-%d')


def check_if_data_ready(**kwargs):
    '''
    Check if the data replication (snowflake to postgres) added any new rows.
    If yes, run the next steps including insights api call
    If no, skip the api call
    '''
    from db_connectors.sf_connector import Snowflake
    ti = kwargs['ti']
    sf_client = Snowflake()
    snapshot_date = get_snapshot_date()
    query = f"""SELECT * FROM sandbox.ops.pricing_engine_v0  WHERE snapshot_date='{snapshot_date}'"""
    _, num_rows = sf_client.get_data(query)
    if num_rows > 0:
        return 'get_insights.replicate_asin_sf_to_pg'
    return 'skip_insights'


def check_if_data_replicated(**kwargs):
    '''
    Check if the data replication (snowflake to postgres) added any new rows.
    If yes, run the next steps including insights api call
    If no, skip the api call
    '''
    from db_connectors.sf_connector import Snowflake
    ti = kwargs['ti']
    status = ti.xcom_pull(task_ids='get_insights.replicate_asin_sf_to_pg')
    logger.info(f'Job status: {status}')
    if status:
        return 'get_insights.insights_api_call'
    return 'get_insights.skip_no_new_data'


def generate_payload():
    '''
    Generate the payload for the Insights API call. The snapshot_date is chosen to be one day prior,
    based on UTC time.
    '''
    request_id = str(uuid.uuid4())
    snapshot_date = get_snapshot_date()
    payload = {
        "request_id": request_id,
        "snapshot_date": snapshot_date,
        "entity_type": "DYNAMIC_PRICING",
        "user_id": "hydy"
    }
    return json.dumps(payload)


def check_response(response):
    '''
    Monitor the Insights API job status.
    The function returns True if status="completed" else it returns False.
    Any error in reading status does not cause the job to fail, as we might
    see intermittent errors in the API call
    '''
    try:
        json_response = response.json()
        print(f'RESPONSE: {json_response}')
        status = json_response.get('status', '').lower()
        if status == 'completed':
            return True
    except Exception as e:
        print(f'API ERROR - {e}')
    return False


with DAG(
        dag_id=DAG_ID,
        start_date=datetime(2022, 11, 22),
        schedule_interval=DAG_SCHEDULE,
        catchup=False,
        max_active_runs=1,
        params={"author": "nagesh"},
        tags=['Nagesh'],
) as dag:
    import tasklib.replication as tlr

    check_if_data_ready_task = BranchPythonOperator(
        task_id='check_if_data_ready',
        python_callable=check_if_data_ready,
        provide_context=True,
    )

    skip_insights_task = DummyOperator(task_id="skip_insights")
    with TaskGroup(group_id="get_insights") as get_insights_task:
        replicate_asin_sf_to_pg_task = PythonOperator(
            task_id="replicate_asin_sf_to_pg",
            python_callable=tlr.replicate_data,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={"args_file": "data_replication/sf_to_pg/dsie_pricing.yaml"}
        )

        replicate_tacos_sf_to_pg_task = PythonOperator(
            task_id="replicate_tacos_sf_to_pg",
            python_callable=tlr.replicate_data,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={"args_file": "data_replication/sf_to_pg/dsie_pricing_tacos.yaml"}
        )

        check_if_data_replicated_task = BranchPythonOperator(
            task_id='check_if_data_replicated',
            python_callable=check_if_data_replicated,
            provide_context=True,
        )

        skip_no_new_data_task = DummyOperator(task_id="skip_no_new_data")

        insights_api_call_task = SimpleHttpOperator(
            task_id="insights_api_call",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='insights/items/async',
            method='PUT',
            data=generate_payload(),
            headers={"Content-Type": "application/json", "Authorization": "Bearer dummy_jwt"},
            response_filter=lambda response: response.json(),
            log_response=True,
        )

        insights_api_status_monitor_task = HttpSensor(
            task_id='insights_api_status_monitor',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            headers={"Authorization": "Bearer dummy_jwt"},
            endpoint="requests/{{ti.xcom_pull(task_ids='get_insights.insights_api_call')}}",
            response_check=lambda response: check_response(response),
            poke_interval=300,  # Check the job status every 5 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=10800,  # Job timeout after 3 hours
            extra_options={'check_response': False},
        )

        replicate_asin_insights_pg_to_sf_task = PythonOperator(
            task_id="replicate_asin_insights_pg_to_sf",
            python_callable=tlr.replicate_data,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={"args_file": "data_replication/pg_to_sf/dsie_pricing_insights.yaml"}
        )

        replicate_asin_sf_to_pg_task >> replicate_tacos_sf_to_pg_task >> check_if_data_replicated_task >> [insights_api_call_task, skip_no_new_data_task]
        insights_api_call_task >> insights_api_status_monitor_task >> replicate_asin_insights_pg_to_sf_task

    begin_task = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end_task = DummyOperator(
        task_id="end",
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    begin_task >> check_if_data_ready_task >> [get_insights_task, skip_insights_task] >> end_task

import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'brand_ebitda_revenue_daily_targets'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '30 8 15 * *')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2025, 6, 30),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'BRAND_EBITDA_REVENUE_DAILY_TARGETS',
            'author': 'leav'},
    tags=['leav', 'Raptor']
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}

    )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load brand_ebitda_revenue_daily_targets ---

    with TaskGroup(group_id='load_brand_ebitda_revenue_daily_targets') as tg_brand_ebitda_revenue_daily_targets:
        list_s3_files_brand_ebitda_revenue_daily_targets_task = PythonOperator(
            task_id="list_s3_files_brand_ebitda_revenue_daily_targets",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "brand_ebitda_framework/list_s3_brand_ebitda_revenue_daily_targets.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        s3_to_snowflake_brand_ebitda_revenue_daily_targets_task = PythonOperator(
            task_id="s3_to_snowflake_brand_ebitda_revenue_daily_targets",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "brand_ebitda_framework/s3_to_sf_raw_brand_ebitda_revenue_daily_targets.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_brand_ebitda_revenue_daily_targets_task = PythonOperator(
            task_id="dedupe_brand_ebitda_revenue_daily_targets",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/revenue_targets/dedupe_brand_ebitda_revenue_daily_targets.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_brand_ebitda_revenue_daily_targets',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_brand_ebitda_revenue_daily_targets',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_soft_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_soft",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_brand_ebitda_revenue_daily_targets',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_brand_ebitda_revenue_daily_targets',
                    field_list=['pk'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )


        merge_stage_brand_ebitda_revenue_daily_targets_task = PythonOperator(
            task_id="merge_stage_brand_ebitda_revenue_daily_targets",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/revenue_targets/merge_stage_brand_ebitda_revenue_daily_targets.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )



        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_brand_ebitda_revenue_daily_targets',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_brand_ebitda_revenue_daily_targets',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_brand_ebitda_revenue_daily_targets',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_brand_ebitda_revenue_daily_targets',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )


        run_audit_brand_ebitda_revenue_daily_targets_task = PythonOperator(
            task_id="run_audit_brand_ebitda_revenue_daily_targets",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_brand_ebitda_revenue_daily_targets",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )


        (
            list_s3_files_brand_ebitda_revenue_daily_targets_task >>
            s3_to_snowflake_brand_ebitda_revenue_daily_targets_task >>
            dedupe_brand_ebitda_revenue_daily_targets_task >>
            [run_dq_is_unique_dedupe_pk_hard_task, run_dq_is_null_dedupe_pk_soft_task] >>
            merge_stage_brand_ebitda_revenue_daily_targets_task >>
            [run_dq_is_unique_merge_pk_hard_task, run_dq_is_null_merge_pk_hard_task] >>
            run_audit_brand_ebitda_revenue_daily_targets_task
        )

    # ---- Main branch ----
    chain(
       begin,
       get_workflow_parameters,
       tg_brand_ebitda_revenue_daily_targets,
       update_workflow_parameters,
       end
    )

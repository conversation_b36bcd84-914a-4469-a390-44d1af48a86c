from airflow import D<PERSON>
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.sensors.external_task import ExternalTaskSensor
import logging

log = logging.getLogger(__name__)

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id="shopify_refunds_hourly",
    start_date=datetime(2022, 5, 8),
    schedule_interval="@hourly",
    catchup=False,
    max_active_runs=1,
    params={
        "workflow_name": "SHOPIFY_REFUNDS_HOURLY",
        "author": "ayush"
    },
) as dag:
    import tasklib.workflow as tlw
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    create_stg_raw_shopify_refunds_to_process = PythonOperator(
        task_id="create_stg_raw_shopify_refunds_to_process",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "refunds/shopify/create_stg_raw_shopify_refunds_to_process.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_shopify_refunds_sku_agg = PythonOperator(
        task_id="create_stg_shopify_refunds_sku_agg",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "refunds/shopify/create_stg_shopify_refunds_sku_agg.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    # run_audit_shopify_refunds = PythonOperator(
    #     task_id="run_audit_shopify_refunds",
    #     python_callable=tla.run_audit,
    #     op_kwargs={"table_name": "$stage_db.log_shopify_refunds",
    #                "wf_params": WF_PARAMS_EXPR},
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert
    # )

    create_stg_shopify_refunds_order_metadata = PythonOperator(
        task_id="create_stg_shopify_refunds_order_metadata",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "refunds/shopify/create_stg_shopify_refunds_order_metadata.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    # Add DQ to check if any order is missing, should not be the case for shopify

    create_stg_shopify_fact_all_refunds = PythonOperator(
        task_id="create_stg_shopify_fact_all_refunds",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "refunds/shopify/create_stg_shopify_fact_all_refunds.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_shopify_fact_all_refunds_v2 = PythonOperator(
        task_id="create_stg_shopify_fact_all_refunds_v2",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "refunds/shopify/create_stg_shopify_reunds_v2.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    merge_shopify_fact_all_refunds = PythonOperator(
        task_id="merge_shopify_fact_all_refunds",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "refunds/shopify/merge_shopify_fact_all_refunds.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    update_shopify_fact_all_refunds = PythonOperator(
        task_id="update_shopify_fact_all_refunds",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "refunds/shopify/update_shopify_fact_all_refunds.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    # run_dq_refund_pk_is_unique = PythonOperator(
    #     task_id="run_dq_refund_pk_is_unique",
    #     python_callable=tldq.run_dq_tests,
    #     op_kwargs={"dq_id": "1c677809-3679-4e05-be79-8fee2f4829e0"},
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert,
    # )

    run_audit_fact_all_refunds = PythonOperator(
            task_id="run_audit_fact_all_refunds",
            python_callable=tla.run_audit,
            op_kwargs={"table_name": "$curated_db.fact_all_refunds",
                       "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
    )

    wait_on_shopify_orders = ExternalTaskSensor(task_id="wait_on_shopify_orders",
                               external_dag_id="shopify_v2_orders",
                               external_task_id="end",
                               allowed_states=["success"],
                               poke_interval=60 * 3,
                               mode="reschedule",
                               timeout=60 * 60 * 4,
                               on_failure_callback=alerts.send_failure_alert
                               )

    end = DummyOperator(task_id="end")

    chain(
        get_workflow_params,
        wait_on_shopify_orders,
        create_stg_raw_shopify_refunds_to_process,
        create_stg_shopify_refunds_sku_agg,
        create_stg_shopify_refunds_order_metadata,
        create_stg_shopify_fact_all_refunds,
        create_stg_shopify_fact_all_refunds_v2,
        merge_shopify_fact_all_refunds,
        update_shopify_fact_all_refunds,
        run_audit_fact_all_refunds,
        update_workflow_params,
        end,
    )

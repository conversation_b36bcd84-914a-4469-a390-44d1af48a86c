from airflow import D<PERSON>
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.sensors.external_task import ExternalTaskSensor
import logging

log = logging.getLogger(__name__)

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id="shopify_shipments_hourly",
    start_date=datetime(2022, 5, 8),
    schedule_interval="@hourly",
    catchup=False,
    max_active_runs=1,
    params={
        "workflow_name": "SHOPIFY_SHIPMENTS_HOURLY",
        "author": "ruchira"
    },
) as dag:
    import tasklib.workflow as tlw
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    create_stg_raw_shopify_shipments_to_process = PythonOperator(
        task_id="create_stg_raw_shopify_shipments_to_process",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "shipments/shopify/create_stg_raw_shopify_shipments_to_process.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_shopify_shipments = PythonOperator(
        task_id="create_stg_shopify_shipments",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "shipments/shopify/create_stg_shopify_shipments.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_shopify_fact_all_shipments = PythonOperator(
        task_id="create_stg_shopify_fact_all_shipments",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "shipments/shopify/create_stg_shopify_fact_all_shipments.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_dq_shipment_pk_is_unique = PythonOperator(
        task_id="run_dq_shipment_pk_is_unique",
        python_callable=tldq.run_dq_tests,
        op_kwargs={"dq_id": "e8178ee6-788d-4fb6-914c-3fd614392268"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    merge_fact_all_shipments = PythonOperator(
        task_id="merge_fact_all_shipments",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "shipments/shopify/merge_fact_all_shipments.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    update_orders_fulfillment_channel = PythonOperator(
        task_id="update_orders_fulfillment_channel",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "shipments/shopify/update_orders_fulfillment_channel.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    update_fact_all_shipments = PythonOperator(
        task_id="update_fact_all_shipments",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "shipments/shopify/update_fact_all_shipments.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_audit_fact_all_shipments = PythonOperator(
            task_id="run_audit_fact_all_shipments",
            python_callable=tla.run_audit,
            op_kwargs={"table_name": "$curated_db.fact_all_shipments",
                       "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
    )

    wait_on_shopify_orders = ExternalTaskSensor(task_id="wait_on_shopify_orders",
                               external_dag_id="shopify_v2_orders",
                               external_task_id="end",
                               allowed_states=["success"],
                               timeout=60 * 60 * 4,
                               on_failure_callback=alerts.send_failure_alert
                               )

    end = DummyOperator(task_id="end")

    chain(
        get_workflow_params,
        wait_on_shopify_orders,
        create_stg_raw_shopify_shipments_to_process,
        create_stg_shopify_shipments,
        create_stg_shopify_fact_all_shipments,
        run_dq_shipment_pk_is_unique,
        merge_fact_all_shipments,
        update_orders_fulfillment_channel,
        update_fact_all_shipments,
        run_audit_fact_all_shipments,
        update_workflow_params,
        end,
    )

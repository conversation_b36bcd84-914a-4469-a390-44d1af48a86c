from datetime import datetime
from airflow import DAG
import logging
import pytz
import ast
from db_connectors.pg_connector import Post<PERSON><PERSON>
from datetime import timedelta

from airflow.utils.task_group import TaskGroup
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.http.operators.http import SimpleHttpOperator
from airflow.providers.http.sensors.http import HttpSensor

import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.workflow as tlw
from tasklib import alerts
import tasklib.sql as tlsql
from tasklib.loader import S3ToSnowflake
load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'netsuite_saved_search_ingestion'
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_params') }}"
API_ENDPOINT_CONN_ID = 'nss_sync_api_endpoint'
DAG_WF_TIME_DELTA = 4

log = logging.getLogger(__name__)


def _set_workflow_params(workflow_name: str = None):
    curr_ts = datetime.now(pytz.utc)
    params = tlw.get_workflow_params(workflow_name)
    log.info(f"workflow parameters: {params}")
    if params["wf_interval"] == 'CUSTOM':
        params["wf_start_ts_iso_utc"] = params["wf_end_ts_iso_utc"]
        params["wf_end_ts_iso_utc"] = (curr_ts + timedelta(hours=DAG_WF_TIME_DELTA)).strftime("%Y-%m-%d %H:%M:%S")
        log.info("adjusting workflow watermark timestamps")
        log.info(f"new workflow parameters: {params}")
    return params


def _update_workflow_params(wf_params: str) -> None:
    wf_params_dict = ast.literal_eval(wf_params)
    workflow_name = wf_params_dict['wf_name']
    end_ts = wf_params_dict['wf_end_ts_iso_utc']

    # adjust the start timestamp for the next run
    start_tsz = pytz.utc.localize(datetime.fromisoformat(end_ts)) - timedelta(hours=DAG_WF_TIME_DELTA)
    upd_start_ts = start_tsz.strftime('%Y-%m-%d %H:%M:%S')

    # adjust the end timestamp for the next run
    end_tsz = datetime.now(pytz.utc)
    upd_end_ts = end_tsz.strftime('%Y-%m-%d %H:%M:%S')

    query = f"UPDATE workflow_configurations.workflow SET watermark_start_timestamp_utc='{upd_start_ts}', " \
            f"watermark_end_timestamp_utc='{upd_end_ts}', " \
            f"updated_timestamp_utc=current_timestamp, updated_by='amazon_fba_orders_etl' where name='{workflow_name}'"
    log.info(query)
    try:
        postgres = Postgres()
        postgres.execute_statement(query)
    except Exception as err:
        message = "Error occurred while updating workflow attributes"
        log.info(message)
        log.error(err)
        raise


def check_response(response):
    """
    Monitor Netsuite Saved Search --> S3 sync status.
    The function returns True if status="COMPLETED" else it returns False.
    Any error in reading status does not cause the job to fail, as we might
    see intermittent errors in the API call
    """
    try:
        json_response = response.json()
        log.info(f'RESPONSE: {json_response}')
        status = json_response.get('status', '').lower()
        if status == 'completed':
            return True
    except Exception as e:
        log.error(f'API ERROR - {e}')
    return False


def check_sync_response(response):
    try:
        json_response = response.json()
        log.info(f'RESPONSE: {json_response}')
        batch_id = json_response.get('batch_id', '')
        log.info(batch_id)
        return batch_id
    except Exception as e:
        log.error(f'API ERROR - {e}')
    return


with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2023, 1, 5),
    schedule_interval='0 14,15,16,17,18,19,20,21,22,23 * * *',
    catchup=False,
    max_active_runs=1,
    concurrency=4,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'ayush'},
    tags=['Ayush', 'Netsuite']
) as dag:

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    get_workflow_params = PythonOperator(
        task_id="get_workflow_params",
        python_callable=_set_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    with TaskGroup(group_id="update_bill_information") as update_bill_information:
        sync_api_call_bill_information = SimpleHttpOperator(
            task_id="sync_api_call_bill_information",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "0f0f1c6c-82c9-4b84-924b-b866bd2c6355"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_bill_information = HttpSensor(
            task_id='sync_api_status_monitor_bill_information',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_bill_information.sync_api_call_bill_information')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 min
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_billinformation = PythonOperator(
            task_id="s3_list_files_billinformation",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_billinformation.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_billinformation = PythonOperator(
            task_id="s3_to_snowflake_billinformation",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_bill_information.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_bill_information = PythonOperator(
            task_id="merge_bill_information",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_bill_information.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_bill_information >> sync_api_status_monitor_bill_information >>
         s3_list_files_billinformation >> s3_to_snowflake_billinformation >> merge_bill_information)

    with TaskGroup(group_id="update_brand_mapping") as update_brand_mapping:
        sync_api_call_brand_mapping = SimpleHttpOperator(
            task_id="sync_api_call_brand_mapping",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "1db06901-7ffe-403b-a9ef-f0825673ed09"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_brand_mapping = HttpSensor(
            task_id='sync_api_status_monitor_brand_mapping',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_brand_mapping.sync_api_call_brand_mapping')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minute
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_brandmapping = PythonOperator(
            task_id="s3_list_files_brandmapping",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_brandmapping.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_brandmapping = PythonOperator(
            task_id="s3_to_snowflake_brandmapping",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_brand_mapping.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_brand_mapping = PythonOperator(
            task_id="merge_brand_mapping",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_brand_mapping.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_brand_mapping >> sync_api_status_monitor_brand_mapping >>
         s3_list_files_brandmapping >> s3_to_snowflake_brandmapping >> merge_brand_mapping)

    with TaskGroup(group_id="update_brands") as update_brands:
        sync_api_call_brands = SimpleHttpOperator(
            task_id="sync_api_call_brands",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "de265cb5-0975-4d58-a84a-bd84effc7d75"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_brands = HttpSensor(
            task_id='sync_api_status_monitor_brands',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={"batch_id": "{{ti.xcom_pull(task_ids='update_brands.sync_api_call_brands')}}"},
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_brands = PythonOperator(
            task_id="s3_list_files_brands",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_brands.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_brands = PythonOperator(
            task_id="s3_to_snowflake_brands",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_brands.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_brands = PythonOperator(
            task_id="merge_brands",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_brands.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_brands >> sync_api_status_monitor_brands >>
         s3_list_files_brands >> s3_to_snowflake_brands >> merge_brands)

    with TaskGroup(group_id="update_customer_location") as update_customer_location:
        sync_api_call_customer_location = SimpleHttpOperator(
            task_id="sync_api_call_customer_location",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "fec3c76d-540c-4bf3-9426-6c48d8e6b237"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_customer_location = HttpSensor(
            task_id='sync_api_status_monitor_customer_location',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_customer_location.sync_api_call_customer_location')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minute
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_customerlocation = PythonOperator(
            task_id="s3_list_files_customerlocation",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_customerlocation.yaml",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_customerlocation = PythonOperator(
            task_id="s3_to_snowflake_customerlocation",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_customer_location.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_customer_location = PythonOperator(
            task_id="merge_customer_location",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_customer_location.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_customer_location >> sync_api_status_monitor_customer_location >>
         s3_list_files_customerlocation >> s3_to_snowflake_customerlocation >> merge_customer_location)

    with TaskGroup(group_id="update_foreign_exchange") as update_foreign_exchange:
        sync_api_call_foreign_exchange = SimpleHttpOperator(
            task_id="sync_api_call_foreign_exchange",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "1eff8069-9b5a-470e-8d0c-2f101a0b9141"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_foreign_exchange = HttpSensor(
            task_id='sync_api_status_monitor_foreign_exchange',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_foreign_exchange.sync_api_call_foreign_exchange')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_foreignexchange = PythonOperator(
            task_id="s3_list_files_foreignexchange",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_foreignexchange.yaml",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_foreignexchange = PythonOperator(
            task_id="s3_to_snowflake_foreignexchange",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_foreign_exchange.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_foreign_exchange = PythonOperator(
            task_id="merge_foreign_exchange",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_foreign_exchange.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_foreign_exchange >> sync_api_status_monitor_foreign_exchange >>
         s3_list_files_foreignexchange >> s3_to_snowflake_foreignexchange >> merge_foreign_exchange)

    with TaskGroup(group_id="update_inventory_by_location") as update_inventory_by_location:
        sync_api_call_inventory_by_location = SimpleHttpOperator(
            task_id="sync_api_call_inventory_by_location",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "5cc788a1-7942-447a-a1a9-8a2b92279b40"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_inventory_by_location = HttpSensor(
            task_id='sync_api_status_monitor_inventory_by_location',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_inventory_by_location.sync_api_call_inventory_by_location')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_inventorybylocation = PythonOperator(
            task_id="s3_list_files_inventorybylocation",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_inventorybylocation.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_inventorybylocation = PythonOperator(
            task_id="s3_to_snowflake_inventorybylocation",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_inventory_by_location.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_inventory_by_location = PythonOperator(
            task_id="merge_inventory_by_location",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_inventory_by_location.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_inventory_by_location >> sync_api_status_monitor_inventory_by_location >>
         s3_list_files_inventorybylocation >> s3_to_snowflake_inventorybylocation >> merge_inventory_by_location)

    with TaskGroup(group_id="update_inbound_shipments") as update_inbound_shipments:
        sync_api_call_inbound_shipments = SimpleHttpOperator(
            task_id="sync_api_call_inbound_shipments",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "7e237d5b-e302-4092-9103-a65aa7d12e02"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_inbound_shipments = HttpSensor(
            task_id='sync_api_status_monitor_inbound_shipments',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_inbound_shipments.sync_api_call_inbound_shipments')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_inbound_shipments = PythonOperator(
            task_id="s3_list_files_inbound_shipments",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_inbound_shipments.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_inbound_shipments = PythonOperator(
            task_id="s3_to_snowflake_inbound_shipments",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_inbound_shipments.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_inbound_shipments = PythonOperator(
            task_id="merge_inbound_shipments",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_inbound_shipments.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_inbound_shipments >> sync_api_status_monitor_inbound_shipments >>
         s3_list_files_inbound_shipments >> s3_to_snowflake_inbound_shipments >> merge_inbound_shipments)

    with TaskGroup(group_id="update_item_fulfillment") as update_item_fulfillment:
        sync_api_call_item_fulfillment = SimpleHttpOperator(
            task_id="sync_api_call_item_fulfillment",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "b017c052-770d-49c8-a91d-ba3618f1d5b5"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_item_fulfillment = HttpSensor(
            task_id='sync_api_status_monitor_item_fulfillment',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_item_fulfillment.sync_api_call_item_fulfillment')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=300,  # Check the job status every 5 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_itemfulfillment = PythonOperator(
            task_id="s3_list_files_itemfulfillment",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_itemfulfillment.yaml",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_itemfulfillment = PythonOperator(
            task_id="s3_to_snowflake_itemfulfillment",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_item_fulfillment.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_item_fulfillment = PythonOperator(
            task_id="merge_item_fulfillment",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_item_fulfillment.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_item_fulfillment >> sync_api_status_monitor_item_fulfillment >>
         s3_list_files_itemfulfillment >> s3_to_snowflake_itemfulfillment >> merge_item_fulfillment)

    with TaskGroup(group_id="update_item_master") as update_item_master:
        sync_api_call_item_master = SimpleHttpOperator(
            task_id="sync_api_call_item_master",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "c67cef18-ab19-4a80-871f-4b54c09a7006"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_item_master = HttpSensor(
            task_id='sync_api_status_monitor_item_master',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={"batch_id": "{{ti.xcom_pull(task_ids='update_item_master.sync_api_call_item_master')}}"},
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_itemmaster = PythonOperator(
            task_id="s3_list_files_itemmaster",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_itemmaster.yaml",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_itemmaster = PythonOperator(
            task_id="s3_to_snowflake_itemmaster",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_item_master.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_item_master = PythonOperator(
            task_id="merge_item_master",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_item_master.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_item_master >> sync_api_status_monitor_item_master >>
         s3_list_files_itemmaster >> s3_to_snowflake_itemmaster >> merge_item_master)

    with TaskGroup(group_id="update_item_master_bom") as update_item_master_bom:
        sync_api_call_item_master_bom = SimpleHttpOperator(
            task_id="sync_api_call_item_master_bom",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "227de244-92cc-4a54-a6d4-849355e5e3d1"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_item_master_bom = HttpSensor(
            task_id='sync_api_status_monitor_item_master_bom',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_item_master_bom.sync_api_call_item_master_bom')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_itemmasterbom = PythonOperator(
            task_id="s3_list_files_itemmasterbom",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_itemmasterbom.yaml",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_itemmasterbom = PythonOperator(
            task_id="s3_to_snowflake_itemmasterbom",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_item_master_bom.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_item_master_bom = PythonOperator(
            task_id="merge_item_master_bom",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_item_master_bom.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_item_master_bom >> sync_api_status_monitor_item_master_bom >>
         s3_list_files_itemmasterbom >> s3_to_snowflake_itemmasterbom >> merge_item_master_bom)

    with TaskGroup(group_id="update_item_receipts") as update_item_receipts:
        sync_api_call_item_receipts = SimpleHttpOperator(
            task_id="sync_api_call_item_receipts",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "d5bcf5bc-749f-4e35-9736-d95b2d08e6ce"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_item_receipts = HttpSensor(
            task_id='sync_api_status_monitor_item_receipts',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_item_receipts.sync_api_call_item_receipts')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_itemreceipts = PythonOperator(
            task_id="s3_list_files_itemreceipts",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_itemreceipts.yaml",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_itemreceipts = PythonOperator(
            task_id="s3_to_snowflake_itemreceipts",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_item_receipts.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_item_receipts = PythonOperator(
            task_id="merge_item_receipts",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_item_receipts.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_item_receipts >> sync_api_status_monitor_item_receipts >>
         s3_list_files_itemreceipts >> s3_to_snowflake_itemreceipts >> merge_item_receipts)

    with TaskGroup(group_id="update_item_sales_transaction") as update_item_sales_transaction:
        sync_api_call_item_sales_transaction = SimpleHttpOperator(
            task_id="sync_api_call_item_sales_transaction",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "01aaf93d-117e-4ab0-9368-c62e54307c8f"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_item_sales_transaction = HttpSensor(
            task_id='sync_api_status_monitor_item_sales_transaction',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_item_sales_transaction.sync_api_call_item_sales_transaction')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=1800,  # Check the job status every 30 mins
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=14400,  # Job timeout after 4 hours
            extra_options={'check_response': False}
        )

        s3_list_files_itemsalestransaction = PythonOperator(
            task_id="s3_list_files_itemsalestransaction",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_itemsalestransaction.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_itemsalestransaction = PythonOperator(
            task_id="s3_to_snowflake_itemsalestransaction",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_item_sales_transaction.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_item_sales_transaction = PythonOperator(
            task_id="merge_item_sales_transaction",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_item_sales_transaction.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_item_sales_transaction >> sync_api_status_monitor_item_sales_transaction >>
         s3_list_files_itemsalestransaction >> s3_to_snowflake_itemsalestransaction >> merge_item_sales_transaction)

    with TaskGroup(group_id="update_locations") as update_locations:
        sync_api_call_locations = SimpleHttpOperator(
            task_id="sync_api_call_locations",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "a947b697-2d6d-4746-8921-0990b8e2a522"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_locations = HttpSensor(
            task_id='sync_api_status_monitor_locations',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={"batch_id": "{{ti.xcom_pull(task_ids='update_locations.sync_api_call_locations')}}"},
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_locations = PythonOperator(
            task_id="s3_list_files_locations",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_locations.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_locations = PythonOperator(
            task_id="s3_to_snowflake_locations",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_locations.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_locations = PythonOperator(
            task_id="merge_locations",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_locations.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_locations >> sync_api_status_monitor_locations >>
         s3_list_files_locations >> s3_to_snowflake_locations >> merge_locations)

    with TaskGroup(group_id="update_open_po_to_so") as update_open_po_to_so:
        sync_api_call_open_po_to_so = SimpleHttpOperator(
            task_id="sync_api_call_open_po_to_so",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "b095cb43-4b8d-42b0-9a1e-b55b36d56b64"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_open_po_to_so = HttpSensor(
            task_id='sync_api_status_monitor_open_po_to_so',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_open_po_to_so.sync_api_call_open_po_to_so')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_open_po_to_so = PythonOperator(
            task_id="s3_list_files_open_po_to_so",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_open_po_to_so.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_open_po_to_so = PythonOperator(
            task_id="s3_to_snowflake_open_po_to_so",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_open_po_to_so.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_open_po_to_so = PythonOperator(
            task_id="merge_open_po_to_so",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_open_po_to_so.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_open_po_to_so >> sync_api_status_monitor_open_po_to_so >>
         s3_list_files_open_po_to_so >> s3_to_snowflake_open_po_to_so >> merge_open_po_to_so)

    with TaskGroup(group_id="update_purchase_order") as update_purchase_order:
        sync_api_call_purchase_order = SimpleHttpOperator(
            task_id="sync_api_call_purchase_order",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "30b5669e-8944-4eb4-ad11-8684cfd82c21"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_purchase_order = HttpSensor(
            task_id='sync_api_status_monitor_purchase_order',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_purchase_order.sync_api_call_purchase_order')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_purchaseorder = PythonOperator(
            task_id="s3_list_files_purchaseorder",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_purchaseorder.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_purchaseorder = PythonOperator(
            task_id="s3_to_snowflake_purchaseorder",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_purchase_order.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_purchase_order = PythonOperator(
            task_id="merge_purchase_order",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_purchase_order.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_purchase_order >> sync_api_status_monitor_purchase_order >>
         s3_list_files_purchaseorder >> s3_to_snowflake_purchaseorder >> merge_purchase_order)

    with TaskGroup(group_id="update_sales_order") as update_sales_order:
        sync_api_call_sales_order = SimpleHttpOperator(
            task_id="sync_api_call_sales_order",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "d030e6de-c112-4b8f-9bb2-357e36604ac4"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_sales_order = HttpSensor(
            task_id='sync_api_status_monitor_sales_order',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={"batch_id": "{{ti.xcom_pull(task_ids='update_sales_order.sync_api_call_sales_order')}}"},
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_salesorder = PythonOperator(
            task_id="s3_list_files_salesorder",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_salesorder.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_salesorder = PythonOperator(
            task_id="s3_to_snowflake_salesorder",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_sales_order.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_sales_order = PythonOperator(
            task_id="merge_sales_order",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_sales_order.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_sales_order >> sync_api_status_monitor_sales_order >>
         s3_list_files_salesorder >> s3_to_snowflake_salesorder >> merge_sales_order)

    with TaskGroup(group_id="update_shopify_customer_location") as update_shopify_customer_location:
        sync_api_call_shopify_customer_location = SimpleHttpOperator(
            task_id="sync_api_call_shopify_customer_location",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "4772f36b-2cb6-4907-bc5a-01e5b9f604e3"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_shopify_customer_location = HttpSensor(
            task_id='sync_api_status_monitor_shopify_customer_location',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_shopify_customer_location.sync_api_call_shopify_customer_location')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_shopifycustomerlocation = PythonOperator(
            task_id="s3_list_files_shopifycustomerlocation",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_shopifycustomerlocation.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_shopifycustomerlocation = PythonOperator(
            task_id="s3_to_snowflake_shopifycustomerlocation",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_shopify_customer_location.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_shopify_customer_location = PythonOperator(
            task_id="merge_shopify_customer_location",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_shopify_customer_location.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_shopify_customer_location >> sync_api_status_monitor_shopify_customer_location >>
         s3_list_files_shopifycustomerlocation >> s3_to_snowflake_shopifycustomerlocation >>
         merge_shopify_customer_location)

    with TaskGroup(group_id="update_transfer_order") as update_transfer_order:
        sync_api_call_transfer_order = SimpleHttpOperator(
            task_id="sync_api_call_transfer_order",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "928a7183-5686-4705-b159-4b8cb12e5439"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_transfer_order = HttpSensor(
            task_id='sync_api_status_monitor_transfer_order',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_transfer_order.sync_api_call_transfer_order')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_transferorder = PythonOperator(
            task_id="s3_list_files_transferorder",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_transferorder.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_transferorder = PythonOperator(
            task_id="s3_to_snowflake_transferorder",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_transfer_order.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_transfer_order = PythonOperator(
            task_id="merge_transfer_order",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_transfer_order.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_transfer_order >> sync_api_status_monitor_transfer_order >>
         s3_list_files_transferorder >> s3_to_snowflake_transferorder >> merge_transfer_order)

    with TaskGroup(group_id="update_vendor") as update_vendor:
        sync_api_call_vendor = SimpleHttpOperator(
            task_id="sync_api_call_vendor",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "b540a46d-fa6a-476e-baa4-cfdb35cacd2c"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_vendor = HttpSensor(
            task_id='sync_api_status_monitor_vendor',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={"batch_id": "{{ti.xcom_pull(task_ids='update_vendor.sync_api_call_vendor')}}"},
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_vendor = PythonOperator(
            task_id="s3_list_files_vendor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_vendor.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_vendor = PythonOperator(
            task_id="s3_to_snowflake_vendor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_vendor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_vendor = PythonOperator(
            task_id="merge_vendor",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_vendor.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_vendor >> sync_api_status_monitor_vendor >>
         s3_list_files_vendor >> s3_to_snowflake_vendor >> merge_vendor)

    with TaskGroup(group_id="update_virtual_bundles") as update_virtual_bundles:
        sync_api_call_virtual_bundles = SimpleHttpOperator(
            task_id="sync_api_call_virtual_bundles",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "a27293a8-367b-425b-bdf1-ebc5609598c3"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_virtual_bundles = HttpSensor(
            task_id='sync_api_status_monitor_virtual_bundles',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_virtual_bundles.sync_api_call_virtual_bundles')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_virtualbundles = PythonOperator(
            task_id="s3_list_files_virtualbundles",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_virtualbundles.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_virtualbundles = PythonOperator(
            task_id="s3_to_snowflake_virtualbundles",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_virtual_bundles.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_virtual_bundles = PythonOperator(
            task_id="merge_virtual_bundles",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_virtual_bundles.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_virtual_bundles >> sync_api_status_monitor_virtual_bundles >>
         s3_list_files_virtualbundles >> s3_to_snowflake_virtualbundles >> merge_virtual_bundles)

    with TaskGroup(group_id="update_retail_revenue") as update_retail_revenue:
        sync_api_call_retail_revenue = SimpleHttpOperator(
            task_id="sync_api_call_retail_revenue",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "cf52ee57-9561-4ea3-994b-d07bb7d1773e"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_retail_revenue = HttpSensor(
            task_id='sync_api_status_monitor_retail_revenue',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_retail_revenue.sync_api_call_retail_revenue')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_retailrevenue = PythonOperator(
            task_id="s3_list_files_retailrevenue",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_retailrevenue.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_retailrevenue = PythonOperator(
            task_id="s3_to_snowflake_retailrevenue",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_retail_revenue.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_retail_revenue = PythonOperator(
            task_id="merge_retail_revenue",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_retail_revenue.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_retail_revenue >> sync_api_status_monitor_retail_revenue >>
         s3_list_files_retailrevenue >> s3_to_snowflake_retailrevenue >> merge_retail_revenue)

    with TaskGroup(group_id="update_posted_cashsales") as update_posted_cashsales:
        sync_api_call_posted_cashsales = SimpleHttpOperator(
            task_id="sync_api_call_posted_cashsales",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "89a1f0ab-b120-43a9-af1b-34706dbd116e"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_posted_cashsales = HttpSensor(
            task_id='sync_api_status_monitor_posted_cashsales',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_posted_cashsales.sync_api_call_posted_cashsales')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_posted_cashsales = PythonOperator(
            task_id="s3_list_files_posted_cashsales",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_posted_cashsales.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_posted_cashsales = PythonOperator(
            task_id="s3_to_snowflake_posted_cashsales",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_posted_cashsales.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_posted_cashsales = PythonOperator(
            task_id="merge_posted_cashsales",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_posted_cashsales.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_posted_cashsales >> sync_api_status_monitor_posted_cashsales >>
         s3_list_files_posted_cashsales >> s3_to_snowflake_posted_cashsales >> merge_posted_cashsales)

    with TaskGroup(group_id="update_posted_cash_refunds") as update_posted_cash_refunds:
        sync_api_call_posted_cash_refunds = SimpleHttpOperator(
            task_id="sync_api_call_posted_cash_refunds",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "af3267ba-c5bf-420e-afd1-812a547b8d2e"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_posted_cash_refunds = HttpSensor(
            task_id='sync_api_status_monitor_posted_cash_refunds',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_posted_cash_refunds.sync_api_call_posted_cash_refunds')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_posted_cash_refunds = PythonOperator(
            task_id="s3_list_files_posted_cash_refunds",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_posted_cash_refunds.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_posted_cash_refunds = PythonOperator(
            task_id="s3_to_snowflake_posted_cash_refunds",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_posted_cash_refunds.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_posted_cash_refunds = PythonOperator(
            task_id="merge_posted_cash_refunds",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_posted_cash_refunds.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_posted_cash_refunds >> sync_api_status_monitor_posted_cash_refunds >>
         s3_list_files_posted_cash_refunds >> s3_to_snowflake_posted_cash_refunds >> merge_posted_cash_refunds)


    with TaskGroup(group_id="update_net_revenue_offline_channels") as update_net_revenue_offline_channels:
        sync_api_call_net_revenue_offline_channels = SimpleHttpOperator(
            task_id="sync_api_call_net_revenue_offline_channels",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "8a96c9b5-ffc5-448e-b364-20d5022fc8ea"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_net_revenue_offline_channels = HttpSensor(
            task_id='sync_api_status_monitor_net_revenue_offline_channels',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_net_revenue_offline_channels.sync_api_call_net_revenue_offline_channels')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_net_revenue_offline_channels = PythonOperator(
            task_id="s3_list_files_net_revenue_offline_channels",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_net_revenue_offline_channels.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_net_revenue_offline_channels = PythonOperator(
            task_id="s3_to_snowflake_net_revenue_offline_channels",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_net_revenue_offline_channels.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_net_revenue_offline_channels = PythonOperator(
            task_id="merge_net_revenue_offline_channels",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_net_revenue_offline_channels.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_net_revenue_offline_channels >> sync_api_status_monitor_net_revenue_offline_channels >>
         s3_list_files_net_revenue_offline_channels >> s3_to_snowflake_net_revenue_offline_channels >> merge_net_revenue_offline_channels)

    with TaskGroup(group_id="update_sales_orders_offline") as update_sales_orders_offline:
        sync_api_call_sales_orders_offline = SimpleHttpOperator(
            task_id="sync_api_call_sales_orders_offline",
            http_conn_id=API_ENDPOINT_CONN_ID,
            endpoint='/sync-snapshot-by-id-async',
            method='GET',
            data={"saved_search_id": "1a1d1eb9-8098-4150-ada4-d1878202f238"},
            headers={"Content-Type": "application/json"},
            response_filter=lambda response: check_sync_response(response),
            log_response=True
        )

        sync_api_status_monitor_sales_orders_offline = HttpSensor(
            task_id='sync_api_status_monitor_sales_orders_offline',
            http_conn_id=API_ENDPOINT_CONN_ID,
            method="GET",
            endpoint="get-batch-status",
            request_params={
                "batch_id": "{{ti.xcom_pull(task_ids='update_sales_orders_offline.sync_api_call_sales_orders_offline')}}"
            },
            response_check=lambda response: check_response(response),
            poke_interval=60,  # Check the job status every 1 minutes
            mode='reschedule',  # Free up the worker slot for other jobs
            timeout=7200,  # Job timeout after 2 hours
            extra_options={'check_response': False}
        )

        s3_list_files_sales_orders_offline = PythonOperator(
            task_id="s3_list_files_sales_orders_offline",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "netsuite_saved_search/s3_list_folders_sales_orders_offline.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_sales_orders_offline = PythonOperator(
            task_id="s3_to_snowflake_sales_orders_offline",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "netsuite_saved_search/s3_to_sf_netsuite_ss_sales_orders_offline.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_sales_orders_offline = PythonOperator(
            task_id="merge_sales_orders_offline",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "netsuite_saved_search/merge_sales_orders_offline.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (sync_api_call_sales_orders_offline >> sync_api_status_monitor_sales_orders_offline >>
         s3_list_files_sales_orders_offline >> s3_to_snowflake_sales_orders_offline >> merge_sales_orders_offline)


    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=_update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    small_tasks_completed = DummyOperator(task_id="small_tasks_completed")
    medium_tasks_completed = DummyOperator(task_id="medium_tasks_completed")
    large_tasks_completed = DummyOperator(task_id="large_tasks_completed")

    small_tables = [
        update_bill_information,
        update_brand_mapping,
        update_brands,
        update_customer_location,
        update_foreign_exchange,
        update_inbound_shipments,
        update_inventory_by_location,
        update_item_master_bom,
        update_locations,
        update_open_po_to_so,
        update_purchase_order,
        update_shopify_customer_location,
        update_vendor,
        update_virtual_bundles,
        update_retail_revenue,
        update_posted_cashsales,
        update_posted_cash_refunds,
        update_net_revenue_offline_channels,
        update_sales_orders_offline
    ]

    medium_tables = [
        update_item_fulfillment,
        update_item_master,
        update_sales_order,
        update_transfer_order,
        update_item_receipts,
    ]

    large_tables = [
        update_item_sales_transaction
    ]

    begin >> get_workflow_params >> small_tables >> small_tasks_completed >> medium_tables >> medium_tasks_completed \
        >> large_tables >> large_tasks_completed >> update_workflow_params >> end

from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.models import Variable
import logging
from tasklib import alerts
import tasklib.workflow as tlw
import tasklib.s3 as tls3
import tasklib.glue as tlg
import tasklib.sql as tlsql
import tasklib.audit as tla
import tasklib.dq as tldq
from airflow.sensors.external_task import ExternalTaskSensor

from tasklib.loader import S3ToSnowflake
load_obj = S3ToSnowflake()

log = logging.getLogger(__name__)

WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"
DAG_ID = "shopify_subledger_netsuite_automation"

with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022,7,30),
    schedule_interval=Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 */4 * * *'),
    catchup=False,
    max_active_runs=1,
    params={'workflow_name':'SHOPIFY_SUBLEDGER_NETSUITE_AUTOMATION'
            ,'author':"ruchira, ayush"},
    tags=['Ruchira']
) as dag:

    get_workflow_params=PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )

    #store customer location mapping file
    list_s3_modified_files_store_customer_location_mapping=PythonOperator(
        task_id="task_list_s3_modified_files_store_customer_location_mapping",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"shopify_subledger_netsuite_automation/store_customer_location_mapping_s3_list_folders.yaml",
                   "wf_params":WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )
    
    transfer_s3_to_snowflake_store_customer_location_mapping=PythonOperator(
        task_id="task_transfer_s3_to_snowflake_store_customer_location_mapping",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file":"shopify_subledger_netsuite_automation/s3_to_sf_shopify_store_customer_location_mapping_stg.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_store_customer_location_mapping_dedup=PythonOperator(
        task_id="task_create_stg_store_customer_location_mapping_dedup",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"subledger_netsuite_automation/shopify/create_stg_store_customer_location_mapping_dedup.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_stg_store_customer_location_mapping = PythonOperator(
        task_id="task_run_dq_tests_stg_store_customer_location_mapping",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.SHOPIFY_STORE_CUSTOMER_LOCATION_MAPPING",
            'test_name': 'store customer location mapping duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.SHOPIFY_STORE_CUSTOMER_LOCATION_MAPPING",
                field_list=['"customer_name"', '"customer_internal_id"', '"store_id"', '"country_code"', '"category"',
                            '"fulfillment_channel"', '"location"', '"location_internal_id"', '"brand"',
                            '"shopify_location_id"'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    #cashsale gl item mapping
    list_s3_modified_files_cash_sale_gl_item_mapping=PythonOperator(
        task_id="task_list_s3_modified_files_cash_sale_gl_item_mapping",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"shopify_subledger_netsuite_automation/cash_sale_gl_item_mapping_s3_list_folders.yaml",
                   "wf_params":WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )
    
    transfer_s3_to_snowflake_cash_sale_gl_item_mapping=PythonOperator(
        task_id="task_transfer_s3_to_snowflake_cash_sale_gl_item_mapping",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file":"shopify_subledger_netsuite_automation/s3_to_sf_shopify_cash_sale_gl_item_mapping_stg.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_store_cash_sale_gl_item_mapping_dedup=PythonOperator(
        task_id="task_create_stg_cash_sale_gl_item_mapping_dedup",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"subledger_netsuite_automation/shopify/create_stg_cash_sale_gl_item_mapping_dedup.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_stg_cash_sale_gl_item_mapping = PythonOperator(
        task_id="task_run_dq_tests_stg_cash_sale_gl_item_mapping",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.SHOPIFY_CASH_SALE_GL_ITEM_MAPPING",
            'test_name': 'shopify cashsale gl mapping duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.SHOPIFY_CASH_SALE_GL_ITEM_MAPPING",
                field_list=['"type"', '"transaction_type"', '"netsuite_item_number"', '"netsuite_id"', '"netsuite_gl"'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    #cashsale sku gl item mapping
    list_s3_modified_files_cash_sale_sku_gl_item_mapping=PythonOperator(
        task_id="task_list_s3_modified_files_cash_sale_sku_gl_item_mapping",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"shopify_subledger_netsuite_automation/cash_sale_sku_gl_item_mapping_s3_list_folders.yaml",
                   "wf_params":WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )
    
    transfer_s3_to_snowflake_cash_sale_sku_gl_item_mapping=PythonOperator(
        task_id="task_transfer_s3_to_snowflake_cash_sale_sku_gl_item_mapping",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file":"shopify_subledger_netsuite_automation/s3_to_sf_shopify_cash_sale_sku_gl_item_mapping_stg.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_store_cash_sale_sku_gl_item_mapping_dedup=PythonOperator(
        task_id="task_create_stg_cash_sale_sku_gl_item_mapping_dedup",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"subledger_netsuite_automation/shopify/create_stg_cash_sale_sku_gl_item_mapping_dedup.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_stg_cash_sale_sku_gl_item_mapping = PythonOperator(
        task_id="task_run_dq_tests_stg_cash_sale_sku_gl_item_mapping",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.SHOPIFY_CASH_SALE_SKU_GL_ITEM_MAPPING",
            'test_name': 'shopify cashsale sku gl mapping duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.SHOPIFY_CASH_SALE_SKU_GL_ITEM_MAPPING",
                field_list=['"sku"', '"transaction_type"', '"netsuite_item_number"', '"netsuite_id"', '"netsuite_gl"'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )
    
    #subledger load
    fact_shopify_subledger_load=PythonOperator(
        task_id="task_fact_shopify_subledger_load",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"subledger_netsuite_automation/shopify/fact_shopify_subledger_load.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_fact_shopify_subledger = PythonOperator(
        task_id="task_run_dq_tests_fact_shopify_subledger",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'task_run_dq_tests_fact_shopify_subledger',
            'tb_name': "$curated_db.FACT_SHOPIFY_SUBLEDGER",
            'query_file': "subledger_netsuite_automation/shopify/shopify_subledger_dq.yaml"
        },
        on_failure_callback=alerts.send_failure_alert
    )

    #subledger aggregate load
    fact_shopify_subledger_aggregate_load=PythonOperator(
        task_id="task_fact_shopify_subledger_aggregate_load",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"subledger_netsuite_automation/shopify/fact_shopify_subledger_aggregate_load.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_fact_shopify_subledger_aggregate = PythonOperator(
        task_id="task_run_dq_tests_fact_shopify_subledger_aggregate",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'task_run_dq_tests_fact_shopify_subledger_aggregate',
            'tb_name': "$curated_db.FACT_ALL_SUBLEDGER_AGGREGATE",
            'query_file': "subledger_netsuite_automation/shopify/shopify_subledger_agg_dq.yaml"
        },
        on_failure_callback=alerts.send_failure_alert
    )

    run_audit_sl = PythonOperator(
        task_id="run_audit_sl",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.FACT_SHOPIFY_SUBLEDGER",
                   "wf_params": WF_PARAMS_EXPR,
                   "ts_created_field": '"record_created_timestamp_utc"',
                   "ts_updated_field": '"record_created_timestamp_utc"'
                   },
        on_failure_callback=alerts.send_failure_alert
    )

    run_audit_agg = PythonOperator(
        task_id="run_audit_agg",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.FACT_ALL_SUBLEDGER_AGGREGATE",
                   "wf_params": WF_PARAMS_EXPR,
                   "ts_created_field": '"record_created_timestamp_utc"',
                   "ts_updated_field": '"record_created_timestamp_utc"'
                   },
        on_failure_callback=alerts.send_failure_alert
    )
    
    #workflow update
    update_workflow_params=PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    
    wait_on_fact_all_revenue = ExternalTaskSensor(task_id="wait_on_fact_all_revenue",
                               external_dag_id="fact_all_revenue",
                               external_task_id="end",
                               allowed_states=["success"],
                              poke_interval=60 * 3,
                              mode="reschedule",
                               timeout=60 * 60 * 4,
                               on_failure_callback=alerts.send_failure_alert 
                               )

    wait_on_shopify_refunds = ExternalTaskSensor(task_id="wait_on_shopify_refunds",
                               external_dag_id="shopify_refunds_hourly",
                               external_task_id="end",
                               allowed_states=["success"],
                             poke_interval=60 * 3,
                             mode="reschedule",
                               timeout=60 * 60 * 4,
                               on_failure_callback=alerts.send_failure_alert 
                               )
    
    begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
    end = DummyOperator(task_id="end")    
    upstream_dependencies_successful = DummyOperator(task_id="upstream_dependencies_successful")
    all_staging_loads_completed = DummyOperator(task_id="all_staging_loads_completed")  
  
    #Main Branch 
    
    chain(
        begin, 
        get_workflow_params,
        [wait_on_fact_all_revenue,wait_on_shopify_refunds],
        upstream_dependencies_successful,
        [list_s3_modified_files_store_customer_location_mapping,list_s3_modified_files_cash_sale_gl_item_mapping,list_s3_modified_files_cash_sale_sku_gl_item_mapping],
        [transfer_s3_to_snowflake_store_customer_location_mapping,transfer_s3_to_snowflake_cash_sale_gl_item_mapping,transfer_s3_to_snowflake_cash_sale_sku_gl_item_mapping],
        [create_stg_store_customer_location_mapping_dedup,create_stg_store_cash_sale_gl_item_mapping_dedup,create_stg_store_cash_sale_sku_gl_item_mapping_dedup],
        [run_dq_tests_stg_store_customer_location_mapping,run_dq_tests_stg_cash_sale_gl_item_mapping,run_dq_tests_stg_cash_sale_sku_gl_item_mapping],
        all_staging_loads_completed,
        fact_shopify_subledger_load,
        run_dq_tests_fact_shopify_subledger,
        fact_shopify_subledger_aggregate_load,
        run_dq_tests_fact_shopify_subledger_aggregate,
        run_audit_sl,
        run_audit_agg,
        update_workflow_params,
        end,
    )

import logging
from datetime import datetime

from airflow import DAG
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from db_connectors.sf_connector import Snowflake
from helpers.helper import get_absolute_sql_file_path, read_and_parse_sql_file
from tasklib import alerts

BUILD_NUM = '09'
DAG_ID = 'fact_amazon_settlements_backfill'
RELEASE_DEF = '1'

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"

log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2022, 11, 28),
    schedule_interval=None,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'FACT_AMAZON_SETTLEMENTS',
            'author': 'harshad'},
    tags=['Harshad']
) as dag:

    begin_task = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)

    create_merged_staging_historical_and_legacy_task = PythonOperator(
        task_id="create_merged_staging_historical_and_legacy",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "fact_amazon_settlements/backfill/merged_staging_amazon_settlements_historical_and_legacy.sql",
            "wf_params": '{}'
        },
        provide_context=True
    )

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "workflow_name": "{{ params.get('workflow_name') }}"
        },
    )

    list_s3_modified_files_task = PythonOperator(
        task_id="list_s3_modified_files",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={
            "args_file": "fact_amazon_settlements/s3_list_folders.yaml",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake_task = PythonOperator(
        task_id="transfer_s3_to_snowflake",
        python_callable=tlg.transfer_s3_to_snowflake,
        op_kwargs={
            "args_file": "fact_amazon_settlements/s3_to_snowflake.yaml"
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_raw=PythonOperator(
            task_id="task_run_dq_tests_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$raw_db.raw_amazon_settlements",
                'test_name': 'raw_primary_fields_not_null',
                'sql_query': """
                    SELECT CASE WHEN COUNT(1) = COUNT(sellingpartnerid) 
                                 AND COUNT(1) = COUNT(settlement_id)
                                 AND COUNT(1) = COUNT(_daton_batch_runtime) 
                                THEN 0
                                ELSE 1
                           END AS "result"
                    FROM $raw_db.raw_amazon_settlements
                """
                },
            on_failure_callback=alerts.send_failure_alert
        )

    create_deduped_incremental_task = PythonOperator(
        task_id="create_deduped_incremental",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "fact_amazon_settlements/deduped_amazon_settlements_incremental.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    insert_log_incremental_task = PythonOperator(
        task_id="insert_log_incremental",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "fact_amazon_settlements/backfill/log_amazon_settlements_incremental_backfill.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True
    )

    create_staging_incremental_task = PythonOperator(
        task_id="create_staging_incremental",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "fact_amazon_settlements/staging_amazon_settlements_incremental.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True
    )

    insert_merged_staging_incremental_task = PythonOperator(
        task_id="insert_merged_staging_incremental",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "fact_amazon_settlements/backfill/merged_staging_amazon_settlements_incremental_backfill.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True
    )

    create_fact_amazon_settlements_task = PythonOperator(
        task_id="create_fact_amazon_settlements",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "fact_amazon_settlements/backfill/fact_amazon_settlements_backfill.sql",
            "wf_params": '{}'
        },
        provide_context=True
    )

    create_historical_snapshot_task = PythonOperator(
        task_id="create_historical_snapshot",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "fact_amazon_settlements/amazon_settlements_historical_snapshot.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True
    )

    update_workflow_params_task = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    end_task = DummyOperator(task_id="end")

    # --- Assemble the dag ---
    begin_task >> (create_merged_staging_historical_and_legacy_task, get_workflow_params)

    (   
        get_workflow_params >>
        list_s3_modified_files_task >>
        transfer_s3_to_snowflake_task >>
        run_dq_tests_raw >>
        create_deduped_incremental_task >>
        insert_log_incremental_task >>
        create_staging_incremental_task
    )
    
    (
        (create_merged_staging_historical_and_legacy_task, create_staging_incremental_task) >>
        insert_merged_staging_incremental_task >>
        create_fact_amazon_settlements_task >>
        create_historical_snapshot_task >>
        update_workflow_params_task >>
        end_task
    )

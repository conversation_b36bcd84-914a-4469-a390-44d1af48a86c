"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'amazon_ads_attribution_product'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2024, 4, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'AMAZON_ADS_ATTRIBUTION_PRODUCT',
            'author': 'anubhav'},
    tags=['anubhav', 'Raptor']
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
        
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load tg_amazon_ads_attribution_product ---
    
    with TaskGroup(group_id='load_amazon_ads_attribution_product') as tg_amazon_ads_attribution_product:
        list_s3_files_amazon_ads_attribution_product_task = PythonOperator(
            task_id="list_s3_files_amazon_ads_attribution_product",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_attribution_product/list_s3_amazon_ads_attribution_product.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_amazon_ads_attribution_product_task_goessor = PythonOperator(
            task_id="list_s3_files_amazon_ads_attribution_product_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ads_attribution_product/list_s3_amazon_ads_attribution_product_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_amazon_ads_attribution_product_task = DummyOperator(task_id="begin_insert_amazon_ads_attribution_product")
        skip_insert_amazon_ads_attribution_product_task = DummyOperator(task_id="skip_insert_amazon_ads_attribution_product")
        end_insert_amazon_ads_attribution_product_task = DummyOperator(task_id="end_insert_amazon_ads_attribution_product")
        end_amazon_ads_attribution_product_task = DummyOperator(
            task_id="end_amazon_ads_attribution_product",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_amazon_ads_attribution_product_task = BranchPythonOperator(
            task_id='check_new_files_found_amazon_ads_attribution_product',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ads_attribution_product/s3_to_snowflake_amazon_ads_attribution_product.yaml",
                       "args_file_second": "amazon_ads_attribution_product/s3_to_snowflake_amazon_ads_attribution_product_goessor.yaml",
                       "skip_task_id": "load_amazon_ads_attribution_product.skip_insert_amazon_ads_attribution_product",
                       "next_task_id": "load_amazon_ads_attribution_product.begin_insert_amazon_ads_attribution_product"
            },
        )

        s3_to_snowflake_amazon_ads_attribution_product_task = PythonOperator(
            task_id="s3_to_snowflake_amazon_ads_attribution_product",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_attribution_product/s3_to_sf_raw_amazon_ads_attribution_product.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_amazon_ads_attribution_product_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_amazon_ads_attribution_product_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ads_attribution_product/s3_to_sf_raw_amazon_ads_attribution_product_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_amazon_ads_attribution_product',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_amazon_ads_attribution_product',
                    field_list=['reportdate', 'adgroupid', 'campaignid'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        insert_log_amazon_ads_attribution_product_task = PythonOperator(
            task_id="insert_log_amazon_ads_attribution_product",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_attribution_product/insert_log_amazon_ads_attribution_product.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_amazon_ads_attribution_product_task = PythonOperator(
            task_id="dedupe_amazon_ads_attribution_product",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_attribution_product/dedupe_amazon_ads_attribution_product.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_unique_dedupe_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_ads_attribution_product',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amazon_ads_attribution_product',
                    field_list=['reportdate', 'accountid', 'campaignid', 'countryname'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_ads_attribution_product',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amazon_ads_attribution_product',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amazon_ads_attribution_product',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amazon_ads_attribution_product',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_fact_amazon_ads_attribution_product = PythonOperator(
            task_id="merge_fact_amazon_ads_attribution_product",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ads_attribution_product/merge_fact_amazon_ads_attribution_product.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.fact_amazon_ads_attribution_product',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$curated_db.fact_amazon_ads_attribution_product',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.fact_amazon_ads_attribution_product',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$curated_db.fact_amazon_ads_attribution_product',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_fact_amazon_ads_attribution_product = PythonOperator(
            task_id="run_audit_fact_amazon_ads_attribution_product",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_amazon_ads_attribution_product",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                [list_s3_files_amazon_ads_attribution_product_task, list_s3_files_amazon_ads_attribution_product_task_goessor] >>
                check_new_files_found_amazon_ads_attribution_product_task >>
                [begin_insert_amazon_ads_attribution_product_task, skip_insert_amazon_ads_attribution_product_task]
        )

        (
                begin_insert_amazon_ads_attribution_product_task >>
                s3_to_snowflake_amazon_ads_attribution_product_task >>
                s3_to_snowflake_amazon_ads_attribution_product_task_goessor >>
                (run_dq_is_null_raw_task) >>

                insert_log_amazon_ads_attribution_product_task >>
                dedupe_amazon_ads_attribution_product_task >>

                (run_dq_is_unique_dedupe_pk_hard_task,
                 run_dq_is_unique_dedupe_task,
                 run_dq_is_null_dedupe_pk_hard_task) >>

                merge_fact_amazon_ads_attribution_product >>

                (run_dq_is_unique_merge_pk_hard_task,

                 run_dq_is_null_merge_pk_hard_task) >>

                run_audit_fact_amazon_ads_attribution_product >>
                end_insert_amazon_ads_attribution_product_task >>
                end_amazon_ads_attribution_product_task
        )

        skip_insert_amazon_ads_attribution_product_task >> end_amazon_ads_attribution_product_task

    # ---- Main branch ----
    chain(
       begin,
       get_workflow_parameters,
       tg_amazon_ads_attribution_product,
       update_workflow_parameters,
       end
    )
    
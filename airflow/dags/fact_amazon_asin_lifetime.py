from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging
from tasklib import alerts
import tasklib.sql as tlsql
import tasklib.dq as tldq

log = logging.getLogger(__name__)

BUILD_NUM = '1'
RELEASE_DEF = '1'
DAG_ID = 'lifetime_quantity_value'
WF_PARAMS_EXPR = "{'wf_name': '" + DAG_ID.upper() + "'}"

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2024, 2, 1),
    schedule_interval='15 15 1,15 * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'anubhav'},
    tags=['Anubhav']
) as dag:

    create_asin_lifetime_table = PythonOperator(
        task_id="create_asin_lifetime_quantity_value",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "data_analysis/asin_lifetime_value.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_brand_lifetime_table = PythonOperator(
        task_id="create_brand_lifetime_quantity_value",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "data_analysis/brand_lifetime_value.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    chain(
        begin,
        create_asin_lifetime_table,
        create_brand_lifetime_table,
        end
    )

import logging
from datetime import datetime

from airflow import DAG
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator

import tasklib.dq as tldq
import tasklib.sql as tlsql
from tasklib import alerts
from airflow.sensors.sql import SqlSensor
from airflow.utils.task_group import TaskGroup
from airflow.models import Variable

DAG_ID = 'amazon_ads_account_asin_mapping'
TRANSFORM_BASE = DAG_ID
RELEASE_DEF = '1'
BUILD_NUM = '01'
run_date = '{{ ds }}'

WF_PARAMS_EXPR = "{}"

log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2022, 5, 8),
    schedule_interval=Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 4 * * *'),
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'sauvik'},
    tags=['Sauvik']
) as dag:

    with TaskGroup(group_id='tg_amazon_ads_asin_account_mapping') as tg_amazon_ads_asin_account_mapping:
        stg_amz_ads_acc_asin_parent_map = PythonOperator(
            task_id="stg_amz_ads_acc_asin_parent_map",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/stg_amz_ads_acc_asin_parent_map.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        stg_amz_ads_acc_asin_spend_agg = PythonOperator(
            task_id="stg_amz_ads_acc_asin_spend_agg",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/stg_amz_ads_acc_asin_spend_agg.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        stg_amz_ads_acc_asin_listings = PythonOperator(
            task_id="stg_amz_ads_acc_asin_listings",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/stg_amz_ads_acc_asin_listings.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        stg_amz_ads_acc_asin_product_ad = PythonOperator(
            task_id="stg_amz_ads_acc_asin_product_ad",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/stg_amz_ads_acc_asin_product_ad.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        stg_amz_ads_acc_asin_join_all = PythonOperator(
            task_id="stg_amz_ads_acc_asin_join_all",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/stg_amz_ads_acc_asin_join_all.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        run_dq_stg_amz_ads_acc_asin_join_all_asin = PythonOperator(
            task_id="run_dq_stg_amz_ads_acc_asin_join_all_asin",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'stg_amz_ads_acc_asin_join_all asin grain',
                'sql_query': """
                    SELECT 
                        CASE WHEN COUNT(1) > 0 THEN 2 ELSE 0 END as "result"
                    FROM (
                        SELECT 1
                        FROM $stage_db.stg_amz_ads_acc_asin_join_all
                        where asin_rn=1
                        GROUP BY asin,country_code
                        HAVING COUNT(1) > 1
                        LIMIT 1
                    ) T
                """
                },
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_stg_amz_ads_acc_asin_join_all_parent_asin = PythonOperator(
            task_id="run_dq_stg_amz_ads_acc_asin_join_all_parent_asin",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'stg_amz_ads_acc_asin_join_all asin grain',
                'sql_query': """
                    SELECT 
                        CASE WHEN COUNT(1) > 0 THEN 2 ELSE 0 END as "result"
                    FROM (
                        SELECT 1
                        FROM $stage_db.stg_amz_ads_acc_asin_join_all
                        where parent_asin_rn=1
                        and parent_asin is not null
                        GROUP BY parent_asin,country_code
                        HAVING COUNT(1) > 1
                        LIMIT 1
                    ) T
                """
                },
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_stg_amz_ads_acc_asin_is_null_country_code = PythonOperator(
            task_id="run_dq_stg_amz_ads_acc_asin_is_null_country_code",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'country_code_is_null_check_mapping_table',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.stg_amz_ads_acc_asin_join_all', 
                    field_list=['country_code'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        log_stg_amz_ads_acc_asin_join_all = PythonOperator(
            task_id="log_stg_amz_ads_acc_asin_join_all",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/log_stg_amz_ads_acc_asin_join_all.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        amazon_ads_account_asin_mapping = PythonOperator(
            task_id="amazon_ads_account_asin_mapping",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/amazon_ads_account_asin_mapping.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        amazon_ads_account_parent_asin_mapping = PythonOperator(
            task_id="amazon_ads_account_parent_asin_mapping",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/amazon_ads_account_parent_asin_mapping.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        [stg_amz_ads_acc_asin_parent_map, stg_amz_ads_acc_asin_spend_agg, stg_amz_ads_acc_asin_listings, stg_amz_ads_acc_asin_product_ad] >> stg_amz_ads_acc_asin_join_all >> [run_dq_stg_amz_ads_acc_asin_join_all_parent_asin, run_dq_stg_amz_ads_acc_asin_join_all_asin, run_dq_stg_amz_ads_acc_asin_is_null_country_code] >> log_stg_amz_ads_acc_asin_join_all >> [amazon_ads_account_asin_mapping, amazon_ads_account_parent_asin_mapping]


    with TaskGroup(group_id='tg_amazon_ads_account_profile_mapping') as tg_amazon_ads_account_profile_mapping:
        stg_amz_ads_acc_profile_mapping = PythonOperator(
            task_id="stg_amz_ads_acc_profile_mapping",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/stg_amz_ads_acc_profile_mapping.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        run_dq_is_unique_stg_amz_ads_acc_profile_mapping = PythonOperator(
            task_id="run_dq_is_unique_stg_amz_ads_acc_profile_mapping",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.stg_amz_ads_acc_profile_mapping', 
                    field_list=['account_id', 'country_code'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_stg_amz_ads_acc_profile_mapping_is_null_country_code = PythonOperator(
            task_id="run_dq_stg_amz_ads_acc_profile_mapping_is_null_country_code",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'country_code_is_null_check_mapping_table',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.stg_amz_ads_acc_profile_mapping', 
                    field_list=['country_code'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        log_stg_amz_ads_acc_profile_mapping = PythonOperator(
            task_id="log_stg_amz_ads_acc_profile_mapping",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/log_stg_amz_ads_acc_profile_mapping.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        amazon_ads_account_profile_mapping = PythonOperator(
            task_id="amazon_ads_account_profile_mapping",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/amazon_ads_account_profile_mapping.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        stg_amz_ads_acc_profile_mapping >> [run_dq_is_unique_stg_amz_ads_acc_profile_mapping,run_dq_stg_amz_ads_acc_profile_mapping_is_null_country_code] >> log_stg_amz_ads_acc_profile_mapping >> amazon_ads_account_profile_mapping

    

    # --- Audit the data checks ---

    import tasklib.audit as tla

    run_audit_parent_asin = PythonOperator(
        task_id="run_audit_parent_asin",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$adtech_db.amazon_ads_account_parent_asin_mapping",
                   "wf_params": WF_PARAMS_EXPR,
                   "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                   "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                   },
        on_failure_callback=alerts.send_failure_alert
    )

    run_audit_asin = PythonOperator(
        task_id="run_audit_asin",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$adtech_db.amazon_ads_account_asin_mapping",
                   "wf_params": WF_PARAMS_EXPR,
                   "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                   "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                   },
        on_failure_callback=alerts.send_failure_alert
    )

    run_audit_profile_map = PythonOperator(
        task_id="run_audit_profile_map",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$adtech_db.amazon_ads_account_profile_mapping",
                   "wf_params": WF_PARAMS_EXPR,
                   "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                   "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                   },
        on_failure_callback=alerts.send_failure_alert
    )


begin = DummyOperator(task_id="begin")
wait_done = DummyOperator(task_id="wait_done")
create_done = DummyOperator(task_id="create_done")
end = DummyOperator(task_id="end")

wait_for_merge_amazon_ads_sp_product_adex = SqlSensor(
    task_id='wait_for_merge_amazon_ads_sp_product_adex',
    conn_id = 'snowflake_connection',    
    timeout=60 * 60 * 4,
    poke_interval=60 * 5,
    mode="reschedule",
    on_failure_callback=alerts.send_failure_alert,
    sql=
    f"""
        select 
            1 
        from
            dwh.staging.merge_amazon_ads_sp_product_adex
        where fetchdate = '{run_date}'::DATE
        limit 1
    """,
    dag=dag
)

wait_for_fact_amazon_ad_asins = SqlSensor(
    task_id='wait_for_fact_amazon_ad_asins',
    conn_id = 'snowflake_connection',    
    timeout=60 * 60 * 4,
    poke_interval=60 * 5,
    mode="reschedule",
    on_failure_callback=alerts.send_failure_alert,
    sql=
    f"""
        select 
            1 
        from
            dwh.prod.fact_amazon_ad_asins
        where report_date = '{run_date}'::DATE
        limit 1
    """,
    dag=dag
)

begin >> [wait_for_merge_amazon_ads_sp_product_adex,wait_for_fact_amazon_ad_asins] >> wait_done >> [tg_amazon_ads_asin_account_mapping, tg_amazon_ads_account_profile_mapping] >> create_done >> [run_audit_parent_asin, run_audit_asin, run_audit_profile_map] >> end

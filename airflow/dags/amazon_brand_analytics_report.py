"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.utils.task_group import TaskGroup
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON>ythonOperator, PythonOperator
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib.loader import check_for_new_files
from tasklib import alerts

BUILD_NUM = '1'
DAG_ID = 'amazon_brand_analytics_report'
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2023, 2, 6),
    schedule_interval='@hourly',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'AMAZON_BRAND_ANALYTICS_REPORT',
            'author': 'arnab'},
    tags=['arnab', 'Raptor']
) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
        
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_params = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load brand_view_report ---
    
    with TaskGroup(group_id='load_brand_view_report') as tg_brand_view_report:
        list_s3_files_brand_view_report_task = PythonOperator(
            task_id="list_s3_files_brand_view_report",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_brand_analytics_report/list_s3_brand_view_report.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            
        )

        # Need a begin dummy operator for branching
        begin_insert_brand_view_report_task = DummyOperator(task_id="begin_insert_brand_view_report")
        skip_insert_brand_view_report_task = DummyOperator(task_id="skip_insert_brand_view_report")
        end_insert_brand_view_report_task = DummyOperator(task_id="end_insert_brand_view_report")

        from tasklib.loader import S3ToSnowflake

        load_obj = S3ToSnowflake()
        s3_to_snowflake_brand_view_report_task = PythonOperator(
            task_id="s3_to_snowflake_brand_view_report",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_brand_analytics_report/s3_to_sf_raw_brand_view_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_brand_view_report_task = BranchPythonOperator(
            task_id='check_new_files_found_brand_view_report',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_brand_analytics_report/s3_to_snowflake_brand_view_report.yaml",
                       "skip_task_id": "load_brand_view_report.skip_insert_brand_view_report",
                       "next_task_id": "load_brand_view_report.begin_insert_brand_view_report"
            },
        )

        insert_log_brand_view_report_task = PythonOperator(
            task_id="insert_log_brand_view_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/insert_log_brand_view_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_brand_view_report_task = PythonOperator(
            task_id="dedupe_brand_view_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/dedupe_brand_view_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_brand_view_report_task = PythonOperator(
            task_id="merge_stage_brand_view_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/merge_stage_brand_view_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_brand_view_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_brand_view_report', 
                    field_list=['seller_id', 'sub_brand_name', 'qp_brand_query', 'market_place_id', 'weekly_week_end_date'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_raw_task = PythonOperator(
            task_id="run_dq_is_unique_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_brand_view_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$raw_db.raw_brand_view_report', 
                    field_list=['seller_id', 'sub_brand_name', 'qp_brand_query', 'market_place_id', 'weekly_week_end_date', 'report_fetched_and_loaded_at'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_brand_view_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_brand_view_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_brand_view_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_brand_view_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_brand_view_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_brand_view_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_brand_view_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_brand_view_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        merge_fact_brand_view_report_task = PythonOperator(
            task_id="merge_fact_brand_view_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/merge_fact_brand_view_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_unique_fact_pk_task = PythonOperator(
            task_id="run_dq_is_unique_fact_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.fact_brand_view_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$curated_db.fact_brand_view_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_audit_brand_view_report_task = PythonOperator(
            task_id="run_audit_brand_view_report",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_brand_view_report",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )


        (
            list_s3_files_brand_view_report_task >>
            check_new_files_found_brand_view_report_task >>
            [begin_insert_brand_view_report_task, skip_insert_brand_view_report_task]
        )

        (
            begin_insert_brand_view_report_task >> 
            s3_to_snowflake_brand_view_report_task >>
            insert_log_brand_view_report_task >>
            
                ( run_dq_is_null_raw_task
                
                , run_dq_is_unique_raw_task
                ) >> dedupe_brand_view_report_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_brand_view_report_task >>
            
                ( run_dq_is_null_merge_pk_task
              
                , run_dq_is_unique_merge_pk_task
              ) >> merge_fact_brand_view_report_task >> run_dq_is_unique_fact_pk_task >> run_audit_brand_view_report_task >>
            end_insert_brand_view_report_task
        )

    #  --- Load asin_view_report ---
    
    with TaskGroup(group_id='load_asin_view_report') as tg_asin_view_report:
        list_s3_files_asin_view_report_task = PythonOperator(
            task_id="list_s3_files_asin_view_report",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_brand_analytics_report/list_s3_asin_view_report.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            
        )

        # Need a begin dummy operator for branching
        begin_insert_asin_view_report_task = DummyOperator(task_id="begin_insert_asin_view_report")
        skip_insert_asin_view_report_task = DummyOperator(task_id="skip_insert_asin_view_report")
        end_insert_asin_view_report_task = DummyOperator(task_id="end_insert_asin_view_report")


        from tasklib.loader import S3ToSnowflake
        load_obj = S3ToSnowflake()
        s3_to_snowflake_asin_view_report_task = PythonOperator(
            task_id="s3_to_snowflake_asin_view_report",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_brand_analytics_report/s3_to_sf_raw_asin_view_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_asin_view_report_task = BranchPythonOperator(
            task_id='check_new_files_found_asin_view_report',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_brand_analytics_report/s3_to_snowflake_asin_view_report.yaml",
                       "skip_task_id": "load_asin_view_report.skip_insert_asin_view_report",
                       "next_task_id": "load_asin_view_report.begin_insert_asin_view_report"
            },
        )

        insert_log_asin_view_report_task = PythonOperator(
            task_id="insert_log_asin_view_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/insert_log_asin_view_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_asin_view_report_task = PythonOperator(
            task_id="dedupe_asin_view_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/dedupe_asin_view_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        merge_stage_asin_view_report_task = PythonOperator(
            task_id="merge_stage_asin_view_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/merge_stage_asin_view_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_asin_view_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_asin_view_report', 
                    field_list=['seller_id', 'asin', 'qp_asin_query', 'market_place_id', 'weekly_week_end_date'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_raw_task = PythonOperator(
            task_id="run_dq_is_unique_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_asin_view_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$raw_db.raw_asin_view_report', 
                    field_list=['seller_id', 'asin', 'qp_asin_query', 'market_place_id', 'weekly_week_end_date', 'report_fetched_and_loaded_at'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_asin_view_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_asin_view_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_dedupe_pk_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_asin_view_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_asin_view_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_null_merge_pk_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_asin_view_report',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_asin_view_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )
        
        
        run_dq_is_unique_merge_pk_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_asin_view_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_asin_view_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        merge_fact_asin_view_report_task = PythonOperator(
            task_id="merge_fact_asin_view_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/merge_fact_asin_view_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_is_unique_fact_pk_task = PythonOperator(
            task_id="run_dq_is_unique_fact_pk",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.fact_asin_view_report',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$curated_db.fact_asin_view_report', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_audit_asin_view_report_task = PythonOperator(
            task_id="run_audit_asin_view_report",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_asin_view_report",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        (
            list_s3_files_asin_view_report_task >>
            check_new_files_found_asin_view_report_task >>
            [begin_insert_asin_view_report_task, skip_insert_asin_view_report_task]
        )

        (
            begin_insert_asin_view_report_task >> 
            s3_to_snowflake_asin_view_report_task >>
            insert_log_asin_view_report_task >>
            
                ( run_dq_is_null_raw_task
                
                , run_dq_is_unique_raw_task
                ) >> dedupe_asin_view_report_task >>
            
                ( run_dq_is_unique_dedupe_pk_task
              
                , run_dq_is_null_dedupe_pk_task
              ) >> merge_stage_asin_view_report_task >>
            
                ( run_dq_is_null_merge_pk_task
              
                , run_dq_is_unique_merge_pk_task
              ) >> merge_fact_asin_view_report_task >> run_dq_is_unique_fact_pk_task >> run_audit_asin_view_report_task >>
            end_insert_asin_view_report_task
        )

    #  --- Load catalogue_performance_report ---
    with TaskGroup(group_id='load_catalogue_performance_report') as tg_catalogue_performance_report:
        list_s3_files_catalogue_performance_report_task = PythonOperator(
            task_id="list_s3_files_catalogue_performance_report",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_brand_analytics_report/list_s3_catalogue_performance_report.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # Need a begin dummy operator for branching
        begin_insert_catalogue_performance_report_task = DummyOperator(task_id="begin_insert_catalogue_performance_report")
        skip_insert_catalogue_performance_report_task = DummyOperator(task_id="skip_insert_catalogue_performance_report")
        end_insert_catalogue_performance_report_task = DummyOperator(task_id="end_insert_catalogue_performance_report")

        from tasklib.loader import S3ToSnowflake

        load_obj = S3ToSnowflake()
        s3_to_snowflake_catalogue_performance_report_task = PythonOperator(
            task_id="s3_to_snowflake_catalogue_performance_report",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_brand_analytics_report/s3_to_sf_raw_catalogue_performance_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_raw_catalogue_performance_report_task = PythonOperator(
            task_id="run_dq_raw_catalogue_performance_report",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_raw_catalogue_performance_report',
                'tb_name': "$raw_db.raw_catalogue_performance_report",
                'query_file': "amazon_brand_analytics_report/dq_raw_catalogue_performance_report.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_catalogue_performance_report_task = BranchPythonOperator(
            task_id='check_new_files_found_catalogue_performance_report',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_brand_analytics_report/s3_to_snowflake_catalogue_performance_report.yaml",
                       "skip_task_id": "load_catalogue_performance_report.skip_insert_catalogue_performance_report",
                       "next_task_id": "load_catalogue_performance_report.begin_insert_catalogue_performance_report"
            },
        )

        insert_log_catalogue_performance_report_task = PythonOperator(
            task_id="insert_log_catalogue_performance_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/insert_log_catalogue_performance_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_catalogue_performance_report_task = PythonOperator(
            task_id="dedupe_catalogue_performance_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/dedupe_catalogue_performance_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_dedupe_catalogue_performance_report_task = PythonOperator(
            task_id="run_dq_dedupe_catalogue_performance_report",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_dedupe_catalogue_performance_report',
                'tb_name': "$stage_db.dedupe_catalogue_performance_report",
                'query_file': "amazon_brand_analytics_report/dq_dedupe_catalogue_performance_report.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        merge_stage_catalogue_performance_report_task = PythonOperator(
            task_id="merge_stage_catalogue_performance_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/merge_stage_catalogue_performance_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_merge_catalogue_performance_report_task = PythonOperator(
            task_id="run_dq_merge_catalogue_performance_report",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_merge_catalogue_performance_report',
                'tb_name': "$stage_db.merge_catalogue_performance_report",
                'query_file': "amazon_brand_analytics_report/dq_merge_catalogue_performance_report.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        insert_fact_catalogue_performance_report_task = PythonOperator(
            task_id="insert_fact_catalogue_performance_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/insert_fact_catalogue_performance_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_fact_catalogue_performance_report_task = PythonOperator(
            task_id="run_dq_fact_catalogue_performance_report",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_fact_catalogue_performance_report',
                'tb_name': "$curated_db.fact_catalogue_performance_report",
                'query_file': "amazon_brand_analytics_report/dq_fact_catalogue_performance_report.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_audit_catalogue_performance_report_task = PythonOperator(
            task_id="run_audit_catalogue_performance_report",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_catalogue_performance_report",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        chain(
            list_s3_files_catalogue_performance_report_task,
            check_new_files_found_catalogue_performance_report_task,
            [begin_insert_catalogue_performance_report_task, skip_insert_catalogue_performance_report_task],
        )

        chain(
            begin_insert_catalogue_performance_report_task,
            s3_to_snowflake_catalogue_performance_report_task,
            insert_log_catalogue_performance_report_task,
            run_dq_raw_catalogue_performance_report_task,
            dedupe_catalogue_performance_report_task,
            run_dq_dedupe_catalogue_performance_report_task,
            merge_stage_catalogue_performance_report_task,
            run_dq_merge_catalogue_performance_report_task,
            insert_fact_catalogue_performance_report_task,
            run_dq_fact_catalogue_performance_report_task,
            run_audit_catalogue_performance_report_task,
            end_insert_catalogue_performance_report_task,
        )

    #  --- Load asins_keyword_details_report ---
    with TaskGroup(group_id='load_asins_keyword_details_report') as tg_asins_keyword_details_report:
        list_s3_files_asins_keyword_details_report_task = PythonOperator(
            task_id="list_s3_files_asins_keyword_details_report",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_brand_analytics_report/list_s3_asins_keyword_details_report.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            
        )

        # Need a begin dummy operator for branching
        begin_insert_asins_keyword_details_report_task = DummyOperator(task_id="begin_insert_asins_keyword_details_report")
        skip_insert_asins_keyword_details_report_task = DummyOperator(task_id="skip_insert_asins_keyword_details_report")
        end_insert_asins_keyword_details_report_task = DummyOperator(task_id="end_insert_asins_keyword_details_report")

        from tasklib.loader import S3ToSnowflake

        load_obj = S3ToSnowflake()
        s3_to_snowflake_asins_keyword_details_report_task = PythonOperator(
            task_id="s3_to_snowflake_asins_keyword_details_report",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_brand_analytics_report/s3_to_sf_raw_asins_keyword_details_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )


        run_dq_raw_asins_keyword_details_report_task = PythonOperator(
            task_id="run_dq_raw_asins_keyword_details_report",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_raw_asins_keyword_details_report',
                'tb_name': "$raw_db.raw_asins_keyword_details_report",
                'query_file': "amazon_brand_analytics_report/dq_raw_asins_keyword_details_report.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        check_new_files_found_asins_keyword_details_report_task = BranchPythonOperator(
            task_id='check_new_files_found_asins_keyword_details_report',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_brand_analytics_report/s3_to_snowflake_asins_keyword_details_report.yaml",
                       "skip_task_id": "load_asins_keyword_details_report.skip_insert_asins_keyword_details_report",
                       "next_task_id": "load_asins_keyword_details_report.begin_insert_asins_keyword_details_report"
            },
        )

        insert_log_asins_keyword_details_report_task = PythonOperator(
            task_id="insert_log_asins_keyword_details_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/insert_log_asins_keyword_details_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        dedupe_asins_keyword_details_report_task = PythonOperator(
            task_id="dedupe_asins_keyword_details_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/dedupe_asins_keyword_details_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_dedupe_asins_keyword_details_report_task = PythonOperator(
            task_id="run_dq_dedupe_asins_keyword_details_report",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_dedupe_asins_keyword_details_report',
                'tb_name': "$stage_db.dedupe_asins_keyword_details_report",
                'query_file': "amazon_brand_analytics_report/dq_dedupe_asins_keyword_details_report.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        merge_stage_asins_keyword_details_report_task = PythonOperator(
            task_id="merge_stage_asins_keyword_details_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/merge_stage_asins_keyword_details_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_merge_asins_keyword_details_report_task = PythonOperator(
            task_id="run_dq_merge_asins_keyword_details_report",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_merge_asins_keyword_details_report',
                'tb_name': "$stage_db.merge_asins_keyword_details_report",
                'query_file': "amazon_brand_analytics_report/dq_merge_asins_keyword_details_report.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        insert_fact_asins_keyword_details_report_task = PythonOperator(
            task_id="insert_fact_asins_keyword_details_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_brand_analytics_report/insert_fact_asins_keyword_details_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_fact_asins_keyword_details_report_task = PythonOperator(
            task_id="run_dq_fact_asins_keyword_details_report",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_fact_asins_keyword_details_report',
                'tb_name': "$curated_db.fact_asins_keyword_details_report",
                'query_file': "amazon_brand_analytics_report/dq_fact_asins_keyword_details_report.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        run_audit_asins_keyword_details_report_task = PythonOperator(
            task_id="run_audit_asins_keyword_details_report",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_asins_keyword_details_report",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        chain(
            list_s3_files_asins_keyword_details_report_task,
            check_new_files_found_asins_keyword_details_report_task,
            [begin_insert_asins_keyword_details_report_task, skip_insert_asins_keyword_details_report_task],
        )

        chain(
            begin_insert_asins_keyword_details_report_task,
            s3_to_snowflake_asins_keyword_details_report_task,
            insert_log_asins_keyword_details_report_task,
            run_dq_raw_asins_keyword_details_report_task,
            dedupe_asins_keyword_details_report_task,
            run_dq_dedupe_asins_keyword_details_report_task,
            merge_stage_asins_keyword_details_report_task,
            run_dq_merge_asins_keyword_details_report_task,
            insert_fact_asins_keyword_details_report_task,
            run_dq_fact_asins_keyword_details_report_task,
            run_audit_asins_keyword_details_report_task,
            end_insert_asins_keyword_details_report_task
        )

    # ---- Main branch ----
    chain(
        begin,
        get_workflow_params,
        [tg_brand_view_report, tg_asin_view_report, tg_catalogue_performance_report, tg_asins_keyword_details_report],
        update_workflow_params,
        end
    )

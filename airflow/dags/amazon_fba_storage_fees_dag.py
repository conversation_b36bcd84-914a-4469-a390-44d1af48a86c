import logging
from airflow import DAG
from datetime import datetime
from airflow.models import Variable
from airflow.models.baseoperator import chain
from airflow.operators.python import Branch<PERSON>ythonOperator, PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.utils.trigger_rule import TriggerRule
from airflow.utils.task_group import TaskGroup
from tasklib.loader import check_for_new_files
from tasklib import alerts
from tasklib.loader import S3ToSnowflake
load_obj = S3ToSnowflake()
log = logging.getLogger(__name__)

DAG_ID = "amazon_fba_storage_fees"
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 7,13,20 * * *')
S3_TO_SNOWFLAKE_YAML = 'amazon_fba_storage_fees/s3_to_snowflake.yaml'
WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022,7,22),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name':'AMAZON_FBA_STORAGE_FEE_REPORT_DAILY',
            'author':'srichand'
           },
    tags=['Vikas'],
) as dag:

    import tasklib.workflow as tlw

    get_workflow_params=PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )

    import tasklib.s3 as tls3

    list_s3_modified_files=PythonOperator(
        task_id="task_list_s3_modified_files",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"amazon_fba_storage_fees/s3_list_folders.yaml", "wf_params":WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_goessor = PythonOperator(
        task_id="task_list_s3_modified_files_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "amazon_fba_storage_fees/s3_list_folders_goessor.yaml", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    skip_insert_data = DummyOperator(task_id='task_skip_insert_data')

    check_new_files_found = BranchPythonOperator(
        task_id='task_check_new_files_found',
        python_callable=check_for_new_files,
        op_kwargs={
            'args_file': S3_TO_SNOWFLAKE_YAML,
            'skip_task_id': 'task_skip_insert_data',
            'next_task_id': 'task_transfer_s3_to_snowflake',
            'args_file_second': 'amazon_fba_storage_fees/s3_to_snowflake_goessor.yaml',
        }
    )

    import tasklib.glue as tlg
    
    transfer_s3_to_snowflake=PythonOperator(
        task_id="task_transfer_s3_to_snowflake",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_fba_storage_fees/s3_to_sf_raw_amazon_fba_storage_fees_report.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake_goessor = PythonOperator(
        task_id="task_transfer_s3_to_snowflake_goessor",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_fba_storage_fees/s3_to_sf_raw_amazon_fba_storage_fees_report_goessor.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_workflow_params=PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    import tasklib.sql as tlsql
    import tasklib.dq as tldq

    run_dq_tests_raw=PythonOperator(
        task_id="task_run_dq_tests_raw",
        python_callable=tldq.run_dq_tests,
        op_kwargs={"dq_id":"7f20975fad044a91a39ccb611301c1a4"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    insert_raw_amazon_fba_storage_fees_report_log=PythonOperator(
        task_id="task_insert_raw_amazon_fba_storage_fees_report_log",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"inventory/amazon/fact_amazon_fba_storage_fees/insert_raw_amazon_fba_storage_fees_log.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_raw_amazon_fba_storage_fees_dedup=PythonOperator(
        task_id="task_create_raw_amazon_fba_storage_fees_dedup",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"inventory/amazon/fact_amazon_fba_storage_fees/create_raw_amazon_fba_storage_fees_dedup.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    merge_stg_amazon_fba_storage_fees=PythonOperator(
        task_id="task_merge_stg_amazon_fba_storage_fees",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"inventory/amazon/fact_amazon_fba_storage_fees/merge_stg_amazon_fba_storage_fees.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_stg_amazon_fba_storage_fees_report=PythonOperator(
        task_id="task_run_dq_tests_stg_amazon_fba_storage_fees_report",
        python_callable=tldq.run_dq_tests,
        op_kwargs={"dq_id":"4d7e3e7504e54d17a5ed9699e559f1d1"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_fact_amazon_fba_storage_fees=PythonOperator(
        task_id="task_create_stg_fact_amazon_fba_storage_fees",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"inventory/amazon/fact_amazon_fba_storage_fees/create_stg_fact_amazon_fba_storage_fees.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    merge_fact_amazon_fba_storage_fees=PythonOperator(
        task_id="task_merge_fact_amazon_fba_storage_fees",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"inventory/amazon/fact_amazon_fba_storage_fees/merge_fact_amazon_fba_storage_fees.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_fact_amazon_fba_storage_fees=PythonOperator(
        task_id="task_update_fact_amazon_fba_storage_fees",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"inventory/amazon/fact_amazon_fba_storage_fees/update_fact_amazon_fba_storage_fees.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_fact=PythonOperator(
        task_id="task_run_dq_tests_fact",
        python_callable=tldq.run_dq_tests,
        op_kwargs={"dq_id":"25407835680844f79e804409e3ec24ad"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    import tasklib.audit as tla

    run_audit=PythonOperator(
        task_id="task_run_audit",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.FACT_AMAZON_FBA_STORAGE_FEES", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    #  --- Load account_brand_map_report ---

    with TaskGroup(group_id='load_account_brand_map_report') as tg_account_brand_map_report:
        list_s3_files_account_brand_map_report_task = PythonOperator(
            task_id="list_s3_files_account_brand_map_report",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "account_brand_map/list_s3_account_brand_map_report.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_account_brand_map_report_task = DummyOperator(task_id="begin_insert_account_brand_map_report")
        skip_insert_account_brand_map_report_task = DummyOperator(task_id="skip_insert_account_brand_map_report")
        end_insert_account_brand_map_report_task = DummyOperator(task_id="end_insert_account_brand_map_report")
        end_account_brand_map_report_task = DummyOperator(
            task_id="end_account_brand_map_report",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_account_brand_map_report_task = BranchPythonOperator(
            task_id='check_new_files_found_account_brand_map_report',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "account_brand_map/s3_to_snowflake_account_brand_map_report.yaml",
                       "skip_task_id": "load_account_brand_map_report.skip_insert_account_brand_map_report",
                       "next_task_id": "load_account_brand_map_report.begin_insert_account_brand_map_report"
                       },
        )

        s3_to_snowflake_account_brand_map_report_task = PythonOperator(
            task_id="s3_to_snowflake_account_brand_map_report",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "account_brand_map/s3_to_sf_raw_account_brand_map_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_account_brand_map_report_task = PythonOperator(
            task_id="insert_log_account_brand_map_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "inventory/fact_amazon_fba_storage_fees/account_brand_map/insert_log_account_brand_map_report.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_account_brand_map_report_task = PythonOperator(
            task_id="dedupe_account_brand_map_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "inventory/fact_amazon_fba_storage_fees/account_brand_map/dedupe_account_brand_map_report.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                list_s3_files_account_brand_map_report_task >>
                check_new_files_found_account_brand_map_report_task >>
                [begin_insert_account_brand_map_report_task, skip_insert_account_brand_map_report_task]
        )

        (
                begin_insert_account_brand_map_report_task >>
                s3_to_snowflake_account_brand_map_report_task >>

                insert_log_account_brand_map_report_task >>
                dedupe_account_brand_map_report_task >>
                end_insert_account_brand_map_report_task >>
                end_account_brand_map_report_task
        )

        skip_insert_account_brand_map_report_task >> end_account_brand_map_report_task

    begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    chain(
        begin,
        get_workflow_params,
        tg_account_brand_map_report,
        [list_s3_modified_files, list_s3_modified_files_goessor],
        check_new_files_found,
        [skip_insert_data, transfer_s3_to_snowflake]
    )

    chain(
        transfer_s3_to_snowflake,
        transfer_s3_to_snowflake_goessor,
        run_dq_tests_raw,
        insert_raw_amazon_fba_storage_fees_report_log,
        create_raw_amazon_fba_storage_fees_dedup,
        merge_stg_amazon_fba_storage_fees,
        run_dq_tests_stg_amazon_fba_storage_fees_report,
        create_stg_fact_amazon_fba_storage_fees,
        merge_fact_amazon_fba_storage_fees,
        update_fact_amazon_fba_storage_fees,
        run_dq_tests_fact,
        run_audit,
    )

    chain(
        [skip_insert_data, run_audit],
        update_workflow_params,
        end,
    )

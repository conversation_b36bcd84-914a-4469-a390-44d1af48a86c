from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.utils.task_group import TaskGroup
import logging
from tasklib import alerts

log = logging.getLogger(__name__)

WF_PARAMS_EXPR="{'wf_name': 'JUNGLE_SCOUT_RIGEL'}"

with DAG(
    dag_id="jungle_scout_rigel",
    start_date=datetime(2022,5,8),
    schedule_interval='0 3,10 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name':'JUNG<PERSON>_SCOUT_RIGEL',
            'author':'srichand'
           }
) as dag:

    import tasklib.sql as tlsql
    import tasklib.dq as tldq

    create_raw_jungle_scout_ranking_daily=PythonOperator(
        task_id="task_create_raw_jungle_scout_ranking_daily",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"rigel/amazon/jungle_scout/create_raw_jungle_scout_ranking_daily.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    merge_jungle_scout_ranking_daily=PythonOperator(
        task_id="task_merge_jungle_scout_ranking_daily",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"rigel/amazon/jungle_scout/merge_jungle_scout_ranking_daily.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )


    data_latency_dq=PythonOperator(
        task_id="task_data_latency_dq",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'jungle_scout_rigel',
            'tb_name': "$dwh_rigel_db.jungle_scout_ranking_daily",
            'test_name': 'Data latency more than 1 day',
            'sql_query': """
                SELECT CASE WHEN CURRENT_DATE - max_snapshot_date > 1 THEN 2 ELSE 0 END AS "result" FROM
                (
                    SELECT COALESCE(MAX("snapshot_date"), '1900-01-01'::DATE)  max_snapshot_date 
                    FROM  $dwh_rigel_db.jungle_scout_ranking_daily
                )
             """
        },
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    chain(
         begin,
         create_raw_jungle_scout_ranking_daily,
         merge_jungle_scout_ranking_daily,
         data_latency_dq, 
         end,
    )

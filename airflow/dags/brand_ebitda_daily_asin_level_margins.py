""" Data transaformation code generator for Brand EBIDTDA framework """

import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake


BUILD_NUM = '1'
DAG_ID = 'brand_ebitda_asin_level_margins'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2024, 1, 1),
    schedule_interval='0 11 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'BRAND_EBITDA_SKU',
            'author': 'akshay'},
    tags=['akshay', 'Raptor']
) as dag:


    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    #  --- process brand edbidta metrics for _report ---
    
    with TaskGroup(group_id='brand_ebitda_mapping_sku_asin') as tg_brand_ebitda_map_sku_asin:
        process_brand_ebitda_metrics_task = PythonOperator(
            task_id="process_brand_ebitda_map_sku_asin",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/stg_brand_ebitda_sku_asin_map_orders_source.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

    # -- process daily files based on hourly table --
    
    with TaskGroup(group_id='brand_ebitda_daily_revenue_calculation') as tg_brand_ebitda_daily_revenue_calculation:
        process_brand_ebitda_metrics_task = PythonOperator(
            task_id="process_brand_ebitda_daily_revenue_calculation",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/stg_brand_ebitda_asin_level_shipment_revenue.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
    
    
    with TaskGroup(group_id='brand_ebitda_daily_margins_calculations') as tg_brand_ebitda_brand_margins_calculations:
        process_brand_ebitda_daily_margins_calculations = PythonOperator(
            task_id="process_brand_ebitda_daily_margins_calculations",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/stg_brand_ebitda_amazon_asin_level_margin.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        process_brand_ebitda_daily_all_components_margins_calculation = PythonOperator(
            task_id="process_brand_ebitda_daily_all_components_margins_calculation",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/stg_brand_ebitda_amazon_asin_level_margins_all_components.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        process_brand_ebitda_daily_all_components_margins_aggregate_calculations = PythonOperator(
            task_id="process_brand_ebitda_daily_all_components_margins_aggregate_calculations",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/stg_brand_ebitda_amazon_asin_level_margins_all_compo_wbr_aggregate.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        process_brand_ebitda_daily_margins_calculations >> process_brand_ebitda_daily_all_components_margins_calculation >> process_brand_ebitda_daily_all_components_margins_aggregate_calculations

    # --- Main DAG chain ---
    chain(
        begin,
        tg_brand_ebitda_map_sku_asin, # This TaskGroup includes the processing and DQ tasks
        tg_brand_ebitda_daily_revenue_calculation,
        tg_brand_ebitda_brand_margins_calculations,
        end
    )

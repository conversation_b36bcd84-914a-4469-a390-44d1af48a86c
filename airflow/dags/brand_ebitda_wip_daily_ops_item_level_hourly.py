""" Data transaformation code generator for Brand EBIDTDA framework """

import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake


BUILD_NUM = '1'
DAG_ID = 'brand_ebitda_WIP_ops_item_level_hourly_WIP'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2024, 1, 1),
    schedule_interval='30 * * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'BRAND_EBITDA_SKU',
            'author': 'akshay'},
    tags=['akshay', 'Raptor']
) as dag:


    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    #  --- process brand edbidta metrics for _report ---
    
    with TaskGroup(group_id='brand_ebitda_hourly_ops_item_level_all_markets') as tg_brand_ebitda_item_ops_hourly:
        process_brand_ebitda_metrics_task = PythonOperator(
            task_id="process_brand_ebitda_sku_level_ops_hourly",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/wip_brand_ebitda_hourly_ops_item_level_all_markets.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

    with TaskGroup(group_id='brand_ebitda_tiktok_shop_daily_sales') as tg_brand_ebitda_tiktok_shop_sales:
        process_brand_ebitda_metrics_task = PythonOperator(
            task_id="process_brand_ebitda_tiktok_shop_daily_sales",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/wip_brand_ebitda_tiktok_shop_sku_level_daily_sales.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
            

    # -- process daily files based on hourly table --
    
    with TaskGroup(group_id='brand_ebitda_daily_ops_item_level_all_channel_markets') as tg_brand_ebitda_item_ops_daily:
        process_brand_ebitda_metrics_task = PythonOperator(
            task_id="process_brand_ebitda_item_level_ops_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/wip_brand_ebitda_daily_ops_item_level_all_channels_markets.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
    
    
    with TaskGroup(group_id='brand_ebitda_daily_ops_brand_level_all_channel_markets') as tg_brand_ebitda_brand_ops_daily:
        process_brand_ebitda_metrics_task = PythonOperator(
            task_id="process_brand_ebitda_brand_level_ops_daily",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/wip_brand_ebitda_daily_ops_brand_level_all_channels_markets.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

    process_brand_ebitda_metrics_task 

    # --- Main DAG chain ---
    chain(
        begin,
        tg_brand_ebitda_item_ops_hourly, # This TaskGroup includes the processing and DQ tasks
        tg_brand_ebitda_tiktok_shop_sales,
        tg_brand_ebitda_item_ops_daily,
        tg_brand_ebitda_brand_ops_daily,
        end
    )

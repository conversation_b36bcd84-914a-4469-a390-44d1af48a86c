"""
Code generator for airflow ingestion dags.
"""

import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain

# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = "1"
DAG_ID = "fact_seller_central_content_creator"
DAG_SCHEDULE = Variable.get("dag_schedules", deserialize_json=True).get(
    DAG_ID, "@daily"
)
RELEASE_DEF = "1"
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f"Release:{RELEASE_DEF}-Build:{BUILD_NUM}",
    start_date=datetime(2025, 1, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={"workflow_name": "SCAS_CONTENT_CREATOR", "author": "duy"},
    tags=["duy", "Raptor"],
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    begin = DummyOperator(
        task_id="begin", depends_on_past=True, wait_for_downstream=True
    )
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load content_creator ---

    with TaskGroup(group_id="load_content_creator") as tg_content_creator:
        list_s3_files_content_creator_task = PythonOperator(
            task_id="list_s3_files_content_creator",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "seller_central_scraper/content_creator/list_s3_content_creator.yaml",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # Need a begin dummy operator for branching
        begin_insert_content_creator_task = DummyOperator(
            task_id="begin_insert_content_creator"
        )
        skip_insert_content_creator_task = DummyOperator(
            task_id="skip_insert_content_creator"
        )
        end_insert_content_creator_task = DummyOperator(
            task_id="end_insert_content_creator"
        )
        end_content_creator_task = DummyOperator(
            task_id="end_content_creator",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_content_creator_task = BranchPythonOperator(
            task_id="check_new_files_found_content_creator",
            python_callable=check_for_new_files,
            op_kwargs={
                "args_file": "seller_central_scraper/content_creator/s3_to_snowflake_content_creator.yaml",
                "skip_task_id": "load_content_creator.skip_insert_content_creator",
                "next_task_id": "load_content_creator.begin_insert_content_creator",
            },
        )

        s3_to_snowflake_content_creator_task = PythonOperator(
            task_id="s3_to_snowflake_content_creator",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "seller_central_scraper/content_creator/s3_to_sf_raw_content_creator.yaml"
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_content_creator_task = PythonOperator(
            task_id="insert_log_content_creator",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "seller_central_scraper/content_creator/insert_log_content_creator.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_content_creator_task = PythonOperator(
            task_id="dedupe_content_creator",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "seller_central_scraper/content_creator/dedupe_content_creator.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.dedupe_scas_content_creator",
                "test_name": "is_null",
                "sql_query": tldq.gen_check_if_nulls(
                    tb_name="$stage_db.dedupe_scas_content_creator",
                    field_list=["pk"],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.dedupe_scas_content_creator",
                "test_name": "is_unique",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.dedupe_scas_content_creator",
                    field_list=["pk"],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_content_creator_task = PythonOperator(
            task_id="merge_stage_content_creator",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "seller_central_scraper/content_creator/merge_stage_content_creator.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.merge_scas_content_creator",
                "test_name": "is_null",
                "sql_query": tldq.gen_check_if_nulls(
                    tb_name="$stage_db.merge_scas_content_creator",
                    field_list=["pk"],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "tb_name": "$stage_db.merge_scas_content_creator",
                "test_name": "is_unique",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.merge_scas_content_creator",
                    field_list=["pk"],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_fact_content_creator_task = PythonOperator(
            task_id="merge_fact_content_creator",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "seller_central_scraper/content_creator/merge_fact_content_creator.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_content_creator_task = PythonOperator(
            task_id="run_audit_content_creator",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_scas_content_creator",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": "record_created_timestamp_utc",
                "ts_updated_field": "record_updated_timestamp_utc",
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_fact_content_creator_ads_asin = PythonOperator(
            task_id="merge_fact_content_creator_ads_asin",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "seller_central_scraper/content_creator/fact_content_creator_ads_asin.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_content_creator_ads_asin = PythonOperator(
            task_id="run_audit_content_creator_ads_asin",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_content_creator_ads_asin",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": "record_created_timestamp_utc",
                "ts_updated_field": "record_updated_timestamp_utc",
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_fact_content_creator_ads_by_date = PythonOperator(
            task_id="merge_fact_content_creator_ads_by_date",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "seller_central_scraper/content_creator/fact_content_creator_ads_by_date.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_fact_content_creator_ads_by_date_task = PythonOperator(
            task_id="run_audit_fact_content_creator_ads_by_date",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_content_creator_ads_by_date",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": "record_created_timestamp_utc",
                "ts_updated_field": "record_updated_timestamp_utc",
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_fact_content_creator_brands = PythonOperator(
            task_id="merge_fact_content_creator_brands",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "seller_central_scraper/content_creator/fact_content_creator_brands.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_fact_content_creator_brands = PythonOperator(
            task_id="run_audit_fact_content_creator_brands",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_content_creator_brands",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": "record_created_timestamp_utc",
                "ts_updated_field": "record_updated_timestamp_utc",
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_fact_content_creator_campaigns = PythonOperator(
            task_id="merge_fact_content_creator_campaigns",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "seller_central_scraper/content_creator/fact_content_creator_campaigns.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_fact_content_creator_campaigns = PythonOperator(
            task_id="run_fact_content_creator_campaigns",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_content_creator_campaigns",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": "record_created_timestamp_utc",
                "ts_updated_field": "record_updated_timestamp_utc",
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            list_s3_files_content_creator_task
            >> check_new_files_found_content_creator_task
            >> [begin_insert_content_creator_task, skip_insert_content_creator_task]
        )

        (
            begin_insert_content_creator_task
            >> s3_to_snowflake_content_creator_task
            >> insert_log_content_creator_task
            >> dedupe_content_creator_task
            >> (
                run_dq_is_null_dedupe_pk_hard_task,
                run_dq_is_unique_dedupe_pk_hard_task,
            )
            >> merge_stage_content_creator_task
            >> (run_dq_is_null_merge_pk_hard_task, run_dq_is_unique_merge_pk_hard_task)
            >> merge_fact_content_creator_task
            >> run_audit_content_creator_task
            >> end_insert_content_creator_task
            >> end_content_creator_task
        )
        run_audit_content_creator_task >> merge_fact_content_creator_ads_asin >> run_audit_content_creator_ads_asin
        run_audit_content_creator_task >> merge_fact_content_creator_ads_by_date >> run_audit_fact_content_creator_ads_by_date_task
        run_audit_content_creator_task >> merge_fact_content_creator_brands >> run_audit_fact_content_creator_brands
        run_audit_content_creator_task >> merge_fact_content_creator_campaigns >> run_fact_content_creator_campaigns

        skip_insert_content_creator_task >> end_content_creator_task

    # ---- Main branch ----
    chain(
        begin,
        get_workflow_parameters,
        tg_content_creator,
        update_workflow_parameters,
        end,
    )

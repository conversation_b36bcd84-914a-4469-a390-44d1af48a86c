import json
import logging
from datetime import datetime
from airflow import DAG
from airflow.models import Variable
from airflow.models.baseoperator import chain
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.utils.task_group import TaskGroup
import tasklib.dq as tldq
import tasklib.sql as tlsql
import tasklib.s3_monitoring as tls3m
from tasklib import alerts

log = logging.getLogger(__name__)

DAG_ID = "fba_inventory_recon"
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 6 * * *')
WF_PARAMS_EXPR = json.dumps({'execution_timestamp': '{{ ts }}'})

with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2024, 1, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'author': 'vikas'},
    tags=["vikas"],
) as dag:

    run_dq_test_source_ddelay = PythonOperator(
        task_id="run_dq_test_source_ddelay",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            "wk_name": DAG_ID,
            "run_id": "run_dq_test_source_ddelay",
            "tb_name": "$curated_db.FACT_AMAZON_INVENTORY",
            "query_file": "inv_recon/dq_stg_fba_inventory_ddelay.yaml",
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    task_create_fba_inv_raw_checks = PythonOperator(
        task_id='create_fba_inv_raw_checks',
        python_callable=tlsql.run_query_file,
        provide_context=True,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file": "inv_recon/create_fba_inv_raw_checks.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    task_create_pipe_wms_raw_checks = PythonOperator(
        task_id='create_pipe_wms_checks',
        python_callable=tlsql.run_query_file,
        provide_context=True,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "inv_recon/create_pipe_raw_checks.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    task_create_anvyl_raw_checks = PythonOperator(
        task_id='create_anvyl_checks',
        python_callable=tlsql.run_query_file,
        provide_context=True,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "inv_recon/create_anvyl_raw_checks.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    with TaskGroup(group_id="run_unified_fact") as run_unified_fact:

        task_insert_recon_data = PythonOperator(
            task_id='insert_recon_data',
            python_callable=tlsql.run_query_file,
            provide_context=True,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file": "inv_recon/insert_inv_recon_alert_data_log.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            on_failure_callback=alerts.send_failure_alert,
        )


        task_insert_recon_data

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    chain(
        begin,
        run_dq_test_source_ddelay,
        [task_create_fba_inv_raw_checks,
         task_create_pipe_wms_raw_checks,
         task_create_anvyl_raw_checks],
        run_unified_fact,
        end
    )

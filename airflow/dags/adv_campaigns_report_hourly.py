import logging
from datetime import datetime

from airflow import DAG
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib.loader import check_for_new_files
from tasklib import alerts
from tasklib.loader import S3ToSnowflake
load_obj = S3ToSnowflake()

DAG_ID = 'adv_campaigns_report_hourly'
RELEASE_DEF = '1'
BUILD_NUM = '01'

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"

log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2022, 5, 8),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'ADV_CAMPAIGN_REPORT_HOURLY',
            'author': 'sauvik'},
    tags=['Goessor']
) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

    start_raw_load_brands = DummyOperator(task_id="start_raw_load_brands")
    start_raw_load_display = DummyOperator(task_id="start_raw_load_display")
    start_raw_load_product = DummyOperator(task_id="start_raw_load_product")
    start_raw_load_video = DummyOperator(task_id="start_raw_load_video")    

    # ------- Parquet ----------
    list_s3_modified_files_brands = PythonOperator(
        task_id="list_s3_modified_files_brands",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "adv_campaigns_report/brands__list_s3.yaml",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_unified_modified_files_brands = PythonOperator(
        task_id="list_s3_unified_modified_files_brands",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "adv_campaigns_report/brands_unified_list_s3.yaml",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_brands_goessor = PythonOperator(
        task_id="list_s3_modified_files_brands_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "adv_campaigns_report/brands__list_s3_goessor.yaml",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_unified_modified_files_brands_goessor = PythonOperator(
        task_id="list_s3_unified_modified_files_brands_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "adv_campaigns_report/brands_unified_list_s3_goessor.yaml",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    check_new_brand_files_found_task = BranchPythonOperator(
        task_id='check_new_brand_files_found',
        python_callable=check_for_new_files,
        op_kwargs={
            'args_file': 'adv_campaigns_report/brands__s3_to_snowflake.yaml',
            'args_file_second': 'adv_campaigns_report/brands__s3_to_snowflake_goessor.yaml',
            'skip_task_id': 'skip_insert_brand_data',
            'next_task_id': 'insert_brand_data.transfer_s3_to_snowflake_brand',
        }
    )

    list_s3_modified_files_display = PythonOperator(
        task_id="list_s3_modified_files_display",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "adv_campaigns_report/display__list_s3.yaml",
                "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_display_goessor = PythonOperator(
        task_id="list_s3_modified_files_display_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "adv_campaigns_report/display__list_s3_goessor.yaml",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    check_new_display_files_found_task = BranchPythonOperator(
        task_id='check_new_display_files_found',
        python_callable=check_for_new_files,
        op_kwargs={
            'args_file': 'adv_campaigns_report/display__s3_to_snowflake.yaml',
            'args_file_second': 'adv_campaigns_report/display__s3_to_snowflake_goessor.yaml',
            'skip_task_id': 'skip_insert_display_data',
            'next_task_id': 'insert_display_data.transfer_s3_to_snowflake_display',
        }
    )

    list_s3_modified_files_product = PythonOperator(
        task_id="list_s3_modified_files_product",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "adv_campaigns_report/product__list_s3.yaml",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_product_goessor = PythonOperator(
        task_id="list_s3_modified_files_product_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "adv_campaigns_report/product__list_s3_goessor.yaml",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    check_new_product_files_found_task = BranchPythonOperator(
        task_id='check_new_product_files_found',
        python_callable=check_for_new_files,
        op_kwargs={
            'args_file': 'adv_campaigns_report/product__s3_to_snowflake.yaml',
            'args_file_second': 'adv_campaigns_report/product__s3_to_snowflake_goessor.yaml',
            'skip_task_id': 'skip_insert_product_data',
            'next_task_id': 'insert_product_data.transfer_s3_to_snowflake_product',
        }
    )

    list_s3_modified_files_video = PythonOperator(
        task_id="list_s3_modified_files_video",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "adv_campaigns_report/video__list_s3.yaml",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_video_goessor = PythonOperator(
        task_id="list_s3_modified_files_video_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "adv_campaigns_report/video__list_s3_goessor.yaml",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    check_new_video_files_found_task = BranchPythonOperator(
        task_id='check_new_video_files_found',
        python_callable=check_for_new_files,
        op_kwargs={
            'args_file': 'adv_campaigns_report/video__s3_to_snowflake.yaml',
            'args_file_second': 'adv_campaigns_report/video__s3_to_snowflake_goessor.yaml',
            'skip_task_id': 'skip_insert_video_data',
            'next_task_id': 'insert_video_data.transfer_s3_to_snowflake_video',
        }
    )

    skip_insert_brand_data_task = DummyOperator(task_id="skip_insert_brand_data")
    skip_insert_display_data_task = DummyOperator(task_id="skip_insert_display_data")
    skip_insert_product_data_task = DummyOperator(task_id="skip_insert_product_data")
    skip_insert_video_data_task = DummyOperator(task_id="skip_insert_video_data")

    # ------- BRAND DATA --------------------------------------------------
    with TaskGroup(group_id="insert_brand_data") as insert_brand_data_task:

        transfer_s3_to_snowflake_brand = PythonOperator(
            task_id="transfer_s3_to_snowflake_brand",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "adv_campaigns_report/s3_to_sf_raw_amz_adv_campaign_rpt_sponsored_brands.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        transfer_s3_to_snowflake_brand_goessor = PythonOperator(
            task_id="transfer_s3_to_snowflake_brand_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "adv_campaigns_report/s3_to_sf_raw_amz_adv_campaign_rpt_sponsored_brands_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_tests_raw_brand = PythonOperator(
            task_id="audit_dq_raw_brand",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "a3ab091af7ed11ecb00bda6ea7a362d4"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        end_raw_load_brand = DummyOperator(task_id="end_raw_load_brands")

        dedup_stage_brand = PythonOperator(
            task_id="dedup_stage_brand",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "ads/amazon/adv_campaigns_report/dedup_brands.sql",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_tests_dedup_brand = PythonOperator(
            task_id="audit_dq_dedup_brand",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "52c775247c8a3e22946246004a0a18fc"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        merge_brand = PythonOperator(
            task_id="merge_brand",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "ads/amazon/adv_campaigns_report/merge_brands.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        run_dq_tests_prod_brand = PythonOperator(
            task_id="audit_dq_prod_brand",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "d78b267c531931c8993a0f7ee2fbdf03"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        end_load_brand = DummyOperator(task_id="end_load_brand")

        (
            transfer_s3_to_snowflake_brand >>
            transfer_s3_to_snowflake_brand_goessor >>
            run_dq_tests_raw_brand >>
            end_raw_load_brand >>
            dedup_stage_brand >>
            run_dq_tests_dedup_brand >>
            merge_brand >>
            run_dq_tests_prod_brand >>
            end_load_brand
        )

    # ------- DISPLAY DATA ----------------------------------------------------
    with TaskGroup(group_id="insert_display_data") as insert_display_data_task:

        transfer_s3_to_snowflake_display = PythonOperator(
            task_id="transfer_s3_to_snowflake_display",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "adv_campaigns_report/s3_to_sf_raw_amz_adv_campaign_rpt_sponsored_display.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        transfer_s3_to_snowflake_display_goessor = PythonOperator(
            task_id="transfer_s3_to_snowflake_display_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "adv_campaigns_report/s3_to_sf_raw_amz_adv_campaign_rpt_sponsored_display_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_tests_raw_display = PythonOperator(
            task_id="audit_dq_raw_display",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "af98aa52f7ed11ecb00bda6ea7a362d4"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )
        
        end_raw_load_display = DummyOperator(task_id="end_raw_load_display")

        dedup_stage_display = PythonOperator(
            task_id="dedup_stage_display",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "ads/amazon/adv_campaigns_report/dedup_display.sql",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_tests_dedup_display = PythonOperator(
            task_id="audit_dq_dedup_display",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "54a6cd6f268f34ff863fe724f81e6a18"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        merge_display = PythonOperator(
            task_id="merge_display",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "ads/amazon/adv_campaigns_report/merge_display.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        run_dq_tests_prod_display = PythonOperator(
            task_id="audit_dq_prod_display",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "45047a4050f533c08dbe8841aa52285f"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        end_load_display = DummyOperator(task_id="end_load_display")

        (
            transfer_s3_to_snowflake_display >>
            transfer_s3_to_snowflake_display_goessor >>
            run_dq_tests_raw_display >>
            end_raw_load_display >>
            dedup_stage_display >>
            run_dq_tests_dedup_display >>
            merge_display >>
            run_dq_tests_prod_display >>
            end_load_display
        )

    # ------- PRODUCT DATA ----------------------------------------------------
    with TaskGroup(group_id="insert_product_data") as insert_product_data_task:

        transfer_s3_to_snowflake_product = PythonOperator(
            task_id="transfer_s3_to_snowflake_product",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "adv_campaigns_report/s3_to_sf_raw_amz_adv_campaign_rpt_sponsored_product.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        transfer_s3_to_snowflake_product_goessor = PythonOperator(
            task_id="transfer_s3_to_snowflake_product_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "adv_campaigns_report/s3_to_sf_raw_amz_adv_campaign_rpt_sponsored_product_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_tests_raw_product = PythonOperator(
            task_id="audit_dq_raw_product",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "bb8703d6f7ed11ecb00bda6ea7a362d4"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        end_raw_load_product = DummyOperator(task_id="end_raw_load_product")

        dedup_stage_product = PythonOperator(
            task_id="dedup_stage_product",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "ads/amazon/adv_campaigns_report/dedup_product.sql",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_tests_dedup_product = PythonOperator(
            task_id="audit_dq_dedup_product",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "76ff0259675130c2a102170de8ccd16c"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        merge_product = PythonOperator(
            task_id="merge_product",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "ads/amazon/adv_campaigns_report/merge_product.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        run_dq_tests_prod_product = PythonOperator(
            task_id="audit_dq_prod_product",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "31486df36467350a9747c46da3453e2a"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        end_load_product = DummyOperator(task_id="end_load_product")

        (
            transfer_s3_to_snowflake_product >>
            transfer_s3_to_snowflake_product_goessor >>
            run_dq_tests_raw_product >>
            end_raw_load_product >>
            dedup_stage_product >>
            run_dq_tests_dedup_product >>
            merge_product >>
            run_dq_tests_prod_product >>
            end_load_product
        )


    # ------- VIDEO DATA --------------------------------------------------
    with TaskGroup(group_id="insert_video_data") as insert_video_data_task:

        transfer_s3_to_snowflake_video = PythonOperator(
            task_id="transfer_s3_to_snowflake_video",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "adv_campaigns_report/s3_to_sf_raw_amz_adv_campaign_video_rpt_sponsored_brands.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        transfer_s3_to_snowflake_video_goessor = PythonOperator(
            task_id="transfer_s3_to_snowflake_video_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "adv_campaigns_report/s3_to_sf_raw_amz_adv_campaign_video_rpt_sponsored_brands_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        transfer_s3_to_snowflake_brand_unified = PythonOperator(
            task_id="transfer_s3_to_snowflake_brand_unified",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "adv_campaigns_report/s3_to_sf_raw_amz_adv_campaign_unified_rpt_sponsored_brands.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        transfer_s3_to_snowflake_brand_goessor_unified = PythonOperator(
            task_id="transfer_s3_to_snowflake_brand_goessor_unified",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "adv_campaigns_report/s3_to_sf_raw_amz_adv_campaign_unified_rpt_sponsored_brands_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_tests_raw_video = PythonOperator(
            task_id="audit_dq_raw_video",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "c7744870f7ed11ecb00bda6ea7a362d5"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        end_raw_load_video = DummyOperator(task_id="end_raw_load_video")

        dedup_stage_video = PythonOperator(
            task_id="dedup_stage_video",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "ads/amazon/adv_campaigns_report/dedup_video.sql",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        run_dq_tests_dedup_video = PythonOperator(
            task_id="audit_dq_dedup_video",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "0736927c2cba3e3da490222cb5fde1fb"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        merge_video = PythonOperator(
            task_id="merge_video",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "ads/amazon/adv_campaigns_report/merge_video.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        run_dq_tests_prod_video = PythonOperator(
            task_id="audit_dq_prod_video",
            python_callable=tldq.run_dq_tests,
            op_kwargs={"dq_id": "1730aa3f315b3d7e8d8254586895a36c"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        end_load_video = DummyOperator(task_id="end_load_video")

        (
            transfer_s3_to_snowflake_video >>
            transfer_s3_to_snowflake_video_goessor >>
            transfer_s3_to_snowflake_brand_unified >>
            transfer_s3_to_snowflake_brand_goessor_unified >>
            run_dq_tests_raw_video >>
            end_raw_load_video >>
            dedup_stage_video >>
            run_dq_tests_dedup_video >>
            merge_video >>
            run_dq_tests_prod_video >>
            end_load_video
        )   

    dedup_dl_mws = PythonOperator(
        task_id="dedup_dl_mws",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/amazon/adv_campaigns_report/dedup_dl_mws_campaigns_report.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_tests_dedup_dl_mws = PythonOperator(
        task_id="audit_dedup_dl_mws",
        python_callable=tldq.run_dq_tests,
        op_kwargs={"dq_id": "91b104b0085c11ed861d0242ac120002"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    merge_dl_mws = PythonOperator(
        task_id="merge_dl_mws",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/amazon/adv_campaigns_report/merge_dl_mws_campaigns_report.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    import tasklib.audit as tla

    run_audit=PythonOperator(
        task_id="task_run_audit",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.FACT_AMAZON_AD_CAMPAIGNS",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}",
                   "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                   "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                   },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    # --- Update workflow status ---

    update_workflow_params = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    # All completed
    all_loads_completed = DummyOperator(
        task_id="all_loads_completed",
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    # --- Assemble the dag ---
    begin >> get_workflow_params

    # Brands
    start_raw_load_brands >> [list_s3_modified_files_brands, list_s3_modified_files_brands_goessor] >> check_new_brand_files_found_task
    check_new_brand_files_found_task >> [skip_insert_brand_data_task, insert_brand_data_task] >> all_loads_completed

    # Display
    start_raw_load_display >> [list_s3_modified_files_display, list_s3_modified_files_display_goessor] >> check_new_display_files_found_task
    check_new_display_files_found_task >> [skip_insert_display_data_task, transfer_s3_to_snowflake_display] 

    # Product
    start_raw_load_product >> [list_s3_modified_files_product, list_s3_modified_files_product_goessor] >> check_new_product_files_found_task
    check_new_product_files_found_task >> [skip_insert_product_data_task, transfer_s3_to_snowflake_product]
    

    # Video
    start_raw_load_video >> [list_s3_modified_files_video, list_s3_modified_files_video_goessor, list_s3_unified_modified_files_brands,
                              list_s3_unified_modified_files_brands_goessor] >> check_new_video_files_found_task
    check_new_video_files_found_task >> [skip_insert_video_data_task, transfer_s3_to_snowflake_video]

    # - Main Branch -
    begin >> get_workflow_params
    get_workflow_params >> [start_raw_load_brands, start_raw_load_display,
                            start_raw_load_product, start_raw_load_video]

    [end_load_brand, skip_insert_brand_data_task] >> all_loads_completed
    [end_load_display, skip_insert_display_data_task] >> all_loads_completed
    [end_load_product, skip_insert_product_data_task] >> all_loads_completed
    [end_load_video, skip_insert_video_data_task] >> all_loads_completed
    all_loads_completed >> dedup_dl_mws >> run_dq_tests_dedup_dl_mws >> merge_dl_mws >> run_audit >> update_workflow_params >> end

import logging
from airflow import DAG
from airflow.operators.dummy_operator import Du<PERSON><PERSON>perator
from airflow.operators.python import Branch<PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.trigger_rule import TriggerRule
from airflow.operators.python import PythonOperator
from airflow.models import Variable
from airflow.models.baseoperator import chain
from airflow.utils.task_group import TaskGroup
from datetime import datetime

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.sql as tlsql
from tasklib import alerts
import tasklib.s3 as tls3
from tasklib.loader import S3ToSnowflake
import tasklib.workflow as tlw
load_obj = S3ToSnowflake()

log = logging.getLogger(__name__)

DAG_ID = 'dynamic_promotions'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily') # Default to daily runs
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

def check_if_run_planning(skip_task_id, next_task_id, **context):
    '''
    Function to check if the performance planning steps should be run
    The DAG runs multiple times in a day (e.g. hourly or every 2 hours) as the
    historical performance data needs to be frequently updated. The performance planning
    tasks do not need to be refreshed so frequently (need to be run once a day). These
    tasks are controlled by a specific airflow key ('performance_planning') under the 'dag_schedules'
    airflow variable.
    '''
    from croniter import croniter

    # Default to daily runs if variable not specified
    planning_schedule = Variable.get('dag_schedules', deserialize_json=True).get('performance_planning', '@daily')
    cron = croniter(planning_schedule)

    # Check if the Airflow execution timestamp matches the cron expression for performance planning
    # E.g. if the performance planning is set to run at 8am everyday and the current DAG run's next execution
    # timestamp matches (i.e. 8am run), then execute the tasks. For every other DAG run, the performance
    # planning tasks are skipped.
    next_execution_date = datetime.fromtimestamp(context['next_execution_date'].timestamp())
    matched = cron.match(planning_schedule, next_execution_date)
    if matched:
        return next_task_id
    return skip_task_id


def check_if_data_ready(**kwargs):
    '''
    Check if the seller central table was updated
    If yes, update the real time table, else exit
    '''
    from db_connectors.sf_connector import Snowflake
    from helpers.helper import resolve_db_variables
    sf_client = Snowflake()

    query = f"""
    WITH seller_central_orders AS 
        (SELECT
            CONVERT_TIMEZONE('UTC', 'America/Los_Angeles', MAX(report_fetched_and_loaded_at)) AS max_report_fetched_and_loaded_at_pst
        FROM dwh.scs.seller_central_orders
    )
    , fact AS (
        SELECT MAX(max_report_fetched_and_loaded_at_pst) AS max_report_fetched_and_loaded_at_pst
        FROM $curated_db.fact_ongoing_promotions_real_time
    )
    SELECT 
        (CASE WHEN t2.max_report_fetched_and_loaded_at_pst IS NULL
                OR t1.max_report_fetched_and_loaded_at_pst > t2.max_report_fetched_and_loaded_at_pst
              THEN 1 ELSE 0
        END) AS is_table_updated
    FROM seller_central_orders AS t1
    JOIN fact AS t2;"""
    query = resolve_db_variables(query)
    results, _ = sf_client.get_data(query)

    if results[0][0]:
        return 'ongoing_promotions_real_time.create_stg_ongoing_promotions_real_time'
    return 'ongoing_promotions_real_time.skip_ongoing_promotions_real_time'


with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2023, 2, 27),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name':DAG_ID.upper()
            ,'author':'harshad'},
    tags=['Harshad'],
) as dag:

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    with TaskGroup(group_id="landed_cost") as landed_cost_task:
        # Create a landed cost table that will be used for both historical & planning
        create_stg_promotions_landed_cost = PythonOperator(
            task_id="create_stg_promotions_landed_cost",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/landed_cost/create_stg_promotions_landed_cost.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # DQ for landed cost table
        run_dq_stg_promotions_landed_cost = PythonOperator(
            task_id="run_dq_stg_promotions_landed_cost",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_stg_promotions_landed_cost',
                'tb_name': "$stage_db.stg_promotions_landed_cost",
                'query_file': "dynamic_promotions/landed_cost/dq_stg_promotions_landed_cost.yaml"
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        create_stg_promotions_landed_cost >> run_dq_stg_promotions_landed_cost

    with TaskGroup(group_id="historical") as historical_task:

        # Update the sales price discount promotion table
        merge_stg_sales_price_discount_promotion = PythonOperator(
            task_id="merge_stg_sales_price_discount_promotion",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/historical/merge_stg_sales_price_discount_promotion.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # DQ for sales price discount promotion table
        run_dq_check_merge_stg_sales_price_discount_promotion = PythonOperator(
            task_id="run_dq_check_merge_stg_sales_price_discount_promotion",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_check_merge_stg_sales_price_discount_promotion',
                'tb_name': "$stage_db.merge_stg_sales_price_discount_promotion",
                'query_file': "dynamic_promotions/historical/dq_merge_stg_sales_price_discount_promotion.yaml"
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        # Create a combined promotions table including previously calculated sales price
        create_stg_historical_promotions = PythonOperator(
            task_id="create_stg_historical_promotions",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/historical/create_stg_historical_promotions.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # DQ for combined (with sales price promotion) promotions table
        run_dq_stg_historical_promotions = PythonOperator(
            task_id="run_dq_stg_historical_promotions",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_stg_historical_promotions',
                'tb_name': "$stage_db.stg_historical_promotions",
                'query_file': "dynamic_promotions/historical/dq_stg_historical_promotions.yaml"
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        # Create a updated promotions table with baseline dates to use
        create_stg_historical_promotions_baseline_dates = PythonOperator(
            task_id="create_stg_historical_promotions_baseline_dates",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/historical/create_stg_historical_promotions_baseline_dates.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # DQ for updated promotions table
        run_dq_stg_historical_promotions_baseline_dates = PythonOperator(
            task_id="run_dq_stg_historical_promotions_baseline_dates",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_stg_historical_promotions_baseline_dates',
                'tb_name': "$stage_db.stg_historical_promotions_baseline_dates",
                'query_file': "dynamic_promotions/historical/dq_stg_historical_promotions_baseline_dates.yaml"
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        # Create a non-promotions dates table to use for both historical & perfomance planning
        # For historical, we get baseline dates from this table
        # For perfomance planning, we get the base prices from these dates
        create_stg_promotions_non_promotion_dates = PythonOperator(
            task_id="create_stg_promotions_non_promotion_dates",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/historical/create_stg_promotions_non_promotion_dates.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # Create the historical performance table
        create_stg_historical_promotions_performance = PythonOperator(
            task_id="create_stg_historical_promotions_performance",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/historical/create_stg_historical_promotions_performance.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # DQ for historical performance table
        run_dq_stg_historical_promotions_performance = PythonOperator(
            task_id="run_dq_stg_historical_promotions_performance",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_stg_historical_promotions_performance',
                'tb_name': "$stage_db.stg_promotion_performance_metrics",
                'query_file': "dynamic_promotions/historical/dq_stg_historical_promotions_performance.yaml"
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        # Insert into the historical performance fact table
        insert_fact_historical_promotions_performance = PythonOperator(
            task_id="insert_fact_historical_promotions_performance",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/historical/insert_fact_historical_promotions_performance.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_fact_historical_promotions_performance = PythonOperator(
            task_id="run_audit_fact_historical_promotions_performance",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_historical_promotions_performance",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        chain(
            merge_stg_sales_price_discount_promotion,
            run_dq_check_merge_stg_sales_price_discount_promotion,
            create_stg_historical_promotions,
            run_dq_stg_historical_promotions,
            create_stg_promotions_non_promotion_dates,
            create_stg_historical_promotions_baseline_dates,
            run_dq_stg_historical_promotions_baseline_dates,
            create_stg_historical_promotions_performance,
            run_dq_stg_historical_promotions_performance,
            insert_fact_historical_promotions_performance,
            run_audit_fact_historical_promotions_performance,
        )

    skip_performance_planning = DummyOperator(task_id="skip_performance_planning")

    check_run_planning = BranchPythonOperator(
        task_id='check_run_planning',
        python_callable=check_if_run_planning,
        op_kwargs={
            'skip_task_id': 'skip_performance_planning',
            'next_task_id': 'asins_for_planning.create_stg_asins_for_planning',
        },
        provide_context=True,
    )

    with TaskGroup(group_id="asins_for_planning") as asins_for_planning_task:

        # Combine ASINs from planning stage with ASINs from future promotions in historical
        create_stg_asins_for_planning = PythonOperator(
            task_id="create_stg_asins_for_planning",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/asins_for_planning/create_stg_asins_for_planning.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # # DQ for ASINs for planning stage table
        run_dq_stg_asins_for_planning = PythonOperator(
            task_id="run_dq_stg_asins_for_planning",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_stg_performance_planning_asins_for_planning',
                'tb_name': "$stage_db.stg_asins_for_planning",
                'query_file': "dynamic_promotions/asins_for_planning/dq_stg_asins_for_planning.yaml",
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        chain(
            create_stg_asins_for_planning,
            run_dq_stg_asins_for_planning,
        )


    with TaskGroup(group_id="perfomance_planning") as perfomance_planning_task:

        # Asins that needs to be excluded from planning
        list_s3_exclude_planning_asins = PythonOperator(
            task_id="task_list_s3_exclude_planning_asins",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={"args_file": "dynamic_promotions/exclude_planning_asins_s3_list_folders.yaml",
                       "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        transfer_s3_to_snowflake_exclude_planning_asin = PythonOperator(
            task_id="task_transfer_s3_to_snowflake_sexclude_planning_asin",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={
                "args_file": "dynamic_promotions/s3_to_sf_raw_exclude_planning_asins.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        create_stg_exclude_planning_asin = PythonOperator(
            task_id="create_stg_exclude_planning_asin",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": "dynamic_promotions/performance_planning/create_stg_exclude_planning_asins.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # Create the modified historical promotions performance data to use for planning
        create_stg_performance_planning_historical = PythonOperator(
            task_id="create_stg_performance_planning_historical",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/performance_planning/create_stg_performance_planning_historical.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # Create a performance planning scenarios table
        create_stg_performance_planning_scenarios = PythonOperator(
            task_id="create_stg_performance_planning_scenarios",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/performance_planning/create_stg_performance_planning_scenarios.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # DQ for performance planning scenarios table
        run_dq_stg_performance_planning_scenarios = PythonOperator(
            task_id="run_dq_stg_performance_planning_scenarios",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_stg_performance_planning_scenarios',
                'tb_name': "$stage_db.stg_all_promotions_dates", # Placeholder table name but DQ run on different tables
                'query_file': "dynamic_promotions/performance_planning/dq_stg_performance_planning_scenarios.yaml"
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        # Create a performance planning scenarios by month table
        create_stg_performance_planning_scenarios_by_month = PythonOperator(
            task_id="create_stg_performance_planning_scenarios_by_month",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/performance_planning/create_stg_performance_planning_scenarios_by_month.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # DQ for performance planning scenarios by month table
        run_dq_stg_performance_planning_scenarios_by_month = PythonOperator(
            task_id="run_dq_stg_performance_planning_scenarios_by_month",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_stg_performance_planning_scenarios_by_month',
                'tb_name': "$stage_db.stg_all_promotions_dates", # Placeholder table name but DQ run on different tables
                'query_file': "dynamic_promotions/performance_planning/dq_stg_performance_planning_scenarios_by_month.yaml"
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        # Create a performance planning table & add all the scenarios
        create_stg_performance_planning = PythonOperator(
            task_id="create_stg_performance_planning",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/performance_planning/create_stg_performance_planning.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # DQ for performance planning table & add all the scenarios
        run_dq_stg_performance_planning = PythonOperator(
            task_id="run_dq_stg_performance_planning",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_stg_performance_planning',
                'tb_name': "$stage_db.stg_performance_planning",
                'query_file': "dynamic_promotions/performance_planning/dq_stg_performance_planning.yaml"
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        # Create a performance planning table & add all the scenarios
        insert_fact_promotions_performance_planning = PythonOperator(
            task_id="insert_fact_promotions_performance_planning",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/performance_planning/insert_fact_promotions_performance_planning.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # DQ for fact performance planning table
        run_dq_fact_promotions_performance_planning = PythonOperator(
            task_id="run_dq_fact_promotions_performance_planning",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_fact_promotions_performance_planning',
                'tb_name': "$curated_db.fact_promotions_performance_planning",
                'query_file': "dynamic_promotions/performance_planning/dq_fact_promotions_performance_planning.yaml"
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_fact_promotions_performance_planning = PythonOperator(
            task_id="run_audit_fact_promotions_performance_planning",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_promotions_performance_planning",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # Create a promotion planning table to power the tableau dashboard
        insert_dynamic_promotions_promotion_planning = PythonOperator(
            task_id="insert_dynamic_promotions_promotion_planning",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/performance_planning/insert_dynamic_promotions_promotion_planning.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        chain(
            list_s3_exclude_planning_asins,
            transfer_s3_to_snowflake_exclude_planning_asin,
            create_stg_exclude_planning_asin,
            create_stg_performance_planning_historical,
            [create_stg_performance_planning_scenarios, create_stg_performance_planning_scenarios_by_month],
            [run_dq_stg_performance_planning_scenarios, run_dq_stg_performance_planning_scenarios_by_month],
            create_stg_performance_planning,
            run_dq_stg_performance_planning,
            insert_fact_promotions_performance_planning,
            run_dq_fact_promotions_performance_planning,
            run_audit_fact_promotions_performance_planning,
            insert_dynamic_promotions_promotion_planning,
        )        

    with TaskGroup(group_id="actual_vs_scheduled") as actual_vs_scheduled_task:

        # Create a actual vs scheduled table mapping for pks to join
        create_stg_actual_vs_scheduled_mapping = PythonOperator(
            task_id="create_stg_actual_vs_scheduled_mapping",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/actual_vs_scheduled/create_stg_actual_vs_scheduled_mapping.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # DQ for the actual vs scheduled table
        run_dq_stg_actual_vs_scheduled_mapping = PythonOperator(
            task_id="run_dq_stg_actual_vs_scheduled_mapping",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_stg_actual_vs_scheduled_mapping',
                'tb_name': "$stage_db.stg_actual_vs_scheduled_mapping",
                'query_file': "dynamic_promotions/actual_vs_scheduled/dq_stg_actual_vs_scheduled_mapping.yaml"
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        # Create a actual vs scheduled metrics fact table
        create_fact_actual_vs_scheduled_metrics = PythonOperator(
            task_id="create_fact_actual_vs_scheduled_metrics",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/actual_vs_scheduled/create_fact_actual_vs_scheduled_metrics.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        chain(
            create_stg_actual_vs_scheduled_mapping,
            run_dq_stg_actual_vs_scheduled_mapping,
            create_fact_actual_vs_scheduled_metrics,
        )

    import tasklib.replication as tlr

    # replicate latest changes to DYPM
    replicate_to_dypm_sf_to_pg_task = PythonOperator(
        task_id="replicate_to_dypm_sf_to_pg_task",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/deal_ongoing_real_time.yaml"},
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    with TaskGroup(group_id="ongoing_promotions_real_time") as ongoing_promotions_real_time_task:

        check_data_ready = BranchPythonOperator(
            task_id='check_if_data_ready',
            python_callable=check_if_data_ready,
            provide_context=True,
        )

        skip_ongoing_promotions_real_time = DummyOperator(task_id="skip_ongoing_promotions_real_time")

        # Create a real time promotions staging table with required metrics (units, cpea etc)
        create_stg_ongoing_promotions_real_time = PythonOperator(
            task_id="create_stg_ongoing_promotions_real_time",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/ongoing_promotions_real_time/create_stg_ongoing_promotions_real_time.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # Insert real time metrics into a fact table
        insert_fact_ongoing_promotions_real_time = PythonOperator(
            task_id="insert_fact_ongoing_promotions_real_time",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"dynamic_promotions/ongoing_promotions_real_time/insert_fact_ongoing_promotions_real_time.sql",
                "wf_params":WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        check_data_ready >> [create_stg_ongoing_promotions_real_time, skip_ongoing_promotions_real_time]
        create_stg_ongoing_promotions_real_time >> insert_fact_ongoing_promotions_real_time

    begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
    end = DummyOperator(task_id="end", trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,)

    #Assemble the dag
    chain(
        begin,
        get_workflow_params,
        landed_cost_task,
        historical_task,
        check_run_planning,
        [asins_for_planning_task, skip_performance_planning],
    )

    skip_performance_planning >> update_workflow_params

    chain(
        asins_for_planning_task,
        perfomance_planning_task,
        actual_vs_scheduled_task,
        update_workflow_params
    )

    historical_task >> ongoing_promotions_real_time_task >> replicate_to_dypm_sf_to_pg_task >> update_workflow_params

    update_workflow_params >> end
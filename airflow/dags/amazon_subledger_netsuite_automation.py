from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models import Variable
from airflow.sensors.external_task import ExternalTaskSensor

import logging
from tasklib import alerts
import tasklib.workflow as tlw
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.dq as tldq
from tasklib.loader import S3ToSnowflake

load_obj = S3ToSnowflake()
log = logging.getLogger(__name__)

DAG_ID = 'amazon_subledger_netsuite_automation'
WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022,5,8),
    schedule_interval=Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 */4 * * *'),
    catchup=False,
    max_active_runs=1,
    params={'workflow_name':'AMAZON_SUBLEDGER_NETSUITE_AUTOMATION'
            ,'author':"ayush"},
    tags=['Ayush']
) as dag:

    get_workflow_params=PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )

    #seller customer location mapping file
    list_s3_modified_files_seller_customer_location_mapping=PythonOperator(
        task_id="task_list_s3_modified_files_seller_customer_location_mapping",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"amazon_subledger_netsuite_automation/seller_customer_location_mapping_s3_list_folders.yaml",
                   "wf_params":WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake_seller_customer_location_mapping=PythonOperator(
        task_id="task_transfer_s3_to_snowflake_seller_customer_location_mapping",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file":"amazon_subledger_netsuite_automation/s3_to_sf_amazon_seller_customer_location_mapping_stg.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_seller_customer_location_mapping_dedup=PythonOperator(
        task_id="task_create_stg_seller_customer_location_mapping_dedup",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"subledger_netsuite_automation/amazon/create_stg_seller_customer_location_mapping_dedup.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_stg_seller_customer_location_mapping = PythonOperator(
        task_id="task_run_dq_tests_stg_seller_customer_location_mapping",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'amazon_subledger_netsuite_automation',
            'test_name': 'amazon seller customer location mapping duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.amazon_seller_customer_location_mapping ",
                field_list=['"customer_name"','"customer_internal_id"','"seller_id"','"country_code"','"category"','"fulfillment_channel"','"location"','"location_internal_id"','"brand"'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    #cashsale gl item mapping
    list_s3_modified_files_cash_sale_gl_item_mapping=PythonOperator(
        task_id="task_list_s3_modified_files_cash_sale_gl_item_mapping",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"amazon_subledger_netsuite_automation/cash_sale_gl_item_mapping_s3_list_folders.yaml",
                   "wf_params":WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake_cash_sale_gl_item_mapping=PythonOperator(
        task_id="task_transfer_s3_to_snowflake_cash_sale_gl_item_mapping",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file":"amazon_subledger_netsuite_automation/s3_to_sf_amazon_cash_sale_gl_item_mapping_stg.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_seller_cash_sale_gl_item_mapping_dedup=PythonOperator(
        task_id="task_create_stg_cash_sale_gl_item_mapping_dedup",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"subledger_netsuite_automation/amazon/create_stg_cash_sale_gl_item_mapping_dedup.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_stg_cash_sale_gl_item_mapping = PythonOperator(
        task_id="task_run_dq_tests_stg_cash_sale_gl_item_mapping",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'amazon_subledger_netsuite_automation',
            'test_name': 'amazon cashsale gl mapping duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.amazon_cash_sale_gl_item_mapping ",
                field_list=['"type"','"marketplace"','"fulfillment_channel"','"description"','"transaction_type"','"netsuite_item_number"','"netsuite_id"','"netsuite_gl"'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    #brand mapping
    list_s3_modified_files_brand_mapping=PythonOperator(
        task_id="task_list_s3_modified_files_brand_mapping",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"amazon_subledger_netsuite_automation/brand_mapping_s3_list_folders.yaml",
                   "wf_params":WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake_brand_mapping=PythonOperator(
        task_id="task_transfer_s3_to_snowflake_brand_mapping",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file":"amazon_subledger_netsuite_automation/s3_to_sf_brand_mapping_stg.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_brand_mapping_dedup=PythonOperator(
        task_id="task_create_stg_brand_mapping_dedup",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"subledger_netsuite_automation/amazon/create_stg_brand_mapping_dedup.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_stg_brand_mapping = PythonOperator(
        task_id="task_run_dq_tests_stg_brand_mapping",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'amazon_subledger_netsuite_automation',
            'test_name': 'netsuite brand mapping duplicate check',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.brand_mapping",
                field_list=['"netsuite_brand_id"', '"brand"'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    #subledger load
    fact_amazon_subledger_load=PythonOperator(
        task_id="task_fact_amazon_subledger_load",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"subledger_netsuite_automation/amazon/fact_amazon_subledger_load.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_fact_amazon_subledger =PythonOperator(
        task_id="task_run_dq_tests_fact_amazon_subledger",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': "amazon_subledger_netsuite_automation",
            'run_id': 'task_run_dq_tests_fact_amazon_subledger',
            'query_file': "subledger_netsuite_automation/amazon/amazon_subledger_dq.yaml"
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    #subledger aggregate load
    fact_amazon_subledger_aggregate_load=PythonOperator(
        task_id="task_fact_amazon_subledger_aggregate_load",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"subledger_netsuite_automation/amazon/fact_amazon_subledger_aggregate_load.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_fact_amazon_subledger_aggregate = PythonOperator(
        task_id="task_run_dq_tests_fact_amazon_subledger_aggregate",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': 'amazon_subledger_netsuite_automation',
            'run_id': 'task_run_dq_tests_fact_amazon_subledger_aggregate',
            'query_file': "subledger_netsuite_automation/amazon/amazon_subledger_agg_dq.yaml"
        },
        on_failure_callback=alerts.send_failure_alert
    )

    #workflow update
    update_workflow_params=PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )


    begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    wait_on_amazon_settlement_transactions_scs = ExternalTaskSensor(
        task_id="wait_on_amazon_settlement_transactions_scs",
        external_dag_id="amazon_settlement_transactions_scs",
        external_task_id="end",
        allowed_states=["success"],
        poke_interval=60 * 3,
        mode="reschedule",
        timeout=60 * 60 * 4,
        on_failure_callback=alerts.send_failure_alert)

    # settlements daily transaction file
    start_load_settlement_transactions = DummyOperator(task_id="start_raw_load_settlement_transactions")
    end_load_settlement_transactions = DummyOperator(task_id="end_raw_load_settlement_transactions")

    #seller customer location mapping file
    start_raw_load_seller_customer_location_mapping = DummyOperator(task_id="start_raw_load_seller_customer_location_mapping")
    end_raw_load_seller_customer_location_mapping = DummyOperator(task_id="end_raw_load_seller_customer_location_mapping")
    end_stg_load_seller_customer_location_mapping = DummyOperator(task_id="end_stg_load_seller_customer_location_mapping")

    #cashsale gl item mapping
    start_raw_load_cash_sale_gl_item_mapping = DummyOperator(task_id="start_raw_load_cash_sale_gl_item_mapping")
    end_raw_load_cash_sale_gl_item_mapping = DummyOperator(task_id="end_raw_load_cash_sale_gl_item_mapping")
    end_stg_load_cash_sale_gl_item_mapping = DummyOperator(task_id="end_stg_load_cash_sale_gl_item_mapping")

    #brand mapping
    start_raw_load_brand_mapping = DummyOperator(task_id="start_raw_load_brand_mapping")
    end_raw_load_brand_mapping = DummyOperator(task_id="end_raw_load_brand_mapping")
    end_stg_load_brand_mapping = DummyOperator(task_id="end_stg_load_brand_mapping")

    #All completed
    all_staging_loads_completed = DummyOperator(task_id="all_staging_loads_completed")
    #all_loads_completed = DummyOperator(task_id="all_loads_completed")

    #Assemble the dag
    begin >> get_workflow_params

    # settlements daily transaction file
    start_load_settlement_transactions >> wait_on_amazon_settlement_transactions_scs >> end_load_settlement_transactions

    #seller customer location mapping file
    start_raw_load_seller_customer_location_mapping >> list_s3_modified_files_seller_customer_location_mapping >> transfer_s3_to_snowflake_seller_customer_location_mapping >> end_raw_load_seller_customer_location_mapping >> create_stg_seller_customer_location_mapping_dedup >> run_dq_tests_stg_seller_customer_location_mapping >> end_stg_load_seller_customer_location_mapping

    #cashsale gl item mapping
    start_raw_load_cash_sale_gl_item_mapping >> list_s3_modified_files_cash_sale_gl_item_mapping >> transfer_s3_to_snowflake_cash_sale_gl_item_mapping >> end_raw_load_cash_sale_gl_item_mapping >> create_stg_seller_cash_sale_gl_item_mapping_dedup >> run_dq_tests_stg_cash_sale_gl_item_mapping >> end_stg_load_cash_sale_gl_item_mapping

    #brand mapping
    start_raw_load_brand_mapping >> list_s3_modified_files_brand_mapping >> transfer_s3_to_snowflake_brand_mapping >> end_raw_load_brand_mapping >> create_stg_brand_mapping_dedup >> run_dq_tests_stg_brand_mapping >> end_stg_load_brand_mapping

    #Main Branch
    begin >> get_workflow_params

    get_workflow_params >> [start_load_settlement_transactions,start_raw_load_seller_customer_location_mapping,start_raw_load_cash_sale_gl_item_mapping,start_raw_load_brand_mapping]

    [end_load_settlement_transactions,end_stg_load_seller_customer_location_mapping,end_stg_load_cash_sale_gl_item_mapping,end_stg_load_brand_mapping] >> all_staging_loads_completed
    
    all_staging_loads_completed >> fact_amazon_subledger_load >> run_dq_tests_fact_amazon_subledger  >> fact_amazon_subledger_aggregate_load >> run_dq_tests_fact_amazon_subledger_aggregate >> update_workflow_params >> end
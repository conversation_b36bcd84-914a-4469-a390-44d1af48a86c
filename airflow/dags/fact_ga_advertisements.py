from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging
from tasklib import alerts

log = logging.getLogger(__name__)
DAG_ID = 'fact_ga_advertisements'

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
        dag_id=DAG_ID,
        start_date=datetime(2022, 5, 8),
        schedule_interval='0 */4 * * *',
        catchup=False,
        max_active_runs=1,
        params={'workflow_name': DAG_ID,
                'author': 'prash<PERSON>jeet'
                },
        tags=["google_analytics"]
) as dag:
    import tasklib.workflow as tlw
    import tasklib.s3 as tls3
    import tasklib.glue as tlg
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

    list_s3_modified_files_campaign_level_v5 = PythonOperator(
        task_id="task_list_s3_modified_files_campaign_level_v5",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "fact_ga_advertisements/s3_list_folders_v5.yaml", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_campaign_level_v2 = PythonOperator(
        task_id="task_list_s3_modified_files_campaign_level_v2",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "fact_ga_advertisements/s3_list_folders_v2.yaml", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake_campaign_level_v5 = PythonOperator(
        task_id="task_transfer_s3_to_snowflake_campaign_level_v5",
        python_callable=tlg.transfer_s3_to_snowflake,
        op_kwargs={"args_file": "fact_ga_advertisements/s3_to_snowflake_v5.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake_campaign_level_v2 = PythonOperator(
        task_id="task_transfer_s3_to_snowflake_campaign_level_v2",
        python_callable=tlg.transfer_s3_to_snowflake,
        op_kwargs={"args_file": "fact_ga_advertisements/s3_to_snowflake_v2.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_raw_campaign_level_v5 = PythonOperator(
        task_id="create_stg_raw_campaign_level_v5",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/google/fact_ga_advertisements/create_stg_raw_v5.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_raw_campaign_level_v2 = PythonOperator(
        task_id="create_stg_raw_campaign_level_v2",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/google/fact_ga_advertisements/create_stg_raw_v2.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_dq_raw_join_colms_must_not_be_null_v5 = PythonOperator(
        task_id="raw_join_colms_must_not_be_null_v5",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'raw_join_colms_must_not_be_null_v5',
            'tb_name': "$stage_db.STG_RAW_GA_CAMPAIGN_LEVEL_V5",
            'query_file': "ads/google/fact_ga_advertisements/dq_raw_v5.yaml"
        },
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_raw_join_colms_must_not_be_null_v2 = PythonOperator(
        task_id="raw_join_colms_must_not_be_null_v2",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'raw_join_colms_must_not_be_null_v2',
            'tb_name': "$stage_db.STG_RAW_GA_CAMPAIGN_LEVEL_V2",
            'query_file': "ads/google/fact_ga_advertisements/dq_raw_v2.yaml"
        },
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_transformed_raw_campaign_level_v5 = PythonOperator(
        task_id="create_stg_transformed_raw_campaign_level_v5",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/google/fact_ga_advertisements/stg_transformed_v5.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    create_stg_transformed_raw_campaign_level_v2 = PythonOperator(
        task_id="create_stg_transformed_raw_campaign_level_v2",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/google/fact_ga_advertisements/stg_transformed_v2.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    merge_raw_campaign_level_v5 = PythonOperator(
        task_id="merge_raw_campaign_level_v5",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/google/fact_ga_advertisements/merge_raw_v5.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    merge_raw_campaign_level_v2 = PythonOperator(
        task_id="merge_raw_campaign_level_v2",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/google/fact_ga_advertisements/merge_raw_v2.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )


    join_v2_v5 = PythonOperator(
        task_id="join_v2_v5",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/google/fact_ga_advertisements/join_v2_v5.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    merge_curated_ga_advertisements = PythonOperator(
        task_id="merge_curated_ga_advertisements",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "ads/google/fact_ga_advertisements/merge_curated_ga_advertisements.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    audit_curated_ga_advertisements = PythonOperator(
        task_id="audit_curated_ga_advertisements",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.fact_ga_advertisements",
                   "wf_params": WF_PARAMS_EXPR,
                   "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                   "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                   },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    chain(
        begin,
        get_workflow_params,
        [list_s3_modified_files_campaign_level_v5, list_s3_modified_files_campaign_level_v2],
        [transfer_s3_to_snowflake_campaign_level_v5, transfer_s3_to_snowflake_campaign_level_v2],
        [create_stg_raw_campaign_level_v5,create_stg_raw_campaign_level_v2],
        [run_dq_raw_join_colms_must_not_be_null_v5,run_dq_raw_join_colms_must_not_be_null_v2],
        [create_stg_transformed_raw_campaign_level_v5,create_stg_transformed_raw_campaign_level_v2],
        [merge_raw_campaign_level_v5,merge_raw_campaign_level_v2],
        join_v2_v5,
        merge_curated_ga_advertisements,
        audit_curated_ga_advertisements,
        update_workflow_params,
        end,
    )

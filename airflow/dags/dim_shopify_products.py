import logging
from airflow import DAG
from airflow.models.baseoperator import chain
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON>Operator, PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.utils.trigger_rule import TriggerRule
from datetime import datetime

from tasklib.loader import check_for_new_files

log = logging.getLogger(__name__)

S3_TO_SNOWFLAKE_YAML = 'dim_shopify_products/s3_to_snowflake.yaml'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id="DIM_SHOPIFY_PRODUCTS",
    start_date=datetime(2022, 5, 8),
    schedule_interval="@hourly",
    catchup=False,
    max_active_runs=1,
    tags=["shopify", "products", 'Vikas'],
    params={
        "workflow_name": "DIM_SHOPIFY_PRODUCTS",
        "author": "vikas"
    },
) as dag:
    import tasklib.workflow as tlw
    import tasklib.s3 as tls3
    import tasklib.glue as tlg
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts
    from tasklib.loader import S3ToSnowflake
    load_obj = S3ToSnowflake()

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    list_s3_modified_files = PythonOperator(
        task_id="task_list_s3_modified_files",
        python_callable=tls3.list_s3_modified_files,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "args_file": "dim_shopify_products/list_s3_files.yaml",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    skip_merge = DummyOperator(task_id='skip_merge')

    check_new_files_found = BranchPythonOperator(
        task_id='check_new_files_found',
        python_callable=check_for_new_files,
        op_kwargs={
            'args_file': S3_TO_SNOWFLAKE_YAML,
            'skip_task_id': 'skip_merge',
            'next_task_id': 'transfer_s3_to_snowflake',
            "args_file_second": "dim_shopify_products/s3_to_snowflake_goessor.yaml",
        }
    )

    transfer_s3_to_snowflake = PythonOperator(
        task_id="transfer_s3_to_snowflake",
        python_callable=load_obj.s3_to_snowflake_load,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "args_file": "dim_shopify_products/s3_to_sf_raw_dim_shopify_products.yaml"},
    )

    list_s3_modified_files_goessor = PythonOperator(
        task_id="task_list_s3_modified_files_goessor",
        python_callable=tls3.list_s3_modified_files,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "args_file": "dim_shopify_products/list_s3_files_goessor.yaml",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    transfer_s3_to_snowflake_goessor = PythonOperator(
        task_id="transfer_s3_to_snowflake_goessor",
        python_callable=load_obj.s3_to_snowflake_load,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "args_file": "dim_shopify_products/s3_to_sf_raw_dim_shopify_products_goessor.yaml"},
    )

    update_workflow_params = PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    merge_stg_dim_shopify_products = PythonOperator(
        task_id="merge_stg_dim_shopify_products",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "products/shopify/merge_stg_dim_shopify_products.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
    )

    begin = DummyOperator(
        task_id="begin", depends_on_past=True, wait_for_downstream=True
    )
    end = DummyOperator(task_id="end")

    chain(
        begin,
        get_workflow_params,
        [list_s3_modified_files,list_s3_modified_files_goessor],
        check_new_files_found,
        [transfer_s3_to_snowflake, skip_merge]
    )

    transfer_s3_to_snowflake >>transfer_s3_to_snowflake_goessor >> merge_stg_dim_shopify_products
    [merge_stg_dim_shopify_products, skip_merge] >> update_workflow_params >> end

from tasklib import alerts
import tasklib.replication as tlr
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models import Variable
from airflow import DAG
from airflow.models.baseoperator import chain
import logging
from datetime import datetime
import warnings
warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)

DAG_ID = 'seo_data_replication'


with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2023, 9, 29),
    schedule_interval=Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '30 1,13 * * *'),
    catchup=False,
    max_active_runs=1,
    params={"author": "arnab"},
    tags=['Arnab', 'DO NOT PAUSE']
) as dag:

    replicate_seo_item_mappings = PythonOperator(
        task_id="replicate_item_mappings",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/seo/seo_item_mappings.yaml"}
    )

    replicate_seo_fact_amazon_listings = PythonOperator(
        task_id="replicate_fact_amazon_listings",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/seo/seo_fact_amazon_listings.yaml"}
    )

    replicate_seo_fact_all_asin_rollup = PythonOperator(
        task_id="replicate_seo_fact_all_asin_rollup",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/seo/seo_fact_all_asin_rollup.yaml"}
    )

    replicate_seo_fact_amazon_reviews = PythonOperator(
        task_id="replicate_seo_fact_amazon_reviews",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/seo/seo_fact_amazon_reviews.yaml"}
    )

    replicate_seo_merge_asin_view_report = PythonOperator(
        task_id="replicate_seo_merge_asin_view_report",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/seo/seo_merge_asin_view_report.yaml"}
    )

    replicate_seo_stg_amazon_competitor_data = PythonOperator(
        task_id="replicate_seo_stg_amazon_competitor_data",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/seo/seo_stg_amazon_competitor_data.yaml"}
    )

    replicate_seo_merge_product_opportunity_explorer_report = PythonOperator(
        task_id="replicate_seo_merge_product_opportunity_explorer_report",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/seo/seo_merge_product_opportunity_explorer_report.yaml"}
    )

    replicate_seo_pricing_shopify_amazon_sku_mapping = PythonOperator(
        task_id="replicate_seo_pricing_shopify_amazon_sku_mapping",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/seo/seo_pricing_shopify_amazon_sku_mapping.yaml"}
    )

    replicate_seo_fact_amazon_organic_keyword_rank = PythonOperator(
        task_id="replicate_seo_fact_amazon_organic_keyword_rank",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/seo/seo_fact_amazon_organic_keyword_rank.yaml"}
    )

    replicate_seo_amazon_ad_keywords_spend_data = PythonOperator(
        task_id="replicate_seo_amazon_ad_keywords_spend_data",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/seo/seo_amazon_ad_keywords_spend_data.yaml"}
    )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    chain(
        begin,
        [
            replicate_seo_item_mappings,
            replicate_seo_fact_amazon_listings,
            replicate_seo_fact_all_asin_rollup,
            replicate_seo_fact_amazon_reviews,
            replicate_seo_stg_amazon_competitor_data,
            replicate_seo_merge_asin_view_report,
            replicate_seo_merge_product_opportunity_explorer_report,
            replicate_seo_pricing_shopify_amazon_sku_mapping,
            replicate_seo_fact_amazon_organic_keyword_rank,
            replicate_seo_amazon_ad_keywords_spend_data,
        ],
        end
    )

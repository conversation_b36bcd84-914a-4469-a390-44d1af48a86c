from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.models import Variable

from tasklib import alerts
import tasklib.workflow as tlw
import tasklib.sql as tlsql
import monitor_sql_dl as mndl
import monitor_sql_dc as mndc

DAG_ID = 'monitor_hourly'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '30 14,17,19,21,3 * * *')
WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022, 10, 26),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    tags=['Brian', 'Monitor'],
    params={
      "workflow_name": "MONITOR_HOURLY",
      "author": "brian.nunes"
    }
) as dag:

    get_workflow_params=PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )

    monitor_business_sql=PythonOperator(
        task_id="task_processing_hourly_business_metadata",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"monitor/monitor_business.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    monitor_sql=PythonOperator(
        task_id="task_processing_hourly_metadata",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"monitor/monitor_hourly.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    monitor_sql_dl=PythonOperator(
        task_id="task_sending_data_delay_alerts",
        python_callable=mndl.run_query_monitor_dl,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    monitor_sql_dc=PythonOperator(
        task_id="task_sending_data_completed_alerts",
        python_callable=mndc.run_query_monitor_dc,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")
    
    #Assemble the dag
    chain(
        begin, 
        get_workflow_params, 
        monitor_business_sql,
        monitor_sql, 
        monitor_sql_dc,
        monitor_sql_dl,
        end,
    )

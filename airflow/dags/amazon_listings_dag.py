from airflow import DAG
from datetime import datetime
from airflow.models import Variable
from airflow.models.baseoperator import chain
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
import logging
from tasklib import alerts

log = logging.getLogger(__name__)

DAG_ID = "amazon_listings"
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 0,1,7,8,13,18,22,23 * * *')
WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022,5,8),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name':'AMAZON_LISTINGS_HOURLY',
            'author':'srichand'
           }
) as dag:

    import tasklib.workflow as tlw

    get_workflow_params=PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )

    import tasklib.s3 as tls3

    list_s3_modified_files=PythonOperator(
        task_id="task_list_s3_modified_files",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"amazon_listings/s3_list_folders.yaml", "wf_params":WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    from tasklib.loader import S3ToSnowflake
    load_obj = S3ToSnowflake()
    
    transfer_s3_to_snowflake=PythonOperator(
        task_id="task_transfer_s3_to_snowflake",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file":"amazon_listings/s3_to_sf_raw_amazon_listings.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_goessor = PythonOperator(
        task_id="task_list_s3_modified_files_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": "amazon_listings/s3_list_folders_goessor.yaml", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    transfer_s3_to_snowflake_goessor = PythonOperator(
        task_id="task_transfer_s3_to_snowflake_goessor",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_listings/s3_to_sf_raw_amazon_listings_goessor.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )
    
    import tasklib.dq as tldq

    run_dq_tests_raw = PythonOperator(
        task_id="task_run_dq_tests_raw",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'task_run_dq_tests_raw',
            'tb_name': "$raw_db.raw_amazon_listings",
            'query_file': "listings/dq_raw_amazon_listings.yaml"
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    import tasklib.sql as tlsql

    insert_raw_amazon_listings_log=PythonOperator(
        task_id="task_insert_raw_amazon_listings_log",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"listings/insert_raw_amazon_listings_log.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    merge_stg_amazon_listings=PythonOperator(
        task_id="task_merge_stg_amazon_listings",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"listings/merge_stg_amazon_listings.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_staging = PythonOperator(
        task_id="task_run_dq_tests_staging",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'task_run_dq_tests_staging',
            'tb_name': "$stage_db.stg_amazon_listings",
            'query_file': "listings/dq_stg_amazon_listings.yaml"
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    create_stg_fact_amazon_listings=PythonOperator(
        task_id="task_create_stg_fact_amazon_listings",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"listings/create_stg_fact_amazon_listings.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_staging_fact = PythonOperator(
        task_id="task_run_dq_tests_staging_fact",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'task_run_dq_tests_staging_fact',
            'tb_name': "$stage_db.stg_fact_amazon_listings",
            'query_file': "listings/dq_stg_fact_amazon_listings.yaml"
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    merge_fact_amazon_listings=PythonOperator(
        task_id="task_merge_fact_amazon_listings",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"listings/merge_fact_amazon_listings.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_fact_amazon_listings=PythonOperator(
        task_id="task_update_fact_amazon_listings",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"listings/update_fact_amazon_listings.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_fact = PythonOperator(
        task_id="task_run_dq_tests_fact",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'task_run_dq_tests_fact',
            'tb_name': "$curated_db.fact_amazon_listings",
            'query_file': "listings/dq_fact_amazon_listings.yaml"
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    import tasklib.audit as tla

    run_audit=PythonOperator(
        task_id="task_run_audit",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.FACT_AMAZON_LISTINGS", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_workflow_params=PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    chain(
         begin,
         get_workflow_params,
         [list_s3_modified_files, list_s3_modified_files_goessor],
         transfer_s3_to_snowflake,
         transfer_s3_to_snowflake_goessor,
         run_dq_tests_raw, 
         insert_raw_amazon_listings_log,
         merge_stg_amazon_listings,
         run_dq_tests_staging, 
         create_stg_fact_amazon_listings,
         run_dq_tests_staging_fact,
         merge_fact_amazon_listings,
         update_fact_amazon_listings,
         run_dq_tests_fact,
         update_workflow_params,
         run_audit,
         end,
    )

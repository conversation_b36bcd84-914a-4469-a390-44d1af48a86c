from airflow import DAG
from datetime import datetime
from airflow.utils.task_group import TaskGroup
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain

import logging

log = logging.getLogger(__name__)

def __get_snow_session_parameters():
    from db_connectors.sf_connector import Snowflake 
    query='SHOW PARAMETERS'
    sf = Snowflake()
    data, num_rows = sf.get_data(sql_statement=query, query_tag=None, as_dict=True)
    log.info(data) 

with DAG(
    dag_id="print_snow_session_parameters",
    start_date=datetime(2023, 10, 31),
    schedule_interval="@hourly",
    catchup=False,
    max_active_runs=1,
    params={
        "author": "srichand"
    },
    tags=['RETIRED'],
) as dag:
    get_snow_session_parameters = PythonOperator(
        task_id="task_get_session_parameters",
        python_callable=__get_snow_session_parameters,
    )

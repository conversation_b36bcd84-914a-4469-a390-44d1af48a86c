from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging
from tasklib import alerts

log = logging.getLogger(__name__)

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id="SHOPIFY_LOCATIONS",
    start_date=datetime(2022, 5, 8),
    schedule_interval="@hourly",
    catchup=False,
    max_active_runs=1,
    params={"workflow_name": "SHOPIFY_LOCATIONS", "author": "ruchira"},
    tags=["RETIRED","shopify"],
) as dag:

    import tasklib.workflow as tlw
    import tasklib.s3 as tls3
    import tasklib.glue as tlg
    import tasklib.dq as tldq
    import tasklib.sql as tlsql
    import tasklib.audit as tla
    from tasklib import alerts

    get_workflow_params = PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"},
    )

    list_s3_modified_files = PythonOperator(
        task_id="task_list_s3_modified_files",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={
            "args_file": "shopify_locations/s3_list_folders.yaml",
            "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    transfer_s3_to_snowflake = PythonOperator(
        task_id="task_transfer_s3_to_snowflake",
        python_callable=tlg.transfer_s3_to_snowflake,
        op_kwargs={"args_file": "shopify_locations/s3_to_snowflake.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    list_s3_modified_files_goessor = PythonOperator(
        task_id="task_list_s3_modified_files_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={
            "args_file": "shopify_locations/s3_list_folders_goessor.yaml",
            "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    transfer_s3_to_snowflake_goessor = PythonOperator(
        task_id="task_transfer_s3_to_snowflake_goessor",
        python_callable=tlg.transfer_s3_to_snowflake,
        op_kwargs={"args_file": "shopify_locations/s3_to_snowflake_goessor.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    merge_curated = PythonOperator(
        task_id="merge_curated",
        python_callable=tlsql.run_query_file,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={
                "connection": "Snowflake",
                "sql_file": "shopify_locations/merge_curated.sql",
                "wf_params": WF_PARAMS_EXPR,
        },
    )

    run_dq_id_is_unique = PythonOperator(
        task_id="id_is_unique",
        python_callable=tldq.run_dq_tests,
        op_kwargs={"dq_id": "8f41daa5-b3f7-4798-b363-9b1c2563cfc1"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    run_audit = PythonOperator(
        task_id="task_run_audit",
        python_callable=tla.run_audit,
        op_kwargs={
            "table_name": "$stage_db.SHOPIFY_LOCATIONS",
            "wf_params": WF_PARAMS_EXPR,
            "ts_created_field": "record_created_timestamp_utc",
            "ts_updated_field":"record_updated_timestamp_utc"
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_workflow_params=PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(
        task_id="begin", depends_on_past=True, wait_for_downstream=True
    )
    end = DummyOperator(task_id="end")

    chain(
        begin,
        get_workflow_params,
        [list_s3_modified_files,
        list_s3_modified_files_goessor],
        transfer_s3_to_snowflake,
        transfer_s3_to_snowflake_goessor,
        merge_curated,
        run_dq_id_is_unique,
        run_audit,
        update_workflow_params,
        end,
    )

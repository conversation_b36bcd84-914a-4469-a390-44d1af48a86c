"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'fba_customer_shipment_sales_report'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@hourly')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2024, 6, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'Ayush Aman'},
    tags=['Ayush', 'Raptor']
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load fba_customer_shipment_sales_report ---
    
    with TaskGroup(group_id='load_fba_customer_shipment_sales_report') as tg_fba_customer_shipment_sales_report:
        list_s3_files_fba_customer_shipment_sales_report_task = PythonOperator(
            task_id="list_s3_files_fba_customer_shipment_sales_report",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fba_customer_shipment_sales_report/list_s3_fba_customer_shipment_sales_report.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        list_s3_files_fba_customer_shipment_sales_report_task_goessor = PythonOperator(
            task_id="list_s3_files_fba_customer_shipment_sales_report_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "fba_customer_shipment_sales_report/list_s3_fba_customer_shipment_sales_report_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        # Need a begin dummy operator for branching
        begin_insert_fba_customer_shipment_sales_report_task = DummyOperator(task_id="begin_insert_fba_customer_shipment_sales_report")
        skip_insert_fba_customer_shipment_sales_report_task = DummyOperator(task_id="skip_insert_fba_customer_shipment_sales_report")
        end_insert_fba_customer_shipment_sales_report_task = DummyOperator(task_id="end_insert_fba_customer_shipment_sales_report")
        end_fba_customer_shipment_sales_report_task = DummyOperator(
            task_id="end_fba_customer_shipment_sales_report",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_fba_customer_shipment_sales_report_task = BranchPythonOperator(
            task_id='check_new_files_found_fba_customer_shipment_sales_report',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "fba_customer_shipment_sales_report/s3_to_snowflake_fba_customer_shipment_sales_report.yaml",
                       "args_file_second": "fba_customer_shipment_sales_report/s3_to_snowflake_fba_customer_shipment_sales_report_goessor.yaml",
                       "skip_task_id": "load_fba_customer_shipment_sales_report.skip_insert_fba_customer_shipment_sales_report",
                       "next_task_id": "load_fba_customer_shipment_sales_report.begin_insert_fba_customer_shipment_sales_report"
            },
        )

        s3_to_snowflake_fba_customer_shipment_sales_report_task = PythonOperator(
            task_id="s3_to_snowflake_fba_customer_shipment_sales_report",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fba_customer_shipment_sales_report/s3_to_sf_raw_fba_customer_shipment_sales_report.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_fba_customer_shipment_sales_report_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_fba_customer_shipment_sales_report_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "fba_customer_shipment_sales_report/s3_to_sf_raw_fba_customer_shipment_sales_report_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_fba_customer_shipment_sales_report_task = PythonOperator(
            task_id="insert_log_fba_customer_shipment_sales_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "fba_customer_shipment_sales_report/insert_log_fba_customer_shipment_sales_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_fba_customer_shipment_sales_report_task = PythonOperator(
            task_id="dedupe_fba_customer_shipment_sales_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "fba_customer_shipment_sales_report/dedupe_fba_customer_shipment_sales_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_fba_customer_shipment_sales_report_task = PythonOperator(
            task_id="merge_stage_fba_customer_shipment_sales_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "fba_customer_shipment_sales_report/merge_stage_fba_customer_shipment_sales_report.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_tests_merge_stage_fba_customer_shipment_sales_report = PythonOperator(
            task_id="task_run_dq_tests_merge_stage_fba_customer_shipment_sales_report",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'task_run_dq_tests_merge_stage_fba_customer_shipment_sales_report',
                'query_file': "fba_customer_shipment_sales_report/merge_dq.yaml"
            },
            on_failure_callback=alerts.send_failure_alert
        )

        fact_fba_customer_shipment_sales_report_task = PythonOperator(
            task_id="fact_fba_customer_shipment_sales_report",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "fba_customer_shipment_sales_report/fact_fba_customer_shipment_sales_report.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_fba_customer_shipment_sales_report_task = PythonOperator(
            task_id="run_audit_fba_customer_shipment_sales_report",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_fba_customer_shipment_sales_report",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        (
            [list_s3_files_fba_customer_shipment_sales_report_task, list_s3_files_fba_customer_shipment_sales_report_task_goessor] >>
            check_new_files_found_fba_customer_shipment_sales_report_task >>
            [begin_insert_fba_customer_shipment_sales_report_task, skip_insert_fba_customer_shipment_sales_report_task]
        )

        (
            begin_insert_fba_customer_shipment_sales_report_task >> 
            s3_to_snowflake_fba_customer_shipment_sales_report_task >>
            s3_to_snowflake_fba_customer_shipment_sales_report_task_goessor >>
            insert_log_fba_customer_shipment_sales_report_task >>
            dedupe_fba_customer_shipment_sales_report_task >>
    
            merge_stage_fba_customer_shipment_sales_report_task >>
            run_dq_tests_merge_stage_fba_customer_shipment_sales_report >>
            fact_fba_customer_shipment_sales_report_task >>
            run_audit_fba_customer_shipment_sales_report_task >>
            end_insert_fba_customer_shipment_sales_report_task >>
            end_fba_customer_shipment_sales_report_task
        )

        skip_insert_fba_customer_shipment_sales_report_task >> end_fba_customer_shipment_sales_report_task

    # ---- Main branch ----
    chain(
       begin,
       get_workflow_parameters,
       tg_fba_customer_shipment_sales_report,
       update_workflow_parameters,
       end
    )
    
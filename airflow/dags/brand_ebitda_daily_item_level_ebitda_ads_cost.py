""" Data transaformation code generator for Brand EBIDTDA framework """

import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake


BUILD_NUM = '1'
DAG_ID = 'brand_ebitda_item_level_ads_cost'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2024, 1, 1),
    schedule_interval='0 11 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'brand_ebitda_item_level_ads_cost',
            'author': 'akshay'},
    tags=['akshay', 'Raptor']
) as dag:


    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")
        
        
    #-- process item level ppc costs -- 
    
    with TaskGroup(group_id='process_item_level_ppc_cost') as tg_brand_ebitda_item_ppc_cost:
        process_brand_ebitda_item_ppc_cost_task = PythonOperator(
            task_id="process_item_level_ppc_cost",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/brand_ebitda_daily_item_level_amz_ppc_spend.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

    with TaskGroup(group_id='process_brand_level_fb_cost') as tg_brand_ebitda_brand_fb_cost:
        process_ebitda_brand_fb_cost_task = PythonOperator(
            task_id="process_brand_level_fb_cost",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/brand_ebitda_daily_brand_level_fb_spend.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

    with TaskGroup(group_id='process_brand_level_google_cost') as tg_brand_ebitda_brand_google_cost:
        process_brand_ebitda_brand_google_cost_task = PythonOperator(
            task_id="process_brand_level_google_cost",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/brand_ebitda_daily_brand_level_google_spend.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

    with TaskGroup(group_id='process_item_level_google_cost') as tg_brand_ebitda_item_google_cost:
        process_brand_ebitda_item_google_cost_task = PythonOperator(
            task_id="process_item_level_google_cost",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/brand_ebitda_daily_item_level_google_spend.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
    
    with TaskGroup(group_id='process_item_level_all_ads_cost_consolidated') as tg_brand_ebitda_item_level_all_ads_cost_consolidated:
        process_brand_ebitda_item_all_ads_cost_consolidated = PythonOperator(
            task_id="process_item_level_all_ads_cost_consolidated",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/brand_ebitda_daily_item_level_ads_costs_consolidated.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

    # process_brand_ebitda_metrics_task 

    # --- Main DAG chain ---
    chain(
        begin, # This TaskGroup includes the processing and DQ tasks
        tg_brand_ebitda_item_ppc_cost,
        tg_brand_ebitda_brand_fb_cost,
        tg_brand_ebitda_brand_google_cost,
        tg_brand_ebitda_item_google_cost,
        tg_brand_ebitda_item_level_all_ads_cost_consolidated,
        end
    )

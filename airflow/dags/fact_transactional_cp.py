from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging
from tasklib import alerts
import tasklib.workflow as tlw
import tasklib.s3 as tls3
import tasklib.glue as tlg
import tasklib.sql as tlsql
import tasklib.audit as tla
import tasklib.dq as tldq
from airflow.sensors.external_task import ExternalTaskSensor

log = logging.getLogger(__name__)

WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"

with DAG(
    dag_id="fact_transactional_cp",
    start_date=datetime(2022,5,8),
    schedule_interval='@hourly',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name':'FACT_TRANSACTIONAL_CP'
            ,'author':'ayush'},
    tags=['Ayush', 'DEPRECATED', 'RETIRED', 'DO NOT ENABLE']
) as dag:

    get_workflow_params=PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )

    #stage landed_cost
    stg_landed_cost=PythonOperator(
        task_id="task_stg_landed_cost",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"fact_transactional_cp/stage_landed_cost.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

   #stage material_cost
    stg_material_cost=PythonOperator(
        task_id="task_stg_material_cost",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"fact_transactional_cp/stage_material_cost.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_landed_cost =PythonOperator(
        task_id="task_run_dq_tests_landed_cost",
        python_callable=tldq.run_dq_tests,
        op_kwargs={"dq_id":"12b6f1929bb7032dd1937444e045b27a"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    #tcp load
    create_stg_fact_transactional_cp=PythonOperator(
        task_id="create_stg_fact_transactional_cp",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"fact_transactional_cp/create_stg_fact_transactional_cp.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    delete_insert_fact_transactional_cp=PythonOperator(
        task_id="delete_insert_fact_transactional_cp",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"fact_transactional_cp/delete_insert_fact_transactional_cp.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    monthly_update_fact_transactional_cp=PythonOperator(
        task_id="monthly_update_fact_transactional_cp",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"fact_transactional_cp/monthly_update_fact_transactional_cp.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_dq_tests_fact_transactional_cp =PythonOperator(
        task_id="task_run_dq_tests_transactional_cp",
        python_callable=tldq.run_dq_tests,
        op_kwargs={"dq_id":"7be2d864f33f13567bde9c36ebaabf0a"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    #workflow update
    update_workflow_params=PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )


    begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
    end = DummyOperator(task_id="end")
    upstream_dependencies_successful = DummyOperator(task_id="upstream_dependencies_successful")

    wait_on_fact_all_revenue = ExternalTaskSensor(task_id="wait_on_fact_all_revenue",
                               external_dag_id="fact_all_revenue",
                               external_task_id="end",
                               allowed_states=["success"],
                               poke_interval=60 * 3,
                               mode="reschedule",
                               timeout=60 * 60 * 4,
                               on_failure_callback=alerts.send_failure_alert 
                               )

    wait_on_amazon_refunds = ExternalTaskSensor(task_id="wait_on_amazon_refunds",
                               external_dag_id="amazon_refunds_hourly",
                               external_task_id="end",
                               allowed_states=["success"],
                               poke_interval=60 * 3,
                               mode="reschedule",
                               timeout=60 * 60 * 4,
                               on_failure_callback=alerts.send_failure_alert 
                               )

    wait_on_shopify_refunds = ExternalTaskSensor(task_id="wait_on_shopify_refunds",
                               external_dag_id="shopify_refunds_hourly",
                               external_task_id="end",
                               allowed_states=["success"],
                               poke_interval=60 * 3,
                               mode="reschedule",
                               timeout=60 * 60 * 4,
                               on_failure_callback=alerts.send_failure_alert 
                               )

    wait_on_amazon_fee_preview = ExternalTaskSensor(task_id="wait_on_amazon_fee_preview",
                               external_dag_id="amazon_fee_preview_hourly_v1",
                               external_task_id="end",
                               allowed_states=["success"],
                               poke_interval=60 * 3,
                               mode="reschedule",
                               timeout=60 * 60 * 4,
                               on_failure_callback=alerts.send_failure_alert 
                               )

    #Assemble the dag 
    begin >> get_workflow_params >> [wait_on_fact_all_revenue,wait_on_amazon_refunds,wait_on_shopify_refunds,wait_on_amazon_fee_preview] >> upstream_dependencies_successful >> stg_landed_cost >>stg_material_cost >> run_dq_tests_landed_cost >> create_stg_fact_transactional_cp >> delete_insert_fact_transactional_cp >> monthly_update_fact_transactional_cp >> run_dq_tests_fact_transactional_cp >> update_workflow_params >> end

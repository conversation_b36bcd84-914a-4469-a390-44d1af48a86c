import logging
from datetime import datetime

from airflow import DAG
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.sensors.external_task import ExternalTaskSensor

import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import S3ToSnowflake
load_obj = S3ToSnowflake()

DAG_ID = 'fact_amazon_ad_keywords'
TRANSFORM_BASE = 'ads/amazon/' + DAG_ID
RELEASE_DEF = '1'
BUILD_NUM = '01'

WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"

log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2022, 5, 8),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'abhishek'},
    tags=['Sauvik','Goessor']
) as dag:

    get_workflow_params = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
    )

    

    # ------- Report Data ----------
    
    list_s3_modified_files_brands = PythonOperator(
        task_id="list_s3_modified_files_brands",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/brands_list_s3.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    transfer_s3_to_snowflake_brands = PythonOperator(
        task_id="transfer_s3_to_snowflake_brands",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "fact_amazon_ad_keywords/s3_to_sf_raw_amazon_sponsoredbrands_keywordsreport.yaml"},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    list_s3_modified_files_brands_goessor = PythonOperator(
        task_id="list_s3_modified_files_brands_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/brands_list_s3_goessor.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    transfer_s3_to_snowflake_brands_goessor = PythonOperator(
        task_id="transfer_s3_to_snowflake_brands_goessor",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "fact_amazon_ad_keywords/s3_to_sf_raw_amazon_sponsoredbrands_keywordsreport_goessor.yaml"},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_transfer_s3_to_snowflake_brands = PythonOperator(
        task_id="run_dq_transfer_s3_to_snowflake_brands",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$raw_db.RAW_AMAZON_SPONSOREDBRANDS_KEYWORDSREPORT",
            'test_name': 'identifiers_not_nulls',
            'sql_query': tldq.gen_check_if_nulls(
                tb_name="$raw_db.RAW_AMAZON_SPONSOREDBRANDS_KEYWORDSREPORT", 
                field_list=['_DATON_BATCH_RUNTIME','KEYWORDID','ADGROUPID','ACCOUNTID','CAMPAIGNID','REPORTDATE'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_brands_video = PythonOperator(
        task_id="list_s3_modified_files_brands_video",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/brands_video_list_s3.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    transfer_s3_to_snowflake_brands_video = PythonOperator(
        task_id="transfer_s3_to_snowflake_brands_video",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "fact_amazon_ad_keywords/s3_to_sf_raw_amazon_sponsoredbrands_keywordsvideoreport.yaml"},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    list_s3_modified_files_brands_video_goessor = PythonOperator(
        task_id="list_s3_modified_files_brands_video_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/brands_video_list_s3_goessor.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    transfer_s3_to_snowflake_brands_video_goessor = PythonOperator(
        task_id="transfer_s3_to_snowflake_brands_video_goessor",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "fact_amazon_ad_keywords/s3_to_sf_raw_amazon_sponsoredbrands_keywordsvideoreport_goessor.yaml"},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_transfer_s3_to_snowflake_brands_video = PythonOperator(
        task_id="run_dq_transfer_s3_to_snowflake_brands_video",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$raw_db.RAW_AMAZON_SPONSOREDBRANDS_KEYWORDSVIDEOREPORT",
            'test_name': 'identifiers_not_nulls',
            'sql_query': tldq.gen_check_if_nulls(
                tb_name="$raw_db.RAW_AMAZON_SPONSOREDBRANDS_KEYWORDSVIDEOREPORT", 
                field_list=['_DATON_BATCH_RUNTIME','KEYWORDID','ADGROUPID','ACCOUNTID','CAMPAIGNID','REPORTDATE'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    list_s3_modified_files_products = PythonOperator(
        task_id="list_s3_modified_files_products",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/products_list_s3.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    transfer_s3_to_snowflake_products = PythonOperator(
        task_id="transfer_s3_to_snowflake_products",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "fact_amazon_ad_keywords/s3_to_sf_raw_amazon_sponsoredproducts_keywordreport.yaml"},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    list_s3_modified_files_products_goessor = PythonOperator(
        task_id="list_s3_modified_files_products_goessor",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file": DAG_ID + "/products_list_s3_goessor.yaml",
                   "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    transfer_s3_to_snowflake_products_goessor = PythonOperator(
        task_id="transfer_s3_to_snowflake_products_goessor",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "fact_amazon_ad_keywords/s3_to_sf_raw_amazon_sponsoredproducts_keywordreport_goessor.yaml"},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_transfer_s3_to_snowflake_products = PythonOperator(
        task_id="run_dq_transfer_s3_to_snowflake_products",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$raw_db.RAW_AMAZON_SPONSOREDPRODUCTS_KEYWORDREPORT",
            'test_name': 'identifiers_not_nulls',
            'sql_query': tldq.gen_check_if_nulls(
                tb_name="$raw_db.RAW_AMAZON_SPONSOREDPRODUCTS_KEYWORDREPORT", 
                field_list=['_DATON_BATCH_RUNTIME','KEYWORDID','ADGROUPID','ACCOUNTID','CAMPAIGNID','REPORTDATE'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    # --- Dedup tasks --------------

    dedup_brands = PythonOperator(
        task_id="dedup_brands",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/dedup_brands.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_dedup_brands = PythonOperator(
        task_id="run_dq_dedup_brands",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.DEDUP_AMAZON_SPONSOREDBRANDS_KEYWORDSREPORT",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.DEDUP_AMAZON_SPONSOREDBRANDS_KEYWORDSREPORT", 
                field_list=['advertisement_pk'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    dedup_products = PythonOperator(
        task_id="dedup_products",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/dedup_products.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_dedup_products = PythonOperator(
        task_id="run_dq_dedup_products",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.DEDUP_AMAZON_SPONSOREDPRODUCTS_KEYWORDREPORT",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.DEDUP_AMAZON_SPONSOREDPRODUCTS_KEYWORDREPORT", 
                field_list=['advertisement_pk'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    dedup_brands_video = PythonOperator(
        task_id="dedup_brands_video",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/dedup_brands_video.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_dedup_brands_video = PythonOperator(
        task_id="run_dq_dedup_brands_video",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.DEDUP_AMAZON_SPONSOREDBRANDSVIDEO_KEYWORDSREPORT",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.DEDUP_AMAZON_SPONSOREDBRANDSVIDEO_KEYWORDSREPORT", 
                field_list=['advertisement_pk'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    # --- Merge Tasks ---
    merge_brands = PythonOperator(
        task_id="merge_brands",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/merge_brands.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    # run_dq_merge_brands = PythonOperator(
    #     task_id="run_dq_merge_brands",
    #     python_callable=tldq.run_dq_string,
    #     op_kwargs={
    #         'wk_name': DAG_ID,
    #         'tb_name': "$stage_db.MERGED_AMAZON_SPONSOREDBRANDS_KEYWORDSREPORT",
    #         'test_name': 'pk_not_duplicate',
    #         'sql_query': tldq.gen_check_unique_key(
    #             tb_name="$stage_db.MERGED_AMAZON_SPONSOREDBRANDS_KEYWORDSREPORT",
    #             field_list=['advertisement_pk'],
    #             hard_alert=True
    #         )
    #         },
    #     on_failure_callback=alerts.send_failure_alert
    # )

    merge_products = PythonOperator(
        task_id="merge_products",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/merge_products.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    # run_dq_merge_products = PythonOperator(
    #     task_id="run_dq_merge_products",
    #     python_callable=tldq.run_dq_string,
    #     op_kwargs={
    #         'wk_name': DAG_ID,
    #         'tb_name': "$stage_db.MERGED_AMAZON_SPONSOREDPRODUCTS_KEYWORDREPORT",
    #         'test_name': 'pk_not_duplicate',
    #         'sql_query': tldq.gen_check_unique_key(
    #             tb_name="$stage_db.MERGED_AMAZON_SPONSOREDPRODUCTS_KEYWORDREPORT",
    #             field_list=['advertisement_pk'],
    #             hard_alert=True
    #         )
    #         },
    #     on_failure_callback=alerts.send_failure_alert
    # )

    merge_brands_video = PythonOperator(
        task_id="merge_brands_video",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/merge_brands_video.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    # run_dq_merge_brands_video = PythonOperator(
    #     task_id="run_dq_merge_brands_video",
    #     python_callable=tldq.run_dq_string,
    #     op_kwargs={
    #         'wk_name': DAG_ID,
    #         'tb_name': "$stage_db.MERGED_AMAZON_SPONSOREDBRANDSVIDEO_KEYWORDSREPORT",
    #         'test_name': 'pk_not_duplicate',
    #         'sql_query': tldq.gen_check_unique_key(
    #             tb_name="$stage_db.MERGED_AMAZON_SPONSOREDBRANDSVIDEO_KEYWORDSREPORT",
    #             field_list=['advertisement_pk'],
    #             hard_alert=True
    #         )
    #         },
    #     on_failure_callback=alerts.send_failure_alert
    # )

    dedupe_join_all = PythonOperator(
        task_id="dedupe_join_all",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/dedupe_join_all.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_dedupe_join_all = PythonOperator(
        task_id="run_dq_dedupe_join_all",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.DEDUP_FACT_AMAZON_AD_KEYWORDS",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.DEDUP_FACT_AMAZON_AD_KEYWORDS", 
                field_list=['advertisement_pk'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    merge_all = PythonOperator(
        task_id="merge_all",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/merge_all.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    # run_dq_merge_all = PythonOperator(
    #     task_id="run_dq_merge_all",
    #     python_callable=tldq.run_dq_string,
    #     op_kwargs={
    #         'wk_name': DAG_ID,
    #         'tb_name': "$curated_db.FACT_AMAZON_AD_KEYWORDS",
    #         'test_name': 'pk_not_duplicate',
    #         'sql_query': tldq.gen_check_unique_key(
    #             tb_name="$curated_db.FACT_AMAZON_AD_KEYWORDS",
    #             field_list=['advertisement_pk'],
    #             hard_alert=True
    #         )
    #         },
    #     on_failure_callback=alerts.send_failure_alert
    # )

    run_dq_merge_all = PythonOperator(
        task_id="run_dq_merge_all",
        python_callable=tldq.run_dq_file,
        op_kwargs={
            'wk_name': DAG_ID,
            'run_id': 'run_dq_merge_all',
            'tb_name': "$curated_db.FACT_AMAZON_AD_KEYWORDS",
            'query_file': f"{TRANSFORM_BASE}/dq_merge_pk_check.yaml"
        },
        on_failure_callback=alerts.send_failure_alert,
    )

    # --- Audit the data checks ---

    import tasklib.audit as tla

    run_audit = PythonOperator(
        task_id="run_audit",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.FACT_AMAZON_AD_KEYWORDS",
                   "wf_params": "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}",
                   "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                   "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                   },
        on_failure_callback=alerts.send_failure_alert
    )

    # --- Update workflow status ---

    update_workflow_params = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )


begin = DummyOperator(task_id="begin")
end = DummyOperator(task_id="end")


wait_on_amazon_ads_base_data_products_biddable = ExternalTaskSensor(task_id="wait_on_amazon_ads_base_data_products_biddable",
                               external_dag_id="amazon_ads_base_data",
                               external_task_id="end_products_biddable",
                               timeout=60 * 60 * 4,
                               allowed_states=["success"],
                               poke_interval=60 * 3,
                               mode="reschedule",
                               dag=dag
                               )
wait_on_amazon_ads_base_data_products_adgroup = ExternalTaskSensor(task_id="wait_on_amazon_ads_base_data_products_adgroup",
                               external_dag_id="amazon_ads_base_data",
                               external_task_id="end_products_adgroup",
                               allowed_states=["success"],
                               poke_interval=60 * 3,
                               mode="reschedule",
                               timeout=60 * 60 * 4,
                               dag=dag
                               )



start_brands = DummyOperator(task_id="start_brands")
end_brands = DummyOperator(task_id="end_brands")
start_brands >> [list_s3_modified_files_brands,list_s3_modified_files_brands_goessor] >> transfer_s3_to_snowflake_brands >> transfer_s3_to_snowflake_brands_goessor >> run_dq_transfer_s3_to_snowflake_brands >> dedup_brands >> run_dq_dedup_brands >> merge_brands >> end_brands

start_products = DummyOperator(task_id="start_products")
end_products = DummyOperator(task_id="end_products")
start_products >> [list_s3_modified_files_products, list_s3_modified_files_products_goessor] >> transfer_s3_to_snowflake_products >> transfer_s3_to_snowflake_products_goessor >> run_dq_transfer_s3_to_snowflake_products >> dedup_products >> run_dq_dedup_products >> merge_products >> end_products

start_brands_video = DummyOperator(task_id="start_brands_video")
end_brands_video = DummyOperator(task_id="end_brands_video")

start_brands_video >> [list_s3_modified_files_brands_video, list_s3_modified_files_brands_video_goessor] >> transfer_s3_to_snowflake_brands_video >> transfer_s3_to_snowflake_brands_video_goessor >> run_dq_transfer_s3_to_snowflake_brands_video >> dedup_brands_video >> run_dq_dedup_brands_video >> merge_brands_video >> end_brands_video

begin >> [wait_on_amazon_ads_base_data_products_biddable,wait_on_amazon_ads_base_data_products_adgroup] >> get_workflow_params >> [start_brands,start_products,start_brands_video]
[end_brands,end_products,end_brands_video] >> dedupe_join_all >> run_dq_dedupe_join_all >> merge_all >> run_dq_merge_all >> run_audit >> update_workflow_params >> end

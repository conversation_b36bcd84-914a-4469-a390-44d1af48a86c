import logging
from airflow import DAG
from datetime import datetime
from airflow.models import Variable
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule
from tasklib import alerts
from tasklib.loader import check_for_new_files

log = logging.getLogger(__name__)

DAG_ID = 'fact_amazon_reviews'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 6 * * *') # Default to run at 6am UTC
WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='get_workflow_params') }}"

with DAG(
    dag_id=DAG_ID,
    start_date=datetime(2022, 12, 12),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name':DAG_ID.upper(),
            'author':'harshad'
           },
    tags=['Harshad'],
) as dag:

    import tasklib.workflow as tlw

    get_workflow_params = PythonOperator(
        task_id="get_workflow_params",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )

    import tasklib.s3 as tls3

    list_s3_modified_files = PythonOperator(
        task_id="list_s3_modified_files",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"fact_amazon_reviews/s3_list_folders.yaml", "wf_params":WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    check_new_files_found = BranchPythonOperator(
        task_id='check_new_files_found',
        python_callable=check_for_new_files,
        op_kwargs={"args_file": f"fact_amazon_reviews/s3_to_snowflake.yaml",
                    "skip_task_id": "skip_insert_fact_amazon_reviews",
                    "next_task_id": "insert_fact_amazon_reviews.transfer_s3_to_snowflake"
        },
    )

    skip_insert_fact_amazon_reviews = DummyOperator(task_id="skip_insert_fact_amazon_reviews")

    with TaskGroup(group_id="insert_fact_amazon_reviews") as insert_fact_amazon_reviews:

        from tasklib.loader import S3ToSnowflake
        load_obj = S3ToSnowflake()
        
        transfer_s3_to_snowflake = PythonOperator(
            task_id="transfer_s3_to_snowflake",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file":"fact_amazon_reviews/s3_to_sf_raw_amazon_reviews.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        import tasklib.dq as tldq

        run_dq_tests_raw_amazon_reviews = PythonOperator(
            task_id="run_dq_tests_raw_amazon_reviews",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_tests_raw_amazon_reviews',
                'tb_name': "$raw_db.raw_amazon_reviews",
                'query_file': f"fact_amazon_reviews/dq_raw_amazon_reviews.yaml"
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        import tasklib.sql as tlsql

        log_amazon_reviews = PythonOperator(
            task_id="log_amazon_reviews",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"fact_amazon_reviews/log_amazon_reviews.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_amazon_reviews = PythonOperator(
            task_id="dedupe_amazon_reviews",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"fact_amazon_reviews/dedupe_amazon_reviews.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        stage_amazon_reviews = PythonOperator(
            task_id="stage_amazon_reviews",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"fact_amazon_reviews/stage_amazon_reviews.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_tests_stage_amazon_reviews = PythonOperator(
            task_id="run_dq_tests_stage_amazon_reviews",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_tests_stage_amazon_reviews',
                'tb_name': "$stage_db.stage_amazon_reviews",
                'query_file': f"fact_amazon_reviews/dq_stage_amazon_reviews.yaml"
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_staging_amazon_reviews = PythonOperator(
            task_id="merge_staging_amazon_reviews",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"fact_amazon_reviews/merge_staging_amazon_reviews.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_tests_merge_staging_amazon_reviews = PythonOperator(
            task_id="run_dq_tests_merge_staging_amazon_reviews",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_tests_merge_staging_amazon_reviews',
                'tb_name': "$stage_db.merge_staging_amazon_reviews",
                'query_file': f"fact_amazon_reviews/dq_merge_staging_amazon_reviews.yaml"
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_fact_amazon_reviews = PythonOperator(
            task_id="merge_fact_amazon_reviews",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection":"Snowflake",
                "sql_file":"fact_amazon_reviews/fact_amazon_reviews.sql",
                "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_tests_fact_amazon_reviews = PythonOperator(
            task_id="run_dq_tests_fact_amazon_reviews",
            python_callable=tldq.run_dq_file,
            op_kwargs={
                'wk_name': DAG_ID,
                'run_id': 'run_dq_tests_fact_amazon_reviews',
                'tb_name': "$curated_db.fact_amazon_reviews",
                'query_file': f"fact_amazon_reviews/dq_fact_amazon_reviews.yaml"
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        import tasklib.audit as tla

        run_audit = PythonOperator(
            task_id="run_audit",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_amazon_reviews",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            transfer_s3_to_snowflake >>
            run_dq_tests_raw_amazon_reviews >>
            log_amazon_reviews >>
            dedupe_amazon_reviews >> 
            stage_amazon_reviews >>
            run_dq_tests_stage_amazon_reviews >>
            merge_staging_amazon_reviews >>
            run_dq_tests_merge_staging_amazon_reviews >>
            merge_fact_amazon_reviews >>
            run_dq_tests_fact_amazon_reviews >>
            run_audit
        )

    update_workflow_params = PythonOperator(
        task_id="update_workflow_params",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    (
        begin >>
        get_workflow_params >>
        list_s3_modified_files >>
        check_new_files_found >>
        [transfer_s3_to_snowflake, skip_insert_fact_amazon_reviews]
    )

    (
        [run_audit, skip_insert_fact_amazon_reviews] >>
        update_workflow_params >>
        end
    )

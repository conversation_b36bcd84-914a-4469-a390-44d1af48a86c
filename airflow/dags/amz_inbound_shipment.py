"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'amz_inbound_shipment'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@hourly')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2025, 1, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'AMZ_INBOUND_SHIPMENT',
            'author': 'Thinh'},
    tags=['Thinh', 'Raptor']
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
        
    )

    begin = DummyOperator(task_id="begin", depends_on_past=False, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load amz_inbound_shipment ---
    
    with TaskGroup(group_id='load_amz_inbound_shipment') as tg_amz_inbound_shipment:
        list_s3_files_amz_inbound_shipment_task = PythonOperator(
            task_id="list_s3_files_amz_inbound_shipment",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ib_shipment/list_s3_amz_inbound_shipment.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            
        )
        list_s3_files_hd_amz_inbound_shipment_task = PythonOperator(
            task_id="list_s3_files_hd_amz_inbound_shipment",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ib_shipment/list_s3_hd_amz_inbound_shipment.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_amz_inbound_shipment_task = DummyOperator(task_id="begin_insert_amz_inbound_shipment")
        begin_insert_hd_amz_inbound_shipment_task = DummyOperator(task_id="begin_hd_insert_amz_inbound_shipment")
        skip_insert_amz_inbound_shipment_task = DummyOperator(task_id="skip_insert_amz_inbound_shipment")
        skip_insert_hd_amz_inbound_shipment_task = DummyOperator(task_id="skip_insert_hd_amz_inbound_shipment")
        end_insert_amz_inbound_shipment_task = DummyOperator(task_id="end_insert_amz_inbound_shipment")
        end_amz_inbound_shipment_task = DummyOperator(
            task_id="end_amz_inbound_shipment",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_amz_inbound_shipment_task = BranchPythonOperator(
            task_id='check_new_files_found_amz_inbound_shipment',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_snowflake_amz_inbound_shipment.yaml",
                       "skip_task_id": "load_amz_inbound_shipment.skip_insert_amz_inbound_shipment",
                       "next_task_id": "load_amz_inbound_shipment.begin_insert_amz_inbound_shipment"
            },
        )

        s3_to_snowflake_amz_inbound_shipment_task = PythonOperator(
            task_id="s3_to_snowflake_amz_inbound_shipment",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_sf_raw_amz_inbound_shipment.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        check_new_files_found_hd_amz_inbound_shipment_task = BranchPythonOperator(
            task_id='check_new_files_found_hd_amz_inbound_shipment',
            python_callable=check_for_new_files,
            op_kwargs={
                       "args_file": "amazon_ib_shipment/s3_to_snowflake_hd_amz_inbound_shipment.yaml",
                       "skip_task_id": "load_amz_inbound_shipment.skip_insert_hd_amz_inbound_shipment",
                       "next_task_id": "load_amz_inbound_shipment.begin_hd_insert_amz_inbound_shipment"
                       },
        )

        s3_to_snowflake_hd_amz_inbound_shipment_task = PythonOperator(
            task_id="s3_to_snowflake_hd_amz_inbound_shipment",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_sf_hd_raw_amz_inbound_shipment.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        

        insert_log_amz_inbound_shipment_task = PythonOperator(
            task_id="insert_log_amz_inbound_shipment",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "amazon_ib_shipment/insert_log_amz_inbound_shipment.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_amz_inbound_shipment_task = PythonOperator(
            task_id="dedupe_amz_inbound_shipment",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ib_shipment/dedupe_amz_inbound_shipment.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_inbound_shipment',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_inbound_shipment',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_inbound_shipment',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amz_inbound_shipment',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_amz_inbound_shipment_task = PythonOperator(
            task_id="merge_stage_amz_inbound_shipment",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ib_shipment/merge_stage_amz_inbound_shipment.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_inbound_shipment',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amz_inbound_shipment',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_inbound_shipment',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amz_inbound_shipment',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_amz_inbound_shipment_task = PythonOperator(
            task_id="run_audit_amz_inbound_shipment",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amz_inbound_shipment",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            [list_s3_files_amz_inbound_shipment_task] >>
            check_new_files_found_amz_inbound_shipment_task >>
            [begin_insert_amz_inbound_shipment_task, skip_insert_amz_inbound_shipment_task]
        )
        (
            [list_s3_files_hd_amz_inbound_shipment_task] >>
            check_new_files_found_hd_amz_inbound_shipment_task >>
            [begin_insert_hd_amz_inbound_shipment_task, skip_insert_hd_amz_inbound_shipment_task]
        )

        (
            begin_insert_amz_inbound_shipment_task >>
            s3_to_snowflake_amz_inbound_shipment_task >>
            begin_insert_hd_amz_inbound_shipment_task >>
            s3_to_snowflake_hd_amz_inbound_shipment_task >>
            insert_log_amz_inbound_shipment_task >>
            dedupe_amz_inbound_shipment_task >>
            
            (run_dq_is_null_dedupe_pk_hard_task,
             run_dq_is_unique_dedupe_pk_hard_task) >>
            
            merge_stage_amz_inbound_shipment_task >>
            
            (run_dq_is_null_merge_pk_hard_task,
             run_dq_is_unique_merge_pk_hard_task) >>
            
            run_audit_amz_inbound_shipment_task >>
            end_insert_amz_inbound_shipment_task >>
            end_amz_inbound_shipment_task
        )

        skip_insert_amz_inbound_shipment_task >> end_amz_inbound_shipment_task
        skip_insert_hd_amz_inbound_shipment_task >> end_amz_inbound_shipment_task

    with TaskGroup(group_id='load_amz_shipment_plan') as tg_amz_shipment_plan:
        list_s3_files_amz_shipment_plan_task = PythonOperator(
            task_id="list_s3_files_amz_shipment_plan",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ib_shipment/list_s3_raw_amz_inbound_plan.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )
        list_s3_files_hd_amz_shipment_plan_task = PythonOperator(
            task_id="list_s3_files_hd_amz_shipment_plan",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ib_shipment/list_s3_hd_raw_amz_inbound_plan.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_amz_shipment_plan_task = DummyOperator(task_id="begin_insert_amz_shipment_plan")
        begin_insert_hd_amz_shipment_plan_task = DummyOperator(task_id="begin_insert_hd_amz_shipment_plan")
        skip_insert_amz_shipment_plan_task = DummyOperator(task_id="skip_insert_amz_shipment_plan")
        skip_insert_hd_amz_shipment_plan_task = DummyOperator(task_id="skip_insert_hd_amz_shipment_plan")
        end_insert_amz_shipment_plan_task = DummyOperator(task_id="end_insert_amz_shipment_plan")
        end_amz_inbound_shipment_plan_task = DummyOperator(
            task_id="end_amz_shipment_plan",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_amz_shipment_plan_task = BranchPythonOperator(
            task_id='check_new_files_found_amz_shipment_plan',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_snowflake_raw_amz_inbound_plan.yaml",
                       "skip_task_id": "load_amz_shipment_plan.skip_insert_amz_shipment_plan",
                       "next_task_id": "load_amz_shipment_plan.begin_insert_amz_shipment_plan"
                       },
        )
        check_new_files_found_hd_amz_shipment_plan_task = BranchPythonOperator(
            task_id='check_new_files_found_hd_amz_shipment_plan',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_snowflake_raw_hd_amz_inbound_plan.yaml",
                       "skip_task_id": "load_amz_shipment_plan.skip_insert_hd_amz_shipment_plan",
                       "next_task_id": "load_amz_shipment_plan.begin_insert_hd_amz_shipment_plan"
                       },
        )
        s3_to_snowflake_hd_amz_shipment_plan_task = PythonOperator(
            task_id="s3_to_snowflake_hd_amz_shipment_plan",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_sf_hd_raw_amz_inbound_plan.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        s3_to_snowflake_amz_shipment_plan_task = PythonOperator(
            task_id="s3_to_snowflake_amz_shipment_plan",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_sf_raw_amz_inbound_plan.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_amz_shipment_plan_task = PythonOperator(
            task_id="insert_log_amz_shipment_plan",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ib_shipment/insert_log_raw_amz_inbound_plan.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_amz_shipment_plan_task = PythonOperator(
            task_id="dedupe_amz_shipment_plan",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ib_shipment/dedupe_amz_inbound_plan.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_plan_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_plan_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_inbound_plan',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_inbound_plan',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_plan_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_plan_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_inbound_plan',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amz_inbound_plan',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_amz_shipment_plan_task = PythonOperator(
            task_id="merge_stage_amz_shipment_plan",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ib_shipment/merge_stage_amz_inbound_plan.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_plan_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_plan_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_inbound_plan',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amz_inbound_plan',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_plan_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_plan_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_inbound_plan',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amz_inbound_plan',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_amz_shipment_plan_task = PythonOperator(
            task_id="run_audit_amz_shipment_plan",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amz_inbound_plan",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                list_s3_files_amz_shipment_plan_task >>
                check_new_files_found_amz_shipment_plan_task >>
                [begin_insert_amz_shipment_plan_task, skip_insert_amz_shipment_plan_task]
        )
        (
                list_s3_files_hd_amz_shipment_plan_task >>
                check_new_files_found_hd_amz_shipment_plan_task >>
                [begin_insert_hd_amz_shipment_plan_task, skip_insert_hd_amz_shipment_plan_task]
        )

        (
                begin_insert_amz_shipment_plan_task >>
                s3_to_snowflake_amz_shipment_plan_task >>
                begin_insert_hd_amz_shipment_plan_task >>
                s3_to_snowflake_hd_amz_shipment_plan_task >>
                insert_log_amz_shipment_plan_task >>
                dedupe_amz_shipment_plan_task >>
                
                (run_dq_is_null_dedupe_plan_pk_hard_task,
                 run_dq_is_unique_dedupe_plan_pk_hard_task) >>

                merge_stage_amz_shipment_plan_task >>
                
                (run_dq_is_null_merge_plan_pk_hard_task,
                 run_dq_is_unique_merge_plan_pk_hard_task) >>

                run_audit_amz_shipment_plan_task >>
                end_insert_amz_shipment_plan_task >>
                end_amz_inbound_shipment_plan_task
        )

        skip_insert_amz_shipment_plan_task >> end_amz_inbound_shipment_plan_task
        skip_insert_hd_amz_shipment_plan_task >> end_amz_inbound_shipment_plan_task


    with TaskGroup(group_id='load_amz_inbound_shipment_detail') as tg_amz_inbound_shipment_detail:
        list_s3_files_amz_inbound_shipment_detail_task = PythonOperator(
            task_id="list_s3_files_amz_inbound_shipment_detail",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ib_shipment/list_s3_raw_amz_inbound_shipment_detail.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )
        list_s3_files_hd_amz_inbound_shipment_detail_task = PythonOperator(
            task_id="list_s3_files_hd_amz_inbound_shipment_detail",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ib_shipment/list_s3_hd_raw_amz_inbound_shipment_detail.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_amz_inbound_shipment_detail_task = DummyOperator(task_id="begin_insert_amz_inbound_shipment_detail")
        begin_insert_hd_amz_inbound_shipment_detail_task = DummyOperator(task_id="begin_insert_hd_amz_inbound_shipment_detail")
        skip_insert_amz_inbound_shipment_detail_task = DummyOperator(task_id="skip_insert_amz_inbound_shipment_detail")
        skip_insert_hd_amz_inbound_shipment_detail_task = DummyOperator(task_id="skip_insert_hd_amz_inbound_shipment_detail")
        end_insert_amz_inbound_shipment_detail_task = DummyOperator(task_id="end_insert_amz_inbound_shipment_detail")
        end_amz_inbound_shipment_detail_task = DummyOperator(
            task_id="end_amz_inbound_shipment_detail",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_amz_inbound_shipment_detail_task = BranchPythonOperator(
            task_id='check_new_files_found_amz_inbound_shipment_detail',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_snowflake_raw_amz_inbound_shipment_detail.yaml",
                       "skip_task_id": "load_amz_inbound_shipment_detail.skip_insert_amz_inbound_shipment_detail",
                       "next_task_id": "load_amz_inbound_shipment_detail.begin_insert_amz_inbound_shipment_detail"
                      },
        )
        check_new_files_found_hd_amz_inbound_shipment_detail_task = BranchPythonOperator(
            task_id='check_new_files_found_hd_amz_inbound_shipment_detail',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_snowflake_raw_hd__amz_inbound_shipment_detail.yaml",
                       "skip_task_id": "load_amz_inbound_shipment_detail.skip_insert_amz_inbound_shipment_detail",
                       "next_task_id": "load_amz_inbound_shipment_detail.begin_insert_amz_inbound_shipment_detail"
                       },
        )

        s3_to_snowflake_hd_amz_inbound_shipment_detail_task = PythonOperator(
            task_id="s3_to_snowflake_hd_amz_inbound_shipment_detail",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_sf_raw_hd_amz_inbound_shipment_detail.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_amz_inbound_shipment_detail_task = PythonOperator(
            task_id="s3_to_snowflake_amz_inbound_shipment_detail",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_sf_raw_amz_inbound_shipment_detail.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_amz_inbound_shipment_detail_task = PythonOperator(
            task_id="insert_log_amz_inbound_shipment_detail",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ib_shipment/insert_log_raw_amz_inbound_shipment_detail.sql",
                       "wf_params": WF_PARAMS_EXPR
                      },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_amz_inbound_shipment_detail_task = PythonOperator(
            task_id="dedupe_amz_inbound_shipment_detail",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ib_shipment/dedupe_amz_inbound_shipment_detail.sql",
                       "wf_params": WF_PARAMS_EXPR
                      },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_null_dedupe_detail_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_detail_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_inbound_shipment_detail',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_amz_inbound_shipment_detail',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_detail_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_detail_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_amz_inbound_shipment_detail',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_amz_inbound_shipment_detail',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_amz_inbound_shipment_detail_task = PythonOperator(
            task_id="merge_stage_amz_inbound_shipment_detail",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ib_shipment/merge_stage_amz_inbound_shipment_detail.sql",
                       "wf_params": WF_PARAMS_EXPR
                      },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_null_merge_detail_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_detail_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_inbound_shipment_detail',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_amz_inbound_shipment_detail',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_detail_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_detail_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_amz_inbound_shipment_detail',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_amz_inbound_shipment_detail',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_amz_inbound_shipment_detail_task = PythonOperator(
            task_id="run_audit_amz_inbound_shipment_detail",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_amz_inbound_shipment_detail",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
            [list_s3_files_amz_inbound_shipment_detail_task] >>
            check_new_files_found_amz_inbound_shipment_detail_task >>
            [begin_insert_amz_inbound_shipment_detail_task, skip_insert_amz_inbound_shipment_detail_task]
        )

        (
            [list_s3_files_hd_amz_inbound_shipment_detail_task] >>
            check_new_files_found_hd_amz_inbound_shipment_detail_task >>
            [begin_insert_hd_amz_inbound_shipment_detail_task, skip_insert_hd_amz_inbound_shipment_detail_task]
        )

        (
            begin_insert_amz_inbound_shipment_detail_task >>
            s3_to_snowflake_amz_inbound_shipment_detail_task >>
            begin_insert_hd_amz_inbound_shipment_detail_task >>
            s3_to_snowflake_hd_amz_inbound_shipment_detail_task >>
            insert_log_amz_inbound_shipment_detail_task >>
            dedupe_amz_inbound_shipment_detail_task >>
            
            (run_dq_is_null_dedupe_detail_pk_hard_task,
             run_dq_is_unique_dedupe_detail_pk_hard_task) >>
            
            merge_stage_amz_inbound_shipment_detail_task >>
            
            (run_dq_is_null_merge_detail_pk_hard_task,
             run_dq_is_unique_merge_detail_pk_hard_task) >>
            
            run_audit_amz_inbound_shipment_detail_task >>
            end_insert_amz_inbound_shipment_detail_task >>
            end_amz_inbound_shipment_detail_task
        )

        skip_insert_amz_inbound_shipment_detail_task >> end_amz_inbound_shipment_detail_task
        skip_insert_hd_amz_inbound_shipment_detail_task >> end_amz_inbound_shipment_detail_task

    # --- Create fact table that joins plans with shipments ---
    with TaskGroup(group_id='create_fact_tables') as tg_create_fact_tables:
        begin_create_fact_tables_task = DummyOperator(task_id="begin_create_fact_tables")
        end_create_fact_tables_task = DummyOperator(task_id="end_create_fact_tables")
        
        create_fact_amz_inbound_shipment_task = PythonOperator(
            task_id="create_fact_amz_inbound_shipment",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                      "sql_file": "amazon_ib_shipment/create_fact_amz_inbound_shipment.sql",
                      "wf_params": WF_PARAMS_EXPR
                     },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_null_fact_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_fact_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.fact_amz_inbound_shipment',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$curated_db.fact_amz_inbound_shipment',
                    field_list=['fact_pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_fact_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_fact_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$curated_db.fact_amz_inbound_shipment',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$curated_db.fact_amz_inbound_shipment',
                    field_list=['fact_pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_audit_fact_amz_inbound_shipment_task = PythonOperator(
            task_id="run_audit_fact_amz_inbound_shipment",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$curated_db.fact_amz_inbound_shipment",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'created_at',
                "ts_updated_field": 'updated_at'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        (
            begin_create_fact_tables_task >>
            create_fact_amz_inbound_shipment_task >>
            (run_dq_is_null_fact_pk_hard_task,
             run_dq_is_unique_fact_pk_hard_task) >>
            run_audit_fact_amz_inbound_shipment_task >>
            end_create_fact_tables_task
        )
    with TaskGroup(group_id='load_list_transportation_options') as tg_list_transportation_options:
        list_s3_files_list_transportation_options_task = PythonOperator(
            task_id="list_s3_files_list_transportation_options",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ib_shipment/list_s3_list_transportation_options.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )
        list_s3_files_hd_list_transportation_options_task = PythonOperator(
            task_id="list_s3_files_hd_list_transportation_options",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "amazon_ib_shipment/list_s3_hd_list_transportation_options.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_list_transportation_options_task = DummyOperator(task_id="begin_insert_list_transportation_options")
        begin_insert_hd_list_transportation_options_task = DummyOperator(task_id="begin_insert_hd_list_transportation_options")
        skip_insert_list_transportation_options_task = DummyOperator(task_id="skip_insert_list_transportation_options")
        skip_insert_hd_list_transportation_options_task = DummyOperator(task_id="skip_insert_hd_list_transportation_options")
        end_insert_list_transportation_options_task = DummyOperator(task_id="end_insert_list_transportation_options")
        end_list_transportation_options_task = DummyOperator(
            task_id="end_list_transportation_options",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_list_transportation_options_task = BranchPythonOperator(
            task_id='check_new_files_found_list_transportation_options',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_snowflake_list_transportation_options.yaml",
                       "skip_task_id": "load_list_transportation_options.skip_insert_list_transportation_options",
                       "next_task_id": "load_list_transportation_options.begin_insert_list_transportation_options"
                       },
        )
        check_new_files_found_hd_list_transportation_options_task = BranchPythonOperator(
            task_id='check_new_files_found_hd_list_transportation_options',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_snowflake_hd_list_transportation_options.yaml",
                       "skip_task_id": "load_list_transportation_options.skip_insert_list_transportation_options",
                       "next_task_id": "load_list_transportation_options.begin_insert_list_transportation_options"
                       },
        )

        s3_to_snowflake_hd_list_transportation_options_task = PythonOperator(
            task_id="s3_to_snowflake_hd_list_transportation_options",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_sf_raw_hd_list_transportation_options.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_list_transportation_options_task = PythonOperator(
            task_id="s3_to_snowflake_list_transportation_options",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "amazon_ib_shipment/s3_to_sf_raw_list_transportation_options.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_list_transportation_options_task = PythonOperator(
            task_id="insert_log_list_transportation_options",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ib_shipment/insert_log_list_transportation_options.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_list_transportation_options_task = PythonOperator(
            task_id="dedupe_list_transportation_options",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "amazon_ib_shipment/dedupe_list_transportation_options.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_null_dedupe_transportation_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_transportation_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_list_transportation_options',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_list_transportation_options',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_transportation_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_transportation_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_list_transportation_options',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_list_transportation_options',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_list_transportation_options_task = PythonOperator(
            task_id="merge_stage_list_transportation_options",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                      "sql_file": "amazon_ib_shipment/merge_stage_list_transportation_options.sql",
                      "wf_params": WF_PARAMS_EXPR
                     },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_null_merge_transportation_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_transportation_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_list_transportation_options',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_list_transportation_options',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_transportation_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_transportation_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_list_transportation_options',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_list_transportation_options',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_list_transportation_options_task = PythonOperator(
            task_id="run_audit_list_transportation_options",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_list_transportation_options",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                [list_s3_files_list_transportation_options_task] >>
                check_new_files_found_list_transportation_options_task >>
                [begin_insert_list_transportation_options_task, skip_insert_list_transportation_options_task]
        )

        (
                [list_s3_files_hd_list_transportation_options_task] >>
                check_new_files_found_hd_list_transportation_options_task >>
                [begin_insert_hd_list_transportation_options_task, skip_insert_hd_list_transportation_options_task]
        )

        (
                begin_insert_list_transportation_options_task >>
                s3_to_snowflake_list_transportation_options_task >>
                begin_insert_hd_list_transportation_options_task >>
                s3_to_snowflake_hd_list_transportation_options_task>>
                insert_log_list_transportation_options_task >>
                dedupe_list_transportation_options_task >>
                
                (run_dq_is_null_dedupe_transportation_pk_hard_task,
                 run_dq_is_unique_dedupe_transportation_pk_hard_task) >>

                merge_stage_list_transportation_options_task >>
                
                (run_dq_is_null_merge_transportation_pk_hard_task,
                 run_dq_is_unique_merge_transportation_pk_hard_task) >>

                run_audit_list_transportation_options_task >>
                end_insert_list_transportation_options_task >>
                end_list_transportation_options_task
        )

        skip_insert_list_transportation_options_task >> end_list_transportation_options_task
        skip_insert_hd_list_transportation_options_task >> end_list_transportation_options_task

    # Set dependencies between task groups
    chain(
        get_workflow_parameters >> begin >> 
        [tg_amz_inbound_shipment, tg_amz_shipment_plan, tg_amz_inbound_shipment_detail, tg_list_transportation_options] >>
        tg_create_fact_tables >>
        update_workflow_parameters >> end
    )
    
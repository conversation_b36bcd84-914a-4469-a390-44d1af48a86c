
import logging
from datetime import datetime
import json

from airflow import DAG
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
import tasklib.sql as tlsql
from tasklib import alerts
from airflow.models.baseoperator import chain
import tasklib.workflow as tlw
import tasklib.dq as tldq
import tasklib.dq as tldq
from airflow.utils.task_group import TaskGroup

"""
This pipeline unifies Amazon Ads data with Finance and Retail data. This dataset is being used by the
ad bidding ML algorithm to create model which will be used to predict future bid values.
To backfill data use run dag with conf option and set the 'backfill_date' parameter to the desired date.
e.g. set the data to "backfill_date":"2021-01-01" to backfill from begining of 2021
Below is the exact config you can use:
{
    "workflow_name": "FACT_ADTECH_KEYWORD_FEATURES",
    "author": "Sauvik",
    "backfill_date":"2021-01-01"
}
"""

DAG_ID = 'fact_adtech_keyword_features'
TRANSFORM_BASE = 'ads/adtech/' + DAG_ID
RELEASE_DEF = '1'
BUILD_NUM = '01'

WF_PARAMS_EXPR = json.dumps({'start_date': "{{ execution_date if dag_run.conf.get('backfill_date') is none else dag_run.conf.get('backfill_date') }}"})

log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2022, 5, 8),
    schedule_interval='@daily',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'sauvik'
            },
    tags=['RETIRED']
) as dag:
    
    with TaskGroup(group_id='tg_load_stg_adtech_ad_group_asin') as tg_load_stg_adtech_ad_group_asin:
        load_stg_adtech_ad_group_asin = PythonOperator(
                task_id="load_stg_adtech_ad_group_asin",
                python_callable=tlsql.run_query_file,
                op_kwargs={
                    "connection": "Snowflake",
                    "sql_file": TRANSFORM_BASE + "/stg_adtech_ad_group_asin.sql",
                    "wf_params": WF_PARAMS_EXPR},
                on_failure_callback=alerts.send_failure_alert,
                provide_context=True
        )

        run_dq_stg_adtech_ad_group_asin = PythonOperator(
            task_id="run_dq_stg_adtech_ad_group_asin",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': "$stage_db.STG_ADTECH_AD_GROUP_ASIN",
                'test_name': 'key_fields_not_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name="$stage_db.STG_ADTECH_AD_GROUP_ASIN", 
                    field_list=['report_date','ad_group_id','asin'],
                    hard_alert=True
                )
                },
            on_failure_callback=alerts.send_failure_alert
        )

    load_stg_adtech_ad_group_asin >> run_dq_stg_adtech_ad_group_asin

    load_stg_adtech_asin_cpaa_agg = PythonOperator(
        task_id="load_stg_adtech_asin_cpaa_agg",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/stg_adtech_asin_cpaa_agg.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    load_stg_fact_adtech_keyword_features = PythonOperator(
        task_id="load_stg_fact_adtech_keyword_features",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/stg_fact_adtech_keyword_features.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    run_dq_stg_fact_adtech_keyword_features = PythonOperator(
        task_id="run_dq_stg_fact_adtech_keyword_features",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': DAG_ID,
            'tb_name': "$stage_db.STG_FACT_ADTECH_KEYWORD_FEATURES",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.STG_FACT_ADTECH_KEYWORD_FEATURES", 
                field_list=['advertisement_pk'],
                hard_alert=True
            )
            },
        on_failure_callback=alerts.send_failure_alert
    )

    run_merge = PythonOperator(
        task_id="run_merge",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/merge.sql",
            "wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True
    )

    # --- Audit the data checks ---

    import tasklib.audit as tla

    run_audit = PythonOperator(
        task_id="run_audit",
        python_callable=tla.run_audit,
        op_kwargs={"table_name": "$curated_db.FACT_ADTECH_KEYWORD_FEATURES",
                   "wf_params": "{}",
                   "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                   "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC'
                   },
        on_failure_callback=alerts.send_failure_alert
    )


begin = DummyOperator(task_id="begin")
end = DummyOperator(task_id="end")

chain(
    begin,
    [tg_load_stg_adtech_ad_group_asin, load_stg_adtech_asin_cpaa_agg],
    load_stg_fact_adtech_keyword_features,
    run_dq_stg_fact_adtech_keyword_features,
    run_merge,
    run_audit,
    end,
)

import json
import logging
from datetime import datetime

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.sql as tlsql
from tasklib import alerts

from airflow import DAG
from airflow.models import Variable

# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.sensors.sql import SqlSensor
from airflow.utils.task_group import TaskGroup

DAG_ID = "adtech_ads_metrics"
TRANSFORM_BASE = "ads/adtech/" + DAG_ID
RELEASE_DEF = "1"
BUILD_NUM = "01"


SUGGESTED_BID_LOGIC = Variable.get("adtech_suggested_bid_logic")

run_date = "{{ macros.ds_add(ds, -1) }}"

WF_PARAMS_EXPR = json.dumps(
    {"run_date": run_date, "suggested_bid_logic": SUGGESTED_BID_LOGIC}
)

log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description="Release:{0}-Build:{1}".format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2022, 5, 8),
    schedule_interval="0 17 * * *",
    catchup=False,
    max_active_runs=1,
    params={"workflow_name": DAG_ID, "author": "sauvik"},
    tags=["Sauvik"],
) as dag:

    stg_adtech_parentasin_tacos_ads_map = PythonOperator(
        task_id="stg_adtech_parentasin_tacos_ads_map",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/stg_adtech_parentasin_tacos_ads_map.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    stg_adtech_adgroup_parent_asin_map = PythonOperator(
        task_id="stg_adtech_adgroup_parent_asin_map",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/stg_adtech_adgroup_parent_asin_map.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    stg_adtech_campaign_parent_asin_map = PythonOperator(
        task_id="stg_adtech_campaign_parent_asin_map",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/stg_adtech_campaign_parent_asin_map.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    stg_adtech_adgroup_metadata = PythonOperator(
        task_id="stg_adtech_adgroup_metadata",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/stg_adtech_adgroup_metadata.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )
    stg_adtech_campaigns_metadata = PythonOperator(
        task_id="stg_adtech_campaigns_metadata",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/stg_adtech_campaigns_metadata.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    stg_adtech_experiment_parent_asins = PythonOperator(
        task_id="stg_adtech_experiment_parent_asins",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": TRANSFORM_BASE + "/stg_adtech_experiment_parent_asins.sql",
            "wf_params": WF_PARAMS_EXPR,
        },
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    with TaskGroup(
        group_id="tg_amazon_ads_adgroup_metrics"
    ) as tg_amazon_ads_adgroup_metrics:

        stg_adtech_target_keyword_metadata = PythonOperator(
            task_id="stg_adtech_target_keyword_metadata",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/stg_adtech_target_keyword_metadata.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )
        stg_adtech_amazon_ads_adgroup_metrics = PythonOperator(
            task_id="stg_adtech_amazon_ads_adgroup_metrics",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE
                + "/stg_adtech_amazon_ads_adgroup_metrics.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )

        run_dq_stg_adtech_amazon_ads_adgroup_metrics = PythonOperator(
            task_id="run_dq_stg_adtech_amazon_ads_adgroup_metrics",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "test_name": "ad_group_id_not_duplicate",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.stg_adtech_amazon_ads_adgroup_metrics",
                    field_list=["ad_group_id"],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        log_stg_adtech_amazon_ads_adgroup_metrics = PythonOperator(
            task_id="log_stg_adtech_amazon_ads_adgroup_metrics",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE
                + "/log_stg_adtech_amazon_ads_adgroup_metrics.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )

        amazon_ads_adgroup_metrics = PythonOperator(
            task_id="amazon_ads_adgroup_metrics",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/amazon_ads_adgroup_metrics.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )

        adgroup_metrics_audit = PythonOperator(
            task_id="adgroup_metrics_audit",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$adtech_db.amazon_ads_adgroup_metrics",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": "RECORD_CREATED_TIMESTAMP_UTC",
                "ts_updated_field": "RECORD_UPDATED_TIMESTAMP_UTC",
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
    (
        stg_adtech_target_keyword_metadata
        >> stg_adtech_amazon_ads_adgroup_metrics
        >> run_dq_stg_adtech_amazon_ads_adgroup_metrics
        >> log_stg_adtech_amazon_ads_adgroup_metrics
        >> amazon_ads_adgroup_metrics
        >> adgroup_metrics_audit
    )

    with TaskGroup(
        group_id="tg_amazon_ads_campaign_metrics"
    ) as tg_amazon_ads_campaign_metrics:
        stg_adtech_amazon_ads_campaign_metrics = PythonOperator(
            task_id="stg_adtech_amazon_ads_campaign_metrics",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE
                + "/stg_adtech_amazon_ads_campaign_metrics.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )

        run_dq_stg_adtech_amazon_ads_campaign_metrics = PythonOperator(
            task_id="run_dq_stg_adtech_amazon_ads_campaign_metrics",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "test_name": "campaign_id_not_duplicate",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.stg_adtech_amazon_ads_campaign_metrics",
                    field_list=["campaign_id"],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        log_stg_adtech_amazon_ads_campaign_metrics = PythonOperator(
            task_id="log_stg_adtech_amazon_ads_campaign_metrics",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE
                + "/log_stg_adtech_amazon_ads_campaign_metrics.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )

        amazon_ads_campaign_metrics = PythonOperator(
            task_id="amazon_ads_campaign_metrics",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/amazon_ads_campaign_metrics.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )
        campaign_metrics_audit = PythonOperator(
            task_id="campaign_metrics_audit",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$adtech_db.amazon_ads_campaign_metrics",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": "RECORD_CREATED_TIMESTAMP_UTC",
                "ts_updated_field": "RECORD_UPDATED_TIMESTAMP_UTC",
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
    (
        stg_adtech_amazon_ads_campaign_metrics
        >> run_dq_stg_adtech_amazon_ads_campaign_metrics
        >> log_stg_adtech_amazon_ads_campaign_metrics
        >> amazon_ads_campaign_metrics
        >> campaign_metrics_audit
    )

    with TaskGroup(
        group_id="tg_amazon_ads_target_metrics"
    ) as tg_amazon_ads_target_metrics:

        stg_adtech_amazon_ads_targets_metrics = PythonOperator(
            task_id="stg_adtech_amazon_ads_targets_metrics",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE
                + "/stg_adtech_amazon_ads_targets_metrics.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )

        run_dq_stg_adtech_amazon_ads_targets_metrics = PythonOperator(
            task_id="run_dq_stg_adtech_amazon_ads_targets_metrics",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "test_name": "target_id_not_duplicate",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.stg_adtech_amazon_ads_targets_metrics",
                    field_list=["target_id", "country_code"],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        log_stg_adtech_amazon_ads_targets_metrics = PythonOperator(
            task_id="log_stg_adtech_amazon_ads_targets_metrics",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE
                + "/log_stg_adtech_amazon_ads_targets_metrics.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )

        amazon_ads_targets_metrics = PythonOperator(
            task_id="amazon_ads_targets_metrics",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/amazon_ads_targets_metrics.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )

        targets_metrics_audit = PythonOperator(
            task_id="targets_metrics_audit",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$adtech_db.amazon_ads_targets_metrics",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": "RECORD_CREATED_TIMESTAMP_UTC",
                "ts_updated_field": "RECORD_UPDATED_TIMESTAMP_UTC",
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
    (
        stg_adtech_amazon_ads_targets_metrics
        >> run_dq_stg_adtech_amazon_ads_targets_metrics
        >> log_stg_adtech_amazon_ads_targets_metrics
        >> amazon_ads_targets_metrics
        >> targets_metrics_audit
    )

    with TaskGroup(
        group_id="tg_amazon_ads_target_keyword_fanout_details"
    ) as tg_amazon_ads_target_keyword_fanout_details:
        stg_adtech_amazon_ads_target_keyword_fanout_details = PythonOperator(
            task_id="stg_adtech_amazon_ads_target_keyword_fanout_details",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE
                + "/stg_adtech_amazon_ads_target_keyword_fanout_details.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )

        run_dq_stg_adtech_amazon_ads_target_keyword_fanout_details = PythonOperator(
            task_id="run_dq_stg_adtech_amazon_ads_target_keyword_fanout_details",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                "wk_name": DAG_ID,
                "test_name": "key_not_duplicate",
                "sql_query": tldq.gen_check_unique_key(
                    tb_name="$stage_db.stg_adtech_amazon_ads_target_keyword_fanout_details",
                    field_list=["target_id"],
                    hard_alert=True,
                ),
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        log_stg_adtech_amazon_ads_target_keyword_fanout_details = PythonOperator(
            task_id="log_stg_adtech_amazon_ads_target_keyword_fanout_details",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE
                + "/log_stg_adtech_amazon_ads_target_keyword_fanout_details.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )

        amazon_ads_target_keyword_fanout_details = PythonOperator(
            task_id="amazon_ads_target_keyword_fanout_details",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE
                + "/amazon_ads_target_keyword_fanout_details.sql",
                "wf_params": WF_PARAMS_EXPR,
            },
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True,
        )
        fanout_details_audit = PythonOperator(
            task_id="fanout_details_audit",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$adtech_db.amazon_ads_target_keyword_fanout_details",
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": "RECORD_CREATED_TIMESTAMP_UTC",
                "ts_updated_field": "RECORD_UPDATED_TIMESTAMP_UTC",
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

    (
        stg_adtech_amazon_ads_target_keyword_fanout_details
        >> run_dq_stg_adtech_amazon_ads_target_keyword_fanout_details
        >> log_stg_adtech_amazon_ads_target_keyword_fanout_details
        >> amazon_ads_target_keyword_fanout_details
        >> fanout_details_audit
    )

    # with TaskGroup(group_id="tg_amazon_rpc_calculations") as tg_amazon_rpc_calculations:
    #     fetch_ad_group_universe_non_skc = PythonOperator(
    #         task_id="fetch_ad_group_universe_non_skc",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={
    #             "connection": "Snowflake",
    #             "sql_file": "ads/adtech/rpc_calculations/adgroup_universe.sql",
    #             "wf_params": WF_PARAMS_EXPR,
    #         },
    #         on_failure_callback=alerts.send_failure_alert,
    #         provide_context=True,
    #     )

    #     fetch_ad_group_aggregates_non_skc = PythonOperator(
    #         task_id="fetch_ad_group_aggregates_non_skc",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={
    #             "connection": "Snowflake",
    #             "sql_file": "ads/adtech/rpc_calculations/adgroup_aggregates.sql",
    #             "wf_params": WF_PARAMS_EXPR,
    #         },
    #         on_failure_callback=alerts.send_failure_alert,
    #         provide_context=True,
    #     )

    #     fetch_campaign_aggregates_non_skc = PythonOperator(
    #         task_id="fetch_campaign_aggregates_non_skc",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={
    #             "connection": "Snowflake",
    #             "sql_file": "ads/adtech/rpc_calculations/campaign_aggregates.sql",
    #             "wf_params": WF_PARAMS_EXPR,
    #         },
    #         on_failure_callback=alerts.send_failure_alert,
    #         provide_context=True,
    #     )

    #     fetch_ppc_readiness_non_skc = PythonOperator(
    #         task_id="fetch_ppc_readiness_non_skc",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={
    #             "connection": "Snowflake",
    #             "sql_file": "ads/adtech/rpc_calculations/ppc_readiness.sql",
    #             "wf_params": WF_PARAMS_EXPR,
    #         },
    #         on_failure_callback=alerts.send_failure_alert,
    #         provide_context=True,
    #     )

    #     fetch_acos_ceiling_calculation_non_skc = PythonOperator(
    #         task_id="fetch_acos_ceiling_calculation_non_skc",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={
    #             "connection": "Snowflake",
    #             "sql_file": "ads/adtech/rpc_calculations/acos_calculation.sql",
    #             "wf_params": WF_PARAMS_EXPR,
    #         },
    #         on_failure_callback=alerts.send_failure_alert,
    #         provide_context=True,
    #     )

    #     calculate_adgroup_rpc = PythonOperator(
    #         task_id="calculate_adgroup_rpc",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={
    #             "connection": "Snowflake",
    #             "sql_file": "ads/adtech/rpc_calculations/adgroup_rpc_calculation.sql",
    #             "wf_params": WF_PARAMS_EXPR,
    #         },
    #         on_failure_callback=alerts.send_failure_alert,
    #         provide_context=True,
    #     )

    #     campaign_rpc_calculation = PythonOperator(
    #         task_id="campaign_rpc_calculation",
    #         python_callable=tlsql.run_query_file,
    #         op_kwargs={
    #             "connection": "Snowflake",
    #             "sql_file": "ads/adtech/rpc_calculations/campaign_rpc_calculation.sql",
    #             "wf_params": WF_PARAMS_EXPR,
    #         },
    #         on_failure_callback=alerts.send_failure_alert,
    #         provide_context=True,
    #     )

    # (
    #     [
    #         fetch_ad_group_universe_non_skc,
    #         fetch_ad_group_aggregates_non_skc,
    #         fetch_campaign_aggregates_non_skc,
    #         fetch_ppc_readiness_non_skc,
    #         fetch_acos_ceiling_calculation_non_skc,
    #     ]
    #     >> calculate_adgroup_rpc
    #     >> campaign_rpc_calculation
    # )

    wait_for_fact_amazon_ad_asins = SqlSensor(
        task_id="wait_for_fact_amazon_ad_asins",
        conn_id="snowflake_connection",
        timeout=60 * 60 * 12,
        poke_interval=60 * 3,
        mode="reschedule",
        on_failure_callback=alerts.send_failure_alert,
        sql=f"""
            select 
                1 
            from
                dwh.prod.fact_amazon_ad_asins
            where
                report_date = '{run_date}'::date
            limit 1
        """,
        dag=dag,
    )

    wait_for_fact_all_asin_rollup = SqlSensor(
        task_id="wait_for_fact_all_asin_rollup",
        conn_id="snowflake_connection",
        timeout=60 * 60 * 12,
        poke_interval=60 * 3,
        mode="reschedule",
        on_failure_callback=alerts.send_failure_alert,
        sql=f"""
            select 
                1 
            from
                dwh.prod.fact_all_asin_rollup
            where flag_refresh_ads=1
            and flag_refresh_orders=1
            and snapshot_date = '{run_date}'::date
            limit 1
        """,
        dag=dag,
    )

    begin = DummyOperator(
        task_id="begin", depends_on_past=False, wait_for_downstream=False
    )
    end_wait = DummyOperator(task_id="end_wait")
    end = DummyOperator(task_id="end")

    (
        begin
        >> [wait_for_fact_amazon_ad_asins, wait_for_fact_all_asin_rollup]
        >> end_wait
        >> [
            stg_adtech_campaigns_metadata,
            stg_adtech_adgroup_metadata,
            stg_adtech_adgroup_parent_asin_map,
            stg_adtech_campaign_parent_asin_map,
            stg_adtech_parentasin_tacos_ads_map,
            stg_adtech_experiment_parent_asins,
        ]
        >> tg_amazon_ads_target_keyword_fanout_details
        >> [
            tg_amazon_ads_adgroup_metrics,
            tg_amazon_ads_campaign_metrics,
            tg_amazon_ads_target_metrics,
        ]
        >> end
    )

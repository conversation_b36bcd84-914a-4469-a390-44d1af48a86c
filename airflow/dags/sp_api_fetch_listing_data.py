import warnings
import uuid
import json
import logging
from datetime import datetime
from airflow import DAG
from airflow.models import Variable
from airflow.providers.http.operators.http import SimpleHttpOperator
from airflow.operators.python import PythonOperator, BranchPythonOperator
from airflow.operators.dummy_operator import DummyOperator
from tasklib import alerts
from tasklib.loader import S3To<PERSON>nowflake
from helpers.helper import get_snapshot_date

load_obj = S3ToSnowflake()

log = logging.getLogger(__name__)

warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)

DAG_ID = 'sp_api_fetch_listing_data'
API_ENDPOINT_CONN_ID = 'dynamic_pricing_root_api_endpoint'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '0 17 * * *')  # Default to hourly runs
REQUEST_ID = str(uuid.uuid4())
WF_PARAMS_EXPR_MOD = json.dumps({"request_id": f'\'{REQUEST_ID}\''})
WF_PARAMS_EXPR = "{'wf_name': '" + DAG_ID.upper() + "'}"


def check_response(response):
    try:
        if response.status_code == 204:
            return True
        else:
            return False
    except Exception as e:
        log.error(f'API ERROR - {e}')
    return False


with DAG(
        dag_id=DAG_ID,
        start_date=datetime(2024, 8, 2),
        schedule_interval=DAG_SCHEDULE,
        catchup=False,
        max_active_runs=1,
        params={"author": "nagesh", 'workflow_name': DAG_ID.upper()},
        tags=['Nagesh'],
) as dag:
    import tasklib.sql as tlsql
    import tasklib.replication as tlr

    replicate_amazon_listings_sf_to_pg_task = PythonOperator(
        task_id="replicate_amazon_listings_sf_to_pg_task",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/pricing_amazon_listings.yaml"}
    )

    pricing_api_call_task = SimpleHttpOperator(
        task_id="pricing_api_call",
        http_conn_id=API_ENDPOINT_CONN_ID,
        endpoint=f'asin-price-validation/fetch/listings?snapshot_date={get_snapshot_date(delta_hours=0)}',
        method='POST',
        data="",
        headers={"Content-Type": "application/json"},
        response_check=lambda response: check_response(response),
        log_response=True,
    )

    begin_task = DummyOperator(task_id="begin")
    end_task = DummyOperator(task_id="end")

    begin_task >> replicate_amazon_listings_sf_to_pg_task >> pricing_api_call_task >> end_task

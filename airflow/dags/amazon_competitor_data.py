import logging
from airflow import DAG
from airflow.operators.python import Branch<PERSON>ythonOperator, PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
from airflow.utils.trigger_rule import TriggerRule
from datetime import datetime

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.loader as tll
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts

log = logging.getLogger(__name__)

S3_TO_SNOWFLAKE_ASIN_CATEGORY_MAPPING_YAML = 'amazon_competitor_data/asin_category_mapping_s3_to_snowflake.yaml'
S3_TO_SNOWFLAKE_CATEGORY_MAPPING_YAML = 'amazon_competitor_data/category_mapping_s3_to_snowflake.yaml'
S3_TO_SNOWFLAKE_COMPETITOR_ASIN_LIST_YAML = 'amazon_competitor_data/competitor_asin_list_s3_to_snowflake.yaml'
S3_TO_SNOWFLAKE_COMPETITOR_DATA_YAML = 'amazon_competitor_data/competitor_data_s3_to_snowflake.yaml'

S3_TO_SNOWFLAKE_JUNGLE_SCOUT_REVENUE_ESTIMATE_YAML  = 'amazon_competitor_data/jungle_scout_revenue_estimate_s3_to_snowflake.yaml'
WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"


with DAG(
    dag_id="amazon_competitor_data",
    start_date=datetime(2022,5,8),
    schedule_interval='0 */4 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name':'AMAZON_COMPETITOR_DATA'
           ,'author':'ruchira'},
    tags=['Ruchira']
) as dag:

    get_workflow_params=PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )

    #KEEPA file
    list_s3_modified_files_competitor_data=PythonOperator(
        task_id="task_list_s3_modified_files_competitor_data",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"amazon_competitor_data/competitor_data_s3_list_folders.yaml",
                   "wf_params":WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    skip_insert_competitor_data = DummyOperator(task_id='skip_insert_competitor_data')

    check_new_files_found_competitor_data = BranchPythonOperator(
        task_id='task_check_new_files_found_competitor_data',
        python_callable=tll.check_for_new_files,
        op_kwargs={
            'args_file': S3_TO_SNOWFLAKE_COMPETITOR_DATA_YAML,
            'skip_task_id': 'skip_insert_competitor_data',
            'next_task_id': 'task_transfer_s3_to_snowflake_competitor_data',
        }
    )

    from tasklib.loader import S3ToSnowflake
    load_obj = S3ToSnowflake()

    transfer_s3_to_snowflake_competitor_data = PythonOperator(
        task_id="task_transfer_s3_to_snowflake_competitor_data",
        python_callable=load_obj.s3_to_snowflake_load,
        op_kwargs={"args_file": "amazon_competitor_data/s3_to_sf_raw_amazon_competitor_data.yaml"},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )
    
    # transfer_s3_to_snowflake_competitor_data=PythonOperator(
    #     task_id="task_transfer_s3_to_snowflake_competitor_data",
    #     python_callable=tlg.transfer_s3_to_snowflake,
    #     op_kwargs={"args_file": S3_TO_SNOWFLAKE_COMPETITOR_DATA_YAML},
    #     provide_context=True,
    #     on_failure_callback=alerts.send_failure_alert
    # )

    insert_raw_amazon_competitor_data_log=PythonOperator(
        task_id="task_insert_raw_amazon_competitor_data_log",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/insert_raw_amazon_competitor_data_log.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )
    
    merge_stg_amazon_competitor_data=PythonOperator(
        task_id="task_merge_stg_amazon_competitor_data",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/merge_stg_amazon_competitor_data.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    #competitor ASIN list file
    list_s3_modified_files_competitor_asin_list=PythonOperator(
        task_id="task_list_s3_modified_files_competitor_asin_list",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"amazon_competitor_data/competitor_asin_list_s3_list_folders.yaml",
                   "wf_params":WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    skip_insert_competitor_asin_list = DummyOperator(task_id='skip_insert_competitor_asin_list')

    check_new_files_found_competitor_asin_list = BranchPythonOperator(
        task_id='task_check_new_files_found_competitor_asin_list',
        python_callable=tll.check_for_new_files,
        op_kwargs={
            'args_file': S3_TO_SNOWFLAKE_COMPETITOR_ASIN_LIST_YAML,
            'skip_task_id': 'skip_insert_competitor_asin_list',
            'next_task_id': 'task_transfer_s3_to_snowflake_competitor_asin_list',
        }
    )

    transfer_s3_to_snowflake_competitor_asin_list=PythonOperator(
        task_id="task_transfer_s3_to_snowflake_competitor_asin_list",
        python_callable=tlg.transfer_s3_to_snowflake,
        op_kwargs={"args_file": S3_TO_SNOWFLAKE_COMPETITOR_ASIN_LIST_YAML},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    insert_raw_amazon_competitor_asin_list_log=PythonOperator(
        task_id="task_insert_raw_amazon_competitor_asin_list_log",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/insert_raw_amazon_competitor_asin_list_log.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )
    
    merge_stg_amazon_competitor_asin_list=PythonOperator(
        task_id="task_merge_stg_amazon_competitor_asin_list",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/merge_stg_amazon_competitor_asin_list.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    #category mapping file
    list_s3_modified_files_category_mapping=PythonOperator(
        task_id="task_list_s3_modified_files_category_mapping",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"amazon_competitor_data/category_mapping_s3_list_folders.yaml",
                   "wf_params":WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    skip_insert_category_mapping = DummyOperator(task_id='skip_insert_category_mapping')

    check_new_files_found_category_mapping = BranchPythonOperator(
        task_id='task_check_new_files_found_category_mapping',
        python_callable=tll.check_for_new_files,
        op_kwargs={
            'args_file': S3_TO_SNOWFLAKE_CATEGORY_MAPPING_YAML,
            'skip_task_id': 'skip_insert_category_mapping',
            'next_task_id': 'task_transfer_s3_to_snowflake_category_mapping',
        }
    )

    transfer_s3_to_snowflake_category_mapping=PythonOperator(
        task_id="task_transfer_s3_to_snowflake_category_mapping",
        python_callable=tlg.transfer_s3_to_snowflake,
        op_kwargs={"args_file": S3_TO_SNOWFLAKE_CATEGORY_MAPPING_YAML},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    insert_raw_amazon_category_mapping_log=PythonOperator(
        task_id="task_insert_raw_amazon_category_mapping_log",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/insert_raw_amazon_category_mapping_log.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )
    
    merge_stg_amazon_category_mapping=PythonOperator(
        task_id="task_merge_stg_amazon_category_mapping",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/merge_stg_amazon_category_mapping.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    #heyday asin category mapping file
    list_s3_modified_files_asin_category_mapping=PythonOperator(
        task_id="task_list_s3_modified_files_asin_category_mapping",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"amazon_competitor_data/asin_category_mapping_s3_list_folders.yaml",
                   "wf_params":WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    skip_insert_asin_category_mapping = DummyOperator(task_id='skip_insert_asin_category_mapping')

    check_new_files_found_asin_category_mapping = BranchPythonOperator(
        task_id='task_check_new_files_found_asin_category_mapping',
        python_callable=tll.check_for_new_files,
        op_kwargs={
            'args_file': S3_TO_SNOWFLAKE_ASIN_CATEGORY_MAPPING_YAML,
            'skip_task_id': 'skip_insert_asin_category_mapping',
            'next_task_id': 'task_transfer_s3_to_snowflake_asin_category_mapping',
        }
    )

    transfer_s3_to_snowflake_asin_category_mapping=PythonOperator(
        task_id="task_transfer_s3_to_snowflake_asin_category_mapping",
        python_callable=tlg.transfer_s3_to_snowflake,
        op_kwargs={"args_file": S3_TO_SNOWFLAKE_ASIN_CATEGORY_MAPPING_YAML},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    insert_raw_amazon_asin_category_mapping_log=PythonOperator(
        task_id="task_insert_raw_amazon_asin_category_mapping_log",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/insert_raw_amazon_asin_category_mapping_log.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )
    
    merge_stg_amazon_asin_category_mapping=PythonOperator(
        task_id="task_merge_stg_amazon_asin_category_mapping",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/merge_stg_amazon_asin_category_mapping.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )


    #jungle scout file
    list_s3_modified_files_jungle_scout_revenue_estimate=PythonOperator(
        task_id="task_list_s3_modified_files_jungle_scout_revenue_estimate",
        python_callable=tls3.list_s3_modified_files,
        op_kwargs={"args_file":"amazon_competitor_data/jungle_scout_revenue_estimate_s3_list_folders.yaml",
                   "wf_params":WF_PARAMS_EXPR },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    skip_insert_jungle_scout_revenue_estimate = DummyOperator(task_id='skip_insert_jungle_scout_revenue_estimate')

    check_new_files_found_jungle_scout_revenue_estimate = BranchPythonOperator(
        task_id='task_check_new_files_found_jungle_scout_revenue_estimate',
        python_callable=tll.check_for_new_files,
        op_kwargs={
            'args_file': S3_TO_SNOWFLAKE_JUNGLE_SCOUT_REVENUE_ESTIMATE_YAML,
            'skip_task_id': 'skip_insert_jungle_scout_revenue_estimate',
            'next_task_id': 'task_transfer_s3_to_snowflake_jungle_scout_revenue_estimate_s3_list_folders',
        }
    )

    transfer_s3_to_snowflake_jungle_scout_revenue_estimate=PythonOperator(
        task_id="task_transfer_s3_to_snowflake_jungle_scout_revenue_estimate_s3_list_folders",
        python_callable=tlg.transfer_s3_to_snowflake,
        op_kwargs={"args_file": S3_TO_SNOWFLAKE_JUNGLE_SCOUT_REVENUE_ESTIMATE_YAML},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    insert_raw_amazon_competitor_revenue_estimate_log=PythonOperator(
        task_id="task_insert_raw_amazon_competitor_revenue_estimate_log",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/insert_raw_amazon_competitor_revenue_estimate_log.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )
    
    merge_stg_amazon_competitor_revenue_estimate=PythonOperator(
        task_id="task_merge_stg_amazon_competitor_revenue_estimate",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/merge_stg_amazon_competitor_revenue_estimate.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    #fact_amazon_competitor_data load
    create_stg_fact_amazon_competitor_data=PythonOperator(
        task_id="task_create_stg_fact_amazon_competitor_data",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/create_stg_fact_amazon_competitor_data.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_uniqueness_check_fact=PythonOperator(
        task_id="task_run_uniqueness_check_fact",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'amazon_competitor_data',
            'tb_name': "$stage_db.STG_fact_amazon_competitor_data",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.STG_fact_amazon_competitor_data", 
                field_list=['SNAPSHOT_DATE_PST','ASIN','BSR_CATEGORY'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    negative_avg_rating_dq_check=PythonOperator(
        task_id="task_negative_avg_rating_dq_check",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'amazon_competitor_data',
            'tb_name': "$stage_db.STG_fact_amazon_competitor_data",
            'test_name': 'AVG_RATING cannot be negative',
            'sql_query': """
                SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" FROM 
                (
                    SELECT * 
                    FROM  $stage_db.STG_fact_amazon_competitor_data
                    WHERE AVG_RATING IS NOT NULL AND AVG_RATING < 0
                    LIMIT 1
                )
            """
        },
        on_failure_callback=alerts.send_failure_alert
    )

    negative_amounts_dq_check=PythonOperator(
        task_id="task_negative_amounts_dq_check",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'amazon_competitor_data',
            'tb_name': "$stage_db.STG_fact_amazon_competitor_data",
            'test_name': 'amounts cannot be negative',
            'sql_query': """
                SELECT CASE WHEN COUNT(1)=1 THEN 1 ELSE 0 END AS "result" FROM 
                (
                    SELECT * 
                    FROM  $stage_db.STG_fact_amazon_competitor_data
                    WHERE LIST_PRICE < 0 OR BUY_BOX_PRICE < 0 OR BUY_BOX_PRICE_WITH_LIGHTNING_DEAL < 0 OR COUPON_DISCOUNT_AMOUNT < 0
                    LIMIT 1
                )
            """
        },
        on_failure_callback=alerts.send_failure_alert
    )

    merge_fact_amazon_competitor_data=PythonOperator(
        task_id="task_merge_fact_amazon_competitor_data",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/merge_fact_amazon_competitor_data.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_fact_amazon_competitor_data=PythonOperator(
        task_id="task_update_fact_amazon_competitor_data",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/update_fact_amazon_competitor_data.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_jungle_scout_data=PythonOperator(
        task_id="task_update_jungle_scout_data",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/update_jungle_scout_data.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_stg_fact_hydy_competitor_comparison=PythonOperator(
        task_id="task_create_stg_fact_hydy_competitor_comparison",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/create_stg_fact_hydy_competitor_comparison.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )
    
    run_uniqueness_check_comparison_fact=PythonOperator(
        task_id="task_run_uniqueness_check_comparison_fact",
        python_callable=tldq.run_dq_string,
        op_kwargs={
            'wk_name': 'amazon_competitor_data',
            'tb_name': "$stage_db.STG_FACT_HYDY_COMPETITOR_COMPARISON",
            'test_name': 'pk_not_duplicate',
            'sql_query': tldq.gen_check_unique_key(
                tb_name="$stage_db.STG_FACT_HYDY_COMPETITOR_COMPARISON", 
                field_list=['SNAPSHOT_DATE','MARKETPLACE','HYDY_SELLER_ID','HYDY_COUNTRY_CODE','HYDY_ASIN','CATEGORY','HYDY_BRAND_CODE'],
                hard_alert=True
            )
        },
        on_failure_callback=alerts.send_failure_alert
    )

    merge_fact_hydy_competitor_comparison=PythonOperator(
        task_id="task_merge_fact_hydy_competitor_comparison",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake","sql_file":"competitor_data/amazon/merge_fact_hydy_competitor_comparison.sql", "wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    update_fact_hydy_competitor_comparison = PythonOperator(
        task_id="task_update_fact_hydy_competitor_comparison",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection":"Snowflake",
            "sql_file":"competitor_data/amazon/update_fact_hydy_competitor_comparison.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    #workflow update
    update_workflow_params=PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    run_audit_l2=PythonOperator(
        task_id="task_run_audit_l2",
        python_callable=tla.run_audit,
        op_kwargs={
                    "table_name": "$curated_db.FACT_AMAZON_COMPETITOR_DATA", 
                    "wf_params": WF_PARAMS_EXPR,
                    "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                    "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC' 
                  },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    run_audit_l3=PythonOperator(
        task_id="task_run_audit_l3",
        python_callable=tla.run_audit,
        op_kwargs={
                    "table_name": "$curated_db.FACT_HYDY_COMPETITOR_COMPARISON", 
                    "wf_params": WF_PARAMS_EXPR,
                    "ts_created_field": 'RECORD_CREATED_TIMESTAMP_UTC',
                    "ts_updated_field": 'RECORD_UPDATED_TIMESTAMP_UTC' 
                  },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin",depends_on_past=True,wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    staging_load_complete = DummyOperator(
        task_id="staging_load_complete",
        wait_for_downstream=True,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
    )

    skip_insert = DummyOperator(task_id='skip_insert')

    check_new_files_found = BranchPythonOperator( 
        task_id='check_new_files_found',
        python_callable=tll.check_all_files,
        provide_context=True,
        op_kwargs={
            'args_files': [
                S3_TO_SNOWFLAKE_ASIN_CATEGORY_MAPPING_YAML,
                S3_TO_SNOWFLAKE_CATEGORY_MAPPING_YAML,
                S3_TO_SNOWFLAKE_COMPETITOR_ASIN_LIST_YAML,
                S3_TO_SNOWFLAKE_COMPETITOR_DATA_YAML,
                S3_TO_SNOWFLAKE_JUNGLE_SCOUT_REVENUE_ESTIMATE_YAML,
            ],
            'skip_task_id': 'skip_insert',
            'next_task_id': 'task_create_stg_fact_amazon_competitor_data',
        }
    )

    #Assemble the dag 
    chain(
        begin, 
        get_workflow_params, 
        [list_s3_modified_files_competitor_data,list_s3_modified_files_competitor_asin_list,list_s3_modified_files_category_mapping,list_s3_modified_files_asin_category_mapping,list_s3_modified_files_jungle_scout_revenue_estimate], 
        [check_new_files_found_competitor_data, check_new_files_found_competitor_asin_list, check_new_files_found_category_mapping, check_new_files_found_asin_category_mapping, check_new_files_found_jungle_scout_revenue_estimate],
    )

    check_new_files_found_competitor_data >> [transfer_s3_to_snowflake_competitor_data, skip_insert_competitor_data]
    check_new_files_found_competitor_asin_list >> [transfer_s3_to_snowflake_competitor_asin_list, skip_insert_competitor_asin_list]
    check_new_files_found_category_mapping >> [transfer_s3_to_snowflake_category_mapping, skip_insert_category_mapping]
    check_new_files_found_asin_category_mapping >> [transfer_s3_to_snowflake_asin_category_mapping, skip_insert_asin_category_mapping]
    check_new_files_found_jungle_scout_revenue_estimate >> [transfer_s3_to_snowflake_jungle_scout_revenue_estimate, skip_insert_jungle_scout_revenue_estimate]

    [skip_insert_competitor_data, skip_insert_competitor_asin_list, skip_insert_category_mapping, skip_insert_asin_category_mapping, skip_insert_jungle_scout_revenue_estimate] >> staging_load_complete

    chain(
        [transfer_s3_to_snowflake_competitor_data, transfer_s3_to_snowflake_competitor_asin_list, transfer_s3_to_snowflake_category_mapping, transfer_s3_to_snowflake_asin_category_mapping, transfer_s3_to_snowflake_jungle_scout_revenue_estimate],
        [insert_raw_amazon_competitor_data_log,insert_raw_amazon_competitor_asin_list_log,insert_raw_amazon_category_mapping_log,insert_raw_amazon_asin_category_mapping_log,insert_raw_amazon_competitor_revenue_estimate_log],
        [merge_stg_amazon_competitor_data,merge_stg_amazon_competitor_asin_list,merge_stg_amazon_category_mapping,merge_stg_amazon_asin_category_mapping,merge_stg_amazon_competitor_revenue_estimate],
        staging_load_complete,
        check_new_files_found,
        [create_stg_fact_amazon_competitor_data, skip_insert]
    )

    chain(
        create_stg_fact_amazon_competitor_data,
        run_uniqueness_check_fact,
        negative_avg_rating_dq_check,
        negative_amounts_dq_check,
        merge_fact_amazon_competitor_data,
        update_fact_amazon_competitor_data,
        update_jungle_scout_data,
        create_stg_fact_hydy_competitor_comparison,
        run_uniqueness_check_comparison_fact,
        merge_fact_hydy_competitor_comparison,
        update_fact_hydy_competitor_comparison,
        run_audit_l2,
        run_audit_l3,
        update_workflow_params,
        end
    )

    chain(
        skip_insert,
        update_workflow_params,
        end
    )

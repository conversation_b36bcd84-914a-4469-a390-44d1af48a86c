from airflow import DAG
from datetime import datetime
from airflow.operators.python import PythonOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.models.baseoperator import chain
import logging
from tasklib import alerts
import tasklib.sql as tlsql
import tasklib.dq as tldq

log = logging.getLogger(__name__)

BUILD_NUM = '1'
RELEASE_DEF = '1'
DAG_ID = 'subledger_essor_airbyte'
WF_PARAMS_EXPR = "{'wf_name': '" + DAG_ID.upper() + "'}"

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2023, 5, 1),
    schedule_interval='@daily',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID.upper(),
            'author': 'sahil'},
) as dag:

    sp_api_amazon_drr_consolidated_clean = PythonOperator(
        task_id="sp_api_amazon_drr_consolidated_clean",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "subledger_essor_airbyte/sp_api_amazon_drr_consolidated_clean.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    amazon_settlements_transactions_airbyte = PythonOperator(
        task_id="amazon_settlements_transactions_airbyte",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "subledger_essor_airbyte/amazon_settlements_transactions_airbyte.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    amazon_drr_with_gl_mapping = PythonOperator(
        task_id="amazon_drr_with_gl_mapping",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "subledger_essor_airbyte/amazon_drr_with_gl_mapping.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    amazon_drr_netsuite = PythonOperator(
        task_id="amazon_drr_netsuite",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "subledger_essor_airbyte/amazon_drr_netsuite.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")


    chain(
        begin,
        sp_api_amazon_drr_consolidated_clean,
        amazon_drr_netsuite,
        amazon_drr_with_gl_mapping,
        amazon_settlements_transactions_airbyte,
        end
    )

import logging
from datetime import datetime, timedelta

from airflow import DAG
import json
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.utils.task_group import TaskGroup
import tasklib.audit as tla
from airflow.models.baseoperator import chain

from tasklib.sql import resolve_query
import tasklib.dq as tldq
import tasklib.sql as tlsql
from tasklib import alerts
from airflow.models import Variable

import sys
import warnings

import yaml
from boto3 import client
from dateutil import parser
from db_connectors.pg_connector import Postgres
from db_connectors.sf_connector import Snowflake

DAG_ID = 'adtech_ads_metrics_all'
TRANSFORM_BASE = 'ads/adtech/' + DAG_ID
RELEASE_DEF = '1'
BUILD_NUM = '01'

DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '40 * * * *')

BUDGET_FLAG_LOGIC = Variable.get("stg_ad_campaign_metrics_today_all__budget_flag_logic")
TACOS_FLAG_LOGIC = Variable.get("stg_ad_group_metrics_today_all__tacos_flag_logic")
SUGGESTED_BUDGET_PCT_CHANGE_LOGIC = Variable.get("stg_ad_campaign_metrics_today_all__suggested_budget_pct_change_logic")
SUGGESTED_BID_LOGIC = Variable.get("stg_ad_group_metrics_today_all__suggested_bid_logic")

_AIRFLOW_CONN_ID = 'airflow_conn_id'
_DB_TYPE = 'db_type'
_FULL_TABLE_NAME = 'full_table_name'
_INCREMENTAL_COLUMN = 'incremental_column'
_LOAD_DATA_OPTIONS = 'load_data_from_s3_copy_options'
_POSTGRES = 'postgres'
_SNOWFLAKE = 'snowflake'
_SOURCE = 'source'
_TARGET = 'target'
_UNLOAD_DATA_OPTIONS = 'unload_data_into_s3_copy_options'
_UNIQUE_KEY_COLUMNS = 'unique_key_columns'
_FUNC_MAPPING = {
    _SNOWFLAKE: Snowflake,
    _POSTGRES: Postgres,
}
_QUERY_FILE_PATH = 'query_file_path'

run_date = '{{ macros.ds_add(ds, -1) }}'

WF_PARAMS_EXPR = json.dumps(
    {
        'run_date': run_date, 
        'budget_flag_logic': BUDGET_FLAG_LOGIC, 
        'tacos_flag_logic': TACOS_FLAG_LOGIC, 
        'suggested_budget_pct_change_logic': SUGGESTED_BUDGET_PCT_CHANGE_LOGIC, 
        'suggested_bid_logic': SUGGESTED_BID_LOGIC
    }
)

log = logging.getLogger(__name__)
warnings.filterwarnings("ignore")

class DataReplicator():
    def __init__(self, args_file):
        self.s3_client = client('s3')
        import tasklib.config as tlcfg
        env_configs = tlcfg.get_config()
        self.source_run_env = env_configs.get('environment')
        self.aws_region = env_configs.get('region')

        log.info(f"run_env: {self.source_run_env}, aws_region: {self.aws_region}")

        self.snowflake_stage = env_configs.get('replication_config').get('snowflake_stage')
        self.s3_staging_bucket = env_configs.get('s3_staging_bucket')
        self.config_file_bucket = env_configs.get('glue_config').get('s3_bucket')
        self.config_file_key = env_configs.get('glue_config').get('config_path')
        self.config_file_path = f"{self.config_file_key}/{args_file}"

        self.config_res = self.s3_client.get_object(Bucket=self.config_file_bucket, Key=self.config_file_path)
        self.replication_configs = yaml.safe_load(self.config_res["Body"])

        # Source parameters
        self.src_airflow_conn_id = self.replication_configs[_SOURCE][_AIRFLOW_CONN_ID]
        self.src_full_table_name = self.replication_configs[_SOURCE][_FULL_TABLE_NAME]
        self.src_unload_copy_options = self.replication_configs[_SOURCE][_UNLOAD_DATA_OPTIONS]
        self.src_client_type = self.replication_configs[_SOURCE][_DB_TYPE]

        # Target parameters
        self.tgt_airflow_conn_id = self.replication_configs[_TARGET][_AIRFLOW_CONN_ID]
        self.tgt_full_table_name = self.replication_configs[_TARGET][_FULL_TABLE_NAME]
        self.tgt_load_copy_options = self.replication_configs[_TARGET][_LOAD_DATA_OPTIONS]
        self.tgt_client_type = self.replication_configs[_TARGET][_DB_TYPE]

        # Incremental & unique key columns
        self.key_columns = self.replication_configs[_UNIQUE_KEY_COLUMNS]
        self.incremental_column = self.replication_configs[_INCREMENTAL_COLUMN]
        self.query_file_path = self.replication_configs.get(_QUERY_FILE_PATH)

        if self.src_client_type == self.tgt_client_type:
            raise Exception('Source & Target db_type should not be the same')

        if len(set([self.src_client_type, self.tgt_client_type]) ^ set([_SNOWFLAKE, _POSTGRES])):
            raise Exception(f'Source & Target db_type should be either {_SNOWFLAKE} or {_POSTGRES}')

        # The snowflake s3 stage is setup with the raptor prefix e.g. s3://staging-data-layer/raptor
        # For postgres, we need to add the raptor prefix to the path
        s3_prefix = self.src_full_table_name.replace('.', '_').lower()
        if self.src_client_type == _SNOWFLAKE:
            self.src_s3_prefix = f"data_replication/{self.src_client_type.lower()}_landing/{s3_prefix}"
            self.tgt_s3_prefix = f"raptor/{self.src_s3_prefix}/"
        elif self.src_client_type == _POSTGRES:
            self.tgt_s3_prefix = f"data_replication/{self.src_client_type.lower()}_landing/{s3_prefix}"
            self.src_s3_prefix = f"raptor/{self.tgt_s3_prefix}/"

        self.src_client = _FUNC_MAPPING[self.src_client_type](self.src_airflow_conn_id)
        self.tgt_client = _FUNC_MAPPING[self.tgt_client_type](self.tgt_airflow_conn_id)

        self.src_database_name, _, _ = self.src_full_table_name.split('.')
        self.tgt_database_name, _, _ = self.tgt_full_table_name.split('.')


def replicate_data(args_file) -> None:
    """
    Sync data from Source to Target using S3 as middle layer.
    """

    rep_obj = DataReplicator(args_file)

    # Snowflake needs the stage name, Postgres needs the s3 bucket name
    if rep_obj.src_client_type == _SNOWFLAKE:
        unload_param = rep_obj.snowflake_stage
        load_param = rep_obj.s3_staging_bucket
    elif rep_obj.src_client_type == _POSTGRES:
        unload_param = rep_obj.s3_staging_bucket
        load_param = rep_obj.snowflake_stage

    try:
        run = False
        condition = "1=1"
        if rep_obj.incremental_column is None:
            log.info(
                f"Truncating table {rep_obj.tgt_full_table_name} for full load as incremental column is not defined in config file")
            rep_obj.tgt_client.execute_statement(f"TRUNCATE TABLE {rep_obj.tgt_full_table_name}")
            run = True
        else:
            query = f"""SELECT COALESCE(MAX({rep_obj.incremental_column}), '1990-01-01 00:00:00.000000') FROM {rep_obj.src_full_table_name}"""
            src_max_datetime, _ = rep_obj.src_client.get_data(query)
            src_max_datetime = parser.parse(str(src_max_datetime[0][0]))

            query = f"""SELECT COALESCE(MAX({rep_obj.incremental_column}), '1990-01-01 00:00:00.000000') FROM {rep_obj.tgt_full_table_name}"""
            tgt_max_datetime, _ = rep_obj.tgt_client.get_data(query)
            tgt_max_datetime = parser.parse(str(tgt_max_datetime[0][0]))

            if src_max_datetime > tgt_max_datetime:
                condition = f"{rep_obj.incremental_column} >= '{(tgt_max_datetime + timedelta(seconds=1)).replace(microsecond=0)}'"
                run = True
            else:
                log.info(
                    f"Skipping incremental replication for table {rep_obj.src_full_table_name}; data up-to-date")

        if run:
            data_query = f"""SELECT * FROM {rep_obj.src_full_table_name} WHERE {condition}"""

            # Block Summary: Unload the data from source to s3
            if rep_obj.query_file_path:
                log.info(f"Picking Query from {rep_obj.query_file_path}")
                data_query = resolve_query(sql_file=rep_obj.query_file_path)
                data_query = data_query.replace(":filter_statement", condition)
                log.info(f"Query {data_query}")

            rep_obj.src_client.unload_data_into_s3(unload_param, rep_obj.src_s3_prefix, data_query,
                                                   rep_obj.src_unload_copy_options)

            # Block Summary: Load the data from S3 into postgres
            rep_obj.tgt_client.load_data_from_s3(
                load_param, rep_obj.tgt_s3_prefix, rep_obj.tgt_full_table_name, rep_obj.key_columns,
                rep_obj.tgt_load_copy_options
            )

        return run

    except Exception as err:
        exc_type, exception_value, _ = sys.exc_info()
        log.error(
            str(exception_value),
            extra={
                "failure_details": {
                    "failure_code": f"{exc_type.__name__}",
                    "failure_reason": str(exception_value),
                }
            },
            exc_info=err,
        )
        raise err

with DAG(
    dag_id=DAG_ID,
    description='Release:{0}-Build:{1}'.format(RELEASE_DEF, BUILD_NUM),
    start_date=datetime(2022, 5, 8),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': DAG_ID,
            'author': 'vaibhav'},
    tags=['vaibhav']
) as dag:

    with TaskGroup(group_id='load_scs_amazon_ad_campaigns') as tg_scs_amazon_ad_campaigns:

        stg_scs_scrape_ad_campaigns_today = PythonOperator(
            task_id="stg_scs_scrape_ad_campaigns_today",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file":TRANSFORM_BASE + "/stg_scs_scrape_ad_campaigns_today.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_stg_scs_scrape_ad_campaigns_today = PythonOperator(
            task_id="run_dq_is_unique_stg_scs_scrape_ad_campaigns_today",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.stg_scs_scrape_ad_campaigns_today', 
                    field_list=['campaign_id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        stg_scs_scrape_ad_campaigns_today >> run_dq_is_unique_stg_scs_scrape_ad_campaigns_today

    #  --- Load scs_amazon_orders ---
    
    with TaskGroup(group_id='load_scs_amazon_orders') as tg_scs_amazon_orders:
        stg_scs_scrape_orders_asin_today = PythonOperator(
            task_id="stg_scs_scrape_orders_asin_today",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file":TRANSFORM_BASE + "/stg_scs_scrape_orders_asin_today.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_stg_scs_scrape_orders_asin_today = PythonOperator(
            task_id="run_dq_is_unique_stg_scs_scrape_orders_asin_today",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.stg_scs_scrape_orders_asin_today', 
                    field_list=['seller_id', 'seller_order_id','order_item_id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
        
        stg_scs_scrape_orders_asin_today >> run_dq_is_unique_stg_scs_scrape_orders_asin_today
    
    stg_scs_scrape_parent_asin_tacos_today = PythonOperator(
                task_id="stg_scs_scrape_parent_asin_tacos_today",
                python_callable=tlsql.run_query_file,
                op_kwargs={"connection":"Snowflake",
                            "sql_file":TRANSFORM_BASE + "/stg_scs_scrape_parent_asin_tacos_today.sql",
                            "wf_params": WF_PARAMS_EXPR
                },
                provide_context=True,
                on_failure_callback=alerts.send_failure_alert,
    )

    stg_scs_scrape_adtech_adgroup_metadata = PythonOperator(
        task_id="stg_scs_scrape_adtech_adgroup_metadata",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake",
                    "sql_file":TRANSFORM_BASE + "/stg_scs_scrape_adtech_adgroup_metadata.sql",
                    "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    stg_scs_scrape_adtech_campaigns_metadata = PythonOperator(
        task_id="stg_scs_scrape_adtech_campaigns_metadata",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection":"Snowflake",
                    "sql_file":TRANSFORM_BASE + "/stg_scs_scrape_adtech_campaigns_metadata.sql",
                    "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    stg_scs_scrape_adtech_targets_metadata = PythonOperator(
        task_id="stg_scs_scrape_adtech_targets_metadata",
        python_callable=tlsql.run_query_file,
        op_kwargs={"connection": "Snowflake",
                   "sql_file": TRANSFORM_BASE + "/stg_scs_scrape_adtech_targets_metadata.sql",
                   "wf_params": WF_PARAMS_EXPR
                   },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    with TaskGroup(group_id='tg_stg_ad_campaign_metrics_today_all') as tg_stg_ad_campaign_metrics_today_all:
        
        stg_ad_campaign_metrics_today_all = PythonOperator(
            task_id="stg_ad_campaign_metrics_today_all",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                        "sql_file":TRANSFORM_BASE + "/stg_ad_campaign_metrics_today_all.sql",
                        "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_stg_ad_campaign_metrics_today_all = PythonOperator(
            task_id="run_dq_is_unique_stg_ad_campaign_metrics_today_all",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.stg_ad_campaign_metrics_today_all', 
                    field_list=['campaign_id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        amazon_ads_campaign_metrics_all = PythonOperator(
            task_id="amazon_ads_campaign_metrics_all",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                        "sql_file":TRANSFORM_BASE + "/amazon_ads_campaign_metrics_all.sql",
                        "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        log_stg_ad_campaign_metrics_today_all = PythonOperator(
            task_id="log_stg_ad_campaign_metrics_today_all",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/log_stg_ad_campaign_metrics_today_all.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        stg_ad_campaign_metrics_today_all >> run_dq_is_unique_stg_ad_campaign_metrics_today_all >> amazon_ads_campaign_metrics_all >> log_stg_ad_campaign_metrics_today_all


    with TaskGroup(group_id='tg_stg_ad_group_metrics_today_all') as tg_stg_ad_group_metrics_today_all:
        stg_ad_group_metrics_today_all = PythonOperator(
            task_id="stg_ad_group_metrics_today_all",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                        "sql_file":TRANSFORM_BASE + "/stg_ad_group_metrics_today_all.sql",
                        "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_stg_ad_group_metrics_today_all = PythonOperator(
            task_id="run_dq_is_unique_stg_ad_group_metrics_today_all",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.stg_ad_group_metrics_today_all', 
                    field_list=['ad_group_id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        amazon_ads_adgroup_metrics_all = PythonOperator(
            task_id="amazon_ads_adgroup_metrics_all",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                        "sql_file":TRANSFORM_BASE + "/amazon_ads_adgroup_metrics_all.sql",
                        "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        log_stg_ad_group_metrics_metrics_today_all = PythonOperator(
            task_id="log_stg_ad_group_metrics_metrics_today_all",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/log_stg_ad_group_metrics_metrics_today_all.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        stg_ad_group_metrics_today_all >> run_dq_is_unique_stg_ad_group_metrics_today_all >> amazon_ads_adgroup_metrics_all >> log_stg_ad_group_metrics_metrics_today_all

    with TaskGroup(group_id='tg_stg_targets_metrics_today_all') as tg_stg_targets_metrics_today_all:
        stg_targets_metrics_today_all = PythonOperator(
            task_id="stg_targets_metrics_today_all",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                        "sql_file":TRANSFORM_BASE + "/stg_ad_targets_metrics_today_all.sql",
                        "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_stg_targets_metrics_today_all = PythonOperator(
            task_id="run_dq_is_unique_stg_targets_metrics_today_all",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.stg_targets_metrics_today_all',
                    field_list=['target_id', 'country_code'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        amazon_ads_targets_metrics_all = PythonOperator(
            task_id="amazon_ads_targets_metrics_all",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                        "sql_file":TRANSFORM_BASE + "/amazon_ads_targets_metrics_all.sql",
                        "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        log_stg_targets_metrics_metrics_today_all = PythonOperator(
            task_id="log_stg_targets_metrics_metrics_today_all",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/log_stg_targets_metrics_metrics_today_all.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        stg_targets_metrics_today_all >> run_dq_is_unique_stg_targets_metrics_today_all >> amazon_ads_targets_metrics_all >> log_stg_targets_metrics_metrics_today_all

    with TaskGroup(group_id='tg_stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all') as tg_stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all:
        stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all = PythonOperator(
            task_id="stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                        "sql_file":TRANSFORM_BASE + "/stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all.sql",
                        "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all = PythonOperator(
            task_id="run_dq_is_unique_stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all', 
                    field_list=['target_id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        amazon_ads_target_keyword_fanout_details_all = PythonOperator(
            task_id="amazon_ads_target_keyword_fanout_details_all",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                        "sql_file":TRANSFORM_BASE + "/amazon_ads_target_keyword_fanout_details_all.sql",
                        "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        log_stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all = PythonOperator(
            task_id="log_stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": TRANSFORM_BASE + "/log_stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all.sql",
                "wf_params": WF_PARAMS_EXPR},
            on_failure_callback=alerts.send_failure_alert,
            provide_context=True
        )

        stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all >> run_dq_is_unique_stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all >> amazon_ads_target_keyword_fanout_details_all >> log_stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all

    with TaskGroup(group_id='hannibal_sync') as tg_hannibal_sync:
        task_amazon_ad_metrics = PythonOperator(
            task_id="task_amazon_ad_metrics_replication",
            python_callable=replicate_data,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={"args_file": "data_replication/sf_to_pg/hannibal/config_amazon_ad_group_metrics.yaml"},
        )

        task_amazon_targets = PythonOperator(
            task_id="task_amazon_targets_replication",
            python_callable=replicate_data,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={"args_file": "data_replication/sf_to_pg/hannibal/config_amazon_targets_metrics.yaml"},
        )

        task_amazon_campaign_metrics = PythonOperator(
            task_id="task_amazon_campaign_metrics_replication",
            python_callable=replicate_data,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={"args_file": "data_replication/sf_to_pg/hannibal/config_amazon_campaign_metrics.yaml"},
        )

        task_amazon_fan_out = PythonOperator(
            task_id="task_amazon_fan_out_replication",
            python_callable=replicate_data,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={"args_file": "data_replication/sf_to_pg/hannibal/config_fan_out_details.yaml"},
        )

        task_ad_sdk_amazon_fan_out = PythonOperator(
            task_id="task_ad_sdk_amazon_fan_out",
            python_callable=replicate_data,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={"args_file": "data_replication/sf_to_pg/amazon_ad_sdk/config_fan_out_details.yaml"},
        )

        # task_dim_parent_asin = PythonOperator(
        #     task_id="task_dim_parent_asin_replication",
        #     python_callable=replicate_data,
        #     provide_context=True,
        #     on_failure_callback=alerts.send_failure_alert,
        #     op_kwargs={"args_file": "data_replication/sf_to_pg/hannibal/config_dim_parent_asin.yaml"},
        # )

        task_optimization_details = PythonOperator(
            task_id="task_optimization_details",
            python_callable=replicate_data,
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            op_kwargs={"args_file": "data_replication/pg_to_sf/hannibal/optimization_details.yaml"},
        )

        begin_sync = DummyOperator(task_id="begin_sync", depends_on_past=False, wait_for_downstream=False)
        dummy = DummyOperator(task_id="dummy")
        end_sync = DummyOperator(task_id="end_sync")

        begin_sync >> [task_amazon_ad_metrics, task_amazon_targets, task_amazon_campaign_metrics,
                  task_amazon_fan_out] >> dummy >> [task_ad_sdk_amazon_fan_out, task_optimization_details] >> end_sync

    begin = DummyOperator(task_id="begin", depends_on_past=False, wait_for_downstream=False)
    end = DummyOperator(task_id="end")

    # ---- Main branch ----
    scs_load = [
        tg_scs_amazon_ad_campaigns,
        tg_scs_amazon_orders,
        stg_scs_scrape_adtech_adgroup_metadata,
        stg_scs_scrape_adtech_campaigns_metadata,
        stg_scs_scrape_adtech_targets_metadata
    ]

    begin >> scs_load >>stg_scs_scrape_parent_asin_tacos_today

    stg_scs_scrape_parent_asin_tacos_today >> tg_stg_targets_metrics_today_all >> tg_stg_ad_campaign_metrics_today_all >> tg_stg_ad_group_metrics_today_all
    stg_scs_scrape_parent_asin_tacos_today >> tg_stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all

    metrics_load = [
        tg_stg_ad_group_metrics_today_all,
        tg_stg_scs_scrape_amazon_ads_target_keyword_fanout_details_all
    ]

    metrics_load >> tg_hannibal_sync >> end

    
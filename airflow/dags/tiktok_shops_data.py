"""
Code generator for airflow ingestion dags.
"""
import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import Branch<PERSON><PERSON>honOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake

load_obj = S3ToSnowflake()

BUILD_NUM = '1'
DAG_ID = 'tiktok_shops_data'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{{ ti.xcom_pull(task_ids='get_workflow_parameters') }}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2024, 1, 1),
    schedule_interval=DAG_SCHEDULE,
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'TIKTOK_SHOPS_DATA',
            'author': 'vikas'},
    tags=['vikas', 'Raptor', 'Goessor']
) as dag:

    get_workflow_parameters = PythonOperator(
        task_id="get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}"}
        
    )

    begin = DummyOperator(task_id="begin", depends_on_past=True, wait_for_downstream=True)
    end = DummyOperator(task_id="end")

    update_workflow_parameters = PythonOperator(
        task_id="update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": WF_PARAMS_EXPR},
        on_failure_callback=alerts.send_failure_alert,
        provide_context=True,
    )

    #  ----[ The task groups for each table load ]----

    #  --- Load tiktok_shop_orders ---
    
    with TaskGroup(group_id='load_tiktok_shop_orders') as tg_tiktok_shop_orders:
        list_s3_files_tiktok_shop_orders_task = PythonOperator(
            task_id="list_s3_files_tiktok_shop_orders",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_shops_data/list_s3_tiktok_shop_orders.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_tiktok_shop_orders_task_goessor = PythonOperator(
            task_id="list_s3_files_tiktok_shop_orders_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_shops_data/list_s3_tiktok_shop_orders_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_tiktok_shop_orders_task = DummyOperator(task_id="begin_insert_tiktok_shop_orders")
        skip_insert_tiktok_shop_orders_task = DummyOperator(task_id="skip_insert_tiktok_shop_orders")
        end_insert_tiktok_shop_orders_task = DummyOperator(task_id="end_insert_tiktok_shop_orders")
        end_tiktok_shop_orders_task = DummyOperator(
            task_id="end_tiktok_shop_orders",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_tiktok_shop_orders_task = BranchPythonOperator(
            task_id='check_new_files_found_tiktok_shop_orders',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_snowflake_tiktok_shop_orders.yaml",
                       "args_file_second": "tiktok_shops_data/s3_to_snowflake_tiktok_shop_orders_goessor.yaml",
                       "skip_task_id": "load_tiktok_shop_orders.skip_insert_tiktok_shop_orders",
                       "next_task_id": "load_tiktok_shop_orders.begin_insert_tiktok_shop_orders"
            },
        )

        s3_to_snowflake_tiktok_shop_orders_task = PythonOperator(
            task_id="s3_to_snowflake_tiktok_shop_orders",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_sf_raw_tiktok_shop_orders.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_tiktok_shop_orders_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_tiktok_shop_orders_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_sf_raw_tiktok_shop_orders_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_tiktok_shop_orders_task = PythonOperator(
            task_id="insert_log_tiktok_shop_orders",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/insert_log_tiktok_shop_orders.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_tiktok_shop_orders_task = PythonOperator(
            task_id="dedupe_tiktok_shop_orders",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/dedupe_tiktok_shop_orders.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_orders',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_tiktok_shop_orders', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_orders',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_tiktok_shop_orders', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        merge_stage_tiktok_shop_orders_task = PythonOperator(
            task_id="merge_stage_tiktok_shop_orders",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/merge_stage_tiktok_shop_orders.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        
        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_orders',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_tiktok_shop_orders', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_orders',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_tiktok_shop_orders', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        run_audit_tiktok_shop_orders_task = PythonOperator(
            task_id="run_audit_tiktok_shop_orders",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_tiktok_shop_orders", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        (
            [list_s3_files_tiktok_shop_orders_task, list_s3_files_tiktok_shop_orders_task_goessor] >>
            check_new_files_found_tiktok_shop_orders_task >>
            [begin_insert_tiktok_shop_orders_task, skip_insert_tiktok_shop_orders_task]
        )

        (
            begin_insert_tiktok_shop_orders_task >> 
            s3_to_snowflake_tiktok_shop_orders_task >>
            s3_to_snowflake_tiktok_shop_orders_task_goessor >>
            insert_log_tiktok_shop_orders_task >>
            dedupe_tiktok_shop_orders_task >>
            
         ( run_dq_is_unique_dedupe_pk_hard_task, 
    
         run_dq_is_null_dedupe_pk_hard_task) >>
    
            merge_stage_tiktok_shop_orders_task >>
            
         ( run_dq_is_unique_merge_pk_hard_task, 
    
         run_dq_is_null_merge_pk_hard_task) >>
    
            run_audit_tiktok_shop_orders_task >>
            end_insert_tiktok_shop_orders_task >>
            end_tiktok_shop_orders_task
        )

        skip_insert_tiktok_shop_orders_task >> end_tiktok_shop_orders_task

    #  --- Load tiktok_shop_packages ---
    
    with TaskGroup(group_id='load_tiktok_shop_packages') as tg_tiktok_shop_packages:
        list_s3_files_tiktok_shop_packages_task = PythonOperator(
            task_id="list_s3_files_tiktok_shop_packages",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_shops_data/list_s3_tiktok_shop_packages.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        list_s3_files_tiktok_shop_packages_task_goessor = PythonOperator(
            task_id="list_s3_files_tiktok_shop_packages_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_shops_data/list_s3_tiktok_shop_packages_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_tiktok_shop_packages_task = DummyOperator(task_id="begin_insert_tiktok_shop_packages")
        skip_insert_tiktok_shop_packages_task = DummyOperator(task_id="skip_insert_tiktok_shop_packages")
        end_insert_tiktok_shop_packages_task = DummyOperator(task_id="end_insert_tiktok_shop_packages")
        end_tiktok_shop_packages_task = DummyOperator(
            task_id="end_tiktok_shop_packages",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_tiktok_shop_packages_task = BranchPythonOperator(
            task_id='check_new_files_found_tiktok_shop_packages',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_snowflake_tiktok_shop_packages.yaml",
                       "args_file_second": "tiktok_shops_data/s3_to_snowflake_tiktok_shop_packages_goessor.yaml",
                       "skip_task_id": "load_tiktok_shop_packages.skip_insert_tiktok_shop_packages",
                       "next_task_id": "load_tiktok_shop_packages.begin_insert_tiktok_shop_packages"
            },
        )

        s3_to_snowflake_tiktok_shop_packages_task = PythonOperator(
            task_id="s3_to_snowflake_tiktok_shop_packages",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_sf_raw_tiktok_shop_packages.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_tiktok_shop_packages_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_tiktok_shop_packages_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_sf_raw_tiktok_shop_packages_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_tiktok_shop_packages_task = PythonOperator(
            task_id="insert_log_tiktok_shop_packages",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/insert_log_tiktok_shop_packages.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_tiktok_shop_packages_task = PythonOperator(
            task_id="dedupe_tiktok_shop_packages",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/dedupe_tiktok_shop_packages.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_packages',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_tiktok_shop_packages', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_packages',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_tiktok_shop_packages', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        merge_stage_tiktok_shop_packages_task = PythonOperator(
            task_id="merge_stage_tiktok_shop_packages",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/merge_stage_tiktok_shop_packages.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        
        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_packages',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_tiktok_shop_packages', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_packages',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_tiktok_shop_packages', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        run_audit_tiktok_shop_packages_task = PythonOperator(
            task_id="run_audit_tiktok_shop_packages",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_tiktok_shop_packages", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        (
            [list_s3_files_tiktok_shop_packages_task, list_s3_files_tiktok_shop_packages_task_goessor] >>
            check_new_files_found_tiktok_shop_packages_task >>
            [begin_insert_tiktok_shop_packages_task, skip_insert_tiktok_shop_packages_task]
        )

        (
            begin_insert_tiktok_shop_packages_task >> 
            s3_to_snowflake_tiktok_shop_packages_task >>
            s3_to_snowflake_tiktok_shop_packages_task_goessor >>
            insert_log_tiktok_shop_packages_task >>
            dedupe_tiktok_shop_packages_task >>
            
         ( run_dq_is_unique_dedupe_pk_hard_task, 
    
         run_dq_is_null_dedupe_pk_hard_task) >>
    
            merge_stage_tiktok_shop_packages_task >>
            
         ( run_dq_is_unique_merge_pk_hard_task, 
    
         run_dq_is_null_merge_pk_hard_task) >>
    
            run_audit_tiktok_shop_packages_task >>
            end_insert_tiktok_shop_packages_task >>
            end_tiktok_shop_packages_task
        )

        skip_insert_tiktok_shop_packages_task >> end_tiktok_shop_packages_task

    #  --- Load tiktok_shop_payments ---
    
    with TaskGroup(group_id='load_tiktok_shop_payments') as tg_tiktok_shop_payments:
        list_s3_files_tiktok_shop_payments_task = PythonOperator(
            task_id="list_s3_files_tiktok_shop_payments",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_shops_data/list_s3_tiktok_shop_payments.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        list_s3_files_tiktok_shop_payments_task_goessor = PythonOperator(
            task_id="list_s3_files_tiktok_shop_payments_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_shops_data/list_s3_tiktok_shop_payments_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_tiktok_shop_payments_task = DummyOperator(task_id="begin_insert_tiktok_shop_payments")
        skip_insert_tiktok_shop_payments_task = DummyOperator(task_id="skip_insert_tiktok_shop_payments")
        end_insert_tiktok_shop_payments_task = DummyOperator(task_id="end_insert_tiktok_shop_payments")
        end_tiktok_shop_payments_task = DummyOperator(
            task_id="end_tiktok_shop_payments",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_tiktok_shop_payments_task = BranchPythonOperator(
            task_id='check_new_files_found_tiktok_shop_payments',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_snowflake_tiktok_shop_payments.yaml",
                       "args_file_second": "tiktok_shops_data/s3_to_snowflake_tiktok_shop_payments_goessor.yaml",
                       "skip_task_id": "load_tiktok_shop_payments.skip_insert_tiktok_shop_payments",
                       "next_task_id": "load_tiktok_shop_payments.begin_insert_tiktok_shop_payments"
            },
        )

        s3_to_snowflake_tiktok_shop_payments_task = PythonOperator(
            task_id="s3_to_snowflake_tiktok_shop_payments",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_sf_raw_tiktok_shop_payments.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_tiktok_shop_payments_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_tiktok_shop_payments_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_sf_raw_tiktok_shop_payments_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_tiktok_shop_payments',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_tiktok_shop_payments', 
                    field_list=['id'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        insert_log_tiktok_shop_payments_task = PythonOperator(
            task_id="insert_log_tiktok_shop_payments",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/insert_log_tiktok_shop_payments.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_tiktok_shop_payments_task = PythonOperator(
            task_id="dedupe_tiktok_shop_payments",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/dedupe_tiktok_shop_payments.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_payments',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_tiktok_shop_payments', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_payments',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_tiktok_shop_payments', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        merge_stage_tiktok_shop_payments_task = PythonOperator(
            task_id="merge_stage_tiktok_shop_payments",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/merge_stage_tiktok_shop_payments.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        
        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_payments',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_tiktok_shop_payments', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_payments',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_tiktok_shop_payments', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        run_audit_tiktok_shop_payments_task = PythonOperator(
            task_id="run_audit_tiktok_shop_payments",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_tiktok_shop_payments", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        (
            [list_s3_files_tiktok_shop_payments_task, list_s3_files_tiktok_shop_payments_task_goessor] >>
            check_new_files_found_tiktok_shop_payments_task >>
            [begin_insert_tiktok_shop_payments_task, skip_insert_tiktok_shop_payments_task]
        )

        (
            begin_insert_tiktok_shop_payments_task >> 
            s3_to_snowflake_tiktok_shop_payments_task >>
            s3_to_snowflake_tiktok_shop_payments_task_goessor >>
         ( run_dq_is_null_raw_task) >>
    
            insert_log_tiktok_shop_payments_task >>
            dedupe_tiktok_shop_payments_task >>
            
         ( run_dq_is_unique_dedupe_pk_hard_task, 
    
         run_dq_is_null_dedupe_pk_hard_task) >>
    
            merge_stage_tiktok_shop_payments_task >>
            
         ( run_dq_is_unique_merge_pk_hard_task, 
    
         run_dq_is_null_merge_pk_hard_task) >>
    
            run_audit_tiktok_shop_payments_task >>
            end_insert_tiktok_shop_payments_task >>
            end_tiktok_shop_payments_task
        )

        skip_insert_tiktok_shop_payments_task >> end_tiktok_shop_payments_task

    #  --- Load tiktok_shop_products ---
    
    with TaskGroup(group_id='load_tiktok_shop_products') as tg_tiktok_shop_products:
        list_s3_files_tiktok_shop_products_task = PythonOperator(
            task_id="list_s3_files_tiktok_shop_products",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_shops_data/list_s3_tiktok_shop_products.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        list_s3_files_tiktok_shop_products_task_goessor = PythonOperator(
            task_id="list_s3_files_tiktok_shop_products_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_shops_data/list_s3_tiktok_shop_products_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_tiktok_shop_products_task = DummyOperator(task_id="begin_insert_tiktok_shop_products")
        skip_insert_tiktok_shop_products_task = DummyOperator(task_id="skip_insert_tiktok_shop_products")
        end_insert_tiktok_shop_products_task = DummyOperator(task_id="end_insert_tiktok_shop_products")
        end_tiktok_shop_products_task = DummyOperator(
            task_id="end_tiktok_shop_products",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_tiktok_shop_products_task = BranchPythonOperator(
            task_id='check_new_files_found_tiktok_shop_products',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_snowflake_tiktok_shop_products.yaml",
                       "args_file_second": "tiktok_shops_data/s3_to_snowflake_tiktok_shop_products_goessor.yaml",
                       "skip_task_id": "load_tiktok_shop_products.skip_insert_tiktok_shop_products",
                       "next_task_id": "load_tiktok_shop_products.begin_insert_tiktok_shop_products"
            },
        )

        s3_to_snowflake_tiktok_shop_products_task = PythonOperator(
            task_id="s3_to_snowflake_tiktok_shop_products",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_sf_raw_tiktok_shop_products.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_tiktok_shop_products_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_tiktok_shop_products_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_sf_raw_tiktok_shop_products_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_raw_task = PythonOperator(
            task_id="run_dq_is_null_raw",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$raw_db.raw_tiktok_shop_products',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$raw_db.raw_tiktok_shop_products', 
                    field_list=['id'],
                    hard_alert=False,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        insert_log_tiktok_shop_products_task = PythonOperator(
            task_id="insert_log_tiktok_shop_products",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/insert_log_tiktok_shop_products.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_tiktok_shop_products_task = PythonOperator(
            task_id="dedupe_tiktok_shop_products",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/dedupe_tiktok_shop_products.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_products',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_tiktok_shop_products', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_products',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_tiktok_shop_products', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        merge_stage_tiktok_shop_products_task = PythonOperator(
            task_id="merge_stage_tiktok_shop_products",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/merge_stage_tiktok_shop_products.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        
        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_products',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_tiktok_shop_products', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_products',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_tiktok_shop_products', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        run_audit_tiktok_shop_products_task = PythonOperator(
            task_id="run_audit_tiktok_shop_products",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_tiktok_shop_products", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        (
            [list_s3_files_tiktok_shop_products_task, list_s3_files_tiktok_shop_products_task_goessor] >>
            check_new_files_found_tiktok_shop_products_task >>
            [begin_insert_tiktok_shop_products_task, skip_insert_tiktok_shop_products_task]
        )

        (
            begin_insert_tiktok_shop_products_task >> 
            s3_to_snowflake_tiktok_shop_products_task >>
            s3_to_snowflake_tiktok_shop_products_task_goessor >>
         ( run_dq_is_null_raw_task) >>
    
            insert_log_tiktok_shop_products_task >>
            dedupe_tiktok_shop_products_task >>
            
         ( run_dq_is_unique_dedupe_pk_hard_task, 
    
         run_dq_is_null_dedupe_pk_hard_task) >>
    
            merge_stage_tiktok_shop_products_task >>
            
         ( run_dq_is_unique_merge_pk_hard_task, 
    
         run_dq_is_null_merge_pk_hard_task) >>
    
            run_audit_tiktok_shop_products_task >>
            end_insert_tiktok_shop_products_task >>
            end_tiktok_shop_products_task
        )

        skip_insert_tiktok_shop_products_task >> end_tiktok_shop_products_task

    #  --- Load tiktok_shop_withdrawl ---
    
    with TaskGroup(group_id='load_tiktok_shop_withdrawl') as tg_tiktok_shop_withdrawl:
        list_s3_files_tiktok_shop_withdrawl_task = PythonOperator(
            task_id="list_s3_files_tiktok_shop_withdrawl",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_shops_data/list_s3_tiktok_shop_withdrawl.yaml",
                "wf_params": WF_PARAMS_EXPR}, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        list_s3_files_tiktok_shop_withdrawl_task_goessor = PythonOperator(
            task_id="list_s3_files_tiktok_shop_withdrawl_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_shops_data/list_s3_tiktok_shop_withdrawl_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        # Need a begin dummy operator for branching
        begin_insert_tiktok_shop_withdrawl_task = DummyOperator(task_id="begin_insert_tiktok_shop_withdrawl")
        skip_insert_tiktok_shop_withdrawl_task = DummyOperator(task_id="skip_insert_tiktok_shop_withdrawl")
        end_insert_tiktok_shop_withdrawl_task = DummyOperator(task_id="end_insert_tiktok_shop_withdrawl")
        end_tiktok_shop_withdrawl_task = DummyOperator(
            task_id="end_tiktok_shop_withdrawl",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )
        
        check_new_files_found_tiktok_shop_withdrawl_task = BranchPythonOperator(
            task_id='check_new_files_found_tiktok_shop_withdrawl',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_snowflake_tiktok_shop_withdrawl.yaml",
                       "args_file_second": "tiktok_shops_data/s3_to_snowflake_tiktok_shop_withdrawl_goessor.yaml",
                       "skip_task_id": "load_tiktok_shop_withdrawl.skip_insert_tiktok_shop_withdrawl",
                       "next_task_id": "load_tiktok_shop_withdrawl.begin_insert_tiktok_shop_withdrawl"
            },
        )

        s3_to_snowflake_tiktok_shop_withdrawl_task = PythonOperator(
            task_id="s3_to_snowflake_tiktok_shop_withdrawl",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_sf_raw_tiktok_shop_withdrawl.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_tiktok_shop_withdrawl_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_tiktok_shop_withdrawl_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_sf_raw_tiktok_shop_withdrawl_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_tiktok_shop_withdrawl_task = PythonOperator(
            task_id="insert_log_tiktok_shop_withdrawl",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/insert_log_tiktok_shop_withdrawl.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_tiktok_shop_withdrawl_task = PythonOperator(
            task_id="dedupe_tiktok_shop_withdrawl",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/dedupe_tiktok_shop_withdrawl.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        run_dq_is_unique_dedupe_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_withdrawl',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_tiktok_shop_withdrawl', 
                    field_list=['id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_withdrawl',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_tiktok_shop_withdrawl', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_withdrawl',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_tiktok_shop_withdrawl', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        merge_stage_tiktok_shop_withdrawl_task = PythonOperator(
            task_id="merge_stage_tiktok_shop_withdrawl",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "tiktok_shops_data/merge_stage_tiktok_shop_withdrawl.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        
        run_dq_is_unique_merge_task = PythonOperator(
            task_id="run_dq_is_unique_merge",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_withdrawl',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_tiktok_shop_withdrawl', 
                    field_list=['id'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_withdrawl',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_tiktok_shop_withdrawl', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    
        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_withdrawl',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_tiktok_shop_withdrawl', 
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )
    

        run_audit_tiktok_shop_withdrawl_task = PythonOperator(
            task_id="run_audit_tiktok_shop_withdrawl",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_tiktok_shop_withdrawl", # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            }, 
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        

        (
            [list_s3_files_tiktok_shop_withdrawl_task, list_s3_files_tiktok_shop_withdrawl_task_goessor] >>
            check_new_files_found_tiktok_shop_withdrawl_task >>
            [begin_insert_tiktok_shop_withdrawl_task, skip_insert_tiktok_shop_withdrawl_task]
        )

        (
            begin_insert_tiktok_shop_withdrawl_task >> 
            s3_to_snowflake_tiktok_shop_withdrawl_task >>
            s3_to_snowflake_tiktok_shop_withdrawl_task_goessor >>
            insert_log_tiktok_shop_withdrawl_task >>
            dedupe_tiktok_shop_withdrawl_task >>
            
         ( run_dq_is_unique_dedupe_task, 
    
         run_dq_is_null_dedupe_pk_hard_task, 
    
         run_dq_is_unique_dedupe_pk_hard_task) >>
    
            merge_stage_tiktok_shop_withdrawl_task >>
            
         ( run_dq_is_unique_merge_task, 
    
         run_dq_is_null_merge_pk_hard_task, 
    
         run_dq_is_unique_merge_pk_hard_task) >>
    
            run_audit_tiktok_shop_withdrawl_task >>
            end_insert_tiktok_shop_withdrawl_task >>
            end_tiktok_shop_withdrawl_task
        )

        skip_insert_tiktok_shop_withdrawl_task >> end_tiktok_shop_withdrawl_task


    #  --- Load tiktok_shop_order_statement_transactions ---
    with TaskGroup(group_id='load_tiktok_shop_order_statement_transactions') as tg_tiktok_shop_order_statement_transactions:
        list_s3_files_tiktok_shop_order_statement_transactions_task = PythonOperator(
            task_id="list_s3_files_tiktok_shop_order_statement_transactions",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_shops_data/list_s3_tiktok_shop_order_statement_transactions.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        list_s3_files_tiktok_shop_order_statement_transactions_task_goessor = PythonOperator(
            task_id="list_s3_files_tiktok_shop_order_statement_transactions_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_shops_data/list_s3_tiktok_shop_order_statement_transactions_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert
        )

        # Need a begin dummy operator for branching
        begin_insert_tiktok_shop_order_statement_transactions_task = DummyOperator(task_id="begin_insert_tiktok_shop_order_statement_transactions")
        skip_insert_tiktok_shop_order_statement_transactions_task = DummyOperator(task_id="skip_insert_tiktok_shop_order_statement_transactions")
        end_insert_tiktok_shop_order_statement_transactions_task = DummyOperator(task_id="end_insert_tiktok_shop_order_statement_transactions")
        end_tiktok_shop_order_statement_transactions_task = DummyOperator(
            task_id="end_tiktok_shop_order_statement_transactions",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_tiktok_shop_order_statement_transactions_task = BranchPythonOperator(
            task_id='check_new_files_found_tiktok_shop_order_statement_transactions',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_snowflake_tiktok_shop_order_statement_transactions.yaml",
                       "args_file_second": "tiktok_shops_data/s3_to_snowflake_tiktok_shop_order_statement_transactions_goessor.yaml",
                       "skip_task_id": "load_tiktok_shop_order_statement_transactions.skip_insert_tiktok_shop_order_statement_transactions",
                       "next_task_id": "load_tiktok_shop_order_statement_transactions.begin_insert_tiktok_shop_order_statement_transactions"
                       },
        )

        s3_to_snowflake_tiktok_shop_order_statement_transactions_task = PythonOperator(
            task_id="s3_to_snowflake_tiktok_shop_order_statement_transactions",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_sf_raw_tiktok_shop_order_statement_transactions.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_tiktok_shop_order_statement_transactions_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_tiktok_shop_order_statement_transactions_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_sf_raw_tiktok_shop_order_statement_transactions_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_tiktok_shop_order_statement_transactions_task = PythonOperator(
            task_id="insert_log_tiktok_shop_order_statement_transactions",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "tiktok_shops_data/insert_log_tiktok_shop_order_statement_transactions.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_tiktok_shop_order_statement_transactions_task = PythonOperator(
            task_id="dedupe_tiktok_shop_order_statement_transactions",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "tiktok_shops_data/dedupe_tiktok_shop_order_statement_transactions.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_order_statement_transactions',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_tiktok_shop_order_statement_transactions',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_order_statement_transactions',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_tiktok_shop_order_statement_transactions',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_tiktok_shop_order_statement_transactions_task = PythonOperator(
            task_id="merge_stage_tiktok_shop_order_statement_transactions",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "tiktok_shops_data/merge_stage_tiktok_shop_order_statement_transactions.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_order_statement_transactions',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_tiktok_shop_order_statement_transactions',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_order_statement_transactions',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_tiktok_shop_order_statement_transactions',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_tiktok_shop_order_statement_transactions_task = PythonOperator(
            task_id="run_audit_tiktok_shop_order_statement_transactions",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_tiktok_shop_order_statement_transactions",  # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                [list_s3_files_tiktok_shop_order_statement_transactions_task, list_s3_files_tiktok_shop_order_statement_transactions_task_goessor] >>
                check_new_files_found_tiktok_shop_order_statement_transactions_task >>
                [begin_insert_tiktok_shop_order_statement_transactions_task, skip_insert_tiktok_shop_order_statement_transactions_task]
        )

        (
                begin_insert_tiktok_shop_order_statement_transactions_task >>
                s3_to_snowflake_tiktok_shop_order_statement_transactions_task >>
                s3_to_snowflake_tiktok_shop_order_statement_transactions_task_goessor >>
                insert_log_tiktok_shop_order_statement_transactions_task >>
                dedupe_tiktok_shop_order_statement_transactions_task >>

                (run_dq_is_unique_dedupe_pk_hard_task,

                 run_dq_is_null_dedupe_pk_hard_task) >>

                merge_stage_tiktok_shop_order_statement_transactions_task >>

                (run_dq_is_unique_merge_pk_hard_task,

                 run_dq_is_null_merge_pk_hard_task) >>

                run_audit_tiktok_shop_order_statement_transactions_task >>
                end_insert_tiktok_shop_order_statement_transactions_task >>
                end_tiktok_shop_order_statement_transactions_task
        )

        skip_insert_tiktok_shop_order_statement_transactions_task >> end_tiktok_shop_order_statement_transactions_task


    #  --- Load tiktok_shop_returns ---
    with TaskGroup(group_id='load_tiktok_shop_returns') as tg_tiktok_shop_returns:
        list_s3_files_tiktok_shop_returns_task = PythonOperator(
            task_id="list_s3_files_tiktok_shop_returns",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_shops_data/list_s3_tiktok_shop_returns.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        list_s3_files_tiktok_shop_returns_task_goessor = PythonOperator(
            task_id="list_s3_files_tiktok_shop_returns_goessor",
            python_callable=tls3.list_s3_modified_files,
            op_kwargs={
                "args_file": "tiktok_shops_data/list_s3_tiktok_shop_returns_goessor.yaml",
                "wf_params": WF_PARAMS_EXPR},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,

        )

        # Need a begin dummy operator for branching
        begin_insert_tiktok_shop_returns_task = DummyOperator(task_id="begin_insert_tiktok_shop_returns")
        skip_insert_tiktok_shop_returns_task = DummyOperator(task_id="skip_insert_tiktok_shop_returns")
        end_insert_tiktok_shop_returns_task = DummyOperator(task_id="end_insert_tiktok_shop_returns")
        end_tiktok_shop_returns_task = DummyOperator(
            task_id="end_tiktok_shop_returns",
            trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS,
        )

        check_new_files_found_tiktok_shop_returns_task = BranchPythonOperator(
            task_id='check_new_files_found_tiktok_shop_returns',
            python_callable=check_for_new_files,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_snowflake_tiktok_shop_returns.yaml",
                       "args_file_second": "tiktok_shops_data/s3_to_snowflake_tiktok_shop_returns_goessor.yaml",
                       "skip_task_id": "load_tiktok_shop_returns.skip_insert_tiktok_shop_returns",
                       "next_task_id": "load_tiktok_shop_returns.begin_insert_tiktok_shop_returns"
                       },
        )

        s3_to_snowflake_tiktok_shop_returns_task = PythonOperator(
            task_id="s3_to_snowflake_tiktok_shop_returns",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_sf_raw_tiktok_shop_returns.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        s3_to_snowflake_tiktok_shop_returns_task_goessor = PythonOperator(
            task_id="s3_to_snowflake_tiktok_shop_returns_goessor",
            python_callable=load_obj.s3_to_snowflake_load,
            op_kwargs={"args_file": "tiktok_shops_data/s3_to_sf_raw_tiktok_shop_returns_goessor.yaml"},
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        insert_log_tiktok_shop_returns_task = PythonOperator(
            task_id="insert_log_tiktok_shop_returns",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "tiktok_shops_data/insert_log_tiktok_shop_returns.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        dedupe_tiktok_shop_returns_task = PythonOperator(
            task_id="dedupe_tiktok_shop_returns",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "tiktok_shops_data/dedupe_tiktok_shop_returns.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_returns',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.dedupe_tiktok_shop_returns',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_dedupe_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_dedupe_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.dedupe_tiktok_shop_returns',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.dedupe_tiktok_shop_returns',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        merge_stage_tiktok_shop_returns_task = PythonOperator(
            task_id="merge_stage_tiktok_shop_returns",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection": "Snowflake",
                       "sql_file": "tiktok_shops_data/merge_stage_tiktok_shop_returns.sql",
                       "wf_params": WF_PARAMS_EXPR
                       },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_unique_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_unique_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_returns',
                'test_name': 'is_unique',
                'sql_query': tldq.gen_check_unique_key(
                    tb_name='$stage_db.merge_tiktok_shop_returns',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_dq_is_null_merge_pk_hard_task = PythonOperator(
            task_id="run_dq_is_null_merge_pk_hard",
            python_callable=tldq.run_dq_string,
            op_kwargs={
                'wk_name': DAG_ID,
                'tb_name': '$stage_db.merge_tiktok_shop_returns',
                'test_name': 'is_null',
                'sql_query': tldq.gen_check_if_nulls(
                    tb_name='$stage_db.merge_tiktok_shop_returns',
                    field_list=['pk'],
                    hard_alert=True,
                )
            },
            on_failure_callback=alerts.send_failure_alert,
        )

        run_audit_tiktok_shop_returns_task = PythonOperator(
            task_id="run_audit_tiktok_shop_returns",
            python_callable=tla.run_audit,
            op_kwargs={
                "table_name": "$stage_db.merge_tiktok_shop_returns",  # Replace with final table (usually FACT table)
                "wf_params": WF_PARAMS_EXPR,
                "ts_created_field": 'record_created_timestamp_utc',
                "ts_updated_field": 'record_updated_timestamp_utc'
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        (
                [list_s3_files_tiktok_shop_returns_task, list_s3_files_tiktok_shop_returns_task_goessor] >>
                check_new_files_found_tiktok_shop_returns_task >>
                [begin_insert_tiktok_shop_returns_task, skip_insert_tiktok_shop_returns_task]
        )

        (
                begin_insert_tiktok_shop_returns_task >>
                s3_to_snowflake_tiktok_shop_returns_task >>
                s3_to_snowflake_tiktok_shop_returns_task_goessor >>
                insert_log_tiktok_shop_returns_task >>
                dedupe_tiktok_shop_returns_task >>

                (run_dq_is_unique_dedupe_pk_hard_task,

                 run_dq_is_null_dedupe_pk_hard_task) >>

                merge_stage_tiktok_shop_returns_task >>

                (run_dq_is_unique_merge_pk_hard_task,

                 run_dq_is_null_merge_pk_hard_task) >>

                run_audit_tiktok_shop_returns_task >>
                end_insert_tiktok_shop_returns_task >>
                end_tiktok_shop_returns_task
        )

        skip_insert_tiktok_shop_returns_task >> end_tiktok_shop_returns_task


    tiktok_shop_data_load_completed = DummyOperator(task_id="tiktok_shop_data_load_completed")

    fact_tiktok_orders_task = PythonOperator(
        task_id="merge_fact_tiktok_orders",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "tiktok_shops_data/fact_tiktok_orders.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )

    fact_tiktok_returns_task = PythonOperator(
        task_id="merge_fact_tiktok_returns",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "tiktok_shops_data/fact_tiktok_returns.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
    )


    # ---- Main branch ----
    chain(
        begin,
        get_workflow_parameters,
        [
            tg_tiktok_shop_orders,
            tg_tiktok_shop_packages,
            tg_tiktok_shop_payments,
            tg_tiktok_shop_products,
            tg_tiktok_shop_withdrawl,
            tg_tiktok_shop_order_statement_transactions,
            tg_tiktok_shop_returns
        ],
        tiktok_shop_data_load_completed,
        [
            fact_tiktok_orders_task,
            fact_tiktok_returns_task
        ],
        update_workflow_parameters,
        end
    )
    
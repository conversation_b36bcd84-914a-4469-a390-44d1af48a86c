""" Data transaformation code generator for Brand EBIDTDA framework """

import logging
from datetime import datetime
from airflow import DAG

from airflow.models import Variable
from airflow.models.baseoperator import chain
# noinspection PyDeprecation
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import BranchPythonOperator, PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.utils.trigger_rule import TriggerRule

import tasklib.audit as tla
import tasklib.dq as tldq
import tasklib.glue as tlg
import tasklib.s3 as tls3
import tasklib.sql as tlsql
import tasklib.workflow as tlw
from tasklib import alerts
from tasklib.loader import check_for_new_files, S3ToSnowflake


BUILD_NUM = '1'
DAG_ID = 'brand_ebitda_shipment_revenue_process'
DAG_SCHEDULE = Variable.get('dag_schedules', deserialize_json=True).get(DAG_ID, '@daily')
RELEASE_DEF = '1'
WF_PARAMS_EXPR = "{}"
log = logging.getLogger(__name__)

with DAG(
    dag_id=DAG_ID,
    description=f'Release:{RELEASE_DEF}-Build:{BUILD_NUM}',
    start_date=datetime(2024, 1, 1),
    schedule_interval='0 5 * * *',
    catchup=False,
    max_active_runs=1,
    params={'workflow_name': 'BRAND_EBITDA_SKU',
            'author': 'akshay'},
    tags=['akshay', 'Raptor']
) as dag:


    begin = DummyOperator(task_id="begin")
    end = DummyOperator(task_id="end")

    #  --- process brand edbidta metrics for _report ---
    
    with TaskGroup(group_id='brand_ebitda_order_level') as tg_brand_ebitda_shipment_revenue_order_level:
        
        brand_ebitda_order_non_order_revenue_fba_all_components = PythonOperator(
            task_id="brand_ebitda_order_non_order_revenue_fba_all_components",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_sku_level_order_non_order_revenue_fba_all_components.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        brand_ebitda_shipment_level_aggregate = PythonOperator(
            task_id="brand_ebitda_shipment_level_aggregate",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_shipment_level_aggregate.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        brand_ebitda_drr_order_level_aggregate = PythonOperator(
            task_id="brand_ebitda_drr_order_level_aggregate",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_drr_order_level_aggregate.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        brand_ebitda_deffered_order_level_aggregate = PythonOperator(
            task_id="brand_ebitda_deffered_order_level_aggregate",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_deffered_order_level_aggregate.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        brand_ebitda_drr_deffered_shipment_consolidate = PythonOperator(
            task_id="brand_ebitda_drr_deffered_shipment_consolidate",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_drr_deffered_shipment_consolidated.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        
        (   brand_ebitda_order_non_order_revenue_fba_all_components
            >> brand_ebitda_shipment_level_aggregate
            >> brand_ebitda_drr_order_level_aggregate
            >> brand_ebitda_deffered_order_level_aggregate
            >> brand_ebitda_drr_deffered_shipment_consolidate
        )

    with TaskGroup(group_id='brand_ebitda_non_order_level') as tg_brand_ebitda_shipment_revenue_drr_non_order_level:
        brand_ebitda_drr_non_order_level_aggregate = PythonOperator(
            task_id="brand_ebitda_drr_non_order_level_aggregate",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_non_order_level_drr_aggregate_bi_map.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        brand_ebitda_non_order_level_sku_specific_bi_map = PythonOperator(
            task_id="brand_ebitda_non_order_level_sku_specific_bi_map",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_non_order_sku_specified_bi_mapping.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        brand_ebitda_non_order_level_account_level_aggregate = PythonOperator(
            task_id="brand_ebitda_non_order_level_account_level_aggregate",
            python_callable=tlsql.run_query_file,
            op_kwargs={"connection":"Snowflake",
                       "sql_file": "brand_ebitda_framework/brandebitda_shipment_revenue/branebitda_non_order_account_specified_bi_mapping.sql",
                       "wf_params": WF_PARAMS_EXPR
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )
        
        (
            brand_ebitda_drr_non_order_level_aggregate
            >> brand_ebitda_non_order_level_sku_specific_bi_map
            >> brand_ebitda_non_order_level_account_level_aggregate
        )

    with TaskGroup(group_id='brand_ebitda_sku_enrichment') as tg_brand_ebitda_sku_enrichment:

        brandebitda_shipment_revenue_contribution_sku_level = PythonOperator(
            task_id="brandebitda_shipment_revenue_contribution_sku_level",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": (
                    "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_shipment_revenue_contribution_sku_level.sql"
                ),
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        brandebitda_non_order_level_account_to_sku_conversion = PythonOperator(
            task_id="brandebitda_non_order_level_account_to_sku_conversion",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": (
                    "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_non_order_level_account_to_sku_conversion.sql"
                ),
                "wf_params": WF_PARAMS_EXPR,
            },
            provide_context=True,
            on_failure_callback=alerts.send_failure_alert,
        )

        brandebitda_non_order_sku_level_consolidated_bi_mapping = PythonOperator(
            task_id="brandebitda_non_order_sku_level_consolidated_bi_mapping",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": (
                    "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_non_order_level_sku_consolidated_bi_mapping.sql"
                ),
                "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        )
        
        brandebitda_sku_level_complete_aggregation_bi_mapping = PythonOperator(
            task_id="brandebitda_sku_level_complete_aggregation_bi_mapping",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": (
                    "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_sku_level_complete_aggregation_bi_mapping.sql"
                ),
                "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        )
        
        brandebitda_sku_level_complete_aggregation_wbr_subtotal = PythonOperator(
            task_id="brandebitda_sku_level_complete_aggregation_wbr_subtotal",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": (
                    "brand_ebitda_framework/brandebitda_shipment_revenue/branedbitda_sku_level_complete_aggregation_wbr_sub_total.sql"
                ),
                "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        )
        
        brandebitda_sku_level_complete_aggregation_wbr_totals = PythonOperator(
            task_id="brandebitda_sku_level_complete_aggregation_wbr_totals",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": (
                    "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_sku_level_complete_aggregation_wbr_total.sql"
                ),
                "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        )
        
        brandebitda_asin_level_complete_aggregation_wbr_totals = PythonOperator(
            task_id="brandebitda_asin_level_complete_aggregation_wbr_totals",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": (
                    "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_asin_level_complete_aggregation_wbr_total.sql"
                ),
                "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        )
        
        brandebitda_sku_level_revenue_fba_all_components = PythonOperator(
            task_id="brandebitda_sku_level_revenue_fba_all_components",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": (
                    "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_sku_level_revenue_fba_all_components.sql"
                ),
                "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        )
        
        brandebitda_asin_level_revenue_fba_all_components = PythonOperator(
            task_id="brandebitda_asin_level_revenue_fba_all_components",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": (
                    "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_asin_level_revenue_fba_all_components.sql"
                ),
                "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        )
        
        brandebitda_sku_level_complete_aggregation = PythonOperator(
            task_id="brandebitda_sku_level_complete_aggregation",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": (
                    "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_sku_level_complete_aggregation.sql"
                ),
                "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        )
        
        brandebitda_asin_level_complete_aggregation = PythonOperator(
            task_id="brandebitda_asin_level_complete_aggregation",
            python_callable=tlsql.run_query_file,
            op_kwargs={
                "connection": "Snowflake",
                "sql_file": (
                    "brand_ebitda_framework/brandebitda_shipment_revenue/brandebitda_asin_level_complete_aggregation.sql"
                ),
                "wf_params": WF_PARAMS_EXPR,
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        )
        
       
        (
            brandebitda_shipment_revenue_contribution_sku_level
            >> brandebitda_non_order_level_account_to_sku_conversion
            >> brandebitda_non_order_sku_level_consolidated_bi_mapping
            >> brandebitda_sku_level_complete_aggregation_bi_mapping
            >> brandebitda_sku_level_complete_aggregation_wbr_subtotal
            >> brandebitda_sku_level_complete_aggregation_wbr_totals
            >> brandebitda_asin_level_complete_aggregation_wbr_totals
            >> brandebitda_sku_level_revenue_fba_all_components
            >> brandebitda_asin_level_revenue_fba_all_components
            >> brandebitda_sku_level_complete_aggregation
            >> brandebitda_asin_level_complete_aggregation
        )


    # --- Main DAG chain ---
    chain(
        begin,
        tg_brand_ebitda_shipment_revenue_order_level,
        tg_brand_ebitda_shipment_revenue_drr_non_order_level,
        tg_brand_ebitda_sku_enrichment, # This TaskGroup includes the processing and DQ tasks
        end
    )

import json
import logging
import os
from airflow import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.amazon.aws.transfers.s3_to_sftp import S3ToSFTPOperator
from airflow.sensors.external_task import ExternalTaskSensor
from datetime import datetime, timedelta

from tasklib.alerts import send_failure_alert
from tasklib.config import get_s3_staging_bucket
from tasklib.s3 import write_snowflake_data_to_s3
from tasklib.sql import run_query_file
import tasklib.workflow as tlw

log = logging.getLogger(__name__) 

_WF_PARAMS_EXPR="{{ ti.xcom_pull(task_ids='task_get_workflow_parameters') }}"
_S3_BUCKET = get_s3_staging_bucket()
_S3_KEY = "data_exchange/outbound/postie/heyday_ris_orders_{{ dag_run.get_task_instance('write_data_to_s3').start_date.timestamp()|int }}.csv"
_DELIM = ','

with DAG(
    dag_id='postie_ris_data_feed_daily',
    start_date=datetime(2022, 11, 18),
    schedule_interval='30 12 * * *',
    catchup=False,
    max_active_runs=1,
    params={
        'workflow_name': 'POSTIE_RIS_DATA_FEED',
        'author': 'srichand'
    },
    tags=['RETIRED','data feed'],
) as dag:

    begin = DummyOperator(task_id='begin')

    get_workflow_params=PythonOperator(
        task_id="task_get_workflow_parameters",
        python_callable=tlw.get_workflow_params,
        provide_context=True,
        on_failure_callback=send_failure_alert,
        op_kwargs={"workflow_name": "{{ params.get('workflow_name') }}" }
    )

    wait_on_amazon_orders_task = ExternalTaskSensor(
        task_id='wait_on_amazon_orders',
        external_dag_id='amazon_orders',
        external_task_id='end',
        allowed_states=['success'],
        timeout=60 * 60 * 4,
        execution_delta=timedelta(minutes=30)
    )

    wait_on_shopify_orders_task = ExternalTaskSensor(
        task_id='wait_on_shopify_orders',
        external_dag_id='shopify_orders_hourly',
        external_task_id='end',
        allowed_states=['success'],
        timeout=60 * 60 * 4,
        execution_delta=timedelta(minutes=30)
    )

    write_snowflake_data_to_s3_task = PythonOperator(
        task_id='write_data_to_s3',
        python_callable=write_snowflake_data_to_s3,
        provide_context=True,
        op_kwargs={
            'sql_file': 'data_feeds/postie/ris_orders_daily.sql',
            'wf_params': _WF_PARAMS_EXPR,
            's3_bucket': _S3_BUCKET,
            's3_key': _S3_KEY,
            'delim': _DELIM,
        },
        on_failure_callback=send_failure_alert,
    )

    send_data_s3_to_sftp_task = S3ToSFTPOperator(
        task_id='send_data_s3_to_sftp',
        sftp_conn_id='postie_sftp_conn_id',
        sftp_path=os.path.basename(_S3_KEY),
        s3_bucket=_S3_BUCKET,
        s3_key=_S3_KEY,
        on_failure_callback=send_failure_alert,
    )

    update_workflow_params=PythonOperator(
        task_id="task_update_workflow_parameters",
        python_callable=tlw.update_workflow_params,
        op_kwargs={"wf_params": _WF_PARAMS_EXPR},
        provide_context=True,
        on_failure_callback=send_failure_alert
    )

    end = DummyOperator(task_id='end')

    (
        begin >>
        get_workflow_params >>
        [wait_on_amazon_orders_task, wait_on_shopify_orders_task] >>
        write_snowflake_data_to_s3_task >>
        send_data_s3_to_sftp_task >>
        update_workflow_params >>
        end
    )

import logging
import tasklib.sql as tlsql

from airflow import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.http.operators.http import SimpleHttpOperator
from tasklib import alerts
from datetime import datetime

log = logging.getLogger(__name__)
API_ENDPOINT_CONN_ID = 'dynamic_pricing_api_endpoint'
DAG_ID = 'amazon_list_price_automation'
WF_PARAMS_EXPR = "{'wf_name': '" + DAG_ID.upper() + "'}"

with DAG(
    dag_id="amazon_list_price_automation",
    start_date=datetime(2024, 11, 10),
    schedule_interval="0 9 * * *",
    catchup=False,
    max_active_runs=1,
    params={"workflow_name": "AMAZON_LIST_PRICE_AUTOMATION", "author": "nagesh"},
    tags=["Nagesh"],
) as dag:
    begin = DummyOperator(task_id="begin")

    create_candidate_asin_price_data_set = PythonOperator(
        task_id="create_candidate_asin_price_data_set",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "pricing/amazon/list_price_automation/create_list_price_candidates.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    insert_candidate_asin_price_data_log = PythonOperator(
        task_id="insert_candidate_asin_price_data_log",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "pricing/amazon/list_price_automation/insert_candidate_asins_log.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    create_candidate_asin_data_set = PythonOperator(
        task_id="create_candidate_asin_data_set",
        python_callable=tlsql.run_query_file,
        op_kwargs={
            "connection": "Snowflake",
            "sql_file": "pricing/amazon/list_price_automation/candidate_asin_listing_data.sql",
            "wf_params": WF_PARAMS_EXPR
        },
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert
    )

    import tasklib.replication as tlr

    replicate_candidate_asin_price_data_set_sf_to_pg_task = PythonOperator(
        task_id="replicate_candidate_asin_price_data_set_sf_to_pg_task",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/list_price_automation_candidates.yaml"}
    )

    replicate_candidate_asin_listing_data_set_sf_to_pg_task = PythonOperator(
        task_id="replicate_candidate_asin_listing_data_set_sf_to_pg_task",
        python_callable=tlr.replicate_data,
        provide_context=True,
        on_failure_callback=alerts.send_failure_alert,
        op_kwargs={"args_file": "data_replication/sf_to_pg/list_price_automation_candidates_data_set.yaml"}
    )

    invoke_pricing_list_price_api = SimpleHttpOperator(
        task_id="invoke_pricing_list_price_api",
        http_conn_id=API_ENDPOINT_CONN_ID,
        endpoint="/asin-list-price/update?snapshot_date={{ ds }}",
        method="POST",
        data={},
        headers={
            "Content-Type": "application/json",
        },
        response_filter=lambda response: response.json(),
        log_response=True,
    )

    end = DummyOperator(task_id="end")

    (begin >> create_candidate_asin_price_data_set >> insert_candidate_asin_price_data_log >> create_candidate_asin_data_set >>
     replicate_candidate_asin_price_data_set_sf_to_pg_task >> replicate_candidate_asin_listing_data_set_sf_to_pg_task >> invoke_pricing_list_price_api >> end)

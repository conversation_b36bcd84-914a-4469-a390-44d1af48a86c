from datetime import datetime, timedelta
from helpers.helpers import *
from db_connectors.sf_connector import <PERSON><PERSON><PERSON>
from db_connectors.pg_connector import Postgres
from airflow.operators.python import PythonOperator
from airflow.providers.slack.operators.slack_webhook import SlackWebhookOperator
from airflow.sensors.external_task import ExternalTaskSensor
from airflow.operators.dummy_operator import DummyOperator
from airflow import DAG
import logging
import json

log = logging.getLogger(__name__)

sfc = Snowflake()
pgc = Postgres()

TRANSFORMATION_SCRIPTS = 'data_transformations'
SLACK_CONN_ID = 'slack'

parameters = {
    'transform_job_id': '${transform_job_id}',
    'dependency_dags': ${dependency_dags},
    'dag_id': '${dag_name}',
    'description': '${dag_description}',
    'run_schedule': '${dag_schedule_cron}',
    'tags': ${dag_tags},
    'transform_sql_file_name': '${transform_sql_file_name}',
    'table_schema': '${table_schema}',
    'table_name': '${table_name}'
}


def send_failure_alert(context):
    """
    This is a call back function that will be called when a dag fails. The first step is setting up the job run status
    in workflow_configurations.etl_job_runs table and then alerting the DE team of a failure via slack
    :param context:
    :return:
    """
    # update_current_etl_run_status('ERROR', **context)
    log.info('Sending alert')
    slack_msg = """
            :red_circle: Task Failed while executing dag *{dag}*.
            *Task*: {task}
            *Dag*: {dag}
            *Execution Time*: {exec_date}
            *Log Url*: <{log_url}|Click Here>
            """.format(
        task=context.get('task_instance').task_id,
        dag=context.get('task_instance').dag_id,
        ti=context.get('task_instance'),
        exec_date=context.get('execution_date'),
        log_url=context.get('task_instance').log_url,
    )
    failed_alert = SlackWebhookOperator(
        slack_webhook_conn_id='slack',
        task_id='dag_failure_notification',
        message=slack_msg,
        username='airflow')
    failed_alert.execute(context=context)


def send_audit_test_failure_alert(failed_tests, **context):
    """
    This function will be called if there are audit test failures. Slack alert will be sent
    """
    ti = context['ti']
    etl_job_run_id = ti.xcom_pull(key='etl_job_run_id', task_ids=['initialize_etl_run'])[0]
    etl_job_run_time = ti.xcom_pull(key='etl_batch_run_time', task_ids=['initialize_etl_run'])[0]
    transform_job_id = parameters.get('transform_job_id')

    slack_msg = """
    :flashlight: Audit tests failed for the transformation *{dag}* at time *{etl_batch_run_time}*.
    *Failed audit tests*: {failed_tests}
    *etl_job_run_id*: {etl_job_run_id}
    *transform_job_id*: {transform_job_id}

    """.format(dag=context.get('task_instance').dag_id,
               etl_batch_run_time=etl_job_run_time,
               etl_job_run_id=etl_job_run_id,
               failed_tests=failed_tests,
               transform_job_id=transform_job_id
               )
    failed_audit_alert = SlackWebhookOperator(
        task_id='failed_audit_notification',
        slack_webhook_conn_id='slack',
        message=slack_msg,
        username='airflow'
    )
    failed_audit_alert.execute(context=context)


def set_dag_run(**context):
    """
    Function will call generate_etl_job_run_id and generate_etl_batch_run_time and will initialize a new job run
    by calling set_new_etl_run.sql script. Job run will set to RUNNING and job_type will be set to INGESTION. Newly
    generated etl_job_run_id and etl_batch_run_time will be added to task_instance by utilizing xcom_push so that these
    values can be used by other functions in the script
    :param context:
    :return:
    """

    # Generate a new etl run. This is a stringified value of uuid4()
    etl_job_run_id = generate_etl_job_run_id()
    # Generate a new etl_batch_run_time. This is a UTC datetime value that is precisioned at the hour level
    etl_batch_run_time = generate_etl_batch_run_time()
    # This is the id that corresponds to report_id value from api_report_info_table
    job_id = parameters.get('transform_job_id')
    log.info(f'New elt_job_run_id is {etl_job_run_id} and etl_batch_run_time is {etl_batch_run_time}')
    # get the query that will be executed to insert new row into etl_job_runs table. Query is located in sql_scripts
    # module.
    query = f"""call workflow_configurations.sp_set_etl_job_run('{etl_job_run_id}', '{job_id}','TRANSFORMATION','RUNNING','{etl_batch_run_time}') """

    log.info("Initializing run and setting metdata in backend")
    # execute the query to insert data into table. This is by using Postgres Hook
    pgc.execute_statement(query)

    # add run id and batch run time to airflow context. etl_job_run_id and etl_batch_run_time will be used by succeeding
    # tasks in the dag
    ti = context['ti']
    ti.xcom_push(key='etl_job_run_id', value=etl_job_run_id)
    ti.xcom_push(key='etl_batch_run_time', value=etl_batch_run_time)


def transform_data(**context):
    """
    This function will submit a glue job based on the properties for the transformations to run. Once the job is submitted,
    this process will also poke the glue job run to get the status until either its stopped/ failed/ success and will
    raise error or exit based on the job run status.
    Glue job will run sql transformations
    :param context:
    :return:
    """
    ti = context['ti']
    dwh.stage
    db_info = parameters.get('table_schema').split('.')
    db = db_info[0]
    schema = db_info[1]
    table_name = parameters.get('table_name')

    etl_batch_run_time = ti.xcom_pull(key='etl_batch_run_time', task_ids=['initialize_etl_run'])[0]
    etl_job_run_id = ti.xcom_pull(key='etl_job_run_id', task_ids=['initialize_etl_run'])[0]

    params = {'db': db,
                'schema': schema,
                'table_name': table_name,
                'etl_batch_run_time': etl_batch_run_time
              }

    # handling execution of multiple statement scripts and by defalut, the statement terminator
    # is a semi-colon (;)
    query = multi_replace(TRANSFORMATION_SCRIPTS, parameters.get('transform_sql_file_name'), params)
    query = query.split(';')[:-1:] if ';' in query else query.split(';')

    sfc.execute_multi_statements(query)

    # get the new rows inserted and rows updated for the current run
    new_rows = f"""
    SELECT COUNT(1) AS "row_count"
    FROM "{db}"."{schema}"."{table_name}"
    WHERE "dw_insert_time" = '{etl_batch_run_time}'
    """

    update_rows = f"""
    SELECT COUNT(1) AS "row_count"
    FROM "{db}"."{schema}"."{table_name}"
    WHERE "dw_last_update_time" = '{etl_batch_run_time}'
    AND "dw_insert_time" <> '{etl_batch_run_time}'
    """

    # Get relevant data from by executing above queries
    insert_rows, _ = sfc.get_data(new_rows, as_dict=True)
    update_rows, _ = sfc.get_data(update_rows, as_dict=True)

    # assign values to variables appropriately
    if insert_rows:
        insert_row_count = insert_rows[0].get('row_count')
    else:
        insert_row_count = 0
    if update_rows:
        update_row_count = update_rows[0].get('row_count')
    else:
        update_row_count = 0

    # updated etl_job_runs rows_inserted and rows_updated columns
    update_job_runs = f"""UPDATE workflow_configurations.etl_job_runs
                        SET rows_inserted = {insert_row_count}
                            ,rows_updated = {update_row_count}
                        WHERE etl_job_run_id = '{etl_job_run_id}'
        """
    pgc.execute_statement(update_job_runs)


def run_audit_tests(**context):
    """
    This function will execute audit tests for the current transformation and alerts via slack if there
    are any audit test failures
    :param context:
    :return:
    """
    ti = context['ti']
    etl_job_run_id = ti.xcom_pull(key='etl_job_run_id', task_ids=['initialize_etl_run'])[0]
    results = dict()
    failed_tests = []
    transform_job_id = parameters.get('transform_job_id')
    # Get all the audit test to run for this transformation
    query = f"""SELECT * FROM workflow_configurations.udf_get_audit_tests_for_transform_job('{transform_job_id}')"""
    data, _ = pgc.get_data(query, as_dict=True)
    # If there are audit tests, execute each audit test and store the results in results dictionary
    # The format will be {'audit_test_name': (0,1,2)}
    if data:
        for audit_test in data:
            audit_test_name = audit_test.get('audit_test_name')
            query_to_execute = audit_test.get('query_to_execute')
            audit_result, _ = sfc.get_data(query_to_execute, as_dict=True)
            results[audit_test_name] = audit_result[0].get('result')
            # if the output of query is > 0 then add it to failed_tests list
            if audit_result[0].get('result') > 0:
                failed_tests.append(audit_test_name)
        # convert the results dict to string to store it as json object in the backend
        res_str = json.dumps(results)
        # get what the max value for the audit test results is.
        max_res = max(results.values())
        pass_results = False
        if max_res == 0:
            pass_results = True

        query = f"""call workflow_configurations.sp_set_audit_test_results_for_transformation('{etl_job_run_id}',
                                                                                                '{transform_job_id}',
                                                                                                '{res_str}',
                                                                                                {pass_results})"""
        pgc.execute_statement(query)
        # Check if either audit fail alert or dag failure should happen
        # if max_res is 1 then its a soft failure
        # if its 2 then its a hard failure
        try:
            if failed_tests and max_res == 1:
                send_audit_test_failure_alert(failed_tests, **context)
            if failed_tests and max_res == 2:
                send_audit_test_failure_alert(failed_tests, **context)
                raise Exception('Audit tests failed with hard failure alert. Failing the task')
        except Exception as err:
            msg = traceback_fmt(err)
            log.error(msg)
            raise


def update_current_etl_run_status(status='SUCCESS', **context):
    """
    This function will update the current etl_run's job run status. By default, the status is SUCCESS. But, other
    statuses can be passed in as parameter
    """
    ti = context['ti']
    etl_job_run_id = ti.xcom_pull(key='etl_job_run_id', task_ids=['initialize_etl_run'])[0]

    query = f"""UPDATE workflow_configurations.etl_job_runs
            SET job_run_status = '{status}'
                ,job_end_time_utc = CURRENT_TIMESTAMP
            WHERE etl_job_run_id = '{etl_job_run_id}'
        """
    pgc.execute_statement(query)


# DAG definition starts from here
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email': ['<EMAIL>'],
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 0,
    'retry_delay': timedelta(minutes=5),
    'on_failure_callback': send_failure_alert
}

with DAG(
    dag_id=parameters.get('dag_id'),
    default_args=default_args,
    schedule_interval=parameters.get('run_schedule'),
    start_date=datetime(2020, 8, 1, 0, 0),
    catchup=False,
    tags=parameters.get('tags'),
) as dag:

    init_etl = PythonOperator(
        task_id='initialize_etl_run',
        python_callable=set_dag_run,
        provide_context=True,
        on_failure_callback=send_failure_alert,
    )

    upd_etl = PythonOperator(
        task_id='set_current_run_to_success',
        python_callable=update_current_etl_run_status,
        op_kwargs={'status': 'SUCCESS'},
        on_failure_callback=send_failure_alert,
    )

    transform_data = PythonOperator(
        task_id='transform_data',
        python_callable=transform_data,
        provide_context=True,
        on_failure_callback=send_failure_alert,
    )

    run_audit_tests = PythonOperator(
        task_id='run_audit_tests',
        python_callable=run_audit_tests,
        provide_context=True,
        on_failure_callback=send_failure_alert,
    )

    dep_chk_start = DummyOperator(
        task_id='start_dag',
    )

    dep_chk_end =DummyOperator(
        task_id='dependencies_met',
    )

    for dag_dep in parameters.get('dependency_dags'):
        dep_chk = ExternalTaskSensor(
            task_id=f'wait_for_{dag_dep}_completeness',
            external_dag_id=dag_dep,
            external_task_id='set_current_run_to_success',
            check_existence=True,
            on_failure_callback=send_failure_alert,

        )

        init_etl >> dep_chk_start >> dep_chk >> dep_chk_end >> transform_data >> run_audit_tests >> upd_etl
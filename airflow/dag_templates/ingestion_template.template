from datetime import datetime, timedelta
from helpers.helpers import *
from db_connectors.sf_connector import <PERSON><PERSON><PERSON>
from db_connectors.pg_connector import Postg<PERSON>
from airflow.operators.python import PythonOperator
from airflow.providers.slack.operators.slack_webhook import SlackWebhookOperator
from airflow import DAG
import boto3
import time
import logging

log = logging.getLogger(__name__)


sfc = Snowflake()
pgc = Postgres()

SLACK_CONN_ID = 'slack'


parameters = {
    'report_id': '${report_id}',
    'channel': '${channel}',

    'glue_job_name': '${ingestion_glue_job}',
    'stage_table_schema': '${stage_table_schema}',
    'stage_table_name': '${stage_table_name}',
    'run_schedule': '${dag_schedule_cron}',
    'report_refresh_type': '${report_refresh_type}',
    'stage_data_s3_location': '${compressed_dataset_s3_location}',
    'region_name': '${region_name}',

    'dag_id': '${dag_name}',
    'description': '${dag_description}',
    'tags': ${dag_tags},
    'report_name': '${report_name}'
}


def send_failure_alert(context):
    """
    This is a call back function that will be called when a dag fails. The first step is setting up the job run status
    in workflow_configurations.etl_job_runs table and then alerting the DE team of a failure via slack
    :param context:
    :return:
    """
    update_current_etl_run_status('ERROR', **context)
    log.info('Sending alert')
    slack_msg = """
            :red_circle: Task Failed while executing dag *{dag}*.
            *Task*: {task}
            *Dag*: {dag}
            *Execution Time*: {exec_date}
            *Log Url*: <{log_url}|Click Here>
            """.format(
            task=context.get('task_instance').task_id,
            dag=context.get('task_instance').dag_id,
            ti=context.get('task_instance'),
            exec_date=context.get('execution_date'),
            log_url=context.get('task_instance').log_url,
        )
    failed_alert = SlackWebhookOperator(
        slack_webhook_conn_id='slack',
        task_id='dag_failure_notification',
        message=slack_msg,
        username='airflow')
    failed_alert.execute(context=context)


def send_failed_report_alert(**context):
    """
    This function will send slack alert about failed reports with detailed information on how to check
    what reports have failed.
    """
    ti = context['ti']
    etl_job_run_id = ti.xcom_pull(key='etl_job_run_id', task_ids=['initialize_etl_run'])[0]
    etl_job_run_time = ti.xcom_pull(key='etl_job_run_time', task_ids=['initialize_etl_run'])[0]
    glue_job_run_id = ti.xcom_pull(key='glue_job_run_id', task_ids=['ingest_new_data'])[0]

    slack_msg = """
    :flashlight: Reports failed to process when running *{dag}* at time *{etl_batch_run_time}*.
    To find what reports have failed to process, follow the following instructions.
    The etl_job_run_id is *{etl_job_run_id}*
    Report_id is *{report_id}*
    *Get Failed Reports*: `SELECT * FROM workflow_configurations.udf_get_failed_reports_for_report('{report_id}')`
    *Glue Job Run Id*: `{glue_job_run_id}`

    """.format(dag=context.get('task_instance').dag_id,
               etl_batch_run_time=etl_job_run_time,
               etl_job_run_id=etl_job_run_id,
               glue_job_run_id=glue_job_run_id,
               report_id=parameters.get('report_id')
               )
    failed_reports_alert = SlackWebhookOperator(
        task_id='failed_report_notification',
        slack_webhook_conn_id='slack',
        message=slack_msg,
        username='airflow'
    )
    failed_reports_alert.execute(context=context)


def set_dag_run(**context):
    """
    Function will call generate_etl_job_run_id and generate_etl_batch_run_time and will initialize a new job run
    by calling set_new_etl_run.sql script. Job run will set to RUNNING and job_type will be set to INGESTION. Newly
    generated etl_job_run_id and etl_batch_run_time will be added to task_instance by utilizing xcom_push so that these
    values can be used by other functions in the script
    :param context:
    :return:
    """

    # Generate a new etl run. This is a stringified value of uuid4()
    etl_job_run_id = generate_etl_job_run_id()
    # Generate a new etl_batch_run_time. This is a UTC datetime value that is precisioned at the hour level
    etl_batch_run_time = generate_etl_batch_run_time()
    # This is the id that corresponds to report_id value from api_report_info_table
    job_id = parameters.get('report_id')
    log.info(f'New elt_job_run_id is {etl_job_run_id} and etl_batch_run_time is {etl_batch_run_time}')
    # get the query that will be executed to insert new row into etl_job_runs table. Query is located in sql_scripts
    # module.
    query = f"""call workflow_configurations.sp_set_etl_job_run('{etl_job_run_id}', '{job_id}','INGESTION','RUNNING','{etl_batch_run_time}') """

    log.info("Initializing run and setting metdata in backend")
    # execute the query to insert data into table. This is by using Postgres Hook
    pgc.execute_statement(query)

    # add run id and batch run time to airflow context. etl_job_run_id and etl_batch_run_time will be used by succeeding
    # tasks in the dag
    ti = context['ti']
    ti.xcom_push(key='etl_job_run_id', value=etl_job_run_id)
    ti.xcom_push(key='etl_batch_run_time', value=etl_batch_run_time)


def stage_and_merge_data(**context):
    """
    This function will submit a glue job based on the properties for the report to run. Once the job is submitted,
    this process will also poke the glue job run to get the status until either its stopped/ failed/ success and will
    raise error or exit based on the job run status.
    Glue job will find the new data to be loaded, cleanse, compress, stage and merge new data
    :param context:
    :return:
    """

    ti = context['ti']
    etl_job_run_id = ti.xcom_pull(key='etl_job_run_id', task_ids=['initialize_etl_run'])[0]
    etl_job_run_time = ti.xcom_pull(key='etl_batch_run_time', task_ids=['initialize_etl_run'])[0]
    glue_job_name = parameters.get('glue_job_name')
    glue_con = boto3.client('glue')
    log.info("""Submitting glue job to ingest new data. Glue job will perform the following tasks
    1. Get the new s3 files to be processed for each seller for the report
    2. Pick any previously failed reports
    3. Cleanse data
    4. Stage data
    5. Merge data into final table""")
    try:
        # Submit new glue job run to process data
        response = glue_con.start_job_run(JobName=glue_job_name,
                                          Arguments={'--current_etl_batch_run_time': etl_job_run_time,
                                                     '--etl_job_run_id': etl_job_run_id,
                                                     '--report_name': parameters.get('report_name'),
                                                     '--stage_table_schema': parameters.get('stage_table_schema'),
                                                     '--stage_table_name': parameters.get('stage_table_name'),
                                                     '--stage_data_s3_location': parameters.get('stage_data_s3_location'),
                                                     '--region_name': parameters.get('region_name')
                                                     }
                                          )
        log.info(f"Job response is:\n{response}")
        job_run_id = response.get('JobRunId')
        ti.xcom_push(key='glue_job_run_id', value=job_run_id)
        submission_status = response.get('ResponseMetadata').get('HTTPStatusCode')

        if submission_status != 200:
            raise Exception('Glue job submission returned non 200 status')
    except Exception as err:
        msg = traceback_fmt(err)
        log.error(msg)
        raise

    query = f"""UPDATE workflow_configurations.etl_job_runs
                SET ingestion_glue_job_run_id = '{job_run_id}'
                WHERE etl_job_run_id = '{etl_job_run_id}'
            """

    pgc.execute_statement(query)

    # Poke glue job run
    while True:
        response = glue_con.get_job_run(JobName=parameters.get('glue_job_name'), RunId=job_run_id)
        job_run_status = response.get('JobRun').get('JobRunState')

        try:
            # if the job run failed or stopped, raise an exception and fail the task
            if job_run_status in ['FAILED', 'STOPPED']:
                raise Exception(f"Glue run did not succeed. Status is:\n{job_run_status}")
        except Exception as err:
            msg = traceback_fmt(err)
            log.error(msg)
            raise
        # if the job run succeeded, break the loop and exit the task
        if job_run_status == 'SUCCEEDED':
            break
        time.sleep(15)


def update_current_etl_run_status(status='SUCCESS', **context):
    """
    This function will update the current etl_run's job run status. By default, the status is SUCCESS. But, other
    statuses can be passed in as parameter
    """
    ti = context['ti']
    etl_job_run_id = ti.xcom_pull(key='etl_job_run_id', task_ids=['initialize_etl_run'])[0]

    query = f"""UPDATE workflow_configurations.etl_job_runs
            SET job_run_status = '{status}'
                ,job_end_time_utc = CURRENT_TIMESTAMP
            WHERE etl_job_run_id = '{etl_job_run_id}'
        """
    pgc.execute_statement(query)


def get_failed_reports(**context):
    """
    This function will check for any failed reports for a given report and will send a slack alert
    """
    ti = context['ti']
    etl_job_run_id = ti.xcom_pull(key='etl_job_run_id', task_ids=['initialize_etl_run'])[0]
    report_id = parameters.get('report_id')
    query = f"""SELECT * FROM workflow_configurations.udf_get_failed_reports_for_report('{report_id}')"""
    failed_reports, _ = pgc.get_data(query, as_dict=True)
    if failed_reports:
        send_failed_report_alert(**context)

# DAG definition starts from here
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email': ['<EMAIL>'],
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 0,
    'retry_delay': timedelta(minutes=5),
    'on_failure_callback': send_failure_alert
}

with DAG(
    dag_id=parameters.get('dag_id'),
    default_args=default_args,
    schedule_interval=parameters.get('run_schedule'),
    start_date=datetime(2020, 8, 1, 0, 0),
    catchup=False,
    tags=parameters.get('tags'),
) as dag:

    init_etl = PythonOperator(
        task_id='initialize_etl_run',
        python_callable=set_dag_run,
        provide_context=True,
        on_failure_callback=send_failure_alert,
    )
    upsert_data = PythonOperator(
        task_id='ingest_new_data',
        python_callable=stage_and_merge_data,
        provide_context=True,
        on_failure_callback=send_failure_alert,

    )

    failed_rpt = PythonOperator(
        task_id='check_for_failed_reports',
        python_callable=get_failed_reports,
        provide_context=True,
        on_failure_callback=send_failure_alert,
    )

    upd_etl = PythonOperator(
        task_id='set_current_run_to_success',
        python_callable=update_current_etl_run_status,
        op_kwargs={'status': 'SUCCESS'},
        on_failure_callback=send_failure_alert,
    )

    init_etl >> upsert_data >> failed_rpt >> upd_etl

"""
This generic template outlines the standards that Engineers will need to follow while creating a custom dag and
the following are absolutely necessary

1. Implementation of alerts - Alerts should be sent to airflow_alerts channel and the alert should include the
Engineer's name who implemented the dag.
2. Implementation of data quality checks - There should be one or more data quality checks that should be run on the
data. Before upserting/replacing data in to the final table, a new table should be created with all the data in the final
table and dq checks should be performed on the whole data (existing and new data that is supposed to be inserted/ updated/
replaced). Once all the DQ checks pass, execute the actual upsert_data method and drop the temp table. If the DQ checks
don't pass, then dag should be failed and alert should be triggered

If the process involves staging the data and then upserting/ replacing, following two functions should be present
1. stage_data
2. upsert_data (this can either be merge/ replace)

General structure. There will be instances where this dag might depend on another dag for data completeness. In this case
data completeness check should be executed first by adding new ExternalTaskSensor

check_for_upstream_completeness
stage_data
run_dq checks
    if all pass then
        upsert data
    if any failure
        send alert
        fail the dag

DAG failure alerts should be sent using a function named send_failure_alerts
Data staging should be run using stage_data function. No processing of data should be done in airflow. Rather
it has to be done in either spark or on the destination service (glue/ snowflake/ aurora)
DQ checks should be run using dq_checks function. If any of the dq checks fail, the function should raise an
exception so airflow kicks off send_failure_alerts and dag fails.
If the dq checks pass, the final data merge (either upsert/ replace data) should be run using refresh_data function

If connecting to database always use the plugins created.
For snowflake use from db_connectors.sf_connector import Snowflake

Usage for snowflake connector
# Initialize connector instance
sfc = Snowflake()
# to get results use get_data method. More informatio



"""
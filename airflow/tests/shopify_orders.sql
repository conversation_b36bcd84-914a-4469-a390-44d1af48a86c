select * from dwh_dev.prod.fact_all_orders_new_arch where "external_order_id"='2812028289118'
union all
select *, null, null from dwh.prod.fact_all_orders where "external_order_id"=2812028289118::number(38,17)::varchar;

create or replace table dwh_dev.prod.shop_old_fact_all_orders_new_pk as 
select
  "marketplace",
  md5(cast(
        coalesce(cast("brand" as varchar ), '') || '-' ||
        coalesce(cast("external_order_id"::number(38,0) as varchar ), '') || '-' ||
        coalesce(cast("sku" as varchar ), '') || '-' ||
        coalesce(cast("order_id" as varchar ), '') || '-' ||
        coalesce(cast("product_name" as varchar ), '') as varchar
    )) as "order_pk",
  "brand",
  "seller_id",
  "sales_channel",
  "fulfillment_channel",
  "order_id",
  cast("external_order_id"::number(38,0) as varchar ) "external_order_id",
  "purchase_date_utc",
  "sku",
  "netsuite_item_number",
  "product_name",
  "order_status",
  "order_item_status",
  "quantity",
  "country_code",
  "currency",
  "item_price_per_unit",
  "gift_wrap_price_per_unit",
  "shipping_price_per_unit",
  "item_promotion_discount_per_unit",
  "ship_promotion_discount_per_unit",
  "item_tax_lc",
  "shipping_tax_lc",
  "gift_wrap_tax_lc",
  "total_tax_per_unit_lc",
  "total_tax_lc",
  "item_promotion_discount_lc",
  "ship_promotion_discount_lc",
  "gross_revenue_lc",
  "is_replacement_or_disposition",
  "innovation_flag",
  "daton_batch_runtime",
  "source_table",
  "brand_timezone",
  sysdate() "record_created_timestamp_utc", 
  sysdate() "record_updated_timestamp_utc" 
from dwh.prod.fact_all_orders 
where "marketplace"='SHOPIFY';

select * from dwh_dev.prod.fact_all_orders_new_arch where "external_order_id"='3817373761630'
union all
select * from dwh_dev.prod.shop_old_fact_all_orders_new_pk where "external_order_id"='3817373761630';


 
create or replace table dwh_dev.prod.shop_new_old_missing as 
select distinct "order_pk" from dwh_dev.prod.fact_all_orders_new_arch where "marketplace" = 'SHOPIFY'
minus
select distinct "order_pk" from dwh_dev.prod.shop_old_fact_all_orders_new_pk;

create or replace table dwh_dev.prod.old_shop_orders_to_copy as
select * from dwh_dev.prod.shop_old_fact_all_orders_new_pk where "order_pk" not in (select "order_pk" from dwh_dev.prod.shop_new_old_missing);

insert into dwh_dev.prod.fact_all_orders_new_arch (
 "marketplace",
  "order_pk",
  "brand",
  "seller_id",
  "sales_channel",
  "fulfillment_channel",
  "order_id",
  "external_order_id",
  "purchase_date_utc",
  "sku",
  "netsuite_item_number",
  "product_name",
  "order_status",
  "order_item_status",
  "quantity",
  "country_code",
  "currency",
  "item_price_per_unit",
  "gift_wrap_price_per_unit",
  "shipping_price_per_unit",
  "item_promotion_discount_per_unit",
  "ship_promotion_discount_per_unit",
  "item_tax_lc",
  "shipping_tax_lc",
  "gift_wrap_tax_lc",
  "total_tax_per_unit_lc",
  "total_tax_lc",
  "item_promotion_discount_lc",
  "ship_promotion_discount_lc",
  "gross_revenue_lc",
  "is_replacement_or_disposition",
  "innovation_flag",
  "daton_batch_runtime",
  "source_table",
  "brand_timezone",
  "record_created_timestamp_utc",
  "record_updated_timestamp_utc"
)
select "marketplace",
  "order_pk",
  "brand",
  "seller_id",
  "sales_channel",
  "fulfillment_channel",
  "order_id",
  "external_order_id",
  "purchase_date_utc",
  "sku",
  "netsuite_item_number",
  "product_name",
  "order_status",
  "order_item_status",
  "quantity",
  "country_code",
  "currency",
  "item_price_per_unit",
  "gift_wrap_price_per_unit",
  "shipping_price_per_unit",
  "item_promotion_discount_per_unit",
  "ship_promotion_discount_per_unit",
  "item_tax_lc",
  "shipping_tax_lc",
  "gift_wrap_tax_lc",
  "total_tax_per_unit_lc",
  "total_tax_lc",
  "item_promotion_discount_lc",
  "ship_promotion_discount_lc",
  "gross_revenue_lc",
  "is_replacement_or_disposition",
  "innovation_flag",
  "daton_batch_runtime",
  "source_table",
  "brand_timezone",
  "record_created_timestamp_utc",
  "record_updated_timestamp_utc" from dwh_dev.prod.old_shop_orders_to_copy;
  
  select * from dwh_dev.prod.fact_all_orders_new_arch where "marketplace" = 'SHOPIFY';
  
  drop table dwh_dev.prod.old_shop_orders_to_copy;
  drop table dwh_dev.prod.shop_old_fact_all_orders_new_pk;
  drop table dwh_dev.prod.shop_new_old_missing;
  
  
  



version: 0.2
phases:
  install:
    runtime-versions:
      python: 3.12
    commands:
      - |
        echo "Current git commit: - $CODEBUILD_RESOLVED_SOURCE_VERSION"
      - aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 156515529675.dkr.ecr.us-east-2.amazonaws.com
      - export PYTHONPATH=$PYTHONPATH:src
      - export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain hydy --domain-owner 156515529675 --query authorizationToken --output text`
      - export POETRY_HTTP_BASIC_ARTIFACT_USERNAME=aws
      - export POETRY_HTTP_BASIC_ARTIFACT_PASSWORD=$CODEARTIFACT_AUTH_TOKEN
      - export PIP_INDEX_URL=https://aws:$<EMAIL>/pypi/python-common/simple/
      - pip3 config set global.index-url https://aws:$<EMAIL>/pypi/python-common/simple/
      - pip3 install awscli --upgrade --user
  build:
    commands:
      - ls -al
      - |
        echo "Airflow Home: $CODEBUILD_AIRFLOW_HOME/$EFS_AIRFLOW_DIR"
      - EFS_DIR="$CODEBUILD_AIRFLOW_HOME/$EFS_AIRFLOW_DIR"
      - |
        echo "Checking if Airflow home DIR: $EFS_AIRFLOW_DIR is present for current env"
        if [ ! -d "$EFS_DIR" ]; then
          echo "$EFS_DIR does not exist. Creating it on EFS"
          mkdir -p "$EFS_DIR"
          echo "Make sure dir has correct permissions"
          chown -R 50000:0 "$EFS_DIR"
          chmod -R g=rwx "$EFS_DIR"
        fi
      - |
        echo "Checking if Ops Airflow home DIR: $EFS_DIR/dags/ops_dags/dags is present for current env"
        if [ ! -d "$EFS_DIR/dags/ops_dags" ]; then
          echo "$EFS_DIR/dags/ops_dags does not exist. Creating it on EFS"
          mkdir -p "$EFS_DIR/dags/ops_dags/dags"
          mkdir -p "$EFS_DIR/dags/ops_dags/notebooks"
          echo "Make sure dir has correct permissions"
          chown -R 50000:0 "$EFS_DIR/dags/ops_dags"
          chmod -R g=rwx "$EFS_DIR/dags/ops_dags"
        fi
      - |
        echo "Root dir: $CODEBUILD_AIRFLOW_HOME state"
      - ls -al "$CODEBUILD_AIRFLOW_HOME"
      - |
        echo "AIRFLOW_HOME dir: $EFS_DIR state"
      - ls -al "$EFS_DIR"
      - |
        set -e
        echo "Find out current upstream repo"
        echo "source_ouput_2 dir: ${CODEBUILD_SRC_DIR_source_output_2}"
        ls -al ${CODEBUILD_SRC_DIR_source_output_2}
        echo "source_ouput_1 dir: ${CODEBUILD_SRC_DIR}"
        ls -al ${CODEBUILD_SRC_DIR}
        echo "List Ops dir"
        ls -al "$EFS_DIR/dags/ops_dags"
      - |
        set -e
        case "$BUILD_STAGE" in
          "DAG_BUILD")
            echo "Starting Incremental Build..."
            rsync -rlcD --progress airflow/plugins "$EFS_DIR"
            rsync -rlcD --progress airflow/dags "$EFS_DIR"
            rsync -rlcD --progress deployment/airflow/webserver_config.py "$EFS_DIR"
            rsync -rlcD --progress deployment/airflow/health_server.py "$EFS_DIR"
            rsync -rlcD --progress deployment/$AIRFLOW_ENV/airflow.cfg "$EFS_DIR"
            aws s3 cp --recursive glue/configurations/ s3://$GLUE_ASSET_BUCKET/glue-assets/configurations/
            echo "Moving DAGs from Ops secondary repo"
            rsync -rclpD --progress ${CODEBUILD_SRC_DIR_source_output_2}/dags "$EFS_DIR/dags/ops_dags"
            rsync -rclpD --progress ${CODEBUILD_SRC_DIR_source_output_2}/notebooks "$EFS_DIR/dags/ops_dags"
            echo "Finished Incremental Build..."
            echo "Removing dangling file: $EFS_DIR/dags/ops_dags/dags/tasks_to_snowflake/future_oos.py"
            rm -rf "$EFS_DIR/dags/ops_dags/dags/tasks_to_snowflake/future_oos.py"
            ;;
          "IMAGE_BUILD")
            echo "Starting Full Image Build..."
            docker build -t 156515529675.dkr.ecr.us-east-2.amazonaws.com/iac-devflow-ecr-live-7e39387:airflow-$CODEBUILD_RESOLVED_SOURCE_VERSION --build-arg PIP_INDEX_URL=$PIP_INDEX_URL -f deployment/airflow/Dockerfile .
            docker build -t 156515529675.dkr.ecr.us-east-2.amazonaws.com/iac-devflow-ecr-live-7e39387:airflow-ops-$CODEBUILD_RESOLVED_SOURCE_VERSION --build-arg PIP_INDEX_URL=$PIP_INDEX_URL -f deployment/airflow/Dockerfile_ops_worker .
            echo "Container build finished"
            echo "Pushing container to ECR"
            docker push 156515529675.dkr.ecr.us-east-2.amazonaws.com/iac-devflow-ecr-live-7e39387:airflow-$CODEBUILD_RESOLVED_SOURCE_VERSION
            docker push 156515529675.dkr.ecr.us-east-2.amazonaws.com/iac-devflow-ecr-live-7e39387:airflow-ops-$CODEBUILD_RESOLVED_SOURCE_VERSION
            echo 'Pushed image: 156515529675.dkr.ecr.us-east-2.amazonaws.com/iac-devflow-ecr-live-7e39387:airflow-'"$CODEBUILD_RESOLVED_SOURCE_VERSION"
            echo 'Pushed image: 156515529675.dkr.ecr.us-east-2.amazonaws.com/iac-devflow-ecr-live-7e39387:airflow-ops-'"$CODEBUILD_RESOLVED_SOURCE_VERSION"
            ;;
          *)
            echo "Invalid BUILD_STAGE provided: $BUILD_STAGE"
            echo "Accepted values are: DAG_BUILD and IMAGE_BUILD"
            exit 1
            ;;
        esac
  post_build:
    commands:
      - |
        set -e
        if [ $CODEBUILD_BUILD_SUCCEEDING -eq 0 ]; then
          echo "Exiting as build failed"
          exit 1;
        fi
      - |
        echo "Current Environment: $AIRFLOW_ENV"
      - |
        set -e
        case "$BUILD_STAGE" in
          "DAG_BUILD")
            echo "Making sure posix permissions are correct for airflow user"
            echo "Permissions post the owner updates at root: $CODEBUILD_AIRFLOW_HOME level"
            ls -al "$CODEBUILD_AIRFLOW_HOME"
            echo "Permissions post the owner update at airflow home: $EFS_DIR level"
            ls -al "$EFS_DIR"
            echo "Creating build Artifacts for DAG Build"
            printf '[{"ImageURI":"156515529675.dkr.ecr.us-east-2.amazonaws.com/iac-devflow-ecr-live-47db7ed:latest"}]' > imageDetail.json
            cp deployment/$AIRFLOW_ENV/taskdef.json taskdef.json
            cp deployment/$AIRFLOW_ENV/taskdef_worker.json taskdef_worker.json
            ;;
          "IMAGE_BUILD")
            echo "Creating build Artifacts for IMAGE Build"
            printf '[{"ImageURI":"156515529675.dkr.ecr.us-east-2.amazonaws.com/iac-devflow-ecr-live-7e39387:airflow-'"$CODEBUILD_RESOLVED_SOURCE_VERSION"'"}]' > imageDetail.json
            sed 's/IMAGENAME_AIRFLOW/'"156515529675.dkr.ecr.us-east-2.amazonaws.com\/iac-devflow-ecr-live-7e39387:airflow-$CODEBUILD_RESOLVED_SOURCE_VERSION"'/g' deployment/$AIRFLOW_ENV/taskdef.json > taskdef.json
            sed 's/IMAGENAME_AIRFLOW/'"156515529675.dkr.ecr.us-east-2.amazonaws.com\/iac-devflow-ecr-live-7e39387:airflow-$CODEBUILD_RESOLVED_SOURCE_VERSION"'/g' deployment/$AIRFLOW_ENV/taskdef_worker.json > taskdef_worker.json
            sed 's/IMAGENAME_AIRFLOW/'"156515529675.dkr.ecr.us-east-2.amazonaws.com\/iac-devflow-ecr-live-7e39387:airflow-ops-$CODEBUILD_RESOLVED_SOURCE_VERSION"'/g' deployment/$AIRFLOW_ENV/taskdef_additional_worker.json > taskdef_additional_worker.json
            ;;
          *)
            echo "Invalid BUILD_STAGE provided: $BUILD_STAGE"
            echo "Accepted values are: DAG_BUILD and IMAGE_BUILD"
            exit 1
            ;;
        esac
      - cp deployment/$AIRFLOW_ENV/appspec.yaml appspec.yaml
      - cp deployment/$AIRFLOW_ENV/appspec_worker.yaml appspec_worker.yaml
      - cp deployment/$AIRFLOW_ENV/appspec_additional_worker.yaml appspec_additional_worker.yaml
artifacts:
  files:
    - imageDetail.json
    - appspec.yaml
    - appspec_worker.yaml
    - taskdef.json
    - taskdef_worker.json
    - appspec_additional_worker.yaml
    - taskdef_additional_worker.json

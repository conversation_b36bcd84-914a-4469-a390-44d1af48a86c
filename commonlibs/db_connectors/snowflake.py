import snowflake.connector
from snowflake.connector import DictCursor
import awswrangler as wr
import jsonlogger
import boto3
from contextlib import closing
from commonlibs.helpers.helpers import *
from commonlibs.exceptions.exceptions import MissingRegionNameException, NoneParameterError

logger = jsonlogger.configure_logging("snowflake_db_connector", application_name="commonlibs_db_connectors")
s3_client = boto3.client("s3")


class Snowflake(object):
    """
    Class to perform operations on snowflake. Following functionalities are provided by the class
    1. Retrieve data from snowflake - takes in sql statement and the result set can be retrieved as
    list of dictionaries or list of tuples
    2. Run statements - These are DML statements that doesn't need any output dataset to be retrieved

    Users will have the flexibility to provide connection details individually or by specifying a
    secret name that is stored in secrets manager. When specifying secrets, users should also specify the region
    where that secret is stored to retrieve correct configurations
    """
    def __init__(self, user: str = None, password: str = None, account: str = None,
                 warehouse: str = None, role: str = None, secret_name: str = None, region_name=None):
        """
        Constructor that will accept connection parameters for snowflake. Either individual attributes
        can be passed or secret name from secret manager along with region name can be passed.
        """
        self.connection_details = self._get_connection_details(user, password, account, warehouse,
                                                               role, secret_name, region_name)
        self.connection = self._get_connection(**self.connection_details)
        logger.info("Snowflake connection is initialized")

    def __del__(self) -> None:
        """Automatically closes Snowflake connection (if in open state) at the end of script"""
        if self.connection:
            self.connection.close()
            logger.info("Snowflake connection is closed")

    def __str__(self):
        """
        This method overrides the default __str__ method of the object. Returns formatted connection details
        when used with print statements
        :return:
        """
        calling_instance = self.__class__.__name__
        return(str({'calling_class': calling_instance, 'user': self.connection_details['user'],
                    'account': self.connection_details['account'],
                    'warehouse': self.connection_details['warehouse'],
                    'role': self.connection_details['role']}))

    def _get_connection_details(self, user: str = None, password: str = None, account: str = None,
                                warehouse: str = None, role: str = None, secret_name: str = None,
                                region_name: str = None) -> dict:
        """
        It will accept either individual attributes or secret name from secret manager along with region name
        and will create necessary information for Snowflake connection
        """
        if secret_name and not region_name:
            logger.error("Region name is not specified while initializing the Snowflake DB connection")
            raise MissingRegionNameException
        elif secret_name and region_name:
            connection_details = wr.secretsmanager.get_secret_json(secret_name,
                                                                   boto3.Session(region_name=region_name))
        else:
            connection_details = {'user': user,
                                  'password': password,
                                  'account': account,
                                  'warehouse': warehouse,
                                  'role': role}
            if not (user and password and account and warehouse and role):
                logger.error("All individual attributes needs to be specified while initializing "
                             "the Snowflake DB connection")
                raise NoneParameterError([key for key, val in connection_details.items() if not val])
        return connection_details

    def _get_connection(self, **connection_details: dict) -> snowflake.connector:
        """
        This method gets connection using snowflake connector and returns connection object that can be used to perform
        database operations. This is a static method of base class and takes in one parameter
        :param connection_details: dictionary of connection details (host, port, database, user, password)
        :return: connection object
        """
        try:
            logger.info("Getting connection to snowflake")
            conn = snowflake.connector.connect(**connection_details)
            return conn
        except Exception as err:
            msg = traceback_fmt(err)
            logger.error(f"Error occured while trying to connect to database: {msg}")
            raise

    def get_data(self, sql_statement: str, as_dict: bool = False):
        """
        Method will execute query and returns result.
        Optional parameter:
            as_dict => can be passed to get the resultset as list of dictionaries instead of list of tuples
                for further processing or referring columns. Method returns resultset and record count.
        Usage:  res, _ = get_data('select * from table')
                res, _ = get_data('select * from table', as_dict=True)
        to discard row_count just use _ or some garbage variable that can be discarded. If row_count is required for
            further processing, assign it to a named variable

        :param sql_statement: valid SQL statement that can be executed by most of the db engines
        :param as_dict:
        :return: resultset and row count (resultset can be either a list of tuples or list of dictionaries)
        """
        try:
            with closing(self.connection.cursor(DictCursor) if as_dict
                         else self.connection.cursor()) as active_db_cursor:
                logger.info(f"Executing sql statement:\n{sql_statement}")
                res = active_db_cursor.execute(sql_statement).fetchall()
                row_count = active_db_cursor.rowcount
                return res, row_count
        except Exception as err:
            self.connection.rollback()
            msg = traceback_fmt(err)
            logger.error(f"Error occured while trying to connect to database: {msg}")
            raise

    def _execute(self, query_to_execute: str = None, statements_list: list = None):
        """
        This is a private method that will be called by execute_statement and execute_multi_statement methods
        depending on whether a single statement is executed or list of statements are executed, this method will loop
        through all the different statements and executes them.

        :param: query_to_execute: valid sql query to execute
        :param: statements_list: list of valid sql statements to execute

        #TO-DO: Implementation of multistatement execution in a transaction
        """
        try:
            with closing(self.connection.cursor()) as active_db_cursor:
                if query_to_execute:
                    logger.info(f"Executing sql statement:\n {query_to_execute}")
                    active_db_cursor.execute(query_to_execute)

                if statements_list:
                    for query in statements_list:
                        logger.info(f"Executing sql statement:\n {query}")
                        active_db_cursor.execute(query)
        except Exception as err:
            self.connection.rollback()
            msg = traceback_fmt(err)
            logger.error(f"Error occured while trying to connect to database:\n {msg}")
            raise

    def execute_statement(self, query_to_execute: str):
        """
        This method will call _execute private method and passes sql query to execute
        """
        self._execute(query_to_execute=query_to_execute)

    def execute_multi_statements(self, statements_list: list):
        """
        This method will call _execute private method and passses list of sql statements to execute
        """
        self._execute(statements_list=statements_list)

    def unload_data_into_s3(
            self, stage_name: str, s3_prefix: str, data_query: str
    ) -> None:
        """Unloads the data from Snowflake table to AWS S3 files

        Args:
            stage_name: str => name of the external stage created for AWS S3,
            s3_prefix: str => path inside the AWS S3 bucket,
            data_query: str => database select query to target table data which is to be unloaded

        Returns:
            None
        """
        s3_prefix = s3_prefix.strip('/')

        query = f"""rm @{stage_name}/{s3_prefix}/"""
        self.execute_statement(query)

        query = f"""copy into @{stage_name}/{s3_prefix}/
                from ({data_query})
                FILE_FORMAT = (TYPE=CSV, COMPRESSION=NONE, NULL_IF=())
                HEADER = TRUE"""
        self.execute_statement(query)

    def load_data_from_s3(
            self, stage_name: str, s3_prefix: str,
            table_name: str, unique_keys_ls: list = None
    ) -> None:
        """Loads the data from AWS S3 files into Snowflake table using either Merge or Insert
        command and then removes the AWS S3 files.
        If argument "unique_keys_ls" is None then insert data into table
        If argument "unique_keys_ls" is not None then merge data into table

        Args:
            stage_name: str => name of the external stage created for AWS S3,
            s3_prefix: str => path inside the AWS S3 bucket where data files resides,
            table_name: str => full name of the snowflake table including database and schema
                where data is to be loaded,
            unique_keys_ls: list => list of the unique column names which defines the primary key
                on target snowflake table

        Returns:
            None
        """
        s3_prefix = s3_prefix.strip('/')

        database_name = table_name.split('.')[0].upper()
        schema_name = table_name.split('.')[1].upper()
        table_name = table_name.split('.')[2]
        temp_table_name = f"{table_name}_TEMP"

        query = f"LIST @{stage_name}/{s3_prefix}/"
        file_list_df, _ = self.get_data(query)

        if len(file_list_df) == 0:
            logger.info("No Data Available to Load into Snowflake")
        else:
            query = f"USE DATABASE {database_name}"
            self.execute_statement(query)
            query = f"""create temporary table {schema_name}.{temp_table_name}
                        like {database_name}.{schema_name}.{table_name}
                        """
            self.execute_statement(query)

            query = f"""copy into {schema_name}.{temp_table_name}
                    from @{stage_name}/{s3_prefix}/
                    file_format = (type = csv FIELD_OPTIONALLY_ENCLOSED_BY = '"' skip_header = 1)
                    """
            self.execute_statement(query)

            query = f"""SELECT COLUMN_NAME
                    FROM {database_name}.INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = '{schema_name}'
                    AND   TABLE_NAME = '{table_name}'
                    ORDER BY ORDINAL_POSITION"""
            column_names_ls, _ = self.get_data(query)
            column_names_ls = [col[0] for col in column_names_ls]

            if unique_keys_ls is not None:
                query = f"""MERGE INTO {database_name}.{schema_name}.{table_name} AS TARGET
                            USING {schema_name}.{temp_table_name} AS SOURCE ON
                            (
                              {' AND '.join(['Target.' + a + ' = Source.' + a for a in unique_keys_ls])}
                            )
                            WHEN MATCHED THEN UPDATE
                              SET {', '.join(['Target.' + a + ' = Source.' + a for a in column_names_ls])}
                            WHEN NOT MATCHED THEN 
                              INSERT
                              (
                                {','.join(column_names_ls)}
                              )
                              VALUES
                              (
                                {', '.join(['Source.' + a for a in column_names_ls])}
                              )
                        """
            else:
                query = f"""INSERT INTO {database_name}.{schema_name}.{table_name}
                            (
                              {','.join(column_names_ls)}
                            ) 
                            SELECT {','.join(column_names_ls)}
                            FROM {schema_name}.{temp_table_name}
                        """
            self.execute_statement(query)

            query = f"""drop table {schema_name}.{temp_table_name}"""
            self.execute_statement(query)

            query = f"""rm @{stage_name}/{s3_prefix}/"""
            self.execute_statement(query)
            logger.info("Processed files are deleted")

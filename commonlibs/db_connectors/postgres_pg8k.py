import pg8000
import awswrangler as wr
from commonlibs.helpers.helpers import *
import jsonlogger
from commonlibs.exceptions.exceptions import MissingRegionNameException, NoneParameterError
from contextlib import closing
import boto3

logger = jsonlogger.configure_logging("postgres_db_connector", application_name="commonlibs_db_connectors")
s3_client = boto3.client("s3")


class Postgres(object):
    """Class to perform operations on Postgres. Following functionalities are provided by the class
    1. Retrieve data from Postgres - takes in sql statement and the result set can be retrieved as
    list of dictionaries or list of tuples
    2. Run statements - These are DML statements that doesn't need any output dataset to be retrieved

    Users will have the flexibility to provide connection details individually or by specifying a
    secret name that is stored in secrets manager. When specifying secrets, users should also specify the region
    where that secret is stored to retrieve correct configurations"""
    def __init__(self, user: str = None, password: str = None, host: str = None, database: str = None,
                 port: str = None, secret_name: str = None, region_name: str = None):
        """
        Constructor that will accept connection parameters for Postgres. Either individual attributes
        can be passed or secret name from secret manager along with region name can be passed.
        """
        self.connection_details = self._get_connection_details(user, password, host, database,
                                                               port, secret_name, region_name)
        self.connection = self._get_connection(**self.connection_details)
        logger.info("Postgre connection is initialized")

    def __del__(self) -> None:
        """Automatically closes Postgres connection (if in open state) at the end of script"""
        if self.connection:
            self.connection.close()
            logger.info("Postgres connection is closed")

    def __str__(self):
        """
        This method overrides the default __str__ method of the object. Returns formatted connection details
        when used with print statements
        :return:
        """
        calling_instance = self.__class__.__name__
        return(str({'calling_class': calling_instance, 'user': self.connection_details['user'],
                    'password': 'XXXXX', 'database': self.connection_details['database'],
                    'host': self.connection_details['host'], 'port': self.connection_details['port']}))

    def _get_connection_details(self, user: str = None, password: str = None, host: str = None,
                                database: str = None, port: str = None, secret_name: str = None,
                                region_name: str = None) -> dict:
        """
        It will accept either individual attributes or secret name from secret manager along with region name
        and will create necessary information for Postgres connection
        """
        if secret_name and not region_name:
            logger.error("Region name is not specified while initializing the Postgres DB connection")
            raise MissingRegionNameException
        elif secret_name and region_name:
            connection_details = wr.secretsmanager.get_secret_json(secret_name,
                                                                   boto3.Session(region_name=region_name))
        else:
            connection_details = {'user': user,
                                  'password': password,
                                  'host': host,
                                  'database': database,
                                  'port': port}
            if not (user and password and host and database and port):
                logger.error("All individual attributes needs to be specified while initializing "
                             "the Postgres DB connection")
                raise NoneParameterError([key for key, val in connection_details.items() if not val])
        return connection_details

    def _get_connection(self, **connection_details: dict) -> pg8000.dbapi.Connection:
        """
        This method gets connection using snowflake connector and returns connection object that can be used to perform
        database operations. This is a static method of base class and takes in one parameter
        :param connection_details: dictionary of connection details (host, port, database, user, password)
        :return: connection object
        """
        try:
            logger.info("Getting connection to postgres using pg8k")
            conn = pg8000.dbapi.connect(**connection_details)
            return conn
        except Exception as err:
            msg = traceback_fmt(err)
            logger.error(f"Error occured while trying to connect to database: {msg}")
            raise

    def get_data(self, sql_statement: str, as_dict: bool = False):
        """
        Method will execute query and returns result.
        Optional parameters:
            as_dict => can be passed to get the resultset as list of dictionaries instead of list of tuples
                for further processing or referring columns. Method returns resultset and record count.
        Usage:  res, _ = get_data('select * from table')
                res, _ = get_data('select * from table', as_dict=True)
        To discard row_count just use _ or some garbage variable that can be discarded. If row_count is required for
            further processing, assign it to a named variable

        :param sql_statement: valid SQL statement that can be executed by most of the db engines
        :param as_dict:
        :return: resultset and row count (resultset can be either a list of tuples or list of dictionaries)
        """
        try:
            with closing(self.connection.cursor()) as active_db_cursor:
                logger.info(f"Executing sql statement:\n{sql_statement}")
                active_db_cursor.execute(sql_statement)
                res = active_db_cursor.fetchall()
                row_count = active_db_cursor.rowcount
                if as_dict:
                    column_names = [i[0] for i in active_db_cursor.description]
                    return [dict(zip(column_names, row)) for row in res], row_count
                else:
                    return res, row_count
        except Exception as err:
            self.connection.rollback()
            msg = traceback_fmt(err)
            logger.error(f"Error occured while trying to connect to database: {msg}")
            raise

    def _execute(self, query_to_execute: str = None, statements_list: list = None, procedure_name: str = None, params: str = None):
        """
        This is a private method that will be called by execute_statement and execute_multi_statement methods
        depending on whether a single statement is executed or list of statements are executed, this method will loop
        through all the different statements and executes them.

        :param: query_to_execute: valid sql query to execute
        :param: statements_list: list of valid sql statements to execute

        #TO-DO: Implementation of multistatement execution in a transaction
        """
        try:
            with closing(self.connection.cursor()) as active_db_cursor:
                if query_to_execute:
                    logger.info(f"Executing sql statement:\n {query_to_execute}")
                    active_db_cursor.execute(query_to_execute)
                    self.connection.commit()
                elif statements_list:
                    for query in statements_list:
                        logger.info(f"Executing sql statement:\n {query}")
                        active_db_cursor.execute(query)
                    self.connection.commit()
                elif procedure_name:
                    logger.info(f"Calling procedure: {procedure_name} with parameters:{params}")
                    active_db_cursor.callproc(procedure_name, params) if params else active_db_cursor.callproc(procedure_name)
                    self.connection.commit()
                else:
                    raise Exception("Choose correct execution method")
        except Exception as err:
            self.connection.rollback()
            msg = traceback_fmt(err)
            logger.error(f"Error occured while trying to connect to database:\n {msg}")
            raise

    def execute_statement(self, query_to_execute: str):
        """
        This method will call _execute private method and passes sql query to execute
        """
        self._execute(query_to_execute=query_to_execute)

    def execute_multi_statements(self, statements_list: list):
        """
        This method will call _execute private method and passses list of sql statements to execute
        """
        self._execute(statements_list=statements_list)

    def execute_procedure(self, procedure_name, params=None):
        """
        This method will call _execute private method which inturn does a callproc call and executes a
        stored procedure
        :param procedure_name: name of the procedure to call
        :param params: optional parameters of tuples
        :return:
        """
        self._execute(procedure_name=procedure_name, params=params)

    def unload_data_into_s3(
            self, s3_bucket: str, s3_prefix: str, data_query: str
    ) -> None:
        """This function will only work with active aws_s3 extension with below grants:
            CREATE EXTENSION IF NOT EXISTS aws_s3 CASCADE;
            GRANT USAGE ON SCHEMA aws_s3 TO <role>;
            GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA aws_s3 TO <role>;

        It unloads the data from Postgre table to AWS S3 files

        Args:
            s3_bucket: str => name of the AWS S3 bucket,
            s3_prefix: str => path inside the AWS S3 bucket,
            data_query: str => database select query to target table data which is to be unloaded

        Returns:
            None
        """
        s3_prefix = s3_prefix.strip('/')

        """Block Summary: Removing any old pending files from S3"""
        obj_dict = s3_client.list_objects(Bucket=s3_bucket, Prefix=s3_prefix)
        if 'Contents' in obj_dict:
            for key in obj_dict['Contents']:
                file_prefix = key['Key']
                s3_client.delete_object(Bucket=s3_bucket, Key=file_prefix)

        s3_prefix = s3_prefix + '/datafile.csv'

        query = f"""SELECT *
                FROM aws_s3.query_export_to_s3
                ('{data_query}',
                aws_commons.create_s3_uri('{s3_bucket}','{s3_prefix}','us-east-2'),
                options:='format csv, force_quote *, header true')"""
        self.execute_statement(query)

    def load_data_from_s3(
            self, s3_bucket: str, s3_prefix: str,
            table_name: str, unique_keys_ls: list = None
    ) -> None:
        """This function will only work with active aws_s3 extension with below grants:
            CREATE EXTENSION IF NOT EXISTS aws_s3 CASCADE;
            GRANT USAGE ON SCHEMA aws_s3 TO <role>;
            GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA aws_s3 TO <role>;

        It loads the data from AWS S3 files into postgre table using either Merge or Insert
        command and then removes the AWS S3 files.
            If argument "unique_keys_ls" is None then insert data into table
            If argument "unique_keys_ls" is not None then merge data into table

        Args:
            s3_bucket: str => name of the AWS S3 bucket,
            s3_prefix: str => path inside the AWS S3 bucket where data files resides,
            table_name: str => full name of the postgre table including schema where data is to be loaded,
            unique_keys_ls: list => list of the unique column names which defines the primary key
                on target postgre table

        Returns:
            None
        """
        s3_prefix = s3_prefix.strip('/')
        schema_name = table_name.split('.')[0].upper()
        table_name = table_name.split('.')[1]
        temp_table_name = f"{table_name}_TEMP"

        file_names = []
        obj_dict = s3_client.list_objects(Bucket=s3_bucket, Prefix=s3_prefix)
        if 'Contents' not in obj_dict:
            logger.info("No Data Available to Load into Postgre")
        else:
            for key in obj_dict['Contents']:
                file_names.append(key['Key'])

            query = f"""CREATE TEMPORARY TABLE {temp_table_name}
                    AS
                    TABLE {schema_name}.{table_name} WITH NO DATA"""
            self.execute_statement(query)

            for file_prefix in file_names:
                query = f"""SELECT aws_s3.table_import_from_s3('{temp_table_name}', '', '(format csv, header true)',
                                aws_commons.create_s3_uri('{s3_bucket}','{file_prefix}','us-east-2'))"""
                self.execute_statement(query)

            query = f"""SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE UPPER(TABLE_SCHEMA) = '{schema_name}'
                    AND UPPER(TABLE_NAME) = '{table_name}'
                    ORDER BY ORDINAL_POSITION"""
            column_names_ls, _ = self.get_data(query)
            column_names_ls = [col[0] for col in column_names_ls]

            if unique_keys_ls is not None:
                query = f"""WITH UPSERT AS
                        (
                          UPDATE {schema_name}.{table_name} AS TARGET
                             SET {', '.join([a + ' = Source.' + a for a in column_names_ls])}
                          FROM {temp_table_name} AS SOURCE
                          WHERE {' AND '.join(['Target.' + a + ' = Source.' + a for a in unique_keys_ls])}
                          RETURNING TARGET.*
                        )
                        INSERT INTO {schema_name}.{table_name}
                          SELECT {','.join(['T.'+a for a in column_names_ls])}
                          FROM {temp_table_name} AS T
                          WHERE ({','.join(['T.'+a for a in unique_keys_ls])}) NOT IN
                            (SELECT {','.join(['S.'+a for a in unique_keys_ls])} FROM UPSERT AS S)
                        """
            else:
                query = f"""INSERT INTO {schema_name}.{table_name}
                            (
                              {','.join(column_names_ls)}
                            )
                            SELECT {','.join(column_names_ls)}
                            FROM {temp_table_name}
                        """
            self.execute_statement(query)

            query = f"""DROP TABLE {temp_table_name}"""
            self.execute_statement(query)

            for file_prefix in file_names:
                s3_client.delete_object(Bucket=s3_bucket, Key=file_prefix)
            logger.info("Processed files are deleted")

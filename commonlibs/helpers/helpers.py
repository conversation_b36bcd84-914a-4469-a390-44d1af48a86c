import traceback

from commonlibs.custom_logging.logger import get_logger
from commonlibs.exceptions.exceptions import NoneParameterError, DateTimeFormatException
import pkgutil
import json
import re
import datetime
from datetime import datetime, timedelta
log = get_logger(__name__)


def traceback_fmt(err):
    """
    Function will take the stack strace and properly format so the full trace can be logged
    in user readable fashion
    :param err: stack trace
    :return: formatted string
    """
    return "".join(traceback.format_exception(type(err), err, err.__traceback__))


def convert_to_utc_aware(date_time=None, date_time_str: str = None, strp_format: str = None):
    """
    Function that will convert datetime object to timezone aware object using pytz localize
    if datetime object is passed, its directly converted to utc
    if a string is passed, format should also be passed so that its converted to datetime object
    and timezone aware datetime object is returned
    :param date_time: datetime object
    :param date_time_str: datetime as string
    :param strp_format: format to which datetime string need to be converted
    :return: datetime.datetime object with tzinfo set to UTC
    """
    try:
        if date_time_str and not strp_format:
            raise DateTimeFormatException

        import pytz
        utc=pytz.UTC
        if date_time:
            return utc.localize(date_time)
        else:
            return utc.localize(datetime.strptime(date_time_str, strp_format))
    except DateTimeFormatException as err:
        msg = "".join(traceback.format_exception(type(err), err, err.__traceback__))
        log.error(msg)


def get_empty_parameters(**kwargs) -> list:
    """
    This method takes **kwargs as input and loops through each key and returns list of keys where value is none in the
    sorted order of keys
    :param kwargs:
    :return:
    """
    return sorted([key for key, value in kwargs.items() if value is None])


def multi_replace(package_path: str, file_name: str, dictionary_map: dict) -> str:
    """
    This method will take package_path and file_name and mapping for placeholders and returns replaced string
    :param package_path: absolute package path where the file is
    :param file_name: name of the file
    :param dictionary_map: placeholders that need to be replaced
    :return: formatted string with replaced placeholders with actual values
    """
    from string import Template
    return Template(pkgutil.get_data(package_path, file_name).decode('utf-8')).safe_substitute(**dictionary_map)


def read_contents_from_file(package_path: str, file_name: str):
    """
    This method will take package_path and file_name and returns content of file as string
    :param package_path: absolute package path where the file is
    :param file_name: name of the file
    :return: string
    """
    return pkgutil.get_data(package_path, file_name).decode('utf-8')


def multi_replace_string(str_: str, dictionary_map: dict) -> str:
    """
    :param str_: Pass a string with place holders
    :param dictionary_map: dictionary that will have placeholders and the values to replaces
    :return: string with replaced placeholders
    """
    from string import Template
    return Template(str_).safe_substitute(**dictionary_map)


def extract_from_configuration_file(package_path: str, file_name: str, key: str=None):
    """
    Given a package path and file name and key to extract, this function will parse the file load contents as json and
    returns the value associated with the key
    :param package_path:
    :param file_name:
    :param key:
    :return:
    """
    configurations = eval(pkgutil.get_data(package_path, file_name).decode('utf-8'))
    if key:
        return configurations[key]
    else:
        return configurations


def get_dict(key_list: tuple, value_list: tuple) -> dict:
    """
    Generates dictionary when two lists of same length are passed. First argument will be considered as keys
    and second argument as values
    """
    return dict(zip(key_list, value_list))


def generate_date_range(start_date=None,end_date=None,lookback_period=None):
    """
    Function takes three arguments either two or 1 are required based on what arguments are being passed
    if start_date is passed then end_date is required argument and lookback_period is optional
    if lookback_period is passed then both start and end dates are optional
    returns list of dates sorted in ascending order
    """
    try:
        if not start_date and not end_date and not lookback_period:
            log.info("Function requires either combination of star and end dates or lookback period")
        elif start_date and not end_date:
            log.info("End date should be specified with start date")
        elif end_date and not start_date:
            log.info("Start date should be specified with end date")

        else:
            if start_date and end_date:
                start_date_time = datetime.strptime(start_date, '%Y-%m-%d').date()
                end_date_time = datetime.strptime(end_date, '%Y-%m-%d').date()
                delta_days = (end_date_time - start_date_time).days
            else:
                delta_days = lookback_period
                end_date_time = datetime.now().date()
                start_date_time = end_date_time - timedelta(delta_days)
            log.info("Generating date range sequence")
            return sorted([(start_date_time + timedelta(days=x)).strftime('%Y-%m-%d') for x in range(delta_days + 1)])
    except Exception as err:
        msg = traceback_fmt(err)
        log.error(msg)
        raise


def write_sql_output_to_file(result_set, file_name, column_delimiter):
        """
        Function takes in result_set, file_name(including path) and column_delimiter and creates a file
        This function is useful to save the file to s3 and load data into a database or further procesing of data
        using redshift/ emr etc...
        """
        with open(file_name, 'w') as f:
            log.info(f"Writing contents to file: {file_name}")
            for row in result_set:
                print(column_delimiter.join(map(str, row)).replace('None', 'NULL'), file=f)


def compare_list_data(list_1, list_2):

    """
    This function compares two lists and returns a list from data in list_1 that doesn't exist in list_2
    """
    return [i for i in list_1 if i not in list_2]


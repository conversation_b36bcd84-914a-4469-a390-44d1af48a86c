class NoneParameterError(Exception):
    """
    This is a custom Exception derieved from Base class. When raised, this exception
    will raise NoneParameterError and shows what keys are not set to any value
    """
    def __init__(self, params_list):
        self.params_list = params_list
        self.values = ','.join("'{0}'".format(param) for param in self.params_list)

    def __str__(self):
        if len(self.params_list) > 1:
            message = 'parameters are set to None'
        else:
            message = 'is set to None'
        return "{} {}".format(self.values, message)


class MissingRegionNameException(Exception):

    def __str__(self):
        return "region name is required when secret name is passed"


class DateTimeFormatException(Exception):
    """
    This is a custom exception that will be used in the method convert_to_utc_aware to ensure
    pair of inputs are properly passed
    """
    def __str__(self):
        return "strp format is required when string representation of datetime is passed"

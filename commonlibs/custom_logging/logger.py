import logging

# Set the logging format
FORMATTER = logging.Formatter('[%(asctime)s]{%(filename)s:%(lineno)d<%(funcName)s>} %(levelname)s - %(message)s')


def get_console_handler():
    """
    This method will add console handler to logger when called
    :return:
    """
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(FORMATTER)
    return console_handler


def get_file_handler(log_file_name):
    """
    This method will add file handler to logger when called with appropriate file name
    :param log_file_name:
    :return:
    """
    file_handler = logging.FileHandler(log_file_name)
    file_handler.setFormatter(FORMATTER)
    return file_handler


def get_logger(logger_name, log_level='INFO', log_file_name=None, log_handler=None):
    """
    Exposed method to initialize logger. This can be used in every module and the propogation
    is set to false so that it will be at the module level.
    :param logger_name: name of the logger. In most scenarios __name__ meaning, the module from where
    the log is invoked
    :param log_level: level of logging to set
    DEBUG - will log everything from and over DEBUG
    INFO - will log everything from and over INFO
    :param log_file_name: Passing this parameter will enable to write logs to file as well besides
    logging to console
    :return: logger with all the configurations passed
    """
    logger = logging.getLogger(logger_name)
    numeric_level = getattr(logging, log_level.upper(), 10)
    logger.setLevel(numeric_level)
    logger.addHandler(get_console_handler())
    if log_file_name:
        logger.addHandler(get_file_handler(log_file_name))
    if log_handler:
        logger.addHandler(log_handler)
    logger.propagate = False
    return logger

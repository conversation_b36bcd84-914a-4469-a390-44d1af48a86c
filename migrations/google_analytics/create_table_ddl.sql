-- drop table dwh_dev.common.dim_google_analytics_id_brand_map;
create table dwh_dev.common.dim_google_analytics_id_brand_map if not exists (
    id NUMBER(38,0) UNIQUE NOT NULL,
    brand VARCHAR,
	source_tz VARCHAR,
    created_by VARCHAR NOT NULL,
    updated_by VARCHAR,
    record_created_timestamp_utc TIMESTAMP_NTZ(9),
	record_updated_timestamp_utc TIMESTAMP_NTZ(9)
)
DATA_RETENTION_TIME_IN_DAYS=14
COMMENT='This table contains the mapping between ga id and brand';

INSERT INTO DWH_DEV.COMMON.dim_google_analytics_id_brand_map 
(id,brand,source_tz, created_by, updated_by, record_created_timestamp_utc, record_updated_timestamp_utc) 
VALUES
(152539505,'HTS','PT','tanmai.naik', null, sysdate(), null),
(207718060,'IRO','CT','tanmai.naik', null, sysdate(), null),
(229106377,'DCZ','CT','tanmai.naik', null, sysdate(), null),
(172460324,'DCZ','CT','tanmai.naik', null, sysdate(), null),
(162074361,'BSY',NULL,'tanmai.naik', null, sysdate(), null),
(212182936,'BSY','PT','tanmai.naik', null, sysdate(), null),
(192810148,'BSY',NULL,'tanmai.naik', null, sysdate(), null),
(109589024,'MRD','ET','tanmai.naik', null, sysdate(), null),
(236611258,'TOL','CT','tanmai.naik', null, sysdate(), null),
(106433187,'BOK','CT','tanmai.naik', null, sysdate(), null);

-- drop table  dwh_dev.raw.google_analytics_campaign_level_v2;
create table dwh_dev.raw.google_analytics_campaign_level_v2 if not exists (
	D_ga_campaign VARCHAR,
	D_ga_date TIMESTAMP_NTZ(9),
	D_ga_medium VARCHAR,
	D_ga_source VARCHAR,
	D_ga_sourceMedium VARCHAR,
	EndDate TIMESTAMP_NTZ(9),
  	StartDate TIMESTAMP_NTZ(9),
	M_ga_adClicks FLOAT,
	M_ga_adCost FLOAT,
	M_ga_impressions FLOAT,
	M_ga_itemRevenue FLOAT,
	M_ga_itemsPerPurchase FLOAT,
	M_ga_productRevenuePerPurchase FLOAT,
	M_ga_uniquePurchases FLOAT,
	M_ga_visits FLOAT,
    ga_account_id NUMBER(38,0) NOT NULL,
    brand VARCHAR NOT NULL,
	source_tz VARCHAR,
	daton_batch_runtime NUMBER(38,0),
    daton_golden BOOLEAN,
	"record_created_timestamp_utc" TIMESTAMP_NTZ(9),
	"record_updated_timestamp_utc" TIMESTAMP_NTZ(9),
	file_name VARCHAR,
    etl_batch_run_time TIMESTAMP_NTZ(9)
)
DATA_RETENTION_TIME_IN_DAYS=14
COMMENT='This table is to keep track of raw google_analytics_campaign_level_v2';

drop table dwh_dev.prod.google_analytics_advertisements;
create TABLE dwh_dev.prod.google_analytics_advertisements if not exists(
	"ga_ads_pk" VARCHAR(32) NOT NULL UNIQUE,
	"ga_account_id" VARCHAR(********),
	"brand" VARCHAR(********),
	"dim_ga_date" DATE,
	"start_date" DATE,
	"end_date" DATE,
	"source_tz" VARCHAR(********),
	"dim_ga_campaign" VARCHAR(********),
	"drv_source" VARCHAR(9),
	"drv_medium" VARCHAR(7),
	"dim_ga_source_medium" VARCHAR(********),
	"metric_ga_ad_clicks" FLOAT,
	"metric_ga_ad_cost" FLOAT,
	"metric_ga_impressions" FLOAT,
	"metric_ga_item_revenue" FLOAT,
	"metric_ga_items_per_purchase" FLOAT,
	"metric_ga_product_revenue_per_purchase" FLOAT,
	"metric_ga_unique_purchases" FLOAT,
	"metric_ga_visits" FLOAT,
	"daton_golden" BOOLEAN,
	"record_created_timestamp_utc" TIMESTAMP_NTZ(9),
	"record_updated_timestamp_utc" TIMESTAMP_NTZ(9)
);
DATA_RETENTION_TIME_IN_DAYS=14
COMMENT='This table contains the published data for google ads';

drop table dwh_dev.prod.google_analytics_costs_clicks_impressions;
create TABLE dwh_dev.prod.google_analytics_costs_clicks_impressions if not exists (
	"ga_ads_pk" VARCHAR(32) NOT NULL UNIQUE,
	"ga_account_id" VARCHAR(********),
	"brand" VARCHAR(********),
	"D_ga_date" DATE,
	"source_tz" VARCHAR(********),
	"D_ga_source" VARCHAR(********),
	"D_ga_medium" VARCHAR(********),
	"D_ga_sourceMedium" VARCHAR(********),
	"drv_source" VARCHAR(9),
	"drv_medium" VARCHAR(7),
	"D_ga_campaign" VARCHAR(********),
	"M_ga_impressions" FLOAT,
	"M_ga_adClicks" FLOAT,
	"M_ga_adCost" FLOAT,
	"record_created_timestamp_utc" TIMESTAMP_NTZ(9),
	"record_updated_timestamp_utc" TIMESTAMP_NTZ(9)
)
DATA_RETENTION_TIME_IN_DAYS=14;


-- V4
-- drop table  dwh_dev.raw.google_analytics_campaign_level_v4;
create table dwh_dev.raw.google_analytics_campaign_level_v4 if not exists (
	D_GA_CAMPAIGN VARCHAR(********),
	D_GA_DATE DATE,
	D_GA_MEDIUM VARCHAR(********),
	D_GA_SOURCEMEDIUM VARCHAR(********),
	D_GA_TRANSACTIONID VARCHAR(********),
	ENDDATE DATE,
	STARTDATE DATE,
	M_GA_REFUNDAMOUNT VARCHAR(********),
	M_GA_TOTALREFUNDS VARCHAR(********),
	M_GA_TRANSACTIONREVENUE VARCHAR(********),
	M_GA_TRANSACTIONSHIPPING VARCHAR(********),
	M_GA_TRANSACTIONTAX VARCHAR(********),
	M_GA_UNIQUEPURCHASES VARCHAR(********),
	GA_ACCOUNT_ID VARCHAR(********),
	BRAND VARCHAR(********),
	SOURCE_TZ VARCHAR(********),
	DATON_BATCH_RUNTIME NUMBER(38,0),
	DATON_GOLDEN BOOLEAN,
	"record_created_timestamp_utc" TIMESTAMP_NTZ(9),
	"record_updated_timestamp_utc" TIMESTAMP_NTZ(9),
	file_name VARCHAR,
    etl_batch_run_time TIMESTAMP_NTZ(9)
)
DATA_RETENTION_TIME_IN_DAYS=14
COMMENT='This table is to keep track of raw google_analytics_campaign_level_v4';



drop table dwh_dev.prod.google_analytics_marketing_orders_revenue;
create TABLE dwh_dev.prod.google_analytics_marketing_orders_revenue if not exists (
	"ga_ads_pk" VARCHAR(32) NOT NULL UNIQUE,
	"ga_account_id" VARCHAR(********),
	"brand" VARCHAR(********),
	"D_ga_date" DATE,
	"D_ga_campaign" VARCHAR(********),
	"drv_source" VARCHAR(9),
	"drv_medium" VARCHAR(7),
	"D_ga_sourceMedium" VARCHAR(********),
	"D_ga_transactionId" VARCHAR(********),
	"marketing_orders" NUMBER(38,0),
	"marketing_revenue" FLOAT,
	"record_created_timestamp_utc" TIMESTAMP_NTZ(9),
	"record_updated_timestamp_utc" TIMESTAMP_NTZ(9)
)
DATA_RETENTION_TIME_IN_DAYS=14;
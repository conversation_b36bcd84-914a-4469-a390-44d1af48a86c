-- use role transform_role

-- dwh.prod.FACT_ALL_REFUNDS definition
-- drop table if exists dwh.prod.FACT_ALL_REFUNDS;

create or replace TABLE dwh.prod.FACT_ALL_REFUNDS (
	"refund_pk" VARCHAR(32),
	"marketplace" VARCHAR(21),
	"brand" VARCHAR(16777216),
	"seller_id" VARCHAR(16777216),
	"sales_channel" VARCHAR(16777216),
	"fulfillment_channel" VARCHAR(3),
	"order_id" VARCHAR(16777216),
	"external_order_id" VARCHAR(16777216),
	"sku" VARCHAR(16777216),
	"netsuite_item_number" VARCHAR(16777216),
	"purchase_date_utc" TIMESTAMP_NTZ(9),
	"refund_date_utc" TIMESTAMP_NTZ(9),
	"quantity" NUMBER(38,0),
	"product_refund" FLOAT,
	"tax_refund" FLOAT,
	"shipping_refund" FLOAT,
	"other_refund" FLOAT,
	"country_code" VARCHAR(16777216),
	"currency" VARCHAR(16777216),
	"innovation_flag" BOOLEAN,
	"daton_batch_runtime" NUMBER(38,17),
	"source_table" VARCHAR(300),
	"record_updated_timestamp_utc" VARCHAR(16777216),
	"record_created_timestamp_utc" TIMESTAMP_LTZ(9)
)
DATA_RETENTION_TIME_IN_DAYS=14
COMMENT='This table is used as primary dataset to track refunds data for Amazon and shopify sellers.';

-- dwh.staging.AMAZON_FINANCIAL_EVENTS_REFUNDS_SHIPMENTS definition
-- drop table if exists dwh.staging.AMAZON_FINANCIAL_EVENTS_REFUNDS_SHIPMENTS;

create TABLE dwh.staging.AMAZON_FINANCIAL_EVENTS_REFUNDS_SHIPMENTS IF NOT EXISTS(
	"record_created_timestamp_utc" TIMESTAMP_NTZ(9),
	"record_updated_timestamp_utc" TIMESTAMP_NTZ(9),
	"fv_refunds_pk" VARCHAR(32),
	"order_id" VARIANT,
	"seller_order_id" VARIANT,
	"order_charge_list" VARIANT,
	"order_charge_adjustment_list" VARIANT,
	"shipment_fee_list" VARIANT,
	"shipment_fee_adjustment_list" VARIANT,
	"order_fee_list" VARIANT,
	"order_fee_adjustment_list" VARIANT,
	"direct_payment_list" VARIANT,
	"posted_date" TIMESTAMP_NTZ(9),
	"shipment_item_list" VARIANT,
	"shipment_item_adjustment_list" VARIANT,
	"daton_batch_runtime" NUMBER(38,0),
	"marketplace_id" VARCHAR(16777216),
	"marketplace_name" VARCHAR(16777216),
	"selling_partner_id" VARCHAR(16777216)
)
DATA_RETENTION_TIME_IN_DAYS=14
COMMENT='This table contains data from the AMAZON_FINANCIAL_EVENTS_REFUNDS_SHIPMENTS feed';


-- dwh.staging.park_amazon_refunds_with_no_orders definition
-- drop table if exists dwh.staging.park_amazon_refunds_with_no_orders;

create or replace TABLE dwh.staging.park_amazon_refunds_with_no_orders (
	"fv_refunds_pk" VARCHAR(32),
	"record_created_timestamp_utc" TIMESTAMP_NTZ(9),
	"record_updated_timestamp_utc" TIMESTAMP_NTZ(9)
)
DATA_RETENTION_TIME_IN_DAYS=14
COMMENT='This table contains amazon refund records not having a corresponding order';
truncate table if exists dwh.prod.fact_all_refunds;
INSERT INTO dwh.prod.fact_all_refunds
("refund_pk" ,
	"marketplace" ,
	"brand",
	"seller_id",
	"sales_channel",
	"fulfillment_channel" 
	"order_id",
	"external_order_id",
	"sku",
	"netsuite_item_number",
	"purchase_date_utc",
	"refund_date_utc",
	"quantity",
	"product_refund",
	"tax_refund",
	"shipping_refund",
	"other_refund",
	"country_code",
	"currency",
	"innovation_flag",
	"daton_batch_runtime",
	"source_table",
	"record_updated_timestamp_utc",
	"record_created_timestamp_utc")
SELECT 
    "refund_pk" ,
	"marketplace" ,
	"brand",
	"seller_id",
	"sales_channel",
	"fulfillment_channel" 
	"order_id",
	"external_order_id",
	"sku",
	"netsuite_item_number",
	"purchase_date_utc",
	"refund_date_utc",
	"quantity",
	"product_refund",
	"tax_refund",
	"shipping_refund",
	"other_refund",
	"country_code",
	"currency",
	"innovation_flag",
	"daton_batch_runtime",
	"source_table",
	"record_updated_timestamp_utc",
	"record_created_timestamp_utc"
FROM dwh_dev.prod.fact_all_refunds_new_arch;

truncate table if exists dwh.staging.AMAZON_FINANCIAL_EVENTS_REFUNDS_SHIPMENTS;
insert into dwh.staging.AMAZON_FINANCIAL_EVENTS_REFUNDS_SHIPMENTS
("record_created_timestamp_utc", "record_updated_timestamp_utc", "fv_refunds_pk", "order_id", "seller_order_id", "order_charge_list", "order_charge_adjustment_list", "shipment_fee_list", "shipment_fee_adjustment_list", "order_fee_list", "order_fee_adjustment_list", "direct_payment_list", "posted_date", "shipment_item_list", "shipment_item_adjustment_list", "daton_batch_runtime", "marketplace_id", "marketplace_name", "selling_partner_id")
select 
"record_created_timestamp_utc", "record_updated_timestamp_utc", "fv_refunds_pk", "order_id", "seller_order_id", "order_charge_list", "order_charge_adjustment_list", "shipment_fee_list", "shipment_fee_adjustment_list", "order_fee_list", "order_fee_adjustment_list", "direct_payment_list", "posted_date", "shipment_item_list", "shipment_item_adjustment_list", "daton_batch_runtime", "marketplace_id", "marketplace_name", "selling_partner_id"
FROM dwh_dev.staging.AMAZON_FINANCIAL_EVENTS_REFUNDS_SHIPMENTS;

truncate table if exists dwh.staging.PARK_AMAZON_REFUNDS_WITH_NO_ORDERS;
INSERT INTO dwh.staging.PARK_AMAZON_REFUNDS_WITH_NO_ORDERS
("order_id", "sku", "seller_id", "refund_date_utc", "quantity", "product_refund", "tax_refund", "shipping_refund", "other_refund", "currency", "daton_batch_runtime")
select 
"order_id", "sku", "seller_id", "refund_date_utc", "quantity", "product_refund", "tax_refund", "shipping_refund", "other_refund", "currency", "daton_batch_runtime"
from dwh_dev.staging.PARK_AMAZON_REFUNDS_WITH_NO_ORDERS;
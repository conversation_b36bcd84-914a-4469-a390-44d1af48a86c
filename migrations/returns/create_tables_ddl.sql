-- use role transform_role

-- dwh.prod.FACT_ALL_RETURNS definition
-- drop table if exists dwh.prod.FACT_ALL_RETURNS;

create TABLE dwh.prod.FACT_ALL_RETURNS IF NOT EXISTS(
	"returns_pk" VARCHAR(32),
	"seller_id" VARCHAR(16777216),
	"marketplace" VARCHAR(6),
	"brand" VARCHAR(16777216),
	"sales_channel" VARCHAR(16777216),
	"fulfillment_channel" VARCHAR(3),
	"order_id" VARCHAR(16777216),
	"sku" VARCHAR(16777216),
	"netsuite_item_number" VARCHAR(16777216),
	"country_code" VARCHAR(16777216),
	"item_condition" VARCHAR(16777216),
	"detailed_disposition" VARCHAR(16777216),
	"return_date_utc" TIMESTAMP_NTZ(9),
	"quantity_returned" NUMBER(38,0),
	"record_updated_timestamp_utc" TIMESTAMP_NTZ(9),
	"record_created_timestamp_utc" TIMESTAMP_NTZ(9),
	"source_table" VARCHAR(30)
)
DATA_RETENTION_TIME_IN_DAYS=14
COMMENT='This table is used as primary dataset to track retuerns data for Amazon and shopify sellers.';
truncate table if exists dwh.prod.FACT_ALL_RETURNS;

INSERT INTO DWH_DEV.PROD.FACT_ALL_RETURNS
("returns_pk", "seller_id", "marketplace", "brand", "sales_channel", "fulfillment_channel", "order_id", "sku", "netsuite_item_number", "country_code", "item_condition", "detailed_disposition", "return_date_utc", "quantity_returned", "record_updated_timestamp_utc", "record_created_timestamp_utc", "source_table")
select
"returns_pk", "seller_id", "marketplace", "brand", "sales_channel", "fulfillment_channel", "order_id", "sku", "netsuite_item_number", "country_code", "item_condition", "detailed_disposition", "return_date_utc", "quantity_returned", "record_updated_timestamp_utc", "record_created_timestamp_utc", "source_table"
from dwh_dev.prod.FACT_ALL_RETURNS;

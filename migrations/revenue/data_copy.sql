truncate table if exists dwh.prod.FACT_ALL_REVENUE;

INSERT INTO dwh.prod.fact_all_revenue (
  "marketplace",
  "brand",
  "seller_id",
  "order_id",
  "external_order_id",
  "order_item_id",
  "shipment_id",
  "shipment_item_id",
  "sku",
  "innovation_flag",
  "netsuite_item_number",
  "product_name",
  "purchase_date_utc",
  "order_status",
  "order_item_status",
  "sales_channel",
  "fulfillment_channel",
  "shipment_date_utc",
  "quantity_ordered",
  "quantity_shipped",
  "country_code",
  "currency",
  "sell_price_per_unit_lc",
  "gift_wrap_price_per_unit_lc",
  "shipping_price_per_unit_lc",
  "item_promotion_discount_per_unit_lc",
  "ship_promotion_discount_per_unit_lc",
  "gift_wrap_price_lc",
  "shipping_price_lc",
  "item_promotion_discount_lc",
  "ship_promotion_discount_lc",
  "item_tax_lc",
  "shipping_tax_lc",
  "gift_wrap_tax_lc",
  "sell_price_lc",
  "brand_timezone",
  "record_created_timestamp_utc",
  "record_updated_timestamp_utc"
)
SELECT 
    "marketplace",
    "brand",
    "seller_id",
    "order_id",
    "external_order_id",
    "order_item_id",
    "shipment_id",
    "shipment_item_id",
    "sku",
    "innovation_flag",
    "netsuite_item_number",
    "product_name",
    "purchase_date_utc",
    "order_status",
    "order_item_status",
    "sales_channel",
    "fulfillment_channel",
    "shipment_date_utc",
    "quantity_ordered",
    "quantity_shipped",
    "country_code",
    "currency",
    "sell_price_per_unit_lc",
    "gift_wrap_price_per_unit_lc",
    "shipping_price_per_unit_lc",
    "item_promotion_discount_per_unit_lc",
    "ship_promotion_discount_per_unit_lc",
    "gift_wrap_price_lc",
    "shipping_price_lc",
    "item_promotion_discount_lc",
    "ship_promotion_discount_lc",
    "item_tax_lc",
    "shipping_tax_lc",
    "gift_wrap_tax_lc",
    "sell_price_lc",
    "brand_timezone",
    "record_created_timestamp_utc",
    "record_updated_timestamp_utc"
FROM dwh_dev.prod.fact_all_revenue_new_arch;
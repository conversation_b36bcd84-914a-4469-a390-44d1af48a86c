-- use role transform_role

-- dwh.prod.FACT_ALL_REVENUE definition
-- Truncate table
-- drop table if exists dwh.prod.FACT_ALL_REVENUE;

create TABLE dwh.prod.FACT_ALL_REVENUE IF NOT EXISTS(
	"marketplace" VARCHAR(21),
	"brand" VARCHAR(16777216),
	"seller_id" VARCHAR(16777216),
	"order_id" VARCHAR(16777216),
	"external_order_id" VARCHAR(16777216),
	"order_item_id" VARCHAR(16777216),
	"shipment_id" VARCHAR(16777216),
	"shipment_item_id" VARCHAR(16777216),
	"sku" VARCHAR(16777216),
	"innovation_flag" BOOLEAN,
	"netsuite_item_number" VARCHAR(16777216),
	"product_name" VARCHAR(16777216),
	"purchase_date_utc" TIMESTAMP_NTZ(9),
	"order_status" VARCHAR(16777216),
	"order_item_status" VARCHAR(16777216),
	"sales_channel" VARCHAR(16777216),
	"fulfillment_channel" VARCHAR(3),
	"shipment_date_utc" TIMESTAMP_NTZ(9),
	"quantity_ordered" NUMBER(38,0),
	"quantity_shipped" NUMBER(38,0),
	"country_code" VARCHAR(16777216),
	"currency" VARCHAR(16777216),
	"sell_price_per_unit_lc" FLOAT,
	"gift_wrap_price_per_unit_lc" FLOAT,
	"shipping_price_per_unit_lc" FLOAT,
	"item_promotion_discount_per_unit_lc" FLOAT,
	"ship_promotion_discount_per_unit_lc" FLOAT,
	"gift_wrap_price_lc" FLOAT,
	"shipping_price_lc" FLOAT,
	"item_promotion_discount_lc" FLOAT,
	"ship_promotion_discount_lc" FLOAT,
	"item_tax_lc" FLOAT,
	"shipping_tax_lc" FLOAT,
	"gift_wrap_tax_lc" FLOAT,
	"sell_price_lc" FLOAT,
	"brand_timezone" VARCHAR(16777216),
	"record_updated_timestamp_utc" TIMESTAMP_NTZ(9),
	"record_created_timestamp_utc" TIMESTAMP_NTZ(9)
)
DATA_RETENTION_TIME_IN_DAYS=14
COMMENT='This table is used as primary dataset in Contribution Project to match revenue based on shipment data with Amazon, shopify sellers dashboard.';
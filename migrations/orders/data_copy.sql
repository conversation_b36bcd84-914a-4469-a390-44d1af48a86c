-- use role transform_role

-- truncate table dwh.prod.raw_shopify_orders;

insert into dwh.staging.raw_shopify_orders(
    "id",
    "brand_name",
    "shopify_store",
    "file_name",
    "admin_graphql_api_id",
    "app_id",
    "billing_address",
    "browser_ip",
    "buyer_accepts_marketing",
    "cancel_reason",
    "cancelled_at",
    "cart_token",
    "checkout_id",
    "checkout_token",
    "client_details",
    "closed_at",
    "confirmed",
    "contact_email",
    "created_at",
    "currency",
    "customer",
    "customer_locale",
    "device_id",
    "discount_codes",
    "email",
    "financial_status",
    "fulfillment_status",
    "fulfillments",
    "gateway",
    "landing_site",
    "landing_site_ref",
    "line_items",
    "location_id",
    "name",
    "note",
    "note_attributes",
    "number",
    "order_number",
    "order_status_url",
    "payment_details",
    "payment_gateway_names",
    "phone",
    "processed_at",
    "processing_method",
    "reference",
    "referring_site",
    "refunds",
    "shipping_address",
    "shipping_lines",
    "source_identifier",
    "source_name",
    "source_url",
    "subtotal_price",
    "tags",
    "tax_lines",
    "taxes_included",
    "test",
    "token",
    "total_discounts",
    "total_line_items_price",
    "total_price",
    "total_price_usd",
    "total_tax",
    "total_weight",
    "transactions",
    "updated_at",
    "user_id",
    "daton_batch_runtime",
    "daton_batch_id",
    "daton_user_id",
    "record_created_timestamp_utc",
    "record_updated_timestamp_utc"
)
select
    "id",
    "brand_name",
    "shopify_store",
    "file_name",
    "admin_graphql_api_id",
    "app_id",
    "billing_address",
    "browser_ip",
    "buyer_accepts_marketing",
    "cancel_reason",
    "cancelled_at",
    "cart_token",
    "checkout_id",
    "checkout_token",
    "client_details",
    "closed_at",
    "confirmed",
    "contact_email",
    "created_at",
    "currency",
    "customer",
    "customer_locale",
    "device_id",
    "discount_codes",
    "email",
    "financial_status",
    "fulfillment_status",
    "fulfillments",
    "gateway",
    "landing_site",
    "landing_site_ref",
    "line_items",
    "location_id",
    "name",
    "note",
    "note_attributes",
    "number",
    "order_number",
    "order_status_url",
    "payment_details",
    "payment_gateway_names",
    "phone",
    "processed_at",
    "processing_method",
    "reference",
    "referring_site",
    "refunds",
    "shipping_address",
    "shipping_lines",
    "source_identifier",
    "source_name",
    "source_url",
    "subtotal_price",
    "tags",
    "tax_lines",
    "taxes_included",
    "test",
    "token",
    "total_discounts",
    "total_line_items_price",
    "total_price",
    "total_price_usd",
    "total_tax",
    "total_weight",
    "transactions",
    "updated_at",
    "user_id",
    "daton_batch_runtime",
    "daton_batch_id",
    "daton_user_id",
    "record_created_timestamp_utc",
    "record_updated_timestamp_utc"
from dwh_dev.staging.raw_shopify_orders;

-- delete from dwh.prod.fact_all_orders where "marketplace" = 'SHOPIFY'
insert into dwh.prod.fact_all_orders(
    "marketplace",
    "order_pk",
    "brand",
    "seller_id",
    "sales_channel",
    "fulfillment_channel",
    "order_id",
    "external_order_id",
    "purchase_date_utc",
    "sku",
    "netsuite_item_number",
    "product_name",
    "order_status",
    "order_item_status",
    "quantity",
    "country_code",
    "currency",
    "item_price_per_unit",
    "gift_wrap_price_per_unit",
    "shipping_price_per_unit",
    "item_promotion_discount_per_unit",
    "ship_promotion_discount_per_unit",
    "item_tax_lc",
    "shipping_tax_lc",
    "gift_wrap_tax_lc",
    "total_tax_per_unit_lc",
    "total_tax_lc",
    "item_promotion_discount_lc",
    "ship_promotion_discount_lc",
    "gross_revenue_lc",
    "is_replacement_or_disposition",
    "innovation_flag",
    "source_table",
    "brand_timezone",
    "record_created_timestamp_utc",
    "record_updated_timestamp_utc"
)
select
    "marketplace",
    "order_pk",
    "brand",
    "seller_id",
    "sales_channel",
    "fulfillment_channel",
    "order_id",
    "external_order_id",
    "purchase_date_utc",
    "sku",
    "netsuite_item_number",
    "product_name",
    "order_status",
    "order_item_status",
    "quantity",
    "country_code",
    "currency",
    "item_price_per_unit",
    "gift_wrap_price_per_unit",
    "shipping_price_per_unit",
    "item_promotion_discount_per_unit",
    "ship_promotion_discount_per_unit",
    "item_tax_lc",
    "shipping_tax_lc",
    "gift_wrap_tax_lc",
    "total_tax_per_unit_lc",
    "total_tax_lc",
    "item_promotion_discount_lc",
    "ship_promotion_discount_lc",
    "gross_revenue_lc",
    "is_replacement_or_disposition",
    "innovation_flag",
    "source_table",
    "brand_timezone",
    "record_created_timestamp_utc",
    "record_updated_timestamp_utc"
from dwh_dev.prod.fact_all_orders_new_arch where "marketplace" = 'SHOPIFY';
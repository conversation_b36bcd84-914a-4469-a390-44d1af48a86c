-- use role transform_role

-- dwh.staging.raw_shopify_orders definition
-- Drop table
-- DROP TABLE dwh.staging.raw_shopify_orders;

create table dwh.staging.raw_shopify_orders if not exists (
	"id" FLOAT,
	"brand_name" VARCHAR(16777216),
	"shopify_store" VARCHAR(16777216),
	"admin_graphql_api_id" VARCHAR(16777216),
	"app_id" NUMBER(38,0),
	"billing_address" VARIANT,
	"browser_ip" VARCHAR(16777216),
	"buyer_accepts_marketing" BOOLEAN,
	"cancel_reason" VARCHAR(16777216),
	"cancelled_at" VARCHAR(16777216),
	"cart_token" VARCHAR(16777216),
	"checkout_id" FLOAT,
	"checkout_token" VARCHAR(16777216),
	"client_details" VARIANT,
	"closed_at" VARCHAR(16777216),
	"confirmed" BOOLEAN,
	"contact_email" VARCHAR(16777216),
	"created_at" VARCHAR(16777216),
	"currency" VARCHAR(16777216),
	"customer" VARIANT,
	"customer_locale" VARCHAR(16777216),
	"device_id" FLOAT,
	"discount_codes" VARIANT,
	"email" VARCHAR(16777216),
	"financial_status" VARCHAR(16777216),
	"fulfillment_status" VARCHAR(16777216),
	"fulfillments" VARIANT,
	"gateway" VARCHAR(16777216),
	"landing_site" VARCHAR(16777216),
	"landing_site_ref" VARCHAR(16777216),
	"line_items" VARIANT,
	"location_id" FLOAT,
	"name" VARCHAR(16777216),
	"note" VARCHAR(16777216),
	"note_attributes" VARIANT,
	"number" NUMBER(38,0),
	"order_number" VARCHAR(16777216),
	"order_status_url" VARCHAR(16777216),
	"payment_details" VARIANT,
	"payment_gateway_names" VARCHAR(16777216),
	"phone" VARCHAR(16777216),
	"processed_at" VARCHAR(16777216),
	"processing_method" VARCHAR(16777216),
	"reference" VARCHAR(16777216),
	"referring_site" VARCHAR(16777216),
	"refunds" VARIANT,
	"shipping_address" VARIANT,
	"shipping_lines" VARIANT,
	"source_identifier" VARCHAR(16777216),
	"source_name" VARCHAR(16777216),
	"source_url" VARCHAR(16777216),
	"subtotal_price" FLOAT,
	"tags" VARCHAR(16777216),
	"tax_lines" VARIANT,
	"taxes_included" BOOLEAN,
	"test" BOOLEAN,
	"token" VARCHAR(16777216),
	"total_discounts" NUMBER(8,2),
	"total_line_items_price" FLOAT,
	"total_price" FLOAT,
	"total_price_usd" FLOAT,
	"total_tax" NUMBER(6,2),
	"total_weight" NUMBER(38,0),
	"transactions" VARIANT,
	"updated_at" TIMESTAMP_NTZ(9),
	"user_id" FLOAT,
	"daton_batch_runtime" NUMBER(38,0),
	"daton_batch_id" NUMBER(38,0),
	"daton_user_id" NUMBER(38,0),
	"record_created_timestamp_utc" TIMESTAMP_NTZ(9),
	"record_updated_timestamp_utc" TIMESTAMP_NTZ(9),
	"file_name" VARCHAR(16777216)
)
DATA_RETENTION_TIME_IN_DAYS=14
COMMENT='This table is keep track of raw shopify orders dataset (https://shopify.dev/api/admin-rest/2021-10/resources/order#resource-object)';

-- $curated_db.fact_all_orders definition
-- truncate table
-- TRUNCATE $curated_db.fact_all_orders;
-- create table $curated_db.fact_all_orders if not exists(
-- 	"marketplace" VARCHAR(21),
-- 	"order_pk" VARCHAR(50),
-- 	"brand" VARCHAR(16777216),
-- 	"seller_id" VARCHAR(16777216),
-- 	"sales_channel" VARCHAR(16777216),
-- 	"fulfillment_channel" VARCHAR(3),
-- 	"order_id" VARCHAR(16777216),
-- 	"external_order_id" VARCHAR(16777216),
-- 	"purchase_date_utc" TIMESTAMP_NTZ(9),
-- 	"sku" VARCHAR(16777216),
-- 	"netsuite_item_number" VARCHAR(16777216),
-- 	"product_name" VARCHAR(16777216),
-- 	"order_status" VARCHAR(16777216),
-- 	"order_item_status" VARCHAR(16777216),
-- 	"quantity" NUMBER(38,0),
-- 	"country_code" VARCHAR(16777216),
-- 	"currency" VARCHAR(16777216),
-- 	"item_price_per_unit" FLOAT,
-- 	"gift_wrap_price_per_unit" FLOAT,
-- 	"shipping_price_per_unit" FLOAT,
-- 	"item_promotion_discount_per_unit" FLOAT,
-- 	"ship_promotion_discount_per_unit" FLOAT,
-- 	"item_tax_lc" FLOAT,
-- 	"shipping_tax_lc" FLOAT,
-- 	"gift_wrap_tax_lc" FLOAT,
-- 	"total_tax_per_unit_lc" FLOAT,
-- 	"total_tax_lc" FLOAT,
-- 	"item_promotion_discount_lc" FLOAT,
-- 	"ship_promotion_discount_lc" FLOAT,
-- 	"gross_revenue_lc" FLOAT,
-- 	"is_replacement_or_disposition" NUMBER(1,0),
-- 	"innovation_flag" BOOLEAN,
-- 	"daton_batch_runtime" NUMBER(38,17),
-- 	"source_table" VARCHAR(93),
-- 	"brand_timezone" VARCHAR(16777216),
-- 	"record_created_timestamp_utc" TIMESTAMP_NTZ(9),
-- 	"record_updated_timestamp_utc" TIMESTAMP_NTZ(9)
-- )
-- DATA_RETENTION_TIME_IN_DAYS=14
-- COMMENT='This table is used as primary dataset to get orders and line_items based on order data from Amazon and shopify.';
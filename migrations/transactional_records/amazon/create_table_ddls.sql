create TABLE DWH_DEV.PROD.AMAZON_TRANSACTIONAL_RECORDS IF NOT EXISTS (
	"item_pk" VARCHAR(16777216),
	"seller_id" VARCHAR(16777216),
	"brand_name" VARCHAR(16777216),
	"marketplace" VARCHAR(16777216),
	"source_type" VARCHAR(16777216),
	"connector_region" VARCHAR(16777216),
	"country" VARCHAR(16777216),
	"fnsku" VARCHAR(16777216),
	"sku" VARCHAR(16777216),
	"product_name" VARCHAR(16777216),
	"asin" VARCHAR(16777216),
	"last_encountered_date" DATE,
	"record_created_timestamp_utc" TIMESTAMP_NTZ(9),
	"record_updated_timestamp_utc" TIMESTAMP_NTZ(9)
)
DATA_RETENTION_TIME_IN_DAYS=14
COMMENT='This table is used as primary dataset to track transactional records from amazon';
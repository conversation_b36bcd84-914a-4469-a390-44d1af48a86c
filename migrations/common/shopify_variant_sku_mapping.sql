create or replace TABLE dwh.common.shopify_variant_sku_mapping (
	"brand_name" VARCHAR(16777216) NOT NULL,
	"variant_id" NUMBER(38,0) NOT NULL,
	"sku" VARCHAR(16777216) NOT NULL,
	"first_encountered_at" TIMESTAMP_NTZ(9) NOT NULL,
  "record_created_timestamp_utc" TIMESTAMP_NTZ(9) NOT NULL,
  "record_updated_timestamp_utc" TIMESTAMP_NTZ(9) NOT NULL
);

insert into dwh.common.shopify_variant_sku_mapping (
	"brand_name",
	"variant_id",
	"sku",
	"first_encountered_at",
	"record_created_timestamp_utc",
	"record_updated_timestamp_utc" 
)
select
"brand_name",
	"variant_id",
	"sku",
	"first_encountered_at",
	"record_created_timestamp_utc",
	"record_updated_timestamp_utc" 
from
dwh_dev.common.shopify_variant_sku_mapping;
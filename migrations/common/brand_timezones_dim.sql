create TABLE dwh.prod.BRAND_TIMEZONES_DIM if not exists(
	"brand" VARCHAR(16777216) NOT NULL,
	"marketplace" VARCHAR(16777216) NOT NULL,
	"data_identifier" VARCHAR(16777216) NOT NULL,
	"country_code" VARCHAR(16777216) NOT NULL,
	"time_zone" VARCHAR(16777216) NOT NULL,
	"snowflake_time_zone" VARCHAR(16777216) NOT NULL,
	"delta_hours_utc" NUMBER(38,0) NOT NULL
);

-- truncate table if exists dwh.prod.BRAND_TIMEZONES_DIM;

INSERT INTO dwh.prod.BRAND_TIMEZONES_DIM
("brand", "marketplace", "data_identifier", "country_code", "time_zone", "snowflake_time_zone", "delta_hours_utc")
select 
"brand", "marketplace", "data_identifier", "country_code", "time_zone", "snowflake_time_zone", "delta_hours_utc"
from  ANALYTICS.COMMON.BRAND_TIMEZONES_DIM;

create or replace TABLE dwh.common.shopify_order_line_item_updates (
	"brand_name" VARCHAR(16777216),
	"id" FLOAT,
	"line_item_id" NUMBER(38,0),
	"variant_id" NUMBER(38,0),
	"sku" VARCHAR(16777216),
	"derived_sku" VARCHAR(16777216),
  "record_updated_timestamp_utc" TIMESTAMP_NTZ(9)
);

insert into dwh.common.shopify_order_line_item_updates (
	"brand_name",
	"id",
	"line_item_id",
	"variant_id",
	"sku",
	"derived_sku",
  	"record_updated_timestamp_utc"
)
select "brand_name",
	"id",
	"line_item_id",
	"variant_id",
	"sku",
	"derived_sku",
  	"record_updated_timestamp_utc"
from
dwh_dev.common.shopify_order_line_item_updates
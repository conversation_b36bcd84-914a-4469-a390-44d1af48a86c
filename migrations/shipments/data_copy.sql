truncate table if exists dwh.prod.FACT_ALL_SHIPMENTS definition
INSERT INTO dwh.prod.FACT_ALL_SHIPMENTS (
    "shipment_pk", 
    "marketplace", 
    "brand", 
    "seller_id", 
    "order_id", 
    "external_order_id", 
    "order_item_id", 
    "shipment_id", 
    "shipment_item_id", 
    "purchase_date_utc", 
    "sku", 
    "netsuite_item_number", 
    "product_name", 
    "quantity_ordered", 
    "item_price_per_unit", 
    "shipping_price_per_unit", 
    "order_status", 
    "order_item_status", 
    "country_code", 
    "currency", 
    "shipment_date_utc", 
    "quantity_shipped", 
    "daton_batch_runtime", 
    "brand_timezone", 
    "record_created_timestamp_utc", 
    "record_updated_timestamp_utc", 
    "source_table", 
    "fulfillment_channel", 
    "innovation_flag"
)
SELECT 
    "shipment_pk", 
    "marketplace", 
    "brand", 
    "seller_id", 
    "order_id", 
    "external_order_id", 
    "order_item_id", 
    "shipment_id", 
    "shipment_item_id", 
    "purchase_date_utc", 
    "sku", 
    "netsuite_item_number", 
    "product_name", 
    "quantity_ordered", 
    "item_price_per_unit", 
    "shipping_price_per_unit", 
    "order_status", 
    "order_item_status", 
    "country_code", 
    "currency", 
    "shipment_date_utc", 
    "quantity_shipped", 
    "daton_batch_runtime", 
    "brand_timezone", 
    "record_created_timestamp_utc", 
    "record_updated_timestamp_utc", 
    "source_table", 
    "fulfillment_channel", 
    "innovation_flag"
FROM dwh_dev.prod.fact_all_shipments_new_arch;

truncate table if exists dwh.staging.AMAZON_FBA_FULFILLED_SHIPMENTS;
insert into dwh.staging.AMAZON_FBA_FULFILLED_SHIPMENTS (
    "shipment_pk",
    "marketplace_id",
    "marketplace_name",
    "seller_id",
    "amazon_order_id",
    "merchant_order_id",
    "shipment_id",
    "shipment_item_id",
    "amazon_order_item_id",
    "merchant_order_item_id",
    "purchase_date",
    "payments_date",
    "shipment_date",
    "reporting_date",
    "buyer_email",
    "buyer_name",
    "buyer_phone_number",
    "sku",
    "product_name",
    "quantity_shipped",
    "currency",
    "item_price",
    "item_tax",
    "shipping_price",
    "shipping_tax",
    "gift_wrap_price",
    "gift_wrap_tax",
    "ship_service_level",
    "recipient_name",
    "ship_address_1",
    "ship_address_2",
    "ship_address_3",
    "ship_city",
    "ship_state",
    "ship_postal_code",
    "ship_country",
    "ship_phone_number",
    "bill_address_1",
    "bill_address_2",
    "bill_address_3",
    "bill_city",
    "bill_state",
    "bill_postal_code",
    "bill_country",
    "item_promotion_discount",
    "ship_promotion_discount",
    "carrier",
    "tracking_number",
    "estimated_arrival_date",
    "fulfillment_center_id",
    "fulfillment_channel",
    "sales_channel",
    "daton_batch_runtime",
    "record_created_timestamp_utc",
    "record_updated_timestamp_utc",
    "source_table"
)
select 
    "shipment_pk",
    "marketplace_id",
    "marketplace_name",
    "seller_id",
    "amazon_order_id",
    "merchant_order_id",
    "shipment_id",
    "shipment_item_id",
    "amazon_order_item_id",
    "merchant_order_item_id",
    "purchase_date",
    "payments_date",
    "shipment_date",
    "reporting_date",
    "buyer_email",
    "buyer_name",
    "buyer_phone_number",
    "sku",
    "product_name",
    "quantity_shipped",
    "currency",
    "item_price",
    "item_tax",
    "shipping_price",
    "shipping_tax",
    "gift_wrap_price",
    "gift_wrap_tax",
    "ship_service_level",
    "recipient_name",
    "ship_address_1",
    "ship_address_2",
    "ship_address_3",
    "ship_city",
    "ship_state",
    "ship_postal_code",
    "ship_country",
    "ship_phone_number",
    "bill_address_1",
    "bill_address_2",
    "bill_address_3",
    "bill_city",
    "bill_state",
    "bill_postal_code",
    "bill_country",
    "item_promotion_discount",
    "ship_promotion_discount",
    "carrier",
    "tracking_number",
    "estimated_arrival_date",
    "fulfillment_center_id",
    "fulfillment_channel",
    "sales_channel",
    "daton_batch_runtime",
    "record_created_timestamp_utc",
    "record_updated_timestamp_utc",
    "source_table"
FROM dwh_dev.staging.AMAZON_FBA_FULFILLED_SHIPMENTS;

truncate table if exists dwh.staging.PARK_AMAZON_SHIPMENTS_WITH_NO_ORDERS;
INSERT INTO dwh.staging.PARK_AMAZON_SHIPMENTS_WITH_NO_ORDERS
("order_id", "sku", "order_item_id", "shipment_id", "shipment_item_id", "shipments_country_code", "shipments_currency", "shipment_date_utc", "quantity_shipped", "seller_id", "product_name", "daton_batch_runtime")
select 
"order_id", "sku", "order_item_id", "shipment_id", "shipment_item_id", "shipments_country_code", "shipments_currency", "shipment_date_utc", "quantity_shipped", "seller_id", "product_name", "daton_batch_runtime"
from dwh_dev.staging.PARK_AMAZON_SHIPMENTS_WITH_NO_ORDERS;
-- use role transform_role

-- dwh.prod.FACT_ALL_SHIPMENTS definition
-- drop table if exists dwh.prod.FACT_ALL_SHIPMENTS;

create TABLE dwh.prod.FACT_ALL_SHIPMENTS IF NOT EXISTS(
	"shipment_pk" VARCHAR(32),
	"marketplace" VARCHAR(21),
	"brand" VARCHAR(16777216),
	"seller_id" VARCHAR(16777216),
	"order_id" VARCHAR(16777216),
	"external_order_id" VARCHAR(16777216),
	"order_item_id" VARCHAR(16777216),
	"shipment_id" VARCHAR(16777216),
	"shipment_item_id" VARCHAR(16777216),
	"purchase_date_utc" TIMESTAMP_NTZ(9),
	"sku" VARCHAR(16777216),
	"netsuite_item_number" VARCHAR(16777216),
	"product_name" VARCHAR(16777216),
	"quantity_ordered" NUMBER(38,0),
	"item_price_per_unit" FLOAT,
	"shipping_price_per_unit" FLOAT,
	"order_status" VARCHAR(16777216),
	"order_item_status" VARCHAR(16777216),
	"country_code" VARCHAR(16777216),
	"currency" VARCHAR(16777216),
	"shipment_date_utc" TIMESTAMP_NTZ(9),
	"quantity_shipped" NUMBER(38,0),
	"daton_batch_runtime" VARCHAR(16777216),
	"brand_timezone" VARCHAR(16777216),
	"record_created_timestamp_utc" TIMESTAMP_NTZ(9),
	"record_updated_timestamp_utc" TIMESTAMP_NTZ(9),
	"source_table" VARCHAR(16777216),
	"fulfillment_channel" VARCHAR(16777216),
	"innovation_flag" BOOLEAN
)
DATA_RETENTION_TIME_IN_DAYS=14
COMMENT='This table is used as primary dataset to track shipment data for Amazon and shopify sellers.';

-- dwh.staging.AMAZON_FBA_FULFILLED_SHIPMENTS definition
-- drop table if exists dwh.staging.AMAZON_FBA_FULFILLED_SHIPMENTS;

create TABLE dwh.staging.AMAZON_FBA_FULFILLED_SHIPMENTS IF NOT EXISTS(
	"shipment_pk" VARCHAR(32),
	"marketplace_id" VARCHAR(16777216),
	"marketplace_name" VARCHAR(16777216),
	"seller_id" VARCHAR(16777216),
	"amazon_order_id" VARCHAR(16777216),
	"merchant_order_id" VARCHAR(16777216),
	"shipment_id" VARCHAR(16777216),
	"shipment_item_id" VARCHAR(16777216),
	"amazon_order_item_id" NUMBER(38,0),
	"merchant_order_item_id" VARCHAR(16777216),
	"purchase_date" VARCHAR(16777216),
	"payments_date" VARCHAR(16777216),
	"shipment_date" VARCHAR(16777216),
	"reporting_date" VARCHAR(16777216),
	"buyer_email" VARCHAR(16777216),
	"buyer_name" VARCHAR(16777216),
	"buyer_phone_number" VARCHAR(16777216),
	"sku" VARCHAR(16777216),
	"product_name" VARCHAR(16777216),
	"quantity_shipped" NUMBER(38,0),
	"currency" VARCHAR(16777216),
	"item_price" FLOAT,
	"item_tax" FLOAT,
	"shipping_price" FLOAT,
	"shipping_tax" FLOAT,
	"gift_wrap_price" FLOAT,
	"gift_wrap_tax" FLOAT,
	"ship_service_level" VARCHAR(16777216),
	"recipient_name" VARCHAR(16777216),
	"ship_address_1" VARCHAR(16777216),
	"ship_address_2" VARCHAR(16777216),
	"ship_address_3" VARCHAR(16777216),
	"ship_city" VARCHAR(16777216),
	"ship_state" VARCHAR(16777216),
	"ship_postal_code" VARCHAR(16777216),
	"ship_country" VARCHAR(16777216),
	"ship_phone_number" VARCHAR(16777216),
	"bill_address_1" VARCHAR(16777216),
	"bill_address_2" VARCHAR(16777216),
	"bill_address_3" VARCHAR(16777216),
	"bill_city" VARCHAR(16777216),
	"bill_state" VARCHAR(16777216),
	"bill_postal_code" VARCHAR(16777216),
	"bill_country" VARCHAR(16777216),
	"item_promotion_discount" FLOAT,
	"ship_promotion_discount" FLOAT,
	"carrier" VARCHAR(16777216),
	"tracking_number" VARCHAR(16777216),
	"estimated_arrival_date" VARCHAR(16777216),
	"fulfillment_center_id" VARCHAR(16777216),
	"fulfillment_channel" VARCHAR(16777216),
	"sales_channel" VARCHAR(16777216),
	"daton_batch_runtime" NUMBER(38,0),
	"record_created_timestamp_utc" TIMESTAMP_NTZ(9),
	"record_updated_timestamp_utc" TIMESTAMP_NTZ(9),
	"source_table" VARCHAR(16777216)
)
DATA_RETENTION_TIME_IN_DAYS=14
COMMENT='This table contains data from the AMAZON_FBA_FULFILLED_SHIPMENTS feed';


-- dwh.staging.PARK_AMAZON_SHIPMENTS_WITH_NO_ORDERS definition
-- drop table if exists dwh.staging.PARK_AMAZON_SHIPMENTS_WITH_NO_ORDERS;

create or replace TABLE dwh.staging.PARK_AMAZON_SHIPMENTS_WITH_NO_ORDERS (
	"order_id" VARCHAR(16777216),
	"sku" VARCHAR(16777216),
	"order_item_id" NUMBER(38,0),
	"shipment_id" VARCHAR(16777216),
	"shipment_item_id" VARCHAR(16777216),
	"shipments_country_code" VARCHAR(16777216),
	"shipments_currency" VARCHAR(16777216),
	"shipment_date_utc" VARCHAR(16777216),
	"quantity_shipped" NUMBER(38,0),
	"seller_id" VARCHAR(16777216),
	"product_name" VARCHAR(16777216),
	"daton_batch_runtime" NUMBER(38,0)
)
DATA_RETENTION_TIME_IN_DAYS=14
COMMENT='This table contains amazon shipment records not having a corresponding order';
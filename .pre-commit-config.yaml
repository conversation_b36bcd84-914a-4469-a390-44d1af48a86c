# /!\ Don't forget to update .gitlab/ci/images/files/pre-commit-config.yaml
#     if you add a new hook to this file so that it can be pre-installed
#     in the linter image
#
# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    -   id: check-merge-conflict
    -   id: trailing-whitespace
        exclude: .+.patch$
    -   id: end-of-file-fixer
    -   id: check-yaml
        args: ['--unsafe', '--allow-multiple-documents']
        exclude: templates\/.+.yaml$|pnpm-lock.yaml|helmfile.yaml
    -   id: check-json
    -   id: check-added-large-files
        args: ['--maxkb=750']
-   repo: https://github.com/psf/black
    rev: 23.9.1
    hooks:
    -   id: black
-   repo: https://github.com/sqlfluff/sqlfluff
    rev: 2.3.2
    hooks:
    -   id: sqlfluff-lint
        additional_dependencies: ['dbt-bigquery==1.5.9', 'dbt-core==1.5.11', 'sqlfluff-templater-dbt==2.3.2']
    -   id: sqlfluff-fix
        args: [--rules, "LT01,LT02,LT04,LT06,LT07,LT08,LT09,LT10,LT11,LT12,CP01,CP02,CP03,CP04,CP05,CV03,CV10,AM05"]
        additional_dependencies: ['dbt-bigquery==1.5.9', 'dbt-core==1.5.11', 'sqlfluff-templater-dbt==2.3.2']
-   repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.83.4
    hooks:
    -   id: terraform_fmt
-   repo: https://github.com/terraform-docs/terraform-docs
    rev: v0.16.0
    hooks:
      - id: terraform-docs-go
        args: ["-c", "infra/gcp/terraform/modules/terraform-docs.yml", "infra/gcp/terraform/modules/airflow/"]
      - id: terraform-docs-go
        args: ["-c", "infra/gcp/terraform/modules/terraform-docs.yml", "infra/gcp/terraform/modules/fivetran/"]
      - id: terraform-docs-go
        args: ["-c", "infra/gcp/terraform/modules/terraform-docs.yml", "infra/gcp/terraform/modules/secret_factory/"]
      - id: terraform-docs-go
        args: ["-c", "infra/gcp/terraform/modules/terraform-docs.yml", "infra/gcp/terraform/modules/service_account_factory/"]
-   repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.6
    hooks:
    -   id: shellcheck
-   repo: https://github.com/zricethezav/gitleaks
    rev: v8.18.0
    hooks:
    -   id: gitleaks
        args: ["--config", ".gitleaks.toml"]
-   repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
    -  id: isort
       args: ["--profile", "black", "--filter-files"]
-   repo: local
    hooks:
    -   id: update_sellers
        name: Updating .json files for sellers
        entry: python data/pipelines/scripts/sellers/update_seller_data_json_files.py
        language: python
        additional_dependencies: [ pandas==1.5.3, numpy==1.24.2, pyyaml==6.0 ]
        files: '^data\/models\/data\/core\/amazon\/amazon_(advertising_|seller_)[a-z_.]+.csv'
    -   id: update_models
        name: Updating .sql files for advertising reports
        entry: python data/pipelines/scripts/sellers/update_seller_data_model_files.py
        language: python
        additional_dependencies: [ pandas==1.5.3, numpy==1.24.2, pyyaml==6.0 ]
        files: 'data/pipelines/plugins/data/advertising_reports.json'
    -   id: schema-sorter
        name: Sort .json schema definition in plugins/schema
        entry: python data/pipelines/scripts/fix_schemas/schema_sorter.py
        language: python
        files: ^data\/pipelines\/plugins/schemas\/.+\.json$
    -   id: bazel-buildifier
        name: buildifier
        description: Runs bazel buildifier, requires buildifier binary
        entry: buildifier
        files: '^(.*/)?(BUILD\.bazel|BUILD|WORKSPACE)$|\.BUILD$|\.bzl$'
        language: system
    -   id: go-fmt
        name: go-fmt
        description: Format *.go files, requires Golang
        entry: apps/tools/go-fmt.sh
        language: script
        files: \.go$

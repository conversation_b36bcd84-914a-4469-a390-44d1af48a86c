import sys
import pyspark.sql
from commonlibs.custom_logging.logger import get_logger
from commonlibs.db_connectors.snowflake import Snowflake
from commonlibs.db_connectors.postgres_pg8k import Postgres
from commonlibs.aws.s3 import S3
from pyspark.sql.functions import *
from awsglue.context import GlueContext
from awsglue.utils import getResolvedOptions
from pyspark.context import SparkContext
from awsglue.job import Job
from commonlibs.helpers.helpers import *
from pyspark.sql import SQLContext
from pyspark.sql.types import *
import awswrangler as wr
from py4j.java_gateway import java_import

# initialize variables
SNOWFLAKE_SOURCE_NAME = "net.snowflake.spark.snowflake"

args = getResolvedOptions(sys.argv,
                          ['JOB_NAME',
                           'etl_job_run_id',
                           'current_etl_batch_run_time',
                           'report_name',
                           'stage_table_name',
                           'stage_table_schema',
                           'stage_data_s3_location',
                           'region_name'
                           ])
sc = SparkContext()
glueContext = GlueContext(sc)
spark = glueContext.sparkSession
spark_sql = SQLContext(sc)
job = Job(glueContext)

job.init(args['JOB_NAME'], args)

# These imports are specific to SNOWFLAKE spark dependent JARS
java_import(spark._jvm, SNOWFLAKE_SOURCE_NAME)
spark._jvm.net.snowflake.spark.snowflake.SnowflakeConnectorUtils.enablePushdownSession(
    spark._jvm.org.apache.spark.sql.SparkSession.builder().getOrCreate())

log = get_logger(args.get('JOB_NAME'))

region_name = args.get('region_name')
sfc = Snowflake(secret_name='prod/snowflake/transform_user', region_name=region_name)
pgc = Postgres(secret_name='dataplatform/aurorapostgres/etl_user', region_name=region_name)


class SpReportIngest(object):

    def __init__(self):
        self.report_name = args.get('report_name')
        self.report_api = 'SP'
        self.channel = 'Amazon'
        self.stage_table_schema = args.get('stage_table_schema')
        self.stage_table_name = args.get('stage_table_name')

    def get_previous_failed_files(self, report_metadata_id):
        """
        This method will retrieve previously failed reports for a given report_metadata_id. These files
        will also get re-tried during current run
        :param report_metadata_id:
        :return:
        """
        log.info("Retrieving previously failed files")
        # use the UDF to get failed reports for the current seller
        query = f"""SELECT * FROM workflow_configurations.udf_get_failed_reports('{report_metadata_id}')
        """
        data, _ = pgc.get_data(query, as_dict=True)
        return data

    def delete_previous_failed_files(self, file_name):
        """
            This method will delete data from workflow_configurations.failed_reports_info table for given file_name
        :param file_name:
        :return:
        """

        # call the stored procedure to delete data from previous_failed_reports table
        query = f"""call workflow_configurations.sp_delete_previous_failed_reports('{file_name}') """
        pgc.execute_statement(query)

    def update_failed_files(self, report_id, report_metadata_id, etl_job_run_id, file_name):
        """
        This method will add the files that failed to process during current run to workflow_configurations.failed_reports_info
        table
        :param report_metadata_id: report_metadata_id
        :param etl_job_run_id: current_etl_batch_run_id
        :param file_name: s3 object path (complete path)
        :param report_id: report_id
        :return: None
        """

        query = f""" call workflow_configurations.sp_set_failed_reports('{report_metadata_id}', '{etl_job_run_id}', '{file_name}', '{report_id}')"""
        pgc.execute_statement(query)

    def get_seller_report_metadata_info(self):
        """
            This method will get all the configurations that are required to process a given report for sellers
        :return: Configurations required for processing reports
        """

        query = """SELECT * FROM workflow_configurations.udf_get_seller_report_metadata('{report_name}','{channel}','{report_api}')""".format(report_name=self.report_name,
                                                                                                                                            channel=self.channel,
                                                                                                                                            report_api=self.report_api)

        data, _ = pgc.get_data(query, as_dict=True)

        return [{k: convert_to_utc_aware(v) if k == 'etl_batch_run_time' else v for k, v in row.items()} for row in data]

    def get_qualifying_reports_to_process(self, bucket, prefix, suffix, last_batch_run_time, current_batch_run_time):
        """
        This method will get the new s3 files that need to be processed for the report
        :param bucket:
        :param prefix:
        :param suffix:
        :param last_batch_run_time:
        :param current_batch_run_time:
        :return:
        """
        s3 = S3()
        return s3.get_files_to_process(bucket, prefixes=prefix, suffixes=suffix,
                                       last_batch_run_time=last_batch_run_time,
                                       current_batch_run_time=current_batch_run_time)

    def stage_data(self, dataset_location, seller_code, write_mode='append', truncate_stage_table=False):
        """
        This method will stage data to snowflake using snowflake spark connector. spark data frame
        will be written directly
        :param dataset_location: s3 location from where data frame need to be created
        :param seller_code: seller code for which data will be staged
        :param write_mode: default is append. It can be overwrite
        :param truncate_stage_table: This flag is set to False. If its set to true, the write_mode will be set
        to overwrite mode
        :return:
        """
        sfOptions = wr.secretsmanager.get_secret_json("prod/sparksnowflake/transform_user")
        db_schema = self.stage_table_schema.split('.')
        staging_db = db_schema[0].upper()
        staging_schema = db_schema[1].upper()
        current_etl_batch_run_time = args.get('current_etl_batch_run_time')

        sfOptions['sfDatabase'] = db_schema[0]
        sfOptions['sfSchema'] = db_schema[1]

        log.info(f"Reading dataset to stage from {dataset_location}seller_code={seller_code}")
        df = spark.read.parquet(f'{dataset_location}seller_code={seller_code}/')

        # filter only the new files that are to be processed
        df = df.filter(df.etl_batch_run_time == lit(args.get('current_etl_batch_run_time')))

        # Drop file_key column
        df = df.drop('file_key')

        # Check if stage table already exists. If so, set the overwrite to append mode
        query = f"""select 1 as table_exists
                    from {staging_db}.INFORMATION_SCHEMA.TABLES
                    where TABLE_SCHEMA = '{staging_schema}'
                    AND TABLE_NAME = '{self.stage_table_name.upper()}'
        """
        data_, _ = sfc.get_data(query, as_dict=True)

        if not data_:
            write_mode = 'overwrite'
        else:
            write_mode = 'overwrite' if data_ and truncate_stage_table else 'append'

        if data_ and not truncate_stage_table:
            # Delete data from stage if already existing for current run. This could happen if there is a retry
            query = f"""DELETE FROM {self.stage_table_schema}.{self.stage_table_name} 
                        WHERE ETL_BATCH_RUN_TIME = '{current_etl_batch_run_time}'
                        AND seller_code = '{seller_code}'
                    """
            sfc.execute_statement(query)

        # check if prior run completed. When the run is complete, staging table shouldn't have any data
        log.info(f"Writing dataframe to {self.stage_table_schema}.{self.stage_table_name} with write_mode={write_mode}")
        try:
            df.write.format(SNOWFLAKE_SOURCE_NAME).options(**sfOptions).option("dbtable", self.stage_table_name).mode(write_mode).save()

        except Exception as err:
            msg = traceback_fmt(err)
            log.info(msg)
            raise

    def update_current_run_row_count(self, current_etl_batch_run_time):
        """
        This method will update etl_batch_run_time table with the number of rows inserted for a report
        during the current run
        :param current_etl_batch_run_time:
        :return:
        """

        # query stage table to get the number of rows inserted for current run
        query = f"""
                SELECT COUNT(1) AS "row_count"
                FROM {self.stage_table_schema}.{self.stage_table_name}
                WHERE etl_batch_run_time = '{current_etl_batch_run_time}'
                """
        data, _ = sfc.get_data(query, as_dict=True)

        if data:
            row_count = data[0].get('row_count')
        else:
            row_count = 0

        # update the row count in etl_job_runs table
        query = f"""
                    UPDATE workflow_configurations.etl_job_runs
                        SET rows_inserted = COALESCE({row_count},0)
                    WHERE etl_job_run_id = '{args.get('etl_job_run_id')}'
            """
        pgc.execute_statement(query)

    def update_report_latest_process_info(self, report_metadata_id, etl_job_run_id, current_etl_batch_run_time):
        """
            This method will update the etl_batch_run_time and etl_job_run_id for a given report_metadata_id
        """
        log.info(f"Updating etl batch run time for report_metadata_id: {report_metadata_id}")
        query = f"""call workflow_configurations.sp_set_report_latest_process_info('{report_metadata_id}','{etl_job_run_id}', '{current_etl_batch_run_time}') """
        pgc.execute_statement(query)

    def process_data(self):
        """
        This is the core method that will process a given report for all sellers and following are the steps
        Seller report metadata is pulled from database for a give parent report, channel and api grouping
        For each seller report metadata row
            The process checks for new files that are required to be processed for that seller. This is done by taking
            the last etl_batch_run and current_etl_batch_run and scanning s3 for files that are in between these two
            run times.
            Process will check for files that were not processed earlier (not recorded as successful process in the
            earlier run) and adds those files for processing and deletes the information from report_process_info table

            Before processing a file, a new entry is made into report_process_info with current etl_job_run_id, report_metadata_id
            and file_information and status is set to false

            File is cleansed and compressed. New columns will be added to the dataframe (seller_id, seller_code, source_file)
            and pushed back to s3

            report_process_info is updated and is_successful set to True

        After all the files are processed for seller, last_etl_batch_run_time is updated with current_etl_batch_run_time

        :return:
        """

        log.info("""Parameters received are .....
            Stage Table Name: {stage_table_name}
            Stage Table Schema: {stage_table_schema}
            Report Name: {report_name}
            Current ETL Batch Time: {current_etl_batch_run_time}
            ETL Run Id: {etl_job_run_id}
            Stage S3 Location:{stage_data_s3_location}""".format(stage_table_name=args.get('stage_table_name'),
                                                                 stage_table_schema=args.get('stage_table_schema'),
                                                                 report_name=args.get('report_name'),
                                                                 current_etl_batch_run_time=args.get('current_etl_batch_run_time'),
                                                                 etl_job_run_id=args.get('etl_job_run_id'),
                                                                 stage_data_s3_location=args.get('stage_data_s3_location')
                                                                 ))

        log.info(f'{self.report_name} is being processed for all sellers')
        # Get all the configurations to process given report for all the active sellers
        seller_report_information = self.get_seller_report_metadata_info()

        # For each seller get the reports to be processed
        for seller_info in seller_report_information:
            seller_code = seller_info.get('seller_code')
            bucket = seller_info.get('s3_bucket')
            prefix = seller_info.get('source_data_location')
            report_metadata_id = seller_info.get('report_metadata_id')
            suffix = seller_info.get('object_suffixes')
            last_batch_run_time_utc = convert_to_utc_aware(date_time=seller_info.get('last_etl_batch_run_time'))
            current_etl_batch_run_time_utc = convert_to_utc_aware(date_time_str=args.get('current_etl_batch_run_time'),
                                                                  strp_format='%Y-%m-%d %H:%M:%S')
            compressed_dataset_s3_location = seller_info.get('cleansed_s3_prefix')
            report_id = seller_info.get('report_id')
            etl_job_run_id = args.get('etl_job_run_id')
            last_etl_process_time_stamp = seller_info.get('last_etl_batch_run_time').strftime('%Y-%m-%d %H:00:00')

            # Get all the files to be processed from s3. Files that will be picked will have
            # last_batch_run_time <= LastModifiedS3Object < current_etl_batch_run_time
            files_to_process = []
            files_to_process = self.get_qualifying_reports_to_process(bucket, prefix, suffix, last_batch_run_time_utc,
                                                                      current_etl_batch_run_time_utc)

            # get files that were not properly processed during earlier run
            get_failed_files_list = self.get_previous_failed_files(report_metadata_id)

            # If there are files that weren't processed properly earlier, pick them
            if get_failed_files_list:
                for file_ in get_failed_files_list:
                    files_to_process.append(file_)
                    key = file_.get('key')
                    # delete file information from report_process_info
                    self.delete_previous_failed_files(key)

            log.info('Number of files to be processed for seller {}: {}'.format(seller_code, str(len(files_to_process))))
            for new_file in files_to_process:
                file_name = new_file.get('key')
                try:
                    if file_name.endswith('.csv'):
                        log.info(f"File format for {file_name} is csv")
                        df = spark.read.csv(file_name, sep=',', inferSchema=True, quote='"', escape='"', header=True)
                    else:
                        log.info(f"File format for {file_name} is parquet")
                        df = spark.read.parquet(file_name)

                    # cleanse data and add seller_code, file_name (complete s3 path) and current batch run time
                    df = df.withColumn('seller_code', lit(seller_code).cast(StringType())) \
                        .withColumn('file_name', lit(file_name).cast(StringType())) \
                        .withColumn('etl_batch_run_time',
                                    to_timestamp(lit(args.get('current_etl_batch_run_time')), 'yyyy-MM-dd HH:mm:ss'))

                    # write the data into compressed s3 data location and the partitioning keys will be seller_code and file_name
                    # without the extension. Since the name of files are named with epoch timestamp, the value is assigned to
                    # file_stamp variable by getting only the name part of the file
                    file_stamp = file_name.split('/')[-1::][0].split('.')[0]
                    cleansed_folder = f"{compressed_dataset_s3_location}seller_code={seller_code}/file_key={file_stamp}/"
                    log.info(f"Compressing and writing parquet file to the following folder: {cleansed_folder}")
                    # Since reports can be run more than one time, we are over writing the files each time the data is processed
                    df.write.mode("overwrite").parquet(cleansed_folder)

                except Exception as err:
                    log.info(f"There is an issue while processing the file: {file_name}\n.Logging into failed reports table")
                    self.update_failed_files(report_id, report_metadata_id, etl_job_run_id, file_name)
                    msg = traceback_fmt(err)
                    log.info(msg)
            if files_to_process:
                try:
                    log.info(f"""New files have been processed for {seller_code}. Staging to snowflake""")
                    self.stage_data(args.get('stage_data_s3_location'), seller_code)
                    self.update_report_latest_process_info(report_metadata_id, etl_job_run_id, current_etl_batch_run_time_utc)
                except Exception as err:
                    msg = traceback_fmt(err)
                    log.info(msg)
                    raise
        # Once the process is complete, update the current runs row count in the table
        self.update_current_run_row_count(args.get('current_etl_batch_run_time'))


if __name__ == "__main__":
    sp = SpReportIngest()
    sp.process_data()

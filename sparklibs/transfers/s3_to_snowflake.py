import pytz
import json
import argparse
from pyspark.sql.types import StructType
from pyspark.sql.dataframe import <PERSON><PERSON><PERSON><PERSON>
from pyspark.sql import SparkSession

class S3ToSnowflakeOperator(object):

    # default dataframe write options
    default_write_opts = {"column_mapping":"order","keep_column_case":"off","truncate_table":"on"}

    # format to be specified in the dataframe in the write operation
    SNOWFLAKE_SOURCE_NAME = "net.snowflake.spark.snowflake"

    def __init__(self, **kwargs):
        self.s3_connection = kwargs.get('s3_connection')
        self.s3_region = kwargs.get('s3_region')
        self.s3_folder_uris = kwargs.get('s3_folder_uris', [])
        self.s3_uri_list_file = kwargs.get('s3_uri_list_file')
        self.s3_file_uris = kwargs.get('s3_file_uris', [])
        self.s3_exclude_file_uris = kwargs.get('s3_exclude_file_uris', [])
        self.s3_file_suffix = kwargs.get('s3_file_suffix')
        self.s3_file_format = kwargs.get('s3_file_format')
        self.s3_file_encoding = kwargs.get('s3_file_encoding')
        self.s3_file_header = kwargs.get('s3_file_header')
        self.s3_dataframe_opts = kwargs.get('s3_dataframe_opts', {})
        self.s3_struct_type = kwargs.get('s3_struct_type', {})
        self.s3_struct = kwargs.get('s3_struct')

        self.snow_connection = kwargs.get('snow_connection')
        self.snow_connection_string = kwargs.get('snow_connection_string')
        self.snow_warehouse = kwargs.get('snow_warehouse')
        self.snow_schema = kwargs.get('snow_schema')
        self.snow_table = kwargs.get('snow_table_name')
        self.snow_stage_table = kwargs.get('snow_stage_table_name')
        self.snow_database = kwargs.get('snow_database')
        self.snow_save_mode = kwargs.get('snow_savemode')
        self.snow_struct = kwargs.get('snow_struct')
        self.snow_struct_file = kwargs.get('snow_struct_file')
        self.snow_struct_type = kwargs.get('snow_struct_type')
        self.snow_dataframe_opts = kwargs.get('snow_dataframe_opts', {})

        self.transform_expr = kwargs.get('transform_expr')
        self.transform_expr_delimiter = kwargs.get('transform_expr_delimiter')
        self.spark=None

    def get_s3_uri_attributes(self):
        result={}
        result['folder_uris']=self.s3_folder_uris
        result['exclude_file_uris'] = self.s3_exclude_file_uris
        result['file_suffix'] = self.s3_file_suffix
        result['region'] = self.s3_region

        return result

    def get_s3_uri_list(self,s3_uri_list_file:str):
        from commonlibs.aws.s3 import S3
        s3_obj=S3() 
        s3_uri_list=s3_obj.read_text_file(s3_uri_list_file)
        if s3_uri_list:
            return s3_uri_list.split("\n")

    def _read(self):
        if self.s3_file_format == 'DELIMITED':
            self.source_format = 'csv'
        elif self.s3_file_format == 'PARQUET':
            self.source_format = 'parquet'
        elif self.s3_file_format == 'JSON':
            self.source_format = 'json'
        else:
            raise "invalid s3 file format specified."

        if not self.s3_dataframe_opts.get('encoding') and self.s3_file_encoding:
            self.s3_dataframe_opts['encoding']=self.s3_file_encoding
        if not self.s3_dataframe_opts.get('header') and self.s3_file_header:
            self.s3_dataframe_opts['header'] = self.s3_file_header

        files=[]
        if self.s3_uri_list_file:
            files=self.get_s3_uri_list(self.s3_uri_list_file)
        elif self.s3_file_uris:
            files=self.s3_file_uris

        s3_schema=""
        df=None
        if self.s3_struct:
            s3_schema=StructType.fromJson(json.loads(self.s3_struct))
        if s3_schema:
            df = self.spark.read.schema(s3_schema).format(self.source_format).options(**self.s3_dataframe_opts).load(files)
        else:
            df = self.spark.read.format(self.source_format).options(**self.s3_dataframe_opts).load(files)

        return df

    def _transform(self,df):
        new_df=df
        if self.transform_expr:
            select_expr_list = self.transform_expr.split(self.transform_expr_delimiter)
            new_df = df.selectExpr(*select_expr_list)
        return new_df

    def _load(self,df):
        if self.snow_dataframe_opts.get("dbtable") is None:
            self.snow_dataframe_opts["dbtable"]=self.snow_table
        if self.snow_dataframe_opts.get("sfDatabase") is None:
            self.snow_dataframe_opts["sfDatabase"] = self.snow_database
        if self.snow_dataframe_opts.get("sfSchema") is None:
            self.snow_dataframe_opts["sfSchema"] = self.snow_schema
        if self.snow_dataframe_opts.get("sfWarehouse") is None:
            self.snow_dataframe_opts["sfWarehouse"] = self.snow_warehouse

        #populate default values
        for opt_key,opt_val in S3ToSnowflakeOperator.default_write_opts.items():
            if not self.snow_dataframe_opts.get(opt_key):
                self.snow_dataframe_opts[opt_key]=opt_val

        df.write.format(S3ToSnowflakeOperator.SNOWFLAKE_SOURCE_NAME).options(**self.snow_dataframe_opts).mode(saveMode=self.snow_save_mode).save()

    def run(self):
        self.spark=SparkSession.getActiveSession()
        df=self._read()
        df2=self._transform(df)
        df3=self._load(df2)

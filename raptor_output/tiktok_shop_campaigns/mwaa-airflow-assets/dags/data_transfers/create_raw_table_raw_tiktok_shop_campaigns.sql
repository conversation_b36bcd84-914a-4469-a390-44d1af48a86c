
CREATE OR REPLACE TABLE $raw_db.raw_tiktok_shop_campaigns
  USING TEMPLATE (
    SELECT ARRAY_AGG(OBJECT_CONSTRUCT(*))
    WITHIN GROUP (ORDER BY ORDER_ID)
      FROM TABLE(
         INFER_SCHEMA(
      LOCATION=>'@$raw_db.datalake_stage/raw/daton/BOK/BOK_TIKTOK_US/Product_GMV_Max_Campaign_Daily/1742978186711.parquet'
      , FILE_FORMAT=>'$raw_db.parquet'
     ,IGNORE_CASE=>TRUE
      )
      )
  );

ALTER TABLE $raw_db.raw_tiktok_shop_campaigns ADD COLUMN FILE_NAME STRING;
ALTER TABLE $raw_db.raw_tiktok_shop_campaigns ADD COLUMN ETL_BATCH_RUN_TIME TIMESTAMP_NTZ;
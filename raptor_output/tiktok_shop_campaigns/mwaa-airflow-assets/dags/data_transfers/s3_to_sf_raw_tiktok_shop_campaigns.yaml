---
job_name: 'load raw_tiktok_shop_campaigns'
job_description: 'Transfer data from S3 to Snowflake table using Snowflake Copy'
job_type: 'TRANSFER'
job_engine: 'SNOWFLAKE'
source: 'S3'
target: 'SNOWFLAKE'
s3_file_format: 'PARQUET'
expressions: {'_DATON_BATCH_ID': "GET_IGNORE_CASE($1, '_DATON_BATCH_ID')::NUMBER(38, 0)", '_DATON_BATCH_RUNTIME': "GET_IGNORE_CASE($1, '_DATON_BATCH_RUNTIME')::NUMBER(38, 0)", '_DATON_USER_ID': "GET_IGNORE_CASE($1, '_DATON_USER_ID')::NUMBER(38, 0)", 'BID_TYPE': "GET_IGNORE_CASE($1, 'BID_TYPE')::TEXT", 'CAMPAIGN_ID': "GET_IGNORE_CASE($1, 'CAMPAIGN_ID')::REAL", 'CAMPAIGN_NAME': "GET_IGNORE_CASE($1, 'CAMPAIGN_NAME')::TEXT", 'COST': "GET_IGNORE_CASE($1, 'COST')::REAL", 'COST_PER_ORDER': "GET_IGNORE_CASE($1, 'COST_PER_ORDER')::REAL", 'GROSS_REVENUE': "GET_IGNORE_CASE($1, 'GROSS_REVENUE')::REAL", 'MAX_DELIVERY_BUDGET': "GET_IGNORE_CASE($1, 'MAX_DELIVERY_BUDGET')::REAL", 'NET_COST': "GET_IGNORE_CASE($1, 'NET_COST')::REAL", 'OPERATION_STATUS': "GET_IGNORE_CASE($1, 'OPERATION_STATUS')::TEXT", 'ORDERS': "GET_IGNORE_CASE($1, 'ORDERS')::NUMBER(38, 0)", 'ROAS_BID': "GET_IGNORE_CASE($1, 'ROAS_BID')::REAL", 'ROI': "GET_IGNORE_CASE($1, 'ROI')::REAL", 'SCHEDULE_END_TIME': "GET_IGNORE_CASE($1, 'SCHEDULE_END_TIME')::TIMESTAMP_NTZ", 'SCHEDULE_START_TIME': "GET_IGNORE_CASE($1, 'SCHEDULE_START_TIME')::TIMESTAMP_NTZ", 'SCHEDULE_TYPE': "GET_IGNORE_CASE($1, 'SCHEDULE_TYPE')::TEXT", 'STAT_TIME_DAY': "GET_IGNORE_CASE($1, 'STAT_TIME_DAY')::TIMESTAMP_NTZ", 'TARGET_ROI_BUDGET': "GET_IGNORE_CASE($1, 'TARGET_ROI_BUDGET')::REAL"}
snow_table_name: 'raw_tiktok_shop_campaigns'
snow_database: '{{ snowflake.db.dwh }}'
snow_schema: 'raw'
snow_savemode: 'override'
stage_name: '{{ snowflake.stage.datalake_stage }}'
additional_columns: {'ETL_BATCH_RUN_TIME':'SYSDATE()','FILE_NAME':"CONCAT('{{source_bucket_name }}',METADATA$FILENAME)"}
s3_uri_list_file: 's3://{{ aws.s3.buckets.data_platform_assets }}/{{ aws.s3.prefixes.glue_tmp }}/tiktok_shop_campaigns_tiktok_shop_campaigns.lst'
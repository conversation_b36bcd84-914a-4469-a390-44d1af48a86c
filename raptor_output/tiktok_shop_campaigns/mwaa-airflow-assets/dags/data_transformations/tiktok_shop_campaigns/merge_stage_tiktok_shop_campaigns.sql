CREATE TABLE IF NOT EXISTS $curated_db.fact_tiktok_gmv_campaigns_data AS
    SELECT
        
        *,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_created_timestamp_utc,
        '1900-01-01 00:00:00'::TIMESTAMP AS record_updated_timestamp_utc
    FROM $stage_db.dedupe_tiktok_shop_campaigns
    WHERE 1 = 0;

MERGE INTO
    $curated_db.fact_tiktok_gmv_campaigns_data AS tgt
USING
    $stage_db.dedupe_tiktok_shop_campaigns AS src
    
        ON 1 = 1
       AND src.pk = tgt.pk
       
WHEN MATCHED THEN
UPDATE SET
    tgt.pk = src.pk,
    tgt._daton_batch_id = src._daton_batch_id,
    tgt._daton_batch_runtime = src._daton_batch_runtime,
    tgt._daton_user_id = src._daton_user_id,
    tgt.bid_type = src.bid_type,
    tgt.campaign_id = src.campaign_id,
    tgt.campaign_name = src.campaign_name,
    tgt.cost = src.cost,
    tgt.cost_per_order = src.cost_per_order,
    tgt.gross_revenue = src.gross_revenue,
    tgt.max_delivery_budget = src.max_delivery_budget,
    tgt.net_cost = src.net_cost,
    tgt.operation_status = src.operation_status,
    tgt.orders = src.orders,
    tgt.roas_bid = src.roas_bid,
    tgt.roi = src.roi,
    tgt.schedule_end_time = src.schedule_end_time,
    tgt.schedule_start_time = src.schedule_start_time,
    tgt.schedule_type = src.schedule_type,
    tgt.stat_time_day = src.stat_time_day,
    tgt.target_roi_budget = src.target_roi_budget,
    tgt.file_name = src.file_name,
    tgt.etl_batch_run_time = src.etl_batch_run_time,
    tgt.record_updated_timestamp_utc = SYSDATE()
WHEN NOT MATCHED THEN
INSERT (
    pk,
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    bid_type,
    campaign_id,
    campaign_name,
    cost,
    cost_per_order,
    gross_revenue,
    max_delivery_budget,
    net_cost,
    operation_status,
    orders,
    roas_bid,
    roi,
    schedule_end_time,
    schedule_start_time,
    schedule_type,
    stat_time_day,
    target_roi_budget,
    file_name,
    etl_batch_run_time,
    record_created_timestamp_utc,
    record_updated_timestamp_utc
)
VALUES
(
    src.pk,
    src._daton_batch_id, 
    src._daton_batch_runtime, 
    src._daton_user_id, 
    src.bid_type, 
    src.campaign_id, 
    src.campaign_name, 
    src.cost, 
    src.cost_per_order, 
    src.gross_revenue, 
    src.max_delivery_budget, 
    src.net_cost, 
    src.operation_status, 
    src.orders, 
    src.roas_bid, 
    src.roi, 
    src.schedule_end_time, 
    src.schedule_start_time, 
    src.schedule_type, 
    src.stat_time_day, 
    src.target_roi_budget, 
    src.file_name, 
    src.etl_batch_run_time, 
    SYSDATE(),
    SYSDATE()
);
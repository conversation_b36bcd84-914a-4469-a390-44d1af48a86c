CREATE TRANSIENT TABLE IF NOT EXISTS $raw_db.log_tiktok_shop_campaigns AS
    SELECT 
         * 
       , '1900-01-01 00:00:00'::TIMESTAMP AS log_timestamp_utc
    FROM $raw_db.raw_tiktok_shop_campaigns
    WHERE 1 = 0;

INSERT INTO $raw_db.log_tiktok_shop_campaigns (
    _daton_batch_id,
    _daton_batch_runtime,
    _daton_user_id,
    bid_type,
    campaign_id,
    campaign_name,
    cost,
    cost_per_order,
    gross_revenue,
    max_delivery_budget,
    net_cost,
    operation_status,
    orders,
    roas_bid,
    roi,
    schedule_end_time,
    schedule_start_time,
    schedule_type,
    stat_time_day,
    target_roi_budget,
    file_name,
    etl_batch_run_time,
    log_timestamp_utc 
)
    SELECT
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        bid_type,
        campaign_id,
        campaign_name,
        cost,
        cost_per_order,
        gross_revenue,
        max_delivery_budget,
        net_cost,
        operation_status,
        orders,
        roas_bid,
        roi,
        schedule_end_time,
        schedule_start_time,
        schedule_type,
        stat_time_day,
        target_roi_budget,
        file_name,
        etl_batch_run_time,
        SYSDATE() AS log_timestamp_utc
    FROM 
    $raw_db.raw_tiktok_shop_campaigns;
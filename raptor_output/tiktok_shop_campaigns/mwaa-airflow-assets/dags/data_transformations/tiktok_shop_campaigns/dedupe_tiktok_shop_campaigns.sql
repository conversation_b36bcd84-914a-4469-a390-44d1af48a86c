CREATE OR REPLACE TRANSIENT TABLE $stage_db.dedupe_tiktok_shop_campaigns AS (
    SELECT
        --  primary key  --
        MD5(CONCAT(
            COALESCE(CAST(campaign_id AS VARCHAR), ''), '-',
            COALESCE(CAST(stat_time_day::date AS VARCHAR), '')
            )) AS pk,
        _daton_batch_id,
        _daton_batch_runtime,
        _daton_user_id,
        bid_type,
        campaign_id,
        campaign_name,
        cost,
        cost_per_order,
        gross_revenue,
        max_delivery_budget,
        net_cost,
        operation_status,
        orders,
        roas_bid,
        roi,
        schedule_end_time,
        schedule_start_time,
        schedule_type,
        stat_time_day,
        target_roi_budget,
        file_name,
        etl_batch_run_time
        
    FROM $raw_db.raw_tiktok_shop_campaigns
    WHERE 1=1 
    QUALIFY ROW_NUMBER() OVER(
        PARTITION BY campaign_id
        ORDER BY stat_time_day::date DESC NULLS LAST) = 1
);
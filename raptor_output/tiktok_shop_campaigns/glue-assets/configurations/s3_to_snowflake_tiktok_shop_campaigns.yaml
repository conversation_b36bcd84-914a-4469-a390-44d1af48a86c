---
job_name: "load_tiktok_shop_campaigns_tb_tiktok_shop_campaigns"
job_description: "Transfer data from S3 to Snowflake table using Pyspark"
job_type: "TRANSFER"
job_engine: "PYSPARK"
job_type_version: "1.0"
job_parameters:
  s3_connection: ""
  s3_region: '{{ aws.regions.default }}'
  s3_file_uris: []
  s3_file_suffix: ".parquet"
  s3_file_format: "PARQUET"
  s3_file_encoding: "UTF-8"
  s3_uri_list_file: "s3://{{ aws.s3.buckets.data_platform_assets }}/{{ aws.s3.prefixes.glue_tmp }}/tiktok_shop_campaigns_tiktok_shop_campaigns.lst"
  s3_file_header: true
  s3_dataframe_opts: {"sep":",","multiline": false, "inferSchema":false, "escapeQuotes": true}
  s3_struct_type: "JSON"
  s3_struct: '{"type": "struct", "fields": [{"name": "_daton_batch_id", "type": "long", "nullable": true, "metadata": {}}, {"name": "_daton_batch_runtime", "type": "long", "nullable": true, "metadata": {}}, {"name": "_daton_user_id", "type": "long", "nullable": true, "metadata": {}}, {"name": "bid_type", "type": "string", "nullable": true, "metadata": {}}, {"name": "campaign_id", "type": "double", "nullable": true, "metadata": {}}, {"name": "campaign_name", "type": "string", "nullable": true, "metadata": {}}, {"name": "cost", "type": "double", "nullable": true, "metadata": {}}, {"name": "cost_per_order", "type": "double", "nullable": true, "metadata": {}}, {"name": "gross_revenue", "type": "double", "nullable": true, "metadata": {}}, {"name": "max_delivery_budget", "type": "double", "nullable": true, "metadata": {}}, {"name": "net_cost", "type": "double", "nullable": true, "metadata": {}}, {"name": "operation_status", "type": "string", "nullable": true, "metadata": {}}, {"name": "orders", "type": "integer", "nullable": true, "metadata": {}}, {"name": "roas_bid", "type": "double", "nullable": true, "metadata": {}}, {"name": "roi", "type": "double", "nullable": true, "metadata": {}}, {"name": "schedule_end_time", "type": "timestamp", "nullable": true, "metadata": {}}, {"name": "schedule_start_time", "type": "timestamp", "nullable": true, "metadata": {}}, {"name": "schedule_type", "type": "string", "nullable": true, "metadata": {}}, {"name": "stat_time_day", "type": "timestamp", "nullable": true, "metadata": {}}, {"name": "target_roi_budget", "type": "double", "nullable": true, "metadata": {}}]}'
  snow_warehouse: '{{ snowflake.wh.transform }}'
  snow_schema: '{{ snowflake.schema_.raw }}'
  snow_table_name: 'raw_tiktok_shop_campaigns'
  snow_database: '{{ snowflake.db.dwh }}'
  snow_savemode: 'overwrite'
  snow_dataframe_opts: {"usestagingtable":"on", "truncate_table":"off"}
  transform_expr: '*, input_file_name() as FILE_NAME, current_timestamp() as ETL_BATCH_RUN_TIME'
  transform_expr_delimiter: ","
services:
  postgresdb:
    image: "public.ecr.aws/docker/library/postgres:13"
    ports:
      - "5432:5432"
    restart: always
    networks:
      - back-tier
    environment:
      POSTGRES_USER: airflow
      POSTGRES_PASSWORD: airflow
      POSTGRES_DB: airflow
      PGUSER: airflow
      PGDATA: /var/lib/postgresql/data/pgdata
    healthcheck:
      test: ["CMD-SHELL", "pg_isready", "-U", "airflow"]
      interval: 5s
      timeout: 2s
      retries: 3
      start_period: 5s  
    volumes:
      - postgres_home:/var/lib/postgresql/data
    expose:
      - "5432"

  redis_broker:
    image: redis:latest
    restart: always
    networks:
      - back-tier
    expose:
      - "6379"

  webserver:
    image: airflow-local-base:latest
    pull_policy: never
    healthcheck:
      test: ["CMD-SHELL","curl -f http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 2s
      retries: 2
      start_period: 30s
    depends_on:
      postgresdb:
        condition: service_healthy
    build:
      context: .
      dockerfile: deployment/local/Dockerfile
    ports:
      - "8080:8080"
    env_file:
      - $PWD/deployment/local/.env.local
      - $PWD/deployment/local/.env_webserver.local
    networks:
      - front-tier
      - back-tier
    volumes:
      - airflow_home:/opt/airflow
      - type: bind
        source: "$PWD/aws_home_local"
        target: /home/<USER>/.aws
        read_only: true

  scheduler:
    pull_policy: never
    depends_on:
      webserver:
        condition: service_healthy
      redis_broker:
        condition: service_started
    image: airflow-local-base:latest
    volumes:
      - airflow_home:/opt/airflow
      - type: bind
        source: "$PWD/aws_home_local"
        target: /home/<USER>/.aws
        read_only: true
    networks:
      - back-tier
    env_file:
      - $PWD/deployment/local/.env.local
      - $PWD/deployment/local/.env_scheduler.local

  worker:
    pull_policy: never
    depends_on:
      webserver:
        condition: service_healthy
      redis_broker:
        condition: service_started
    image: airflow-local-base:latest
    volumes:
      - airflow_home:/opt/airflow
      - type: bind
        source: "$PWD/aws_home_local"
        target: /home/<USER>/.aws
        read_only: true
    networks:
      - back-tier
    env_file:
      - $PWD/deployment/local/.env.local
      - $PWD/deployment/local/.env_worker.local
  
#  ops-worker:
#    depends_on:
#      webserver:
#        condition: service_healthy
#      redis_broker:
#        condition: service_started
#    build:
#      context: .
#      platforms:
#        - "linux/amd64"
#      dockerfile: deployment/local/Dockerfile_ops_worker
#    image: airflow-local-ops-base:latest
#    volumes:
#      - airflow_home:/opt/airflow
#      - type: bind
#        source: "$PWD/aws_home_local"
#        target: /home/<USER>/.aws
#        read_only: true
#    networks:
#      - back-tier
#    env_file:
#      - $PWD/deployment/local/.env.local
#      - $PWD/deployment/local/.env_worker.local
#      - $PWD/deployment/local/.env_worker_ops.local

volumes:
  airflow_home:
    driver: local
    driver_opts:
      type: none
      device: "$PWD/airflow_home_local"
      o: bind
  postgres_home:
    driver: local
    driver_opts:
      type: none
      device: "$PWD/postgres_db"
      o: bind

networks:
  # The presence of these objects is sufficient to define them
  front-tier: {}
  back-tier: {}
